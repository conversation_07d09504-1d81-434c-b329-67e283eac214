# Mobile App Template iOS

A comprehensive iOS mobile application template built with SwiftUI and Clean Architecture patterns. This template provides a solid foundation for building modern iOS applications with authentication, role-based navigation, and common mobile app features.

## 🏗️ Architecture

This template follows **Clean Architecture** principles with clear separation of concerns:

```
VantisInstructor/
├── Core/                    # Core infrastructure layer
│   ├── Network/            # API client, endpoints, networking
│   ├── Services/           # Business services (Analytics, Config, Sync)
│   ├── Storage/            # Data persistence (Keychain, UserDefaults)
│   └── Utils/              # Utilities, constants, extensions
├── Domain/                 # Business logic layer
│   ├── Models/             # Data models and entities
│   └── Repositories/       # Repository interfaces
├── Presentation/           # UI layer
│   ├── Authentication/     # Login, register, auth flows
│   ├── Common/            # Shared UI components and views
│   ├── Home/              # Home screen
│   ├── Profile/           # User profile screens
│   ├── Transactions/      # Transaction history
│   └── Wallet/            # Wallet functionality
└── Assets.xcassets        # Images, colors, app icons
```

## ✨ Features

### 🔐 Authentication System
- Email/password authentication
- Biometric authentication (Face ID/Touch ID)
- Auto-login with secure token storage
- Role-based access control (User, Business, Admin)

### 🎨 Modern UI Components
- Custom tab bar with smooth animations
- Reusable UI components
- Dark mode support
- Responsive design for all iOS devices

### 🏢 Role-Based Navigation
- **User Role**: Home, Wallet, Items, History, Profile
- **Business Role**: Dashboard, Wallet, Payments, Analytics, Profile
- **Admin Role**: Dashboard, Users, Businesses, Items, Profile

### 🔧 Core Services
- **Configuration Service**: Remote config and feature flags
- **Analytics Service**: Event tracking and user analytics
- **Sync Service**: Background data synchronization
- **Notification Service**: Push notifications support

### 🛡️ Security Features
- Keychain integration for secure storage
- Token-based authentication with refresh
- Biometric authentication support
- Network security with retry mechanisms

## 🚀 Getting Started

### Prerequisites
- **Xcode**: 15.0 or later
- **iOS**: 17.0 or later
- **Swift**: 5.9 or later
- **macOS**: 13.0 or later

### Installation

1. **Clone or download** this template
2. **Open** `VantisInstructor.xcodeproj` in Xcode
3. **Customize** the app configuration (see Customization section)
4. **Build and run** the project

```bash
# Open the project
open VantisInstructor.xcodeproj

# Or build from command line
xcodebuild -scheme VantisInstructor -destination 'platform=iOS Simulator,name=iPhone 16 Pro' build
```

## 🎯 Customization Guide

### 1. App Configuration

Update `Core/Utils/AppConstants.swift`:

```swift
struct AppConstants {
    // Update API configuration
    struct API {
        static let baseURL = "https://api.yourapp.com/v1"  // Your API URL
    }
    
    // Update app information
    struct AppInfo {
        static let name = "YourApp"  // Your app name
        static let bundleId = "com.yourapp.mobile"  // Your bundle ID
    }
    
    // Update URLs
    struct URLs {
        static let website = "https://yourapp.com"
        static let support = "https://support.yourapp.com"
        // ... other URLs
    }
}
```

### 2. Bundle Identifier

Update in Xcode project settings:
1. Select your project in the navigator
2. Go to **Build Settings**
3. Update **Product Bundle Identifier** to your app's bundle ID

### 3. App Name and Display Name

Update in `VantisInstructor.xcodeproj`:
1. Select your project
2. Go to **Build Settings**
3. Update **Product Name** and **Bundle Display Name**

### 4. Colors and Branding

Update colors in `Core/Utils/AppConstants.swift`:

```swift
struct Colors {
    static let primary = Color(red: 0.2, green: 0.6, blue: 1.0)  // Your primary color
    static let secondary = Color(red: 1.0, green: 0.6, blue: 0.2)  // Your secondary color
    // ... other colors
}
```

### 5. API Endpoints

Update `Core/Network/APIEndpoints.swift` with your API endpoints:

```swift
struct APIEndpoints {
    static let auth = "/auth"
    static let users = "/users"
    // Add your specific endpoints
}
```

## 📱 Key Components

### Authentication Flow
- `AuthViewModel`: Handles authentication state and logic
- `LoginView`: Login screen UI
- `AuthRepository`: Authentication API calls

### Navigation System
- `RoleBasedTabView`: Main navigation based on user role
- `ModernTabBar`: Custom animated tab bar
- Role-specific tab views for different user types

### Data Models
- `User`: User model with role-based properties
- `Transaction`: Transaction/payment model
- `Item`: Generic item/product model (formerly rewards)
- `Business`: Business/merchant model

### Core Services
- `ConfigurationService`: App configuration and feature flags
- `AnalyticsService`: Event tracking and analytics
- `SyncService`: Background data synchronization
- `NotificationService`: Push notification handling

## 🔧 Development Tips

### Adding New Features
1. Create models in `Domain/Models/`
2. Add repository interfaces in `Domain/Repositories/`
3. Implement UI in `Presentation/`
4. Add any core services in `Core/Services/`

### Customizing User Roles
Update `UserRole` enum in `Domain/Models/User.swift`:

```swift
enum UserRole: String, Codable, CaseIterable {
    case user = "USER"
    case business = "BUSINESS"
    case admin = "ADMIN"
    // Add your custom roles
}
```

### Adding New Tab Views
1. Update `RoleBasedTabView.swift`
2. Add new tab items to the appropriate role's tab array
3. Create corresponding view controllers

### SwiftUI Navigation Requirements

⚠️ **Important**: When using `NavigationStack` with programmatic navigation in SwiftUI:

1. **navigationDestination modifiers MUST be attached directly to NavigationStack**, not to child views
2. Place all `.navigationDestination(isPresented:)` modifiers immediately after the NavigationStack's content
3. This ensures navigation works correctly across all navigation triggers

Example:
```swift
NavigationStack {
    // Your content here
    ScrollView {
        // Views with navigation triggers
    }
}
// ✅ Correct: navigationDestination attached to NavigationStack
.navigationDestination(isPresented: $navigateToQuiz) {
    QuizView()
}
.navigationDestination(isPresented: $navigateToAssignment) {
    AssignmentView()
}
```

❌ **Avoid**: Placing navigationDestination modifiers on child views inside NavigationStack

## 🧪 Testing

The template includes test targets:
- `VantisInstructorTests`: Unit tests
- `VantisInstructorUITests`: UI tests

Run tests:
```bash
# Run all tests
xcodebuild test -scheme VantisInstructor -destination 'platform=iOS Simulator,name=iPhone 16 Pro'
```

## 📚 Dependencies

This template uses only native iOS frameworks:
- **SwiftUI**: Modern UI framework
- **Combine**: Reactive programming
- **Foundation**: Core functionality
- **UserNotifications**: Push notifications
- **LocalAuthentication**: Biometric authentication

## 🤝 Contributing

This template is designed to be a starting point. Feel free to:
1. Fork and customize for your needs
2. Add new features and components
3. Improve the architecture and patterns
4. Share improvements with the community

## 📄 License

This template is provided as-is for educational and development purposes. Customize and use it according to your project's requirements.

## 🆘 Support

For questions about using this template:
1. Check the documentation in each module
2. Review the code comments and examples
3. Refer to Apple's SwiftUI documentation
4. Check iOS development best practices

---

**Happy Coding! 🚀**

Built with ❤️ using SwiftUI and Clean Architecture
