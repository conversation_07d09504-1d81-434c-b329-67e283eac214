<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright 2025 EarnBase Technology <https://earnbase.io> -->
<!-- License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html) -->
<odoo>
    
    <!-- Inherit FastAPI Endpoint Form View -->
    <record id="view_fastapi_endpoint_form_inherit_auth" model="ir.ui.view">
        <field name="name">fastapi.endpoint.form.inherit.auth</field>
        <field name="model">fastapi.endpoint</field>
        <field name="inherit_id" ref="fastapi.fastapi_endpoint_form_view"/>
        <field name="arch" type="xml">
            <!-- Add API Authentication configuration tab -->
            <xpath expr="//sheet/span[@name='configuration']" position="replace">
                <notebook>
                    <page string="API Configuration" name="api_config">
                        <group>
                            <group string="Authentication and Security">
                                <field name="enable_rate_limiting"/>
                            </group>
                        </group>
                    </page>
                    <page string="Advanced" name="advanced">
                        <span name="configuration"/>
                    </page>
                </notebook>
            </xpath>
        </field>
    </record>

</odoo> 