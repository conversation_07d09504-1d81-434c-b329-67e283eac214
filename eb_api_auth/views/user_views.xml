<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Mở rộng view form user để thêm page API Tokens -->
    <record id="view_users_form_api_tokens" model="ir.ui.view">
        <field name="name">res.users.form.api.tokens</field>
        <field name="model">res.users</field>
        <field name="inherit_id" ref="base.view_users_form_simple_modif"/>
        <field name="arch" type="xml">
            <notebook position="inside">
                <page string="API Tokens" name="api_tokens">
                    <field name="token_ids" readonly="1" widget="one2many">
                        <list>
                            <field name="token_id"/>
                            <field name="token_type"/>
                            <field name="status"/>
                            <field name="issued_at"/>
                            <field name="expiration_date"/>
                            <field name="last_used"/>
                            <button name="invalidate" string="Revoke" type="object" 
                                    icon="fa-times" class="text-danger"
                                    display="status == 'active'"/>
                        </list>
                    </field>
                    <group>
                        <div class="alert alert-info" role="alert">
                            <p>Các API tokens được cấp khi bạn xác thực qua API.</p>
                            <p>Bạn có thể thu hồi tokens bất kỳ lúc nào nếu nghi ngờ chúng đã bị xâm phạm.</p>
                        </div>
                    </group>
                </page>
            </notebook>
        </field>
    </record>
</odoo> 