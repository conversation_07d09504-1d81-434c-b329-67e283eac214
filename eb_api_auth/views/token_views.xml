<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- AuthToken list view -->
    <record id="view_eb_api_auth_token_list" model="ir.ui.view">
        <field name="name">eb_api_auth.token.list</field>
        <field name="model">eb_api_auth.token</field>
        <field name="arch" type="xml">
            <list string="API Tokens">
                <field name="token_id"/>
                <field name="user_id"/>
                <field name="token_type"/>
                <field name="status"/>
                <field name="issued_at"/>
                <field name="expiration_date"/>
                <field name="last_used"/>
            </list>
        </field>
    </record>

    <!-- AuthToken form view -->
    <record id="view_eb_api_auth_token_form" model="ir.ui.view">
        <field name="name">eb_api_auth.token.form</field>
        <field name="model">eb_api_auth.token</field>
        <field name="arch" type="xml">
            <form string="API Token">
                <header>
                    <button name="invalidate" string="Revoke Token" type="object"
                           confirm="Are you sure you want to revoke this token?" class="oe_highlight"
                           display="status == 'active'" />
                    <field name="status" widget="statusbar" statusbar_visible="active,expired,revoked"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="token_id"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="user_id"/>
                            <field name="token_type"/>
                            <field name="jti"/>
                        </group>
                        <group>
                            <field name="issued_at"/>
                            <field name="expiration_date"/>
                            <field name="last_used"/>
                        </group>
                    </group>
                    <group string="Revocation Info" display="status == 'revoked'">
                        <field name="revoked_date" readonly="1"/>
                        <field name="revoked_reason" readonly="1"/>
                    </group>
                    <group string="Client Info">
                        <field name="ip_address"/>
                        <field name="user_agent"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- AuthToken search view -->
    <record id="view_eb_api_auth_token_search" model="ir.ui.view">
        <field name="name">eb_api_auth.token.search</field>
        <field name="model">eb_api_auth.token</field>
        <field name="arch" type="xml">
            <search string="Search Tokens">
                <field name="token_id"/>
                <field name="user_id"/>
                <field name="jti"/>
                <separator/>
                <filter string="Active" name="active" domain="[('status', '=', 'active')]"/>
                <filter string="Expired" name="expired" domain="[('status', '=', 'expired')]"/>
                <filter string="Revoked" name="revoked" domain="[('status', '=', 'revoked')]"/>
                <separator/>
                <filter string="Access Tokens" name="access_tokens" domain="[('token_type', '=', 'access_token')]"/>
                <filter string="Refresh Tokens" name="refresh_tokens" domain="[('token_type', '=', 'refresh_token')]"/>
                <separator/>
                <filter string="Issued Today" name="issued_today"
                       domain="[('issued_at', '>=', context_today().strftime('%Y-%m-%d'))]"/>
                <group expand="0" string="Group By">
                    <filter string="User" name="user" context="{'group_by': 'user_id'}"/>
                    <filter string="Token Type" name="token_type" context="{'group_by': 'token_type'}"/>
                    <filter string="Status" name="status" context="{'group_by': 'status'}"/>
                    <filter string="Issue Date" name="issue_date" context="{'group_by': 'issued_at:day'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Token action window -->
    <record id="action_eb_api_auth_token" model="ir.actions.act_window">
        <field name="name">API Tokens</field>
        <field name="res_model">eb_api_auth.token</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_eb_api_auth_token_search"/>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No API tokens found
            </p>
            <p>
                API tokens are created when users authenticate via the API.
                You can revoke tokens from this interface.
            </p>
        </field>
    </record>

</odoo>