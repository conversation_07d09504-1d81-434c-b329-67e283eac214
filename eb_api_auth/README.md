# EarnBase API Authentication

Module này cung cấp hệ thống xác thực API bằng JWT (JSON Web Token) cho các ứng dụng của EarnBase trên nền tảng Odoo.

## Tính năng

- Tích hợp với `auth_jwt` và `fastapi_auth_jwt` để xác thực API
- Quản lý và theo dõi các token API
- Hỗ trợ access token và refresh token
- Tính năng chứng thực dựa trên JWT theo tiêu chuẩn
- Tích hợp với FastAPI Odoo để cung cấp API endpoints
- Hỗ trợ cookie-based authentication
- Rate limiting (giới hạn truy cập)

## Phụ thuộc

- `auth_jwt`: Quản lý JWT validator
- `fastapi`: Framework FastAPI cho Odoo
- `fastapi_auth_jwt`: Tích hợp JWT với FastAPI

## C<PERSON>u hình

### Tạo JWT Validator

1. T<PERSON><PERSON> cập **API Auth → JWT Validators**
2. Tạo validator mới với tên `eb_api_default` (hoặc tên tùy chọn)
3. Cấu hình các thông số JWT:
   - Secret key
   - Algorithm
   - Audience
   - Issuer
   - Cookie settings (nếu cần)

### Tạo API Endpoint

1. Truy cập **API Auth → API Endpoints**
2. Tạo endpoint mới với ứng dụng là "EB Auth API"
3. Cấu hình API endpoint:
   - Host và port
   - API version
   - JWT Validator (chọn validator đã tạo)
   - Thiết lập rate limiting nếu cần

## Sử dụng API

### Authentication Endpoints

- `POST /token`: Lấy access token và refresh token
- `GET /me`: Lấy thông tin người dùng hiện tại
- `GET /verify`: Xác thực token
- `GET /tokens`: Liệt kê tất cả token của người dùng
- `POST /revoke`: Thu hồi token
- `POST /token/request`: Yêu cầu token mới (hỗ trợ nhiều grant types)
- `POST /refresh`: Refresh access token bằng refresh token

### Yêu cầu token mới

```http
POST /api/v1/token
Content-Type: application/x-www-form-urlencoded

username=admin&password=admin
```

### Lấy thông tin người dùng

```http
GET /api/v1/me
Authorization: Bearer {access_token}
```

## Phát triển

### Mở rộng API

Để thêm endpoint mới vào API, bạn có thể:

1. Tạo router mới trong thư mục `routers/`
2. Đăng ký router với API registry thông qua hàm `register_router`
3. Sử dụng các dependencies có sẵn để xác thực và kiểm soát quyền

### Liên kết với ứng dụng khác

Module này có thể được sử dụng như cơ sở xác thực cho các ứng dụng:

- Web applications sử dụng cookie-based auth
- Mobile apps sử dụng JWT tokens
- Single-page applications (SPA)
- Microservices architecture

## Security Considerations

- JWT tokens được mã hóa và ký bằng secret key
- Tokens được lưu trữ trong database với thông tin meta
- Rate limiting giúp ngăn chặn các cuộc tấn công brute-force
- Tokens có thể bị thu hồi bất cứ lúc nào
- Refresh tokens có thời hạn dài hơn, access tokens ngắn hơn

## Đóng góp

Vui lòng gửi các vấn đề và đề xuất cải tiến qua [GitHub Issues](https://github.com/earnbase/eb_api_auth/issues) hoặc [EarnBase Support](https://earnbase.io/support). 