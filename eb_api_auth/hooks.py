# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging

_logger = logging.getLogger(__name__)


def post_init_hook(env):
    """Khởi tạo dữ liệu sau khi cài đặt module

    Args:
        env: Odoo environment
    """

    # Đảm bảo cấu hình JWT Validator mặc định đã được tạo
    jwt_validator = env["auth.jwt.validator"].search(
        [("name", "=", "eb_api_default")], limit=1
    )

    if not jwt_validator:
        _logger.warning("JWT Validator not found, please check data files were loaded")

    # Đảm bảo API auth endpoint đã được tạo và đồng bộ
    try:
        # Đồng bộ tất cả các FastAPI endpoints
        endpoints = env["fastapi.endpoint"].search([])
        for endpoint in endpoints:
            if not endpoint.registry_sync:
                _logger.info("Syncing FastAPI endpoint: %s", endpoint.name)
                try:
                    endpoint.action_sync_registry()
                except Exception as e:
                    _logger.error("Error syncing endpoint %s: %s", endpoint.name, e)

        _logger.info("All FastAPI endpoints synced successfully")
    except Exception as e:
        _logger.error("Error initializing API endpoints: %s", e)


def post_load_hook():
    """Được gọi khi module được tải"""
    # Đảm bảo API registry được khởi tạo
    _logger.info("JWT API Authentication Registry initialized successfully")
