# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

# Re-export các dependency từ fastapi_auth_jwt
from odoo.addons.fastapi_auth_jwt.dependencies import (  # noqa: F401
    auth_jwt_authenticated_payload,
    auth_jwt_optionally_authenticated_payload,
    auth_jwt_authenticated_partner,
    auth_jwt_optionally_authenticated_partner,
    auth_jwt_authenticated_odoo_env,
    auth_jwt_default_validator_name,
    auth_jwt_http_header_authorization,
)

# Export các dependency từ auth.py
from .auth import (  # noqa: F401
    auth_jwt_validator_name,
    get_jwt_validator,
    get_current_user,
)

# Import từ eb_api_core
from odoo.addons.eb_api_core.dependencies.rate_limit import (  # noqa: F401
    rate_limit_dependency,
    rate_limited,
)
