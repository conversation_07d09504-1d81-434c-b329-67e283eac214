# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import time
import uuid
from typing import Optional, Annotated, Any, Dict, List

from fastapi import Depends

from odoo.api import Environment
from odoo.addons.base.models.res_users import Users
from odoo.addons.fastapi.dependencies import odoo_env
from odoo.addons.fastapi_auth_jwt.dependencies import (
    auth_jwt_authenticated_payload,
)

from odoo.addons.eb_api_core.schemas.error import ErrorCode
from odoo.addons.eb_api_core.utils.exceptions import api_exception
from odoo.addons.eb_api_core.utils.logging import get_request_logger

_logger = get_request_logger(__name__)


def get_user_role(user: Users, env: Environment) -> str:
    """
    Determine user role based on LMS models.

    Args:
        user: res.users record
        env: Odoo environment

    Returns:
        str: Role name ('instructor', 'student', 'admin', 'user')
    """
    try:
        # Check if user is instructor
        instructor = env["eb.instructor.instructor"].search([
            ("user_id", "=", user.id),
            ("active", "=", True)
        ], limit=1)

        if instructor:
            return "instructor"

        # Check if user is student
        student = env["eb.student.student"].search([
            ("user_id", "=", user.id),
            ("active", "=", True)
        ], limit=1)

        if student:
            return "student"

        # Check if user has admin privileges
        if user.has_group('base.group_system'):
            return "admin"

        # Default role
        return "user"

    except Exception as e:
        _logger.error(f"Error determining user role: {str(e)}")
        return "user"


def get_user_permissions(role: str, user: Users, env: Environment) -> List[str]:
    """
    Get role-based permissions for user.

    Args:
        role: User role
        user: res.users record
        env: Odoo environment

    Returns:
        List[str]: List of permissions
    """
    permissions = []

    # Base permissions for all authenticated users
    permissions.extend([
        "read_own_profile",
        "update_own_profile"
    ])

    if role == "instructor":
        permissions.extend([
            "read_assigned_courses",
            "update_assigned_courses",
            "read_assigned_students",
            "manage_attendance",
            "read_own_schedule",
            "update_own_schedule",
            "manage_lessons",
            "create_evaluations",
            "read_own_analytics",
            "manage_leave_requests"
        ])

    elif role == "student":
        permissions.extend([
            "read_enrolled_courses",
            "read_own_lessons",
            "submit_attendance",
            "read_own_progress",
            "read_own_grades",
            "submit_evaluations",
            "read_own_payments",
            "read_own_certificates"
        ])

    elif role == "admin":
        permissions.extend([
            "manage_all_courses",
            "manage_all_students",
            "manage_all_instructors",
            "manage_all_lessons",
            "manage_system_settings",
            "read_all_analytics"
        ])

    return permissions


# Override default validator name for the module
def auth_jwt_validator_name() -> str:
    """Return the default JWT validator name for this module"""
    return "eb_api_default"


# Get JWT validator record with additional fallback logic
def get_jwt_validator(
    validator_name: str = "eb_api_default",
    env: Annotated[Environment, Depends(odoo_env)] = None,
):
    """Get JWT validator with fallback logic.

    This extends the standard validator lookup with:
    1. Search by specific name
    2. Fallback to any validator if not found
    3. Error if no validator exists
    """
    validator = (
        env["auth.jwt.validator"]
        .sudo()
        .search([("name", "=", validator_name)], limit=1)
    )

    if not validator:
        # Fallback to first validator if not found by name
        validator = env["auth.jwt.validator"].sudo().search([], limit=1)

    if not validator:
        # Error if no validator at all
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR, "No JWT validator configuration found"
        )

    return validator


# Get current user from JWT payload
def get_current_user(
    payload: Annotated[Dict[str, Any], Depends(auth_jwt_authenticated_payload)],
    env: Annotated[Environment, Depends(odoo_env)],
) -> Users:
    """Get the current user from the JWT payload.

    This uses the standard JWT payload authenticated by fastapi_auth_jwt
    but returns a res.users record instead of partner.
    """
    user_id = payload.get("sub")
    if not user_id:
        raise api_exception(ErrorCode.UNAUTHORIZED, "Invalid user identity in token")

    try:
        user = env["res.users"].sudo().browse(int(user_id))
        if not user.exists():
            raise api_exception(ErrorCode.UNAUTHORIZED, "User not found")
        return user
    except Exception as e:
        _logger.error(f"Error getting current user: {str(e)}")
        raise api_exception(ErrorCode.UNAUTHORIZED, f"Invalid user identity: {str(e)}")


# Get optional user (may be None)
def get_optional_user(
    payload: Annotated[
        Optional[Dict[str, Any]], Depends(auth_jwt_authenticated_payload)
    ],
    env: Annotated[Environment, Depends(odoo_env)],
) -> Optional[Users]:
    """Get the current user if available, or None if not authenticated."""
    if not payload:
        return None

    user_id = payload.get("sub")
    if not user_id:
        return None

    try:
        user = env["res.users"].sudo().browse(int(user_id))
        if not user.exists():
            return None
        return user
    except Exception as e:
        _logger.error(f"Error getting optional user: {str(e)}")
        return None


# Authenticate and create tokens
def authenticate_user(
    username: str, password: str, env: Annotated[Environment, Depends(odoo_env)]
) -> Dict[str, Any]:
    """Authenticate user with username and password, create token.

    Args:
        username: User login
        password: User password
        env: Odoo environment

    Returns:
        Dictionary with token information and user

    Raises:
        HTTPException: If authentication fails
    """
    try:
        # Tìm user bằng username hoặc email
        login_field = username

        # Nếu username có dạng email, tìm user bằng email
        if "@" in username:
            user_by_email = env["res.users"].sudo().search([("email", "=", username)], limit=1)
            if user_by_email:
                login_field = user_by_email.login

        # Authenticate user
        auth_info = env["res.users"].authenticate(
            db=env.cr.dbname,
            credential={"login": login_field, "password": password, "type": "password"},
            user_agent_env={
                "interactive": False,
            },
        )

        if not auth_info["uid"]:
            raise api_exception(
                ErrorCode.INVALID_CREDENTIALS,
                "Invalid username or password",
            )

        # Get user information
        user = env["res.users"].sudo().browse(auth_info["uid"])

        # Get user role and permissions
        role = get_user_role(user, env)
        permissions = get_user_permissions(role, user, env)

        # Get JWT validator
        validator = get_jwt_validator(env=env)

        # Create JWT payloads
        now = int(time.time())
        access_expires = 365 * 24 * 3600  # 365 days (1 year)
        refresh_expires = 365 * 24 * 3600  # 365 days (same as access token)

        # Thêm JWT ID cho tracking
        access_jti = str(uuid.uuid4())
        refresh_jti = str(uuid.uuid4())

        # Access token payload with role and permissions
        access_payload = {
            "iat": now,
            "sub": str(auth_info["uid"]),
            "name": user.name,
            "login": user.login,
            "role": role,
            "permissions": permissions,
            "type": "access_token",
            "jti": access_jti
        }
        
        _logger.info(f"Creating access token with payload: {access_payload}")

        # Refresh token payload with role
        refresh_payload = {
            "iat": now,
            "sub": str(auth_info["uid"]),
            "name": user.name,
            "login": user.login,
            "role": role,
            "type": "refresh_token",
            "jti": refresh_jti
        }
        
        _logger.info(f"Creating refresh token with payload: {refresh_payload}")

        # Use validator's _encode method to create token
        access_token = validator._encode(
            access_payload, validator.secret_key, access_expires
        )
        refresh_token = validator._encode(
            refresh_payload, validator.secret_key, refresh_expires
        )

        # Lấy IP address và user agent từ request
        request = getattr(env.context, "request", None)
        ip_address = None
        user_agent = None
        
        if request:
            ip_address = request.httprequest.environ.get("REMOTE_ADDR")
            user_agent = request.httprequest.environ.get("HTTP_USER_AGENT")

        # Lưu thông tin token vào database
        try:
            # Tạo access token record
            env["eb_api_auth.token"].sudo().create_token(
                user_id=auth_info["uid"],
                token_type="access_token",
                expiration_seconds=access_expires,
                ip_address=ip_address,
                user_agent=user_agent,
                jti=access_jti
            )
            
            # Tạo refresh token record
            env["eb_api_auth.token"].sudo().create_token(
                user_id=auth_info["uid"],
                token_type="refresh_token",
                expiration_seconds=refresh_expires,
                ip_address=ip_address,
                user_agent=user_agent,
                jti=refresh_jti
            )
        except Exception as e:
            _logger.error(f"Could not store token in database: {str(e)}")
            # Không block request nếu lưu token thất bại

        return {
            "user": user,
            "access_token": access_token,
            "token_type": "Bearer",
            "expires_in": access_expires,
            "refresh_token": refresh_token,
            "refresh_token_expires_in": refresh_expires,
            "issued_at": now,
        }

    except Exception as e:
        if isinstance(e, api_exception.__class__):
            raise
        _logger.error(f"Authentication error: {str(e)}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Authentication failed: {str(e)}",
        )
