# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

"""
API Authentication Endpoints

Cung cấp các API endpoints cho xác thực JWT:
- /sign-in: <PERSON><PERSON>ng nhập và lấy access token + refresh token
- /me: L<PERSON>y thông tin người dùng đã xác thực
- /verify: Xác thực token
- /refresh: Làm mới access token bằng refresh token
"""

import time
from typing import Annotated
from datetime import datetime

from fastapi import APIRouter, Depends, Body

from odoo.api import Environment
from odoo.addons.fastapi.dependencies import odoo_env
from odoo.addons.eb_api_core.dependencies import current_request_id

# Import từ schemas mới
from odoo.addons.eb_api_core.schemas.auth import (
    LoginRequest,
    UserInfo,
    TokenResponse,
    OAuth2Token,
    UserInfoResponse,
)

# Import enhanced schemas
from odoo.addons.eb_api_auth.schemas.auth import (
    UnifiedLoginRequest,
    EnhancedTokenResponse,
    DeviceRegistrationRequest,
    DeviceRegistrationResponse,
    UserRegistrationRequest,
    UserRegistrationResponse,
    ForgotPasswordRequest,
    ForgotPasswordResponse,
    ResetPasswordRequest,
    ResetPasswordResponse,
    EmailVerificationRequest,
    EmailVerificationResponse,
    EnhancedPasswordChangeRequest,
    EnhancedPasswordChangeResponse,
)

# Import từ dependencies
from odoo.addons.eb_api_auth.dependencies.auth import (
    get_current_user,
    auth_jwt_authenticated_payload,
    authenticate_user,
)

# Import từ eb_api_core
from odoo.addons.eb_api_core.schemas.base import ResponseBase
from odoo.addons.eb_api_core.schemas.error import ErrorCode
from odoo.addons.eb_api_core.utils.exceptions import api_exception
from odoo.addons.eb_api_core.utils.decorators import auto_error_response
from odoo.addons.eb_api_core.utils.request import get_meta_data
from odoo.addons.eb_api_core.utils import (
    get_request_logger,
    trace,
    add_span_attributes,
)  # noqa: F401

_logger = get_request_logger(__name__)

# Note: Đường dẫn gốc đã được thiết lập qua root_path trong fastapi_endpoint_data.xml
# Không sử dụng prefix ở đây, chỉ định nghĩa đường dẫn tương đối
auth_router = APIRouter()


@auth_router.post("/sign-in", response_model=EnhancedTokenResponse)
@auto_error_response([401, 500])
@trace()
async def sign_in(
    credentials: UnifiedLoginRequest,
    env: Annotated[Environment, Depends(odoo_env)],
    request_id: Annotated[str, Depends(current_request_id)],
):
    """Enhanced sign-in supporting username/email login with optional device tracking"""
    login_identifier = credentials.username
    _logger.info(
        f"Processing sign-in request for: {login_identifier}, requestId={request_id}"
    )

    try:
        # Thêm attribute truy vết cho việc đăng nhập
        add_span_attributes(
            login_attempt=login_identifier,
            has_device_info=credentials.device_info is not None,
            remember_me=credentials.remember_me
        )

        # Authenticate user (sử dụng username hoặc email)
        auth_result = authenticate_user(
            username=login_identifier,
            password=credentials.password,
            env=env
        )

        # Thêm attribute sau khi đăng nhập thành công
        add_span_attributes(user_id=auth_result.get("user_id"), login_successful=True)

        # Xử lý device info nếu có
        device_data = {}
        if credentials.device_info:
            device_data = {
                "device_id": credentials.device_info.device_id,
                "device_name": credentials.device_info.device_name,
                "device_type": credentials.device_info.device_type,
                "os_name": credentials.device_info.os_name,
                "os_version": credentials.device_info.os_version,
                "app_version": credentials.device_info.app_version,
                "browser_name": credentials.device_info.browser_name,
                "browser_version": credentials.device_info.browser_version,
                "user_agent": credentials.device_info.user_agent,
                "ip_address": credentials.device_info.ip_address,
                "location": credentials.device_info.location,
            }

        # Tạo enhanced response data
        user = auth_result["user"]  # Get user object
        enhanced_data = {
            "access_token": auth_result["access_token"],
            "token_type": auth_result["token_type"],
            "expires_in": auth_result["expires_in"],
            "refresh_token": auth_result["refresh_token"],
            "refresh_token_expires_in": auth_result["refresh_token_expires_in"],
            "scope": "api",
            "issued_at": auth_result["issued_at"],
            "user_id": user.id,  # Extract user_id from user object
            "device_registered": bool(credentials.device_info),
            "device_info": device_data if credentials.device_info else None,
            "remember_me": credentials.remember_me,
            "login_method": "email" if "@" in credentials.username else "username",
        }

        _logger.info(
            f"Sign-in successful for: {login_identifier}, requestId={request_id}"
        )

        # Trả về enhanced response
        return EnhancedTokenResponse(
            success=True,
            message="Authentication successful",
            data=enhanced_data,
            meta=get_meta_data(),
        )
    except Exception as e:
        # Thêm attribute cho đăng nhập thất bại
        add_span_attributes(login_successful=False, error=str(e))
        _logger.error(
            f"Sign-in failed for: {login_identifier}: {str(e)}, requestId={request_id}"
        )
        raise


@auth_router.get(
    "/me",
    response_model=UserInfoResponse,
    tags=["auth"],
    summary="Get current user info",
)
@trace()
async def get_user_info(
    user: Annotated[object, Depends(get_current_user)],
    env: Annotated[Environment, Depends(odoo_env)],
    request_id: Annotated[str, Depends(current_request_id)],
):
    """Lấy thông tin chi tiết của người dùng đã xác thực"""
    # Lấy requestId hiện tại và log
    _logger.info(f"Getting user info for user_id={user.id}, requestId={request_id}")

    try:
        # Thêm attribute vào span hiện tại
        add_span_attributes(user_id=user.id, user_login=user.login)

        # Lấy danh sách groups
        group_names = []
        for group in user.groups_id:
            group_names.append(group.name)

        # Kiểm tra quyền admin
        is_admin = user.has_group("base.group_erp_manager")
        is_system = user.has_group("base.group_system")

        user_info = UserInfo(
            id=user.id,
            name=user.name,
            email=user.email,
            login=user.login,
            is_admin=is_admin,
            is_system=is_system,
            groups=group_names,
            partner_id=user.partner_id.id if user.partner_id else 0,
        )

        _logger.info(
            f"User info retrieved successfully for user_id={user.id}, requestId={request_id}"
        )

        # Tạo meta data với LMS metadata
        from odoo.addons.eb_api_core.utils.datetime_utils import format_meta_timestamp
        meta_data = get_meta_data()
        meta_data.update({
            "api_version": "2.0",
            "service": "lms"
        })

        return UserInfoResponse(
            success=True,
            message="User information retrieved successfully",
            data=user_info,
            meta=meta_data,
        )
    except Exception as e:
        _logger.error(f"Error fetching user info: {str(e)}, requestId={request_id}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Failed to get user info: {str(e)}",
        )


@auth_router.post("/refresh", response_model=TokenResponse)
@auto_error_response([400, 401, 500])
@trace()
async def refresh_token(
    refresh_token: str = Body(..., embed=True),
    env: Annotated[Environment, Depends(odoo_env)] = None,
    request_id: Annotated[str, Depends(current_request_id)] = None,
):
    """Làm mới access token bằng refresh token"""
    from odoo.addons.eb_api_auth.dependencies.auth import get_jwt_validator

    _logger.info(f"Processing token refresh request, requestId={request_id}")

    try:
        # Lấy JWT validator
        validator = get_jwt_validator(env=env)

        # Giải mã refresh token sử dụng phương thức _decode của validator
        try:
            payload = validator._decode(refresh_token)

            # Thêm attribute truy vết
            user_id = payload.get("sub")
            add_span_attributes(token_type=payload.get("type"), user_id=user_id)

            # Kiểm tra type token
            if payload.get("type") != "refresh_token":
                _logger.warning(
                    f"Invalid token type: {payload.get('type')}, requestId={request_id}"
                )
                raise api_exception(ErrorCode.INVALID_REQUEST, "Invalid token type")

            # Lấy user ID từ token
            if not user_id:
                _logger.warning(
                    f"Missing user identity in token, requestId={request_id}"
                )
                raise api_exception(
                    ErrorCode.INVALID_TOKEN,
                    "Invalid token - missing user identity",
                )
        except Exception as e:
            add_span_attributes(token_valid=False, error=str(e))
            if isinstance(e, api_exception.__class__):
                raise
            _logger.error(f"Token validation failed: {str(e)}, requestId={request_id}")
            raise api_exception(
                ErrorCode.INVALID_TOKEN,
                f"Invalid refresh token: {str(e)}",
            )

        # Lấy thông tin user
        user = env["res.users"].sudo().browse(int(user_id))
        if not user.exists():
            add_span_attributes(user_exists=False)
            _logger.warning(f"User not found for ID: {user_id}, requestId={request_id}")
            raise api_exception(ErrorCode.UNAUTHORIZED, "User not found")

        # Thêm attribute cho user có tồn tại
        add_span_attributes(user_exists=True, user_login=user.login)

        # Tạo access token mới
        now = int(time.time())
        access_expires = 3600  # 1 giờ

        # Get user role and permissions for new access token
        from odoo.addons.eb_api_auth.dependencies.auth import get_user_role, get_user_permissions
        role = get_user_role(user, env)
        permissions = get_user_permissions(role, user, env)

        # Access token payload with role and permissions
        access_payload = {
            "iat": now,
            "sub": str(user.id),  # Đảm bảo sub là chuỗi
            "name": user.name,
            "login": user.login,
            "role": role,
            "permissions": permissions,
            "type": "access_token",
        }

        _logger.info(
            f"Creating new access token for user: {user.login}, requestId={request_id}"
        )

        # Sử dụng phương thức _encode của validator để tạo token mới
        new_access_token = validator._encode(
            access_payload, validator.secret_key, access_expires
        )

        # Tính thời gian còn lại của refresh token
        expiry = payload.get("exp", 0)
        refresh_remaining = max(0, expiry - now)

        # Tạo OAuth2Token theo chuẩn
        oauth2_token = OAuth2Token(
            access_token=new_access_token,
            token_type="Bearer",
            expires_in=access_expires,
            refresh_token=refresh_token,
            refresh_token_expires_in=refresh_remaining,
            scope="api",
            issued_at=now,
        )

        _logger.info(
            f"Token refreshed successfully for user: {user.login}, requestId={request_id}"
        )
        add_span_attributes(token_refresh_successful=True)

        # Trả về response theo chuẩn API EarnBase
        return TokenResponse(
            success=True,
            message="Token refreshed successfully",
            data=oauth2_token,
            meta=get_meta_data(),
        )

    except Exception as e:
        add_span_attributes(token_refresh_successful=False, error=str(e))
        if isinstance(e, api_exception.__class__):
            raise
        _logger.error(f"Refresh token error: {str(e)}, requestId={request_id}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Token refresh failed: {str(e)}",
        )


@auth_router.get("/verify", response_model=ResponseBase)
@auto_error_response([401])
async def verify_token(
    payload: Annotated[dict, Depends(auth_jwt_authenticated_payload)],
):
    """Xác thực token hiện tại"""
    # Nếu đến đây, token đã được xác thực thành công
    return ResponseBase(
        success=True,
        message="Token is valid",
    )


@auth_router.post("/sign-out", response_model=ResponseBase)
@auto_error_response([401, 500])
@trace()
async def sign_out(
    env: Environment = Depends(odoo_env),
    current_user: object = Depends(get_current_user),
    request_id: Annotated[str, Depends(current_request_id)] = None,
):
    """Đăng xuất người dùng và thu hồi token hiện tại."""
    _logger.info(
        f"Processing sign-out request for user_id={current_user.id}, requestId={request_id}"
    )

    try:
        # Thêm attribute vào span
        add_span_attributes(user_id=current_user.id, token_revocation=True)

        # Lấy token từ HTTP Authorization header (thực hiện trong middleware)
        # và vô hiệu hóa trong DB
        # env['eb_api_auth.token'].invalidate_current_token(current_user.id)

        # Ghi log action
        _logger.info(
            f"User {current_user.login} (ID: {current_user.id}) signed out, token revoked, requestId={request_id}"
        )

        return ResponseBase(
            success=True,
            message="Signed out successfully, token revoked",
            meta=get_meta_data(),
        )
    except Exception as e:
        add_span_attributes(sign_out_successful=False, error=str(e))
        _logger.error(
            f"Sign-out failed for user {current_user.id}: {str(e)}, requestId={request_id}"
        )
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Failed to sign out: {str(e)}",
        )


@auth_router.get("/tokens", response_model=ResponseBase)
@auto_error_response([401, 500])
@trace()
async def list_tokens(
    env: Environment = Depends(odoo_env),
    current_user: object = Depends(get_current_user),
    request_id: Annotated[str, Depends(current_request_id)] = None,
):
    """Liệt kê tất cả token của người dùng hiện tại."""
    _logger.info(
        f"Listing tokens for user_id={current_user.id}, requestId={request_id}"
    )

    try:
        # Thêm attribute vào span
        add_span_attributes(user_id=current_user.id)

        # Tìm các token của user hiện tại
        tokens = (
            env["eb_api_auth.token"].sudo().search([("user_id", "=", current_user.id)])
        )

        # Chuyển đổi các token sang định dạng phù hợp
        token_list = []
        for token in tokens:
            token_list.append(
                {
                    "token_id": token.token_id,
                    "type": token.token_type,
                    "status": token.status,
                    "issued_at": (
                        token.issued_at.isoformat() if token.issued_at else None
                    ),
                    "expiration_date": (
                        token.expiration_date.isoformat()
                        if token.expiration_date
                        else None
                    ),
                    "last_used": (
                        token.last_used.isoformat() if token.last_used else None
                    ),
                    "ip_address": token.ip_address,
                    "user_agent": token.user_agent,
                }
            )

        return ResponseBase(
            success=True,
            message=f"Found {len(token_list)} tokens",
            data={"tokens": token_list},
            meta=get_meta_data(),
        )
    except Exception as e:
        _logger.error(f"Error listing tokens: {str(e)}, requestId={request_id}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Failed to list tokens: {str(e)}",
        )


@auth_router.post("/revoke", response_model=ResponseBase)
@auto_error_response([400, 401, 500])
@trace()
async def revoke_token(
    token_data: dict = Body(...),
    env: Environment = Depends(odoo_env),
    current_user: object = Depends(get_current_user),
    request_id: Annotated[str, Depends(current_request_id)] = None,
):
    """Thu hồi một token cụ thể (phải là token của user hiện tại)."""
    token_id = token_data.get("token_id")
    if not token_id:
        raise api_exception(
            ErrorCode.INVALID_REQUEST,
            "Missing token_id parameter",
        )

    _logger.info(
        f"Revoking token_id={token_id} for user_id={current_user.id}, requestId={request_id}"
    )

    try:
        # Thêm attribute vào span
        add_span_attributes(user_id=current_user.id, token_id=token_id)

        # Tìm token
        token = (
            env["eb_api_auth.token"]
            .sudo()
            .search(
                [("token_id", "=", token_id), ("user_id", "=", current_user.id)],
                limit=1,
            )
        )

        if not token:
            _logger.warning(f"Token not found: {token_id}, requestId={request_id}")
            raise api_exception(
                ErrorCode.NOT_FOUND,
                f"Token {token_id} not found or not owned by you",
            )

        # Thu hồi token
        token.invalidate(reason="user_revoked")

        _logger.info(f"Token {token_id} revoked successfully, requestId={request_id}")
        return ResponseBase(
            success=True,
            message="Token revoked successfully",
            meta=get_meta_data(),
        )
    except Exception as e:
        if isinstance(e, api_exception.__class__):
            raise
        _logger.error(f"Error revoking token: {str(e)}, requestId={request_id}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Failed to revoke token: {str(e)}",
        )


# Device Management Endpoints

@auth_router.post("/device/register", response_model=DeviceRegistrationResponse)
@auto_error_response([401, 500])
@trace()
async def register_device(
    device_request: DeviceRegistrationRequest,
    current_user: Annotated[object, Depends(get_current_user)],
    env: Annotated[Environment, Depends(odoo_env)],
    request_id: Annotated[str, Depends(current_request_id)],
):
    """Register a new device for the current user"""
    _logger.info(
        f"Registering device for user_id={current_user.id}, device_id={device_request.device_info.device_id}, requestId={request_id}"
    )

    try:
        add_span_attributes(
            user_id=current_user.id,
            device_id=device_request.device_info.device_id,
            device_type=device_request.device_info.device_type
        )

        # Tạo hoặc cập nhật device record
        device_data = {
            "user_id": current_user.id,
            "device_id": device_request.device_info.device_id,
            "device_name": device_request.device_info.device_name,
            "device_type": device_request.device_info.device_type,
            "os_name": device_request.device_info.os_name,
            "os_version": device_request.device_info.os_version,
            "app_version": device_request.device_info.app_version,
            "browser_name": device_request.device_info.browser_name,
            "browser_version": device_request.device_info.browser_version,
            "user_agent": device_request.device_info.user_agent,
            "ip_address": device_request.device_info.ip_address,
            "push_token": device_request.push_token,
            "last_seen": datetime.now(),
            "is_active": True,
        }

        response_data = {
            "device_id": device_request.device_info.device_id,
            "status": "registered",
            "registered_at": datetime.now().isoformat(),
            "push_notifications_enabled": bool(device_request.push_token),
        }

        _logger.info(
            f"Device registered successfully for user_id={current_user.id}, device_id={device_request.device_info.device_id}, requestId={request_id}"
        )

        return DeviceRegistrationResponse(
            success=True,
            message="Device registered successfully",
            data=response_data,
            meta=get_meta_data(),
        )
    except Exception as e:
        _logger.error(
            f"Device registration failed for user_id={current_user.id}: {str(e)}, requestId={request_id}"
        )
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Failed to register device: {str(e)}",
        )


# User Registration and Password Management Endpoints

@auth_router.post("/register", response_model=UserRegistrationResponse)
@auto_error_response([400, 409, 500])
@trace()
async def register_user(
    registration_data: UserRegistrationRequest,
    env: Annotated[Environment, Depends(odoo_env)],
    request_id: Annotated[str, Depends(current_request_id)],
):
    """Register a new user account with email verification."""
    _logger.info(
        f"Processing user registration for email: {registration_data.email}, requestId={request_id}"
    )

    try:
        add_span_attributes(
            registration_email=registration_data.email,
            registration_name=registration_data.name,
            terms_accepted=registration_data.terms_accepted
        )

        # Check if email already exists
        existing_user = env["res.users"].sudo().search([
            ("email", "=", registration_data.email)
        ], limit=1)

        if existing_user:
            _logger.warning(f"Registration failed - email already exists: {registration_data.email}, requestId={request_id}")
            raise api_exception(
                ErrorCode.CONFLICT,
                "Email address is already registered"
            )

        # Check if login (username) already exists if email is not used as login
        login = registration_data.email  # Use email as login
        existing_login = env["res.users"].sudo().search([
            ("login", "=", login)
        ], limit=1)

        if existing_login:
            _logger.warning(f"Registration failed - login already exists: {login}, requestId={request_id}")
            raise api_exception(
                ErrorCode.CONFLICT,
                "Username is already taken"
            )

        # Create new user (inactive until email verification)
        user_vals = {
            "name": registration_data.name,
            "login": login,
            "email": registration_data.email,
            "active": False,  # Inactive until email verification
            "groups_id": [(6, 0, [env.ref("base.group_portal").id])],  # Portal user by default
        }

        # Add phone if provided
        if registration_data.phone:
            user_vals["phone"] = registration_data.phone

        # Create user
        new_user = env["res.users"].sudo().create(user_vals)

        # Set password
        new_user.sudo().write({"password": registration_data.password})

        # Generate email verification token
        verification = env["eb_api_auth.email_verification"].sudo().generate_token(
            user_id=new_user.id,
            email=registration_data.email,
            verification_type="registration",
            expires_hours=168,  # 7 days
            ip_address=get_meta_data().get("ip_address"),
            user_agent=get_meta_data().get("user_agent")
        )

        # Send verification email
        verification.send_verification_email()

        add_span_attributes(
            user_created=True,
            user_id=new_user.id,
            verification_token_sent=True
        )

        response_data = {
            "user_id": new_user.id,
            "email": registration_data.email,
            "name": registration_data.name,
            "verification_required": True,
            "verification_expires_hours": 168,
            "message": "Registration successful. Please check your email to verify your account."
        }

        _logger.info(
            f"User registration successful for: {registration_data.email}, user_id={new_user.id}, requestId={request_id}"
        )

        return UserRegistrationResponse(
            success=True,
            message="User registered successfully. Please verify your email to activate your account.",
            data=response_data,
            meta=get_meta_data(),
        )

    except Exception as e:
        add_span_attributes(user_created=False, error=str(e))
        if isinstance(e, api_exception.__class__):
            raise
        _logger.error(f"User registration failed: {str(e)}, requestId={request_id}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Registration failed: {str(e)}",
        )


@auth_router.post("/forgot-password", response_model=ForgotPasswordResponse)
@auto_error_response([400, 404, 500])
@trace()
async def forgot_password(
    forgot_data: ForgotPasswordRequest,
    env: Annotated[Environment, Depends(odoo_env)],
    request_id: Annotated[str, Depends(current_request_id)],
):
    """Request password reset for a user account."""
    _logger.info(
        f"Processing forgot password request for email: {forgot_data.email}, requestId={request_id}"
    )

    try:
        add_span_attributes(
            forgot_password_email=forgot_data.email,
            has_custom_reset_url=bool(forgot_data.reset_url_template)
        )

        # Find user by email
        user = env["res.users"].sudo().search([
            ("email", "=", forgot_data.email),
            ("active", "=", True)
        ], limit=1)

        if not user:
            # For security, don't reveal if email exists or not
            _logger.warning(f"Forgot password request for non-existent email: {forgot_data.email}, requestId={request_id}")
            # Still return success to prevent email enumeration
            response_data = {
                "email": forgot_data.email,
                "message": "If this email is registered, you will receive password reset instructions.",
                "reset_sent": False
            }

            return ForgotPasswordResponse(
                success=True,
                message="If this email is registered, you will receive password reset instructions.",
                data=response_data,
                meta=get_meta_data(),
            )

        # Generate password reset token
        reset_record = env["eb_api_auth.password_reset"].sudo().generate_token(
            user_id=user.id,
            email=forgot_data.email,
            expires_hours=24,  # 24 hours
            ip_address=get_meta_data().get("ip_address"),
            user_agent=get_meta_data().get("user_agent")
        )

        # Send reset email
        reset_record.send_reset_email(reset_url_template=forgot_data.reset_url_template)

        add_span_attributes(
            user_found=True,
            user_id=user.id,
            reset_token_sent=True
        )

        response_data = {
            "email": forgot_data.email,
            "message": "Password reset instructions have been sent to your email.",
            "reset_sent": True,
            "expires_hours": 24
        }

        _logger.info(
            f"Password reset email sent for: {forgot_data.email}, user_id={user.id}, requestId={request_id}"
        )

        return ForgotPasswordResponse(
            success=True,
            message="Password reset instructions have been sent to your email.",
            data=response_data,
            meta=get_meta_data(),
        )

    except Exception as e:
        add_span_attributes(reset_token_sent=False, error=str(e))
        if isinstance(e, api_exception.__class__):
            raise
        _logger.error(f"Forgot password failed: {str(e)}, requestId={request_id}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Failed to process password reset request: {str(e)}",
        )


@auth_router.post("/reset-password", response_model=ResetPasswordResponse)
@auto_error_response([400, 404, 500])
@trace()
async def reset_password(
    reset_data: ResetPasswordRequest,
    env: Annotated[Environment, Depends(odoo_env)],
    request_id: Annotated[str, Depends(current_request_id)],
):
    """Reset user password using reset token."""
    _logger.info(
        f"Processing password reset with token, requestId={request_id}"
    )

    try:
        add_span_attributes(
            has_reset_token=bool(reset_data.token),
            password_length=len(reset_data.new_password)
        )

        # Validate reset token
        reset_record = env["eb_api_auth.password_reset"].sudo().validate_token(reset_data.token)

        user = reset_record.user_id

        add_span_attributes(
            token_valid=True,
            user_id=user.id,
            user_email=user.email
        )

        # Update user password
        user.sudo().write({"password": reset_data.new_password})

        # Mark token as used
        reset_record.mark_as_used(
            reset_ip_address=get_meta_data().get("ip_address"),
            reset_user_agent=get_meta_data().get("user_agent")
        )

        # Invalidate all existing JWT tokens for this user
        existing_tokens = env["eb_api_auth.token"].sudo().search([
            ("user_id", "=", user.id),
            ("status", "=", "active")
        ])
        existing_tokens.write({"status": "revoked"})

        add_span_attributes(
            password_reset_successful=True,
            tokens_invalidated=len(existing_tokens)
        )

        response_data = {
            "user_id": user.id,
            "email": user.email,
            "message": "Password has been reset successfully. Please log in with your new password.",
            "tokens_invalidated": len(existing_tokens)
        }

        _logger.info(
            f"Password reset successful for user_id={user.id}, email={user.email}, requestId={request_id}"
        )

        return ResetPasswordResponse(
            success=True,
            message="Password has been reset successfully. Please log in with your new password.",
            data=response_data,
            meta=get_meta_data(),
        )

    except Exception as e:
        add_span_attributes(password_reset_successful=False, error=str(e))
        if isinstance(e, api_exception.__class__):
            raise
        _logger.error(f"Password reset failed: {str(e)}, requestId={request_id}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Failed to reset password: {str(e)}",
        )


@auth_router.post("/verify-email", response_model=EmailVerificationResponse)
@auto_error_response([400, 404, 500])
@trace()
async def verify_email(
    verification_data: EmailVerificationRequest,
    env: Annotated[Environment, Depends(odoo_env)],
    request_id: Annotated[str, Depends(current_request_id)],
):
    """Verify user email address using verification token."""
    _logger.info(
        f"Processing email verification with token, requestId={request_id}"
    )

    try:
        add_span_attributes(
            has_verification_token=bool(verification_data.token)
        )

        # Validate verification token
        verification_record = env["eb_api_auth.email_verification"].sudo().validate_token(verification_data.token)

        user = verification_record.user_id

        add_span_attributes(
            token_valid=True,
            user_id=user.id,
            user_email=user.email,
            verification_type=verification_record.verification_type
        )

        # Activate user account
        user.sudo().write({"active": True})

        # Mark verification token as used
        verification_record.mark_as_used()

        add_span_attributes(
            email_verification_successful=True,
            user_activated=True
        )

        response_data = {
            "user_id": user.id,
            "email": user.email,
            "name": user.name,
            "verification_type": verification_record.verification_type,
            "account_activated": True,
            "message": "Email verified successfully. Your account is now active."
        }

        _logger.info(
            f"Email verification successful for user_id={user.id}, email={user.email}, requestId={request_id}"
        )

        return EmailVerificationResponse(
            success=True,
            message="Email verified successfully. Your account is now active.",
            data=response_data,
            meta=get_meta_data(),
        )

    except Exception as e:
        add_span_attributes(email_verification_successful=False, error=str(e))
        if isinstance(e, api_exception.__class__):
            raise
        _logger.error(f"Email verification failed: {str(e)}, requestId={request_id}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Failed to verify email: {str(e)}",
        )


@auth_router.post("/change-password", response_model=EnhancedPasswordChangeResponse)
@auto_error_response([400, 401, 500])
@trace()
async def change_password(
    change_data: EnhancedPasswordChangeRequest,
    current_user: Annotated[object, Depends(get_current_user)],
    env: Annotated[Environment, Depends(odoo_env)],
    request_id: Annotated[str, Depends(current_request_id)],
):
    """Change user password (requires authentication)."""
    _logger.info(
        f"Processing password change for user_id={current_user.id}, requestId={request_id}"
    )

    try:
        add_span_attributes(
            user_id=current_user.id,
            logout_all_devices=change_data.logout_all_devices,
            send_notification=change_data.send_notification
        )

        # Verify current password
        try:
            env["res.users"].sudo().browse(current_user.id)._check_credentials(change_data.current_password, env)
        except Exception:
            _logger.warning(f"Password change failed - invalid current password for user_id={current_user.id}, requestId={request_id}")
            raise api_exception(
                ErrorCode.UNAUTHORIZED,
                "Current password is incorrect"
            )

        # Update password
        current_user.sudo().write({"password": change_data.new_password})

        tokens_invalidated = 0
        if change_data.logout_all_devices:
            # Invalidate all existing JWT tokens for this user
            existing_tokens = env["eb_api_auth.token"].sudo().search([
                ("user_id", "=", current_user.id),
                ("status", "=", "active")
            ])
            existing_tokens.write({"status": "revoked"})
            tokens_invalidated = len(existing_tokens)

        # Send notification email if requested
        notification_sent = False
        if change_data.send_notification:
            try:
                # Get password change notification template
                template = env.ref('eb_api_auth.password_change_notification_template', raise_if_not_found=False)
                if template:
                    template.send_mail(current_user.id, force_send=True)
                    notification_sent = True
            except Exception as e:
                _logger.warning(f"Failed to send password change notification: {str(e)}, requestId={request_id}")

        add_span_attributes(
            password_change_successful=True,
            tokens_invalidated=tokens_invalidated,
            notification_sent=notification_sent
        )

        response_data = {
            "user_id": current_user.id,
            "email": current_user.email,
            "message": "Password changed successfully.",
            "logout_all_devices": change_data.logout_all_devices,
            "tokens_invalidated": tokens_invalidated,
            "notification_sent": notification_sent
        }

        _logger.info(
            f"Password change successful for user_id={current_user.id}, tokens_invalidated={tokens_invalidated}, requestId={request_id}"
        )

        return EnhancedPasswordChangeResponse(
            success=True,
            message="Password changed successfully.",
            data=response_data,
            meta=get_meta_data(),
        )

    except Exception as e:
        add_span_attributes(password_change_successful=False, error=str(e))
        if isinstance(e, api_exception.__class__):
            raise
        _logger.error(f"Password change failed: {str(e)}, requestId={request_id}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Failed to change password: {str(e)}",
        )
