# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging

from fastapi import APIRouter
from odoo.addons.eb_api_core.utils.decorators import register_api_module

_logger = logging.getLogger(__name__)


@register_api_module(
    module_name="auth",
    description="Authentication API for JWT token management",
    version="1.0",
    app_name="auth_api",
)
class AuthAPI:
    """API module for authentication endpoints"""

    # Router instance
    router = APIRouter(tags=["auth"], prefix="/auth")

    # Import routers để đăng ký endpoints
    from odoo.addons.eb_api_auth.routers.auth_router import auth_router

    # Đăng ký sub-routers
    router.include_router(auth_router)
