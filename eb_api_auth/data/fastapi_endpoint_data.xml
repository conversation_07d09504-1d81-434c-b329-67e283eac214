<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright 2025 EarnBase Technology <https://earnbase.io> -->
<!-- License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html) -->
<odoo noupdate="1">
    
    <!-- Tạo FastAPI Endpoint cho API Authentication -->
    <record id="auth_api_endpoint" model="fastapi.endpoint">
        <field name="name">EB Auth API</field>
        <field name="app">eb_auth</field>
        <field name="description">API Authentication endpoints for JWT token management</field>
        <field name="root_path">/api/v1/auth</field>
        <field name="registry_sync" eval="False"/>
        <field name="user_id" ref="base.user_root"/>
    </record>

</odoo> 