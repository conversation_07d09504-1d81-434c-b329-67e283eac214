<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright 2025 EarnBase Technology <https://earnbase.io> -->
<!-- License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html) -->
<odoo noupdate="1">

    <!-- Email Verification Template -->
    <record id="email_verification_template" model="mail.template">
        <field name="name">Email Verification - EarnBase API Auth</field>
        <field name="model_id" ref="model_eb_api_auth_email_verification"/>
        <field name="subject"><PERSON><PERSON><PERSON> thực email của bạn - ${object.user_id.company_id.name}</field>
        <field name="email_from">${(object.user_id.company_id.email or user.email)|safe}</field>
        <field name="email_to">${object.email}</field>
        <field name="lang">${object.user_id.lang}</field>
        <field name="auto_delete" eval="True"/>
        <field name="body_html" type="html">
<div style="margin: 0px; padding: 0px; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f8f9fa;">
    <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; background-color: #f8f9fa;">
        <tr>
            <td align="center" style="padding: 40px 20px;">
                <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; max-width: 600px; background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <!-- Header -->
                    <tr>
                        <td style="padding: 40px 40px 20px 40px; text-align: center; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 8px 8px 0 0;">
                            <h1 style="margin: 0; color: #ffffff; font-size: 28px; font-weight: 600;">
                                🔐 Xác thực Email
                            </h1>
                            <p style="margin: 10px 0 0 0; color: #e8f0fe; font-size: 16px;">
                                ${object.user_id.company_id.name}
                            </p>
                        </td>
                    </tr>
                    
                    <!-- Content -->
                    <tr>
                        <td style="padding: 40px;">
                            <h2 style="margin: 0 0 20px 0; color: #333333; font-size: 24px; font-weight: 600;">
                                Chào ${object.user_id.name}!
                            </h2>
                            
                            <p style="margin: 0 0 20px 0; color: #666666; font-size: 16px; line-height: 1.6;">
                                Cảm ơn bạn đã đăng ký tài khoản. Để hoàn tất quá trình đăng ký, vui lòng xác thực địa chỉ email của bạn bằng cách nhấp vào nút bên dưới:
                            </p>
                            
                            <!-- Verification Button -->
                            <div style="text-align: center; margin: 30px 0;">
                                <a href="${ctx.get('verification_url', '#')}" 
                                   style="display: inline-block; padding: 15px 30px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: #ffffff; text-decoration: none; border-radius: 6px; font-size: 16px; font-weight: 600; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);">
                                    ✅ Xác thực Email
                                </a>
                            </div>
                            
                            <p style="margin: 20px 0; color: #666666; font-size: 14px; line-height: 1.6;">
                                Hoặc copy và paste link sau vào trình duyệt của bạn:
                            </p>
                            <p style="margin: 0 0 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 4px; font-family: monospace; font-size: 14px; word-break: break-all; color: #495057;">
                                ${ctx.get('verification_url', '#')}
                            </p>
                            
                            <div style="margin: 30px 0; padding: 20px; background-color: #fff3cd; border-left: 4px solid #ffc107; border-radius: 4px;">
                                <p style="margin: 0; color: #856404; font-size: 14px;">
                                    <strong>⚠️ Lưu ý quan trọng:</strong><br/>
                                    • Link xác thực này sẽ hết hạn sau <strong>7 ngày</strong><br/>
                                    • Chỉ sử dụng được một lần<br/>
                                    • Không chia sẻ link này với người khác
                                </p>
                            </div>
                            
                            <p style="margin: 20px 0 0 0; color: #666666; font-size: 14px; line-height: 1.6;">
                                Nếu bạn không đăng ký tài khoản này, vui lòng bỏ qua email này.
                            </p>
                        </td>
                    </tr>
                    
                    <!-- Footer -->
                    <tr>
                        <td style="padding: 30px 40px; background-color: #f8f9fa; border-radius: 0 0 8px 8px; text-align: center;">
                            <p style="margin: 0 0 10px 0; color: #6c757d; font-size: 14px;">
                                Email được gửi từ ${object.user_id.company_id.name}
                            </p>
                            <p style="margin: 0; color: #6c757d; font-size: 12px;">
                                © ${datetime.datetime.now().year} ${object.user_id.company_id.name}. All rights reserved.
                            </p>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</div>
        </field>
    </record>

    <!-- Password Reset Template -->
    <record id="password_reset_template" model="mail.template">
        <field name="name">Password Reset - EarnBase API Auth</field>
        <field name="model_id" ref="model_eb_api_auth_password_reset"/>
        <field name="subject">Đặt lại mật khẩu - ${object.user_id.company_id.name}</field>
        <field name="email_from">${(object.user_id.company_id.email or user.email)|safe}</field>
        <field name="email_to">${object.email}</field>
        <field name="lang">${object.user_id.lang}</field>
        <field name="auto_delete" eval="True"/>
        <field name="body_html" type="html">
<div style="margin: 0px; padding: 0px; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f8f9fa;">
    <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; background-color: #f8f9fa;">
        <tr>
            <td align="center" style="padding: 40px 20px;">
                <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; max-width: 600px; background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <!-- Header -->
                    <tr>
                        <td style="padding: 40px 40px 20px 40px; text-align: center; background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); border-radius: 8px 8px 0 0;">
                            <h1 style="margin: 0; color: #ffffff; font-size: 28px; font-weight: 600;">
                                🔑 Đặt lại mật khẩu
                            </h1>
                            <p style="margin: 10px 0 0 0; color: #ffe8e8; font-size: 16px;">
                                ${object.user_id.company_id.name}
                            </p>
                        </td>
                    </tr>
                    
                    <!-- Content -->
                    <tr>
                        <td style="padding: 40px;">
                            <h2 style="margin: 0 0 20px 0; color: #333333; font-size: 24px; font-weight: 600;">
                                Chào ${object.user_id.name}!
                            </h2>
                            
                            <p style="margin: 0 0 20px 0; color: #666666; font-size: 16px; line-height: 1.6;">
                                Chúng tôi nhận được yêu cầu đặt lại mật khẩu cho tài khoản của bạn. Nhấp vào nút bên dưới để tạo mật khẩu mới:
                            </p>
                            
                            <!-- Reset Button -->
                            <div style="text-align: center; margin: 30px 0;">
                                <a href="${ctx.get('reset_url', '#')}" 
                                   style="display: inline-block; padding: 15px 30px; background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); color: #ffffff; text-decoration: none; border-radius: 6px; font-size: 16px; font-weight: 600; box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);">
                                    🔑 Đặt lại mật khẩu
                                </a>
                            </div>
                            
                            <p style="margin: 20px 0; color: #666666; font-size: 14px; line-height: 1.6;">
                                Hoặc copy và paste link sau vào trình duyệt của bạn:
                            </p>
                            <p style="margin: 0 0 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 4px; font-family: monospace; font-size: 14px; word-break: break-all; color: #495057;">
                                ${ctx.get('reset_url', '#')}
                            </p>
                            
                            <div style="margin: 30px 0; padding: 20px; background-color: #f8d7da; border-left: 4px solid #dc3545; border-radius: 4px;">
                                <p style="margin: 0; color: #721c24; font-size: 14px;">
                                    <strong>🚨 Bảo mật quan trọng:</strong><br/>
                                    • Link này sẽ hết hạn sau <strong>24 giờ</strong><br/>
                                    • Chỉ sử dụng được một lần<br/>
                                    • Không chia sẻ link này với bất kỳ ai<br/>
                                    • Nếu không phải bạn yêu cầu, hãy bỏ qua email này
                                </p>
                            </div>
                            
                            <div style="margin: 20px 0; padding: 15px; background-color: #d1ecf1; border-left: 4px solid #17a2b8; border-radius: 4px;">
                                <p style="margin: 0; color: #0c5460; font-size: 14px;">
                                    <strong>💡 Mẹo bảo mật:</strong> Hãy chọn mật khẩu mạnh có ít nhất 8 ký tự, bao gồm chữ cái và số.
                                </p>
                            </div>
                            
                            <p style="margin: 20px 0 0 0; color: #666666; font-size: 14px; line-height: 1.6;">
                                Thông tin yêu cầu:<br/>
                                • Thời gian: ${object.created_at.strftime('%d/%m/%Y %H:%M:%S')}<br/>
                                • IP Address: ${object.ip_address or 'Không xác định'}
                            </p>
                        </td>
                    </tr>
                    
                    <!-- Footer -->
                    <tr>
                        <td style="padding: 30px 40px; background-color: #f8f9fa; border-radius: 0 0 8px 8px; text-align: center;">
                            <p style="margin: 0 0 10px 0; color: #6c757d; font-size: 14px;">
                                Email được gửi từ ${object.user_id.company_id.name}
                            </p>
                            <p style="margin: 0; color: #6c757d; font-size: 12px;">
                                © ${datetime.datetime.now().year} ${object.user_id.company_id.name}. All rights reserved.
                            </p>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</div>
        </field>
    </record>

    <!-- Password Change Notification Template -->
    <record id="password_change_notification_template" model="mail.template">
        <field name="name">Password Change Notification - EarnBase API Auth</field>
        <field name="model_id" ref="base.model_res_users"/>
        <field name="subject">Mật khẩu đã được thay đổi - ${object.company_id.name}</field>
        <field name="email_from">${(object.company_id.email or user.email)|safe}</field>
        <field name="email_to">${object.email}</field>
        <field name="lang">${object.lang}</field>
        <field name="auto_delete" eval="True"/>
        <field name="body_html" type="html">
<div style="margin: 0px; padding: 0px; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f8f9fa;">
    <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; background-color: #f8f9fa;">
        <tr>
            <td align="center" style="padding: 40px 20px;">
                <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; max-width: 600px; background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <!-- Header -->
                    <tr>
                        <td style="padding: 40px 40px 20px 40px; text-align: center; background: linear-gradient(135deg, #28a745 0%, #20c997 100%); border-radius: 8px 8px 0 0;">
                            <h1 style="margin: 0; color: #ffffff; font-size: 28px; font-weight: 600;">
                                ✅ Mật khẩu đã thay đổi
                            </h1>
                            <p style="margin: 10px 0 0 0; color: #e8f5e8; font-size: 16px;">
                                ${object.company_id.name}
                            </p>
                        </td>
                    </tr>

                    <!-- Content -->
                    <tr>
                        <td style="padding: 40px;">
                            <h2 style="margin: 0 0 20px 0; color: #333333; font-size: 24px; font-weight: 600;">
                                Chào ${object.name}!
                            </h2>

                            <p style="margin: 0 0 20px 0; color: #666666; font-size: 16px; line-height: 1.6;">
                                Chúng tôi xác nhận rằng mật khẩu tài khoản của bạn đã được thay đổi thành công.
                            </p>

                            <div style="margin: 30px 0; padding: 20px; background-color: #d4edda; border-left: 4px solid #28a745; border-radius: 4px;">
                                <p style="margin: 0; color: #155724; font-size: 14px;">
                                    <strong>✅ Thông tin thay đổi:</strong><br/>
                                    • Thời gian: ${datetime.datetime.now().strftime('%d/%m/%Y %H:%M:%S')}<br/>
                                    • Tài khoản: ${object.email}<br/>
                                    • Trạng thái: Thành công
                                </p>
                            </div>

                            <div style="margin: 20px 0; padding: 20px; background-color: #fff3cd; border-left: 4px solid #ffc107; border-radius: 4px;">
                                <p style="margin: 0; color: #856404; font-size: 14px;">
                                    <strong>🔒 Lưu ý bảo mật:</strong><br/>
                                    • Nếu bạn không thực hiện thay đổi này, vui lòng liên hệ ngay với chúng tôi<br/>
                                    • Tất cả phiên đăng nhập khác có thể đã bị đăng xuất<br/>
                                    • Hãy đăng nhập lại với mật khẩu mới
                                </p>
                            </div>

                            <p style="margin: 20px 0 0 0; color: #666666; font-size: 14px; line-height: 1.6;">
                                Nếu bạn cần hỗ trợ, vui lòng liên hệ với đội ngũ hỗ trợ của chúng tôi.
                            </p>
                        </td>
                    </tr>

                    <!-- Footer -->
                    <tr>
                        <td style="padding: 30px 40px; background-color: #f8f9fa; border-radius: 0 0 8px 8px; text-align: center;">
                            <p style="margin: 0 0 10px 0; color: #6c757d; font-size: 14px;">
                                Email được gửi từ ${object.company_id.name}
                            </p>
                            <p style="margin: 0; color: #6c757d; font-size: 12px;">
                                © ${datetime.datetime.now().year} ${object.company_id.name}. All rights reserved.
                            </p>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</div>
        </field>
    </record>

</odoo>
