# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

"""
Enhanced Authentication Schemas

Provides advanced authentication schemas for:
- Enhanced login with device tracking
- Multi-factor authentication
- Device management
- Session management
- Role-based authentication
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, EmailStr, field_validator

from odoo.addons.eb_api_core.schemas.base import ResponseBase


class DeviceInfo(BaseModel):
    """Device information schema."""
    device_id: str = Field(..., description="Unique device identifier")
    device_name: Optional[str] = Field(None, description="Device name")
    device_type: str = Field(..., description="Device type (mobile, tablet, desktop)")
    os_name: Optional[str] = Field(None, description="Operating system name")
    os_version: Optional[str] = Field(None, description="Operating system version")
    app_version: Optional[str] = Field(None, description="Application version")
    browser_name: Optional[str] = Field(None, description="Browser name")
    browser_version: Optional[str] = Field(None, description="Browser version")
    user_agent: Optional[str] = Field(None, description="User agent string")
    ip_address: Optional[str] = Field(None, description="IP address")
    location: Optional[Dict[str, Any]] = Field(None, description="Location data")
    
    @field_validator('device_type')
    @classmethod
    def validate_device_type(cls, v):
        """Validate device type."""
        allowed_types = ['mobile', 'tablet', 'desktop', 'unknown']
        if v not in allowed_types:
            raise ValueError(f"Device type must be one of: {', '.join(allowed_types)}")
        return v


class EnhancedLoginRequest(BaseModel):
    """Enhanced login request with device tracking."""
    email: EmailStr = Field(..., description="User email")
    password: str = Field(..., description="User password")
    device_info: Optional[DeviceInfo] = Field(None, description="Device information")
    remember_me: bool = Field(False, description="Remember login")
    mfa_code: Optional[str] = Field(None, description="Multi-factor authentication code")
    
    @field_validator('password')
    @classmethod
    def validate_password(cls, v):
        """Validate password."""
        if len(v) < 6:
            raise ValueError("Password must be at least 6 characters long")
        return v


class EnhancedLoginResponse(ResponseBase[Dict[str, Any]]):
    """Enhanced login response."""
    pass


class DeviceRegistrationRequest(BaseModel):
    """Device registration request."""
    device_info: DeviceInfo = Field(..., description="Device information")
    push_token: Optional[str] = Field(None, description="Push notification token")
    
    
class DeviceRegistrationResponse(ResponseBase[Dict[str, Any]]):
    """Device registration response."""
    pass


class SessionInfo(BaseModel):
    """Session information schema."""
    session_id: str = Field(..., description="Session ID")
    user_id: int = Field(..., description="User ID")
    device_id: Optional[str] = Field(None, description="Device ID")
    created_at: datetime = Field(..., description="Session creation time")
    last_activity: datetime = Field(..., description="Last activity time")
    expires_at: Optional[datetime] = Field(None, description="Session expiration time")
    is_active: bool = Field(True, description="Session status")
    ip_address: Optional[str] = Field(None, description="IP address")
    user_agent: Optional[str] = Field(None, description="User agent")


class SessionListResponse(ResponseBase[List[SessionInfo]]):
    """Session list response."""
    pass


class LogoutRequest(BaseModel):
    """Logout request."""
    device_id: Optional[str] = Field(None, description="Device ID to logout from")
    logout_all_devices: bool = Field(False, description="Logout from all devices")


class LogoutResponse(ResponseBase[Dict[str, Any]]):
    """Logout response."""
    pass


class RefreshTokenRequest(BaseModel):
    """Refresh token request."""
    refresh_token: str = Field(..., description="Refresh token")
    device_id: Optional[str] = Field(None, description="Device ID")


class RefreshTokenResponse(ResponseBase[Dict[str, Any]]):
    """Refresh token response."""
    pass


class PasswordChangeRequest(BaseModel):
    """Password change request."""
    current_password: str = Field(..., description="Current password")
    new_password: str = Field(..., description="New password")
    confirm_password: str = Field(..., description="Confirm new password")
    
    @field_validator('new_password')
    @classmethod
    def validate_new_password(cls, v):
        """Validate new password."""
        if len(v) < 8:
            raise ValueError("New password must be at least 8 characters long")
        return v
    
    @field_validator('confirm_password')
    @classmethod
    def validate_confirm_password(cls, v, info):
        """Validate password confirmation."""
        if 'new_password' in info.data and v != info.data['new_password']:
            raise ValueError("Password confirmation does not match")
        return v


class PasswordChangeResponse(ResponseBase[Dict[str, Any]]):
    """Password change response."""
    pass


class UserProfileInfo(BaseModel):
    """User profile information."""
    id: int = Field(..., description="User ID")
    name: str = Field(..., description="User name")
    email: str = Field(..., description="User email")
    login: str = Field(..., description="User login")
    active: bool = Field(..., description="User status")
    groups: List[str] = Field(default_factory=list, description="User groups")
    roles: List[str] = Field(default_factory=list, description="User roles")
    permissions: List[str] = Field(default_factory=list, description="User permissions")
    last_login: Optional[datetime] = Field(None, description="Last login time")
    created_at: Optional[datetime] = Field(None, description="Account creation time")


class UserProfileResponse(ResponseBase[UserProfileInfo]):
    """User profile response."""
    pass


class MFASetupRequest(BaseModel):
    """Multi-factor authentication setup request."""
    method: str = Field(..., description="MFA method (totp, sms, email)")
    phone: Optional[str] = Field(None, description="Phone number for SMS")
    
    @field_validator('method')
    @classmethod
    def validate_method(cls, v):
        """Validate MFA method."""
        allowed_methods = ['totp', 'sms', 'email']
        if v not in allowed_methods:
            raise ValueError(f"MFA method must be one of: {', '.join(allowed_methods)}")
        return v


class MFASetupResponse(ResponseBase[Dict[str, Any]]):
    """MFA setup response."""
    pass


class MFAVerifyRequest(BaseModel):
    """MFA verification request."""
    code: str = Field(..., description="MFA code")
    method: str = Field(..., description="MFA method")


class MFAVerifyResponse(ResponseBase[Dict[str, Any]]):
    """MFA verification response."""
    pass


class DeviceListResponse(ResponseBase[List[Dict[str, Any]]]):
    """Device list response."""
    pass


class DeviceRevokeRequest(BaseModel):
    """Device revoke request."""
    device_id: str = Field(..., description="Device ID to revoke")


class DeviceRevokeResponse(ResponseBase[Dict[str, Any]]):
    """Device revoke response."""
    pass


class SecurityEventInfo(BaseModel):
    """Security event information."""
    event_type: str = Field(..., description="Event type")
    timestamp: datetime = Field(..., description="Event timestamp")
    ip_address: Optional[str] = Field(None, description="IP address")
    device_info: Optional[Dict[str, Any]] = Field(None, description="Device information")
    details: Optional[Dict[str, Any]] = Field(None, description="Event details")


class SecurityEventsResponse(ResponseBase[List[SecurityEventInfo]]):
    """Security events response."""
    pass
