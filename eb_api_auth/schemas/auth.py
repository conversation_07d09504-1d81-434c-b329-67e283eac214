# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

"""
Enhanced Authentication Schemas

Provides advanced authentication schemas for:
- Enhanced login with device tracking
- Multi-factor authentication
- Device management
- Session management
- Role-based authentication
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, EmailStr, field_validator

# Sử dụng các models từ eb_api_core thay vì định nghĩa lại
from odoo.addons.eb_api_core.schemas.auth import (  # noqa: F401
    LoginRequest,
    TokenRequest,
    TokenRevokeRequest,
    UserInfo,
)
from odoo.addons.eb_api_core.schemas.base import ResponseBase


# Enhanced Authentication Schemas

class DeviceInfo(BaseModel):
    """Device information schema."""
    device_id: str = Field(
        ...,
        description="Unique device identifier",
        examples=["test-device-001", "iphone-12-pro-max", "samsung-galaxy-s21"]
    )
    device_name: Optional[str] = Field(
        None,
        description="Device name",
        examples=["iPhone 12 Pro Max", "Samsung Galaxy S21", "MacBook Pro 2023"]
    )
    device_type: str = Field(
        ...,
        description="Device type (mobile, tablet, desktop, unknown)",
        examples=["mobile", "tablet", "desktop"]
    )
    os_name: Optional[str] = Field(
        None,
        description="Operating system name",
        examples=["iOS", "Android", "Windows", "macOS", "Linux"]
    )
    os_version: Optional[str] = Field(
        None,
        description="Operating system version",
        examples=["15.7", "13.0", "11.0", "13.2.1"]
    )
    app_version: Optional[str] = Field(
        None,
        description="Application version",
        examples=["1.0.0", "2.1.3", "3.0.0-beta"]
    )
    browser_name: Optional[str] = Field(
        None,
        description="Browser name",
        examples=["Safari", "Chrome", "Firefox", "Edge"]
    )
    browser_version: Optional[str] = Field(
        None,
        description="Browser version",
        examples=["16.6", "118.0.5993.88", "119.0"]
    )
    user_agent: Optional[str] = Field(
        None,
        description="User agent string",
        examples=[
            "Mozilla/5.0 (iPhone; CPU iPhone OS 15_7 like Mac OS X) AppleWebKit/605.1.15",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        ]
    )
    ip_address: Optional[str] = Field(
        None,
        description="IP address",
        examples=["*************", "********", "***************"]
    )
    location: Optional[Dict[str, Any]] = Field(
        None,
        description="Location data",
        examples=[
            {"latitude": 21.0285, "longitude": 105.8542, "city": "Hanoi", "country": "Vietnam"},
            {"latitude": 10.8231, "longitude": 106.6297, "city": "Ho Chi Minh", "country": "Vietnam"}
        ]
    )

    @field_validator('device_type')
    @classmethod
    def validate_device_type(cls, v):
        """Validate device type."""
        allowed_types = ['mobile', 'tablet', 'desktop', 'unknown']
        if v not in allowed_types:
            raise ValueError(f"Device type must be one of: {', '.join(allowed_types)}")
        return v


class UnifiedLoginRequest(BaseModel):
    """Unified login request - username can be either username or email."""
    username: str = Field(
        ...,
        description="Username or email address",
        examples=["admin", "user123", "instructor01", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
    )
    password: str = Field(
        ...,
        description="User password (minimum 6 characters)",
        examples=["earnbase@2025", "mypassword123", "securepass"]
    )
    device_info: Optional[DeviceInfo] = Field(
        None,
        description="Device information (optional for enhanced tracking)"
    )
    remember_me: bool = Field(
        False,
        description="Remember login session for extended period"
    )
    mfa_code: Optional[str] = Field(
        None,
        description="Multi-factor authentication code (6 digits)",
        examples=["123456", "789012", "456789"]
    )

    @field_validator('password')
    @classmethod
    def validate_password(cls, v):
        """Validate password."""
        if len(v) < 6:
            raise ValueError("Password must be at least 6 characters long")
        return v

    @field_validator('username')
    @classmethod
    def validate_username(cls, v):
        """Validate username (can be username or email)."""
        if not v or len(v.strip()) == 0:
            raise ValueError("Username or email is required")
        return v.strip()


class EnhancedTokenResponse(ResponseBase[Dict[str, Any]]):
    """Enhanced token response with additional information."""
    pass


class DeviceRegistrationRequest(BaseModel):
    """Device registration request."""
    device_info: DeviceInfo = Field(..., description="Device information")
    push_token: Optional[str] = Field(
        None,
        description="Push notification token",
        examples=[
            "dGVzdC1wdXNoLXRva2VuLTEyMzQ1Ng==",
            "fcm:eHample_token_here_123456789",
            "apns:device_token_example_abcdef"
        ]
    )


class DeviceRegistrationResponse(ResponseBase[Dict[str, Any]]):
    """Device registration response."""
    pass


class SessionInfo(BaseModel):
    """Session information schema."""
    session_id: str = Field(
        ...,
        description="Session ID",
        examples=["sess_abc123def456", "session_789xyz012", "s_**********abcdef"]
    )
    user_id: int = Field(
        ...,
        description="User ID",
        examples=[1, 42, 123, 999]
    )
    device_id: Optional[str] = Field(
        None,
        description="Device ID",
        examples=["test-device-001", "iphone-12-pro-max", "samsung-galaxy-s21"]
    )
    created_at: datetime = Field(
        ...,
        description="Session creation time",
        examples=["2025-06-16T10:30:00Z", "2025-06-15T14:22:15Z"]
    )
    last_activity: datetime = Field(
        ...,
        description="Last activity time",
        examples=["2025-06-16T15:45:30Z", "2025-06-16T09:12:45Z"]
    )
    expires_at: Optional[datetime] = Field(
        None,
        description="Session expiration time",
        examples=["2025-06-23T10:30:00Z", "2025-07-16T10:30:00Z"]
    )
    is_active: bool = Field(
        True,
        description="Session status",
        examples=[True, False]
    )
    ip_address: Optional[str] = Field(
        None,
        description="IP address",
        examples=["*************", "********", "***************"]
    )
    user_agent: Optional[str] = Field(
        None,
        description="User agent",
        examples=[
            "Mozilla/5.0 (iPhone; CPU iPhone OS 15_7 like Mac OS X) AppleWebKit/605.1.15",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        ]
    )


class SessionListResponse(ResponseBase[List[SessionInfo]]):
    """Session list response."""
    pass


class LogoutRequest(BaseModel):
    """Logout request."""
    device_id: Optional[str] = Field(
        None,
        description="Device ID to logout from",
        examples=["test-device-001", "iphone-12-pro-max", "samsung-galaxy-s21"]
    )
    logout_all_devices: bool = Field(
        False,
        description="Logout from all devices",
        examples=[False, True]
    )


class LogoutResponse(ResponseBase[Dict[str, Any]]):
    """Logout response."""
    pass


class RefreshTokenRequest(BaseModel):
    """Refresh token request."""
    refresh_token: str = Field(
        ...,
        description="Refresh token",
        examples=[
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************",
            "refresh_token_example_abc123def456",
            "rt_**********abcdef"
        ]
    )
    device_id: Optional[str] = Field(
        None,
        description="Device ID",
        examples=["test-device-001", "iphone-12-pro-max", "samsung-galaxy-s21"]
    )


class RefreshTokenResponse(ResponseBase[Dict[str, Any]]):
    """Refresh token response."""
    pass


class PasswordChangeRequest(BaseModel):
    """Password change request."""
    current_password: str = Field(
        ...,
        description="Current password",
        examples=["oldpassword123", "currentpass", "myoldpassword"]
    )
    new_password: str = Field(
        ...,
        description="New password",
        examples=["newpassword123", "mynewsecurepass", "strongpassword2025"]
    )
    confirm_password: str = Field(
        ...,
        description="Confirm new password",
        examples=["newpassword123", "mynewsecurepass", "strongpassword2025"]
    )

    @field_validator('new_password')
    @classmethod
    def validate_new_password(cls, v):
        """Validate new password."""
        if len(v) < 8:
            raise ValueError("New password must be at least 8 characters long")
        return v

    @field_validator('confirm_password')
    @classmethod
    def validate_confirm_password(cls, v, info):
        """Validate password confirmation."""
        if 'new_password' in info.data and v != info.data['new_password']:
            raise ValueError("Password confirmation does not match")
        return v


class PasswordChangeResponse(ResponseBase[Dict[str, Any]]):
    """Password change response."""
    pass


class UserProfileInfo(BaseModel):
    """User profile information."""
    id: int = Field(
        ...,
        description="User ID",
        examples=[1, 42, 123, 999]
    )
    name: str = Field(
        ...,
        description="User name",
        examples=["Nguyễn Văn A", "Trần Thị B", "Administrator", "John Doe"]
    )
    email: str = Field(
        ...,
        description="User email",
        examples=["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
    )
    login: str = Field(
        ...,
        description="User login",
        examples=["admin", "user123", "instructor01", "student_001"]
    )
    active: bool = Field(
        ...,
        description="User status",
        examples=[True, False]
    )
    groups: List[str] = Field(
        default_factory=list,
        description="User groups",
        examples=[
            ["User", "Student"],
            ["User", "Instructor", "Manager"],
            ["User", "Administrator"]
        ]
    )
    roles: List[str] = Field(
        default_factory=list,
        description="User roles",
        examples=[
            ["student"],
            ["instructor", "course_manager"],
            ["admin", "super_user"]
        ]
    )
    permissions: List[str] = Field(
        default_factory=list,
        description="User permissions",
        examples=[
            ["read_course", "submit_assignment"],
            ["create_lesson", "grade_assignment", "manage_class"],
            ["manage_users", "system_config", "view_reports"]
        ]
    )
    last_login: Optional[datetime] = Field(
        None,
        description="Last login time",
        examples=["2025-06-16T10:30:00Z", "2025-06-15T14:22:15Z"]
    )
    created_at: Optional[datetime] = Field(
        None,
        description="Account creation time",
        examples=["2025-01-15T08:00:00Z", "2024-12-01T09:30:00Z"]
    )


class UserProfileResponse(ResponseBase[UserProfileInfo]):
    """User profile response."""
    pass


class MFASetupRequest(BaseModel):
    """Multi-factor authentication setup request."""
    method: str = Field(
        ...,
        description="MFA method (totp, sms, email)",
        examples=["totp", "sms", "email"]
    )
    phone: Optional[str] = Field(
        None,
        description="Phone number for SMS",
        examples=["+***********", "+***********", "+**********"]
    )

    @field_validator('method')
    @classmethod
    def validate_method(cls, v):
        """Validate MFA method."""
        allowed_methods = ['totp', 'sms', 'email']
        if v not in allowed_methods:
            raise ValueError(f"MFA method must be one of: {', '.join(allowed_methods)}")
        return v


class MFASetupResponse(ResponseBase[Dict[str, Any]]):
    """MFA setup response."""
    pass


class MFAVerifyRequest(BaseModel):
    """MFA verification request."""
    code: str = Field(
        ...,
        description="MFA code",
        examples=["123456", "789012", "456789"]
    )
    method: str = Field(
        ...,
        description="MFA method",
        examples=["totp", "sms", "email"]
    )


class MFAVerifyResponse(ResponseBase[Dict[str, Any]]):
    """MFA verification response."""
    pass


class DeviceListResponse(ResponseBase[List[Dict[str, Any]]]):
    """Device list response."""
    pass


class DeviceRevokeRequest(BaseModel):
    """Device revoke request."""
    device_id: str = Field(
        ...,
        description="Device ID to revoke",
        examples=["test-device-001", "iphone-12-pro-max", "samsung-galaxy-s21"]
    )


class DeviceRevokeResponse(ResponseBase[Dict[str, Any]]):
    """Device revoke response."""
    pass


class SecurityEventInfo(BaseModel):
    """Security event information."""
    event_type: str = Field(
        ...,
        description="Event type",
        examples=["login", "logout", "password_change", "failed_login", "device_registered"]
    )
    timestamp: datetime = Field(
        ...,
        description="Event timestamp",
        examples=["2025-06-16T10:30:00Z", "2025-06-15T14:22:15Z"]
    )
    ip_address: Optional[str] = Field(
        None,
        description="IP address",
        examples=["*************", "********", "***************"]
    )
    device_info: Optional[Dict[str, Any]] = Field(
        None,
        description="Device information",
        examples=[
            {"device_id": "test-device-001", "device_type": "mobile", "os_name": "iOS"},
            {"device_id": "laptop-001", "device_type": "desktop", "os_name": "Windows"}
        ]
    )
    details: Optional[Dict[str, Any]] = Field(
        None,
        description="Event details",
        examples=[
            {"success": True, "method": "password"},
            {"success": False, "reason": "invalid_credentials", "attempts": 3}
        ]
    )


class SecurityEventsResponse(ResponseBase[List[SecurityEventInfo]]):
    """Security events response."""
    pass


# User Registration Schemas

class UserRegistrationRequest(BaseModel):
    """User registration request schema."""
    name: str = Field(
        ...,
        min_length=2,
        max_length=100,
        description="Full name of the user",
        examples=["Nguyễn Văn A", "Trần Thị B", "John Doe", "Jane Smith"]
    )
    email: EmailStr = Field(
        ...,
        description="Email address (must be unique)",
        examples=["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
    )
    password: str = Field(
        ...,
        min_length=8,
        description="Password (minimum 8 characters, must contain letters and numbers)",
        examples=["mypassword123", "securepass2025", "strongPassword!"]
    )
    confirm_password: str = Field(
        ...,
        description="Confirm password (must match password)",
        examples=["mypassword123", "securepass2025", "strongPassword!"]
    )
    phone: Optional[str] = Field(
        None,
        description="Phone number (optional)",
        examples=["+***********", "+***********", "+**********", "0987654321"]
    )
    terms_accepted: bool = Field(
        ...,
        description="User must accept terms and conditions",
        examples=[True]
    )
    newsletter_subscription: bool = Field(
        False,
        description="Subscribe to newsletter (optional)",
        examples=[True, False]
    )

    @field_validator('password')
    @classmethod
    def validate_password_strength(cls, v):
        """Validate password strength."""
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")

        # Check for at least one letter and one number
        has_letter = any(c.isalpha() for c in v)
        has_number = any(c.isdigit() for c in v)

        if not has_letter:
            raise ValueError("Password must contain at least one letter")
        if not has_number:
            raise ValueError("Password must contain at least one number")

        return v

    @field_validator('confirm_password')
    @classmethod
    def validate_password_confirmation(cls, v, info):
        """Validate password confirmation matches password."""
        if 'password' in info.data and v != info.data['password']:
            raise ValueError("Password confirmation does not match")
        return v

    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        """Validate name format."""
        if not v or len(v.strip()) < 2:
            raise ValueError("Name must be at least 2 characters long")
        return v.strip()

    @field_validator('phone')
    @classmethod
    def validate_phone(cls, v):
        """Validate phone number format."""
        if v is None:
            return v

        # Remove spaces and common separators
        phone_clean = v.replace(" ", "").replace("-", "").replace("(", "").replace(")", "")

        # Check if it's a valid phone number (basic validation)
        if not phone_clean.replace("+", "").isdigit():
            raise ValueError("Phone number must contain only digits and optional + prefix")

        if len(phone_clean) < 10 or len(phone_clean) > 15:
            raise ValueError("Phone number must be between 10 and 15 digits")

        return phone_clean

    @field_validator('terms_accepted')
    @classmethod
    def validate_terms_accepted(cls, v):
        """Validate terms acceptance."""
        if not v:
            raise ValueError("You must accept the terms and conditions to register")
        return v


class UserRegistrationResponse(ResponseBase[Dict[str, Any]]):
    """User registration response schema."""
    pass


# Forgot Password Schemas

class ForgotPasswordRequest(BaseModel):
    """Forgot password request schema."""
    email: EmailStr = Field(
        ...,
        description="Email address of the account",
        examples=["<EMAIL>", "<EMAIL>", "<EMAIL>"]
    )
    reset_url_template: Optional[str] = Field(
        None,
        description="Custom reset URL template (optional, for frontend integration)",
        examples=[
            "https://lms.earnbase.io/reset-password?token={token}",
            "https://app.company.com/auth/reset?token={token}",
            "https://portal.example.com/password-reset/{token}"
        ]
    )
    language: Optional[str] = Field(
        "vi_VN",
        description="Language for email template",
        examples=["vi_VN", "en_US", "fr_FR", "de_DE"]
    )

    @field_validator('reset_url_template')
    @classmethod
    def validate_reset_url_template(cls, v):
        """Validate reset URL template format."""
        if v is None:
            return v

        if "{token}" not in v:
            raise ValueError("Reset URL template must contain {token} placeholder")

        if not v.startswith(("http://", "https://")):
            raise ValueError("Reset URL template must be a valid HTTP/HTTPS URL")

        return v


class ForgotPasswordResponse(ResponseBase[Dict[str, Any]]):
    """Forgot password response schema."""
    pass


# Reset Password Schemas

class ResetPasswordRequest(BaseModel):
    """Reset password request schema."""
    token: str = Field(
        ...,
        description="Reset token received via email",
        examples=[
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************",
            "reset_token_abc123def456",
            "rt_**********abcdef"
        ]
    )
    new_password: str = Field(
        ...,
        min_length=8,
        description="New password (minimum 8 characters)",
        examples=["newpassword123", "mynewsecurepass", "strongpassword2025"]
    )
    confirm_password: str = Field(
        ...,
        description="Confirm new password",
        examples=["newpassword123", "mynewsecurepass", "strongpassword2025"]
    )

    @field_validator('new_password')
    @classmethod
    def validate_new_password_strength(cls, v):
        """Validate new password strength."""
        if len(v) < 8:
            raise ValueError("New password must be at least 8 characters long")

        # Check for at least one letter and one number
        has_letter = any(c.isalpha() for c in v)
        has_number = any(c.isdigit() for c in v)

        if not has_letter:
            raise ValueError("New password must contain at least one letter")
        if not has_number:
            raise ValueError("New password must contain at least one number")

        return v

    @field_validator('confirm_password')
    @classmethod
    def validate_password_confirmation(cls, v, info):
        """Validate password confirmation matches new password."""
        if 'new_password' in info.data and v != info.data['new_password']:
            raise ValueError("Password confirmation does not match")
        return v


class ResetPasswordResponse(ResponseBase[Dict[str, Any]]):
    """Reset password response schema."""
    pass


# Email Verification Schemas

class EmailVerificationRequest(BaseModel):
    """Email verification request schema."""
    token: str = Field(
        ...,
        description="Email verification token received via email",
        examples=[
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************",
            "verify_token_abc123def456",
            "vt_**********abcdef"
        ]
    )

    @field_validator('token')
    @classmethod
    def validate_token(cls, v):
        """Validate token format."""
        if not v or len(v.strip()) == 0:
            raise ValueError("Verification token is required")
        return v.strip()


class EmailVerificationResponse(ResponseBase[Dict[str, Any]]):
    """Email verification response schema."""
    pass


class ResendVerificationRequest(BaseModel):
    """Resend email verification request schema."""
    email: EmailStr = Field(
        ...,
        description="Email address to resend verification",
        examples=["<EMAIL>", "<EMAIL>", "<EMAIL>"]
    )


class ResendVerificationResponse(ResponseBase[Dict[str, Any]]):
    """Resend email verification response schema."""
    pass


# Enhanced Password Change Schemas (extends existing)

class EnhancedPasswordChangeRequest(BaseModel):
    """Enhanced password change request with additional security."""
    current_password: str = Field(
        ...,
        description="Current password for verification",
        examples=["oldpassword123", "currentpass", "myoldpassword"]
    )
    new_password: str = Field(
        ...,
        min_length=8,
        description="New password (minimum 8 characters)",
        examples=["newpassword123", "mynewsecurepass", "strongpassword2025"]
    )
    confirm_password: str = Field(
        ...,
        description="Confirm new password",
        examples=["newpassword123", "mynewsecurepass", "strongpassword2025"]
    )
    logout_all_devices: bool = Field(
        True,
        description="Logout from all devices after password change",
        examples=[True, False]
    )
    send_notification: bool = Field(
        True,
        description="Send email notification about password change",
        examples=[True, False]
    )

    @field_validator('new_password')
    @classmethod
    def validate_new_password_strength(cls, v):
        """Validate new password strength."""
        if len(v) < 8:
            raise ValueError("New password must be at least 8 characters long")

        # Check for at least one letter and one number
        has_letter = any(c.isalpha() for c in v)
        has_number = any(c.isdigit() for c in v)

        if not has_letter:
            raise ValueError("New password must contain at least one letter")
        if not has_number:
            raise ValueError("New password must contain at least one number")

        return v

    @field_validator('confirm_password')
    @classmethod
    def validate_password_confirmation(cls, v, info):
        """Validate password confirmation matches new password."""
        if 'new_password' in info.data and v != info.data['new_password']:
            raise ValueError("Password confirmation does not match")
        return v

    @field_validator('current_password')
    @classmethod
    def validate_current_password(cls, v):
        """Validate current password is provided."""
        if not v or len(v.strip()) == 0:
            raise ValueError("Current password is required")
        return v


class EnhancedPasswordChangeResponse(ResponseBase[Dict[str, Any]]):
    """Enhanced password change response schema."""
    pass


# Account Status Schemas

class AccountStatusRequest(BaseModel):
    """Account status check request schema."""
    email: EmailStr = Field(
        ...,
        description="Email address to check status",
        examples=["<EMAIL>", "<EMAIL>", "<EMAIL>"]
    )


class AccountStatusResponse(ResponseBase[Dict[str, Any]]):
    """Account status response schema."""
    pass


# Token Validation Schemas

class TokenValidationRequest(BaseModel):
    """Token validation request schema."""
    token: str = Field(
        ...,
        description="Token to validate",
        examples=[
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************",
            "token_abc123def456",
            "t_**********abcdef"
        ]
    )
    token_type: str = Field(
        ...,
        description="Type of token to validate",
        examples=["email_verification", "password_reset", "access_token", "refresh_token"]
    )

    @field_validator('token_type')
    @classmethod
    def validate_token_type(cls, v):
        """Validate token type."""
        allowed_types = ['email_verification', 'password_reset', 'access_token', 'refresh_token']
        if v not in allowed_types:
            raise ValueError(f"Token type must be one of: {', '.join(allowed_types)}")
        return v


class TokenValidationResponse(ResponseBase[Dict[str, Any]]):
    """Token validation response schema."""
    pass
