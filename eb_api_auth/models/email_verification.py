# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

"""
Email Verification Model

Manages email verification tokens for user registration and email changes.
Provides secure token generation, validation, and cleanup functionality.
"""

import secrets
import hashlib
from datetime import datetime, timedelta
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError


class EmailVerification(models.Model):
    _name = 'eb_api_auth.email_verification'
    _description = 'Email Verification Tokens'
    _order = 'created_at desc'
    _rec_name = 'token_display'

    # Core fields
    user_id = fields.Many2one(
        'res.users',
        string='User',
        required=True,
        ondelete='cascade',
        help="User associated with this verification token"
    )
    email = fields.Char(
        string='Email Address',
        required=True,
        help="Email address to be verified"
    )
    token = fields.Char(
        string='Verification Token',
        required=True,
        index=True,
        help="Unique verification token sent via email"
    )
    token_hash = fields.Char(
        string='Token Hash',
        required=True,
        index=True,
        help="Hashed version of the token for security"
    )
    
    # Status and timing
    is_used = fields.Boolean(
        string='Is Used',
        default=False,
        help="Whether this token has been used for verification"
    )
    expires_at = fields.Datetime(
        string='Expires At',
        required=True,
        help="When this token expires"
    )
    created_at = fields.Datetime(
        string='Created At',
        default=fields.Datetime.now,
        help="When this token was created"
    )
    used_at = fields.Datetime(
        string='Used At',
        help="When this token was used for verification"
    )
    
    # Tracking fields
    ip_address = fields.Char(
        string='IP Address',
        help="IP address from which the verification was requested"
    )
    user_agent = fields.Text(
        string='User Agent',
        help="User agent string from the verification request"
    )
    verification_type = fields.Selection([
        ('registration', 'User Registration'),
        ('email_change', 'Email Address Change'),
        ('reactivation', 'Account Reactivation'),
    ], string='Verification Type', default='registration', required=True)
    
    # Computed fields
    token_display = fields.Char(
        string='Token Display',
        compute='_compute_token_display',
        help="Masked token for display purposes"
    )
    is_expired = fields.Boolean(
        string='Is Expired',
        compute='_compute_is_expired',
        help="Whether this token has expired"
    )
    status = fields.Selection([
        ('active', 'Active'),
        ('used', 'Used'),
        ('expired', 'Expired'),
    ], string='Status', compute='_compute_status')

    @api.depends('token')
    def _compute_token_display(self):
        """Compute masked token for display."""
        for record in self:
            if record.token and len(record.token) > 8:
                record.token_display = f"{record.token[:4]}...{record.token[-4:]}"
            else:
                record.token_display = "****"

    @api.depends('expires_at')
    def _compute_is_expired(self):
        """Compute if token is expired."""
        now = fields.Datetime.now()
        for record in self:
            record.is_expired = record.expires_at < now

    @api.depends('is_used', 'is_expired')
    def _compute_status(self):
        """Compute token status."""
        for record in self:
            if record.is_used:
                record.status = 'used'
            elif record.is_expired:
                record.status = 'expired'
            else:
                record.status = 'active'

    @api.model
    def generate_token(self, user_id, email, verification_type='registration', 
                      expires_hours=168, ip_address=None, user_agent=None):
        """
        Generate a new email verification token.
        
        Args:
            user_id (int): User ID
            email (str): Email address to verify
            verification_type (str): Type of verification
            expires_hours (int): Hours until token expires (default: 168 = 7 days)
            ip_address (str): IP address of the request
            user_agent (str): User agent of the request
            
        Returns:
            EmailVerification: Created verification record
        """
        # Generate secure random token
        token = secrets.token_urlsafe(32)
        token_hash = hashlib.sha256(token.encode()).hexdigest()
        
        # Calculate expiration time
        expires_at = datetime.now() + timedelta(hours=expires_hours)
        
        # Invalidate any existing active tokens for this user and email
        existing_tokens = self.search([
            ('user_id', '=', user_id),
            ('email', '=', email),
            ('is_used', '=', False),
            ('expires_at', '>', fields.Datetime.now())
        ])
        existing_tokens.write({'is_used': True, 'used_at': fields.Datetime.now()})
        
        # Create new verification record
        verification = self.create({
            'user_id': user_id,
            'email': email,
            'token': token,
            'token_hash': token_hash,
            'verification_type': verification_type,
            'expires_at': expires_at,
            'ip_address': ip_address,
            'user_agent': user_agent,
        })
        
        return verification

    @api.model
    def validate_token(self, token):
        """
        Validate an email verification token.
        
        Args:
            token (str): Token to validate
            
        Returns:
            EmailVerification: Verification record if valid
            
        Raises:
            ValidationError: If token is invalid, expired, or already used
        """
        if not token:
            raise ValidationError(_("Verification token is required"))
        
        # Hash the provided token
        token_hash = hashlib.sha256(token.encode()).hexdigest()
        
        # Find verification record
        verification = self.search([
            ('token_hash', '=', token_hash),
            ('is_used', '=', False)
        ], limit=1)
        
        if not verification:
            raise ValidationError(_("Invalid or already used verification token"))
        
        # Check if token is expired
        if verification.is_expired:
            raise ValidationError(_("Verification token has expired"))
        
        return verification

    def mark_as_used(self):
        """Mark this verification token as used."""
        self.ensure_one()
        if self.is_used:
            raise UserError(_("This verification token has already been used"))
        
        if self.is_expired:
            raise UserError(_("This verification token has expired"))
        
        self.write({
            'is_used': True,
            'used_at': fields.Datetime.now()
        })

    @api.model
    def cleanup_expired_tokens(self):
        """Clean up expired verification tokens (called by cron)."""
        expired_tokens = self.search([
            ('expires_at', '<', fields.Datetime.now())
        ])
        
        count = len(expired_tokens)
        expired_tokens.unlink()
        
        return count

    @api.model
    def get_user_pending_verifications(self, user_id):
        """Get pending verifications for a user."""
        return self.search([
            ('user_id', '=', user_id),
            ('is_used', '=', False),
            ('expires_at', '>', fields.Datetime.now())
        ])

    def send_verification_email(self):
        """Send verification email to user."""
        self.ensure_one()
        
        # Get email template
        template = self.env.ref('eb_api_auth.email_verification_template', raise_if_not_found=False)
        if not template:
            raise UserError(_("Email verification template not found"))
        
        # Send email
        template.send_mail(self.id, force_send=True)
        
        return True

    @api.constrains('token')
    def _check_token_uniqueness(self):
        """Ensure token uniqueness."""
        for record in self:
            if record.token:
                existing = self.search([
                    ('token', '=', record.token),
                    ('id', '!=', record.id)
                ])
                if existing:
                    raise ValidationError(_("Verification token must be unique"))

    @api.constrains('expires_at')
    def _check_expiration_date(self):
        """Ensure expiration date is in the future."""
        for record in self:
            if record.expires_at <= fields.Datetime.now():
                raise ValidationError(_("Expiration date must be in the future"))
