# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging
from typing import Any

from odoo import fields, models

from fastapi import APIRouter

from odoo.addons.eb_api_auth.routers import auth_router, auth_router_doc

_logger = logging.getLogger(__name__)


class FastapiEndpoint(models.Model):
    _inherit = "fastapi.endpoint"

    app = fields.Selection(
        selection_add=[("eb_auth", "EB Auth API")],
        ondelete={"eb_auth": "cascade"},
    )

    # Add additional fields for API configuration
    enable_rate_limiting = fields.Boolean(
        string="Rate Limiting", default=True, help="Enable API rate limiting"
    )

    def _get_fastapi_routers(self) -> list[APIRouter]:
        if self.app == "eb_auth":
            return [auth_router]
        return super()._get_fastapi_routers()

    def _prepare_fastapi_app_params(self) -> dict[str, Any]:
        params = super()._prepare_fastapi_app_params()
        if self.app == "eb_auth":
            tags_metadata = params.get("openapi_tags", []) or []
            tags_metadata.append({"name": "eb_auth", "description": auth_router_doc})
            params["openapi_tags"] = tags_metadata
        return params
