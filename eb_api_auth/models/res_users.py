# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

from odoo import api, fields, models


class ResUsers(models.Model):
    """Mở rộng model res.users để thêm thông tin liên quan đến API Tokens."""

    _inherit = "res.users"

    token_ids = fields.One2many(
        "eb_api_auth.token",
        "user_id",
        string="API Tokens",
        help="API tokens issued for this user",
    )

    active_token_count = fields.Integer(
        string="Active Tokens",
        compute="_compute_token_counts",
        help="Number of active API tokens for this user",
    )

    @api.depends("token_ids.status")
    def _compute_token_counts(self):
        """Tính số lượng token active."""
        for user in self:
            user.active_token_count = len(
                user.token_ids.filtered(lambda t: t.status == "active")
            )

    def action_view_tokens(self):
        """Mở danh sách token của user hiện tại."""
        self.ensure_one()
        action = self.env.ref("eb_api_auth.action_eb_api_auth_token").read()[0]
        action["domain"] = [("user_id", "=", self.id)]
        action["context"] = {"default_user_id": self.id}
        return action

    def revoke_all_tokens(self, token_type=None):
        """Thu hồi tất cả token của user."""
        self.ensure_one()
        self.env["eb_api_auth.token"].invalidate_user_tokens(
            user_id=self.id, token_type=token_type, reason="admin_revoked"
        )
        return True
