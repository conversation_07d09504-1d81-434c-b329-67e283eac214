# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging
import time
import uuid
from datetime import datetime

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError

_logger = logging.getLogger(__name__)


class AuthToken(models.Model):
    """Lưu trữ và quản lý tokens xác thực."""

    _name = "eb_api_auth.token"
    _description = "Authentication Token"
    _order = "create_date desc"
    _rec_name = "token_id"

    token_id = fields.Char(
        string="Token ID",
        required=True,
        readonly=True,
        default=lambda self: str(uuid.uuid4()),
        index=True,
        help="Unique identifier for the token",
    )

    jti = fields.Char(string="JWT ID", help="JWT ID (jti claim) nếu có")

    user_id = fields.Many2one(
        "res.users",
        string="User",
        required=True,
        ondelete="cascade",
        index=True,
        help="User this token belongs to",
    )

    token_type = fields.Selection(
        [("access_token", "Access Token"), ("refresh_token", "Refresh Token")],
        string="Token Type",
        required=True,
        help="Type of token",
    )

    status = fields.Selection(
        [("active", "Active"), ("expired", "Expired"), ("revoked", "Revoked")],
        string="Status",
        default="active",
        required=True,
        index=True,
        help="Current token status",
    )

    expiration_date = fields.Datetime(
        string="Expiration Date", required=True, help="When this token will expire"
    )

    issued_at = fields.Datetime(
        string="Issued At",
        required=True,
        default=fields.Datetime.now,
        help="When this token was issued",
    )

    last_used = fields.Datetime(
        string="Last Used", help="When this token was last used for authentication"
    )

    revoked_date = fields.Datetime(
        string="Revoked Date", help="When this token was revoked"
    )

    revoked_reason = fields.Char(
        string="Revocation Reason", help="Reason for token revocation"
    )

    ip_address = fields.Char(
        string="IP Address", help="IP address that requested this token"
    )

    user_agent = fields.Char(
        string="User Agent", help="User agent that requested this token"
    )

    _sql_constraints = [
        ("token_id_unique", "UNIQUE(token_id)", "Token ID must be unique!")
    ]

    @api.model
    def create_token(
        self,
        user_id,
        token_type,
        expiration_seconds,
        ip_address=None,
        user_agent=None,
        jti=None,
    ):
        """Tạo token mới và lưu vào database.

        Args:
            user_id: ID của người dùng
            token_type: Loại token ('access_token' hoặc 'refresh_token')
            expiration_seconds: Thời gian hết hạn (giây)
            ip_address: IP của client (tùy chọn)
            user_agent: User agent của client (tùy chọn)
            jti: JWT ID claim nếu có (tùy chọn)

        Returns:
            AuthToken: Record đã tạo
        """
        # Xác nhận user tồn tại
        user = self.env["res.users"].sudo().browse(user_id)
        if not user.exists():
            raise ValidationError(_("User does not exist"))

        # Tính thời gian hết hạn
        expiration = datetime.fromtimestamp(time.time() + expiration_seconds)

        # Tạo token
        token = self.create(
            {
                "user_id": user_id,
                "token_type": token_type,
                "status": "active",
                "expiration_date": expiration,
                "ip_address": ip_address,
                "user_agent": user_agent,
                "jti": jti,
            }
        )

        _logger.info(f"Created new {token_type} for user ID {user_id}")
        return token

    def invalidate(self, reason="manually_revoked"):
        """Thu hồi token."""
        for token in self:
            token.write(
                {
                    "status": "revoked",
                    "revoked_date": fields.Datetime.now(),
                    "revoked_reason": reason,
                }
            )
            _logger.info(f"Token {token.token_id} revoked. Reason: {reason}")
        return True

    @api.model
    def invalidate_user_tokens(
        self, user_id, token_type=None, reason="user_revoked_all"
    ):
        """Thu hồi tất cả tokens của một user.

        Args:
            user_id: ID của người dùng
            token_type: Loại token cần thu hồi (tùy chọn)
            reason: Lý do thu hồi (tùy chọn)
        """
        domain = [("user_id", "=", user_id), ("status", "=", "active")]
        if token_type:
            domain.append(("token_type", "=", token_type))

        tokens = self.search(domain)
        if tokens:
            tokens.invalidate(reason=reason)
            _logger.info(f"Revoked {len(tokens)} tokens for user ID {user_id}")
        return True

    @api.model
    def invalidate_token_by_jti(self, jti, reason="token_revoked"):
        """Thu hồi token theo JWT ID.

        Args:
            jti: JWT ID claim
            reason: Lý do thu hồi (tùy chọn)
        """
        tokens = self.search([("jti", "=", jti), ("status", "=", "active")])
        if tokens:
            tokens.invalidate(reason=reason)
            _logger.info(f"Revoked token with jti {jti}")
        return True

    @api.model
    def clean_expired_tokens(self, days=30):
        """Xóa các token đã hết hạn hơn X ngày."""
        cutoff_date = fields.Datetime.subtract(fields.Datetime.now(), days=days)

        expired_tokens = self.search(
            [
                ("status", "in", ["expired", "revoked"]),
                ("expiration_date", "<", cutoff_date),
            ]
        )

        if expired_tokens:
            count = len(expired_tokens)
            expired_tokens.unlink()
            _logger.info(f"Cleaned up {count} expired tokens older than {days} days")

        return True

    @api.model
    def update_expired_tokens(self):
        """Cập nhật trạng thái token đã hết hạn nhưng chưa được đánh dấu."""
        now = fields.Datetime.now()

        expired_tokens = self.search(
            [("status", "=", "active"), ("expiration_date", "<", now)]
        )

        if expired_tokens:
            expired_tokens.write({"status": "expired"})
            _logger.info(f"Updated {len(expired_tokens)} tokens to expired status")

        return True

    @api.model
    def record_token_usage(self, jti=None, token_id=None):
        """Ghi nhận thời gian sử dụng token.

        Args:
            jti: JWT ID claim (tùy chọn)
            token_id: Token ID (tùy chọn)
        """
        domain = [("status", "=", "active")]
        if jti:
            domain.append(("jti", "=", jti))
        elif token_id:
            domain.append(("token_id", "=", token_id))
        else:
            return False

        token = self.search(domain, limit=1)
        if token:
            token.last_used = fields.Datetime.now()
            return True

        return False
