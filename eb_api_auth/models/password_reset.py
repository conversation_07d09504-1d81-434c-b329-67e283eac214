# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

"""
Password Reset Model

Manages password reset tokens for secure password recovery.
Provides secure token generation, validation, and cleanup functionality.
"""

import secrets
import hashlib
from datetime import datetime, timedelta
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError


class PasswordReset(models.Model):
    _name = 'eb_api_auth.password_reset'
    _description = 'Password Reset Tokens'
    _order = 'created_at desc'
    _rec_name = 'token_display'

    # Core fields
    user_id = fields.Many2one(
        'res.users',
        string='User',
        required=True,
        ondelete='cascade',
        help="User requesting password reset"
    )
    email = fields.Char(
        string='Email Address',
        required=True,
        help="Email address for password reset"
    )
    token = fields.Char(
        string='Reset Token',
        required=True,
        index=True,
        help="Unique password reset token sent via email"
    )
    token_hash = fields.Char(
        string='Token Hash',
        required=True,
        index=True,
        help="Hashed version of the token for security"
    )
    
    # Status and timing
    is_used = fields.Boolean(
        string='Is Used',
        default=False,
        help="Whether this token has been used for password reset"
    )
    expires_at = fields.Datetime(
        string='Expires At',
        required=True,
        help="When this token expires"
    )
    created_at = fields.Datetime(
        string='Created At',
        default=fields.Datetime.now,
        help="When this token was created"
    )
    used_at = fields.Datetime(
        string='Used At',
        help="When this token was used for password reset"
    )
    
    # Security tracking
    ip_address = fields.Char(
        string='Request IP Address',
        help="IP address from which the reset was requested"
    )
    user_agent = fields.Text(
        string='Request User Agent',
        help="User agent string from the reset request"
    )
    reset_ip_address = fields.Char(
        string='Reset IP Address',
        help="IP address from which the password was actually reset"
    )
    reset_user_agent = fields.Text(
        string='Reset User Agent',
        help="User agent string from the password reset"
    )
    
    # Computed fields
    token_display = fields.Char(
        string='Token Display',
        compute='_compute_token_display',
        help="Masked token for display purposes"
    )
    is_expired = fields.Boolean(
        string='Is Expired',
        compute='_compute_is_expired',
        help="Whether this token has expired"
    )
    status = fields.Selection([
        ('active', 'Active'),
        ('used', 'Used'),
        ('expired', 'Expired'),
    ], string='Status', compute='_compute_status')

    @api.depends('token')
    def _compute_token_display(self):
        """Compute masked token for display."""
        for record in self:
            if record.token and len(record.token) > 8:
                record.token_display = f"{record.token[:4]}...{record.token[-4:]}"
            else:
                record.token_display = "****"

    @api.depends('expires_at')
    def _compute_is_expired(self):
        """Compute if token is expired."""
        now = fields.Datetime.now()
        for record in self:
            record.is_expired = record.expires_at < now

    @api.depends('is_used', 'is_expired')
    def _compute_status(self):
        """Compute token status."""
        for record in self:
            if record.is_used:
                record.status = 'used'
            elif record.is_expired:
                record.status = 'expired'
            else:
                record.status = 'active'

    @api.model
    def generate_token(self, user_id, email, expires_hours=24, 
                      ip_address=None, user_agent=None):
        """
        Generate a new password reset token.
        
        Args:
            user_id (int): User ID
            email (str): Email address for reset
            expires_hours (int): Hours until token expires (default: 24)
            ip_address (str): IP address of the request
            user_agent (str): User agent of the request
            
        Returns:
            PasswordReset: Created reset record
        """
        # Generate secure random token
        token = secrets.token_urlsafe(32)
        token_hash = hashlib.sha256(token.encode()).hexdigest()
        
        # Calculate expiration time
        expires_at = datetime.now() + timedelta(hours=expires_hours)
        
        # Invalidate any existing active tokens for this user
        existing_tokens = self.search([
            ('user_id', '=', user_id),
            ('is_used', '=', False),
            ('expires_at', '>', fields.Datetime.now())
        ])
        existing_tokens.write({'is_used': True, 'used_at': fields.Datetime.now()})
        
        # Create new reset record
        reset_record = self.create({
            'user_id': user_id,
            'email': email,
            'token': token,
            'token_hash': token_hash,
            'expires_at': expires_at,
            'ip_address': ip_address,
            'user_agent': user_agent,
        })
        
        return reset_record

    @api.model
    def validate_token(self, token):
        """
        Validate a password reset token.
        
        Args:
            token (str): Token to validate
            
        Returns:
            PasswordReset: Reset record if valid
            
        Raises:
            ValidationError: If token is invalid, expired, or already used
        """
        if not token:
            raise ValidationError(_("Reset token is required"))
        
        # Hash the provided token
        token_hash = hashlib.sha256(token.encode()).hexdigest()
        
        # Find reset record
        reset_record = self.search([
            ('token_hash', '=', token_hash),
            ('is_used', '=', False)
        ], limit=1)
        
        if not reset_record:
            raise ValidationError(_("Invalid or already used reset token"))
        
        # Check if token is expired
        if reset_record.is_expired:
            raise ValidationError(_("Reset token has expired"))
        
        return reset_record

    def mark_as_used(self, reset_ip_address=None, reset_user_agent=None):
        """Mark this reset token as used."""
        self.ensure_one()
        if self.is_used:
            raise UserError(_("This reset token has already been used"))
        
        if self.is_expired:
            raise UserError(_("This reset token has expired"))
        
        self.write({
            'is_used': True,
            'used_at': fields.Datetime.now(),
            'reset_ip_address': reset_ip_address,
            'reset_user_agent': reset_user_agent,
        })

    @api.model
    def cleanup_expired_tokens(self):
        """Clean up expired reset tokens (called by cron)."""
        expired_tokens = self.search([
            ('expires_at', '<', fields.Datetime.now())
        ])
        
        count = len(expired_tokens)
        expired_tokens.unlink()
        
        return count

    @api.model
    def get_user_pending_resets(self, user_id):
        """Get pending reset tokens for a user."""
        return self.search([
            ('user_id', '=', user_id),
            ('is_used', '=', False),
            ('expires_at', '>', fields.Datetime.now())
        ])

    def send_reset_email(self, reset_url_template=None):
        """Send password reset email to user."""
        self.ensure_one()
        
        # Get email template
        template = self.env.ref('eb_api_auth.password_reset_template', raise_if_not_found=False)
        if not template:
            raise UserError(_("Password reset email template not found"))
        
        # Prepare context with reset URL
        ctx = dict(self.env.context)
        if reset_url_template:
            reset_url = reset_url_template.format(token=self.token)
            ctx['reset_url'] = reset_url
        
        # Send email with context
        template.with_context(ctx).send_mail(self.id, force_send=True)
        
        return True

    @api.model
    def get_reset_statistics(self, days=30):
        """Get password reset statistics for the last N days."""
        start_date = fields.Datetime.now() - timedelta(days=days)
        
        domain = [('created_at', '>=', start_date)]
        
        total_requests = self.search_count(domain)
        successful_resets = self.search_count(domain + [('is_used', '=', True)])
        expired_tokens = self.search_count(domain + [('is_expired', '=', True), ('is_used', '=', False)])
        
        return {
            'total_requests': total_requests,
            'successful_resets': successful_resets,
            'expired_tokens': expired_tokens,
            'success_rate': (successful_resets / total_requests * 100) if total_requests > 0 else 0,
        }

    @api.constrains('token')
    def _check_token_uniqueness(self):
        """Ensure token uniqueness."""
        for record in self:
            if record.token:
                existing = self.search([
                    ('token', '=', record.token),
                    ('id', '!=', record.id)
                ])
                if existing:
                    raise ValidationError(_("Reset token must be unique"))

    @api.constrains('expires_at')
    def _check_expiration_date(self):
        """Ensure expiration date is in the future."""
        for record in self:
            if record.expires_at <= fields.Datetime.now():
                raise ValidationError(_("Expiration date must be in the future"))
