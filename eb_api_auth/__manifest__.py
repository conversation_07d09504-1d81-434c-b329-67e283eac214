# -*- coding: utf-8 -*-
{
    "name": "Enhanced API Authentication with JWT",
    "summary": "Provides enhanced JWT authentication with device tracking and advanced features",
    "description": """
        This module provides enhanced JWT authentication capabilities for FastAPI
        endpoints with advanced features including:

        Core Features:
        - JWT authentication and authorization
        - Enhanced login with device tracking
        - Multi-factor authentication support
        - Session management
        - Device registration and management
        - Password change functionality
        - Security event logging
        - Token refresh and revocation

        Enhanced Endpoints:
        - /sign-in: Unified login (username/email + optional device tracking)
        - /device/register: Device registration
        - /sessions: Session management
        - /password/change: Password change
        - /devices: Device management
        - /security/events: Security audit

        This module centralizes all authentication features that were previously
        scattered across different modules, providing a unified and secure
        authentication system for all Odoo API endpoints.
    """,
    "version": "18.0.1.0.0",
    "category": "Technical/API",
    "author": "eBill and Vantis Vietnam",
    "website": "https://earnbase.io",
    "license": "LGPL-3",
    "depends": [
        "base",
        "web",
        "fastapi",
        "fastapi_auth_jwt",
        "auth_jwt",
        "eb_api_core",
    ],
    "data": [
        "security/ir.model.access.csv",
        "data/fastapi_endpoint_data.xml",
        "data/auth_settings_data.xml",
        "data/email_templates.xml",
        "views/fastapi_endpoint_views.xml",
        "views/token_views.xml",
        "views/user_views.xml",
    ],
    "demo": [],
    "external_dependencies": {
        "python": [
            "fastapi>=0.104.1",
            "python-jose>=3.3.0",
            "pyjwt>=2.8.0",
            "passlib>=1.7.4",
            "python-multipart>=0.0.6"
        ],
    },
    "post_init_hook": "post_init_hook",
    "post_load": "post_load_hook",
    "installable": True,
    "application": False,
    "auto_install": False,
}
