==========
Base Phone
==========

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:1685f77c2ab6988da842ee87e52d46d4e55bcd2c6bdbe568a730f0f352e2f133
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Beta-yellow.png
    :target: https://odoo-community.org/page/development-status
    :alt: Beta
.. |badge2| image:: https://img.shields.io/badge/licence-AGPL--3-blue.png
    :target: http://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Fconnector--telephony-lightgray.png?logo=github
    :target: https://github.com/OCA/connector-telephony/tree/18.0/base_phone
    :alt: OCA/connector-telephony
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/connector-telephony-18-0/connector-telephony-18-0-base_phone
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/connector-telephony&target_branch=18.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

This module provides common methods and wizards which can be useful to
develop a connector between Odoo and a telephony system. It depends on
the official module *phone_validation* which handle the reformatting of
phone numbers using the
`phonenumbers <https://github.com/daviddrysdale/python-phonenumbers>`__
Python library, which is a port of the library used in Android
smartphones. For example, if your user is linked to a French company and
you update the form view of a partner with a badly written French phone
number such as '01-55-42-12-42', Odoo will automatically update the
phone number to `E.164 <https://en.wikipedia.org/wiki/E.164>`__ format
'+33155421242'. This module extends this reformatting to create() and
write() methods.

This module is used by the Odoo-Asterisk connector of the OCA.

**Table of contents**

.. contents::
   :local:

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/connector-telephony/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/connector-telephony/issues/new?body=module:%20base_phone%0Aversion:%2018.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
-------

* Akretion

Contributors
------------

- Alexis de Lattre <<EMAIL>>
- Sébastien Beau <<EMAIL>>
- `Dixmit <https://www.dixmit.com>`__:

  - Luis David Rodríguez
  - Enric Tobella

- `Heliconia Solutions Pvt. Ltd. <https://www.heliconia.io>`__

  - Bhavesh Heliconia

Maintainers
-----------

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

.. |maintainer-alexis-via| image:: https://github.com/alexis-via.png?size=40px
    :target: https://github.com/alexis-via
    :alt: alexis-via

Current `maintainer <https://odoo-community.org/page/maintainer-role>`__:

|maintainer-alexis-via| 

This module is part of the `OCA/connector-telephony <https://github.com/OCA/connector-telephony/tree/18.0/base_phone>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
