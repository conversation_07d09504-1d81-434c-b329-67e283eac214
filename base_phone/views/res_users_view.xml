<?xml version="1.0" encoding="utf-8" ?>
<!--
  Copyright 2010-2021 Akretion France (http://www.akretion.com/)
  @author: <PERSON> <<EMAIL>>
  License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl).
-->
<odoo>
    <record id="view_users_form" model="ir.ui.view">
        <field name="name">base_phone.res.users.telephony_tab</field>
        <field name="model">res.users</field>
        <field name="inherit_id" ref="base.view_users_form" />
        <field name="arch" type="xml">
            <notebook position="inside">
                <page string="Telephony" name="phone" invisible="1">
                    <!-- Empty page, which will be used by other phone modules -->
                    <group name="phone-preferences" string="Telephony Preferences" />
                </page>
            </notebook>
        </field>
    </record>
    <record id="view_users_form_simple_modif" model="ir.ui.view">
        <field name="name">base_phone.user_preferences.view</field>
        <field name="model">res.users</field>
        <field name="inherit_id" ref="base.view_users_form_simple_modif" />
        <field name="arch" type="xml">
            <group name="signature" position="before">
                <group name="phone" invisible="1">
                    <!-- Empty group, that is used by other phone modules
                         We set it as invisible even if it is empty.
                         That way, we can add a field in this group in crm_phone
                         and have that field invisible. It is inherited by
                         the module asterisk_click2dial to set it to invisible=0
                    -->
                </group>
            </group>
        </field>
    </record>
</odoo>
