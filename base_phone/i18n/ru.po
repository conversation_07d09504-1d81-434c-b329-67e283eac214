# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_phone
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-01 07:19+0000\n"
"PO-Revision-Date: 2023-05-01 07:19+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Incoming calls</span>"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__calling_number
msgid "Calling Number"
msgstr ""

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.reformat_all_phonenumbers_form
msgid "Cancel"
msgstr "Отменена"

#. module: base_phone
#. odoo-javascript
#: code:addons/base_phone/static/src/components/on_dial_button/on_dial_button.esm.js:0
#, python-format
msgid "Click2dial successfull"
msgstr ""

#. module: base_phone
#. odoo-javascript
#: code:addons/base_phone/static/src/components/on_dial_button/on_dial_button.esm.js:0
#, python-format
msgid "Click2dial to %s"
msgstr ""

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.number_not_found_form
#: model_terms:ir.ui.view,arch_db:base_phone.reformat_all_phonenumbers_form
msgid "Close"
msgstr ""

#. module: base_phone
#: model:ir.model,name:base_phone.model_phone_common
msgid "Common methods for phone features"
msgstr ""

#. module: base_phone
#: model:ir.model,name:base_phone.model_res_company
msgid "Companies"
msgstr "Компании"

#. module: base_phone
#: model:ir.model,name:base_phone.model_res_config_settings
msgid "Config Settings"
msgstr "Конфигурационные настройки"

#. module: base_phone
#: model:ir.model,name:base_phone.model_res_partner
msgid "Contact"
msgstr "Контакт"

#. module: base_phone
#. odoo-python
#: code:addons/base_phone/wizard/number_not_found.py:0
#: model_terms:ir.ui.view,arch_db:base_phone.number_not_found_form
#, python-format
msgid "Create New Partner"
msgstr ""

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.number_not_found_form
msgid "Create or Update a Partner"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__create_uid
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__create_uid
msgid "Created by"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__create_date
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__create_date
msgid "Created on"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__current_partner_mobile
msgid "Current Mobile"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__current_partner_phone
msgid "Current Phone"
msgstr ""

#. module: base_phone
#. odoo-javascript
#: code:addons/base_phone/static/src/components/on_dial_button/on_dial_button.xml:0
#, python-format
msgid "Dial"
msgstr ""

#. module: base_phone
#. odoo-javascript
#: code:addons/base_phone/static/src/components/on_dial_button/on_dial_button.esm.js:0
#, python-format
msgid "Dial phone"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__display_name
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__display_name
msgid "Display Name"
msgstr ""

#. module: base_phone
#: model:ir.model.fields.selection,name:base_phone.selection__reformat_all_phonenumbers__state__done
msgid "Done"
msgstr ""

#. module: base_phone
#: model:ir.model.fields.selection,name:base_phone.selection__reformat_all_phonenumbers__state__draft
msgid "Draft"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__e164_number
msgid "E.164 Number"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,help:base_phone.field_number_not_found__e164_number
msgid "E.164 equivalent of the calling number."
msgstr ""

#. module: base_phone
#: model:ir.model.fields.selection,name:base_phone.selection__number_not_found__number_type__phone
msgid "Fixed"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__number_type
msgid "Fixed/Mobile"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__id
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__id
msgid "ID"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,help:base_phone.field_res_company__number_of_digits_to_match_from_end
#: model:ir.model.fields,help:base_phone.field_res_config_settings__number_of_digits_to_match_from_end
msgid ""
"In several situations, Odoo will have to find a Partner/Lead/Employee/... "
"from a phone number presented by the calling party. As the phone numbers "
"presented by your phone operator may not always be displayed in a standard "
"format, the best method to find the related Partner/Lead/Employee/... in "
"Odoo is to try to match the end of the phone number in Odoo with the N last "
"digits of the phone number presented by the calling party. N is the value "
"you should enter in this field."
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__write_uid
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__write_uid
msgid "Last Updated by"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__write_date
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__write_date
msgid "Last Updated on"
msgstr ""

#. module: base_phone
#. odoo-python
#: code:addons/base_phone/wizard/number_not_found.py:0
#, python-format
msgid "Missing country on company %s"
msgstr ""

#. module: base_phone
#: model:ir.model.fields.selection,name:base_phone.selection__number_not_found__number_type__mobile
msgid "Mobile"
msgstr ""

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.number_not_found_form
msgid "Number Not Found"
msgstr ""

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.number_not_found_form
msgid "Number converted to international format:"
msgstr ""

#. module: base_phone
#. odoo-javascript
#: code:addons/base_phone/static/src/components/on_dial_button/on_dial_button.esm.js:0
#, python-format
msgid "Number dialed: %s"
msgstr ""

#. module: base_phone
#: model:ir.model,name:base_phone.model_number_not_found
msgid "Number not found"
msgstr ""

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.res_config_settings_view_form
msgid "Number of Digits"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_res_company__number_of_digits_to_match_from_end
#: model:ir.model.fields,field_description:base_phone.field_res_config_settings__number_of_digits_to_match_from_end
msgid "Number of Digits To Match From End"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,help:base_phone.field_number_not_found__to_update_partner_id
msgid "Partner on which the phone number will be written"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__to_update_partner_id
msgid "Partner to Update"
msgstr ""

#. module: base_phone
#. odoo-python
#: code:addons/base_phone/wizard/number_not_found.py:0
#, python-format
msgid "Partner: %s"
msgstr ""

#. module: base_phone
#: model:res.groups,name:base_phone.group_callerid
msgid "Phone CallerID"
msgstr ""

#. module: base_phone
#: model:ir.model,name:base_phone.model_phone_validation_mixin
msgid "Phone Validation Mixin"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,help:base_phone.field_number_not_found__calling_number
msgid ""
"Phone number of calling party that has been obtained from the telephony "
"server, in the format used by the telephony server (not E.164)."
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__phonenumbers_not_reformatted
msgid "Phone numbers that couldn't be reformatted"
msgstr ""

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.reformat_all_phonenumbers_form
msgid "Phone numbers that couldn't be reformatted:"
msgstr ""

#. module: base_phone
#: model:ir.actions.act_window,name:base_phone.reformat_all_phonenumbers_action
#: model:ir.ui.menu,name:base_phone.reformat_all_phonenumbers_menu
msgid "Reformat Phone Numbers"
msgstr ""

#. module: base_phone
#: model:ir.model,name:base_phone.model_reformat_all_phonenumbers
#: model_terms:ir.ui.view,arch_db:base_phone.reformat_all_phonenumbers_form
msgid "Reformat all phone numbers"
msgstr ""

#. module: base_phone
#. odoo-python
#: code:addons/base_phone/wizard/number_not_found.py:0
#, python-format
msgid "Select the Partner to Update."
msgstr ""

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.res_config_settings_view_form
msgid ""
"Set how many digits must be identical from the end of the phone number to "
"declare it as a match with a partner, a lead, an employee, a candidate, etc."
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__state
msgid "State"
msgstr ""

#. module: base_phone
#: model:ir.ui.menu,name:base_phone.menu_config_phone
#: model_terms:ir.ui.view,arch_db:base_phone.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:base_phone.view_users_form
msgid "Telephony"
msgstr ""

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.view_users_form
msgid "Telephony Preferences"
msgstr ""

#. module: base_phone
#: model:ir.model.constraint,message:base_phone.constraint_res_company_number_of_digits_to_match_from_end_positive
msgid ""
"The value of the field 'Number of Digits To Match From End' must be positive."
msgstr ""

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.reformat_all_phonenumbers_form
msgid ""
"This wizard reformats the phone and mobile numbers of all partners in "
"standard international format e.g. +33 1 41 98 12 42"
msgstr ""

#. module: base_phone
#. odoo-javascript
#: code:addons/base_phone/static/src/components/on_dial_button/on_dial_button.esm.js:0
#, python-format
msgid "Unhook your ringing phone"
msgstr ""

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.number_not_found_form
msgid "Update Existing Partner"
msgstr ""
