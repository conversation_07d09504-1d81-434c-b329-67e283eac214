# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_phone
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-01 07:16+0000\n"
"PO-Revision-Date: 2023-05-01 07:16+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Incoming calls</span>"
msgstr "<span class=\"o_form_label\">Appels entrants</span>"

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__calling_number
msgid "Calling Number"
msgstr "Numéro appellant"

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.reformat_all_phonenumbers_form
msgid "Cancel"
msgstr "Annuler"

#. module: base_phone
#. odoo-javascript
#: code:addons/base_phone/static/src/components/on_dial_button/on_dial_button.esm.js:0
#, python-format
msgid "Click2dial successfull"
msgstr "Click2dial réussi"

#. module: base_phone
#. odoo-javascript
#: code:addons/base_phone/static/src/components/on_dial_button/on_dial_button.esm.js:0
#, python-format
msgid "Click2dial to %s"
msgstr "Click2dial de %s"

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.number_not_found_form
#: model_terms:ir.ui.view,arch_db:base_phone.reformat_all_phonenumbers_form
msgid "Close"
msgstr "Fermer"

#. module: base_phone
#: model:ir.model,name:base_phone.model_phone_common
msgid "Common methods for phone features"
msgstr "Méthodes courantes pour les fonctionnalités du téléphone"

#. module: base_phone
#: model:ir.model,name:base_phone.model_res_company
msgid "Companies"
msgstr "Sociétés"

#. module: base_phone
#: model:ir.model,name:base_phone.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de config"

#. module: base_phone
#: model:ir.model,name:base_phone.model_res_partner
msgid "Contact"
msgstr ""

#. module: base_phone
#. odoo-python
#: code:addons/base_phone/wizard/number_not_found.py:0
#: model_terms:ir.ui.view,arch_db:base_phone.number_not_found_form
#, python-format
msgid "Create New Partner"
msgstr "Créer un nouveau partenaire"

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.number_not_found_form
msgid "Create or Update a Partner"
msgstr "Créer ou mettre à jour un partenaire"

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__create_uid
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__create_date
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__create_date
msgid "Created on"
msgstr "Date"

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__current_partner_mobile
msgid "Current Mobile"
msgstr "Portable actuel"

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__current_partner_phone
msgid "Current Phone"
msgstr "Téléphone actuel"

#. module: base_phone
#. odoo-javascript
#: code:addons/base_phone/static/src/components/on_dial_button/on_dial_button.xml:0
#, python-format
msgid "Dial"
msgstr "Composer"

#. module: base_phone
#. odoo-javascript
#: code:addons/base_phone/static/src/components/on_dial_button/on_dial_button.esm.js:0
#, python-format
msgid "Dial phone"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__display_name
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__display_name
msgid "Display Name"
msgstr "Nom à afficher"

#. module: base_phone
#: model:ir.model.fields.selection,name:base_phone.selection__reformat_all_phonenumbers__state__done
msgid "Done"
msgstr "Fait"

#. module: base_phone
#: model:ir.model.fields.selection,name:base_phone.selection__reformat_all_phonenumbers__state__draft
msgid "Draft"
msgstr "Brouillon"

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__e164_number
msgid "E.164 Number"
msgstr "Numéro E.164"

#. module: base_phone
#: model:ir.model.fields,help:base_phone.field_number_not_found__e164_number
msgid "E.164 equivalent of the calling number."
msgstr "Equivalent au format E.164 du numéro appelant."

#. module: base_phone
#: model:ir.model.fields.selection,name:base_phone.selection__number_not_found__number_type__phone
msgid "Fixed"
msgstr "Fixe"

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__number_type
msgid "Fixed/Mobile"
msgstr "Fixe/Portable"

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__id
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__id
msgid "ID"
msgstr "Identifiant"

#. module: base_phone
#: model:ir.model.fields,help:base_phone.field_res_company__number_of_digits_to_match_from_end
#: model:ir.model.fields,help:base_phone.field_res_config_settings__number_of_digits_to_match_from_end
msgid ""
"In several situations, Odoo will have to find a Partner/Lead/Employee/... "
"from a phone number presented by the calling party. As the phone numbers "
"presented by your phone operator may not always be displayed in a standard "
"format, the best method to find the related Partner/Lead/Employee/... in "
"Odoo is to try to match the end of the phone number in Odoo with the N last "
"digits of the phone number presented by the calling party. N is the value "
"you should enter in this field."
msgstr ""
"Dans différentes situations, Odoo devra trouver un Partenaire/Prospect/"
"Employé/… à partir d’un numéro de téléphone présenté par l’appelant. Comme "
"les numéros de téléphone présentés par votre opérateur téléphonique ne sont "
"pas toujours affichés dans un format standard, la meilleure méthode pour "
"trouver le partenaire/prospect/employé/… associé dans Odoo est d’essayer de "
"faire correspondre la fin du numéro de téléphone dans Odoo. avec les N "
"derniers chiffres du numéro de téléphone présenté par l’appelant. N est la "
"valeur que vous devez saisir dans ce champ."

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__write_uid
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__write_date
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: base_phone
#. odoo-python
#: code:addons/base_phone/wizard/number_not_found.py:0
#, python-format
msgid "Missing country on company %s"
msgstr "Pays non défini pour la société %s"

#. module: base_phone
#: model:ir.model.fields.selection,name:base_phone.selection__number_not_found__number_type__mobile
msgid "Mobile"
msgstr "Portable"

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.number_not_found_form
msgid "Number Not Found"
msgstr "Numéro introuvable"

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.number_not_found_form
msgid "Number converted to international format:"
msgstr "Numéro converti au format international :"

#. module: base_phone
#. odoo-javascript
#: code:addons/base_phone/static/src/components/on_dial_button/on_dial_button.esm.js:0
#, python-format
msgid "Number dialed: %s"
msgstr "Numéro composé : %s"

#. module: base_phone
#: model:ir.model,name:base_phone.model_number_not_found
msgid "Number not found"
msgstr "Numéro introuvable"

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.res_config_settings_view_form
msgid "Number of Digits"
msgstr "Nombre de chiffres à faire correspondre en partant de la fin"

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_res_company__number_of_digits_to_match_from_end
#: model:ir.model.fields,field_description:base_phone.field_res_config_settings__number_of_digits_to_match_from_end
msgid "Number of Digits To Match From End"
msgstr "Nombre de chiffres à faire correspondre en partant de la fin"

#. module: base_phone
#: model:ir.model.fields,help:base_phone.field_number_not_found__to_update_partner_id
msgid "Partner on which the phone number will be written"
msgstr "Partenaire dans lequel le numéro de téléphone sera enregistré"

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__to_update_partner_id
msgid "Partner to Update"
msgstr "Partenaire à mettre à jour"

#. module: base_phone
#. odoo-python
#: code:addons/base_phone/wizard/number_not_found.py:0
#, python-format
msgid "Partner: %s"
msgstr "Partenaire : %s"

#. module: base_phone
#: model:res.groups,name:base_phone.group_callerid
msgid "Phone CallerID"
msgstr "CallerID du téléphone"

#. module: base_phone
#: model:ir.model,name:base_phone.model_phone_validation_mixin
msgid "Phone Validation Mixin"
msgstr "Mixin de la Validation de Téléphones"

#. module: base_phone
#: model:ir.model.fields,help:base_phone.field_number_not_found__calling_number
msgid ""
"Phone number of calling party that has been obtained from the telephony "
"server, in the format used by the telephony server (not E.164)."
msgstr ""
"Numéro de téléphone de la partie appelante qui a été obtenu auprès du "
"serveur de téléphonie, dans le format utilisé par le serveur de téléphonie "
"(et non E.164)."

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__phonenumbers_not_reformatted
msgid "Phone numbers that couldn't be reformatted"
msgstr "Numéros de téléphone qui n'ont pas pu être reformatés"

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.reformat_all_phonenumbers_form
msgid "Phone numbers that couldn't be reformatted:"
msgstr "Numéros de téléphone qui n'ont pas pu être reformatés :"

#. module: base_phone
#: model:ir.actions.act_window,name:base_phone.reformat_all_phonenumbers_action
#: model:ir.ui.menu,name:base_phone.reformat_all_phonenumbers_menu
msgid "Reformat Phone Numbers"
msgstr "Reformate les numéros de téléphone"

#. module: base_phone
#: model:ir.model,name:base_phone.model_reformat_all_phonenumbers
#: model_terms:ir.ui.view,arch_db:base_phone.reformat_all_phonenumbers_form
msgid "Reformat all phone numbers"
msgstr "Reformate tous les numéros de téléphone"

#. module: base_phone
#. odoo-python
#: code:addons/base_phone/wizard/number_not_found.py:0
#, python-format
msgid "Select the Partner to Update."
msgstr "Selectionnez le partenaire à mettre à jour."

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.res_config_settings_view_form
msgid ""
"Set how many digits must be identical from the end of the phone number to "
"declare it as a match with a partner, a lead, an employee, a candidate, etc."
msgstr ""
"Définissez le nombre de chiffres qui doivent être identiques à partir de la "
"fin du numéro de téléphone pour le déclarer comme une correspondance avec un "
"partenaire, un prospect, un employé, un candidat, etc."

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__state
msgid "State"
msgstr "État"

#. module: base_phone
#: model:ir.ui.menu,name:base_phone.menu_config_phone
#: model_terms:ir.ui.view,arch_db:base_phone.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:base_phone.view_users_form
msgid "Telephony"
msgstr "Téléphonie"

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.view_users_form
msgid "Telephony Preferences"
msgstr "Préférences téléphoniques"

#. module: base_phone
#: model:ir.model.constraint,message:base_phone.constraint_res_company_number_of_digits_to_match_from_end_positive
msgid ""
"The value of the field 'Number of Digits To Match From End' must be positive."
msgstr ""
"La valeur du champ 'Nombre de chiffres à faire correspondre à partir de la "
"fin' doit être positive."

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.reformat_all_phonenumbers_form
msgid ""
"This wizard reformats the phone and mobile numbers of all partners in "
"standard international format e.g. +33 1 41 98 12 42"
msgstr ""
"Cet assistant reformate le numéro de téléphone fixe, portable et le fax de "
"tous les partenaires au format standard international i.e. +33141981242"

#. module: base_phone
#. odoo-javascript
#: code:addons/base_phone/static/src/components/on_dial_button/on_dial_button.esm.js:0
#, python-format
msgid "Unhook your ringing phone"
msgstr "Décrochez votre téléphone"

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.number_not_found_form
msgid "Update Existing Partner"
msgstr "Mettre à jour un partenaire existant"

#~ msgid "Last Modified on"
#~ msgstr "Dernière modification le"
