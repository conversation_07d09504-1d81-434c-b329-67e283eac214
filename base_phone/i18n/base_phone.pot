# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_phone
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: base_phone
#: model:ir.model.fields,help:base_phone.field_reformat_all_phonenumbers__google_translate_api_key
msgid ""
"A valid Google API key is required to enable message translation. "
"https://cloud.google.com/translate/docs/setup"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__unsplash_access_key
msgid "Access Key"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__snailmail_cover
msgid "Add a Cover Page"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,help:base_phone.field_reformat_all_phonenumbers__tenor_api_key
msgid ""
"Add a Tenor GIF API key to enable GIFs support. "
"https://developers.google.com/tenor/guides/quickstart#setup"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__alias_domain_id
msgid "Alias Domain"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__module_mail_plugin
msgid "Allow integration with the mail plugins"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__module_google_calendar
msgid "Allow the users to synchronize their calendar  with Google Calendar"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__module_microsoft_calendar
msgid "Allow the users to synchronize their calendar with Outlook Calendar"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__module_base_import
msgid "Allow users to import data from CSV/XLS/XLSX/ODS files"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,help:base_phone.field_reformat_all_phonenumbers__group_multi_currency
msgid "Allows to work in a multi currency environment"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__unsplash_app_id
msgid "Application ID"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,help:base_phone.field_reformat_all_phonenumbers__sfu_server_key
msgid "Base64 encoded key"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__calling_number
msgid "Calling Number"
msgstr ""

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.reformat_all_phonenumbers_form
msgid "Cancel"
msgstr ""

#. module: base_phone
#. odoo-javascript
#: code:addons/base_phone/static/src/components/on_dial_button/on_dial_button.esm.js:0
msgid "Click2dial successfull"
msgstr ""

#. module: base_phone
#. odoo-javascript
#: code:addons/base_phone/static/src/components/on_dial_button/on_dial_button.esm.js:0
msgid "Click2dial to %s"
msgstr ""

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.number_not_found_form
#: model_terms:ir.ui.view,arch_db:base_phone.reformat_all_phonenumbers_form
msgid "Close"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__module_website_cf_turnstile
msgid "Cloudflare Turnstile"
msgstr ""

#. module: base_phone
#: model:ir.model,name:base_phone.model_phone_common
msgid "Common methods for phone features"
msgstr ""

#. module: base_phone
#: model:ir.model,name:base_phone.model_res_company
msgid "Companies"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__company_id
msgid "Company"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__company_country_code
msgid "Company Country Code"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__company_informations
msgid "Company Informations"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__company_name
msgid "Company Name"
msgstr ""

#. module: base_phone
#: model:ir.model,name:base_phone.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: base_phone
#: model:ir.model,name:base_phone.model_res_partner
msgid "Contact"
msgstr ""

#. module: base_phone
#. odoo-python
#: code:addons/base_phone/wizard/number_not_found.py:0
#: model_terms:ir.ui.view,arch_db:base_phone.number_not_found_form
msgid "Create New Partner"
msgstr ""

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.number_not_found_form
msgid "Create or Update a Partner"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__create_uid
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__create_uid
msgid "Created by"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__create_date
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__create_date
msgid "Created on"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__current_partner_mobile
msgid "Current Mobile"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__current_partner_phone
msgid "Current Phone"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__report_footer
msgid "Custom Report Footer"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__auth_signup_uninvited
msgid "Customer Account"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__user_default_rights
msgid "Default Access Rights"
msgstr ""

#. module: base_phone
#. odoo-javascript
#: code:addons/base_phone/static/src/components/on_dial_button/on_dial_button.xml:0
msgid "Dial"
msgstr ""

#. module: base_phone
#. odoo-javascript
#: code:addons/base_phone/static/src/components/on_dial_button/on_dial_button.esm.js:0
msgid "Dial phone"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__display_name
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__display_name
msgid "Display Name"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__external_report_layout_id
msgid "Document Template"
msgstr ""

#. module: base_phone
#: model:ir.model.fields.selection,name:base_phone.selection__reformat_all_phonenumbers__state__done
msgid "Done"
msgstr ""

#. module: base_phone
#: model:ir.model.fields.selection,name:base_phone.selection__reformat_all_phonenumbers__state__draft
msgid "Draft"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__e164_number
msgid "E.164 Number"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,help:base_phone.field_number_not_found__e164_number
msgid "E.164 equivalent of the calling number."
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__email_secondary_color
msgid "Email Button Color"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__email_primary_color
msgid "Email Header Color"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__auth_signup_reset_password
msgid "Enable password reset from Login page"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__fail_counter
msgid "Fail Mail"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,help:base_phone.field_reformat_all_phonenumbers__tenor_gif_limit
msgid "Fetch up to the specified number of GIF."
msgstr ""

#. module: base_phone
#: model:ir.model.fields.selection,name:base_phone.selection__number_not_found__number_type__phone
msgid "Fixed"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__number_type
msgid "Fixed/Mobile"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,help:base_phone.field_reformat_all_phonenumbers__report_footer
msgid "Footer text displayed at the bottom of all reports."
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__module_base_geolocalize
msgid "GeoLocalize"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__module_product_images
msgid "Get product pictures using barcode"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__google_gmail_client_identifier
msgid "Gmail Client Id"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__google_gmail_client_secret
msgid "Gmail Client Secret"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__id
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__id
msgid "ID"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,help:base_phone.field_reformat_all_phonenumbers__alias_domain_id
msgid ""
"If you have setup a catch-all email domain redirected to the Odoo server, "
"enter the domain name here."
msgstr ""

#. module: base_phone
#: model:ir.model.fields,help:base_phone.field_reformat_all_phonenumbers__use_twilio_rtc_servers
msgid "If you want to use twilio as TURN/STUN server provider"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,help:base_phone.field_reformat_all_phonenumbers__number_of_digits_to_match_from_end
#: model:ir.model.fields,help:base_phone.field_res_company__number_of_digits_to_match_from_end
#: model:ir.model.fields,help:base_phone.field_res_config_settings__number_of_digits_to_match_from_end
msgid ""
"In several situations, Odoo will have to find a Partner/Lead/Employee/... "
"from a phone number presented by the calling party. As the phone numbers "
"presented by your phone operator may not always be displayed in a standard "
"format, the best method to find the related Partner/Lead/Employee/... in "
"Odoo is to try to match the end of the phone number in Odoo with the N last "
"digits of the phone number presented by the calling party. N is the value "
"you should enter in this field."
msgstr ""

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.res_config_settings_view_form
msgid "Incoming calls"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__partner_autocomplete_insufficient_credit
msgid "Insufficient credit"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__is_root_company
msgid "Is Root Company"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__module_auth_ldap
msgid "LDAP Authentication"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__write_uid
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__write_uid
msgid "Last Updated by"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__write_date
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__write_date
msgid "Last Updated on"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__module_account_inter_company_rules
msgid "Manage Inter Company"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__google_translate_api_key
msgid "Message Translation API Key"
msgstr ""

#. module: base_phone
#. odoo-python
#: code:addons/base_phone/wizard/number_not_found.py:0
msgid "Missing country on company %s"
msgstr ""

#. module: base_phone
#: model:ir.model.fields.selection,name:base_phone.selection__number_not_found__number_type__mobile
msgid "Mobile"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__group_multi_currency
msgid "Multi-Currencies"
msgstr ""

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.number_not_found_form
msgid "Number Not Found"
msgstr ""

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.number_not_found_form
msgid "Number converted to international format:"
msgstr ""

#. module: base_phone
#. odoo-javascript
#: code:addons/base_phone/static/src/components/on_dial_button/on_dial_button.esm.js:0
msgid "Number dialed: %s"
msgstr ""

#. module: base_phone
#: model:ir.model,name:base_phone.model_number_not_found
msgid "Number not found"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__active_user_count
msgid "Number of Active Users"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__company_count
msgid "Number of Companies"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__number_of_digits_to_match_from_end
#: model:ir.model.fields,field_description:base_phone.field_res_config_settings__number_of_digits_to_match_from_end
msgid "Number of Digits"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_res_company__number_of_digits_to_match_from_end
msgid "Number of Digits To Match From End"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__language_count
msgid "Number of Languages"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__module_partner_autocomplete
msgid "Partner Autocomplete"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,help:base_phone.field_number_not_found__to_update_partner_id
msgid "Partner on which the phone number will be written"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_number_not_found__to_update_partner_id
msgid "Partner to Update"
msgstr ""

#. module: base_phone
#. odoo-python
#: code:addons/base_phone/wizard/number_not_found.py:0
msgid "Partner: %s"
msgstr ""

#. module: base_phone
#: model:res.groups,name:base_phone.group_callerid
msgid "Phone CallerID"
msgstr ""

#. module: base_phone
#: model:ir.model,name:base_phone.model_phone_validation_mixin
msgid "Phone Validation Mixin"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,help:base_phone.field_number_not_found__calling_number
msgid ""
"Phone number of calling party that has been obtained from the telephony "
"server, in the format used by the telephony server (not E.164)."
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__phonenumbers_not_reformatted
msgid "Phone numbers that couldn't be reformatted"
msgstr ""

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.reformat_all_phonenumbers_form
msgid "Phone numbers that couldn't be reformatted:"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__snailmail_duplex
msgid "Print Both sides"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__snailmail_color
msgid "Print In Color"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__profiling_enabled_until
msgid "Profiling enabled until"
msgstr ""

#. module: base_phone
#: model:ir.actions.act_window,name:base_phone.reformat_all_phonenumbers_action
#: model:ir.ui.menu,name:base_phone.reformat_all_phonenumbers_menu
msgid "Reformat Phone Numbers"
msgstr ""

#. module: base_phone
#: model:ir.model,name:base_phone.model_reformat_all_phonenumbers
#: model_terms:ir.ui.view,arch_db:base_phone.reformat_all_phonenumbers_form
msgid "Reformat all phone numbers"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__restrict_template_rendering
msgid "Restrict Template Rendering"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__sfu_server_url
msgid "SFU Server URL"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__sfu_server_key
msgid "SFU Server key"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__module_sms
msgid "SMS"
msgstr ""

#. module: base_phone
#. odoo-python
#: code:addons/base_phone/wizard/number_not_found.py:0
msgid "Select the Partner to Update."
msgstr ""

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.res_config_settings_view_form
msgid ""
"Set how many digits must be identical from the end of the phone number to "
"declare it as a match with a partner, a lead, an employee, a candidate, etc."
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__show_effect
msgid "Show Effect"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__snailmail_cover_readonly
msgid "Snailmail Cover Readonly"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__state
msgid "State"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__module_google_gmail
msgid "Support Gmail Authentication"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__module_microsoft_outlook
msgid "Support Outlook Authentication"
msgstr ""

#. module: base_phone
#: model:ir.ui.menu,name:base_phone.menu_config_phone
#: model_terms:ir.ui.view,arch_db:base_phone.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:base_phone.view_users_form
msgid "Telephony"
msgstr ""

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.view_users_form
msgid "Telephony Preferences"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__auth_signup_template_user_id
msgid "Template user for new users created through signup"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__tenor_api_key
msgid "Tenor API key"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__tenor_gif_limit
msgid "Tenor Gif Limit"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__tenor_content_filter
msgid "Tenor content filter"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,help:base_phone.field_reformat_all_phonenumbers__company_country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""

#. module: base_phone
#: model:ir.model.constraint,message:base_phone.constraint_res_company_number_of_digits_to_match_from_end_positive
msgid ""
"The value of the field 'Number of Digits To Match From End' must be "
"positive."
msgstr ""

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.reformat_all_phonenumbers_form
msgid ""
"This wizard reformats the phone and mobile numbers of all partners in "
"standard international format e.g. +33 1 41 98 12 42"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__twilio_account_token
msgid "Twilio Account Auth Token"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__twilio_account_sid
msgid "Twilio Account SID"
msgstr ""

#. module: base_phone
#. odoo-javascript
#: code:addons/base_phone/static/src/components/on_dial_button/on_dial_button.esm.js:0
msgid "Unhook your ringing phone"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__module_web_unsplash
msgid "Unsplash Image Library"
msgstr ""

#. module: base_phone
#: model_terms:ir.ui.view,arch_db:base_phone.number_not_found_form
msgid "Update Existing Partner"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__external_email_server_default
msgid "Use Custom Email Servers"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__use_twilio_rtc_servers
msgid "Use Twilio ICE servers"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__module_auth_oauth
msgid "Use external authentication providers (OAuth)"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,help:base_phone.field_reformat_all_phonenumbers__restrict_template_rendering
msgid ""
"Users will still be able to render templates.\n"
"However only Mail Template Editors will be able to create new dynamic templates or modify existing ones."
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__module_voip
msgid "VoIP"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__web_app_name
msgid "Web App Name"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,help:base_phone.field_reformat_all_phonenumbers__tenor_content_filter
msgid "https://developers.google.com/tenor/guides/content-filtering"
msgstr ""

#. module: base_phone
#: model:ir.model.fields,field_description:base_phone.field_reformat_all_phonenumbers__module_google_recaptcha
msgid "reCAPTCHA"
msgstr ""
