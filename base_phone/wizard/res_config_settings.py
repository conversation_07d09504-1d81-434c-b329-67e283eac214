# Copyright 2017-2021 Akretion France (http://www.akretion.com/)
# @author: <PERSON> <<EMAIL>>
# License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl).

from odoo import fields, models


class ResConfigSettings(models.TransientModel):
    _inherit = "res.config.settings"

    number_of_digits_to_match_from_end = fields.Integer(
        string="Number of Digits",
        related="company_id.number_of_digits_to_match_from_end",
        readonly=False,
    )
