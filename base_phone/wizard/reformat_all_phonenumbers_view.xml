<?xml version="1.0" encoding="utf-8" ?>
<!--
  Copyright (C) 2012-2021 Akretion France (http://www.akretion.com/)
  @author: <PERSON> <<EMAIL>>
  License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl).
-->
<odoo>
    <record id="reformat_all_phonenumbers_form" model="ir.ui.view">
        <field name="name">reformat_all_phonenumbers.form</field>
        <field name="model">reformat.all.phonenumbers</field>
        <field name="arch" type="xml">
            <form string="Reformat all phone numbers">
                <div invisible="state not in ('draft')">
                    This wizard reformats the phone and mobile numbers of all partners in standard international format e.g. +33 1 41 98 12 42
                </div>

                <div invisible="state not in ('done')">
                    Phone numbers that couldn't be reformatted:
                </div>

                <field
                    name="phonenumbers_not_reformatted"
                    nolabel="1"
                    colspan="2"
                    invisible="state not in ('done')"
                    readonly="1"
                />

                <footer>
                    <button
                        name="run_reformat_all_phonenumbers"
                        string="Reformat all phone numbers"
                        type="object"
                        class="btn-primary"
                        invisible="state not in ('draft')"
                    />
                    <button
                        name="execute"
                        type="object"
                        string="Close"
                        class="btn-primary"
                        invisible="state not in ('done')"
                    />
                    <button
                        special="cancel"
                        string="Cancel"
                        class="btn-default"
                        invisible="state not in ('draft')"
                    />
                </footer>
            </form>
        </field>
    </record>
    <record id="reformat_all_phonenumbers_action" model="ir.actions.act_window">
        <field name="name">Reformat Phone Numbers</field>
        <field name="res_model">reformat.all.phonenumbers</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>
    <!-- Menu entry under Settings > Technical -->
    <menuitem id="menu_config_phone" name="Telephony" parent="base.menu_custom" />
    <menuitem
        id="reformat_all_phonenumbers_menu"
        action="reformat_all_phonenumbers_action"
        parent="menu_config_phone"
        sequence="100"
    />
    <!-- Open the Reformat Phone Numbers wizard after the installation of the module -->
    <record id="reformat_all_phonenumbers_module_install" model="ir.actions.todo">
        <field name="action_id" ref="reformat_all_phonenumbers_action" />
    </record>
</odoo>
