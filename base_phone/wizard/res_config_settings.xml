<?xml version="1.0" encoding="utf-8" ?>
<!--
  Copyright 2014-2019 Akretion France (http://www.akretion.com/)
  @author: <PERSON> <<EMAIL>>
  License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl).
-->
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">base_phone.base.config.settings.form</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="base_setup.res_config_settings_view_form" />
        <field name="arch" type="xml">
            <block id="user_default_rights" position="after">
                <block id="telephony" title="Telephony">
                    <setting
                        id="account_uom"
                        string="Incoming calls"
                        help="Set how many digits must be identical from the end of the phone number to declare it as a match with a partner, a lead, an employee, a candidate, etc."
                    >
                        <div class="d-flex content-group mt16">
                            <label
                                for="number_of_digits_to_match_from_end"
                                class="mr8"
                            />
                            <field name="number_of_digits_to_match_from_end" />
                        </div>
                    </setting>
                </block>
            </block>
        </field>
    </record>
</odoo>
