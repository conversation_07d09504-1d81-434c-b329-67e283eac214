//
//  APIConfiguration.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 22/7/25.
//

import Foundation

// MARK: - API Environment
enum APIEnvironment: String, CaseIterable {
    case development = "development"
    case staging = "staging"
    case production = "production"
    case custom = "custom"
    
    var displayName: String {
        switch self {
        case .development:
            return "Development"
        case .staging:
            return "Staging"
        case .production:
            return "Production"
        case .custom:
            return "Custom"
        }
    }
    
    var defaultBaseURL: String {
        switch self {
        case .development:
            return "https://lms-dev.ebill.vn/api/v1/"
        case .staging:
            return "https://lms-dev.ebill.vn/api/v1/"
        case .production:
            return "https://lms-dev.ebill.vn/api/v1/"
        case .custom:
            return UserDefaults.standard.string(forKey: "custom_api_url") ?? "https://lms-dev.ebill.vn/api/v1/"
        }
    }
}

// MARK: - API Configuration
class APIConfiguration: ObservableObject {
    static let shared = APIConfiguration()
    
    @Published var currentEnvironment: APIEnvironment
    @Published var customBaseURL: String = ""
    
    private let userDefaults = UserDefaults.standard
    private let environmentKey = "api_environment"
    private let customURLKey = "custom_api_url"
    
    private init() {
        // Load saved environment or default to current build environment
        if let savedEnv = userDefaults.string(forKey: environmentKey),
           let environment = APIEnvironment(rawValue: savedEnv) {
            self.currentEnvironment = environment
        } else {
            #if DEBUG
            self.currentEnvironment = .development
            #elseif STAGING
            self.currentEnvironment = .staging
            #else
            self.currentEnvironment = .production
            #endif
        }
        
        self.customBaseURL = userDefaults.string(forKey: customURLKey) ?? ""
    }
    
    // MARK: - Current Configuration
    var baseURL: String {
        switch currentEnvironment {
        case .custom:
            return customBaseURL.isEmpty ? currentEnvironment.defaultBaseURL : customBaseURL
        default:
            return currentEnvironment.defaultBaseURL
        }
    }
    
    var apiVersion: String {
        return "v1"
    }
    
    var timeout: TimeInterval {
        return 30.0
    }
    
    var retryAttempts: Int {
        return 3
    }
    
    var retryDelay: TimeInterval {
        return 1.0
    }
    
    // MARK: - Environment Management
    func setEnvironment(_ environment: APIEnvironment) {
        currentEnvironment = environment
        userDefaults.set(environment.rawValue, forKey: environmentKey)
        
        // Notify observers about configuration change
        NotificationCenter.default.post(name: .apiConfigurationChanged, object: nil)
    }
    
    func setCustomURL(_ url: String) {
        customBaseURL = url
        userDefaults.set(url, forKey: customURLKey)
        
        if currentEnvironment == .custom {
            NotificationCenter.default.post(name: .apiConfigurationChanged, object: nil)
        }
    }
    
    // MARK: - URL Building
    func buildURL(endpoint: String) -> String {
        // Remove trailing slash from baseURL and leading slash from endpoint to avoid double slashes
        let cleanBaseURL = baseURL.hasSuffix("/") ? String(baseURL.dropLast()) : baseURL
        let cleanEndpoint = endpoint.hasPrefix("/") ? endpoint : "/\(endpoint)"
        return cleanBaseURL + cleanEndpoint
    }
    
    func buildURL(endpoint: String, pathParameters: [String: String]) -> String {
        var finalEndpoint = endpoint
        
        for (key, value) in pathParameters {
            finalEndpoint = finalEndpoint.replacingOccurrences(of: "{\(key)}", with: value)
        }
        
        return buildURL(endpoint: finalEndpoint)
    }
    
    // MARK: - Headers
    var defaultHeaders: [String: String] {
        return [
            "Content-Type": "application/json",
            "Accept": "application/json",
            "X-Platform": "iOS",
            "X-App-Version": AppConstants.AppInfo.fullVersion,
            "Accept-Language": Locale.current.languageCode ?? "en"
        ]
    }
    
    // MARK: - Validation
    func validateConfiguration() -> Bool {
        guard let url = URL(string: baseURL) else {
            return false
        }
        
        return url.scheme != nil && url.host != nil
    }
    
    // MARK: - Debug Information
    var debugInfo: [String: Any] {
        return [
            "environment": currentEnvironment.rawValue,
            "baseURL": baseURL,
            "apiVersion": apiVersion,
            "timeout": timeout,
            "retryAttempts": retryAttempts,
            "isValid": validateConfiguration()
        ]
    }
}

// MARK: - Notification Names
extension Notification.Name {
    static let apiConfigurationChanged = Notification.Name("APIConfigurationChanged")
}

// MARK: - Configuration Presets
extension APIConfiguration {
    
    // Quick setup methods for common scenarios
    func setupForDevelopment() {
        setEnvironment(.development)
    }
    
    func setupForStaging() {
        setEnvironment(.staging)
    }
    
    func setupForProduction() {
        setEnvironment(.production)
    }
    
    func setupCustom(baseURL: String) {
        setCustomURL(baseURL)
        setEnvironment(.custom)
    }
    
    // Reset to default
    func resetToDefault() {
        #if DEBUG
        setEnvironment(.development)
        #elseif STAGING
        setEnvironment(.staging)
        #else
        setEnvironment(.production)
        #endif
        setCustomURL("")
    }
}

// MARK: - SwiftUI Integration
extension APIConfiguration {
    var environmentBinding: Binding<APIEnvironment> {
        Binding(
            get: { self.currentEnvironment },
            set: { self.setEnvironment($0) }
        )
    }
    
    var customURLBinding: Binding<String> {
        Binding(
            get: { self.customBaseURL },
            set: { self.setCustomURL($0) }
        )
    }
}
