//
//  InstructorAuthService.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 22/7/25.
//

import Foundation
import Combine

// MARK: - Instructor Authentication Service
class InstructorAuthService: ObservableObject {
    static let shared = InstructorAuthService()
    
    private let apiClient = APIClient.shared
    private let tokenManager = TokenManager.shared
    private var cancellables = Set<AnyCancellable>()
    
    // Published properties for UI binding
    @Published var isAuthenticated = false
    @Published var currentInstructor: InstructorUser?
    @Published var isLoading = false
    @Published var authError: String?
    
    private init() {
        // FORCE clean state on init to prevent any auto-signin
        print("🔐 InstructorAuthService: Initializing with FORCED clean state")
        isAuthenticated = false
        currentInstructor = nil
        isLoading = false
        authError = nil

        // Set up API configuration for LMS
        setupLMSConfiguration()

        // Don't auto-check auth state on init to prevent auto-signin
        // checkAuthState() will be called manually when needed

        // Listen for configuration changes
        NotificationCenter.default.publisher(for: .apiConfigurationChanged)
            .sink { [weak self] _ in
                self?.handleConfigurationChange()
            }
            .store(in: &cancellables)

        print("🔐 InstructorAuthService: Initialization complete - isAuthenticated: \(isAuthenticated)")
    }
    
    private func setupLMSConfiguration() {
        // Configure API for LMS endpoint
        APIConfiguration.shared.setupCustom(baseURL: "https://lms-dev.ebill.vn/api/v1")
    }
    
    private func handleConfigurationChange() {
        print("API Configuration changed - manual auth check required")
        // Don't auto-check auth state to prevent auto-signin
        // Call checkAuthState() manually if needed
    }
    
    // MARK: - Authentication State Management

    /// Manually check authentication state - call this when you want to verify auth
    func checkAuthState() {
        // Check if we have a valid token
        guard let token = tokenManager.getToken() else {
            setUnauthenticated()
            return
        }
        
        // Validate token and check if user is instructor
        if JWTHelper.isTokenExpired(token) {
            print("Token is expired")
            setUnauthenticated()
            return
        }
        
        if !JWTHelper.hasInstructorRole(token: token) {
            print("User does not have instructor role")
            setUnauthenticated()
            return
        }
        
        // Try to get cached user data
        if let cachedUser = tokenManager.getUser() {
            // Convert cached User to InstructorUser if possible
            if let instructorUser = convertToInstructorUser(cachedUser) {
                setAuthenticated(user: instructorUser)
                return
            }
        }
        
        // If we have a valid token but no cached user, fetch user data
        Task {
            await fetchCurrentUser()
        }
    }
    
    @MainActor
    private func setAuthenticated(user: InstructorUser) {
        isAuthenticated = true
        currentInstructor = user
        authError = nil
        print("✅ Instructor authenticated: \(user.displayName)")
    }
    
    @MainActor
    private func setUnauthenticated() {
        isAuthenticated = false
        currentInstructor = nil
        // Don't clear token here as it might be used by other parts of the app
        print("❌ Instructor not authenticated")
    }
    
    // MARK: - Login
    
    @MainActor
    func login(username: String, password: String, rememberMe: Bool = false) async throws {
        isLoading = true
        authError = nil
        
        defer {
            isLoading = false
        }
        
        do {
            let deviceInfo = InstructorLoginRequest.DeviceInfo.current()
            
            let request = InstructorLoginRequest(
                username: username,
                password: password,
                deviceInfo: deviceInfo,
                rememberMe: rememberMe,
                mfaCode: nil // Can be added later for MFA support
            )
            
            print("🔐 Attempting instructor login for: \(username)")
            
            let response: InstructorLoginResponse = try await apiClient.request(
                endpoint: "/auth/sign-in",
                method: .POST,
                parameters: try request.toDictionary(),
                responseType: InstructorLoginResponse.self,
                requiresAuth: false
            )
            
            guard response.success, let loginData = response.data else {
                let errorMessage = response.message ?? "Login failed"
                print("❌ Login failed: \(errorMessage)")
                authError = errorMessage
                throw InstructorAuthError(code: "LOGIN_FAILED", message: errorMessage, details: nil)
            }
            
            // Validate that user has instructor role
            guard loginData.user.isInstructor else {
                let errorMessage = "Access denied. Only instructors can access this application."
                print("❌ Access denied: User is not an instructor")
                authError = errorMessage
                throw InstructorAuthError(code: "ACCESS_DENIED", message: errorMessage, details: nil)
            }
            
            // Save tokens
            tokenManager.saveToken(loginData.accessToken)
            if let refreshToken = loginData.refreshToken {
                try? KeychainManager.shared.saveRefreshToken(refreshToken)
            }
            
            // Convert and save user data
            let convertedUser = convertToAppUser(loginData.user)
            tokenManager.saveUser(convertedUser)
            
            // Update authentication state
            setAuthenticated(user: loginData.user)
            
            print("✅ Instructor login successful: \(loginData.user.displayName)")
            
        } catch {
            print("❌ Login error: \(error)")
            authError = error.localizedDescription
            throw error
        }
    }
    
    // MARK: - Logout
    
    @MainActor
    func logout() async {
        isLoading = true
        
        defer {
            isLoading = false
        }
        
        do {
            // Try to call logout endpoint
            let _: ResponseBase = try await apiClient.request(
                endpoint: "/auth/sign-out",
                method: .POST,
                responseType: ResponseBase.self,
                requiresAuth: true
            )
            print("✅ Server logout successful")
        } catch {
            print("⚠️ Server logout failed, continuing with local logout: \(error)")
        }
        
        // Clear local data completely
        tokenManager.clearToken()
        try? KeychainManager.shared.deleteRefreshToken()
        try? KeychainManager.shared.deleteAccessToken()
        try? KeychainManager.shared.delete(key: "saved_credentials")

        // Clear UserDefaults
        UserDefaults.standard.removeObject(forKey: "saved_email")
        UserDefaults.standard.removeObject(forKey: "linkx_access_token")
        UserDefaults.standard.removeObject(forKey: "linkx_current_user")

        // Update state
        setUnauthenticated()

        print("✅ Local logout completed - all data cleared")
    }
    
    // MARK: - Fetch Current User
    
    private func fetchCurrentUser() async {
        do {
            let apiUser: APIUser = try await apiClient.request(
                endpoint: "/auth/me",
                method: .GET,
                responseType: APIUser.self,
                requiresAuth: true
            )

            // Convert APIUser to InstructorUser
            if let instructorUser = convertAPIUserToInstructor(apiUser) {
                await MainActor.run {
                    setAuthenticated(user: instructorUser)
                }
            } else {
                await MainActor.run {
                    setUnauthenticated()
                }
            }

        } catch {
            print("❌ Failed to fetch current user: \(error)")
            await MainActor.run {
                setUnauthenticated()
            }
        }
    }
    
    // MARK: - Token Refresh
    
    func refreshToken() async throws {
        guard let refreshToken = try? KeychainManager.shared.loadRefreshToken() else {
            throw InstructorAuthError(code: "NO_REFRESH_TOKEN", message: "No refresh token available", details: nil)
        }
        
        let response: TokenResponse = try await apiClient.request(
            endpoint: "/auth/refresh",
            method: .POST,
            parameters: ["refresh_token": refreshToken],
            responseType: TokenResponse.self,
            requiresAuth: false
        )
        
        // Save new tokens
        tokenManager.saveToken(response.accessToken)
        if let newRefreshToken = response.refreshToken {
            try? KeychainManager.shared.saveRefreshToken(newRefreshToken)
        }
        
        print("✅ Token refreshed successfully")
    }
    
    // MARK: - Helper Methods

    private func convertAPIUserToInstructor(_ apiUser: APIUser) -> InstructorUser? {
        return InstructorUser(
            id: String(apiUser.id),
            username: apiUser.login,
            email: apiUser.email ?? apiUser.login,
            firstName: apiUser.name.components(separatedBy: " ").first,
            lastName: apiUser.name.components(separatedBy: " ").count > 1 ?
                     apiUser.name.components(separatedBy: " ").dropFirst().joined(separator: " ") : nil,
            avatar: nil,
            isActive: true,
            isVerified: true,
            createdAt: Date(),
            updatedAt: Date()
        )
    }

    private func convertToInstructorUser(_ user: User) -> InstructorUser? {
        // This is a simplified conversion - you might need to adjust based on your User model
        return InstructorUser(
            id: Int(user.id) ?? 0,
            username: user.email,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            fullName: user.displayName,
            phone: user.phone,
            avatar: user.avatar,
            isActive: user.isActive,
            isVerified: true,
            roles: [UserRole(id: 1, name: "instructor", displayName: "Instructor", description: nil, permissions: nil)],
            permissions: nil,
            profile: nil,
            createdAt: ISO8601DateFormatter().string(from: user.createdAt),
            updatedAt: user.updatedAt != nil ? ISO8601DateFormatter().string(from: user.updatedAt!) : nil,
            lastLoginAt: user.lastLoginAt != nil ? ISO8601DateFormatter().string(from: user.lastLoginAt!) : nil
        )
    }
    
    func convertToAppUser(_ instructorUser: InstructorUser) -> User {
        let dateFormatter = ISO8601DateFormatter()
        
        return User(
            id: String(instructorUser.id),
            email: instructorUser.email,
            firstName: instructorUser.firstName,
            lastName: instructorUser.lastName,
            phone: instructorUser.phone,
            walletAddress: nil,
            role: .admin, // Map instructor to admin role in app
            isActive: instructorUser.isActive,
            avatar: instructorUser.avatar,
            dateOfBirth: nil,
            lastLoginAt: instructorUser.lastLoginAt != nil ? dateFormatter.date(from: instructorUser.lastLoginAt!) : nil,
            createdAt: dateFormatter.date(from: instructorUser.createdAt) ?? Date(),
            updatedAt: instructorUser.updatedAt != nil ? dateFormatter.date(from: instructorUser.updatedAt!) : nil,
            businessName: nil,
            businessId: nil,
            category: nil,
            businessPhone: nil,
            website: nil,
            businessDescription: nil,
            businessStatus: nil,
            onboardedAt: nil
        )
    }
    
    private func convertUserInfoToInstructor(_ userInfo: UserInfoResponse) -> InstructorUser? {
        // Convert UserInfoResponse to InstructorUser
        // This is a simplified conversion - adjust based on your actual API response
        guard let user = userInfo.user as? InstructorUser else {
            return nil
        }
        return user
    }
}

// MARK: - Helper Extensions
extension Encodable {
    func toDictionary() throws -> [String: Any] {
        let data = try JSONEncoder().encode(self)
        guard let dictionary = try JSONSerialization.jsonObject(with: data, options: .allowFragments) as? [String: Any] else {
            throw NSError(domain: "EncodingError", code: 0, userInfo: [NSLocalizedDescriptionKey: "Failed to convert to dictionary"])
        }
        return dictionary
    }
}
