import SwiftUI
import UserNotifications

struct NotificationSettingsView: View {
    @ObservedObject var viewModel: NotificationsViewModel
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            List {
                // General Settings
                Section("General") {
                    SettingsToggleRow(
                        title: "Push Notifications",
                        subtitle: "Receive notifications on this device",
                        icon: "bell",
                        isOn: $viewModel.notificationSettings.pushNotificationsEnabled
                    )
                    
                    SettingsToggleRow(
                        title: "Sound",
                        subtitle: "Play sound for notifications",
                        icon: "speaker.wave.2",
                        isOn: $viewModel.notificationSettings.soundEnabled
                    )
                    
                    SettingsToggleRow(
                        title: "Vibration",
                        subtitle: "Vibrate for notifications",
                        icon: "iphone.radiowaves.left.and.right",
                        isOn: $viewModel.notificationSettings.vibrationEnabled
                    )
                }
                
                // Notification Types
                Section("Notification Types") {
                    SettingsToggleRow(
                        title: "Transactions",
                        subtitle: "Payment confirmations and updates",
                        icon: "creditcard",
                        isOn: $viewModel.notificationSettings.transactionNotifications
                    )
                    
                    SettingsToggleRow(
                        title: "Rewards",
                        subtitle: "New rewards and point updates",
                        icon: "gift",
                        isOn: $viewModel.notificationSettings.rewardNotifications
                    )
                    
                    SettingsToggleRow(
                        title: "Merchants",
                        subtitle: "New merchants and updates",
                        icon: "storefront",
                        isOn: $viewModel.notificationSettings.merchantNotifications
                    )
                    
                    SettingsToggleRow(
                        title: "Promotions",
                        subtitle: "Special offers and deals",
                        icon: "megaphone",
                        isOn: $viewModel.notificationSettings.promotionNotifications
                    )
                    
                    SettingsToggleRow(
                        title: "Security",
                        subtitle: "Security alerts and warnings",
                        icon: "shield",
                        isOn: $viewModel.notificationSettings.securityNotifications
                    )
                    
                    SettingsToggleRow(
                        title: "System",
                        subtitle: "App updates and maintenance",
                        icon: "gear",
                        isOn: $viewModel.notificationSettings.systemNotifications
                    )
                }
                
                // Quiet Hours
                Section("Quiet Hours") {
                    SettingsToggleRow(
                        title: "Enable Quiet Hours",
                        subtitle: "Silence notifications during specified hours",
                        icon: "moon",
                        isOn: $viewModel.notificationSettings.quietHoursEnabled
                    )
                    
                    if viewModel.notificationSettings.quietHoursEnabled {
                        HStack {
                            Image(systemName: "clock")
                                .foregroundColor(AppConstants.Colors.primary)
                                .frame(width: 24)
                            
                            VStack(alignment: .leading, spacing: 2) {
                                Text("Start Time")
                                    .font(.subheadline)
                                    .foregroundColor(AppConstants.Colors.textPrimary)
                                
                                Text(viewModel.notificationSettings.quietHoursStart)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            
                            Spacer()
                            
                            Text(viewModel.notificationSettings.quietHoursStart)
                                .foregroundColor(AppConstants.Colors.primary)
                        }
                        .contentShape(Rectangle())
                        .onTapGesture {
                            // Show time picker for start time
                        }
                        
                        HStack {
                            Image(systemName: "clock")
                                .foregroundColor(AppConstants.Colors.primary)
                                .frame(width: 24)
                            
                            VStack(alignment: .leading, spacing: 2) {
                                Text("End Time")
                                    .font(.subheadline)
                                    .foregroundColor(AppConstants.Colors.textPrimary)
                                
                                Text(viewModel.notificationSettings.quietHoursEnd)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            
                            Spacer()
                            
                            Text(viewModel.notificationSettings.quietHoursEnd)
                                .foregroundColor(AppConstants.Colors.primary)
                        }
                        .contentShape(Rectangle())
                        .onTapGesture {
                            // Show time picker for end time
                        }
                    }
                }
                
                // Actions
                Section("Actions") {
                    Button("Test Notification") {
                        sendTestNotification()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                    
                    Button("Reset to Defaults") {
                        resetToDefaults()
                    }
                    .foregroundColor(.orange)
                }
                
                // Information
                Section("Information") {
                    HStack {
                        Image(systemName: "info.circle")
                            .foregroundColor(.blue)
                        
                        VStack(alignment: .leading, spacing: 2) {
                            Text("Notification Permissions")
                                .font(.subheadline)
                                .foregroundColor(AppConstants.Colors.textPrimary)
                            
                            Text("Manage notification permissions in iOS Settings")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        Button("Open Settings") {
                            openAppSettings()
                        }
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.primary)
                    }
                }
            }
            .navigationTitle("Notification Settings")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        viewModel.saveNotificationSettings()
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
    }
    
    // MARK: - Actions
    private func sendTestNotification() {
        // Send a test notification
        let content = UNMutableNotificationContent()
        content.title = "Test Notification"
        content.body = "This is a test notification from Vantis Instructor"
        content.sound = viewModel.notificationSettings.soundEnabled ? .default : nil
        
        let request = UNNotificationRequest(
            identifier: UUID().uuidString,
            content: content,
            trigger: UNTimeIntervalNotificationTrigger(timeInterval: 1, repeats: false)
        )
        
        UNUserNotificationCenter.current().add(request)
    }
    
    private func resetToDefaults() {
        viewModel.notificationSettings = NotificationSettings()
    }
    
    private func openAppSettings() {
        if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(settingsUrl)
        }
    }
}

// MARK: - Settings Toggle Row
struct SettingsToggleRow: View {
    let title: String
    let subtitle: String
    let icon: String
    @Binding var isOn: Bool
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(AppConstants.Colors.primary)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Toggle("", isOn: $isOn)
                .labelsHidden()
        }
    }
}

// MARK: - Preview
#Preview {
    NotificationSettingsView(viewModel: NotificationsViewModel())
}
