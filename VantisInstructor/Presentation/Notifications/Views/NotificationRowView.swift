import SwiftUI

struct NotificationRowView: View {
    let notification: AppNotification
    let onTap: () -> Void
    let onMarkAsRead: () -> Void
    let onDelete: () -> Void
    
    @State private var showingActionSheet = false
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                // Icon
                notificationIcon
                
                // Content
                VStack(alignment: .leading, spacing: 4) {
                    // Title and timestamp
                    HStack {
                        Text(notification.title)
                            .font(.subheadline)
                            .fontWeight(notification.isRead ? .medium : .semibold)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                            .lineLimit(1)
                        
                        Spacer()
                        
                        Text(notification.formattedDate)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    // Message
                    Text(notification.message)
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .lineLimit(2)
                        .multilineTextAlignment(.leading)
                    
                    // Type and priority badges
                    HStack(spacing: 8) {
                        // Type badge
                        HStack(spacing: 4) {
                            Image(systemName: notification.type.icon)
                                .font(.caption2)
                            Text(notification.type.displayName)
                                .font(.caption2)
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(colorForType(notification.type).opacity(0.1))
                        )
                        .foregroundColor(colorForType(notification.type))
                        
                        // Priority badge (if high or urgent)
                        if notification.priority == .high || notification.priority == .urgent {
                            HStack(spacing: 2) {
                                Image(systemName: "exclamationmark")
                                    .font(.caption2)
                                Text(notification.priority.displayName)
                                    .font(.caption2)
                            }
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(
                                RoundedRectangle(cornerRadius: 6)
                                    .fill(notification.priority == .urgent ? Color.red.opacity(0.1) : Color.orange.opacity(0.1))
                            )
                            .foregroundColor(notification.priority == .urgent ? .red : .orange)
                        }
                        
                        Spacer()
                    }
                }
                
                // Unread indicator
                if !notification.isRead {
                    Circle()
                        .fill(AppConstants.Colors.primary)
                        .frame(width: 8, height: 8)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(notification.isRead ? AppConstants.Colors.surface : AppConstants.Colors.surface.opacity(0.8))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(
                                notification.isRead ? Color.clear : AppConstants.Colors.primary.opacity(0.2),
                                lineWidth: 1
                            )
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .contextMenu {
            contextMenuItems
        }
        .actionSheet(isPresented: $showingActionSheet) {
            ActionSheet(
                title: Text("Notification Options"),
                buttons: [
                    .default(Text(notification.isRead ? "Mark as Unread" : "Mark as Read")) {
                        onMarkAsRead()
                    },
                    .destructive(Text("Delete")) {
                        onDelete()
                    },
                    .cancel()
                ]
            )
        }
    }
    
    // MARK: - Notification Icon
    private var notificationIcon: some View {
        ZStack {
            Circle()
                .fill(colorForType(notification.type).opacity(0.1))
                .frame(width: 40, height: 40)
            
            Image(systemName: notification.type.icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(colorForType(notification.type))
        }
    }
    
    // MARK: - Context Menu Items
    private var contextMenuItems: some View {
        Group {
            Button(action: onTap) {
                Label("Open", systemImage: "arrow.up.right")
            }
            
            Button(action: onMarkAsRead) {
                Label(
                    notification.isRead ? "Mark as Unread" : "Mark as Read",
                    systemImage: notification.isRead ? "envelope.badge" : "envelope.open"
                )
            }
            
            Divider()
            
            Button(role: .destructive, action: onDelete) {
                Label("Delete", systemImage: "trash")
            }
        }
    }
    
    // MARK: - Helper Methods
    private func colorForType(_ type: NotificationType) -> Color {
        switch type {
        case .transaction: return .blue
        case .itemUpdated: return .green
        case .merchant: return .orange
        case .system: return .gray
        case .promotion: return .purple
        case .security: return .red
        case .update: return .indigo
        }
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 16) {
        NotificationRowView(
            notification: AppNotification(
                title: "Payment Received",
                message: "You received 50 LXT from Highlands Coffee transaction",
                type: .transaction,
                category: .all,
                timestamp: Date(),
                isRead: false,
                priority: .normal
            ),
            onTap: {},
            onMarkAsRead: {},
            onDelete: {}
        )
        
        NotificationRowView(
            notification: AppNotification(
                title: "Security Alert",
                message: "New device login detected. If this wasn't you, please secure your account immediately.",
                type: .security,
                category: .all,
                timestamp: Calendar.current.date(byAdding: .hour, value: -2, to: Date()) ?? Date(),
                isRead: true,
                priority: .urgent
            ),
            onTap: {},
            onMarkAsRead: {},
            onDelete: {}
        )
        
        NotificationRowView(
            notification: AppNotification(
                title: "New Reward Available",
                message: "Earn 2x points at CGV Cinemas this weekend!",
                type: .itemUpdated,
                category: .all,
                timestamp: Calendar.current.date(byAdding: .day, value: -1, to: Date()) ?? Date(),
                isRead: false,
                priority: .high
            ),
            onTap: {},
            onMarkAsRead: {},
            onDelete: {}
        )
    }
    .padding()
    .background(Color(.systemGray6))
}
