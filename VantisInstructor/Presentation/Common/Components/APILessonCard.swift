//
//  APILessonCard.swift
//  mobile-app-template
//
//  Created by Inst<PERSON>ctor <PERSON>pp on 28/7/25.
//

import SwiftUI

struct APILessonCard: View {
    let lesson: APILesson
    let onTap: () -> Void
    @State private var showingLessonDetail = false
    
    var body: some View {
        Button(action: {
            showingLessonDetail = true
        }) {
            VStack(alignment: .leading, spacing: 12) {
                // Header with course info and status
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(lesson.classCode)
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)

                        Text(lesson.name)
                            .font(AppConstants.Typography.headline)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                            .lineLimit(2)
                            .multilineTextAlignment(.leading)
                    }
                    
                    Spacer()
                    
                    // Status badge
                    StatusBadge(
                        text: lesson.statusBadgeText,
                        color: lesson.statusBadgeColor,
                        isToday: lesson.isToday,
                        needsAttention: lesson.needsAttention
                    )
                }
                
                // Time and location info
                HStack(spacing: 16) {
                    // Time
                    HStack(spacing: 6) {
                        Image(systemName: "clock")
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                        
                        Text(lesson.timeRange)
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    
                    // Location
                    HStack(spacing: 6) {
                        Image(systemName: "location")
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                        
                        Text("Phòng \(lesson.room)")
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    
                    Spacer()
                }
                
                // Attendance info (only for completed lessons)
                if lesson.state == .completed {
                    attendanceInfoView
                }
                
                // Action buttons for today's lessons
                if lesson.isToday && lesson.state == .scheduled {
                    todayActionButtons
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.white)
                    .shadow(
                        color: Color.black.opacity(0.08),
                        radius: 8,
                        x: 0,
                        y: 2
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .sheet(isPresented: $showingLessonDetail) {
            LessonDetailBottomSheet(lesson: lesson)
        }
    }
    
    // MARK: - Attendance Info View
    private var attendanceInfoView: some View {
        HStack(spacing: 12) {
            // Present count
            HStack(spacing: 4) {
                Image(systemName: "person.fill")
                    .font(.caption2)
                    .foregroundColor(.green)
                
                Text("\(lesson.presentCount)/\(lesson.totalStudents)")
                    .font(AppConstants.Typography.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            
            // Attendance rate
            HStack(spacing: 4) {
                Image(systemName: "chart.bar.fill")
                    .font(.caption2)
                    .foregroundColor(attendanceRateColor)
                
                Text("\(Int(lesson.attendanceRate))%")
                    .font(AppConstants.Typography.caption)
                    .foregroundColor(attendanceRateColor)
                    .fontWeight(.medium)
            }
            
            Spacer()
            
            // Attendance status
            Text(lesson.attendanceStatus)
                .font(AppConstants.Typography.caption)
                .foregroundColor(attendanceRateColor)
                .padding(.horizontal, 8)
                .padding(.vertical, 2)
                .background(
                    RoundedRectangle(cornerRadius: 4)
                        .fill(attendanceRateColor.opacity(0.1))
                )
        }
    }
    
    // MARK: - Today Action Buttons
    private var todayActionButtons: some View {
        HStack(spacing: 8) {
            Button("Bắt đầu lớp") {
                // Handle start class action
                print("Start class: \(lesson.id)")
            }
            .font(AppConstants.Typography.caption)
            .foregroundColor(.white)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 6)
                    .fill(Color.blue)
            )
            
            Button("Xem chi tiết") {
                onTap()
            }
            .font(AppConstants.Typography.caption)
            .foregroundColor(.blue)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 6)
                    .stroke(Color.blue, lineWidth: 1)
            )
            
            Spacer()
        }
    }
    
    // MARK: - Computed Properties
    private var attendanceRateColor: Color {
        if lesson.attendanceRate >= 90 {
            return .green
        } else if lesson.attendanceRate >= 70 {
            return .blue
        } else if lesson.attendanceRate >= 50 {
            return .orange
        } else {
            return .red
        }
    }
}

// MARK: - Status Badge
struct StatusBadge: View {
    let text: String
    let color: String
    let isToday: Bool
    let needsAttention: Bool
    
    var body: some View {
        HStack(spacing: 4) {
            if needsAttention {
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.caption2)
                    .foregroundColor(.orange)
            }
            
            Text(text)
                .font(AppConstants.Typography.caption)
                .fontWeight(.medium)
                .foregroundColor(badgeTextColor)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(badgeBackgroundColor)
        )
    }
    
    private var badgeTextColor: Color {
        if isToday {
            return .white
        } else {
            switch color {
            case "blue": return .blue
            case "green": return .green
            case "orange": return .orange
            case "red": return .red
            default: return .gray
            }
        }
    }
    
    private var badgeBackgroundColor: Color {
        if isToday {
            return .green
        } else {
            switch color {
            case "blue": return .blue.opacity(0.1)
            case "green": return .green.opacity(0.1)
            case "orange": return .orange.opacity(0.1)
            case "red": return .red.opacity(0.1)
            default: return .gray.opacity(0.1)
            }
        }
    }
}

// MARK: - Preview
struct APILessonCard_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 16) {
            // Today lesson
            APILessonCard(
                lesson: APILesson.mockAPILessons[1], // Today lesson
                onTap: {}
            )
            
            // Completed lesson
            APILessonCard(
                lesson: APILesson.mockAPILessons[0], // Completed lesson
                onTap: {}
            )
            
            // Upcoming lesson
            APILessonCard(
                lesson: APILesson.mockAPILessons[2], // Upcoming lesson
                onTap: {}
            )
        }
        .padding()
        .background(AppConstants.Colors.background)
    }
}
