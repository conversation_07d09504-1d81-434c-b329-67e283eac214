//
//  CheckinResultBottomSheet.swift
//  mobile-app-template
//
//  Created by Instructor App on 29/7/25.
//

import SwiftUI

struct CheckinResultBottomSheet: View {
    let isSuccess: Bool
    let message: String
    let onDismiss: () -> Void
    
    var body: some View {
        VStack(spacing: 0) {
            // Handle bar
            RoundedRectangle(cornerRadius: 2.5)
                .fill(Color.gray.opacity(0.3))
                .frame(width: 36, height: 5)
                .padding(.top, 12)
            
            // Content
            VStack(spacing: 24) {
                // Icon and title
                VStack(spacing: 16) {
                    // Status icon
                    ZStack {
                        Circle()
                            .fill(isSuccess ? Color.green.opacity(0.1) : Color.red.opacity(0.1))
                            .frame(width: 80, height: 80)
                        
                        Image(systemName: isSuccess ? "checkmark.circle.fill" : "xmark.circle.fill")
                            .font(.system(size: 40, weight: .medium))
                            .foregroundColor(isSuccess ? .green : .red)
                    }
                    
                    // Title
                    Text(isSuccess ? "Thành công!" : "Có lỗi xảy ra")
                        .font(.beVietnamPro(.bold, size: 24))
                        .foregroundColor(AppConstants.Colors.textPrimary)
                }
                
                // Message
                VStack(spacing: 8) {
                    Text(message)
                        .font(.beVietnamPro(.regular, size: 16))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                        .lineLimit(nil)
                        .fixedSize(horizontal: false, vertical: true)
                }
                .padding(.horizontal, 20)
                
                // Action button
                Button(action: onDismiss) {
                    HStack {
                        Spacer()
                        Text("Đóng")
                            .font(.beVietnamPro(.semiBold, size: 16))
                            .foregroundColor(.white)
                        Spacer()
                    }
                    .frame(height: 52)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(isSuccess ? Color.blue : Color.gray)
                    )
                }
                .buttonStyle(PlainButtonStyle())
                .padding(.horizontal, 20)
            }
            .padding(.top, 32)
            .padding(.bottom, 40)
        }
        .background(Color(.systemBackground))
        .presentationDetents([.height(320)])
        .presentationDragIndicator(.hidden)
        .interactiveDismissDisabled(false)
    }
}

// MARK: - Preview
struct CheckinResultBottomSheet_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // Success state
            CheckinResultBottomSheet(
                isSuccess: true,
                message: "Check-in thành công! Bạn đã xác nhận có mặt tại buổi học.",
                onDismiss: {}
            )
            .previewDisplayName("Success")
            
            // Error state
            CheckinResultBottomSheet(
                isSuccess: false,
                message: "Không thể check-in. Vui lòng kiểm tra kết nối mạng và thử lại.",
                onDismiss: {}
            )
            .previewDisplayName("Error")
            
            // Long message
            CheckinResultBottomSheet(
                isSuccess: false,
                message: "Đã xảy ra lỗi khi thực hiện check-in. Có thể do kết nối mạng không ổn định hoặc server đang bảo trì. Vui lòng thử lại sau ít phút.",
                onDismiss: {}
            )
            .previewDisplayName("Long Message")
        }
    }
}
