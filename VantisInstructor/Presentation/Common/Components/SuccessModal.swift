//
//  SuccessModal.swift
//  VantisInstructor
//
//  Created by VantisInstructor Team on 30/7/25.
//

import SwiftUI

struct SuccessModal: View {
    let title: String
    let message: String
    let buttonTitle: String
    let onDismiss: () -> Void
    
    @State private var showContent = false
    @State private var showCheckmark = false
    @State private var showConfetti = false
    
    var body: some View {
        ZStack {
            // Background overlay
            Color.black.opacity(0.4)
                .ignoresSafeArea()
                .onTapGesture {
                    dismissModal()
                }

            // Confetti effect
            if showConfetti {
                confettiView
            }

            // Modal content
            VStack(spacing: 0) {
                // Success icon
                successIcon
                
                // Content
                VStack(spacing: 16) {
                    // Title
                    Text(title)
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(AppConstants.Colors.textPrimary)
                        .multilineTextAlignment(.center)

                    // Message
                    Text(message)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                        .lineLimit(nil)
                    
                    // But<PERSON>(action: dismissModal) {
                        Text(buttonTitle)
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .frame(height: 48)
                            .background(AppConstants.Colors.primary)
                            .cornerRadius(12)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                .padding(.horizontal, 24)
                .padding(.bottom, 24)
            }
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.white)
                    .shadow(color: Color.black.opacity(0.1), radius: 20, x: 0, y: 10)
            )
            .padding(.horizontal, 40)
            .scaleEffect(showContent ? 1.0 : 0.8)
            .opacity(showContent ? 1.0 : 0.0)
        }
        .onAppear {
            withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                showContent = true
            }
            
            // Delay checkmark animation
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
                    showCheckmark = true
                }
            }

            // Delay confetti animation
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
                withAnimation(.easeOut(duration: 0.5)) {
                    showConfetti = true
                }
            }
        }
    }
    
    private var successIcon: some View {
        ZStack {
            // Outer glow circle
            Circle()
                .fill(AppConstants.Colors.success.opacity(0.2))
                .frame(width: 100, height: 100)
                .scaleEffect(showCheckmark ? 1.0 : 0.3)
                .opacity(showCheckmark ? 1.0 : 0.0)

            // Background circle
            Circle()
                .fill(AppConstants.Colors.success)
                .frame(width: 80, height: 80)
                .scaleEffect(showCheckmark ? 1.0 : 0.5)
                .opacity(showCheckmark ? 1.0 : 0.0)

            // Checkmark
            Image(systemName: "checkmark")
                .font(.system(size: 32, weight: .bold))
                .foregroundColor(.white)
                .scaleEffect(showCheckmark ? 1.0 : 0.3)
                .opacity(showCheckmark ? 1.0 : 0.0)
        }
        .offset(y: -40)
    }

    private var confettiView: some View {
        ZStack {
            ForEach(0..<20, id: \.self) { index in
                ConfettiPiece(
                    color: [AppConstants.Colors.primary, AppConstants.Colors.success, AppConstants.Colors.secondary, AppConstants.Colors.accent].randomElement() ?? AppConstants.Colors.primary,
                    delay: Double(index) * 0.1
                )
            }
        }
        .opacity(showConfetti ? 1.0 : 0.0)
    }
    
    private func dismissModal() {
        withAnimation(.easeInOut(duration: 0.3)) {
            showContent = false
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            onDismiss()
        }
    }
}

// MARK: - Confetti Piece
struct ConfettiPiece: View {
    let color: Color
    let delay: Double

    @State private var yOffset: CGFloat = -100
    @State private var xOffset: CGFloat = 0
    @State private var rotation: Double = 0
    @State private var opacity: Double = 1

    var body: some View {
        Rectangle()
            .fill(color)
            .frame(width: 8, height: 8)
            .cornerRadius(2)
            .offset(x: xOffset, y: yOffset)
            .rotationEffect(.degrees(rotation))
            .opacity(opacity)
            .onAppear {
                let randomX = CGFloat.random(in: -150...150)
                let randomRotation = Double.random(in: 0...360)

                withAnimation(.easeOut(duration: 2.0).delay(delay)) {
                    yOffset = 800
                    xOffset = randomX
                    rotation = randomRotation
                    opacity = 0
                }
            }
    }
}

// MARK: - Preview
struct SuccessModal_Previews: PreviewProvider {
    static var previews: some View {
        SuccessModal(
            title: "Thành công!",
            message: "Quiz 'Kiểm tra toán học' đã được tạo thành công!",
            buttonTitle: "Hoàn tất"
        ) {
            // Preview action
        }
        .previewLayout(.sizeThatFits)
    }
}
