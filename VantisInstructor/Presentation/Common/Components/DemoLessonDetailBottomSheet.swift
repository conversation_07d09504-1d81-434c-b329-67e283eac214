//
//  DemoLessonDetailBottomSheet.swift
//  mobile-app-template
//
//  Created by Instru<PERSON> App on 28/7/25.
//

import SwiftUI

struct DemoLessonDetailBottomSheet: View {
    let lesson: DemoLesson
    @Environment(\.dismiss) private var dismiss
    @StateObject private var checkinViewModel = LessonCheckinViewModel()
    
    var body: some View {
        VStack(spacing: 0) {
            // Custom Header
            customHeader

            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    // Header Section
                    headerSection

                    // Quick Info Cards
                    quickInfoSection

                    // Schedule Info Section
                    scheduleInfoSection

                    // Actions Section
                    actionsSection

                    Spacer(minLength: 20)
                }
                .padding(.horizontal, 20)
                .padding(.top, 8)
            }
        }
        .presentationDetents([.large])
        .presentationDragIndicator(.hidden)
        .disabled(checkinViewModel.isLoading)
        .overlay(
            // Loading overlay
            Group {
                if checkinViewModel.isLoading {
                    Color.black.opacity(0.3)
                        .ignoresSafeArea()

                    VStack(spacing: 16) {
                        ProgressView()
                            .scaleEffect(1.2)
                            .progressViewStyle(CircularProgressViewStyle(tint: .blue))

                        Text("Đang xử lý...")
                            .font(.beVietnamPro(.medium, size: 16))
                            .foregroundColor(.white)
                    }
                    .padding(24)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.black.opacity(0.8))
                    )
                }
            }
        )
        .sheet(isPresented: $checkinViewModel.showingResultBottomSheet) {
            CheckinResultBottomSheet(
                isSuccess: checkinViewModel.isSuccess,
                message: checkinViewModel.resultMessage,
                onDismiss: {
                    checkinViewModel.dismissResult()
                }
            )
        }
    }

    // MARK: - Custom Header
    private var customHeader: some View {
        VStack(spacing: 0) {
            // Drag indicator
            RoundedRectangle(cornerRadius: 2.5)
                .fill(Color(.systemGray4))
                .frame(width: 36, height: 5)
                .padding(.top, 8)

            // Top bar with title and close button
            ZStack {
                // Centered title
                Text("Chi tiết buổi học")
                    .font(.title3)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                // Close button aligned to trailing
                HStack {
                    Spacer()
                    Button(action: { dismiss() }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.secondary)
                            .frame(width: 32, height: 32)
                            .background(Color.white)
                            .clipShape(Circle())
                    }
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)

            Divider()
                .background(Color(.separator))
        }
        .background(Color(.systemBackground))
    }

    // MARK: - Header Section
    private var headerSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Class code and status
            HStack {
                Text(lesson.classCode)
                    .font(AppConstants.Typography.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(AppConstants.Colors.background)
                    .cornerRadius(6)
                
                Spacer()
                
                Text(lesson.statusText)
                    .font(AppConstants.Typography.caption)
                    .fontWeight(.medium)
                    .foregroundColor(lesson.statusColor)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(lesson.statusColor.opacity(0.1))
                    .cornerRadius(12)
            }
            
            // Lesson title
            Text(lesson.name)
                .font(AppConstants.Typography.title2)
                .fontWeight(.bold)
                .foregroundColor(AppConstants.Colors.textPrimary)
                .lineLimit(nil)
            
            // Class name
            Text(lesson.className)
                .font(AppConstants.Typography.body)
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .padding(.bottom, 8)
    }
    
    // MARK: - Quick Info Section
    private var quickInfoSection: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 16), count: 2), spacing: 16) {
            ModernStatCard(
                icon: "clock",
                iconColor: .blue,
                title: "Giờ học",
                value: lesson.timeString
            )

            ModernStatCard(
                icon: "location",
                iconColor: .orange,
                title: "Phòng học",
                value: lesson.room ?? "Chưa xác định"
            )

            ModernStatCard(
                icon: "person.2",
                iconColor: .green,
                title: "Học viên",
                value: "\(lesson.totalStudents) người"
            )

            ModernStatCard(
                icon: "calendar.badge.clock",
                iconColor: .purple,
                title: "Trạng thái",
                value: lesson.statusText
            )
        }
    }
    
    // MARK: - Schedule Info Section
    private var scheduleInfoSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Thông tin lịch học")
                .font(AppConstants.Typography.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            VStack(spacing: 12) {
                ScheduleInfoRow(
                    icon: "calendar",
                    label: "Ngày học",
                    value: formattedDate
                )

                ScheduleInfoRow(
                    icon: "clock",
                    label: "Giờ học",
                    value: lesson.timeString
                )

                if let room = lesson.room {
                    ScheduleInfoRow(
                        icon: "paperplane",
                        label: "Địa điểm",
                        value: room
                    )
                }

                ScheduleInfoRow(
                    icon: "person.2",
                    label: "Số học viên",
                    value: "\(lesson.totalStudents) người"
                )
            }
        }
    }
    
    // MARK: - Actions Section
    private var actionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Thao tác")
                .font(AppConstants.Typography.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            VStack(spacing: 12) {
                if lesson.isToday || lesson.isUpcoming {
                    ModernActionButton(
                        icon: "location.badge.plus",
                        title: "Check-in",
                        subtitle: "Xác nhận có mặt tại buổi học",
                        color: .blue,
                        isPrimary: true
                    ) {
                        print("🎯 Check-in button tapped for lesson ID: \(lesson.id)")
                        print("🎯 Lesson isToday: \(lesson.isToday), isUpcoming: \(lesson.isUpcoming)")
                        Task {
                            print("🎯 Starting checkin process...")
                            await checkinViewModel.checkinToLesson(lessonId: lesson.id)
                        }
                    }

                    ModernActionButton(
                        icon: "person.2.badge.plus",
                        title: "Điểm danh",
                        subtitle: "Ghi nhận sự có mặt của học viên",
                        color: .green,
                        isPrimary: false
                    ) {
                        // Handle attendance action
                        print("Điểm danh tapped")
                    }
                }

                ModernActionButton(
                    icon: "doc.text",
                    title: "Xem chi tiết lớp học",
                    subtitle: "Thông tin đầy đủ về lớp học",
                    color: .gray,
                    isPrimary: false
                ) {
                    // Handle view class details
                    print("Xem chi tiết lớp học tapped")
                }

                ModernActionButton(
                    icon: "square.and.pencil",
                    title: "Chỉnh sửa thông tin",
                    subtitle: "Cập nhật thông tin buổi học",
                    color: .orange,
                    isPrimary: false
                ) {
                    // Handle edit lesson
                    print("Chỉnh sửa thông tin tapped")
                }

                if !lesson.isToday && !lesson.isUpcoming {
                    ModernActionButton(
                        icon: "chart.bar.doc.horizontal",
                        title: "Báo cáo buổi học",
                        subtitle: "Xem báo cáo chi tiết về buổi học",
                        color: .purple,
                        isPrimary: false
                    ) {
                        // Handle lesson report
                        print("Báo cáo buổi học tapped")
                    }
                }
            }
        }
    }
    
    // MARK: - Computed Properties
    private var formattedDate: String {
        let formatter = ISO8601DateFormatter()
        if let date = formatter.date(from: lesson.startDatetime) {
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "dd/MM/yyyy"
            return dateFormatter.string(from: date)
        }
        return "N/A"
    }
}

// MARK: - Supporting Views

// MARK: - Preview
struct DemoLessonDetailBottomSheet_Previews: PreviewProvider {
    static var previews: some View {
        DemoLessonDetailBottomSheet(lesson: DemoLesson(
            id: 1,
            name: "BUỔI 1: TƯ DUY LÃNH ĐẠO TRONG THỜI ĐẠI CHUYỂN HÓA",
            className: "TCI052501",
            classCode: "CLASS-00002",
            startDatetime: "2025-07-28T14:00:00",
            room: "Phòng 101",
            totalStudents: 25,
            isToday: true,
            isUpcoming: false
        ))
    }
}
