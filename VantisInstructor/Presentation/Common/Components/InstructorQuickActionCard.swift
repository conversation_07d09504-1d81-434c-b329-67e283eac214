//
//  InstructorQuickActionCard.swift
//  mobile-app-template
//
//  Created by Instructor <PERSON>pp on 23/7/25.
//

import SwiftUI

struct InstructorQuickActionCard: View {
    let icon: String
    let title: String
    let subtitle: String
    let color: Color
    let badgeCount: Int?
    let action: () -> Void
    
    init(
        icon: String,
        title: String,
        subtitle: String,
        color: Color,
        badgeCount: Int? = nil,
        action: @escaping () -> Void
    ) {
        self.icon = icon
        self.title = title
        self.subtitle = subtitle
        self.color = color
        self.badgeCount = badgeCount
        self.action = action
    }
    
    var body: some View {
        Button(action: {
            print("But<PERSON> tapped: \(title)")
            action()
        }) {
            VStack(spacing: 8) {
                // Modern icon with gradient background
                ZStack {
                    // Gradient background with glassmorphism effect
                    RoundedRectangle(cornerRadius: 18)
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    color.opacity(0.25),
                                    color.opacity(0.15)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 68, height: 68)
                        .overlay(
                            RoundedRectangle(cornerRadius: 18)
                                .stroke(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            color.opacity(0.3),
                                            color.opacity(0.1)
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ),
                                    lineWidth: 1.5
                                )
                        )

                    Image(systemName: icon)
                        .font(.system(size: 26, weight: .medium))
                        .foregroundStyle(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    color,
                                    color.opacity(0.8)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                }

                // Modern text content with better typography
                VStack(spacing: 3) {
                    Text(title)
                        .font(.beVietnamPro(.semiBold, size: 13))
                        .foregroundColor(AppConstants.Colors.textPrimary)
                        .multilineTextAlignment(.center)
                        .lineLimit(1)

                    Text(subtitle)
                        .font(.beVietnamPro(.medium, size: 10))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                        .lineLimit(2)
                }
            }
            .frame(maxWidth: .infinity)
            .frame(height: 120)
            .padding(.horizontal, 8)
            .padding(.vertical, 12)
        }
        .buttonStyle(.plain)
        .scaleEffect(1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: false)
    }
}

// MARK: - Instructor Quick Actions Grid
struct InstructorQuickActionsGrid: View {
    let onAttendance: () -> Void
    let onAssignments: () -> Void
    let onQuizzes: () -> Void
    let onAnnouncements: () -> Void
    let onGrades: () -> Void
    let onStudents: () -> Void
    let onSchedule: () -> Void
    let onNotifications: () -> Void
    
    // Mock data for badges
    @State private var pendingAssignments = 5
    @State private var newAnnouncements = 2
    @State private var pendingGrades = 12
    
    var body: some View {
        VStack(spacing: 4) {
            // Single row - All 4 buttons with modern spacing
            HStack(spacing: 8) {
                InstructorQuickActionCard(
                    icon: "doc.text",
                    title: "Bài tập",
                    subtitle: "Tạo & quản lý",
                    color: .blue,
                    badgeCount: pendingAssignments,
                    action: onAssignments
                )

                InstructorQuickActionCard(
                    icon: "chart.bar.doc.horizontal",
                    title: "Chấm điểm",
                    subtitle: "Đánh giá bài",
                    color: .teal,
                    badgeCount: pendingGrades,
                    action: onGrades
                )

                InstructorQuickActionCard(
                    icon: "calendar",
                    title: "Lịch dạy",
                    subtitle: "Xem lịch trình",
                    color: .cyan,
                    action: onSchedule
                )

                InstructorQuickActionCard(
                    icon: "bell.badge",
                    title: "Thông báo",
                    subtitle: "Tin nhắn mới",
                    color: .orange,
                    action: onNotifications
                )
            }
        }
    }
}

// MARK: - Compact Quick Action
struct CompactQuickAction: View {
    let icon: String
    let title: String
    let color: Color
    let badgeCount: Int?
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                // Icon with badge
                ZStack {
                    // Background rounded rectangle for icon
                    RoundedRectangle(cornerRadius: 10)
                        .fill(color.opacity(0.15))
                        .frame(width: 40, height: 40)

                    Image(systemName: icon)
                        .font(.title3)
                        .foregroundColor(color)
                        .frame(width: 40, height: 40)
                    
                    // Badge - Hidden
                    // if let badgeCount = badgeCount, badgeCount > 0 {
                    //     VStack {
                    //         HStack {
                    //             Spacer()
                    //
                    //             Text("\(badgeCount)")
                    //                 .font(.caption2)
                    //                 .fontWeight(.bold)
                    //                 .foregroundColor(.white)
                    //                 .padding(.horizontal, 4)
                    //                 .padding(.vertical, 2)
                    //                 .background(.red)
                    //                 .cornerRadius(8)
                    //                 .offset(x: 6, y: -6)
                    //         }
                    //         Spacer()
                    //     }
                    // }
                }
                
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            .padding(12)
            .background(AppConstants.Colors.cardBackground)
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Quick Stats Card
struct QuickStatsCard: View {
    let title: String
    let value: String
    let subtitle: String
    let icon: String
    let color: Color
    let trend: StatsTrend?

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header with icon and trend
            HStack {
                // Modern icon with gradient background
                ZStack {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    color.opacity(0.2),
                                    color.opacity(0.1)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 40, height: 40)

                    Image(systemName: icon)
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundStyle(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    color,
                                    color.opacity(0.8)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                }

                Spacer()

                // Trend indicator with modern styling
                if let trend = trend {
                    HStack(spacing: 4) {
                        Image(systemName: trend.isPositive ? "arrow.up.right" : "arrow.down.right")
                            .font(.system(size: 10, weight: .bold))

                        Text(trend.percentage)
                            .font(.beVietnamPro(.semiBold, size: 11))
                    }
                    .foregroundColor(trend.isPositive ? .green : .red)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(trend.isPositive ? Color.green.opacity(0.1) : Color.red.opacity(0.1))
                    )
                }
            }

            // Stats content with better typography
            VStack(alignment: .leading, spacing: 4) {
                Text(value)
                    .font(.beVietnamPro(.bold, size: 28))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Text(title)
                    .font(.beVietnamPro(.semiBold, size: 13))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Text(subtitle)
                    .font(.beVietnamPro(.medium, size: 11))
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.white,
                            Color.white.opacity(0.95)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.black.opacity(0.1), lineWidth: 0.5)
                )
        )
    }
}

// MARK: - Stats Trend
struct StatsTrend {
    let percentage: String
    let isPositive: Bool
}

// MARK: - Preview
struct InstructorQuickActionCard_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            // Grid layout
            InstructorQuickActionsGrid(
                onAttendance: { print("Attendance") },
                onAssignments: { print("Assignments") },
                onQuizzes: { print("Quizzes") },
                onAnnouncements: { print("Announcements") },
                onGrades: { print("Grades") },
                onStudents: { print("Students") },
                onSchedule: { print("Schedule") },
                onNotifications: { print("Notifications") }
            )
            
            // Compact actions
            VStack(spacing: 8) {
                CompactQuickAction(
                    icon: "person.2.badge.plus",
                    title: "Điểm danh lớp học",
                    color: .green,
                    badgeCount: nil
                ) { print("Attendance") }
                
                CompactQuickAction(
                    icon: "doc.text",
                    title: "Bài tập cần chấm",
                    color: .blue,
                    badgeCount: 5
                ) { print("Assignments") }
            }
            
            // Stats cards
            HStack(spacing: 12) {
                QuickStatsCard(
                    title: "Tỷ lệ điểm danh",
                    value: "92%",
                    subtitle: "Tuần này",
                    icon: "person.2.fill",
                    color: .green,
                    trend: StatsTrend(percentage: "+2%", isPositive: true)
                )
                
                QuickStatsCard(
                    title: "Bài tập đã chấm",
                    value: "24/30",
                    subtitle: "Còn 6 bài",
                    icon: "doc.text.fill",
                    color: .blue,
                    trend: nil
                )
            }
        }
        .padding()
        .background(AppConstants.Colors.background)
    }
}
