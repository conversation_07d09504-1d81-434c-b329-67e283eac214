//
//  SimpleLessonDetailBottomSheet.swift
//  mobile-app-template
//
//  Created by Instructor App on 28/7/25.
//

import SwiftUI

struct SimpleLessonDetailBottomSheet: View {
    let lesson: SimpleLesson
    @Environment(\.dismiss) private var dismiss
    @StateObject private var checkinViewModel = LessonCheckinViewModel()
    
    var body: some View {
        VStack(spacing: 0) {
            // Custom Header
            customHeader

            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    // Header Section
                    headerSection

                    // Quick Info Cards
                    quickInfoSection

                    // Attendance Section (if available)
                    if lesson.attendanceRate != nil {
                        attendanceSection
                    }

                    // Schedule Info Section
                    scheduleInfoSection

                    // Actions Section
                    actionsSection

                    Spacer(minLength: 20)
                }
                .padding(.horizontal, 20)
                .padding(.top, 8)
            }
        }
        .presentationDetents([.large])
        .presentationDragIndicator(.hidden)
        .disabled(checkinViewModel.isLoading)
        .overlay(
            // Loading overlay
            Group {
                if checkinViewModel.isLoading {
                    Color.black.opacity(0.3)
                        .ignoresSafeArea()

                    VStack(spacing: 16) {
                        ProgressView()
                            .scaleEffect(1.2)
                            .progressViewStyle(CircularProgressViewStyle(tint: .blue))

                        Text("Đang xử lý...")
                            .font(.beVietnamPro(.medium, size: 16))
                            .foregroundColor(.white)
                    }
                    .padding(24)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.black.opacity(0.8))
                    )
                }
            }
        )
        .sheet(isPresented: $checkinViewModel.showingResultBottomSheet) {
            CheckinResultBottomSheet(
                isSuccess: checkinViewModel.isSuccess,
                message: checkinViewModel.resultMessage,
                onDismiss: {
                    checkinViewModel.dismissResult()
                }
            )
        }
    }

    // MARK: - Custom Header
    private var customHeader: some View {
        VStack(spacing: 0) {
            // Drag indicator
            RoundedRectangle(cornerRadius: 2.5)
                .fill(Color(.systemGray4))
                .frame(width: 36, height: 5)
                .padding(.top, 8)

            // Top bar with title and close button
            ZStack {
                // Centered title
                Text("Chi tiết buổi học")
                    .font(.title3)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                // Close button aligned to trailing
                HStack {
                    Spacer()
                    Button(action: { dismiss() }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.secondary)
                            .frame(width: 32, height: 32)
                            .background(Color.white)
                            .clipShape(Circle())
                    }
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)

            Divider()
                .background(Color(.separator))
        }
        .background(Color(.systemBackground))
    }

    // MARK: - Header Section
    private var headerSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Class code and status
            HStack {
                Text(lesson.classCode)
                    .font(AppConstants.Typography.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(AppConstants.Colors.background)
                    .cornerRadius(6)
                
                Spacer()
                
                Text(lesson.statusText)
                    .font(AppConstants.Typography.caption)
                    .fontWeight(.medium)
                    .foregroundColor(statusColor)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(statusColor.opacity(0.1))
                    .cornerRadius(12)
            }
            
            // Lesson title
            Text(lesson.name)
                .font(AppConstants.Typography.title2)
                .fontWeight(.bold)
                .foregroundColor(AppConstants.Colors.textPrimary)
                .lineLimit(nil)
            
            // Class name
            Text(lesson.className)
                .font(AppConstants.Typography.body)
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .padding(.bottom, 8)
    }
    
    // MARK: - Quick Info Section
    private var quickInfoSection: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 16), count: 2), spacing: 16) {
            ModernStatCard(
                icon: "clock",
                iconColor: .blue,
                title: "Thời lượng",
                value: durationText
            )

            ModernStatCard(
                icon: "location",
                iconColor: .orange,
                title: "Phòng học",
                value: lesson.room ?? "Chưa xác định"
            )

            ModernStatCard(
                icon: "person.2",
                iconColor: .green,
                title: "Học viên",
                value: "\(lesson.totalStudents) người"
            )

            ModernStatCard(
                icon: "calendar.badge.clock",
                iconColor: .purple,
                title: "Buổi học",
                value: lesson.lessonNumber != nil ? "Buổi \(lesson.lessonNumber!)" : "Chưa xác định"
            )
        }
    }
    
    // MARK: - Attendance Section
    private var attendanceSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Điểm danh")
                .font(AppConstants.Typography.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            if let attendanceRate = lesson.attendanceRate {
                VStack(spacing: 12) {
                    // Attendance rate
                    HStack {
                        Text("Tỷ lệ có mặt")
                            .font(AppConstants.Typography.body)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                        
                        Spacer()
                        
                        Text("\(Int(attendanceRate))%")
                            .font(AppConstants.Typography.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(attendanceRateColor(attendanceRate))
                    }
                    
                    // Progress bar
                    ProgressView(value: attendanceRate / 100.0)
                        .progressViewStyle(LinearProgressViewStyle(tint: attendanceRateColor(attendanceRate)))
                        .scaleEffect(x: 1, y: 1.5, anchor: .center)
                    
                    // Attendance breakdown
                    HStack(spacing: 16) {
                        AttendanceStatView(
                            icon: "checkmark.circle.fill",
                            iconColor: .green,
                            title: "Có mặt",
                            value: "\(lesson.presentCount)"
                        )

                        AttendanceStatView(
                            icon: "xmark.circle.fill",
                            iconColor: .red,
                            title: "Vắng mặt",
                            value: "\(lesson.totalStudents - lesson.presentCount)"
                        )

                        Spacer()
                    }
                }
                .padding(16)
                .background(AppConstants.Colors.cardBackground)
                .cornerRadius(AppConstants.UI.cornerRadius)
            }
        }
    }
    
    // MARK: - Schedule Info Section
    private var scheduleInfoSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Thông tin lịch học")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)

            VStack(spacing: 12) {
                SimpleScheduleInfoRow(
                    icon: "calendar",
                    label: "Ngày học",
                    value: lesson.dateString
                )

                SimpleScheduleInfoRow(
                    icon: "clock",
                    label: "Giờ học",
                    value: timeRange
                )

                if let duration = lesson.durationHours {
                    SimpleScheduleInfoRow(
                        icon: "timer",
                        label: "Thời lượng",
                        value: formatDuration(duration)
                    )
                }

                if let room = lesson.room {
                    SimpleScheduleInfoRow(
                        icon: "paperplane",
                        label: "Địa điểm",
                        value: room
                    )
                }
            }
        }
    }
    
    // MARK: - Actions Section
    private var actionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Thao tác")
                .font(AppConstants.Typography.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            VStack(spacing: 12) {
                if lesson.state == "scheduled" || lesson.state == "in_progress" {
                    ModernActionButton(
                        icon: "location.badge.plus",
                        title: "Check-in",
                        subtitle: "Xác nhận có mặt tại buổi học",
                        color: .blue,
                        isPrimary: true
                    ) {
                        print("🎯 Check-in button tapped for lesson ID: \(lesson.id)")
                        print("🎯 Lesson state: \(lesson.state)")
                        Task {
                            print("🎯 Starting checkin process...")
                            await checkinViewModel.checkinToLesson(lessonId: lesson.id)
                        }
                    }

                    ModernActionButton(
                        icon: "person.2.badge.plus",
                        title: "Điểm danh",
                        subtitle: "Ghi nhận sự có mặt của học viên",
                        color: .green,
                        isPrimary: false
                    ) {
                        // Handle attendance action
                        print("Điểm danh tapped")
                    }
                }

                ModernActionButton(
                    icon: "doc.text",
                    title: "Xem chi tiết lớp học",
                    subtitle: "Thông tin đầy đủ về lớp học",
                    color: .gray,
                    isPrimary: false
                ) {
                    // Handle view class details
                    print("Xem chi tiết lớp học tapped")
                }

                if lesson.state == "completed" {
                    ModernActionButton(
                        icon: "chart.bar.doc.horizontal",
                        title: "Báo cáo buổi học",
                        subtitle: "Xem báo cáo chi tiết về buổi học",
                        color: .orange,
                        isPrimary: false
                    ) {
                        // Handle lesson report
                        print("Báo cáo buổi học tapped")
                    }
                }
            }
        }
    }
    
    // MARK: - Computed Properties
    private var statusColor: Color {
        switch lesson.statusColor {
        case "blue": return .blue
        case "green": return .green
        case "red": return .red
        case "orange": return .orange
        default: return .gray
        }
    }
    
    private var durationText: String {
        if let duration = lesson.durationHours {
            return formatDuration(duration)
        }
        return "N/A"
    }
    
    private var timeRange: String {
        let startTime = lesson.timeString
        if let duration = lesson.durationHours {
            let endDate = lesson.startDate.addingTimeInterval(duration * 3600)
            let formatter = DateFormatter()
            formatter.timeStyle = .short
            let endTime = formatter.string(from: endDate)
            return "\(startTime) - \(endTime)"
        }
        return startTime
    }
    
    private func attendanceRateColor(_ rate: Double) -> Color {
        if rate >= 90 {
            return .green
        } else if rate >= 70 {
            return .orange
        } else {
            return .red
        }
    }
    
    private func formatDuration(_ hours: Double) -> String {
        if hours >= 1 {
            let wholeHours = Int(hours)
            let minutes = Int((hours.truncatingRemainder(dividingBy: 1)) * 60)
            if minutes > 0 {
                return "\(wholeHours)h \(minutes)m"
            } else {
                return "\(wholeHours)h"
            }
        } else {
            return "\(Int(hours * 60))m"
        }
    }
}

// MARK: - Supporting Views
struct SimpleScheduleInfoRow: View {
    let icon: String
    let label: String
    let value: String

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.body)
                .foregroundColor(AppConstants.Colors.primary)
                .frame(width: 20)

            Text(label)
                .font(AppConstants.Typography.body)
                .foregroundColor(AppConstants.Colors.textSecondary)

            Spacer()

            Text(value)
                .font(AppConstants.Typography.body)
                .fontWeight(.medium)
                .foregroundColor(AppConstants.Colors.textPrimary)
        }
    }
}

// MARK: - Preview
struct SimpleLessonDetailBottomSheet_Previews: PreviewProvider {
    static var previews: some View {
        SimpleLessonDetailBottomSheet(lesson: SimpleLesson.mockLessons[0])
    }
}
