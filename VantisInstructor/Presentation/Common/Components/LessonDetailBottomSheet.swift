//
//  LessonDetailBottomSheet.swift
//  mobile-app-template
//
//  Created by Instructor App on 28/7/25.
//

import SwiftUI

struct LessonDetailBottomSheet: View {
    let lesson: APILesson
    @Environment(\.dismiss) private var dismiss
    @StateObject private var checkinViewModel = LessonCheckinViewModel()

    var body: some View {
        VStack(spacing: 0) {
            // Custom Header
            customHeader

            // Content
            ScrollView {
                VStack(spacing: 24) {
                    // Lesson Info Section
                    lessonInfoSection

                    // Stats Grid
                    statsGridSection

                    // Schedule Info Section
                    scheduleInfoSection

                    // Attendance Section (if completed)
                    if lesson.state == .completed {
                        attendanceSection
                    }

                    // Action Buttons
                    actionButtonsSection

                    Spacer(minLength: 20)
                }
                .padding(.horizontal, 20)
                .padding(.top, 20)
            }
        }
        .background(Color(.systemBackground))
        .presentationDetents([.large])
        .presentationDragIndicator(.visible)
        .disabled(checkinViewModel.isLoading)
        .overlay(
            // Loading overlay
            Group {
                if checkinViewModel.isLoading {
                    Color.black.opacity(0.3)
                        .ignoresSafeArea()

                    VStack(spacing: 16) {
                        ProgressView()
                            .scaleEffect(1.2)
                            .progressViewStyle(CircularProgressViewStyle(tint: .blue))

                        Text("Đang xử lý...")
                            .font(.beVietnamPro(.medium, size: 16))
                            .foregroundColor(.white)
                    }
                    .padding(24)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.black.opacity(0.8))
                    )
                }
            }
        )
        .sheet(isPresented: $checkinViewModel.showingResultBottomSheet) {
            CheckinResultBottomSheet(
                isSuccess: checkinViewModel.isSuccess,
                message: checkinViewModel.resultMessage,
                onDismiss: {
                    checkinViewModel.dismissResult()
                }
            )
        }
    }
    
    // MARK: - Custom Header
    private var customHeader: some View {
        VStack(spacing: 0) {
            // Drag indicator
            RoundedRectangle(cornerRadius: 2.5)
                .fill(Color(.systemGray4))
                .frame(width: 36, height: 5)
                .padding(.top, 8)

            // Top bar with title and close button
            ZStack {
                // Centered title
                Text("Chi tiết buổi học")
                    .font(.title3)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                // Close button aligned to trailing
                HStack {
                    Spacer()
                    Button(action: { dismiss() }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.secondary)
                            .frame(width: 32, height: 32)
                            .background(Color.white)
                            .clipShape(Circle())
                    }
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)

            Divider()
                .background(Color(.separator))
        }
        .background(Color(.systemBackground))
    }

    // MARK: - Lesson Info Section
    private var lessonInfoSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Class code and status
            HStack {
                Text(lesson.classCode)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(AppConstants.Colors.textSecondary)

                Spacer()

                StatusBadge(
                    text: lesson.statusBadgeText,
                    color: lesson.statusBadgeColor,
                    isToday: lesson.isToday,
                    needsAttention: lesson.needsAttention
                )
            }

            // Lesson title
            Text(lesson.name)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(AppConstants.Colors.textPrimary)
                .multilineTextAlignment(.leading)

            // Class name
            Text(lesson.className)
                .font(.subheadline)
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
    }
    
    // MARK: - Stats Grid Section
    private var statsGridSection: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 16), count: 2), spacing: 16) {
            ModernStatCard(
                icon: "clock",
                iconColor: .blue,
                title: "Giờ học",
                value: lesson.timeRange
            )

            ModernStatCard(
                icon: "location",
                iconColor: .orange,
                title: "Phòng học",
                value: lesson.room
            )

            ModernStatCard(
                icon: "person.2",
                iconColor: .green,
                title: "Học viên",
                value: "\(lesson.totalStudents) người"
            )

            ModernStatCard(
                icon: "calendar.badge.clock",
                iconColor: .purple,
                title: "Trạng thái",
                value: lesson.state.displayName
            )
        }
    }

    // MARK: - Schedule Info Section
    private var scheduleInfoSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Thông tin lịch học")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)

            VStack(spacing: 12) {
                ScheduleInfoRow(
                    icon: "calendar",
                    label: "Ngày học",
                    value: lesson.formattedDate
                )

                ScheduleInfoRow(
                    icon: "clock",
                    label: "Giờ học",
                    value: lesson.timeRange
                )

                ScheduleInfoRow(
                    icon: "paperplane",
                    label: "Địa điểm",
                    value: lesson.room
                )

                ScheduleInfoRow(
                    icon: "person.2",
                    label: "Số học viên",
                    value: "\(lesson.totalStudents) người"
                )
            }
        }
    }

    // MARK: - Attendance Section
    private var attendanceSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Điểm danh")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)

            // Attendance stats in horizontal layout
            HStack(spacing: 20) {
                AttendanceStatView(
                    icon: "checkmark.circle.fill",
                    iconColor: .green,
                    title: "Có mặt",
                    value: "\(lesson.presentCount)"
                )

                AttendanceStatView(
                    icon: "xmark.circle.fill",
                    iconColor: .red,
                    title: "Vắng mặt",
                    value: "\(lesson.totalStudents - lesson.presentCount)"
                )

                Spacer()
            }
        }
        .padding(20)
        .background(Color(.secondarySystemBackground))
        .cornerRadius(16)
    }
    
    // MARK: - Action Buttons Section
    private var actionButtonsSection: some View {
        VStack(spacing: 12) {
            if lesson.state == .scheduled || lesson.state == .inProgress {
                ModernActionButton(
                    icon: "location.badge.plus",
                    title: "Check-in",
                    subtitle: "Xác nhận có mặt tại buổi học",
                    color: .blue,
                    isPrimary: true
                ) {
                    Task {
                        await checkinViewModel.checkinToLesson(lessonId: lesson.id)
                    }
                }

                ModernActionButton(
                    icon: "person.2.badge.plus",
                    title: "Điểm danh",
                    subtitle: "Ghi nhận sự có mặt của học viên",
                    color: .green,
                    isPrimary: false
                ) {
                    // Handle attendance action
                    print("Điểm danh tapped")
                }
            }

            ModernActionButton(
                icon: "doc.text",
                title: "Xem chi tiết lớp học",
                subtitle: "Thông tin đầy đủ về lớp học",
                color: .gray,
                isPrimary: false
            ) {
                // Handle view class details
                print("Xem chi tiết lớp học tapped")
            }

            if lesson.state == .completed {
                ModernActionButton(
                    icon: "chart.bar.doc.horizontal",
                    title: "Báo cáo buổi học",
                    subtitle: "Xem báo cáo chi tiết về buổi học",
                    color: .orange,
                    isPrimary: false
                ) {
                    // Handle lesson report
                    print("Báo cáo buổi học tapped")
                }
            }
        }
    }
    

    
    // MARK: - Computed Properties
    private var attendanceRateColor: Color {
        if lesson.attendanceRate >= 90 {
            return .green
        } else if lesson.attendanceRate >= 70 {
            return .orange
        } else {
            return .red
        }
    }
}

// MARK: - Supporting Views
struct ModernStatCard: View {
    let icon: String
    let iconColor: Color
    let title: String
    let value: String

    var body: some View {
        VStack(spacing: 16) {
            // Icon with circular background
            ZStack {
                Circle()
                    .fill(iconColor.opacity(0.1))
                    .frame(width: 56, height: 56)

                Image(systemName: icon)
                    .font(.title2)
                    .fontWeight(.medium)
                    .foregroundColor(iconColor)
            }

            VStack(spacing: 6) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.center)

                Text(value)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 24)
        .padding(.horizontal, 16)
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(Color(.separator).opacity(0.2), lineWidth: 1)
        )
    }
}

struct AttendanceStatView: View {
    let icon: String
    let iconColor: Color
    let title: String
    let value: String

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(iconColor)

            VStack(spacing: 2) {
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)

                Text(value)
                    .font(.title3)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
            }
        }
    }
}

struct ModernActionButton: View {
    let icon: String
    let title: String
    let subtitle: String
    let color: Color
    let isPrimary: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(isPrimary ? .white : color)
                    .frame(width: 24, height: 24)

                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.body)
                        .fontWeight(.semibold)
                        .foregroundColor(isPrimary ? .white : .primary)

                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(isPrimary ? .white.opacity(0.8) : .secondary)
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(isPrimary ? .white.opacity(0.7) : .secondary)
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(isPrimary ? color : Color(.systemBackground))
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color(.separator).opacity(0.5), lineWidth: 0.5)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct ScheduleInfoRow: View {
    let icon: String
    let label: String
    let value: String

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.body)
                .foregroundColor(AppConstants.Colors.primary)
                .frame(width: 20)

            Text(label)
                .font(AppConstants.Typography.body)
                .foregroundColor(AppConstants.Colors.textSecondary)

            Spacer()

            Text(value)
                .font(AppConstants.Typography.body)
                .fontWeight(.medium)
                .foregroundColor(AppConstants.Colors.textPrimary)
        }
    }
}



// MARK: - Preview
struct LessonDetailBottomSheet_Previews: PreviewProvider {
    static var previews: some View {
        LessonDetailBottomSheet(lesson: APILesson.mockAPILessons[0])
    }
}
