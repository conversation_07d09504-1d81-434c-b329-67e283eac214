//
//  CheckinPreview.swift
//  mobile-app-template
//
//  Created by Inst<PERSON>ctor App on 29/7/25.
//

import SwiftUI

struct CheckinPreview: View {
    @State private var showingLessonDetail = false
    @State private var showingSuccessResult = false
    @State private var showingErrorResult = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header
                    VStack(spacing: 8) {
                        Text("Checkin Feature Preview")
                            .font(.beVietnamPro(.bold, size: 24))
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        
                        Text("Test các tính năng checkin cho giảng viên")
                            .font(.beVietnamPro(.regular, size: 16))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.top, 20)
                    
                    // Test Buttons
                    VStack(spacing: 16) {
                        // Show Lesson Detail
                        Button(action: {
                            showingLessonDetail = true
                        }) {
                            HStack {
                                Image(systemName: "book.fill")
                                    .font(.system(size: 20))
                                Text("Xem Chi Tiết Buổi Học")
                                    .font(.beVietnamPro(.semiBold, size: 16))
                                Spacer()
                                Image(systemName: "chevron.right")
                            }
                            .foregroundColor(.white)
                            .padding()
                            .background(Color.blue)
                            .cornerRadius(12)
                        }
                        
                        // Show Success Result
                        Button(action: {
                            showingSuccessResult = true
                        }) {
                            HStack {
                                Image(systemName: "checkmark.circle.fill")
                                    .font(.system(size: 20))
                                Text("Test Success Result")
                                    .font(.beVietnamPro(.semiBold, size: 16))
                                Spacer()
                                Image(systemName: "chevron.right")
                            }
                            .foregroundColor(.white)
                            .padding()
                            .background(Color.green)
                            .cornerRadius(12)
                        }
                        
                        // Show Error Result
                        Button(action: {
                            showingErrorResult = true
                        }) {
                            HStack {
                                Image(systemName: "xmark.circle.fill")
                                    .font(.system(size: 20))
                                Text("Test Error Result")
                                    .font(.beVietnamPro(.semiBold, size: 16))
                                Spacer()
                                Image(systemName: "chevron.right")
                            }
                            .foregroundColor(.white)
                            .padding()
                            .background(Color.red)
                            .cornerRadius(12)
                        }
                    }
                    .padding(.horizontal, 20)
                    
                    // Feature Description
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Tính năng Checkin")
                            .font(.beVietnamPro(.bold, size: 18))
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        
                        VStack(alignment: .leading, spacing: 8) {
                            FeatureItem(
                                icon: "location.badge.plus",
                                title: "Check-in tự động",
                                description: "Gọi API POST /instructors/lessons/{lesson_id}/checkin"
                            )
                            
                            FeatureItem(
                                icon: "clock.fill",
                                title: "Loading state",
                                description: "Hiển thị loading và disable UI trong khi chờ API response"
                            )
                            
                            FeatureItem(
                                icon: "message.fill",
                                title: "Result feedback",
                                description: "Hiển thị message từ API response trong bottom sheet"
                            )
                            
                            FeatureItem(
                                icon: "location.fill",
                                title: "Location tracking",
                                description: "Tự động gửi vị trí hiện tại (nếu có quyền)"
                            )
                        }
                    }
                    .padding(20)
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                    .padding(.horizontal, 20)
                    
                    Spacer(minLength: 40)
                }
            }
            .navigationTitle("Checkin Preview")
            .navigationBarTitleDisplayMode(.inline)
        }
        .sheet(isPresented: $showingLessonDetail) {
            LessonDetailBottomSheet(lesson: APILesson.mockAPILessons[0])
        }
        .sheet(isPresented: $showingSuccessResult) {
            CheckinResultBottomSheet(
                isSuccess: true,
                message: "Check-in thành công! Bạn đã xác nhận có mặt tại buổi học.",
                onDismiss: {
                    showingSuccessResult = false
                }
            )
        }
        .sheet(isPresented: $showingErrorResult) {
            CheckinResultBottomSheet(
                isSuccess: false,
                message: "Không thể check-in. Vui lòng kiểm tra kết nối mạng và thử lại.",
                onDismiss: {
                    showingErrorResult = false
                }
            )
        }
    }
}

struct FeatureItem: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.blue)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.beVietnamPro(.semiBold, size: 14))
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Text(description)
                    .font(.beVietnamPro(.regular, size: 12))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .lineLimit(nil)
            }
            
            Spacer()
        }
    }
}

// MARK: - Preview
struct CheckinPreview_Previews: PreviewProvider {
    static var previews: some View {
        CheckinPreview()
    }
}
