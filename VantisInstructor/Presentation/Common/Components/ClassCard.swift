//
//  ClassCard.swift
//  mobile-app-template
//
//  Created by In<PERSON><PERSON><PERSON> <PERSON><PERSON> on 23/7/25.
//

import SwiftUI

struct ClassCard: View {
    let classItem: Class
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 12) {
                // Header with course info
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(classItem.courseCode)
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)

                        Text(classItem.title)
                            .font(AppConstants.Typography.headline)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                            .lineLimit(2)
                    }
                    
                    Spacer()
                    
                    // Class type badge
                    ClassTypeBadge(type: classItem.type)
                }
                
                // Time and location
                HStack(spacing: 16) {
                    HStack(spacing: 6) {
                        Image(systemName: "clock")
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                        
                        Text(classItem.formattedTime)
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    
                    if let location = classItem.location {
                        HStack(spacing: 6) {
                            Image(systemName: "location")
                                .font(.caption)
                                .foregroundColor(AppConstants.Colors.textSecondary)
                            
                            Text(location.displayName)
                                .font(AppConstants.Typography.caption)
                                .foregroundColor(AppConstants.Colors.textSecondary)
                        }
                    }
                }
                
                // Status and attendance
                HStack {
                    // Status badge
                    ClassStatusBadge(status: classItem.status)
                    
                    Spacer()
                    
                    // Attendance info
                    if classItem.status == .completed {
                        HStack(spacing: 4) {
                            Image(systemName: "person.2.fill")
                                .font(.caption2)
                                .foregroundColor(AppConstants.Colors.success)
                            
                            Text("\(classItem.attendedStudents)/\(classItem.totalStudents)")
                                .font(AppConstants.Typography.caption)
                                .foregroundColor(AppConstants.Colors.success)
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(AppConstants.Colors.success.opacity(0.1))
                        .cornerRadius(8)
                    } else if classItem.isUpcoming {
                        Text(classItem.timeRemaining)
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.primary)
                    }
                }
                
                // Progress bar for ongoing classes
                if classItem.status == .inProgress {
                    ProgressView(value: 0.6) // Mock progress
                        .progressViewStyle(LinearProgressViewStyle(tint: AppConstants.Colors.primary))
                        .scaleEffect(x: 1, y: 0.5)
                }
            }
            .padding(16)
            .background(AppConstants.Colors.cardBackground)
            .cornerRadius(12)
            .shadow(color: AppConstants.Colors.shadow.opacity(0.1), radius: 4, x: 0, y: 2)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Class Type Badge
struct ClassTypeBadge: View {
    let type: ClassType
    
    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: type.icon)
                .font(.caption2)

            Text(type.displayName)
                .font(AppConstants.Typography.caption)
        }
        .foregroundColor(.white)
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(colorForType(type))
        .cornerRadius(8)
    }
    
    private func colorForType(_ type: ClassType) -> Color {
        switch type.color {
        case "blue": return .blue
        case "green": return .green
        case "orange": return .orange
        case "purple": return .purple
        case "red": return .red
        case "pink": return .pink
        case "yellow": return .yellow
        case "indigo": return .indigo
        default: return .blue
        }
    }
}

// MARK: - Class Status Badge
struct ClassStatusBadge: View {
    let status: ClassStatus
    
    var body: some View {
        Text(status.displayName)
            .font(AppConstants.Typography.caption)
            .foregroundColor(colorForStatus(status))
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(colorForStatus(status).opacity(0.1))
            .cornerRadius(8)
    }
    
    private func colorForStatus(_ status: ClassStatus) -> Color {
        switch status.color {
        case "blue": return .blue
        case "green": return .green
        case "gray": return .gray
        case "red": return .red
        case "orange": return .orange
        default: return .blue
        }
    }
}

// MARK: - Compact Class Card
struct CompactClassCard: View {
    let classItem: Class
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                // Time
                VStack(spacing: 2) {
                    Text(DateFormatter.timeOnly.string(from: classItem.startTime))
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    Text(DateFormatter.timeOnly.string(from: classItem.endTime))
                        .font(.caption2)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
                .frame(width: 50)
                
                // Divider
                Rectangle()
                    .fill(AppConstants.Colors.border)
                    .frame(width: 1, height: 40)
                
                // Content
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(classItem.courseCode)
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                        
                        Spacer()
                        
                        ClassTypeBadge(type: classItem.type)
                    }
                    
                    Text(classItem.title)
                        .font(AppConstants.Typography.subheadline)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                        .lineLimit(1)
                    
                    if let location = classItem.location {
                        Text(location.displayName)
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                }
                
                Spacer()
                
                // Status indicator
                Circle()
                    .fill(colorForStatus(classItem.status))
                    .frame(width: 8, height: 8)
            }
            .padding(12)
            .background(AppConstants.Colors.cardBackground)
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(AppConstants.Colors.border, lineWidth: 0.5)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func colorForStatus(_ status: ClassStatus) -> Color {
        switch status.color {
        case "blue": return .blue
        case "green": return .green
        case "gray": return .gray
        case "red": return .red
        case "orange": return .orange
        default: return .blue
        }
    }
}

// MARK: - Extensions
extension DateFormatter {
    static let timeOnly: DateFormatter = {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter
    }()
}

// MARK: - Preview
struct ClassCard_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 16) {
            ClassCard(classItem: Class.mockClasses[0]) {
                print("Class tapped")
            }
            
            CompactClassCard(classItem: Class.mockClasses[0]) {
                print("Compact class tapped")
            }
        }
        .padding()
        .background(AppConstants.Colors.background)
    }
}
