//
//  TransactionRowView.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import SwiftUI

struct TransactionRowView: View {
    let transaction: Transaction
    
    var body: some View {
        HStack(spacing: 12) {
            // Transaction Icon
            Image(systemName: transaction.typeIcon)
                .font(.title2)
                .foregroundColor(iconColor)
                .frame(width: 40, height: 40)
                .background(iconColor.opacity(0.1))
                .cornerRadius(20)
            
            // Transaction Details
            VStack(alignment: .leading, spacing: 2) {
                Text(transaction.typeDisplayName)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                if let description = transaction.description {
                    Text(description)
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .lineLimit(1)
                } else {
                    Text(transaction.createdAt.timeAgoDisplay())
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
            }
            
            Spacer()
            
            // Amount and Status
            VStack(alignment: .trailing, spacing: 2) {
                HStack(spacing: 4) {
                    Text(amountPrefix)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(amountColor)
                    
                    Text(transaction.displayAmount)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(amountColor)
                }
                
                // Status Badge
                HStack(spacing: 4) {
                    Circle()
                        .fill(statusColor)
                        .frame(width: 6, height: 6)
                    
                    Text(transaction.status.displayName)
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
            }
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .contentShape(Rectangle())
    }
    
    // MARK: - Computed Properties
    private var iconColor: Color {
        switch transaction.type {
        case .purchase:
            return AppConstants.Colors.success
        case .refund:
            return AppConstants.Colors.warning
        case .transferIn:
            return AppConstants.Colors.info
        case .transferOut:
            return AppConstants.Colors.primary
        }
    }
    
    private var amountColor: Color {
        switch transaction.type {
        case .purchase, .transferIn:
            return AppConstants.Colors.success
        case .refund, .transferOut:
            return AppConstants.Colors.error
        }
    }
    
    private var amountPrefix: String {
        switch transaction.type {
        case .purchase, .transferIn:
            return "+"
        case .refund, .transferOut:
            return "-"
        }
    }
    
    private var statusColor: Color {
        switch transaction.status {
        case .pending:
            return AppConstants.Colors.warning
        case .completed:
            return AppConstants.Colors.success
        case .failed:
            return AppConstants.Colors.error
        }
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 8) {
        TransactionRowView(
            transaction: Transaction(
                id: "1",
                userId: nil,  // userId is now optional
                type: .purchase,
                amountUsd: 50.0,
                amountTokens: 50,
                businessId: "business1",
                businessCommission: 10.0,
                commissionRate: 0.02,
                platformProfit: 800,
                blockchainTxHash: "hash1",
                status: .completed,
                description: "Coffee purchase at Starbucks",
                metadata: nil,
                errorMessage: nil,
                retryCount: 0,
                createdAt: Date().addingTimeInterval(-3600),
                updatedAt: Date()
            )
        )
        
        TransactionRowView(
            transaction: Transaction(
                id: "2",
                userId: nil,  // userId is now optional
                type: .refund,
                amountUsd: nil,
                amountTokens: 25,
                businessId: nil,
                businessCommission: nil,
                commissionRate: nil,
                platformProfit: nil,
                blockchainTxHash: "hash2",
                status: .pending,
                description: "Free coffee voucher",
                metadata: nil,
                errorMessage: nil,
                retryCount: 0,
                createdAt: Date().addingTimeInterval(-7200),
                updatedAt: Date()
            )
        )
        
        TransactionRowView(
            transaction: Transaction(
                id: "3",
                userId: nil,  // userId is now optional
                type: .transferOut,
                amountUsd: nil,
                amountTokens: 10,
                businessId: nil,
                businessCommission: nil,
                commissionRate: nil,
                platformProfit: nil,
                blockchainTxHash: "hash3",
                status: .failed,
                description: "Transfer to friend",
                metadata: nil,
                errorMessage: "Insufficient balance",
                retryCount: 1,
                createdAt: Date().addingTimeInterval(-10800),
                updatedAt: Date()
            )
        )
    }
    .padding()
    .background(Color.white)
    .cornerRadius(12)
    .padding()
    .background(AppConstants.Colors.background)
}
