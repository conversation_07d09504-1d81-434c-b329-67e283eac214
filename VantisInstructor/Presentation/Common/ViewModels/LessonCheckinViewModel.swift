//
//  LessonCheckinViewModel.swift
//  mobile-app-template
//
//  Created by Instructor App on 29/7/25.
//

import Foundation
import CoreLocation
import SwiftUI

@MainActor
class LessonCheckinViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var isLoading = false
    @Published var showingResultBottomSheet = false
    @Published var resultMessage = ""
    @Published var isSuccess = false
    
    // MARK: - Private Properties
    private let checkinService: InstructorCheckinService
    private let locationManager = CLLocationManager()
    
    // MARK: - Initialization
    init(checkinService: InstructorCheckinService = InstructorCheckinService()) {
        self.checkinService = checkinService
        setupLocationManager()
    }
    
    // MARK: - Location Setup
    private func setupLocationManager() {
        locationManager.desiredAccuracy = kCLLocationAccuracyBest
        locationManager.requestWhenInUseAuthorization()
    }
    
    // MARK: - Check-in Methods
    func checkinToLesson(lessonId: Int, notes: String? = nil) async {
        print("🔥 checkinTo<PERSON><PERSON>on called with lessonId: \(lessonId)")
        guard !isLoading else {
            print("🔥 Already loading, returning early")
            return
        }

        print("🔥 Setting isLoading to true")
        isLoading = true

        // TEMPORARY: Mock success response for testing UI
        // TODO: Remove this when backend ErrorCode issue is fixed
        do {
            // Simulate network delay
            try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second

            // Mock successful response
            let mockResponse = InstructorCheckInResponse(
                success: true,
                message: "Check-in thành công! Bạn đã xác nhận có mặt tại buổi học.",
                data: nil,
                meta: CheckinMeta()
            )

            await handleCheckinResponse(mockResponse)

        } catch {
            await handleCheckinError(error)
        }

        isLoading = false
    }
    
    func checkoutFromLesson(lessonId: Int, notes: String? = nil, lessonSummary: String? = nil) async {
        guard !isLoading else { return }
        
        isLoading = true
        
        do {
            // Get current location if available
            let location = getCurrentLocation()
            
            // Call API
            let response = try await checkinService.checkoutFromLesson(
                lessonId: lessonId,
                location: location,
                notes: notes,
                lessonSummary: lessonSummary
            )
            
            // Handle response
            await handleCheckinResponse(response)
            
        } catch {
            await handleCheckinError(error)
        }
        
        isLoading = false
    }
    
    // MARK: - Response Handling
    private func handleCheckinResponse(_ response: InstructorCheckInResponse) async {
        isSuccess = response.success
        resultMessage = response.message
        showingResultBottomSheet = true
    }
    
    private func handleCheckinError(_ error: Error) async {
        isSuccess = false
        
        if let checkinError = error as? CheckinError {
            resultMessage = checkinError.localizedDescription
        } else if let networkError = error as? NetworkError {
            switch networkError {
            case .networkUnavailable:
                resultMessage = "Không thể kết nối đến server. Vui lòng kiểm tra kết nối mạng."
            case .unauthorized:
                resultMessage = "Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại."
            case .serverError(let code, let message):
                resultMessage = "Lỗi server (\(code)): \(message ?? "Lỗi không xác định")"
            case .decodingError:
                resultMessage = "Lỗi xử lý dữ liệu từ server."
            case .invalidURL:
                resultMessage = "Lỗi cấu hình ứng dụng."
            case .noData:
                resultMessage = "Không nhận được dữ liệu từ server."
            case .unknown(let underlyingError):
                resultMessage = "Lỗi không xác định: \(underlyingError.localizedDescription)"
            }
        } else if let apiError = error as? APIError {
            resultMessage = "Lỗi API: \(apiError.message)"
        } else {
            resultMessage = "Đã xảy ra lỗi không xác định: \(error.localizedDescription)"
        }
        
        showingResultBottomSheet = true
    }
    
    // MARK: - Location Helper
    private func getCurrentLocation() -> CLLocation? {
        guard locationManager.authorizationStatus == .authorizedWhenInUse ||
              locationManager.authorizationStatus == .authorizedAlways else {
            return nil
        }
        
        return locationManager.location
    }
    
    // MARK: - UI Actions
    func dismissResult() {
        showingResultBottomSheet = false
        resultMessage = ""
        isSuccess = false
    }
    
    func resetState() {
        isLoading = false
        showingResultBottomSheet = false
        resultMessage = ""
        isSuccess = false
    }
}

// MARK: - Preview Helper
extension LessonCheckinViewModel {
    static func preview() -> LessonCheckinViewModel {
        let viewModel = LessonCheckinViewModel()
        return viewModel
    }
    
    static func previewWithSuccess() -> LessonCheckinViewModel {
        let viewModel = LessonCheckinViewModel()
        viewModel.isSuccess = true
        viewModel.resultMessage = "Check-in thành công!"
        viewModel.showingResultBottomSheet = true
        return viewModel
    }
    
    static func previewWithError() -> LessonCheckinViewModel {
        let viewModel = LessonCheckinViewModel()
        viewModel.isSuccess = false
        viewModel.resultMessage = "Không thể check-in. Vui lòng thử lại."
        viewModel.showingResultBottomSheet = true
        return viewModel
    }
}
