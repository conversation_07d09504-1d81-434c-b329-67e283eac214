//
//  FontTestView.swift
//  mobile-app-template
//
//  Created by AI Assistant on 25/07/2025.
//

import SwiftUI

struct FontTestView: View {
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    // Header
                    Text("Be Vietnam Pro Font Test")
                        .font(AppConstants.Typography.largeTitle)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    // Font samples
                    VStack(alignment: .leading, spacing: 16) {
                        FontSample(
                            title: "Large Title",
                            font: AppConstants.Typography.largeTitle,
                            text: "Chào mừng đến với ứng dụng"
                        )
                        
                        FontSample(
                            title: "Title",
                            font: AppConstants.Typography.title,
                            text: "Tiêu đề chính của màn hình"
                        )
                        
                        FontSample(
                            title: "Title 2",
                            font: AppConstants.Typography.title2,
                            text: "Giảng viên <PERSON>"
                        )
                        
                        FontSample(
                            title: "Title 3",
                            font: AppConstants.Typography.title3,
                            text: "Phần tiêu đề phụ"
                        )
                        
                        FontSample(
                            title: "Headline",
                            font: AppConstants.Typography.headline,
                            text: "Lớp học hôm nay"
                        )
                        
                        FontSample(
                            title: "Subheadline",
                            font: AppConstants.Typography.subheadline,
                            text: "Chào buổi sáng!"
                        )
                        
                        FontSample(
                            title: "Body",
                            font: AppConstants.Typography.body,
                            text: "Đây là nội dung chính của ứng dụng với font Be Vietnam Pro"
                        )
                        
                        FontSample(
                            title: "Callout",
                            font: AppConstants.Typography.callout,
                            text: "Thông tin quan trọng cần chú ý"
                        )
                        
                        FontSample(
                            title: "Footnote",
                            font: AppConstants.Typography.footnote,
                            text: "Ghi chú nhỏ ở cuối trang"
                        )
                        
                        FontSample(
                            title: "Caption",
                            font: AppConstants.Typography.caption,
                            text: "3 lớp hôm nay"
                        )
                        
                        FontSample(
                            title: "Caption 2",
                            font: AppConstants.Typography.caption2,
                            text: "Thông tin rất nhỏ"
                        )
                    }
                    
                    // Vietnamese text test
                    VStack(alignment: .leading, spacing: 16) {
                        Text("Vietnamese Text Test")
                            .font(AppConstants.Typography.headline)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        
                        Text("Tiếng Việt với các dấu: áàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđ")
                            .font(AppConstants.Typography.body)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        
                        Text("TIẾNG VIỆT HOA: ÁÀẢÃẠĂẮẰẲẴẶÂẤẦẨẪẬÉÈẺẼẸÊẾỀỂỄỆÍÌỈĨỊÓÒỎÕỌÔỐỒỔỖỘƠỚỜỞỠỢÚÙỦŨỤƯỨỪỬỮỰÝỲỶỸỴĐ")
                            .font(AppConstants.Typography.subheadline)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    
                    // Font weight test
                    VStack(alignment: .leading, spacing: 16) {
                        Text("Font Weight Test")
                            .font(AppConstants.Typography.headline)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        
                        Text("Regular: Be Vietnam Pro Regular")
                            .font(.beVietnamPro(.regular, size: 16))
                        
                        Text("Medium: Be Vietnam Pro Medium")
                            .font(.beVietnamPro(.medium, size: 16))
                        
                        Text("SemiBold: Be Vietnam Pro SemiBold")
                            .font(.beVietnamPro(.semiBold, size: 16))
                        
                        Text("Bold: Be Vietnam Pro Bold")
                            .font(.beVietnamPro(.bold, size: 16))
                    }
                }
                .padding(AppConstants.UI.screenPadding)
            }
            .navigationTitle("Font Test")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

struct FontSample: View {
    let title: String
    let font: Font
    let text: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(title)
                .font(AppConstants.Typography.caption)
                .foregroundColor(AppConstants.Colors.textSecondary)
                .fontWeight(.medium)
            
            Text(text)
                .font(font)
                .foregroundColor(AppConstants.Colors.textPrimary)
        }
        .padding()
        .background(AppConstants.Colors.surface)
        .cornerRadius(AppConstants.UI.cornerRadius)
    }
}

#Preview {
    FontTestView()
}
