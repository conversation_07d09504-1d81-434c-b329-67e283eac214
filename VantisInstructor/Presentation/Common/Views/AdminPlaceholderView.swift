//
//  AdminPlaceholderView.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 22/7/25.
//

import SwiftUI

struct AdminPlaceholderView: View {
    var body: some View {
        VStack {
            Image(systemName: "gear")
                .font(.system(size: 60))
                .foregroundColor(AppConstants.Colors.primary)
                .padding()
            
            Text("Admin Feature")
                .font(.title)
                .fontWeight(.bold)
            
            Text("This is a placeholder for admin functionality. Customize this view based on your admin requirements.")
                .multilineTextAlignment(.center)
                .foregroundColor(AppConstants.Colors.textSecondary)
                .padding()
            
            Spacer()
        }
    }
}

#Preview {
    AdminPlaceholderView()
}
