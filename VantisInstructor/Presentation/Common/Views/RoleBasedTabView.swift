//
//  RoleBasedTabView.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 21/7/25.
//

import SwiftUI

// MARK: - Role-Based Tab View
struct RoleBasedTabView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var notificationsViewModel = NotificationsViewModel()
    
    var body: some View {
        Group {
            if let user = authViewModel.currentUser {
                switch user.role {
                case .admin:
                    AdminTabView()
                        .environmentObject(authViewModel)
                        .environmentObject(notificationsViewModel)
                        .onAppear {
                            print("🔐 RoleBasedTabView: Showing AdminTabView for ADMIN user - ID: \(user.id), Name: \(user.displayName)")
                        }
                case .instructor:
                    InstructorTabView()
                        .environmentObject(authViewModel)
                        .environmentObject(notificationsViewModel)
                        .onAppear {
                            print("🔐 RoleBasedTabView: Showing InstructorTabView for INSTRUCTOR user - ID: \(user.id), Name: \(user.displayName)")
                        }
                case .business:
                    BusinessTabView()
                        .environmentObject(authViewModel)
                        .environmentObject(notificationsViewModel)
                        .onAppear {
                            print("🔐 RoleBasedTabView: Showing BusinessTabView for BUSINESS user - ID: \(user.id), Name: \(user.displayName)")
                        }
                case .user:
                    UserTabView()
                        .environmentObject(notificationsViewModel)
                        .onAppear {
                            print("🔐 RoleBasedTabView: Showing UserTabView for USER user - ID: \(user.id), Name: \(user.displayName)")
                        }
                }
            } else {
                // Fallback to user view if no user data
                UserTabView()
                    .environmentObject(notificationsViewModel)
                    .onAppear {
                        print("🔐 RoleBasedTabView: No user found - showing fallback UserTabView")
                    }
            }
        }
        .onAppear {
            print("🔐 RoleBasedTabView: onAppear - currentUser: \(String(describing: authViewModel.currentUser))")
            if let user = authViewModel.currentUser {
                print("🔐 RoleBasedTabView: onAppear - User role: \(user.role)")
            }
        }
    }
}

// MARK: - Instructor Tab View
struct InstructorTabView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var notificationsViewModel = NotificationsViewModel()

    private var tabs: [TabItem] {
        [
            TabItem(id: 0, title: "Trang chủ", icon: "house.circle", selectedIcon: "house.circle.fill"),
            TabItem(id: 1, title: "Lớp học", icon: "graduationcap", selectedIcon: "graduationcap.fill"),
            TabItem(id: 2, title: "Bài tập", icon: "list.clipboard", selectedIcon: "list.clipboard.fill"),
            TabItem(id: 3, title: "Chấm điểm", icon: "chart.bar.doc.horizontal", selectedIcon: "chart.bar.doc.horizontal.fill"),
            TabItem(id: 4, title: "Hồ sơ", icon: "person.crop.circle", selectedIcon: "person.crop.circle.fill")
        ]
    }

    var body: some View {
        ModernTabView(tabs: tabs) { selectedTab in
            switch selectedTab {
            case 0:
                HomeView()
                    .environmentObject(notificationsViewModel)
            case 1:
                NavigationView {
                    ClassListView()
                }
                .environmentObject(notificationsViewModel)
            case 2:
                NavigationView {
                    QuizManagementView()
                        .environmentObject(authViewModel)
                }
                .environmentObject(notificationsViewModel)
            case 3:
                NavigationView {
                    PendingGradingView()
                }
                .environmentObject(notificationsViewModel)
            case 4:
                ProfileView()
                    .environmentObject(authViewModel)
                    .environmentObject(notificationsViewModel)
            default:
                HomeView()
                    .environmentObject(notificationsViewModel)
            }
        }
    }
}

// MARK: - Admin Tab View
struct AdminTabView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var notificationsViewModel = NotificationsViewModel()
    
    private var tabs: [TabItem] {
        [
            TabItem(id: 0, title: "Bảng điều khiển", icon: "chart.bar.xaxis", selectedIcon: "chart.bar.fill"),
            TabItem(id: 1, title: "Người dùng", icon: "person.2.circle", selectedIcon: "person.2.circle.fill"),
            TabItem(id: 2, title: "Doanh nghiệp", icon: "building.2.circle", selectedIcon: "building.2.circle.fill"),
            TabItem(id: 3, title: "Hồ sơ", icon: "person.crop.circle", selectedIcon: "person.crop.circle.fill")
        ]
    }
    
    var body: some View {
        ModernTabView(tabs: tabs) { selectedTab in
            switch selectedTab {
            case 0:
                AdminDashboardView()
                    .environmentObject(notificationsViewModel)
            case 1:
                AdminUsersView()
                    .environmentObject(notificationsViewModel)
            case 2:
                AdminPlaceholderView()
                    .environmentObject(notificationsViewModel)
            case 3:
                ProfileView()
                    .environmentObject(authViewModel)
                    .environmentObject(notificationsViewModel)
            default:
                AdminDashboardView()
                    .environmentObject(notificationsViewModel)
            }
        }
    }
}

// MARK: - Business Tab View
struct BusinessTabView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var notificationsViewModel = NotificationsViewModel()
    
    private var tabs: [TabItem] {
        [
            TabItem(id: 0, title: "Bảng điều khiển", icon: "chart.line.uptrend.xyaxis.circle", selectedIcon: "chart.line.uptrend.xyaxis.circle.fill"),
            TabItem(id: 1, title: "Bài tập", icon: "list.clipboard", selectedIcon: "list.clipboard.fill"),
            TabItem(id: 2, title: "Thanh toán", icon: "banknote.circle", selectedIcon: "banknote.circle.fill"),
            TabItem(id: 3, title: "Phân tích", icon: "chart.bar.xaxis", selectedIcon: "chart.bar.fill"),
            TabItem(id: 4, title: "Hồ sơ", icon: "person.crop.circle", selectedIcon: "person.crop.circle.fill")
        ]
    }
    
    var body: some View {
        ModernTabView(tabs: tabs) { selectedTab in
            switch selectedTab {
            case 0:
                AdminPlaceholderView()
                    .environmentObject(notificationsViewModel)
            case 1:
                NavigationView {
                    SimpleAssignmentView()
                }
                .environmentObject(notificationsViewModel)
            case 2:
                AdminPlaceholderView()
                    .environmentObject(notificationsViewModel)
            case 3:
                AdminPlaceholderView()
                    .environmentObject(notificationsViewModel)
            case 4:
                ProfileView()
                    .environmentObject(authViewModel)
                    .environmentObject(notificationsViewModel)
            default:
                AdminPlaceholderView()
                    .environmentObject(notificationsViewModel)
            }
        }
    }
}

// MARK: - User Tab View (Original Layout)
struct UserTabView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var notificationsViewModel = NotificationsViewModel()
    
    private var tabs: [TabItem] {
        [
            TabItem(id: 0, title: "Trang chủ", icon: "house.circle", selectedIcon: "house.circle.fill"),
            TabItem(id: 1, title: "Nhiệm vụ", icon: "flag.circle", selectedIcon: "flag.circle.fill"),
            TabItem(id: 2, title: "Bài tập", icon: "list.clipboard", selectedIcon: "list.clipboard.fill"),
            TabItem(id: 3, title: "Lịch dạy", icon: "calendar.circle", selectedIcon: "calendar.circle.fill"),
            TabItem(id: 4, title: "Hồ sơ", icon: "person.crop.circle", selectedIcon: "person.crop.circle.fill")
        ]
    }
    
    var body: some View {
        ModernTabView(tabs: tabs) { selectedTab in
            switch selectedTab {
            case 0:
                HomeView()
                    .environmentObject(notificationsViewModel)
            case 1:
                NavigationStack {
                    MissionsView(userId: authViewModel.currentUser?.id ?? "user_1")
                }
            case 2:
                AssignmentListView()
            case 3:
                NavigationView {
                    ClassListView()
                }
            case 4:
                ProfileView()
                    .environmentObject(authViewModel)
            default:
                HomeView()
                    .environmentObject(notificationsViewModel)
            }
        }
    }
}

// MARK: - Preview
#Preview {
    RoleBasedTabView()
        .environmentObject(AuthViewModel())
}
