//
//  SkeletonLoadingView.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 21/7/25.
//

import SwiftUI

struct SkeletonLoadingView: View {
    @State private var isAnimating = false
    
    var body: some View {
        RoundedRectangle(cornerRadius: 8)
            .fill(Color.gray.opacity(0.3))
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.clear,
                                Color.white.opacity(0.6),
                                Color.clear
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .offset(x: isAnimating ? 200 : -200)
                    .animation(
                        Animation.linear(duration: 1.5)
                            .repeatForever(autoreverses: false),
                        value: isAnimating
                    )
            )
            .clipped()
            .onAppear {
                isAnimating = true
            }
    }
}

struct TransactionSkeletonRow: View {
    var body: some View {
        HStack(spacing: 12) {
            // Icon placeholder
            Circle()
                .fill(Color.gray.opacity(0.3))
                .frame(width: 40, height: 40)
                .overlay(
                    Circle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color.clear,
                                    Color.white.opacity(0.6),
                                    Color.clear
                                ]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                )
            
            VStack(alignment: .leading, spacing: 6) {
                // Title placeholder
                SkeletonLoadingView()
                    .frame(height: 16)
                    .frame(maxWidth: .infinity, alignment: .leading)
                
                // Subtitle placeholder
                SkeletonLoadingView()
                    .frame(height: 12)
                    .frame(width: 120, alignment: .leading)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 6) {
                // Amount placeholder
                SkeletonLoadingView()
                    .frame(width: 80, height: 16)
                
                // Date placeholder
                SkeletonLoadingView()
                    .frame(width: 60, height: 12)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
    }
}

struct RewardSkeletonCard: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Image placeholder
            SkeletonLoadingView()
                .frame(height: 120)
                .cornerRadius(12)
            
            VStack(alignment: .leading, spacing: 8) {
                // Title placeholder
                SkeletonLoadingView()
                    .frame(height: 16)
                
                // Description placeholder
                SkeletonLoadingView()
                    .frame(height: 12)
                    .frame(width: 140)
                
                // Price placeholder
                SkeletonLoadingView()
                    .frame(width: 80, height: 14)
            }
            .padding(.horizontal, 12)
            .padding(.bottom, 12)
        }
        .frame(width: 200)
        .background(AppConstants.Colors.surface)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
    }
}

struct TransactionLoadingState: View {
    var body: some View {
        VStack(spacing: 0) {
            ForEach(0..<3, id: \.self) { _ in
                TransactionSkeletonRow()
                
                Divider()
                    .padding(.leading, 68)
            }
        }
        .background(AppConstants.Colors.surface)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
    }
}

struct RewardLoadingState: View {
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            LazyHStack(spacing: 12) {
                ForEach(0..<3, id: \.self) { _ in
                    RewardSkeletonCard()
                }
            }
            .padding(.horizontal, AppConstants.UI.screenPadding)
        }
        .padding(.horizontal, -AppConstants.UI.screenPadding)
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 20) {
        Text("Transaction Loading State")
            .font(.headline)
        
        TransactionLoadingState()
        
        Text("Reward Loading State")
            .font(.headline)
        
        RewardLoadingState()
    }
    .padding()
    .background(AppConstants.Colors.background)
}
