//
//  ErrorDetailsView.swift
//  VantisInstructor
//
//  Created by AI Assistant on 30/7/25.
//

import SwiftUI

struct ErrorDetailsView: View {
    let error: ErrorInfo
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    // Error overview
                    errorOverviewSection
                    
                    // Error details
                    if let details = error.details {
                        errorDetailsSection(details)
                    }
                    
                    // Timestamp info
                    timestampSection
                    
                    // Troubleshooting tips
                    troubleshootingSection
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            }
            .navigationTitle("Chi tiết lỗi")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    But<PERSON>(action: { dismiss() }) {
                        HStack(spacing: 6) {
                            Image(systemName: "chevron.left")
                                .font(.system(size: 16, weight: .medium))
                            Text("Đóng")
                                .font(.beVietnamPro(.medium, size: 16))
                        }
                        .foregroundColor(AppConstants.Colors.primary)
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: copyErrorDetails) {
                        Image(systemName: "doc.on.doc")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(AppConstants.Colors.primary)
                    }
                }
            }
        }
    }
    
    // MARK: - Error Overview Section
    private var errorOverviewSection: some View {
        VStack(spacing: 16) {
            // Error icon and type
            HStack(spacing: 16) {
                ZStack {
                    Circle()
                        .fill(error.type.color.opacity(0.15))
                        .frame(width: 60, height: 60)
                    
                    Image(systemName: error.type.icon)
                        .font(.system(size: 24, weight: .medium))
                        .foregroundColor(error.type.color)
                }
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(error.type.title)
                        .font(.beVietnamPro(.bold, size: 18))
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    Text("Loại lỗi: \(errorTypeDescription)")
                        .font(.beVietnamPro(.medium, size: 14))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
                
                Spacer()
            }
            
            // Error title and message
            VStack(alignment: .leading, spacing: 12) {
                if error.title != error.type.title {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Tiêu đề:")
                            .font(.beVietnamPro(.semiBold, size: 14))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                        
                        Text(error.title)
                            .font(.beVietnamPro(.medium, size: 16))
                            .foregroundColor(AppConstants.Colors.textPrimary)
                    }
                }
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("Thông báo:")
                        .font(.beVietnamPro(.semiBold, size: 14))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                    
                    Text(error.message)
                        .font(.beVietnamPro(.medium, size: 16))
                        .foregroundColor(AppConstants.Colors.textPrimary)
                        .lineLimit(nil)
                }
            }
        }
        .padding(20)
        .background(AppConstants.Colors.surfaceSecondary)
        .cornerRadius(16)
    }
    
    // MARK: - Error Details Section
    private func errorDetailsSection(_ details: String) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Chi tiết kỹ thuật:")
                .font(.beVietnamPro(.bold, size: 16))
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            ScrollView {
                Text(details)
                    .font(.system(.caption, design: .monospaced))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(12)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
            }
            .frame(maxHeight: 200)
        }
        .padding(20)
        .background(Color.white)
        .cornerRadius(16)
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(Color.gray.opacity(0.2), lineWidth: 1)
        )
    }
    
    // MARK: - Timestamp Section
    private var timestampSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Thông tin thời gian:")
                .font(.beVietnamPro(.bold, size: 16))
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            VStack(spacing: 8) {
                InfoRow(
                    title: "Thời gian xảy ra:",
                    value: formatTimestamp(error.timestamp)
                )
                
                InfoRow(
                    title: "Thời gian trôi qua:",
                    value: timeAgoString(from: error.timestamp)
                )
            }
        }
        .padding(20)
        .background(Color.white)
        .cornerRadius(16)
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(Color.gray.opacity(0.2), lineWidth: 1)
        )
    }
    
    // MARK: - Troubleshooting Section
    private var troubleshootingSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Gợi ý khắc phục:")
                .font(.beVietnamPro(.bold, size: 16))
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            VStack(alignment: .leading, spacing: 8) {
                ForEach(troubleshootingTips, id: \.self) { tip in
                    HStack(alignment: .top, spacing: 8) {
                        Text("•")
                            .font(.beVietnamPro(.medium, size: 14))
                            .foregroundColor(AppConstants.Colors.primary)
                        
                        Text(tip)
                            .font(.beVietnamPro(.medium, size: 14))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                            .lineLimit(nil)
                    }
                }
            }
        }
        .padding(20)
        .background(Color.white)
        .cornerRadius(16)
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(Color.gray.opacity(0.2), lineWidth: 1)
        )
    }
    
    // MARK: - Helper Views
    private struct InfoRow: View {
        let title: String
        let value: String
        
        var body: some View {
            HStack {
                Text(title)
                    .font(.beVietnamPro(.medium, size: 14))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                
                Spacer()
                
                Text(value)
                    .font(.beVietnamPro(.semiBold, size: 14))
                    .foregroundColor(AppConstants.Colors.textPrimary)
            }
        }
    }
    
    // MARK: - Helper Methods
    private var errorTypeDescription: String {
        switch error.type {
        case .network:
            return "Kết nối mạng"
        case .validation:
            return "Xác thực dữ liệu"
        case .authentication:
            return "Xác thực người dùng"
        case .server:
            return "Máy chủ"
        case .unknown:
            return "Không xác định"
        }
    }
    
    private var troubleshootingTips: [String] {
        switch error.type {
        case .network:
            return [
                "Kiểm tra kết nối internet của bạn",
                "Thử kết nối lại WiFi hoặc dữ liệu di động",
                "Khởi động lại ứng dụng",
                "Liên hệ hỗ trợ nếu vấn đề vẫn tiếp tục"
            ]
        case .validation:
            return [
                "Kiểm tra lại thông tin đã nhập",
                "Đảm bảo tất cả trường bắt buộc đã được điền",
                "Xác minh định dạng dữ liệu (email, số điện thoại, v.v.)",
                "Thử nhập lại thông tin"
            ]
        case .authentication:
            return [
                "Đăng xuất và đăng nhập lại",
                "Kiểm tra thông tin đăng nhập",
                "Xóa cache ứng dụng",
                "Liên hệ quản trị viên nếu cần thiết"
            ]
        case .server:
            return [
                "Đợi một lúc và thử lại",
                "Kiểm tra trạng thái máy chủ",
                "Khởi động lại ứng dụng",
                "Báo cáo sự cố cho đội ngũ hỗ trợ"
            ]
        case .unknown:
            return [
                "Khởi động lại ứng dụng",
                "Cập nhật ứng dụng lên phiên bản mới nhất",
                "Khởi động lại thiết bị",
                "Liên hệ hỗ trợ kỹ thuật"
            ]
        }
    }
    
    private func formatTimestamp(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .medium
        formatter.locale = Locale(identifier: "vi_VN")
        return formatter.string(from: date)
    }
    
    private func timeAgoString(from date: Date) -> String {
        let interval = Date().timeIntervalSince(date)
        
        if interval < 60 {
            return "Vừa xong"
        } else if interval < 3600 {
            let minutes = Int(interval / 60)
            return "\(minutes) phút trước"
        } else if interval < 86400 {
            let hours = Int(interval / 3600)
            return "\(hours) giờ trước"
        } else {
            let days = Int(interval / 86400)
            return "\(days) ngày trước"
        }
    }
    
    private func copyErrorDetails() {
        let errorText = """
        Loại lỗi: \(errorTypeDescription)
        Tiêu đề: \(error.title)
        Thông báo: \(error.message)
        Thời gian: \(formatTimestamp(error.timestamp))
        
        Chi tiết kỹ thuật:
        \(error.details ?? "Không có")
        """
        
        UIPasteboard.general.string = errorText
        
        // Show a brief feedback (you might want to implement a toast notification)
        print("📋 Error details copied to clipboard")
    }
}
