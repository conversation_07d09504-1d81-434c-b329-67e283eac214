//
//  ErrorModalView.swift
//  VantisInstructor
//
//  Created by AI Assistant on 30/7/25.
//

import SwiftUI

// MARK: - Error Types
enum ErrorType {
    case network
    case validation
    case authentication
    case server
    case unknown
    
    var icon: String {
        switch self {
        case .network:
            return "wifi.exclamationmark"
        case .validation:
            return "exclamationmark.triangle"
        case .authentication:
            return "person.crop.circle.badge.exclamationmark"
        case .server:
            return "server.rack"
        case .unknown:
            return "questionmark.circle"
        }
    }
    
    var color: Color {
        switch self {
        case .network:
            return AppConstants.Colors.warning
        case .validation:
            return AppConstants.Colors.error
        case .authentication:
            return AppConstants.Colors.accent
        case .server:
            return AppConstants.Colors.error
        case .unknown:
            return AppConstants.Colors.textSecondary
        }
    }
    
    var title: String {
        switch self {
        case .network:
            return "Lỗi kết nối"
        case .validation:
            return "Dữ liệu không hợp lệ"
        case .authentication:
            return "Lỗi xác thực"
        case .server:
            return "Lỗi máy chủ"
        case .unknown:
            return "Lỗi không xác định"
        }
    }
}

// MARK: - Error Info Model
struct ErrorInfo {
    let id = UUID()
    let type: ErrorType
    let title: String
    let message: String
    let timestamp: Date
    let details: String?
    
    init(type: ErrorType, title: String? = nil, message: String, details: String? = nil) {
        self.type = type
        self.title = title ?? type.title
        self.message = message
        self.timestamp = Date()
        self.details = details
    }
}

// MARK: - Error Modal View
struct ErrorModalView: View {
    @Binding var isPresented: Bool
    let errors: [ErrorInfo]
    let onRetry: (() -> Void)?
    let onDismiss: (() -> Void)?
    
    @State private var selectedError: ErrorInfo?
    @State private var showDetails = false
    
    init(
        isPresented: Binding<Bool>,
        errors: [ErrorInfo],
        onRetry: (() -> Void)? = nil,
        onDismiss: (() -> Void)? = nil
    ) {
        self._isPresented = isPresented
        self.errors = errors
        self.onRetry = onRetry
        self.onDismiss = onDismiss
    }
    
    // Convenience init for single error
    init(
        isPresented: Binding<Bool>,
        error: ErrorInfo,
        onRetry: (() -> Void)? = nil,
        onDismiss: (() -> Void)? = nil
    ) {
        self._isPresented = isPresented
        self.errors = [error]
        self.onRetry = onRetry
        self.onDismiss = onDismiss
    }
    
    var body: some View {
        ZStack {
            // Background overlay
            Color.black.opacity(0.4)
                .ignoresSafeArea()
                .onTapGesture {
                    dismissModal()
                }
            
            // Modal content
            VStack(spacing: 0) {
                // Header
                headerSection
                
                // Content
                if errors.count == 1 {
                    singleErrorView(errors.first!)
                } else {
                    multipleErrorsView
                }
                
                // Actions
                actionSection
            }
            .background(Color.white)
            .cornerRadius(20)
            .shadow(color: Color.black.opacity(0.15), radius: 20, x: 0, y: 10)
            .padding(.horizontal, 24)
            .scaleEffect(isPresented ? 1.0 : 0.8)
            .opacity(isPresented ? 1.0 : 0.0)
            .animation(.spring(response: 0.4, dampingFraction: 0.8), value: isPresented)
        }
        .sheet(isPresented: $showDetails) {
            if let selectedError = selectedError {
                ErrorDetailsView(error: selectedError)
            }
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(errors.count == 1 ? "Đã xảy ra lỗi" : "Đã xảy ra \(errors.count) lỗi")
                    .font(.beVietnamPro(.bold, size: 20))
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Text("Vui lòng kiểm tra thông tin bên dưới")
                    .font(.beVietnamPro(.medium, size: 14))
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            
            Spacer()
            
            Button(action: dismissModal) {
                Image(systemName: "xmark.circle.fill")
                    .font(.system(size: 24))
                    .foregroundColor(AppConstants.Colors.textTertiary)
            }
        }
        .padding(.horizontal, 24)
        .padding(.vertical, 20)
        .background(
            Rectangle()
                .fill(AppConstants.Colors.surfaceSecondary)
                .cornerRadius(20, corners: [.topLeft, .topRight])
        )
    }
    
    // MARK: - Single Error View
    private func singleErrorView(_ error: ErrorInfo) -> some View {
        VStack(spacing: 20) {
            // Error icon and info
            VStack(spacing: 16) {
                ZStack {
                    Circle()
                        .fill(error.type.color.opacity(0.15))
                        .frame(width: 80, height: 80)
                    
                    Image(systemName: error.type.icon)
                        .font(.system(size: 32, weight: .medium))
                        .foregroundColor(error.type.color)
                }
                
                VStack(spacing: 8) {
                    Text(error.title)
                        .font(.beVietnamPro(.bold, size: 18))
                        .foregroundColor(AppConstants.Colors.textPrimary)
                        .multilineTextAlignment(.center)
                    
                    Text(error.message)
                        .font(.beVietnamPro(.medium, size: 16))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                        .lineLimit(nil)
                }
            }
            
            // Details button if available
            if error.details != nil {
                Button(action: {
                    selectedError = error
                    showDetails = true
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: "info.circle")
                            .font(.system(size: 14))
                        Text("Xem chi tiết")
                            .font(.beVietnamPro(.medium, size: 14))
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
            }
        }
        .padding(.horizontal, 24)
        .padding(.vertical, 24)
    }
    
    // MARK: - Multiple Errors View
    private var multipleErrorsView: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(errors, id: \.id) { error in
                    ErrorRowView(error: error) {
                        selectedError = error
                        showDetails = true
                    }
                }
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 16)
        }
        .frame(maxHeight: 300)
    }
    
    // MARK: - Action Section
    private var actionSection: some View {
        HStack(spacing: 12) {
            // Dismiss button
            Button(action: dismissModal) {
                Text("Đóng")
                    .font(.beVietnamPro(.semiBold, size: 16))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .frame(maxWidth: .infinity)
                    .frame(height: 48)
                    .background(AppConstants.Colors.surfaceSecondary)
                    .cornerRadius(12)
            }
            
            // Retry button if available
            if let onRetry = onRetry {
                Button(action: {
                    dismissModal()
                    onRetry()
                }) {
                    Text("Thử lại")
                        .font(.beVietnamPro(.semiBold, size: 16))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 48)
                        .background(
                            LinearGradient(
                                gradient: Gradient(colors: [AppConstants.Colors.primary, AppConstants.Colors.primaryDeep]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(12)
                }
            }
        }
        .padding(.horizontal, 24)
        .padding(.bottom, 24)
    }
    
    private func dismissModal() {
        isPresented = false
        onDismiss?()
    }
}

// MARK: - Error Row View
struct ErrorRowView: View {
    let error: ErrorInfo
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                // Error icon
                ZStack {
                    Circle()
                        .fill(error.type.color.opacity(0.15))
                        .frame(width: 40, height: 40)
                    
                    Image(systemName: error.type.icon)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(error.type.color)
                }
                
                // Error info
                VStack(alignment: .leading, spacing: 4) {
                    Text(error.title)
                        .font(.beVietnamPro(.semiBold, size: 14))
                        .foregroundColor(AppConstants.Colors.textPrimary)
                        .lineLimit(1)
                    
                    Text(error.message)
                        .font(.beVietnamPro(.medium, size: 12))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .lineLimit(2)
                }
                
                Spacer()
                
                // Timestamp
                Text(timeAgoString(from: error.timestamp))
                    .font(.beVietnamPro(.medium, size: 10))
                    .foregroundColor(AppConstants.Colors.textTertiary)
                
                // Chevron
                Image(systemName: "chevron.right")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(AppConstants.Colors.textTertiary)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(AppConstants.Colors.surfaceSecondary)
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func timeAgoString(from date: Date) -> String {
        let interval = Date().timeIntervalSince(date)
        
        if interval < 60 {
            return "Vừa xong"
        } else if interval < 3600 {
            let minutes = Int(interval / 60)
            return "\(minutes) phút trước"
        } else if interval < 86400 {
            let hours = Int(interval / 3600)
            return "\(hours) giờ trước"
        } else {
            let days = Int(interval / 86400)
            return "\(days) ngày trước"
        }
    }
}


