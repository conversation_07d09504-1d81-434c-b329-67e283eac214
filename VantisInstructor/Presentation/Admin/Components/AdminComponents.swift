//
//  AdminComponents.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 21/7/25.
//

import SwiftUI

// MARK: - Admin Stat Card
struct AdminStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    let trend: Double?
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                    
                    Text(value)
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                }
                
                Spacer()
                
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
            }
            
            if let trend = trend {
                HStack {
                    Image(systemName: trend >= 0 ? "arrow.up.right" : "arrow.down.right")
                        .font(.caption)
                        .foregroundColor(trend >= 0 ? .green : .red)
                    
                    Text("\(abs(trend), specifier: "%.1f")%")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(trend >= 0 ? .green : .red)
                    
                    Spacer()
                }
            }
        }
        .padding(16)
        .background(Color.white)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
}

// MARK: - Admin Action Card
struct AdminActionCard: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                HStack {
                    Image(systemName: icon)
                        .font(.title2)
                        .foregroundColor(color)
                    
                    Spacer()
                }
                
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(title)
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        
                        Spacer()
                    }
                    
                    HStack {
                        Text(subtitle)
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                            .multilineTextAlignment(.leading)
                        
                        Spacer()
                    }
                }
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(16)
            .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Admin Activity Row
struct AdminActivityRow: View {
    let activity: AdminActivity
    
    var body: some View {
        HStack(spacing: 12) {
            // Icon
            Image(systemName: activity.iconName)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(activity.iconColor)
                .frame(width: 32, height: 32)
                .background(activity.iconColor.opacity(0.1))
                .cornerRadius(8)
            
            // Content
            VStack(alignment: .leading, spacing: 2) {
                Text(activity.description)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                    .lineLimit(2)
                
                Text(formatRelativeTime(activity.timestamp))
                    .font(.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            
            Spacer()
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.03), radius: 4, x: 0, y: 1)
    }
    
    private func formatRelativeTime(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: date, relativeTo: Date())
    }
}

// MARK: - System Status Row
struct SystemStatusRow: View {
    let service: String
    let status: SystemStatus
    let responseTime: String
    
    var body: some View {
        HStack {
            // Status indicator
            Circle()
                .fill(status.color)
                .frame(width: 8, height: 8)
            
            // Service name
            Text(service)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Spacer()
            
            // Response time
            Text(responseTime)
                .font(.caption)
                .foregroundColor(AppConstants.Colors.textSecondary)
            
            // Status text
            Text(status.displayName)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(status.color)
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(Color.white)
        .cornerRadius(8)
        .shadow(color: Color.black.opacity(0.02), radius: 2, x: 0, y: 1)
    }
}

// MARK: - User List Row
struct AdminUserRow: View {
    let user: User
    let onStatusToggle: (String, Bool) -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            // Avatar
            AsyncImage(url: URL(string: user.avatar ?? "")) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                Circle()
                    .fill(AppConstants.Colors.primary.opacity(0.1))
                    .overlay(
                        Text(user.initials)
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(AppConstants.Colors.primary)
                    )
            }
            .frame(width: 40, height: 40)
            .clipShape(Circle())
            
            // User info
            VStack(alignment: .leading, spacing: 2) {
                Text(user.displayName)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Text(user.email)
                    .font(.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                
                HStack(spacing: 8) {
                    // Role badge
                    Text(user.role.displayName)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(user.role == .admin ? .orange : user.role == .business ? .blue : .gray)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(
                            Capsule()
                                .fill((user.role == .admin ? Color.orange : user.role == .business ? Color.blue : Color.gray).opacity(0.1))
                        )
                    
                    Spacer()
                }
            }
            
            Spacer()
            
            // Status toggle
            Toggle("", isOn: Binding(
                get: { user.isActive },
                set: { newValue in
                    onStatusToggle(user.id, newValue)
                }
            ))
            .toggleStyle(SwitchToggleStyle(tint: AppConstants.Colors.primary))
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.03), radius: 4, x: 0, y: 1)
    }
}

// MARK: - Business User Row
struct AdminBusinessRow: View {
    let merchantUser: BusinessUser
    let onApprove: (String) -> Void
    let onSuspend: (String) -> Void
    
    var body: some View {
        VStack(spacing: 12) {
            HStack(spacing: 12) {
                // Avatar placeholder
                Circle()
                    .fill(AppConstants.Colors.primary.opacity(0.1))
                    .frame(width: 40, height: 40)
                    .overlay(
                        Text(merchantUser.initials)
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(AppConstants.Colors.primary)
                    )
                
                // Business info
                VStack(alignment: .leading, spacing: 2) {
                    Text(merchantUser.displayName)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    Text(merchantUser.email)
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                    
                    if let businessName = merchantUser.businessInfo?.businessName {
                        Text(businessName)
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                    }
                }
                
                Spacer()
                
                // Status badge
                if let status = merchantUser.businessInfo?.status {
                    Text(status)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(statusColor(for: status))
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(
                            Capsule()
                                .fill(statusColor(for: status).opacity(0.1))
                        )
                }
            }
            
            // Action buttons
            if merchantUser.businessInfo?.status == "PENDING" {
                HStack(spacing: 8) {
                    Button("Approve") {
                        onApprove(merchantUser.id)
                    }
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 6)
                    .background(.green)
                    .cornerRadius(8)
                    
                    Button("Reject") {
                        onSuspend(merchantUser.id)
                    }
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 6)
                    .background(.red)
                    .cornerRadius(8)
                    
                    Spacer()
                }
            }
        }
        .padding(12)
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.03), radius: 4, x: 0, y: 1)
    }
    
    private func statusColor(for status: String) -> Color {
        switch status.uppercased() {
        case "ACTIVE":
            return .green
        case "PENDING":
            return .orange
        case "SUSPENDED":
            return .red
        default:
            return .gray
        }
    }
}

// MARK: - Supporting Models
struct UserListResponse: Codable {
    let users: [User]
    let total: Int
    let limit: Int
    let offset: Int
}

struct BusinessUser: Codable, Identifiable {
    let id: String
    let email: String
    let firstName: String?
    let lastName: String?
    var businessInfo: BusinessInfo?
    let createdAt: Date
    
    var displayName: String {
        if let firstName = firstName, let lastName = lastName {
            return "\(firstName) \(lastName)"
        }
        return email
    }
    
    var initials: String {
        let components = displayName.components(separatedBy: " ")
        let initials = components.compactMap { $0.first }.map { String($0) }
        return initials.prefix(2).joined().uppercased()
    }
    
    struct BusinessInfo: Codable {
        let businessName: String?
        let businessId: String?
        let category: String?
        let phone: String?
        let website: String?
        let description: String?
        var status: String?
        let onboardedAt: Date?
    }
}
