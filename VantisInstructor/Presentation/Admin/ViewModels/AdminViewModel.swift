//
//  AdminViewModel.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 21/7/25.
//

import Foundation
import SwiftUI

// MARK: - Simple API Response
struct SimpleAPIResponse: Codable {
    let success: Bool
    let message: String?
}

// MARK: - Admin Stats Model
struct AdminStats: Codable {
    let totalUsers: Int
    let activeBusinesss: Int
    let totalTransactions: Int
    let totalRevenue: Double
    let userGrowth: Double?
    let merchantGrowth: Double?
    let transactionGrowth: Double?
    let revenueGrowth: Double?
    let lastUpdated: Date
}

// MARK: - Admin Activity Model
struct AdminActivity: Codable, Identifiable {
    let id: String
    let type: ActivityType
    let description: String
    let userId: String?
    let businessId: String?
    let timestamp: Date
    let metadata: [String: String]?
    
    enum ActivityType: String, Codable {
        case userRegistration = "USER_REGISTRATION"
        case merchantApproval = "MERCHANT_APPROVAL"
        case rewardCreation = "REWARD_CREATION"
        case transactionProcessed = "TRANSACTION_PROCESSED"
        case systemUpdate = "SYSTEM_UPDATE"
    }
    
    var iconName: String {
        switch type {
        case .userRegistration:
            return "person.badge.plus"
        case .merchantApproval:
            return "checkmark.circle"
        case .rewardCreation:
            return "star.circle"
        case .transactionProcessed:
            return "creditcard"
        case .systemUpdate:
            return "gearshape"
        }
    }
    
    var iconColor: Color {
        switch type {
        case .userRegistration:
            return .blue
        case .merchantApproval:
            return .green
        case .rewardCreation:
            return .yellow
        case .transactionProcessed:
            return .purple
        case .systemUpdate:
            return .orange
        }
    }
}

// MARK: - System Status Model
enum SystemStatus: String, Codable {
    case operational = "OPERATIONAL"
    case degraded = "DEGRADED"
    case outage = "OUTAGE"
    
    var color: Color {
        switch self {
        case .operational:
            return .green
        case .degraded:
            return .yellow
        case .outage:
            return .red
        }
    }
    
    var displayName: String {
        switch self {
        case .operational:
            return "Operational"
        case .degraded:
            return "Degraded"
        case .outage:
            return "Outage"
        }
    }
}

// MARK: - Admin Repository Protocol
protocol AdminRepositoryProtocol {
    func getAdminStats() async throws -> AdminStats
    func getRecentActivity(limit: Int) async throws -> [AdminActivity]
    func getAllUsers(page: Int, limit: Int, role: String?, status: String?, search: String?) async throws -> UserListResponse
    func updateUserStatus(userId: String, isActive: Bool) async throws -> Bool
    func getBusinessUsers(status: String?) async throws -> [BusinessUser]
    func approveBusinessUser(userId: String) async throws -> Bool
    func suspendBusinessUser(userId: String) async throws -> Bool
}

// MARK: - Admin Repository Implementation
class AdminRepository: AdminRepositoryProtocol {
    private let apiClient = APIClient.shared
    
    func getAdminStats() async throws -> AdminStats {
        return try await apiClient.request(
            endpoint: "/admin/stats",
            method: .GET,
            responseType: AdminStats.self
        )
    }
    
    func getRecentActivity(limit: Int = 20) async throws -> [AdminActivity] {
        return try await apiClient.request(
            endpoint: "/admin/recent-activity",
            method: .GET,
            parameters: ["limit": limit],
            responseType: [AdminActivity].self
        )
    }
    
    func getAllUsers(page: Int = 1, limit: Int = 20, role: String? = nil, status: String? = nil, search: String? = nil) async throws -> UserListResponse {
        var parameters: [String: Any] = [
            "limit": limit,
            "offset": (page - 1) * limit
        ]
        
        if let role = role {
            parameters["role"] = role
        }
        
        if let status = status {
            parameters["status"] = status
        }
        
        if let search = search, !search.isEmpty {
            parameters["search"] = search
        }
        
        return try await apiClient.request(
            endpoint: "/admin/users",
            method: .GET,
            parameters: parameters,
            responseType: UserListResponse.self
        )
    }
    
    func updateUserStatus(userId: String, isActive: Bool) async throws -> Bool {
        let response: SimpleAPIResponse = try await apiClient.request(
            endpoint: "/admin/users/\(userId)/status",
            method: .PUT,
            parameters: ["isActive": isActive],
            responseType: SimpleAPIResponse.self
        )

        return response.success
    }
    
    func getBusinessUsers(status: String? = nil) async throws -> [BusinessUser] {
        var parameters: [String: Any] = [:]
        
        if let status = status {
            parameters["status"] = status
        }
        
        return try await apiClient.request(
            endpoint: "/admin/merchant-users",
            method: .GET,
            parameters: parameters,
            responseType: [BusinessUser].self
        )
    }
    
    func approveBusinessUser(userId: String) async throws -> Bool {
        let response: SimpleAPIResponse = try await apiClient.request(
            endpoint: "/admin/merchant-users/\(userId)/approve",
            method: .PUT,
            responseType: SimpleAPIResponse.self
        )

        return response.success
    }
    
    func suspendBusinessUser(userId: String) async throws -> Bool {
        let response: SimpleAPIResponse = try await apiClient.request(
            endpoint: "/admin/merchant-users/\(userId)/suspend",
            method: .PUT,
            responseType: SimpleAPIResponse.self
        )

        return response.success
    }
}

// MARK: - Admin ViewModel
@MainActor
class AdminViewModel: ObservableObject {
    @Published var stats: AdminStats?
    @Published var recentActivities: [AdminActivity] = []
    @Published var users: [User] = []
    @Published var merchantUsers: [BusinessUser] = []
    @Published var isLoading = false
    @Published var error: Error?
    
    private let repository: AdminRepositoryProtocol
    private let logger = Logger.shared
    
    init(repository: AdminRepositoryProtocol = AdminRepository()) {
        self.repository = repository
    }
    
    // MARK: - Dashboard Data
    func loadDashboardData() async {
        isLoading = true
        error = nil
        
        do {
            async let statsTask = repository.getAdminStats()
            async let activitiesTask = repository.getRecentActivity(limit: 10)
            
            let (stats, activities) = try await (statsTask, activitiesTask)
            
            self.stats = stats
            self.recentActivities = activities
            
            logger.info("Admin dashboard data loaded successfully", category: .general)
            
        } catch {
            self.error = error
            logger.error("Failed to load admin dashboard data", error: error, category: .general)
        }
        
        isLoading = false
    }
    
    func refreshDashboardData() async {
        await loadDashboardData()
    }
    
    // MARK: - User Management
    func loadUsers(page: Int = 1, role: String? = nil, status: String? = nil, search: String? = nil) async {
        isLoading = true
        error = nil
        
        do {
            let response = try await repository.getAllUsers(
                page: page,
                limit: 50,
                role: role,
                status: status,
                search: search
            )
            
            if page == 1 {
                users = response.users
            } else {
                users.append(contentsOf: response.users)
            }
            
            logger.info("Loaded \(response.users.count) users", category: .general)
            
        } catch {
            self.error = error
            logger.error("Failed to load users", error: error, category: .general)
        }
        
        isLoading = false
    }
    
    func updateUserStatus(userId: String, isActive: Bool) async -> Bool {
        do {
            let success = try await repository.updateUserStatus(userId: userId, isActive: isActive)
            
            if success {
                // Update local user status
                if let index = users.firstIndex(where: { $0.id == userId }) {
                    // TODO: Implement user update functionality
                    // users[index] = updatedUser
                }
                
                logger.info("User status updated successfully", category: .general)
            }
            
            return success
            
        } catch {
            self.error = error
            logger.error("Failed to update user status", error: error, category: .general)
            return false
        }
    }
    
    // MARK: - Business Management
    func loadBusinessUsers(status: String? = nil) async {
        isLoading = true
        error = nil
        
        do {
            merchantUsers = try await repository.getBusinessUsers(status: status)
            logger.info("Loaded \(merchantUsers.count) merchant users", category: .general)
            
        } catch {
            self.error = error
            logger.error("Failed to load merchant users", error: error, category: .general)
        }
        
        isLoading = false
    }
    
    func approveBusinessUser(userId: String) async -> Bool {
        do {
            let success = try await repository.approveBusinessUser(userId: userId)
            
            if success {
                // Update local merchant user status
                if let index = merchantUsers.firstIndex(where: { $0.id == userId }) {
                    merchantUsers[index].businessInfo?.status = "ACTIVE"
                }
                
                logger.info("Business user approved successfully", category: .general)
            }
            
            return success
            
        } catch {
            self.error = error
            logger.error("Failed to approve merchant user", error: error, category: .general)
            return false
        }
    }
    
    func suspendBusinessUser(userId: String) async -> Bool {
        do {
            let success = try await repository.suspendBusinessUser(userId: userId)
            
            if success {
                // Update local merchant user status
                if let index = merchantUsers.firstIndex(where: { $0.id == userId }) {
                    merchantUsers[index].businessInfo?.status = "SUSPENDED"
                }
                
                logger.info("Business user suspended successfully", category: .general)
            }
            
            return success
            
        } catch {
            self.error = error
            logger.error("Failed to suspend merchant user", error: error, category: .general)
            return false
        }
    }
}
