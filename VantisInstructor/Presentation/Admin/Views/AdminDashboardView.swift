//
//  AdminDashboardView.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 21/7/25.
//

import SwiftUI

struct AdminDashboardView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var adminViewModel = AdminViewModel()
    @State private var refreshing = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: AppConstants.UI.sectionSpacing) {
                    // Header
                    headerSection
                    
                    // Platform Stats Cards
                    platformStatsSection
                    
                    // Quick Actions
                    quickActionsSection
                    
                    // Recent Activity
                    recentActivitySection
                    
                    // System Status
                    systemStatusSection
                }
                .padding(.horizontal, AppConstants.UI.screenPadding)
                .padding(.top, 10)
            }
            .navigationBarHidden(true)
            .background(AppConstants.Colors.background.ignoresSafeArea())
            .refreshable {
                await refreshData()
            }
        }
        .task {
            await loadInitialData()
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("Admin Dashboard")
                    .font(.system(size: 32, weight: .bold, design: .rounded))
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                if let user = authViewModel.currentUser {
                    Text("Welcome back, \(user.displayName)")
                        .font(.subheadline)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
            }
            
            Spacer()
            
            // Admin Badge
            HStack(spacing: 6) {
                Image(systemName: "crown.fill")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.orange)
                
                Text("ADMIN")
                    .font(.system(size: 12, weight: .bold))
                    .foregroundColor(.orange)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                Capsule()
                    .fill(.orange.opacity(0.15))
            )
        }
    }
    
    // MARK: - Platform Stats Section
    private var platformStatsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Platform Overview")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Spacer()
                
                if adminViewModel.isLoading {
                    ProgressView()
                        .scaleEffect(0.8)
                }
            }
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 2), spacing: 12) {
                AdminStatCard(
                    title: "Total Users",
                    value: "\(adminViewModel.stats?.totalUsers ?? 0)",
                    icon: "person.2.fill",
                    color: .blue,
                    trend: adminViewModel.stats?.userGrowth
                )
                
                AdminStatCard(
                    title: "Active Businesss",
                    value: "\(adminViewModel.stats?.activeBusinesss ?? 0)",
                    icon: "building.2.fill",
                    color: .green,
                    trend: adminViewModel.stats?.merchantGrowth
                )
                
                AdminStatCard(
                    title: "Total Transactions",
                    value: "\(adminViewModel.stats?.totalTransactions ?? 0)",
                    icon: "creditcard.fill",
                    color: .purple,
                    trend: adminViewModel.stats?.transactionGrowth
                )
                
                AdminStatCard(
                    title: "Platform Revenue",
                    value: formatCurrency(adminViewModel.stats?.totalRevenue ?? 0),
                    icon: "banknote.fill",
                    color: .orange,
                    trend: adminViewModel.stats?.revenueGrowth
                )
            }
        }
    }
    
    // MARK: - Quick Actions Section
    private var quickActionsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Quick Actions")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Spacer()
            }
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 2), spacing: 12) {
                AdminActionCard(
                    title: "Manage Users",
                    subtitle: "View and manage user accounts",
                    icon: "person.2.circle.fill",
                    color: .blue
                ) {
                    // Navigate to users management
                }
                
                AdminActionCard(
                    title: "Approve Businesss",
                    subtitle: "Review pending merchant applications",
                    icon: "checkmark.circle.fill",
                    color: .green
                ) {
                    // Navigate to merchant approval
                }
                

                
                AdminActionCard(
                    title: "System Settings",
                    subtitle: "Configure platform settings",
                    icon: "gearshape.fill",
                    color: .gray
                ) {
                    // Navigate to system settings
                }
            }
        }
    }
    
    // MARK: - Recent Activity Section
    private var recentActivitySection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Recent Activity")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Spacer()
                
                Button("View All") {
                    // Navigate to full activity log
                }
                .font(.subheadline)
                .foregroundColor(AppConstants.Colors.primary)
            }
            
            VStack(spacing: 8) {
                ForEach(adminViewModel.recentActivities.prefix(5), id: \.id) { activity in
                    AdminActivityRow(activity: activity)
                }
                
                if adminViewModel.recentActivities.isEmpty && !adminViewModel.isLoading {
                    Text("No recent activity")
                        .font(.subheadline)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .padding(.vertical, 20)
                }
            }
        }
    }
    
    // MARK: - System Status Section
    private var systemStatusSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("System Status")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Spacer()
                
                // Status indicator
                HStack(spacing: 6) {
                    Circle()
                        .fill(.green)
                        .frame(width: 8, height: 8)
                    
                    Text("All Systems Operational")
                        .font(.caption)
                        .foregroundColor(.green)
                }
            }
            
            VStack(spacing: 12) {
                SystemStatusRow(
                    service: "API Server",
                    status: .operational,
                    responseTime: "45ms"
                )
                
                SystemStatusRow(
                    service: "Database",
                    status: .operational,
                    responseTime: "12ms"
                )
                
                SystemStatusRow(
                    service: "Blockchain Network",
                    status: .operational,
                    responseTime: "2.3s"
                )
                
                SystemStatusRow(
                    service: "Payment Gateway",
                    status: .operational,
                    responseTime: "156ms"
                )
            }
        }
    }
    
    // MARK: - Helper Methods
    private func loadInitialData() async {
        await adminViewModel.loadDashboardData()
    }
    
    private func refreshData() async {
        refreshing = true
        await adminViewModel.refreshDashboardData()
        refreshing = false
    }
    
    private func formatCurrency(_ amount: Double) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "VND"
        formatter.maximumFractionDigits = 0
        return formatter.string(from: NSNumber(value: amount)) ?? "₫0"
    }
}

// MARK: - Preview
#Preview {
    AdminDashboardView()
        .environmentObject(AuthViewModel())
}
