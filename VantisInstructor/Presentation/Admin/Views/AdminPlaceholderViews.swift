//
//  AdminPlaceholderViews.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 21/7/25.
//

import SwiftUI

// MARK: - Admin Users View
struct AdminUsersView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var adminViewModel = AdminViewModel()
    @State private var searchText = ""
    @State private var selectedRole: String?
    @State private var selectedStatus: String?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Search and filters
                searchAndFiltersSection
                
                // Users list
                usersListSection
            }
            .navigationTitle("Manage Users")
            .navigationBarTitleDisplayMode(.large)
            .background(AppConstants.Colors.background.ignoresSafeArea())
        }
        .task {
            await adminViewModel.loadUsers()
        }
    }
    
    private var searchAndFiltersSection: some View {
        VStack(spacing: 12) {
            // Search bar
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(AppConstants.Colors.textSecondary)
                
                TextField("Search users...", text: $searchText)
                    .textFieldStyle(PlainTextFieldStyle())
                
                if !searchText.isEmpty {
                    Button(action: {
                        searchText = ""
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color.white)
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
            
            // Filter buttons
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    AdminFilterButton(title: "All", isSelected: selectedRole == nil) {
                        selectedRole = nil
                    }

                    AdminFilterButton(title: "Users", isSelected: selectedRole == "USER") {
                        selectedRole = "USER"
                    }

                    AdminFilterButton(title: "Businesss", isSelected: selectedRole == "MERCHANT") {
                        selectedRole = "MERCHANT"
                    }

                    AdminFilterButton(title: "Admins", isSelected: selectedRole == "ADMIN") {
                        selectedRole = "ADMIN"
                    }
                }
                .padding(.horizontal, 16)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(AppConstants.Colors.background)
    }
    
    private var usersListSection: some View {
        ScrollView {
            LazyVStack(spacing: 8) {
                ForEach(filteredUsers, id: \.id) { user in
                    AdminUserRow(user: user) { userId, isActive in
                        Task {
                            await adminViewModel.updateUserStatus(userId: userId, isActive: isActive)
                        }
                    }
                }
                
                if adminViewModel.isLoading {
                    ProgressView()
                        .padding()
                }
                
                if filteredUsers.isEmpty && !adminViewModel.isLoading {
                    Text("No users found")
                        .font(.subheadline)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .padding(.vertical, 40)
                }
            }
            .padding(.horizontal, 16)
        }
    }
    
    private var filteredUsers: [User] {
        var users = adminViewModel.users
        
        if let role = selectedRole {
            users = users.filter { $0.role.rawValue == role }
        }
        
        if !searchText.isEmpty {
            users = users.filter { user in
                user.displayName.localizedCaseInsensitiveContains(searchText) ||
                user.email.localizedCaseInsensitiveContains(searchText)
            }
        }
        
        return users
    }
}

// MARK: - Admin Businesss View
struct AdminBusinesssView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var adminViewModel = AdminViewModel()
    @State private var selectedStatus: String?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Status filters
                statusFiltersSection
                
                // Businesss list
                merchantsListSection
            }
            .navigationTitle("Manage Businesss")
            .navigationBarTitleDisplayMode(.large)
            .background(AppConstants.Colors.background.ignoresSafeArea())
        }
        .task {
            await adminViewModel.loadBusinessUsers()
        }
    }
    
    private var statusFiltersSection: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 8) {
                AdminFilterButton(title: "All", isSelected: selectedStatus == nil) {
                    selectedStatus = nil
                }

                AdminFilterButton(title: "Pending", isSelected: selectedStatus == "PENDING") {
                    selectedStatus = "PENDING"
                }

                AdminFilterButton(title: "Active", isSelected: selectedStatus == "ACTIVE") {
                    selectedStatus = "ACTIVE"
                }

                AdminFilterButton(title: "Suspended", isSelected: selectedStatus == "SUSPENDED") {
                    selectedStatus = "SUSPENDED"
                }
            }
            .padding(.horizontal, 16)
        }
        .padding(.vertical, 12)
        .background(AppConstants.Colors.background)
    }
    
    private var merchantsListSection: some View {
        ScrollView {
            LazyVStack(spacing: 8) {
                ForEach(filteredBusinesss, id: \.id) { merchant in
                    AdminBusinessRow(
                        merchantUser: merchant,
                        onApprove: { userId in
                            Task {
                                await adminViewModel.approveBusinessUser(userId: userId)
                            }
                        },
                        onSuspend: { userId in
                            Task {
                                await adminViewModel.suspendBusinessUser(userId: userId)
                            }
                        }
                    )
                }
                
                if adminViewModel.isLoading {
                    ProgressView()
                        .padding()
                }
                
                if filteredBusinesss.isEmpty && !adminViewModel.isLoading {
                    Text("No merchants found")
                        .font(.subheadline)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .padding(.vertical, 40)
                }
            }
            .padding(.horizontal, 16)
        }
    }
    
    private var filteredBusinesss: [BusinessUser] {
        var merchants = adminViewModel.merchantUsers
        
        if let status = selectedStatus {
            merchants = merchants.filter { $0.businessInfo?.status == status }
        }
        
        return merchants
    }
}



// MARK: - Supporting Components
struct AdminFilterButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(isSelected ? .white : AppConstants.Colors.textPrimary)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    Capsule()
                        .fill(isSelected ? AppConstants.Colors.primary : Color.white)
                )
                .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
        }
        .buttonStyle(PlainButtonStyle())
    }
}






