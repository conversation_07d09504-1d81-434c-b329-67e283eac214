//
//  InstructorHomeViewModel.swift
//  mobile-app-template
//
//  Created by Instructor App on 23/7/25.
//

import Foundation
import SwiftUI

@MainActor
class InstructorHomeViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // Dashboard data
    @Published var todayClasses: [Class] = []
    @Published var upcomingAssignments: [Assignment] = []
    @Published var recentAttendance: [ClassAttendanceSummary] = []
    @Published var quickStats: InstructorStats = InstructorStats()
    
    // Managers
    @Published var classManager = ClassManager()
    @Published var assignmentManager = AssignmentManager()
    @Published var studentManager = StudentManager()
    @Published var attendanceManager = AttendanceManager()
    
    // UI State
    @Published var selectedDate = Date()
    @Published var showingClassDetails = false
    @Published var showingAssignmentDetails = false
    @Published var showingAttendanceView = false
    @Published var showingCreateAssignment = false
    @Published var showingAnnouncements = false
    
    // MARK: - Initialization
    init() {
        loadInitialData()
    }
    
    // MARK: - Data Loading
    func loadInitialData() {
        Task {
            await loadDashboardData()
        }
    }
    
    func loadDashboardData() async {
        isLoading = true
        errorMessage = nil
        
        do {
            // Load today's classes
            await loadTodayClasses()
            
            // Load upcoming assignments
            await loadUpcomingAssignments()
            
            // Load recent attendance
            await loadRecentAttendance()
            
            // Load quick stats
            await loadQuickStats()
            
        } catch {
            errorMessage = "Không thể tải dữ liệu: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    private func loadTodayClasses() async {
        // Simulate API call
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        
        // Filter classes for today
        let calendar = Calendar.current
        let today = Date()
        
        todayClasses = Class.mockClasses.filter { classItem in
            calendar.isDate(classItem.scheduledDate, inSameDayAs: today)
        }.sorted { $0.startTime < $1.startTime }
    }
    
    private func loadUpcomingAssignments() async {
        // Simulate API call
        try? await Task.sleep(nanoseconds: 300_000_000) // 0.3 seconds
        
        // Get assignments due in next 7 days
        let nextWeek = Date().addingTimeInterval(7 * 24 * 3600)
        
        upcomingAssignments = Assignment.mockAssignments.filter { assignment in
            assignment.dueDate <= nextWeek && assignment.status == .active
        }.sorted { $0.dueDate < $1.dueDate }
    }
    
    private func loadRecentAttendance() async {
        // Simulate API call
        try? await Task.sleep(nanoseconds: 400_000_000) // 0.4 seconds
        
        // Mock recent attendance data
        recentAttendance = [
            ClassAttendanceSummary(
                id: "attendance_summary_1",
                classId: "class_1",
                courseId: "course_1",
                courseName: "Lập trình iOS với Swift",
                courseCode: "CS301",
                classDate: Date().addingTimeInterval(-24 * 3600), // Yesterday
                classTitle: "SwiftUI Basics",
                totalStudents: 35,
                presentStudents: 32,
                absentStudents: 2,
                lateStudents: 1,
                excusedStudents: 0,
                attendanceRate: 91.4,
                attendanceRecords: Attendance.mockAttendanceRecords,
                createdAt: Date().addingTimeInterval(-24 * 3600),
                updatedAt: Date()
            )
        ]
    }
    
    private func loadQuickStats() async {
        // Simulate API call
        try? await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        // Calculate stats from mock data
        quickStats = InstructorStats(
            totalStudents: 105,
            totalCourses: 3,
            averageAttendanceRate: 89.5,
            pendingAssignments: 12,
            gradedAssignments: 24,
            totalAssignments: 36,
            classesToday: todayClasses.count,
            upcomingDeadlines: upcomingAssignments.count
        )
    }
    
    // MARK: - Actions
    func refreshData() async {
        await loadDashboardData()
    }
    
    func markAttendance(for classItem: Class) {
        print("🎯 InstructorHomeViewModel: markAttendance called")
        showingAttendanceView = true
        // Navigate to attendance marking view
    }

    func createAssignment() {
        print("🎯 InstructorHomeViewModel: createAssignment called")
        showingCreateAssignment = true
    }

    func sendAnnouncement() {
        print("🎯 InstructorHomeViewModel: sendAnnouncement called")
        showingAnnouncements = true
    }
    
    func viewClassDetails(_ classItem: Class) {
        showingClassDetails = true
        // Set selected class and navigate
        print("Viewing class details: \(classItem.title)")
    }

    func viewAssignmentDetails(_ assignment: Assignment) {
        showingAssignmentDetails = true
        // Set selected assignment and navigate
        print("Viewing assignment details: \(assignment.title)")
    }
    
    func gradeAssignment(_ assignment: Assignment) {
        // Navigate to grading view
        print("Grading assignment: \(assignment.title)")
    }
    
    func viewStudents() {
        // Navigate to students list
        print("Viewing students")
    }
    
    func viewSchedule() {
        // Navigate to schedule view
        print("Viewing schedule")
    }
}

// MARK: - Instructor Stats
struct InstructorStats: Codable {
    var totalStudents: Int = 0
    var totalCourses: Int = 0
    var averageAttendanceRate: Double = 0.0
    var pendingAssignments: Int = 0
    var gradedAssignments: Int = 0
    var totalAssignments: Int = 0
    var classesToday: Int = 0
    var upcomingDeadlines: Int = 0
    
    // Computed properties
    var gradingProgress: Double {
        guard totalAssignments > 0 else { return 0 }
        return Double(gradedAssignments) / Double(totalAssignments) * 100
    }
    
    var attendanceGrade: String {
        switch averageAttendanceRate {
        case 95...100: return "Xuất sắc"
        case 85..<95: return "Tốt"
        case 75..<85: return "Khá"
        case 60..<75: return "Trung bình"
        default: return "Cần cải thiện"
        }
    }
    
    var attendanceColor: Color {
        switch averageAttendanceRate {
        case 95...100: return .green
        case 85..<95: return .blue
        case 75..<85: return .orange
        case 60..<75: return .yellow
        default: return .red
        }
    }
}

// MARK: - Dashboard Section
enum DashboardSection: String, CaseIterable {
    case todayClasses = "Lớp học hôm nay"
    case upcomingAssignments = "Bài tập sắp hết hạn"
    case recentAttendance = "Điểm danh gần đây"
    case quickStats = "Thống kê nhanh"
    
    var icon: String {
        switch self {
        case .todayClasses: return "calendar.badge.clock"
        case .upcomingAssignments: return "doc.text.badge.plus"
        case .recentAttendance: return "person.2.badge.plus"
        case .quickStats: return "chart.bar.fill"
        }
    }
}

// MARK: - Extensions
extension InstructorHomeViewModel {
    var hasClassesToday: Bool {
        !todayClasses.isEmpty
    }
    
    var hasUpcomingAssignments: Bool {
        !upcomingAssignments.isEmpty
    }
    
    var nextClass: Class? {
        todayClasses.first { $0.isUpcoming }
    }
    
    var urgentAssignments: [Assignment] {
        upcomingAssignments.filter { $0.isDueSoon || $0.isOverdue }
    }
    
    var todayAttendanceRate: Double {
        guard !todayClasses.isEmpty else { return 0 }
        
        let completedClasses = todayClasses.filter { $0.status == .completed }
        guard !completedClasses.isEmpty else { return 0 }
        
        let totalRate = completedClasses.reduce(0.0) { sum, classItem in
            sum + classItem.attendanceRate
        }
        
        return totalRate / Double(completedClasses.count)
    }
}
