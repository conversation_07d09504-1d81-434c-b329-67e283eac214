import SwiftUI

struct BusinesssView: View {
    @StateObject private var viewModel = BusinessesViewModel()
    @State private var showingBusinessDetail = false
    @State private var selectedBusiness: Business?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header with stats
                headerSection
                
                // Search and filters
                searchAndFiltersSection
                
                // Businesss list
                merchantsListSection
            }
            .navigationTitle("Find Businesss")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        viewModel.showingSearch = true
                    }) {
                        Image(systemName: "magnifyingglass")
                            .foregroundColor(AppConstants.Colors.primary)
                    }
                }
            }
            .refreshable {
                viewModel.refreshBusinesss()
            }
        }
        .sheet(isPresented: $viewModel.showingSearch) {
            BusinessSearchView(viewModel: viewModel)
        }
        .sheet(item: $selectedBusiness) { merchant in
            BusinessDetailView(merchant: merchant)
        }
        .onAppear {
            if viewModel.businesses.isEmpty {
                viewModel.refreshBusinesss()
            }
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("\(viewModel.activeBusinesssCount)")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(AppConstants.Colors.primary)
                    
                    Text("Active Partners")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text("\(viewModel.filteredBusinesss.count)")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(AppConstants.Colors.secondary)
                    
                    Text("Found")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )
        }
        .padding(.horizontal, 16)
        .padding(.top, 8)
    }
    
    // MARK: - Search and Filters Section
    private var searchAndFiltersSection: some View {
        VStack(spacing: 12) {
            // Quick search bar
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                
                TextField("Search merchants...", text: $viewModel.searchText)
                    .textFieldStyle(PlainTextFieldStyle())
                    .onChange(of: viewModel.searchText) { _ in
                        viewModel.searchBusinesss()
                    }
                
                if !viewModel.searchText.isEmpty {
                    Button(action: {
                        viewModel.searchText = ""
                        viewModel.searchBusinesss()
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(Color(.systemGray6))
            )
            .padding(.horizontal, 16)
            
            // Category filters
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    // All categories button
                    CategoryFilterChip(
                        title: "All",
                        count: viewModel.businesses.count,
                        isSelected: viewModel.selectedCategory == nil
                    ) {
                        viewModel.filterByCategory(nil)
                    }
                    
                    // Individual category buttons
                    ForEach(viewModel.categories, id: \.self) { category in
                        CategoryFilterChip(
                            title: category.displayName,
                            count: viewModel.categoryCounts[category] ?? 0,
                            isSelected: viewModel.selectedCategory == category
                        ) {
                            viewModel.filterByCategory(category)
                        }
                    }
                }
                .padding(.horizontal, 16)
            }
        }
        .padding(.vertical, 8)
    }
    
    // MARK: - Businesss List Section
    private var merchantsListSection: some View {
        Group {
            if viewModel.isLoading {
                VStack {
                    Spacer()
                    ProgressView("Loading merchants...")
                        .progressViewStyle(CircularProgressViewStyle())
                    Spacer()
                }
            } else if viewModel.filteredBusinesss.isEmpty {
                VStack(spacing: 16) {
                    Spacer()
                    
                    Image(systemName: "building.2")
                        .font(.system(size: 60))
                        .foregroundColor(.secondary)
                    
                    Text("No merchants found")
                        .font(.title2)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    Text("Try adjusting your search or filters")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                    
                    Button(action: {
                        viewModel.clearFilters()
                    }) {
                        Text("Clear Filters")
                            .font(.headline)
                            .foregroundColor(.white)
                            .padding(.horizontal, 24)
                            .padding(.vertical, 12)
                            .background(AppConstants.Colors.primary)
                            .cornerRadius(8)
                    }
                    
                    Spacer()
                }
                .padding(.horizontal, 32)
            } else {
                ScrollView {
                    LazyVGrid(columns: [
                        GridItem(.flexible()),
                        GridItem(.flexible())
                    ], spacing: 16) {
                        ForEach(viewModel.filteredBusinesss) { merchant in
                            BusinessCard(merchant: merchant) {
                                selectedBusiness = merchant
                                showingBusinessDetail = true
                            }
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.bottom, 20)
                }
            }
        }
    }
}

// MARK: - Category Filter Chip
struct CategoryFilterChip: View {
    let title: String
    let count: Int
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text("(\(count))")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(isSelected ? AppConstants.Colors.primary : Color(.systemGray6))
            )
            .foregroundColor(isSelected ? .white : .primary)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Business Card
struct BusinessCard: View {
    let merchant: Business
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 12) {
                // Logo and status
                HStack {
                    AsyncImage(url: URL(string: merchant.logoUrl ?? "")) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                    } placeholder: {
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color(.systemGray5))
                            .overlay(
                                Image(systemName: "building.2")
                                    .foregroundColor(.secondary)
                            )
                    }
                    .frame(width: 50, height: 50)
                    .cornerRadius(8)
                    
                    Spacer()
                    
                    // Status indicator
                    Circle()
                        .fill(merchant.isActive ? Color.green : Color.orange)
                        .frame(width: 8, height: 8)
                }
                
                // Business info
                VStack(alignment: .leading, spacing: 4) {
                    Text(merchant.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .lineLimit(2)
                        .multilineTextAlignment(.leading)
                    
                    Text(merchant.category.displayName)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(
                            RoundedRectangle(cornerRadius: 4)
                                .fill(Color(.systemGray6))
                        )
                    
                    if let description = merchant.description {
                        Text(description)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .lineLimit(2)
                            .multilineTextAlignment(.leading)
                    }
                }
                
                Spacer()
                
                // Commission rate
                HStack {
                    Text("Commission:")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text(merchant.displayBusinessInfo)
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(AppConstants.Colors.primary)
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Preview
struct BusinesssView_Previews: PreviewProvider {
    static var previews: some View {
        BusinesssView()
    }
}
