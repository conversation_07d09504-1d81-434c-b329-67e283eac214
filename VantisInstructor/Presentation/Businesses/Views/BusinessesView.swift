//
//  BusinessesView.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 22/7/25.
//

import SwiftUI

struct BusinessesView: View {
    @StateObject private var viewModel = BusinessesViewModel()
    @State private var selectedBusiness: Business?
    
    var body: some View {
        NavigationView {
            VStack {
                Image(systemName: "building.2")
                    .font(.system(size: 60))
                    .foregroundColor(AppConstants.Colors.primary)
                    .padding()
                
                Text("Businesses")
                    .font(.title)
                    .fontWeight(.bold)
                
                Text("This is a placeholder for the businesses view. Customize this view to show your business listings.")
                    .multilineTextAlignment(.center)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .padding()
                
                Spacer()
            }
            .navigationTitle("Businesses")
        }
    }
}

#Preview {
    BusinessesView()
}
