//
//  BusinessesViewModel.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 22/7/25.
//

import Foundation
import SwiftUI

@MainActor
class BusinessesViewModel: ObservableObject {
    @Published var businesses: [Business] = []
    @Published var filteredBusinesss: [Business] = []
    @Published var selectedCategory: BusinessCategory?
    @Published var searchText: String = ""
    @Published var isLoading: Bool = false
    @Published var errorMessage: String? = nil
    @Published var showingSearch: Bool = false
    
    // Categories for filtering
    let categories: [BusinessCategory] = BusinessCategory.allCases
    
    init() {
        loadDemoData()
    }
    
    // MARK: - Demo Data
    private func loadDemoData() {
        businesses = [
            Business(
                id: "1",
                name: "Highlands Coffee",
                businessId: "HC001",
                email: "<EMAIL>",
                phone: "+84 28 3825 6789",
                category: .restaurant,
                status: .active,
                description: "Premium coffee chain with multiple locations",
                website: "https://highlands.vn",
                logoUrl: "https://example.com/highlands-logo.png",
                address: nil,
                operatingHours: nil,
                socialMedia: nil,
                totalTransactions: 1250,
                metadata: nil,
                notes: "Premium partner with excellent service",
                lastTransactionAt: Date().addingTimeInterval(-3600),
                onboardedAt: Date().addingTimeInterval(-86400 * 30),
                createdAt: Date().addingTimeInterval(-86400 * 30),
                updatedAt: Date().addingTimeInterval(-3600)
            ),
            Business(
                id: "2",
                name: "The Coffee House",
                businessId: "TCH001",
                email: "<EMAIL>",
                phone: "+84 28 3456 7890",
                category: .restaurant,
                status: .active,
                description: "Local coffee chain with unique Vietnamese flavors",
                website: "https://thecoffeehouse.vn",
                logoUrl: "https://example.com/tch-logo.png",
                address: nil,
                operatingHours: nil,
                socialMedia: nil,
                totalTransactions: 890,
                metadata: nil,
                notes: "Growing partner with good potential",
                lastTransactionAt: Date().addingTimeInterval(-7200),
                onboardedAt: Date().addingTimeInterval(-86400 * 60),
                createdAt: Date().addingTimeInterval(-86400 * 60),
                updatedAt: Date().addingTimeInterval(-7200)
            )
        ]
        filteredBusinesss = businesses
    }
    
    // MARK: - Search and Filter
    func searchBusinesss() {
        filteredBusinesss = businesses
        
        if !searchText.isEmpty {
            var filtered = businesses
            filtered = filtered.filter { business in
                business.name.localizedCaseInsensitiveContains(searchText) ||
                business.description?.localizedCaseInsensitiveContains(searchText) == true
            }
            filteredBusinesss = filtered
        }
        
        if let category = selectedCategory {
            filteredBusinesss = filteredBusinesss.filter { $0.category == category }
        }
    }
    
    func clearFilters() {
        selectedCategory = nil
        searchText = ""
        filteredBusinesss = businesses
    }
    
    // MARK: - Business Actions
    func getBusiness(by id: String) -> Business? {
        return businesses.first { $0.id == id }
    }

    func refreshBusinesss() {
        loadDemoData()
    }

    var activeBusinesssCount: Int {
        return businesses.filter { $0.status == .active }.count
    }

    func filterByCategory(_ category: BusinessCategory?) {
        selectedCategory = category
        searchBusinesss()
    }

    var categoryCounts: [BusinessCategory: Int] {
        var counts: [BusinessCategory: Int] = [:]
        for category in BusinessCategory.allCases {
            counts[category] = businesses.filter { $0.category == category }.count
        }
        return counts
    }
}

// MARK: - Business Extensions
extension Business {
    var displayBusinessInfo: String {
        return description ?? "No description available"
    }
}
