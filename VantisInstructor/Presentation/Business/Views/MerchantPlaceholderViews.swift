//
//  MerchantPlaceholderViews.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 21/7/25.
//

import SwiftUI

// MARK: - Merchant Payments View
struct MerchantPaymentsView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var merchantViewModel = MerchantViewModel()
    @State private var selectedType: MerchantPayment.PaymentType?
    @State private var refreshing = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Payment type filters
                paymentTypeFiltersSection
                
                // Payments list
                paymentsListSection
            }
            .navigationTitle("Payment History")
            .navigationBarTitleDisplayMode(.large)
            .background(AppConstants.Colors.background.ignoresSafeArea())
            .refreshable {
                await refreshData()
            }
        }
        .task {
            await merchantViewModel.loadPaymentHistory(refresh: true)
        }
    }
    
    private var paymentTypeFiltersSection: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 8) {
                PaymentTypeFilterButton(title: "All", isSelected: selectedType == nil) {
                    selectedType = nil
                    Task {
                        await merchantViewModel.loadPaymentHistory(type: nil, refresh: true)
                    }
                }
                
                PaymentTypeFilterButton(title: "Commission", isSelected: selectedType == .commissionPayment) {
                    selectedType = .commissionPayment
                    Task {
                        await merchantViewModel.loadPaymentHistory(type: .commissionPayment, refresh: true)
                    }
                }
                
                PaymentTypeFilterButton(title: "Settlement", isSelected: selectedType == .settlementPayment) {
                    selectedType = .settlementPayment
                    Task {
                        await merchantViewModel.loadPaymentHistory(type: .settlementPayment, refresh: true)
                    }
                }
                
                PaymentTypeFilterButton(title: "Top Up", isSelected: selectedType == .walletTopup) {
                    selectedType = .walletTopup
                    Task {
                        await merchantViewModel.loadPaymentHistory(type: .walletTopup, refresh: true)
                    }
                }
                
                PaymentTypeFilterButton(title: "Withdrawal", isSelected: selectedType == .walletWithdrawal) {
                    selectedType = .walletWithdrawal
                    Task {
                        await merchantViewModel.loadPaymentHistory(type: .walletWithdrawal, refresh: true)
                    }
                }
            }
            .padding(.horizontal, 16)
        }
        .padding(.vertical, 12)
        .background(AppConstants.Colors.background)
    }
    
    private var paymentsListSection: some View {
        ScrollView {
            LazyVStack(spacing: 8) {
                ForEach(merchantViewModel.allPayments, id: \.id) { payment in
                    MerchantPaymentRow(payment: payment)
                }
                
                if merchantViewModel.isLoading {
                    ProgressView()
                        .padding()
                }
                
                if merchantViewModel.allPayments.isEmpty && !merchantViewModel.isLoading {
                    Text("No payments found")
                        .font(.subheadline)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .padding(.vertical, 40)
                }
            }
            .padding(.horizontal, 16)
        }
    }
    
    private func refreshData() async {
        refreshing = true
        await merchantViewModel.loadPaymentHistory(type: selectedType, refresh: true)
        refreshing = false
    }
}

// MARK: - Merchant Analytics View
struct MerchantAnalyticsView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var merchantViewModel = MerchantViewModel()
    @State private var selectedPeriod: AnalyticsPeriod = .month
    
    enum AnalyticsPeriod: String, CaseIterable {
        case week = "Week"
        case month = "Month"
        case quarter = "Quarter"
        case year = "Year"
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: AppConstants.UI.sectionSpacing) {
                    // Period selector
                    periodSelectorSection
                    
                    // Revenue analytics
                    revenueAnalyticsSection
                    
                    // Transaction analytics
                    transactionAnalyticsSection
                    
                    // Performance metrics
                    performanceMetricsSection
                    
                    // Payment breakdown
                    paymentBreakdownSection
                }
                .padding(.horizontal, AppConstants.UI.screenPadding)
                .padding(.top, 10)
            }
            .navigationTitle("Analytics")
            .navigationBarTitleDisplayMode(.large)
            .background(AppConstants.Colors.background.ignoresSafeArea())
        }
        .task {
            await merchantViewModel.loadDashboardData()
        }
    }
    
    private var periodSelectorSection: some View {
        HStack {
            Text("Period")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Spacer()
            
            Picker("Period", selection: $selectedPeriod) {
                ForEach(AnalyticsPeriod.allCases, id: \.self) { period in
                    Text(period.rawValue).tag(period)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
            .frame(width: 200)
        }
    }
    
    private var revenueAnalyticsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Revenue Analytics")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Spacer()
            }
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 2), spacing: 12) {
                MerchantAnalyticsCard(
                    title: "Total Revenue",
                    value: formatCurrency(merchantViewModel.calculateMonthlyRevenue()),
                    subtitle: "This \(selectedPeriod.rawValue.lowercased())",
                    icon: "banknote.fill",
                    color: .green,
                    chartData: generateMockChartData()
                )
                
                MerchantAnalyticsCard(
                    title: "Daily Average",
                    value: formatCurrency(merchantViewModel.calculateDailyAverage()),
                    subtitle: "Average per day",
                    icon: "chart.line.uptrend.xyaxis",
                    color: .blue,
                    chartData: generateMockChartData()
                )
            }
        }
    }
    
    private var transactionAnalyticsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Transaction Analytics")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Spacer()
            }
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 2), spacing: 12) {
                MerchantAnalyticsCard(
                    title: "Total Transactions",
                    value: "\(merchantViewModel.walletInfo?.totalTransactions ?? 0)",
                    subtitle: "All time",
                    icon: "creditcard.fill",
                    color: .purple,
                    chartData: generateMockChartData()
                )
                
                MerchantAnalyticsCard(
                    title: "Success Rate",
                    value: "98.5%",
                    subtitle: "Transaction success",
                    icon: "checkmark.circle.fill",
                    color: .green,
                    chartData: nil
                )
            }
        }
    }
    
    private var performanceMetricsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Performance Metrics")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Spacer()
            }
            
            VStack(spacing: 12) {
                PerformanceMetricRow(
                    title: "Average Transaction Value",
                    value: formatCurrency(calculateAverageTransactionValue()),
                    icon: "chart.bar.fill",
                    color: .blue
                )
                
                PerformanceMetricRow(
                    title: "Peak Hour",
                    value: "2:00 PM - 3:00 PM",
                    icon: "clock.fill",
                    color: .orange
                )
                
                PerformanceMetricRow(
                    title: "Customer Retention",
                    value: "87%",
                    icon: "person.2.fill",
                    color: .green
                )
            }
        }
    }
    
    private var paymentBreakdownSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Payment Breakdown")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Spacer()
            }
            
            VStack(spacing: 8) {
                ForEach(merchantViewModel.getTopPaymentTypes(), id: \.0) { paymentType, amount in
                    PaymentTypeBreakdownRow(
                        type: paymentType,
                        amount: amount,
                        percentage: calculatePercentage(amount: amount)
                    )
                }
                
                if merchantViewModel.getTopPaymentTypes().isEmpty {
                    Text("No payment data available")
                        .font(.subheadline)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .padding(.vertical, 20)
                }
            }
        }
    }
    
    // MARK: - Helper Methods
    private func formatCurrency(_ amount: Double) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "VND"
        formatter.maximumFractionDigits = 0
        return formatter.string(from: NSNumber(value: amount)) ?? "₫0"
    }
    
    private func generateMockChartData() -> [Double] {
        return (0..<7).map { _ in Double.random(in: 0...100) }
    }
    
    private func calculateAverageTransactionValue() -> Double {
        guard let totalTransactions = merchantViewModel.walletInfo?.totalTransactions,
              totalTransactions > 0,
              let totalRevenue = merchantViewModel.walletInfo?.totalCommissionsPaid else {
            return 0
        }
        
        return totalRevenue / Double(totalTransactions)
    }
    
    private func calculatePercentage(amount: Double) -> Double {
        let totalAmount = merchantViewModel.getTopPaymentTypes().reduce(0) { $0 + $1.1 }
        return totalAmount > 0 ? (amount / totalAmount) * 100 : 0
    }
}

// MARK: - Supporting Components
struct PaymentTypeFilterButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(isSelected ? .white : AppConstants.Colors.textPrimary)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    Capsule()
                        .fill(isSelected ? AppConstants.Colors.primary : Color.white)
                )
                .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct PaymentTypeBreakdownRow: View {
    let type: MerchantPayment.PaymentType
    let amount: Double
    let percentage: Double
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: type.iconName)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(type.iconColor)
                .frame(width: 32, height: 32)
                .background(type.iconColor.opacity(0.1))
                .cornerRadius(8)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(type.displayName)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Text("\(percentage, specifier: "%.1f")% of total")
                    .font(.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            
            Spacer()
            
            Text(formatCurrency(amount))
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.03), radius: 4, x: 0, y: 1)
    }
    
    private func formatCurrency(_ amount: Double) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "VND"
        formatter.maximumFractionDigits = 0
        return formatter.string(from: NSNumber(value: amount)) ?? "₫0"
    }
}
