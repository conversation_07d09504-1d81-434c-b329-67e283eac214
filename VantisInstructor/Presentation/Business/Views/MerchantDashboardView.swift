//
//  MerchantDashboardView.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 21/7/25.
//

import SwiftUI

struct MerchantDashboardView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var merchantViewModel = MerchantViewModel()
    @State private var refreshing = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: AppConstants.UI.sectionSpacing) {
                    // Header
                    headerSection
                    
                    // Business Stats Cards
                    businessStatsSection
                    
                    // Wallet Overview
                    walletOverviewSection
                    
                    // Recent Transactions
                    recentTransactionsSection
                    
                    // Quick Actions
                    quickActionsSection
                    
                    // Performance Metrics
                    performanceMetricsSection
                }
                .padding(.horizontal, AppConstants.UI.screenPadding)
                .padding(.top, 10)
            }
            .navigationBarHidden(true)
            .background(AppConstants.Colors.background.ignoresSafeArea())
            .refreshable {
                await refreshData()
            }
        }
        .task {
            await loadInitialData()
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("Merchant Dashboard")
                    .font(.system(size: 32, weight: .bold, design: .rounded))
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                if let user = authViewModel.currentUser, let businessName = user.businessName {
                    Text(businessName)
                        .font(.subheadline)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                } else if let user = authViewModel.currentUser {
                    Text("Welcome back, \(user.displayName)")
                        .font(.subheadline)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
            }
            
            Spacer()
            
            // Merchant Badge
            HStack(spacing: 6) {
                Image(systemName: "building.2.fill")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.blue)
                
                Text("MERCHANT")
                    .font(.system(size: 12, weight: .bold))
                    .foregroundColor(.blue)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                Capsule()
                    .fill(.blue.opacity(0.15))
            )
        }
    }
    
    // MARK: - Business Stats Section
    private var businessStatsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Business Overview")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Spacer()
                
                if merchantViewModel.isLoading {
                    ProgressView()
                        .scaleEffect(0.8)
                }
            }
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 2), spacing: 12) {
                MerchantDashboardStatCard(
                    title: "Total Revenue",
                    value: formatCurrency(merchantViewModel.walletInfo?.totalCommissionsPaid ?? 0),
                    icon: "banknote.fill",
                    color: .green,
                    trend: nil
                )

                MerchantDashboardStatCard(
                    title: "Transactions",
                    value: "\(merchantViewModel.walletInfo?.totalTransactions ?? 0)",
                    icon: "creditcard.fill",
                    color: .blue,
                    trend: nil
                )

                MerchantDashboardStatCard(
                    title: "Token Balance",
                    value: String(format: "%.2f LXT", merchantViewModel.walletInfo?.tokenBalance ?? 0),
                    icon: "bitcoinsign.circle.fill",
                    color: .orange,
                    trend: nil
                )

                MerchantDashboardStatCard(
                    title: "Commission Rate",
                    value: "N/A",
                    icon: "percent",
                    color: .purple,
                    trend: nil
                )
            }
        }
    }
    
    // MARK: - Wallet Overview Section
    private var walletOverviewSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Wallet Overview")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Spacer()
                
                Button("View Details") {
                    // Navigate to wallet details
                }
                .font(.subheadline)
                .foregroundColor(AppConstants.Colors.primary)
            }
            
            VStack(spacing: 12) {
                
                // Balance breakdown
                VStack(spacing: 8) {
                    HStack {
                        Text("Available Balance")
                            .font(.subheadline)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                        
                        Spacer()
                        
                        Text(String(format: "%.2f LXT", merchantViewModel.walletInfo?.tokenBalance ?? 0))
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                    }
                    
                    HStack {
                        Text("Total Earned")
                            .font(.subheadline)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                        
                        Spacer()
                        
                        Text(formatCurrency(merchantViewModel.walletInfo?.totalCommissionsPaid ?? 0))
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.green)
                    }
                }
                .padding(12)
                .background(Color.white)
                .cornerRadius(12)
                .shadow(color: Color.black.opacity(0.03), radius: 4, x: 0, y: 1)
            }
        }
    }
    
    // MARK: - Recent Transactions Section
    private var recentTransactionsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Recent Transactions")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Spacer()
                
                Button("View All") {
                    // Navigate to full transaction history
                }
                .font(.subheadline)
                .foregroundColor(AppConstants.Colors.primary)
            }
            
            VStack(spacing: 8) {
                ForEach(merchantViewModel.recentPayments.prefix(5), id: \.id) { payment in
                    MerchantPaymentRow(payment: payment)
                }
                
                if merchantViewModel.recentPayments.isEmpty && !merchantViewModel.isLoading {
                    Text("No recent transactions")
                        .font(.subheadline)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .padding(.vertical, 20)
                }
            }
        }
    }
    
    // MARK: - Quick Actions Section
    private var quickActionsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Quick Actions")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Spacer()
            }
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 2), spacing: 12) {
                MerchantActionCard(
                    title: "Generate QR",
                    subtitle: "Create payment QR code",
                    icon: "qrcode",
                    color: .blue
                ) {
                    // Generate QR code action
                }
                
                MerchantActionCard(
                    title: "View Analytics",
                    subtitle: "Business performance insights",
                    icon: "chart.bar.fill",
                    color: .purple
                ) {
                    // Navigate to analytics
                }
                
                MerchantActionCard(
                    title: "Payment History",
                    subtitle: "View all transactions",
                    icon: "list.bullet.rectangle",
                    color: .green
                ) {
                    // Navigate to payment history
                }
                
                MerchantActionCard(
                    title: "Settings",
                    subtitle: "Business configuration",
                    icon: "gearshape.fill",
                    color: .gray
                ) {
                    // Navigate to merchant settings
                }
            }
        }
    }
    
    // MARK: - Performance Metrics Section
    private var performanceMetricsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Performance Metrics")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Spacer()
            }
            
            VStack(spacing: 12) {
                PerformanceMetricRow(
                    title: "Average Transaction",
                    value: formatCurrency(calculateAverageTransaction()),
                    icon: "chart.line.uptrend.xyaxis",
                    color: .blue
                )
                
                PerformanceMetricRow(
                    title: "Monthly Growth",
                    value: "+12.5%",
                    icon: "arrow.up.right",
                    color: .green
                )
                
                PerformanceMetricRow(
                    title: "Customer Retention",
                    value: "87%",
                    icon: "person.2.fill",
                    color: .orange
                )
            }
        }
    }
    
    // MARK: - Helper Methods
    private func loadInitialData() async {
        await merchantViewModel.loadDashboardData()
    }
    
    private func refreshData() async {
        refreshing = true
        await merchantViewModel.refreshDashboardData()
        refreshing = false
    }
    
    private func formatCurrency(_ amount: Double) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "VND"
        formatter.maximumFractionDigits = 0
        return formatter.string(from: NSNumber(value: amount)) ?? "₫0"
    }
    
    private func calculateAverageTransaction() -> Double {
        guard let totalTransactions = merchantViewModel.walletInfo?.totalTransactions,
              totalTransactions > 0,
              let totalCommissions = merchantViewModel.walletInfo?.totalCommissionsPaid else {
            return 0
        }
        
        return totalCommissions / Double(totalTransactions)
    }
}

// MARK: - Preview
#Preview {
    MerchantDashboardView()
        .environmentObject(AuthViewModel())
}
