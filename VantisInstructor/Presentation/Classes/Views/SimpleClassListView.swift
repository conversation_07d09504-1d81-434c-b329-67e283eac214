//
//  SimpleClassListView.swift
//  mobile-app-template
//
//  Created by Inst<PERSON><PERSON> App on 28/7/25.
//

import SwiftUI

struct SimpleClassListView: View {
    @StateObject private var viewModel = SimpleClassListViewModel()
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerSection
                
                // Content
                if viewModel.isLoading {
                    loadingView
                } else if let errorMessage = viewModel.errorMessage {
                    errorView(errorMessage)
                } else {
                    lessonsList
                }
            }
            .navigationTitle("")
            .navigationBarHidden(true)
            .task {
                await viewModel.loadLessons()
            }
            .refreshable {
                await viewModel.refreshLessons()
            }
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 16) {
            // Top bar with back button and title
            HStack {
                Button(action: { dismiss() }) {
                    Image(systemName: "chevron.left")
                        .font(.title2)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                }
                
                Spacer()
                
                Text("Lớp học")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Spacer()

                // Refresh button
                Button(action: {
                    Task {
                        await viewModel.refreshLessons()
                    }
                }) {
                    Image(systemName: "arrow.clockwise")
                        .font(.title3)
                        .foregroundColor(.primary)
                }
                .disabled(viewModel.isLoading)
            }
            .padding(.horizontal, 20)
            .padding(.top, 8)
            
            // Stats summary
            if !viewModel.lessons.isEmpty {
                statsSection
            }
        }
        .padding(.bottom, 16)
        .background(Color(.systemBackground))
        .shadow(color: Color.black.opacity(0.05), radius: 1, x: 0, y: 1)
    }
    
    // MARK: - Stats Section
    private var statsSection: some View {
        HStack(spacing: 20) {
            LessonStatItem(
                title: "Tổng số",
                value: "\(viewModel.lessons.count)",
                color: .blue
            )

            LessonStatItem(
                title: "Hôm nay",
                value: "\(viewModel.todayLessonsCount)",
                color: .green
            )

            LessonStatItem(
                title: "Sắp tới",
                value: "\(viewModel.upcomingLessonsCount)",
                color: .orange
            )
        }
        .padding(.horizontal, 20)
    }
    
    // MARK: - Lessons List
    private var lessonsList: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(viewModel.lessons) { lesson in
                    SimpleLessonCard(lesson: lesson)
                        .padding(.horizontal, 16)
                }
            }
            .padding(.vertical, 8)
        }
        .background(Color(.systemGroupedBackground))
    }
    
    // MARK: - Loading View
    private var loadingView: some View {
        VStack(spacing: 16) {
            Spacer()
            
            ProgressView()
                .scaleEffect(1.2)
            
            Text("Đang tải danh sách lớp học...")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemGroupedBackground))
    }
    
    // MARK: - Error View
    private func errorView(_ message: String) -> some View {
        VStack(spacing: 20) {
            Spacer()
            
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 48))
                .foregroundColor(.orange)
            
            Text("Không thể tải dữ liệu")
                .font(.headline)
                .fontWeight(.semibold)
            
            Text(message)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)
            
            Button("Thử lại") {
                Task {
                    await viewModel.loadLessons()
                }
            }
            .buttonStyle(.borderedProminent)
            .controlSize(.large)
            
            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemGroupedBackground))
    }
}

// MARK: - Lesson Stat Item Component
struct LessonStatItem: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

// MARK: - View Model
@MainActor
class SimpleClassListViewModel: ObservableObject {
    @Published var lessons: [SimpleLesson] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    // Always use real API
    
    private var lessonService: SimpleLessonServiceProtocol
    
    init() {
        self.lessonService = SimpleLessonServiceFactory.create(useMockData: false)
        print("🚀 SimpleClassListViewModel: Using REAL API (no mock data)")
    }
    
    // MARK: - Public Methods
    func loadLessons() async {
        isLoading = true
        errorMessage = nil
        
        do {
            let response = try await lessonService.fetchLessons()
            lessons = response.data
            print("✅ Loaded \(lessons.count) lessons successfully")
        } catch {
            errorMessage = handleError(error)
            print("❌ Failed to load lessons: \(error)")
        }
        
        isLoading = false
    }
    
    func refreshLessons() async {
        await loadLessons()
    }
    
    // Removed toggleMockData - always use real API
    
    // MARK: - Computed Properties
    var todayLessonsCount: Int {
        lessons.filter { $0.isToday }.count
    }
    
    var upcomingLessonsCount: Int {
        lessons.filter { $0.isUpcoming }.count
    }
    
    // MARK: - Private Methods
    private func handleError(_ error: Error) -> String {
        if let urlError = error as? URLError {
            switch urlError.code {
            case .notConnectedToInternet:
                return "Không có kết nối internet"
            case .timedOut:
                return "Kết nối bị timeout"
            case .userAuthenticationRequired:
                return "Lỗi xác thực. Vui lòng đăng nhập lại"
            case .badServerResponse:
                return "Lỗi server. Vui lòng thử lại sau"
            default:
                return "Lỗi kết nối: \(urlError.localizedDescription)"
            }
        } else {
            return "Lỗi không xác định: \(error.localizedDescription)"
        }
    }
}

// MARK: - Preview
#Preview {
    SimpleClassListView()
}
