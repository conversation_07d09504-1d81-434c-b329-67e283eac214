//
//  ClassDetailView.swift
//  mobile-app-template
//
//  Created by Instructor App on 23/7/25.
//

import SwiftUI

struct ClassDetailView: View {
    let classItem: Class
    @StateObject private var classManager = ClassManager()
    @StateObject private var attendanceManager = AttendanceManager()
    @Environment(\.dismiss) private var dismiss
    
    @State private var showingAttendanceView = false
    @State private var showingMaterialsView = false
    @State private var showingEditView = false
    @State private var showingActionSheet = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header section
                    headerSection
                    
                    // Quick actions
                    quickActionsSection
                    
                    // Class info
                    classInfoSection
                    
                    // Attendance summary
                    attendanceSummarySection
                    
                    // Materials section
                    materialsSection
                    
                    // Notes section
                    notesSection
                }
                .padding(.horizontal)
                .padding(.bottom, 32)
            }
            .navigationTitle("Chi tiết lớp học")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Đóng") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    But<PERSON>("Thêm") {
                        showingActionSheet = true
                    }
                }
            }
        }
        .sheet(isPresented: $showingAttendanceView) {
            // AttendanceView(classItem: classItem)
            Text("Attendance View - Coming Soon")
        }
        .sheet(isPresented: $showingMaterialsView) {
            // MaterialsView(classItem: classItem)
            Text("Materials View - Coming Soon")
        }
        .sheet(isPresented: $showingEditView) {
            // EditClassView(classItem: classItem)
            Text("Edit Class View - Coming Soon")
        }
        .confirmationDialog("Thao tác", isPresented: $showingActionSheet) {
            Button("Điểm danh") {
                showingAttendanceView = true
            }
            
            Button("Tài liệu") {
                showingMaterialsView = true
            }
            
            Button("Chỉnh sửa") {
                showingEditView = true
            }
            
            if classItem.status == .scheduled {
                Button("Bắt đầu lớp") {
                    startClass()
                }
            }
            
            if classItem.status == .inProgress {
                Button("Kết thúc lớp") {
                    endClass()
                }
            }
            
            Button("Hủy", role: .cancel) { }
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 16) {
            // Course and class info
            VStack(spacing: 8) {
                Text(classItem.courseCode)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(AppConstants.Colors.primary)
                
                Text(classItem.title)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                    .multilineTextAlignment(.center)
                
                Text(classItem.courseName)
                    .font(.subheadline)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            
            // Status and type
            HStack(spacing: 12) {
                ClassStatusIndicator(status: classItem.status)
                ClassTypeBadge(type: classItem.type)
            }
        }
        .padding(.vertical, 20)
        .frame(maxWidth: .infinity)
        .background(
            LinearGradient(
                gradient: Gradient(colors: [
                    AppConstants.Colors.primary.opacity(0.1),
                    AppConstants.Colors.primary.opacity(0.05)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .cornerRadius(16)
    }
    
    // MARK: - Quick Actions Section
    private var quickActionsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Thao tác nhanh")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                ClassQuickActionButton(
                    title: "Điểm danh",
                    icon: "person.2.badge.plus",
                    color: .green
                ) {
                    showingAttendanceView = true
                }

                ClassQuickActionButton(
                    title: "Tài liệu",
                    icon: "doc.text",
                    color: .blue
                ) {
                    showingMaterialsView = true
                }

                if classItem.status == .scheduled {
                    ClassQuickActionButton(
                        title: "Bắt đầu",
                        icon: "play.circle",
                        color: .orange
                    ) {
                        startClass()
                    }
                }

                if classItem.status == .inProgress {
                    ClassQuickActionButton(
                        title: "Kết thúc",
                        icon: "stop.circle",
                        color: .red
                    ) {
                        endClass()
                    }
                }
            }
        }
    }
    
    // MARK: - Class Info Section
    private var classInfoSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Thông tin lớp học")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            VStack(spacing: 12) {
                ClassInfoRow(
                    icon: "calendar",
                    title: "Ngày học",
                    value: formatDate(classItem.scheduledDate)
                )

                ClassInfoRow(
                    icon: "clock",
                    title: "Thời gian",
                    value: classItem.formattedTime
                )

                ClassInfoRow(
                    icon: "clock.arrow.circlepath",
                    title: "Thời lượng",
                    value: "\(classItem.duration) phút"
                )

                if let location = classItem.location {
                    ClassInfoRow(
                        icon: "location",
                        title: "Địa điểm",
                        value: location.displayName
                    )
                }

                ClassInfoRow(
                    icon: "person.3",
                    title: "Học sinh",
                    value: "\(classItem.totalStudents) người"
                )

                if let description = classItem.description {
                    ClassInfoRow(
                        icon: "text.alignleft",
                        title: "Mô tả",
                        value: description
                    )
                }
            }
        }
        .padding(16)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(12)
    }
    
    // MARK: - Attendance Summary Section
    private var attendanceSummarySection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Tóm tắt điểm danh")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Spacer()
                
                if classItem.status == .completed {
                    Text("\(String(format: "%.1f", classItem.attendanceRate))%")
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(attendanceColor)
                }
            }
            
            if classItem.status == .completed {
                HStack(spacing: 16) {
                    AttendanceStatItem(
                        title: "Có mặt",
                        count: classItem.attendedStudents,
                        color: .green
                    )
                    
                    AttendanceStatItem(
                        title: "Vắng mặt",
                        count: classItem.absentStudents,
                        color: .red
                    )
                    
                    AttendanceStatItem(
                        title: "Đi muộn",
                        count: classItem.lateStudents,
                        color: .orange
                    )
                }
            } else {
                Text("Chưa có dữ liệu điểm danh")
                    .font(.subheadline)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding(.vertical, 20)
            }
        }
        .padding(16)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(12)
    }
    
    // MARK: - Materials Section
    private var materialsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Tài liệu")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Spacer()
                
                Button("Xem tất cả") {
                    showingMaterialsView = true
                }
                .font(.subheadline)
                .foregroundColor(AppConstants.Colors.primary)
            }
            
            if let materials = classItem.materials, !materials.isEmpty {
                VStack(spacing: 8) {
                    ForEach(materials.prefix(3)) { material in
                        MaterialRow(material: material)
                    }
                    
                    if materials.count > 3 {
                        Text("và \(materials.count - 3) tài liệu khác...")
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                            .frame(maxWidth: .infinity, alignment: .center)
                            .padding(.top, 8)
                    }
                }
            } else {
                Text("Chưa có tài liệu nào")
                    .font(.subheadline)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding(.vertical, 20)
            }
        }
        .padding(16)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(12)
    }
    
    // MARK: - Notes Section
    private var notesSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Ghi chú")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            if let notes = classItem.notes, !notes.isEmpty {
                Text(notes)
                    .font(.body)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                    .padding(12)
                    .background(AppConstants.Colors.surface)
                    .cornerRadius(8)
            } else {
                Text("Chưa có ghi chú")
                    .font(.subheadline)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding(.vertical, 20)
            }
        }
        .padding(16)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(12)
    }
    
    // MARK: - Computed Properties
    private var attendanceColor: Color {
        switch classItem.attendanceRate {
        case 90...100: return .green
        case 75..<90: return .orange
        default: return .red
        }
    }
    
    // MARK: - Helper Methods
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "EEEE, dd/MM/yyyy"
        return formatter.string(from: date)
    }
    
    private func startClass() {
        Task {
            await classManager.startClass(classItem.id)
        }
    }
    
    private func endClass() {
        Task {
            await classManager.endClass(classItem.id)
        }
    }
}

// MARK: - Supporting Components

// MARK: - Class Quick Action Button
struct ClassQuickActionButton: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)

                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(AppConstants.Colors.textPrimary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(AppConstants.Colors.cardBackground)
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(color.opacity(0.2), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Class Info Row
struct ClassInfoRow: View {
    let icon: String
    let title: String
    let value: String

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.body)
                .foregroundColor(AppConstants.Colors.primary)
                .frame(width: 20)

            Text(title)
                .font(.body)
                .foregroundColor(AppConstants.Colors.textSecondary)

            Spacer()

            Text(value)
                .font(.body)
                .fontWeight(.medium)
                .foregroundColor(AppConstants.Colors.textPrimary)
                .multilineTextAlignment(.trailing)
        }
    }
}

// MARK: - Attendance Stat Item
struct AttendanceStatItem: View {
    let title: String
    let count: Int
    let color: Color

    var body: some View {
        VStack(spacing: 4) {
            Text("\(count)")
                .font(AppConstants.Typography.title2)
                .foregroundColor(color)

            Text(title)
                .font(AppConstants.Typography.caption)
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 12)
        .background(color.opacity(0.1))
        .cornerRadius(8)
    }
}

// MARK: - Material Row
struct MaterialRow: View {
    let material: ClassMaterial

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: material.type.icon)
                .font(.body)
                .foregroundColor(AppConstants.Colors.primary)
                .frame(width: 20)

            VStack(alignment: .leading, spacing: 2) {
                Text(material.title)
                    .font(.body)
                    .fontWeight(.medium)
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Text(material.type.displayName)
                    .font(.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }

            Spacer()

            if material.isRequired {
                Text("Bắt buộc")
                    .font(.caption2)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(AppConstants.Colors.error)
                    .cornerRadius(4)
            }
        }
        .padding(.vertical, 8)
    }
}

// MARK: - Preview
struct ClassDetailView_Previews: PreviewProvider {
    static var previews: some View {
        ClassDetailView(classItem: Class.mockClasses[0])
    }
}
