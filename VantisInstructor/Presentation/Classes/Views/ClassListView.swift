//
//  ClassListView.swift
//  mobile-app-template
//
//  Created by Instructor App on 23/7/25.
//

import SwiftUI

struct ClassListView: View {
    var body: some View {
        LessonsAPIDemo()
    }
}

// MARK: - Legacy ClassListView (kept for reference)
struct LegacyClassListView: View {
    @StateObject private var classManager = ClassManager()
    @StateObject private var tokenManager = TokenManager.shared
    @Environment(\.dismiss) private var dismiss
    @Environment(\.presentationMode) var presentationMode
    @State private var searchText = ""
    @State private var selectedDate = Date()
    @State private var showingFilters = false
    @State private var showingCalendarView = false
    @State private var selectedClass: Class?
    @State private var showingClassDetail = false
    @State private var selectedFilter: ClassFilter = .all
    @State private var selectedStatus: ClassStatus? = nil
    @State private var selectedCourse: String? = nil
    @State private var showingDebugLogin = false
    @State private var showingAPITest = false
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 0) {
                // Modern Header Section
                modernHeaderSection

                // Debug Login Section (only in debug mode)
                // #if DEBUG
                // debugLoginSection
                // #endif

                // View Toggle Section
                viewToggleSection

                // Search and filters
                searchAndFilterSection

                // Content
                if showingCalendarView {
                    calendarContentView
                } else {
                    listContentView
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 16)
        }
        .navigationBarHidden(true)
        .background(
            Color.white
                .ignoresSafeArea()
        )
        .sheet(isPresented: $showingFilters) {
            ClassFilterSheet(
                selectedFilter: $selectedFilter,
                selectedStatus: $selectedStatus,
                selectedCourse: $selectedCourse
            )
        }
        .sheet(isPresented: $showingClassDetail) {
            if let selectedClass = selectedClass {
                ClassDetailView(classItem: selectedClass)
            }
        }
        // .sheet(isPresented: $showingDebugLogin) {
        //     LoginView()
        //         .environmentObject(AuthViewModel())
        // }
        .sheet(isPresented: $showingAPITest) {
            SimpleAPITestView()
        }
        .task {
            await classManager.fetchClasses()
        }
        .onChange(of: tokenManager.isAuthenticated) { _ in
            // Refresh data when authentication status changes
            Task {
                await classManager.fetchClasses()
            }
        }
    }

    // MARK: - Debug Login Section
    #if DEBUG
    private var debugLoginSection: some View {
        VStack(spacing: 8) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("🔐 Debug Mode")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.orange)

                    Text(tokenManager.isAuthenticated ? "✅ Đã đăng nhập" : "❌ Chưa đăng nhập")
                        .font(.caption2)
                        .foregroundColor(tokenManager.isAuthenticated ? .green : .red)
                }

                Spacer()

                HStack(spacing: 8) {
                    Button("Login") {
                        showingDebugLogin = true
                    }
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.orange.opacity(0.2))
                    .foregroundColor(.orange)
                    .cornerRadius(4)

                    Button("API Test") {
                        showingAPITest = true
                    }
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.blue.opacity(0.2))
                    .foregroundColor(.blue)
                    .cornerRadius(4)
                }
                .font(.caption)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color.orange.opacity(0.2))
                .foregroundColor(.orange)
                .cornerRadius(6)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(Color.orange.opacity(0.1))
            .cornerRadius(8)
        }
        .padding(.horizontal, 20)
    }
    #endif

    // MARK: - Modern Header Section
    private var modernHeaderSection: some View {
        VStack(alignment: .center, spacing: 4) {
            Text("Lớp học")
                .font(.beVietnamPro(.bold, size: 24))
                .foregroundColor(AppConstants.Colors.textPrimary)

            Text("Quản lý lịch học và lớp học")
                .font(.beVietnamPro(.medium, size: 14))
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .padding(.bottom, 24)
    }

    // MARK: - View Toggle Section
    private var viewToggleSection: some View {
        HStack {
            Picker("View", selection: $showingCalendarView) {
                Label("Danh sách", systemImage: "list.bullet")
                    .tag(false)
                Label("Lịch", systemImage: "calendar")
                    .tag(true)
            }
            .pickerStyle(SegmentedPickerStyle())

            Spacer()
        }
        .padding(.bottom, 16)
    }
    
    // MARK: - Search and Filter Section
    private var searchAndFilterSection: some View {
        HStack(spacing: 12) {
            // Search bar
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(AppConstants.Colors.textSecondary)
                
                TextField("Tìm kiếm lớp học...", text: $searchText)
                    .textFieldStyle(PlainTextFieldStyle())
                
                if !searchText.isEmpty {
                    Button(action: { searchText = "" }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(AppConstants.Colors.surface)
            .cornerRadius(10)
            
            // Filter button
            Button(action: { showingFilters = true }) {
                HStack(spacing: 4) {
                    Image(systemName: "line.3.horizontal.decrease.circle")
                    
                    if hasActiveFilters {
                        Circle()
                            .fill(AppConstants.Colors.primary)
                            .frame(width: 8, height: 8)
                    }
                }
                .foregroundColor(AppConstants.Colors.primary)
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(AppConstants.Colors.surface)
                .cornerRadius(10)
            }
        }
        .padding(.horizontal)
        .padding(.bottom, 16)
    }
    
    // MARK: - Calendar Content View
    private var calendarContentView: some View {
        LazyVStack(spacing: 16) {
            // Date picker
            DatePicker(
                "Chọn ngày",
                selection: $selectedDate,
                displayedComponents: .date
            )
            .datePickerStyle(GraphicalDatePickerStyle())

            // Classes for selected date
            let classesForDate = classManager.getClassesForDate(selectedDate)

            if classesForDate.isEmpty {
                EmptyClassesView(date: selectedDate)
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(classesForDate) { classItem in
                        ClassCard(classItem: classItem) {
                            selectedClass = classItem
                            showingClassDetail = true
                        }
                    }
                }
            }

            Spacer(minLength: 80)
        }
    }
    
    // MARK: - List Content View
    private var listContentView: some View {
        LazyVStack(spacing: 16) {
            // Quick stats
            quickStatsSection

            // Classes list
            if classManager.isLoading {
                loadingView
            } else if filteredClasses.isEmpty {
                emptyStateView
            } else {
                classesListSection
            }

            Spacer(minLength: 80)
        }
        .refreshable {
            await classManager.refreshData()
        }
    }
    
    // MARK: - Quick Stats Section
    private var quickStatsSection: some View {
        HStack(spacing: 12) {
            ClassStatCard(
                title: "Hôm nay",
                value: "\(getTodayLessonsCount())",
                subtitle: "lớp học",
                color: .blue
            )

            ClassStatCard(
                title: "Tuần này",
                value: "\(getUpcomingLessonsCount())",
                subtitle: "sắp tới",
                color: .green
            )

            ClassStatCard(
                title: "Điểm danh",
                value: String(format: "%.1f%%", getAverageAttendanceRate()),
                subtitle: "trung bình",
                color: .orange
            )
        }
    }

    // MARK: - Stats Helper Methods
    private func getTodayLessonsCount() -> Int {
        if !classManager.apiLessons.isEmpty {
            return classManager.getTodayAPILessons().count
        } else {
            return classManager.todayClasses.count
        }
    }

    private func getUpcomingLessonsCount() -> Int {
        if !classManager.apiLessons.isEmpty {
            return classManager.getUpcomingAPILessons().count
        } else {
            return classManager.upcomingClasses.count
        }
    }

    private func getAverageAttendanceRate() -> Double {
        if !classManager.apiLessons.isEmpty {
            let completedLessons = classManager.apiLessons.filter { $0.state == .completed }
            guard !completedLessons.isEmpty else { return 0.0 }

            let totalRate = completedLessons.reduce(0.0) { sum, lesson in
                sum + lesson.attendanceRate
            }

            return totalRate / Double(completedLessons.count)
        } else {
            return classManager.todayAttendanceRate
        }
    }
    
    // MARK: - Classes List Section
    private var classesListSection: some View {
        LazyVStack(spacing: 12) {
            // Show API lessons if available, otherwise show regular classes
            if !classManager.apiLessons.isEmpty {
                apiLessonsSection
            } else {
                regularClassesSection
            }
        }
    }

    // MARK: - API Lessons Section
    private var apiLessonsSection: some View {
        LazyVStack(spacing: 12) {
            ForEach(groupedAPILessons.keys.sorted(), id: \.self) { date in
                Section {
                    ForEach(groupedAPILessons[date] ?? []) { lesson in
                        APILessonCard(lesson: lesson) {
                            // Convert to Class for detail view compatibility
                            selectedClass = lesson.toClass()
                            showingClassDetail = true
                        }
                    }
                } header: {
                    HStack {
                        Text(formatSectionDate(date))
                            .font(AppConstants.Typography.headline)
                            .foregroundColor(AppConstants.Colors.textPrimary)

                        Spacer()

                        Text("\(groupedAPILessons[date]?.count ?? 0) lớp")
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    .padding(.top, 8)
                }
            }
        }
    }

    // MARK: - Regular Classes Section
    private var regularClassesSection: some View {
        LazyVStack(spacing: 12) {
            ForEach(groupedClasses.keys.sorted(), id: \.self) { date in
                Section {
                    ForEach(groupedClasses[date] ?? []) { classItem in
                        ClassCard(classItem: classItem) {
                            selectedClass = classItem
                            showingClassDetail = true
                        }
                    }
                } header: {
                    HStack {
                        Text(formatSectionDate(date))
                            .font(AppConstants.Typography.headline)
                            .foregroundColor(AppConstants.Colors.textPrimary)

                        Spacer()

                        Text("\(groupedClasses[date]?.count ?? 0) lớp")
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    .padding(.top, 8)
                }
            }
        }
    }
    
    // MARK: - Loading View
    private var loadingView: some View {
        VStack(spacing: 12) {
            ForEach(0..<5, id: \.self) { _ in
                RoundedRectangle(cornerRadius: 12)
                    .fill(AppConstants.Colors.surface)
                    .frame(height: 120)
                    .redacted(reason: .placeholder)
            }
        }
    }
    
    // MARK: - Empty State View
    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "calendar.badge.clock")
                .font(.system(size: 60))
                .foregroundColor(AppConstants.Colors.textSecondary)
            
            Text("Không có lớp học nào")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Text("Thêm lớp học mới hoặc thay đổi bộ lọc để xem kết quả")
                .font(.subheadline)
                .foregroundColor(AppConstants.Colors.textSecondary)
                .multilineTextAlignment(.center)
            
            Button("Thêm lớp học") {
                // TODO: Add new class
                print("Add new class")
            }
            .buttonStyle(.borderedProminent)
        }
        .padding(40)
    }
    
    // MARK: - Computed Properties
    private var filteredClasses: [Class] {
        var classes = classManager.classes
        
        // Apply search filter
        if !searchText.isEmpty {
            classes = classes.filter { classItem in
                classItem.title.localizedCaseInsensitiveContains(searchText) ||
                classItem.courseName.localizedCaseInsensitiveContains(searchText) ||
                classItem.courseCode.localizedCaseInsensitiveContains(searchText)
            }
        }
        
        // Apply status filter
        if let selectedStatus = selectedStatus {
            classes = classes.filter { $0.status == selectedStatus }
        }
        
        // Apply course filter
        if let selectedCourse = selectedCourse {
            classes = classes.filter { $0.courseId == selectedCourse }
        }
        
        // Apply date filter
        switch selectedFilter {
        case .today:
            classes = classes.filter { Calendar.current.isDateInToday($0.scheduledDate) }
        case .thisWeek:
            let startOfWeek = Calendar.current.dateInterval(of: .weekOfYear, for: Date())?.start ?? Date()
            let endOfWeek = Calendar.current.date(byAdding: .day, value: 7, to: startOfWeek) ?? Date()
            classes = classes.filter { $0.scheduledDate >= startOfWeek && $0.scheduledDate < endOfWeek }
        case .upcoming:
            classes = classes.filter { $0.isUpcoming }
        case .completed:
            classes = classes.filter { $0.status == .completed }
        case .all:
            break
        }
        
        return classes.sorted { $0.scheduledDate < $1.scheduledDate }
    }
    
    private var groupedClasses: [Date: [Class]] {
        Dictionary(grouping: filteredClasses) { classItem in
            Calendar.current.startOfDay(for: classItem.scheduledDate)
        }
    }

    private var filteredAPILessons: [APILesson] {
        var lessons = classManager.apiLessons

        // Apply search filter
        if !searchText.isEmpty {
            lessons = lessons.filter { lesson in
                lesson.name.localizedCaseInsensitiveContains(searchText) ||
                lesson.className.localizedCaseInsensitiveContains(searchText) ||
                lesson.classCode.localizedCaseInsensitiveContains(searchText)
            }
        }

        // Apply status filter (convert ClassStatus to APILessonState)
        if let selectedStatus = selectedStatus {
            let apiState: APILessonState
            switch selectedStatus {
            case .scheduled: apiState = .scheduled
            case .inProgress: apiState = .inProgress
            case .completed: apiState = .completed
            case .cancelled: apiState = .cancelled
            case .postponed: apiState = .scheduled // Map postponed to scheduled
            }
            lessons = lessons.filter { $0.state == apiState }
        }

        // Apply general filter
        switch selectedFilter {
        case .all:
            break
        case .today:
            lessons = lessons.filter { $0.isToday }
        case .thisWeek:
            let startOfWeek = Calendar.current.dateInterval(of: .weekOfYear, for: Date())?.start ?? Date()
            let endOfWeek = Calendar.current.date(byAdding: .day, value: 7, to: startOfWeek) ?? Date()
            lessons = lessons.filter {
                $0.startDate >= startOfWeek && $0.startDate < endOfWeek
            }
        case .upcoming:
            lessons = lessons.filter { $0.isUpcoming }
        case .completed:
            lessons = lessons.filter { $0.state == .completed }
        }

        return lessons.sorted { $0.startDate < $1.startDate }
    }

    private var groupedAPILessons: [Date: [APILesson]] {
        Dictionary(grouping: filteredAPILessons) { lesson in
            Calendar.current.startOfDay(for: lesson.startDate)
        }
    }

    private var hasActiveFilters: Bool {
        selectedFilter != .all || selectedStatus != nil || selectedCourse != nil
    }
    
    // MARK: - Helper Methods
    private func formatSectionDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        
        if Calendar.current.isDateInToday(date) {
            return "Hôm nay"
        } else if Calendar.current.isDateInTomorrow(date) {
            return "Ngày mai"
        } else if Calendar.current.isDate(date, equalTo: Date().addingTimeInterval(-24*3600), toGranularity: .day) {
            return "Hôm qua"
        } else {
            formatter.dateFormat = "EEEE, dd/MM/yyyy"
            return formatter.string(from: date)
        }
    }
}

// MARK: - Class Filter Enum
enum ClassFilter: String, CaseIterable {
    case all = "Tất cả"
    case today = "Hôm nay"
    case thisWeek = "Tuần này"
    case upcoming = "Sắp tới"
    case completed = "Đã hoàn thành"
}

// MARK: - Preview
struct ClassListView_Previews: PreviewProvider {
    static var previews: some View {
        ClassListView()
    }
}
