//
//  ClassFilterComponents.swift
//  mobile-app-template
//
//  Created by Instructor App on 23/7/25.
//

import SwiftUI

// MARK: - Advanced Class Filter View
struct AdvancedClassFilterView: View {
    @Binding var selectedDateRange: DateRange?
    @Binding var selectedCourses: Set<String>
    @Binding var selectedStatuses: Set<ClassStatus>
    @Binding var selectedTypes: Set<ClassType>
    @Binding var selectedInstructor: String?
    @Environment(\.dismiss) private var dismiss
    
    // Mock data - in real app, this would come from services
    private let availableCourses = [
        Course.mockCourses[0]
    ]
    
    private let availableInstructors = [
        ("instructor_1", "Nguyễn Văn A"),
        ("instructor_2", "Trần Thị B"),
        ("instructor_3", "Lê Văn C")
    ]
    
    var body: some View {
        NavigationView {
            Form {
                // Date range section
                dateRangeSection
                
                // Course selection
                courseSelectionSection
                
                // Status selection
                statusSelectionSection
                
                // Type selection
                typeSelectionSection
                
                // Instructor selection
                instructorSelectionSection
                
                // Reset section
                resetSection
            }
            .navigationTitle("Bộ lọc nâng cao")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Hủy") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Áp dụng") {
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
    }
    
    // MARK: - Date Range Section
    private var dateRangeSection: some View {
        Section("Khoảng thời gian") {
            ForEach(DateRange.allCases, id: \.self) { range in
                HStack {
                    Text(range.displayName)
                    Spacer()
                    if selectedDateRange == range {
                        Image(systemName: "checkmark")
                            .foregroundColor(AppConstants.Colors.primary)
                    }
                }
                .contentShape(Rectangle())
                .onTapGesture {
                    selectedDateRange = selectedDateRange == range ? nil : range
                }
            }
            
            // Custom date range picker
            if selectedDateRange == .custom {
                DatePicker(
                    "Từ ngày",
                    selection: .constant(Date()),
                    displayedComponents: .date
                )
                
                DatePicker(
                    "Đến ngày",
                    selection: .constant(Date()),
                    displayedComponents: .date
                )
            }
        }
    }
    
    // MARK: - Course Selection Section
    private var courseSelectionSection: some View {
        Section("Môn học") {
            ForEach(availableCourses, id: \.id) { course in
                HStack {
                    VStack(alignment: .leading, spacing: 2) {
                        Text(course.name)
                            .font(.body)
                        Text(course.code)
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    
                    Spacer()
                    
                    if selectedCourses.contains(course.id) {
                        Image(systemName: "checkmark")
                            .foregroundColor(AppConstants.Colors.primary)
                    }
                }
                .contentShape(Rectangle())
                .onTapGesture {
                    if selectedCourses.contains(course.id) {
                        selectedCourses.remove(course.id)
                    } else {
                        selectedCourses.insert(course.id)
                    }
                }
            }
        }
    }
    
    // MARK: - Status Selection Section
    private var statusSelectionSection: some View {
        Section("Trạng thái") {
            ForEach(ClassStatus.allCases, id: \.self) { status in
                HStack {
                    ClassStatusIndicator(status: status)
                    
                    Spacer()
                    
                    if selectedStatuses.contains(status) {
                        Image(systemName: "checkmark")
                            .foregroundColor(AppConstants.Colors.primary)
                    }
                }
                .contentShape(Rectangle())
                .onTapGesture {
                    if selectedStatuses.contains(status) {
                        selectedStatuses.remove(status)
                    } else {
                        selectedStatuses.insert(status)
                    }
                }
            }
        }
    }
    
    // MARK: - Type Selection Section
    private var typeSelectionSection: some View {
        Section("Loại lớp học") {
            ForEach(ClassType.allCases, id: \.self) { type in
                HStack {
                    ClassTypeBadge(type: type)
                    
                    Spacer()
                    
                    if selectedTypes.contains(type) {
                        Image(systemName: "checkmark")
                            .foregroundColor(AppConstants.Colors.primary)
                    }
                }
                .contentShape(Rectangle())
                .onTapGesture {
                    if selectedTypes.contains(type) {
                        selectedTypes.remove(type)
                    } else {
                        selectedTypes.insert(type)
                    }
                }
            }
        }
    }
    
    // MARK: - Instructor Selection Section
    private var instructorSelectionSection: some View {
        Section("Giảng viên") {
            ForEach(availableInstructors, id: \.0) { instructor in
                HStack {
                    Text(instructor.1)
                    
                    Spacer()
                    
                    if selectedInstructor == instructor.0 {
                        Image(systemName: "checkmark")
                            .foregroundColor(AppConstants.Colors.primary)
                    }
                }
                .contentShape(Rectangle())
                .onTapGesture {
                    selectedInstructor = selectedInstructor == instructor.0 ? nil : instructor.0
                }
            }
        }
    }
    
    // MARK: - Reset Section
    private var resetSection: some View {
        Section {
            Button("Đặt lại tất cả") {
                selectedDateRange = nil
                selectedCourses.removeAll()
                selectedStatuses.removeAll()
                selectedTypes.removeAll()
                selectedInstructor = nil
            }
            .foregroundColor(AppConstants.Colors.error)
        }
    }
}

// MARK: - Date Range Enum
enum DateRange: String, CaseIterable {
    case today = "Hôm nay"
    case tomorrow = "Ngày mai"
    case thisWeek = "Tuần này"
    case nextWeek = "Tuần tới"
    case thisMonth = "Tháng này"
    case nextMonth = "Tháng tới"
    case custom = "Tùy chọn"
    
    var displayName: String {
        return self.rawValue
    }
    
    var dateInterval: DateInterval? {
        let calendar = Calendar.current
        let now = Date()
        
        switch self {
        case .today:
            return calendar.dateInterval(of: .day, for: now)
        case .tomorrow:
            let tomorrow = calendar.date(byAdding: .day, value: 1, to: now) ?? now
            return calendar.dateInterval(of: .day, for: tomorrow)
        case .thisWeek:
            return calendar.dateInterval(of: .weekOfYear, for: now)
        case .nextWeek:
            let nextWeek = calendar.date(byAdding: .weekOfYear, value: 1, to: now) ?? now
            return calendar.dateInterval(of: .weekOfYear, for: nextWeek)
        case .thisMonth:
            return calendar.dateInterval(of: .month, for: now)
        case .nextMonth:
            let nextMonth = calendar.date(byAdding: .month, value: 1, to: now) ?? now
            return calendar.dateInterval(of: .month, for: nextMonth)
        case .custom:
            return nil
        }
    }
}

// MARK: - Quick Filter Chips
struct QuickFilterChips: View {
    @Binding var selectedFilter: ClassFilter
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 8) {
                ForEach(ClassFilter.allCases, id: \.self) { filter in
                    ClassFilterChip(
                        title: filter.rawValue,
                        isSelected: selectedFilter == filter
                    ) {
                        selectedFilter = filter
                    }
                }
            }
            .padding(.horizontal)
        }
    }
}

// MARK: - Class Filter Chip
struct ClassFilterChip: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(isSelected ? .white : AppConstants.Colors.textPrimary)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    isSelected ? AppConstants.Colors.primary : AppConstants.Colors.surface
                )
                .cornerRadius(20)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Filter Summary View
struct FilterSummaryView: View {
    let activeFiltersCount: Int
    let onClearAll: () -> Void
    
    var body: some View {
        if activeFiltersCount > 0 {
            HStack {
                Text("\(activeFiltersCount) bộ lọc đang áp dụng")
                    .font(.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                
                Spacer()
                
                Button("Xóa tất cả") {
                    onClearAll()
                }
                .font(.caption)
                .foregroundColor(AppConstants.Colors.primary)
            }
            .padding(.horizontal)
            .padding(.vertical, 8)
            .background(AppConstants.Colors.surface)
        }
    }
}

// MARK: - Class Search Bar
struct ClassSearchBar: View {
    @Binding var searchText: String
    @State private var isEditing = false
    
    var body: some View {
        HStack {
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(AppConstants.Colors.textSecondary)
                
                TextField("Tìm kiếm lớp học, môn học...", text: $searchText)
                    .textFieldStyle(PlainTextFieldStyle())
                    .onTapGesture {
                        isEditing = true
                    }
                
                if !searchText.isEmpty {
                    Button(action: {
                        searchText = ""
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(AppConstants.Colors.surface)
            .cornerRadius(10)
            
            if isEditing {
                Button("Hủy") {
                    searchText = ""
                    isEditing = false
                    hideKeyboard()
                }
                .foregroundColor(AppConstants.Colors.primary)
                .transition(.move(edge: .trailing))
            }
        }
        .animation(.easeInOut(duration: 0.2), value: isEditing)
    }
    
    private func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
}

// MARK: - Preview
struct ClassFilterComponents_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            QuickFilterChips(selectedFilter: .constant(.today))
            
            FilterSummaryView(activeFiltersCount: 3) {
                print("Clear all filters")
            }
            
            ClassSearchBar(searchText: .constant(""))
        }
        .padding()
        .background(AppConstants.Colors.background)
    }
}
