//
//  SimpleLessonCard.swift
//  mobile-app-template
//
//  Created by Inst<PERSON>ctor App on 28/7/25.
//

import SwiftUI

struct SimpleLessonCard: View {
    let lesson: SimpleLesson
    @State private var showingLessonDetail = false

    var body: some View {
        Button(action: {
            showingLessonDetail = true
        }) {
            VStack(alignment: .leading, spacing: 12) {
            // Header with class code and status
            HStack {
                Text(lesson.classCode)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                LessonStatusBadge(
                    text: lesson.statusText,
                    color: statusColor
                )
            }
            
            // Lesson name
            Text(lesson.name)
                .font(.headline)
                .fontWeight(.semibold)
                .lineLimit(2)
                .multilineTextAlignment(.leading)
            
            // Time and location info
            HStack(spacing: 16) {
                // Time
                HStack(spacing: 4) {
                    Image(systemName: "clock")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("\(lesson.timeString) - \(endTimeString)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                // Room
                if let room = lesson.room {
                    HStack(spacing: 4) {
                        Image(systemName: "location")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text(room)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            // Class name
            Text(lesson.className)
                .font(.subheadline)
                .foregroundColor(.primary)
            
            // Bottom info
            HStack {
                // Students count
                HStack(spacing: 4) {
                    Image(systemName: "person.2")
                        .font(.caption)
                        .foregroundColor(.blue)
                    
                    Text("\(lesson.totalStudents) học viên")
                        .font(.caption)
                        .foregroundColor(.blue)
                }
                
                Spacer()
                
                // Attendance rate (if available)
                if let attendanceRate = lesson.attendanceRate {
                    HStack(spacing: 4) {
                        Image(systemName: "checkmark.circle")
                            .font(.caption)
                            .foregroundColor(.green)
                        
                        Text("\(Int(attendanceRate))% có mặt")
                            .font(.caption)
                            .foregroundColor(.green)
                    }
                }
                
                // Duration
                if let duration = lesson.durationHours {
                    HStack(spacing: 4) {
                        Image(systemName: "timer")
                            .font(.caption)
                            .foregroundColor(.orange)
                        
                        Text("\(formatDuration(duration))")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }
                }
            }
        }
            .padding(16)
            .background(Color(.systemBackground))
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
        }
        .buttonStyle(PlainButtonStyle())
        .sheet(isPresented: $showingLessonDetail) {
            SimpleLessonDetailBottomSheet(lesson: lesson)
        }
    }
    
    // MARK: - Computed Properties
    private var statusColor: Color {
        switch lesson.statusColor {
        case "blue": return .blue
        case "green": return .green
        case "red": return .red
        case "orange": return .orange
        default: return .gray
        }
    }
    
    private var endTimeString: String {
        let duration = lesson.durationHours ?? 2.0
        let endDate = lesson.startDate.addingTimeInterval(duration * 3600)
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: endDate)
    }
    
    private func formatDuration(_ hours: Double) -> String {
        if hours >= 1 {
            return "\(Int(hours))h\(hours.truncatingRemainder(dividingBy: 1) > 0 ? "\(Int((hours.truncatingRemainder(dividingBy: 1)) * 60))m" : "")"
        } else {
            return "\(Int(hours * 60))m"
        }
    }
}

// MARK: - Lesson Status Badge Component
struct LessonStatusBadge: View {
    let text: String
    let color: Color
    
    var body: some View {
        Text(text)
            .font(.caption2)
            .fontWeight(.medium)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(color.opacity(0.2))
            .foregroundColor(color)
            .cornerRadius(6)
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 16) {
        SimpleLessonCard(lesson: SimpleLesson.mockLessons[0])
        SimpleLessonCard(lesson: SimpleLesson.mockLessons[1])
        SimpleLessonCard(lesson: SimpleLesson.mockLessons[4])
    }
    .padding()
    .background(Color(.systemGroupedBackground))
}
