//
//  ClassListComponents.swift
//  mobile-app-template
//
//  Created by Inst<PERSON>ctor <PERSON>pp on 23/7/25.
//

import SwiftUI

// MARK: - Class Stat Card
struct ClassStatCard: View {
    let title: String
    let value: String
    let subtitle: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Text(title)
                .font(AppConstants.Typography.caption)
                .foregroundColor(AppConstants.Colors.textSecondary)

            Text(value)
                .font(AppConstants.Typography.title2)
                .foregroundColor(color)

            Text(subtitle)
                .font(AppConstants.Typography.caption)
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(12)
    }
}

// MARK: - Empty Classes View
struct EmptyClassesView: View {
    let date: Date
    
    private var formattedDate: String {
        let formatter = DateFormatter()
        if Calendar.current.isDateInToday(date) {
            return "hôm nay"
        } else if Calendar.current.isDateInTomorrow(date) {
            return "ngày mai"
        } else {
            formatter.dateFormat = "dd/MM/yyyy"
            return formatter.string(from: date)
        }
    }
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "calendar.badge.clock")
                .font(.system(size: 50))
                .foregroundColor(AppConstants.Colors.textSecondary)
            
            Text("Không có lớp học nào")
                .font(.headline)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Text("Không có lớp học nào được lên lịch cho \(formattedDate)")
                .font(.subheadline)
                .foregroundColor(AppConstants.Colors.textSecondary)
                .multilineTextAlignment(.center)
        }
        .padding(40)
        .frame(maxWidth: .infinity)
        .background(Color.clear)
        .cornerRadius(12)
    }
}

// MARK: - Class Filter Sheet
struct ClassFilterSheet: View {
    @Binding var selectedFilter: ClassFilter
    @Binding var selectedStatus: ClassStatus?
    @Binding var selectedCourse: String?
    @Environment(\.dismiss) private var dismiss
    
    // Mock courses - in real app, this would come from a service
    private let availableCourses = [
        ("course_1", "CS301 - Lập trình iOS"),
        ("course_2", "CS302 - Android Development"),
        ("course_3", "CS303 - Web Development")
    ]
    
    var body: some View {
        NavigationView {
            Form {
                // Date filter section
                Section("Lọc theo thời gian") {
                    ForEach(ClassFilter.allCases, id: \.self) { filter in
                        HStack {
                            Text(filter.rawValue)
                            Spacer()
                            if selectedFilter == filter {
                                Image(systemName: "checkmark")
                                    .foregroundColor(AppConstants.Colors.primary)
                            }
                        }
                        .contentShape(Rectangle())
                        .onTapGesture {
                            selectedFilter = filter
                        }
                    }
                }
                
                // Status filter section
                Section("Lọc theo trạng thái") {
                    HStack {
                        Text("Tất cả trạng thái")
                        Spacer()
                        if selectedStatus == nil {
                            Image(systemName: "checkmark")
                                .foregroundColor(AppConstants.Colors.primary)
                        }
                    }
                    .contentShape(Rectangle())
                    .onTapGesture {
                        selectedStatus = nil
                    }
                    
                    ForEach(ClassStatus.allCases, id: \.self) { status in
                        HStack {
                            Text(status.displayName)
                            Spacer()
                            if selectedStatus == status {
                                Image(systemName: "checkmark")
                                    .foregroundColor(AppConstants.Colors.primary)
                            }
                        }
                        .contentShape(Rectangle())
                        .onTapGesture {
                            selectedStatus = status
                        }
                    }
                }
                
                // Course filter section
                Section("Lọc theo môn học") {
                    HStack {
                        Text("Tất cả môn học")
                        Spacer()
                        if selectedCourse == nil {
                            Image(systemName: "checkmark")
                                .foregroundColor(AppConstants.Colors.primary)
                        }
                    }
                    .contentShape(Rectangle())
                    .onTapGesture {
                        selectedCourse = nil
                    }
                    
                    ForEach(availableCourses, id: \.0) { course in
                        HStack {
                            Text(course.1)
                            Spacer()
                            if selectedCourse == course.0 {
                                Image(systemName: "checkmark")
                                    .foregroundColor(AppConstants.Colors.primary)
                            }
                        }
                        .contentShape(Rectangle())
                        .onTapGesture {
                            selectedCourse = course.0
                        }
                    }
                }
                
                // Reset section
                Section {
                    Button("Đặt lại bộ lọc") {
                        selectedFilter = .all
                        selectedStatus = nil
                        selectedCourse = nil
                    }
                    .foregroundColor(AppConstants.Colors.error)
                }
            }
            .navigationTitle("Bộ lọc")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Hủy") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Xong") {
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
    }
}

// MARK: - Class Action Sheet
struct ClassActionSheet: View {
    let classItem: Class
    let onStartClass: () -> Void
    let onEndClass: () -> Void
    let onTakeAttendance: () -> Void
    let onViewMaterials: () -> Void
    let onEditClass: () -> Void
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            VStack(spacing: 8) {
                Text(classItem.title)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Text("\(classItem.courseCode) • \(classItem.formattedTime)")
                    .font(.subheadline)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            .padding(.top, 20)
            .padding(.bottom, 24)
            
            // Actions
            VStack(spacing: 0) {
                if classItem.status == .scheduled && classItem.isUpcoming {
                    ActionButton(
                        title: "Bắt đầu lớp học",
                        icon: "play.circle.fill",
                        color: .green
                    ) {
                        onStartClass()
                        dismiss()
                    }
                }
                
                if classItem.status == .inProgress {
                    ActionButton(
                        title: "Kết thúc lớp học",
                        icon: "stop.circle.fill",
                        color: .red
                    ) {
                        onEndClass()
                        dismiss()
                    }
                }
                
                ActionButton(
                    title: "Điểm danh",
                    icon: "person.2.badge.plus",
                    color: .blue
                ) {
                    onTakeAttendance()
                    dismiss()
                }
                
                ActionButton(
                    title: "Tài liệu",
                    icon: "doc.text",
                    color: .orange
                ) {
                    onViewMaterials()
                    dismiss()
                }
                
                ActionButton(
                    title: "Chỉnh sửa",
                    icon: "pencil",
                    color: .gray
                ) {
                    onEditClass()
                    dismiss()
                }
            }
            
            // Cancel button
            Button("Hủy") {
                dismiss()
            }
            .font(.headline)
            .foregroundColor(AppConstants.Colors.textPrimary)
            .frame(maxWidth: .infinity)
            .frame(height: 56)
            .background(AppConstants.Colors.surface)
            .cornerRadius(12)
            .padding(.top, 8)
            
            Spacer(minLength: 0)
        }
        .padding(.horizontal, 20)
        .background(AppConstants.Colors.background)
    }
}

// MARK: - Action Button
struct ActionButton: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(color)
                    .frame(width: 24)
                
                Text(title)
                    .font(.body)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Spacer()
            }
            .padding(.horizontal, 16)
            .frame(height: 56)
            .background(AppConstants.Colors.cardBackground)
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Class Status Indicator
struct ClassStatusIndicator: View {
    let status: ClassStatus
    
    var body: some View {
        HStack(spacing: 6) {
            Circle()
                .fill(colorForStatus(status))
                .frame(width: 8, height: 8)
            
            Text(status.displayName)
                .font(AppConstants.Typography.caption)
                .foregroundColor(colorForStatus(status))
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(colorForStatus(status).opacity(0.1))
        .cornerRadius(8)
    }
    
    private func colorForStatus(_ status: ClassStatus) -> Color {
        switch status.color {
        case "blue": return .blue
        case "green": return .green
        case "gray": return .gray
        case "red": return .red
        case "orange": return .orange
        default: return .blue
        }
    }
}

// MARK: - Preview
struct ClassListComponents_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            HStack(spacing: 12) {
                ClassStatCard(
                    title: "Hôm nay",
                    value: "3",
                    subtitle: "lớp học",
                    color: .blue
                )

                ClassStatCard(
                    title: "Tuần này",
                    value: "12",
                    subtitle: "sắp tới",
                    color: .green
                )
            }
            
            EmptyClassesView(date: Date())
            
            ClassStatusIndicator(status: .inProgress)
        }
        .padding()
        .background(AppConstants.Colors.background)
    }
}
