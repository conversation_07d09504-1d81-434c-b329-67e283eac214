//
//  MissionViewModel.swift
//  mobile-app-template
//
//  Created for Mission System ViewModel
//

import Foundation
import Combine
import SwiftUI

@MainActor
class MissionViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var activeMissions: [Mission] = []
    @Published var completedMissions: [Mission] = []
    @Published var userStats: UserMissionStats?
    @Published var userBadges: [MissionBadge] = []
    @Published var leaderboard: [MissionLeaderboardEntry] = []
    @Published var streaks: [MissionStreak] = []
    @Published var selectedMission: Mission?
    @Published var isLoading = false
    @Published var error: Error?
    @Published var showRewardAnimation = false
    @Published var lastEarnedReward: MissionReward?
    
    // Filter and sort options
    @Published var selectedCategory: MissionCategory?
    @Published var selectedType: MissionType?
    @Published var sortOption: MissionSortOption = .dueDate
    
    // MARK: - Private Properties
    private let repository: MissionRepositoryProtocol
    private var cancellables = Set<AnyCancellable>()
    private let userId: String
    
    // MARK: - Computed Properties
    var filteredMissions: [Mission] {
        var missions = activeMissions
        
        if let category = selectedCategory {
            missions = missions.filter { $0.category == category }
        }
        
        if let type = selectedType {
            missions = missions.filter { $0.type == type }
        }
        
        // Sort missions
        switch sortOption {
        case .dueDate:
            missions.sort { ($0.endDate ?? Date.distantFuture) < ($1.endDate ?? Date.distantFuture) }
        case .xpReward:
            missions.sort { $0.xpReward > $1.xpReward }
        case .difficulty:
            missions.sort { $0.difficulty.rawValue < $1.difficulty.rawValue }
        case .progress:
            missions.sort { $0.progressPercentage > $1.progressPercentage }
        }
        
        return missions
    }
    
    var dailyMissions: [Mission] {
        activeMissions.filter { $0.type == .daily }
    }
    
    var weeklyMissions: [Mission] {
        activeMissions.filter { $0.type == .weekly }
    }
    
    var totalXP: Int {
        userStats?.totalXP ?? 0
    }
    
    var currentLevel: Int {
        userStats?.currentLevel ?? 1
    }
    
    var levelProgress: Double {
        guard let stats = userStats else { return 0 }
        let currentLevelXP = calculateXPForLevel(stats.currentLevel)
        let nextLevelXP = stats.nextLevelXP
        let progressXP = stats.totalXP - currentLevelXP
        let neededXP = nextLevelXP - currentLevelXP
        return Double(progressXP) / Double(neededXP)
    }
    
    // MARK: - Initialization
    init(repository: MissionRepositoryProtocol = MissionRepository(), userId: String) {
        self.repository = repository
        self.userId = userId
    }
    
    // MARK: - Public Methods
    func loadMissions() {
        isLoading = true
        error = nil
        
        // Fetch active missions
        repository.fetchMissions(for: userId, filter: .active)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    self?.isLoading = false
                    if case .failure(let error) = completion {
                        self?.error = error
                    }
                },
                receiveValue: { [weak self] missions in
                    self?.activeMissions = missions.filter { $0.status == .active || $0.status == .inProgress }
                    self?.completedMissions = missions.filter { $0.status == .completed || $0.status == .claimed }
                }
            )
            .store(in: &cancellables)
        
        // Load user stats
        loadUserStats()
        
        // Load streaks
        loadStreaks()
    }
    
    func loadUserStats() {
        repository.fetchUserStats(userId: userId)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { _ in },
                receiveValue: { [weak self] stats in
                    self?.userStats = stats
                }
            )
            .store(in: &cancellables)
    }
    
    func loadUserBadges() {
        repository.fetchUserBadges(userId: userId)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { _ in },
                receiveValue: { [weak self] badges in
                    self?.userBadges = badges
                }
            )
            .store(in: &cancellables)
    }
    
    func loadLeaderboard(type: LeaderboardType = .weekly) {
        repository.fetchLeaderboard(type: type, limit: 20)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { _ in },
                receiveValue: { [weak self] entries in
                    self?.leaderboard = entries
                }
            )
            .store(in: &cancellables)
    }
    
    func loadStreaks() {
        repository.fetchStreaks(userId: userId)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { _ in },
                receiveValue: { [weak self] streaks in
                    self?.streaks = streaks
                }
            )
            .store(in: &cancellables)
    }
    
    func updateMissionProgress(missionId: String, requirementId: String, increment: Int = 1) {
        guard let mission = activeMissions.first(where: { $0.id == missionId }),
              let requirement = mission.requirements.first(where: { $0.id == requirementId }) else {
            return
        }
        
        let newValue = min(requirement.currentValue + increment, requirement.targetValue)
        
        repository.updateMissionProgress(missionId: missionId, requirementId: requirementId, newValue: newValue)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { _ in },
                receiveValue: { [weak self] progress in
                    // Update local mission data
                    self?.loadMissions()
                    
                    // Check if mission is completed
                    if progress.completedAt != nil {
                        self?.showCompletionAnimation(for: missionId)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    func claimReward(for missionId: String) {
        repository.claimMissionRewards(missionId: missionId, userId: userId)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { _ in },
                receiveValue: { [weak self] reward in
                    self?.lastEarnedReward = reward
                    self?.showRewardAnimation = true
                    self?.loadMissions()
                    self?.loadUserStats()
                    
                    // Auto-hide reward animation after delay
                    DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                        self?.showRewardAnimation = false
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    func selectMission(_ mission: Mission) {
        selectedMission = mission
    }
    
    // MARK: - Private Methods
    private func calculateXPForLevel(_ level: Int) -> Int {
        // Simple level calculation: each level requires more XP
        var totalXP = 0
        for i in 1..<level {
            totalXP += 100 * i
        }
        return totalXP
    }
    
    private func showCompletionAnimation(for missionId: String) {
        // Trigger completion animation
        // This could show a popup or animation
    }
    
    // MARK: - Mission Tracking Integration
    func trackAssignmentCreation() {
        // Find missions with create assignment requirement
        let relevantMissions = activeMissions.filter { mission in
            mission.requirements.contains { $0.type == .createAssignment }
        }
        
        for mission in relevantMissions {
            if let requirement = mission.requirements.first(where: { $0.type == .createAssignment }) {
                updateMissionProgress(missionId: mission.id, requirementId: requirement.id)
            }
        }
    }
    
    func trackGrading(count: Int = 1) {
        let relevantMissions = activeMissions.filter { mission in
            mission.requirements.contains { $0.type == .gradeSubmission }
        }
        
        for mission in relevantMissions {
            if let requirement = mission.requirements.first(where: { $0.type == .gradeSubmission }) {
                updateMissionProgress(missionId: mission.id, requirementId: requirement.id, increment: count)
            }
        }
    }
    
    func trackQuizCreation() {
        let relevantMissions = activeMissions.filter { mission in
            mission.requirements.contains { $0.type == .createQuiz }
        }
        
        for mission in relevantMissions {
            if let requirement = mission.requirements.first(where: { $0.type == .createQuiz }) {
                updateMissionProgress(missionId: mission.id, requirementId: requirement.id)
            }
        }
    }
    
    func trackAttendanceMarking() {
        let relevantMissions = activeMissions.filter { mission in
            mission.requirements.contains { $0.type == .markAttendance }
        }
        
        for mission in relevantMissions {
            if let requirement = mission.requirements.first(where: { $0.type == .markAttendance }) {
                updateMissionProgress(missionId: mission.id, requirementId: requirement.id)
            }
        }
    }
}

// MARK: - Supporting Types
enum MissionSortOption: String, CaseIterable {
    case dueDate = "Thời hạn"
    case xpReward = "Điểm XP"
    case difficulty = "Độ khó"
    case progress = "Tiến độ"
}
