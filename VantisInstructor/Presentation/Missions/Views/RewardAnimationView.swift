//
//  RewardAnimationView.swift
//  mobile-app-template
//
//  Created for Mission Reward Animation
//

import SwiftUI

struct RewardAnimationView: View {
    let reward: MissionReward
    @State private var scale: CGFloat = 0.5
    @State private var opacity: Double = 0
    @State private var offset: CGFloat = 50
    
    var body: some View {
        ZStack {
            // Background overlay
            Color.black.opacity(0.4)
                .ignoresSafeArea()
                .opacity(opacity)
            
            VStack(spacing: 20) {
                // Celebration Icon
                Image(systemName: "star.circle.fill")
                    .font(.system(size: 80))
                    .foregroundColor(.yellow)
                    .scaleEffect(scale)
                
                Text("Phần thưởng đã nhận!")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                VStack(spacing: 12) {
                    // XP Reward
                    HStack(spacing: 8) {
                        Image(systemName: "star.fill")
                            .foregroundColor(.purple)
                        Text("+\(reward.xpEarned) XP")
                            .font(.headline)
                            .foregroundColor(.white)
                    }
                    
                    // Coin Reward
                    if let coins = reward.coinsEarned {
                        HStack(spacing: 8) {
                            Image(systemName: "bitcoinsign.circle.fill")
                                .foregroundColor(.orange)
                            Text("+\(coins) Xu")
                                .font(.headline)
                                .foregroundColor(.white)
                        }
                    }
                    
                    // Badge Reward
                    if let badge = reward.badgeEarned {
                        HStack(spacing: 8) {
                            Image(systemName: "star.circle.fill")
                                .foregroundColor(.yellow)
                            Text("Huy hiệu mới!")
                                .font(.headline)
                                .foregroundColor(.white)
                        }
                    }
                }
                .padding()
                .background(Color.white.opacity(0.2))
                .cornerRadius(12)
            }
            .offset(y: offset)
            .scaleEffect(scale)
            .opacity(opacity)
        }
        .onAppear {
            withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                scale = 1.0
                opacity = 1.0
                offset = 0
            }
            
            // Auto dismiss after 2.5 seconds
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.5) {
                withAnimation(.easeOut(duration: 0.3)) {
                    opacity = 0
                }
            }
        }
    }
}

// MARK: - Preview
#Preview {
    RewardAnimationView(
        reward: MissionReward(
            missionId: "1",
            userId: "1",
            xpEarned: 100,
            coinsEarned: 50,
            badgeEarned: nil,
            claimedAt: Date()
        )
    )
}
