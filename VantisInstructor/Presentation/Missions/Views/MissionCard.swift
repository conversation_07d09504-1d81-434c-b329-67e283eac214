//
//  MissionCard.swift
//  mobile-app-template
//
//  Created for Mission Card Component
//

import SwiftUI

struct MissionCard: View {
    let mission: Mission
    let compact: Bool
    let onTap: () -> Void
    
    init(mission: Mission, compact: Bool = false, onTap: @escaping () -> Void) {
        self.mission = mission
        self.compact = compact
        self.onTap = onTap
    }
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 12) {
                // Header
                HStack(spacing: 12) {
                    // Category Icon
                    ZStack {
                        Circle()
                            .fill(mission.category.color.opacity(0.15))
                            .frame(width: compact ? 36 : 44, height: compact ? 36 : 44)
                        
                        Image(systemName: mission.category.icon)
                            .font(.system(size: compact ? 16 : 20))
                            .foregroundColor(mission.category.color)
                    }
                    
                    VStack(alignment: .leading, spacing: 4) {
                        // Title
                        Text(mission.title)
                            .font(.beVietnamPro(.semiBold, size: compact ? 16 : 18))
                            .foregroundColor(AppConstants.Colors.textPrimary)
                            .lineLimit(1)
                        
                        // Type and Difficulty
                        HStack(spacing: 8) {
                            // Mission Type Badge
                            MissionTypeBadge(type: mission.type)
                            
                            // Difficulty Stars
                            DifficultyStars(difficulty: mission.difficulty, size: 10)
                            
                            Spacer()
                            
                            // Time remaining
                            if let timeRemaining = mission.timeRemaining {
                                HStack(spacing: 4) {
                                    Image(systemName: "clock")
                                        .font(.caption2)
                                    Text(timeRemaining)
                                        .font(.beVietnamPro(.medium, size: 10))
                                }
                                .foregroundColor(mission.isActive ? AppConstants.Colors.textSecondary : .red)
                            }
                        }
                    }
                    
                    Spacer()
                    
                    // Rewards
                    if !compact {
                        VStack(alignment: .trailing, spacing: 4) {
                            // XP Reward
                            HStack(spacing: 4) {
                                Text("+\(mission.xpReward)")
                                    .font(.subheadline)
                                    .fontWeight(.bold)
                                    .foregroundColor(.purple)
                                Text("XP")
                                    .font(.caption2)
                                    .foregroundColor(.purple)
                            }
                            
                            // Coin Reward
                            if let coinReward = mission.coinReward {
                                HStack(spacing: 4) {
                                    Text("+\(coinReward)")
                                        .font(.caption)
                                        .fontWeight(.semibold)
                                        .foregroundColor(.orange)
                                    Image(systemName: "bitcoinsign.circle.fill")
                                        .font(.caption)
                                        .foregroundColor(.orange)
                                }
                            }
                        }
                    }
                }
                
                if !compact {
                    // Description
                    Text(mission.description)
                        .font(.beVietnamPro(.medium, size: 12))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .lineLimit(2)
                    
                    // Requirements Progress
                    VStack(spacing: 8) {
                        ForEach(mission.requirements.prefix(2)) { requirement in
                            RequirementProgress(requirement: requirement)
                        }
                        
                        if mission.requirements.count > 2 {
                            Text("và \(mission.requirements.count - 2) yêu cầu khác")
                                .font(.beVietnamPro(.medium, size: 10))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                        }
                    }
                    
                    // Overall Progress
                    ProgressView(value: mission.progressPercentage / 100)
                        .tint(progressColor)
                        .background(AppConstants.Colors.cardBackground)
                        .cornerRadius(4)
                }
            }
            .padding()
            .background(AppConstants.Colors.surface)
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var progressColor: Color {
        switch mission.progressPercentage {
        case 0..<25: return .red
        case 25..<50: return .orange
        case 50..<75: return .yellow
        case 75..<100: return .blue
        default: return .green
        }
    }
}

// MARK: - Mission Type Badge
struct MissionTypeBadge: View {
    let type: MissionType
    
    var body: some View {
        Text(type.displayName)
            .font(.beVietnamPro(.medium, size: 10))
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(badgeColor.opacity(0.15))
            .foregroundColor(badgeColor)
            .cornerRadius(6)
    }
    
    private var badgeColor: Color {
        switch type {
        case .daily: return .blue
        case .weekly: return .green
        case .monthly: return .orange
        case .semester: return .purple
        case .achievement: return .pink
        case .special: return .red
        }
    }
}

// MARK: - Difficulty Stars
struct DifficultyStars: View {
    let difficulty: MissionDifficulty
    let size: CGFloat
    
    var body: some View {
        HStack(spacing: 2) {
            ForEach(0..<difficulty.stars, id: \.self) { _ in
                Image(systemName: "star.fill")
                    .font(.system(size: size))
                    .foregroundColor(difficulty.color)
            }
        }
    }
}

// MARK: - Requirement Progress
struct RequirementProgress: View {
    let requirement: MissionRequirement
    
    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: requirement.type.icon)
                .font(.caption)
                .foregroundColor(requirement.isCompleted ? .green : AppConstants.Colors.textSecondary)
                .frame(width: 16)
            
            Text(requirement.description)
                .font(.beVietnamPro(.medium, size: 12))
                .foregroundColor(AppConstants.Colors.textPrimary)
                .lineLimit(1)
            
            Spacer()
            
            // Progress
            HStack(spacing: 4) {
                Text("\(requirement.currentValue)")
                    .font(.beVietnamPro(.semiBold, size: 12))
                    .foregroundColor(requirement.isCompleted ? .green : AppConstants.Colors.textPrimary)

                Text("/")
                    .font(.beVietnamPro(.medium, size: 12))
                    .foregroundColor(AppConstants.Colors.textSecondary)

                Text("\(requirement.targetValue)")
                    .font(.beVietnamPro(.medium, size: 12))
                    .foregroundColor(AppConstants.Colors.textSecondary)

                if let unit = requirement.unit {
                    Text(unit)
                        .font(.beVietnamPro(.medium, size: 10))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
            }
            
            if requirement.isCompleted {
                Image(systemName: "checkmark.circle.fill")
                    .font(.caption)
                    .foregroundColor(.green)
            }
        }
    }
}

// MARK: - Completed Mission Card
struct CompletedMissionCard: View {
    let mission: Mission
    let onClaim: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                // Icon
                ZStack {
                    Circle()
                        .fill(Color.green.opacity(0.15))
                        .frame(width: 40, height: 40)
                    
                    Image(systemName: "checkmark.seal.fill")
                        .font(.system(size: 20))
                        .foregroundColor(.green)
                }
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(mission.title)
                        .font(.beVietnamPro(.semiBold, size: 18))
                        .foregroundColor(AppConstants.Colors.textPrimary)

                    Text("Hoàn thành lúc \(formattedCompletionDate)")
                        .font(.beVietnamPro(.medium, size: 12))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
                
                Spacer()
                
                // Rewards
                VStack(alignment: .trailing, spacing: 4) {
                    HStack(spacing: 4) {
                        Text("+\(mission.xpReward)")
                            .font(.beVietnamPro(.bold, size: 16))
                            .foregroundColor(.purple)
                        Text("XP")
                            .font(.beVietnamPro(.medium, size: 10))
                            .foregroundColor(.purple)
                    }
                    
                    if let coinReward = mission.coinReward {
                        HStack(spacing: 4) {
                            Text("+\(coinReward)")
                                .font(.beVietnamPro(.semiBold, size: 12))
                                .foregroundColor(.orange)
                            Image(systemName: "bitcoinsign.circle.fill")
                                .font(.caption)
                                .foregroundColor(.orange)
                        }
                    }
                }
            }
            
            // Claim button if not claimed
            if mission.status == .completed {
                Button(action: onClaim) {
                    HStack {
                        Image(systemName: "gift.fill")
                            .font(.subheadline)
                        Text("Nhận thưởng")
                            .font(.beVietnamPro(.semiBold, size: 16))
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
                    .background(AppConstants.Colors.primary)
                    .cornerRadius(8)
                }
            }
        }
        .padding()
        .background(mission.status == .claimed ? AppConstants.Colors.cardBackground : AppConstants.Colors.surface)
        .cornerRadius(12)
        .overlay(
            mission.status == .claimed ?
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.green, lineWidth: 2)
            : nil
        )
    }
    
    private var formattedCompletionDate: String {
        guard let completedAt = mission.progress.completedAt else { return "" }
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm dd/MM"
        return formatter.string(from: completedAt)
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 16) {
        MissionCard(mission: Mission.mockMissions[0]) {}
        MissionCard(mission: Mission.mockMissions[1], compact: true) {}
        CompletedMissionCard(mission: Mission.mockMissions[2]) {}
    }
    .padding()
    .background(AppConstants.Colors.background)
}
