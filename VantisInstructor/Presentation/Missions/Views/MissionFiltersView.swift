//
//  MissionFiltersView.swift
//  mobile-app-template
//
//  Created for Mission Filters View
//

import SwiftUI

struct MissionFiltersView: View {
    @ObservedObject var viewModel: MissionViewModel
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            Form {
                // Category Filter
                Section("Danh mục") {
                    ForEach(MissionCategory.allCases, id: \.self) { category in
                        Button {
                            if viewModel.selectedCategory == category {
                                viewModel.selectedCategory = nil
                            } else {
                                viewModel.selectedCategory = category
                            }
                        } label: {
                            HStack {
                                Image(systemName: category.icon)
                                    .foregroundColor(category.color)
                                    .frame(width: 24)
                                
                                Text(category.displayName)
                                    .foregroundColor(AppConstants.Colors.textPrimary)
                                
                                Spacer()
                                
                                if viewModel.selectedCategory == category {
                                    Image(systemName: "checkmark")
                                        .foregroundColor(AppConstants.Colors.primary)
                                }
                            }
                        }
                    }
                }
                
                // Type Filter
                Section("Loại nhiệm vụ") {
                    ForEach(MissionType.allCases, id: \.self) { type in
                        Button {
                            if viewModel.selectedType == type {
                                viewModel.selectedType = nil
                            } else {
                                viewModel.selectedType = type
                            }
                        } label: {
                            HStack {
                                Text(type.displayName)
                                    .foregroundColor(AppConstants.Colors.textPrimary)
                                
                                Spacer()
                                
                                if viewModel.selectedType == type {
                                    Image(systemName: "checkmark")
                                        .foregroundColor(AppConstants.Colors.primary)
                                }
                            }
                        }
                    }
                }
                
                // Sort Options
                Section("Sắp xếp theo") {
                    ForEach(MissionSortOption.allCases, id: \.self) { option in
                        Button {
                            viewModel.sortOption = option
                        } label: {
                            HStack {
                                Text(option.rawValue)
                                    .foregroundColor(AppConstants.Colors.textPrimary)
                                
                                Spacer()
                                
                                if viewModel.sortOption == option {
                                    Image(systemName: "checkmark")
                                        .foregroundColor(AppConstants.Colors.primary)
                                }
                            }
                        }
                    }
                }
                
                // Clear Filters
                Section {
                    Button {
                        viewModel.selectedCategory = nil
                        viewModel.selectedType = nil
                        viewModel.sortOption = .dueDate
                    } label: {
                        HStack {
                            Spacer()
                            Text("Xóa bộ lọc")
                                .foregroundColor(.red)
                            Spacer()
                        }
                    }
                }
            }
            .navigationTitle("Bộ lọc")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Xong") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Preview
#Preview {
    MissionFiltersView(viewModel: MissionViewModel(userId: "user_1"))
}
