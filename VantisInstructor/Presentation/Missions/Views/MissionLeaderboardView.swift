//
//  MissionLeaderboardView.swift
//  mobile-app-template
//
//  Created for Mission Leaderboard View
//

import SwiftUI

struct MissionLeaderboardView: View {
    @ObservedObject var viewModel: MissionViewModel
    @State private var selectedPeriod: LeaderboardType = .weekly
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Period Selector
                periodSelector
                
                ScrollView {
                    VStack(spacing: 16) {
                        // Top 3 Podium
                        if viewModel.leaderboard.count >= 3 {
                            topThreePodium
                        }
                        
                        // Remaining Rankings
                        remainingRankings
                    }
                    .padding()
                }
                .background(AppConstants.Colors.background)
            }
            .navigationTitle("Bảng xếp hạng")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Đóng") {
                        dismiss()
                    }
                }
            }
        }
        .task {
            viewModel.loadLeaderboard(type: selectedPeriod)
        }
    }
    
    // MARK: - Period Selector
    private var periodSelector: some View {
        HStack(spacing: 0) {
            ForEach([LeaderboardType.weekly, .monthly, .allTime], id: \.self) { period in
                Button {
                    selectedPeriod = period
                    viewModel.loadLeaderboard(type: period)
                } label: {
                    Text(periodDisplayName(period))
                        .font(.subheadline)
                        .fontWeight(selectedPeriod == period ? .semibold : .regular)
                        .foregroundColor(selectedPeriod == period ? .white : AppConstants.Colors.textSecondary)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(
                            selectedPeriod == period ? AppConstants.Colors.primary : Color.clear
                        )
                }
            }
        }
        .background(AppConstants.Colors.surface)
        .cornerRadius(12)
        .padding()
    }
    
    // MARK: - Top 3 Podium
    private var topThreePodium: some View {
        HStack(alignment: .bottom, spacing: 16) {
            // 2nd Place
            if viewModel.leaderboard.count > 1 {
                PodiumView(
                    entry: viewModel.leaderboard[1],
                    rank: 2,
                    height: 120
                )
            }
            
            // 1st Place
            PodiumView(
                entry: viewModel.leaderboard[0],
                rank: 1,
                height: 150
            )
            
            // 3rd Place
            if viewModel.leaderboard.count > 2 {
                PodiumView(
                    entry: viewModel.leaderboard[2],
                    rank: 3,
                    height: 100
                )
            }
        }
        .padding(.bottom, 24)
    }
    
    // MARK: - Remaining Rankings
    private var remainingRankings: some View {
        VStack(spacing: 12) {
            ForEach(Array(viewModel.leaderboard.enumerated()), id: \.element.id) { index, entry in
                if index >= 3 {
                    LeaderboardRow(entry: entry)
                }
            }
        }
    }
    
    private func periodDisplayName(_ period: LeaderboardType) -> String {
        switch period {
        case .weekly: return "Tuần"
        case .monthly: return "Tháng"
        case .allTime: return "Tổng"
        }
    }
}

// MARK: - Podium View
struct PodiumView: View {
    let entry: MissionLeaderboardEntry
    let rank: Int
    let height: CGFloat
    
    var body: some View {
        VStack(spacing: 8) {
            // Avatar with rank badge
            ZStack(alignment: .bottom) {
                // Avatar
                Circle()
                    .fill(LinearGradient(
                        colors: [rankColor.opacity(0.3), rankColor],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ))
                    .frame(width: 60, height: 60)
                    .overlay(
                        Text(String(entry.userName.prefix(1)))
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                    )
                
                // Rank badge
                Circle()
                    .fill(rankColor)
                    .frame(width: 24, height: 24)
                    .overlay(
                        Text("\(rank)")
                            .font(.caption)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                    )
                    .offset(y: 8)
            }
            
            // Name
            Text(entry.userName)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(AppConstants.Colors.textPrimary)
                .lineLimit(1)
            
            // XP
            Text("\(entry.totalXP) XP")
                .font(.subheadline)
                .fontWeight(.bold)
                .foregroundColor(rankColor)
            
            // Podium
            RoundedRectangle(cornerRadius: 8, style: .continuous)
                .fill(rankColor.opacity(0.15))
                .frame(height: height)
                .overlay(
                    VStack {
                        Spacer()
                        VStack(spacing: 4) {
                            Image(systemName: trophyIcon)
                                .font(.title3)
                                .foregroundColor(rankColor)
                            
                            Text("\(entry.completedMissions)")
                                .font(.caption2)
                                .foregroundColor(AppConstants.Colors.textSecondary)
                            Text("nhiệm vụ")
                                .font(.caption2)
                                .foregroundColor(AppConstants.Colors.textSecondary)
                        }
                        .padding(.bottom, 8)
                    }
                )
        }
        .frame(maxWidth: .infinity)
    }
    
    private var rankColor: Color {
        switch rank {
        case 1: return .yellow
        case 2: return .gray
        case 3: return .orange
        default: return AppConstants.Colors.primary
        }
    }
    
    private var trophyIcon: String {
        switch rank {
        case 1: return "trophy.fill"
        case 2: return "medal.fill"
        case 3: return "rosette"
        default: return "star.fill"
        }
    }
}

// MARK: - Leaderboard Row
struct LeaderboardRow: View {
    let entry: MissionLeaderboardEntry
    
    var body: some View {
        HStack(spacing: 16) {
            // Rank
            ZStack {
                Circle()
                    .fill(AppConstants.Colors.cardBackground)
                    .frame(width: 40, height: 40)
                
                Text("\(entry.rank)")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(AppConstants.Colors.textPrimary)
            }
            
            // Avatar
            Circle()
                .fill(AppConstants.Colors.primary.opacity(0.2))
                .frame(width: 44, height: 44)
                .overlay(
                    Text(String(entry.userName.prefix(1)))
                        .font(.headline)
                        .foregroundColor(AppConstants.Colors.primary)
                )
            
            // User Info
            VStack(alignment: .leading, spacing: 4) {
                Text(entry.userName)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                if let department = entry.department {
                    Text(department)
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
            }
            
            Spacer()
            
            // Stats
            VStack(alignment: .trailing, spacing: 4) {
                Text("\(entry.totalXP) XP")
                    .font(.subheadline)
                    .fontWeight(.bold)
                    .foregroundColor(AppConstants.Colors.primary)
                
                HStack(spacing: 8) {
                    // Missions completed
                    HStack(spacing: 2) {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.caption2)
                        Text("\(entry.completedMissions)")
                            .font(.caption)
                    }
                    
                    // Badges earned
                    HStack(spacing: 2) {
                        Image(systemName: "star.circle.fill")
                            .font(.caption2)
                        Text("\(entry.badgesEarned)")
                            .font(.caption)
                    }
                }
                .foregroundColor(AppConstants.Colors.textSecondary)
            }
            
            // Rank change indicator
            if let rankChange = entry.rankChange {
                VStack {
                    if rankChange > 0 {
                        Image(systemName: "arrow.up")
                            .font(.caption)
                            .foregroundColor(.green)
                    } else if rankChange < 0 {
                        Image(systemName: "arrow.down")
                            .font(.caption)
                            .foregroundColor(.red)
                    } else {
                        Image(systemName: "minus")
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                }
                .frame(width: 20)
            }
        }
        .padding()
        .background(AppConstants.Colors.surface)
        .cornerRadius(12)
    }
}

// MARK: - Preview
#Preview {
    MissionLeaderboardView(viewModel: MissionViewModel(userId: "user_1"))
}
