//
//  BadgeCollectionView.swift
//  mobile-app-template
//
//  Created for Badge Collection View
//

import SwiftUI

struct BadgeCollectionView: View {
    @ObservedObject var viewModel: MissionViewModel
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 16) {
                ForEach(viewModel.userBadges) { badge in
                    MissionBadgeView(badge: badge)
                }
                }
                .padding()
                .background(AppConstants.Colors.background)
            }
            .navigationTitle("Huy hiệu")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Đóng") {
                        dismiss()
                    }
                }
            }
        }
        .onAppear {
            viewModel.loadUserBadges()
        }
    }
}

// MARK: - Mission Badge View
struct MissionBadgeView: View {
    let badge: MissionBadge
    
    var body: some View {
        HStack(spacing: 16) {
            // Badge Icon
            ZStack {
                Circle()
                    .fill(badge.rarity.color.opacity(0.15))
                    .frame(width: 60, height: 60)
                
                Image(systemName: badge.iconName)
                    .font(.title2)
                    .foregroundColor(badge.rarity.color)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                // Badge Name
                Text(badge.name)
                    .font(.headline)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                // Badge Description
                Text(badge.description)
                    .font(.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .fixedSize(horizontal: false, vertical: true)
                
                // Unlocked Status
                if badge.isUnlocked {
                    Text("Đã mở khóa")
                        .font(.caption2)
                        .fontWeight(.semibold)
                        .foregroundColor(.green)
                } else {
                    Text("Chưa mở khóa")
                        .font(.caption2)
                        .fontWeight(.semibold)
                        .foregroundColor(.red)
                }
            }
            
            Spacer()
        }
        .padding()
        .background(AppConstants.Colors.surface)
        .cornerRadius(12)
    }
}

// MARK: - Preview
#Preview {
    BadgeCollectionView(viewModel: MissionViewModel(userId: "user_1"))
}
