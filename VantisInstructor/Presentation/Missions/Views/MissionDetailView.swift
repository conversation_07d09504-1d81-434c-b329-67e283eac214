//
//  MissionDetailView.swift
//  mobile-app-template
//
//  Created for Mission Detail View
//

import SwiftUI

struct MissionDetailView: View {
    let mission: Mission
    @ObservedObject var viewModel: MissionViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var showingShareSheet = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    // Mission Header
                    missionHeader
                    
                    // Description Section
                    descriptionSection
                    
                    // Requirements Section
                    requirementsSection
                    
                    // Rewards Section
                    rewardsSection
                    
                    // Tips Section
                    if !mission.tags.isEmpty {
                        tipsSection
                    }
                    
                    // Action Button
                    if mission.isCompleted && mission.status == .completed {
                        claimRewardButton
                    }
                }
                .padding()
            }
            .background(AppConstants.Colors.background)
            .navigationTitle("Chi tiết nhiệm vụ")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    But<PERSON>("Đóng") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button {
                        showingShareSheet = true
                    } label: {
                        Image(systemName: "square.and.arrow.up")
                    }
                }
            }
        }
        .sheet(isPresented: $showingShareSheet) {
            MissionShareSheet(items: [createShareText()])
        }
    }
    
    // MARK: - Mission Header
    private var missionHeader: some View {
        VStack(spacing: 16) {
            // Category Icon
            ZStack {
                Circle()
                    .fill(mission.category.color.opacity(0.15))
                    .frame(width: 80, height: 80)
                
                Image(systemName: mission.category.icon)
                    .font(.system(size: 36))
                    .foregroundColor(mission.category.color)
            }
            
            // Title
            Text(mission.title)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(AppConstants.Colors.textPrimary)
                .multilineTextAlignment(.center)
            
            // Badges
            HStack(spacing: 12) {
                MissionTypeBadge(type: mission.type)
                
                HStack(spacing: 4) {
                    DifficultyStars(difficulty: mission.difficulty, size: 12)
                    Text(mission.difficulty.displayName)
                        .font(.caption)
                        .foregroundColor(mission.difficulty.color)
                }
                .padding(.horizontal, 10)
                .padding(.vertical, 6)
                .background(mission.difficulty.color.opacity(0.15))
                .cornerRadius(8)
            }
            
            // Progress
            VStack(spacing: 8) {
                HStack {
                    Text("Tiến độ")
                        .font(.subheadline)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                    
                    Spacer()
                    
                    Text("\(Int(mission.progressPercentage))%")
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(progressColor)
                }
                
                ProgressView(value: mission.progressPercentage / 100)
                    .tint(progressColor)
                    .scaleEffect(x: 1, y: 2)
            }
            .padding()
            .background(AppConstants.Colors.cardBackground)
            .cornerRadius(12)
        }
    }
    
    // MARK: - Description Section
    private var descriptionSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Label("Mô tả", systemImage: "text.alignleft")
                .font(.headline)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Text(mission.description)
                .font(.body)
                .foregroundColor(AppConstants.Colors.textSecondary)
                .fixedSize(horizontal: false, vertical: true)
            
            if let timeRemaining = mission.timeRemaining {
                HStack {
                    Image(systemName: "clock.fill")
                        .foregroundColor(.orange)
                    Text("Còn lại: \(timeRemaining)")
                        .font(.subheadline)
                        .foregroundColor(.orange)
                }
                .padding(.top, 4)
            }
        }
        .padding()
        .background(AppConstants.Colors.surface)
        .cornerRadius(12)
    }
    
    // MARK: - Requirements Section
    private var requirementsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Label("Yêu cầu", systemImage: "checklist")
                .font(.headline)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            VStack(spacing: 12) {
                ForEach(mission.requirements) { requirement in
                    RequirementDetailRow(requirement: requirement)
                }
            }
        }
        .padding()
        .background(AppConstants.Colors.surface)
        .cornerRadius(12)
    }
    
    // MARK: - Rewards Section
    private var rewardsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Label("Phần thưởng", systemImage: "gift.fill")
                .font(.headline)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            HStack(spacing: 20) {
                // XP Reward
                VStack(spacing: 8) {
                    ZStack {
                        Circle()
                            .fill(Color.purple.opacity(0.15))
                            .frame(width: 60, height: 60)
                        
                        VStack(spacing: 2) {
                            Text("+\(mission.xpReward)")
                                .font(.headline)
                                .fontWeight(.bold)
                                .foregroundColor(.purple)
                            Text("XP")
                                .font(.caption2)
                                .foregroundColor(.purple)
                        }
                    }
                    
                    Text("Kinh nghiệm")
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
                
                // Coin Reward
                if let coinReward = mission.coinReward {
                    VStack(spacing: 8) {
                        ZStack {
                            Circle()
                                .fill(Color.orange.opacity(0.15))
                                .frame(width: 60, height: 60)
                            
                            VStack(spacing: 2) {
                                Text("+\(coinReward)")
                                    .font(.headline)
                                    .fontWeight(.bold)
                                    .foregroundColor(.orange)
                                Image(systemName: "bitcoinsign.circle.fill")
                                    .font(.caption)
                                    .foregroundColor(.orange)
                            }
                        }
                        
                        Text("Xu")
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                }
                
                // Badge Reward
                if let badgeReward = mission.badgeReward {
                    VStack(spacing: 8) {
                        ZStack {
                            Circle()
                                .fill(Color.yellow.opacity(0.15))
                                .frame(width: 60, height: 60)
                            
                            Image(systemName: "star.circle.fill")
                                .font(.title)
                                .foregroundColor(.yellow)
                        }
                        
                        Text("Huy hiệu")
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                }
                
                Spacer()
            }
        }
        .padding()
        .background(AppConstants.Colors.surface)
        .cornerRadius(12)
    }
    
    // MARK: - Tips Section
    private var tipsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Label("Mẹo hoàn thành", systemImage: "lightbulb.fill")
                .font(.headline)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            VStack(alignment: .leading, spacing: 8) {
                ForEach(getTipsForMission(), id: \.self) { tip in
                    HStack(alignment: .top, spacing: 8) {
                        Image(systemName: "arrow.right.circle.fill")
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.primary)
                            .padding(.top, 2)
                        
                        Text(tip)
                            .font(.subheadline)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                            .fixedSize(horizontal: false, vertical: true)
                    }
                }
            }
        }
        .padding()
        .background(AppConstants.Colors.surface)
        .cornerRadius(12)
    }
    
    // MARK: - Claim Reward Button
    private var claimRewardButton: some View {
        Button {
            viewModel.claimReward(for: mission.id)
            dismiss()
        } label: {
            HStack {
                Image(systemName: "gift.fill")
                    .font(.headline)
                Text("Nhận thưởng")
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding()
            .background(AppConstants.Colors.primary)
            .cornerRadius(12)
        }
    }
    
    // MARK: - Helper Methods
    private var progressColor: Color {
        switch mission.progressPercentage {
        case 0..<25: return .red
        case 25..<50: return .orange
        case 50..<75: return .yellow
        case 75..<100: return .blue
        default: return .green
        }
    }
    
    private func getTipsForMission() -> [String] {
        var tips: [String] = []
        
        // Category-specific tips
        switch mission.category {
        case .teaching:
            tips.append("Chuẩn bị bài giảng kỹ lưỡng để tiết kiệm thời gian")
            tips.append("Sử dụng công cụ hỗ trợ giảng dạy")
        case .engagement:
            tips.append("Tương tác thường xuyên với sinh viên")
            tips.append("Phản hồi nhanh các câu hỏi")
        case .assessment:
            tips.append("Chấm điểm theo lô để tối ưu thời gian")
            tips.append("Sử dụng rubric để đánh giá công bằng")
        case .innovation:
            tips.append("Thử nghiệm các phương pháp giảng dạy mới")
            tips.append("Tham khảo ý kiến đồng nghiệp")
        case .community:
            tips.append("Tham gia các hoạt động chung của khoa")
            tips.append("Chia sẻ kinh nghiệm với đồng nghiệp")
        case .professional:
            tips.append("Cập nhật kiến thức chuyên môn")
            tips.append("Tham gia các khóa đào tạo")
        }
        
        // Type-specific tips
        if mission.type == .daily {
            tips.append("Hoàn thành sớm trong ngày để nhận thưởng streak")
        } else if mission.type == .weekly {
            tips.append("Lên kế hoạch từ đầu tuần để hoàn thành đúng hạn")
        }
        
        return tips
    }
    
    private func createShareText() -> String {
        """
        🎯 Nhiệm vụ: \(mission.title)
        📝 Mô tả: \(mission.description)
        🏆 Phần thưởng: +\(mission.xpReward) XP
        💪 Độ khó: \(mission.difficulty.displayName)
        📊 Tiến độ: \(Int(mission.progressPercentage))%
        
        Hoàn thành nhiệm vụ để nhận thưởng!
        """
    }
}

// MARK: - Requirement Detail Row
struct RequirementDetailRow: View {
    let requirement: MissionRequirement
    
    var body: some View {
        HStack(spacing: 12) {
            // Icon with completion state
            ZStack {
                Circle()
                    .fill(requirement.isCompleted ? Color.green.opacity(0.15) : AppConstants.Colors.cardBackground)
                    .frame(width: 36, height: 36)
                
                Image(systemName: requirement.type.icon)
                    .font(.system(size: 16))
                    .foregroundColor(requirement.isCompleted ? .green : AppConstants.Colors.textSecondary)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text(requirement.description)
                    .font(.subheadline)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                // Progress Bar
                GeometryReader { geometry in
                    ZStack(alignment: .leading) {
                        RoundedRectangle(cornerRadius: 4)
                            .fill(AppConstants.Colors.cardBackground)
                            .frame(height: 6)
                        
                        RoundedRectangle(cornerRadius: 4)
                            .fill(requirement.isCompleted ? Color.green : AppConstants.Colors.primary)
                            .frame(width: geometry.size.width * (requirement.progressPercentage / 100), height: 6)
                    }
                }
                .frame(height: 6)
            }
            
            // Progress Text
            VStack(alignment: .trailing, spacing: 2) {
                Text("\(requirement.currentValue)/\(requirement.targetValue)")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(requirement.isCompleted ? .green : AppConstants.Colors.textPrimary)
                
                if let unit = requirement.unit {
                    Text(unit)
                        .font(.caption2)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
            }
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Mission Share Sheet
struct MissionShareSheet: UIViewControllerRepresentable {
    let items: [Any]
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        UIActivityViewController(activityItems: items, applicationActivities: nil)
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

// MARK: - Preview
#Preview {
    MissionDetailView(
        mission: Mission.mockMissions[0],
        viewModel: MissionViewModel(userId: "user_1")
    )
}
