//
//  LoginView.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import SwiftUI

struct LoginView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @State private var showRegister = false
    @State private var showForgotPassword = false
    @FocusState private var focusedField: LoginField?
    @State private var animateGradient = false
    @State private var animateElements = false
    @State private var isPasswordVisible = false

    enum LoginField {
        case email, password // Keep email for compatibility, but it's actually username
    }

    var body: some View {
        ZStack {
            // Modern gradient background - ensure it doesn't block touches
            modernGradientBackground
                .ignoresSafeArea(.all) // Extend to full screen
                .allowsHitTesting(false)

            ScrollView {
                VStack(spacing: 0) {
                    // Top section with logo and branding
                    topBrandingSection
                        .frame(minHeight: 350)

                    // Bottom section with form
                    bottomFormSection
                }
            }
            .scrollIndicators(.hidden)
            .allowsHitTesting(true)
        }
        .navigationBarHidden(true)
        .ignoresSafeArea(.all) // Extend to full screen
        .onAppear {
            animateElements = true
            // Auto-focus on username field for testing
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                focusedField = .email
            }
        }
        .alert("Error", isPresented: $authViewModel.showError) {
            Button("OK") {
                authViewModel.hideError()
            }
        } message: {
            Text(authViewModel.errorMessage ?? "An error occurred")
        }
        .sheet(isPresented: $showRegister) {
            RegisterView()
        }
        .sheet(isPresented: $showForgotPassword) {
            ForgotPasswordView()
        }
    }

    // MARK: - Modern Gradient Background
    private var modernGradientBackground: some View {
        ZStack {
            // Base gradient - vibrant and modern
            LinearGradient(
                colors: [
                    Color(red: 0.1, green: 0.1, blue: 0.2),        // Deep blue
                    Color(red: 0.2, green: 0.1, blue: 0.3),        // Purple
                    Color(red: 0.3, green: 0.2, blue: 0.4),        // Lighter purple
                    Color(red: 0.1, green: 0.2, blue: 0.3)         // Teal
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )

            // Animated overlay circles - ensure they don't block touches
            Circle()
                .fill(
                    RadialGradient(
                        colors: [
                            Color(red: 1.0, green: 0.6, blue: 0.0).opacity(0.3),
                            Color.clear
                        ],
                        center: .center,
                        startRadius: 50,
                        endRadius: 300
                    )
                )
                .frame(width: 400, height: 400)
                .offset(
                    x: animateGradient ? 100 : -100,
                    y: animateGradient ? -200 : -300
                )
                .animation(.easeInOut(duration: 8).repeatForever(autoreverses: true), value: animateGradient)
                .allowsHitTesting(false)

            Circle()
                .fill(
                    RadialGradient(
                        colors: [
                            Color(red: 0.0, green: 0.8, blue: 1.0).opacity(0.2),
                            Color.clear
                        ],
                        center: .center,
                        startRadius: 30,
                        endRadius: 200
                    )
                )
                .frame(width: 300, height: 300)
                .offset(
                    x: animateGradient ? -150 : 150,
                    y: animateGradient ? 300 : 400
                )
                .animation(.easeInOut(duration: 10).repeatForever(autoreverses: true), value: animateGradient)
                .allowsHitTesting(false)
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 8).repeatForever(autoreverses: true)) {
                animateGradient.toggle()
            }
        }
    }

    // MARK: - Top Branding Section
    private var topBrandingSection: some View {
        VStack(spacing: 0) {
            Spacer()

            // Modern logo design
            ZStack {
                // Outer glow
                Circle()
                    .fill(
                        RadialGradient(
                            colors: [
                                Color.white.opacity(0.1),
                                Color.clear
                            ],
                            center: .center,
                            startRadius: 60,
                            endRadius: 120
                        )
                    )
                    .frame(width: 140, height: 140)
                    .scaleEffect(animateGradient ? 1.1 : 1.0)
                    .animation(.easeInOut(duration: 3).repeatForever(autoreverses: true), value: animateGradient)

                // Main logo circle
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [
                                Color.white,
                                Color(red: 0.95, green: 0.95, blue: 1.0)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 100, height: 100)
                    .shadow(color: Color.black.opacity(0.2), radius: 20, x: 0, y: 10)

                // Logo icon
                Image(systemName: "link")
                    .font(.system(size: 45, weight: .bold))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [
                                Color(red: 0.2, green: 0.1, blue: 0.3),
                                Color(red: 0.3, green: 0.2, blue: 0.4)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
            }
            .padding(.bottom, 40)

            // App name and tagline
            VStack(spacing: 12) {
                Text(AppConstants.appName)
                    .font(.beVietnamPro(.bold, size: 48))
                    .foregroundColor(.white)
                    .tracking(2)

                Text(AppConstants.appTagline)
                    .font(AppConstants.Typography.title3)
                    .foregroundColor(.white.opacity(0.8))
                    .tracking(1)
            }

            Spacer()
        }
        .padding(.horizontal, 40)
    }
    // MARK: - Bottom Form Section
    private var bottomFormSection: some View {
        VStack(spacing: 0) {
            // White rounded container
            VStack(spacing: 32) {
                // Demo Banner - Hidden for production
                // VStack(spacing: 8) {
                //     HStack {
                //         Image(systemName: "info.circle.fill")
                //             .foregroundColor(.blue)
                //         Text("Demo Mode")
                //             .font(.caption)
                //             .fontWeight(.medium)
                //         Spacer()
                //     }
                //     .padding(.horizontal, 16)
                //     .padding(.vertical, 8)
                //     .background(Color.blue.opacity(0.1))
                //     .cornerRadius(8)
                //
                //     Text("Auto-filling demo credentials...")
                //         .font(.caption2)
                //         .foregroundColor(.secondary)
                // }
                // .padding(.top, 20)

                // Header
                VStack(spacing: 8) {
                    Text("Welcome Back")
                        .font(.beVietnamPro(.bold, size: 32))
                        .foregroundColor(.primary)

                    Text("Sign in to continue")
                        .font(AppConstants.Typography.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding(.top, 20)

                // Form fields
                modernFormSection

                // Action buttons
                modernActionSection

                // Register section
                modernRegisterSection
            }
            .background(
                RoundedRectangle(cornerRadius: 40)
                    .fill(Color.white) // White background
                    .shadow(color: Color.black.opacity(0.1), radius: 30, x: 0, y: -10)
            )
            .padding(.horizontal, 20)
        }
    }

    // MARK: - Modern Form Section
    private var modernFormSection: some View {
        VStack(spacing: 24) {
            // Email field
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Image(systemName: "person")
                        .foregroundColor(.secondary)
                        .frame(width: 20)

                    TextField("Username", text: $authViewModel.loginEmail)
                        .font(AppConstants.Typography.body)
                        .textContentType(.username)
                        .keyboardType(.default)
                        .autocapitalization(.none)
                        .focused($focusedField, equals: .email)
                        .onChange(of: authViewModel.loginEmail) { newValue in
                            print("🔐 LoginView: Email changed to: '\(newValue)'")
                            print("🔐 LoginView: Email isValid: \(newValue.isValidEmail)")
                        }
                        .onTapGesture {
                            focusedField = .email
                        }
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(.systemGray6))
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(focusedField == .email ? Color.blue : Color.clear, lineWidth: 2)
                        )
                )
                .allowsHitTesting(true)
                .onTapGesture {
                    focusedField = .email
                }

                if let error = authViewModel.validateEmail(authViewModel.loginEmail), !authViewModel.loginEmail.isEmpty {
                    Text(error)
                        .font(.caption)
                        .foregroundColor(.red)
                        .padding(.leading, 4)
                }
            }

            // Password field
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Image(systemName: "lock")
                        .foregroundColor(.secondary)
                        .frame(width: 20)

                    if isPasswordVisible {
                        TextField("Password", text: $authViewModel.loginPassword)
                            .font(AppConstants.Typography.body)
                            .textContentType(.password)
                            .autocapitalization(.none)
                            .autocorrectionDisabled()
                            .focused($focusedField, equals: .password)
                            .onTapGesture {
                                focusedField = .password
                            }
                    } else {
                        SecureField("Password", text: $authViewModel.loginPassword)
                            .font(AppConstants.Typography.body)
                            .textContentType(.password)
                            .autocapitalization(.none)
                            .autocorrectionDisabled()
                            .focused($focusedField, equals: .password)
                            .onChange(of: authViewModel.loginPassword) { newValue in
                                print("🔐 LoginView: Password changed to: '\(newValue)'")
                                print("🔐 LoginView: Password isValid: \(newValue.isValidPassword)")
                                print("🔐 LoginView: Form isValid: \(authViewModel.isLoginFormValid)")
                            }
                            .onTapGesture {
                                focusedField = .password
                            }
                    }

                    Button(action: {
                        isPasswordVisible.toggle()
                    }) {
                        Image(systemName: isPasswordVisible ? "eye.slash" : "eye")
                            .foregroundColor(.secondary)
                            .frame(width: 20)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(.systemGray6))
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(focusedField == .password ? Color.blue : Color.clear, lineWidth: 2)
                        )
                )
                .allowsHitTesting(true)
                .onTapGesture {
                    focusedField = .password
                }

                if let error = authViewModel.validatePassword(authViewModel.loginPassword), !authViewModel.loginPassword.isEmpty {
                    Text(error)
                        .font(AppConstants.Typography.caption)
                        .foregroundColor(.red)
                        .padding(.leading, 4)
                }
            }

            // Remember me and forgot password
            HStack {
                Button(action: {
                    authViewModel.rememberMe.toggle()
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: authViewModel.rememberMe ? "checkmark.circle.fill" : "circle")
                            .foregroundColor(authViewModel.rememberMe ? .blue : .secondary)
                            .animation(.none, value: authViewModel.rememberMe)

                        Text("Remember me")
                            .font(.subheadline)
                            .foregroundColor(.primary)
                            .animation(.none, value: authViewModel.rememberMe)
                    }
                }
                .animation(.none, value: authViewModel.rememberMe)

                Spacer()

                Button("Forgot Password?") {
                    showForgotPassword = true
                }
                .font(.subheadline)
                .foregroundColor(.blue)
            }
        }
        .padding(.horizontal, 32)
    }
    // MARK: - Modern Action Section
    private var modernActionSection: some View {
        VStack(spacing: 20) {
            // Sign in button
            Button(action: {
                print("🔐 LoginView: Sign in button tapped")
                Task {
                    print("🔐 LoginView: About to call authViewModel.login()")
                    await authViewModel.login()
                    print("🔐 LoginView: authViewModel.login() completed")
                }
            }) {
                HStack(spacing: 12) {
                    if authViewModel.isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.9)
                    }

                    Text(authViewModel.isLoading ? "Signing In..." : "Sign In")
                        .font(.beVietnamPro(.semiBold, size: 18))
                        .foregroundColor(.white)
                }
                .frame(maxWidth: .infinity)
                .frame(height: 56)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color.blue,
                                    Color.blue.opacity(0.8)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .shadow(color: Color.blue.opacity(0.3), radius: 15, x: 0, y: 8)
                )
                .scaleEffect(authViewModel.isLoading ? 0.98 : 1.0)
                .animation(.easeInOut(duration: 0.1), value: authViewModel.isLoading)
            }
            .disabled(!authViewModel.isLoginFormValid || authViewModel.isLoading)
            .opacity(authViewModel.isLoginFormValid ? 1.0 : 0.6)
            .onTapGesture {
                print("🔐 LoginView: Button tap gesture detected")
                print("🔐 LoginView: isLoginFormValid = \(authViewModel.isLoginFormValid)")
                print("🔐 LoginView: isLoading = \(authViewModel.isLoading)")
            }
        }
        .padding(.horizontal, 32)
    }

    // MARK: - Modern Register Section
    private var modernRegisterSection: some View {
        VStack(spacing: 16) {
            // Divider
            HStack {
                Rectangle()
                    .frame(height: 1)
                    .foregroundColor(Color(.systemGray4))

                Text("New to Vantis Instructor?")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 16)

                Rectangle()
                    .frame(height: 1)
                    .foregroundColor(Color(.systemGray4))
            }

            // Create account button
            Button("Create Account") {
                showRegister = true
            }
            .font(.beVietnamPro(.semiBold, size: 16))
            .foregroundColor(.blue)
            .frame(maxWidth: .infinity)
            .frame(height: 56)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemGray6))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .padding(.horizontal, 32)
        .padding(.bottom, 40)
    }



    // MARK: - Helper Functions
    private func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }

    // MARK: - Demo Credentials (Disabled)
    // Uncomment the function below if you want to auto-fill demo credentials for testing
    /*
    private func setupDemoCredentials() {
        print("🔐 LoginView: Setting up instructor demo credentials")
        // Auto-fill instructor credentials for easy testing
        authViewModel.loginEmail = "admin"
        authViewModel.loginPassword = "earnbase@2025"
        print("🔐 LoginView: Instructor credentials set - Username: \(authViewModel.loginEmail), Password: \(authViewModel.loginPassword)")
    }
    */

        // MARK: - Auto-login (Disabled)
        // Uncomment the code below if you want auto-login for testing
        /*
        // Auto-login after a short delay for better UX
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            print("🔐 LoginView: Starting auto-login...")
            Task {
                await authViewModel.login()
                print("🔐 LoginView: Auto-login completed")
            }
        }
        */
    }

// MARK: - Preview
#Preview {
    LoginView()
        .environmentObject(AuthViewModel())
}
