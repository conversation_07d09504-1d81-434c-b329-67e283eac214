//
//  RegisterView.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import SwiftUI

struct RegisterView: View {
    @StateObject private var authViewModel = AuthViewModel()
    @Environment(\.dismiss) private var dismiss
    @FocusState private var focusedField: RegisterField?
    @State private var isPasswordVisible = false
    @State private var isConfirmPasswordVisible = false

    enum RegisterField {
        case firstName, lastName, email, phone, password, confirmPassword
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: AppConstants.UI.sectionSpacing) {
                    // Header
                    headerSection
                    
                    // Register Form
                    registerFormSection
                    
                    // Terms and Privacy
                    termsSection
                    
                    // Register Button
                    registerButtonSection
                    
                    Spacer(minLength: 50)
                }
                .padding(.horizontal, AppConstants.UI.screenPadding)
                .padding(.top, 20)
            }
            .navigationTitle("Create Account")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
            }
            .background(AppConstants.Colors.background.ignoresSafeArea())
            .onTapGesture {
                hideKeyboard()
            }
        }
        .alert("Error", isPresented: $authViewModel.showError) {
            Button("OK") {
                authViewModel.hideError()
            }
        } message: {
            Text(authViewModel.errorMessage ?? "An error occurred")
        }
        .onChange(of: authViewModel.isAuthenticated) { isAuthenticated in
            if isAuthenticated {
                dismiss()
            }
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 12) {
            Image(systemName: "person.badge.plus")
                .font(.system(size: 60))
                .foregroundColor(AppConstants.Colors.primary)
            
            Text("Join Vantis Instructor Community")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Text("Start earning tokens and redeeming rewards")
                .font(.subheadline)
                .foregroundColor(AppConstants.Colors.textSecondary)
                .multilineTextAlignment(.center)
        }
    }
    
    // MARK: - Register Form Section
    private var registerFormSection: some View {
        VStack(spacing: AppConstants.UI.itemSpacing) {
            // Name Fields
            HStack(spacing: 12) {
                // First Name
                VStack(alignment: .leading, spacing: 8) {
                    Text("First Name")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    TextField("First name", text: $authViewModel.firstName)
                        .textFieldStyle()
                        .textContentType(.givenName)
                        .focused($focusedField, equals: .firstName)
                        .onSubmit {
                            focusedField = .lastName
                        }
                    
                    if let error = authViewModel.validateName(authViewModel.firstName, fieldName: "First name"), !authViewModel.firstName.isEmpty {
                        Text(error)
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.error)
                    }
                }
                
                // Last Name
                VStack(alignment: .leading, spacing: 8) {
                    Text("Last Name")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    TextField("Last name", text: $authViewModel.lastName)
                        .textFieldStyle()
                        .textContentType(.familyName)
                        .focused($focusedField, equals: .lastName)
                        .onSubmit {
                            focusedField = .email
                        }
                    
                    if let error = authViewModel.validateName(authViewModel.lastName, fieldName: "Last name"), !authViewModel.lastName.isEmpty {
                        Text(error)
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.error)
                    }
                }
            }
            
            // Email Field
            VStack(alignment: .leading, spacing: 8) {
                Text("Email")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                TextField("Enter your email", text: $authViewModel.registerEmail)
                    .textFieldStyle()
                    .keyboardType(.emailAddress)
                    .textContentType(.emailAddress)
                    .autocapitalization(.none)
                    .focused($focusedField, equals: .email)
                    .onSubmit {
                        focusedField = .phone
                    }
                
                if let error = authViewModel.validateEmail(authViewModel.registerEmail), !authViewModel.registerEmail.isEmpty {
                    Text(error)
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.error)
                }
            }
            
            // Phone Field (Optional)
            VStack(alignment: .leading, spacing: 8) {
                Text("Phone (Optional)")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                TextField("Enter your phone number", text: $authViewModel.phone)
                    .textFieldStyle()
                    .keyboardType(.phonePad)
                    .textContentType(.telephoneNumber)
                    .focused($focusedField, equals: .phone)
                    .onSubmit {
                        focusedField = .password
                    }
                
                if let error = authViewModel.validatePhone(authViewModel.phone), !authViewModel.phone.isEmpty {
                    Text(error)
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.error)
                }
            }
            
            // Password Field
            VStack(alignment: .leading, spacing: 8) {
                Text("Password")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(AppConstants.Colors.textPrimary)

                HStack {
                    if isPasswordVisible {
                        TextField("Create a password", text: $authViewModel.registerPassword)
                            .textFieldStyle()
                            .textContentType(.newPassword)
                            .autocapitalization(.none)
                            .autocorrectionDisabled()
                            .focused($focusedField, equals: .password)
                    } else {
                        SecureField("Create a password", text: $authViewModel.registerPassword)
                            .textFieldStyle()
                            .textContentType(.newPassword)
                            .autocapitalization(.none)
                            .autocorrectionDisabled()
                            .focused($focusedField, equals: .password)
                    }

                    Button(action: {
                        isPasswordVisible.toggle()
                    }) {
                        Image(systemName: isPasswordVisible ? "eye.slash" : "eye")
                            .foregroundColor(.secondary)
                            .frame(width: 20)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                .onSubmit {
                    focusedField = .confirmPassword
                }
                
                if let error = authViewModel.validatePassword(authViewModel.registerPassword), !authViewModel.registerPassword.isEmpty {
                    Text(error)
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.error)
                }
            }
            
            // Confirm Password Field
            VStack(alignment: .leading, spacing: 8) {
                Text("Confirm Password")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(AppConstants.Colors.textPrimary)

                HStack {
                    if isConfirmPasswordVisible {
                        TextField("Confirm your password", text: $authViewModel.confirmPassword)
                            .textFieldStyle()
                            .textContentType(.newPassword)
                            .autocapitalization(.none)
                            .autocorrectionDisabled()
                            .focused($focusedField, equals: .confirmPassword)
                    } else {
                        SecureField("Confirm your password", text: $authViewModel.confirmPassword)
                            .textFieldStyle()
                            .textContentType(.newPassword)
                            .autocapitalization(.none)
                            .autocorrectionDisabled()
                            .focused($focusedField, equals: .confirmPassword)
                    }

                    Button(action: {
                        isConfirmPasswordVisible.toggle()
                    }) {
                        Image(systemName: isConfirmPasswordVisible ? "eye.slash" : "eye")
                            .foregroundColor(.secondary)
                            .frame(width: 20)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                .onSubmit {
                    Task {
                        await authViewModel.register()
                    }
                }
                
                if let error = authViewModel.validateConfirmPassword(), !authViewModel.confirmPassword.isEmpty {
                    Text(error)
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.error)
                }
            }
        }
        .cardStyle()
        .padding(.horizontal, 4)
    }
    
    // MARK: - Terms Section
    private var termsSection: some View {
        Button(action: {
            authViewModel.acceptTerms.toggle()
        }) {
            HStack(alignment: .top, spacing: 12) {
                Image(systemName: authViewModel.acceptTerms ? "checkmark.square.fill" : "square")
                    .foregroundColor(authViewModel.acceptTerms ? AppConstants.Colors.primary : AppConstants.Colors.textSecondary)
                    .font(.title3)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("I agree to the Terms of Service and Privacy Policy")
                        .font(.subheadline)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                        .multilineTextAlignment(.leading)
                    
                    HStack(spacing: 4) {
                        Button("Terms of Service") {
                            // TODO: Show terms
                        }
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.primary)
                        
                        Text("and")
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                        
                        Button("Privacy Policy") {
                            // TODO: Show privacy policy
                        }
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.primary)
                    }
                }
                
                Spacer()
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Register Button Section
    private var registerButtonSection: some View {
        VStack(spacing: 16) {
            Button(action: {
                Task {
                    await authViewModel.register()
                }
            }) {
                HStack {
                    if authViewModel.isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.8)
                    }
                    
                    Text(authViewModel.isLoading ? "Creating Account..." : "Create Account")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .frame(maxWidth: .infinity)
            }
            .primaryButtonStyle()
            .disabled(!authViewModel.isRegisterFormValid || authViewModel.isLoading)
            .opacity(authViewModel.isRegisterFormValid ? 1.0 : 0.6)
            
            HStack {
                Text("Already have an account?")
                    .font(.subheadline)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                
                Button("Sign In") {
                    dismiss()
                }
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.primary)
            }
        }
    }
}

// MARK: - Preview
#Preview {
    RegisterView()
}
