//
//  AuthViewModel.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import Foundation
import SwiftUI
import Combine

@MainActor
class AuthViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var isAuthenticated = false
    @Published var currentUser: User?
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var showError = false
    
    // Login form
    @Published var loginEmail = ""
    @Published var loginPassword = ""
    @Published var rememberMe = false
    
    // Register form
    @Published var registerEmail = ""
    @Published var registerPassword = ""
    @Published var confirmPassword = ""
    @Published var firstName = ""
    @Published var lastName = ""
    @Published var phone = ""
    @Published var acceptTerms = false
    
    // Biometric
    @Published var biometricEnabled = false
    @Published var biometricType = ""
    
    // MARK: - Dependencies
    private let loginService = LoginService.shared
    private let keychainManager = KeychainManager.shared
    private let authStateManager = AuthenticationStateManager.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Computed Properties
    var isLoginFormValid: Bool {
        return loginEmail.isValidEmail && loginPassword.isValidPassword
    }
    
    var isRegisterFormValid: Bool {
        return registerEmail.isValidEmail &&
               registerPassword.isValidPassword &&
               confirmPassword == registerPassword &&
               !firstName.isEmpty &&
               !lastName.isEmpty &&
               acceptTerms
    }
    
    var passwordsMatch: Bool {
        return registerPassword == confirmPassword
    }
    
    // MARK: - Initialization
    init() {
        checkBiometricAvailability()
        loadSavedCredentials()
        setupAuthStateObserver()
        checkAuthState()
    }

    private func setupAuthStateObserver() {
        // Sync with AuthenticationStateManager
        authStateManager.$authenticationState
            .receive(on: DispatchQueue.main)
            .sink { [weak self] state in
                self?.isAuthenticated = state.isAuthenticated
            }
            .store(in: &cancellables)

        authStateManager.$currentUser
            .receive(on: DispatchQueue.main)
            .sink { [weak self] user in
                self?.currentUser = user
            }
            .store(in: &cancellables)
    }
    

    
    private func checkBiometricAvailability() {
        biometricEnabled = keychainManager.isBiometricAvailable()
        biometricType = keychainManager.biometricTypeString()
    }
    
    private func loadSavedCredentials() {
        if rememberMe {
            // Load saved email if remember me was enabled
            loginEmail = UserDefaults.standard.string(forKey: "saved_email") ?? ""
        }
    }
    
    // MARK: - Authentication Actions
    func login() async {
        print("🔐 AuthViewModel: login() called with username: \(loginEmail)")
        print("🔐 AuthViewModel: isLoginFormValid = \(isLoginFormValid)")

        guard isLoginFormValid else {
            print("🔐 AuthViewModel: Login form is invalid")
            showErrorMessage("Vui lòng kiểm tra tên đăng nhập và mật khẩu")
            return
        }

        isLoading = true
        errorMessage = nil
        showError = false

        print("🔐 AuthViewModel: Using AuthenticationStateManager for login")
        do {
            // Use AuthenticationStateManager for centralized authentication
            try await authStateManager.login(username: loginEmail, password: loginPassword)
            print("🔐 AuthViewModel: Login completed successfully via AuthenticationStateManager")

            // State will be updated automatically via observers
            // No need to manually update isAuthenticated and currentUser

            // Save credentials if remember me is enabled
            if rememberMe {
                UserDefaults.standard.set(loginEmail, forKey: "saved_email")

                // Optionally save to keychain for biometric login
                if biometricEnabled {
                    try? keychainManager.saveBiometric(
                        key: "saved_credentials",
                        string: "\(loginEmail):\(loginPassword)",
                        reason: "Save credentials for quick login"
                    )
                }
            }

            clearLoginForm()
            print("🔐 AuthViewModel: Login process completed")

        } catch {
            print("🔐 AuthViewModel: Login failed with error: \(error)")
            showErrorMessage(error.localizedDescription)
        }

        isLoading = false
    }
    
    func register() async {
        showErrorMessage("Đăng ký không khả dụng. Vui lòng liên hệ quản trị viên.")
    }
    
    func loginWithBiometric() async {
        guard biometricEnabled else {
            showErrorMessage("Biometric authentication not available")
            return
        }
        
        do {
            let credentials = try await keychainManager.loadBiometricString(
                key: "saved_credentials",
                reason: "Login with \(biometricType)"
            )
            
            let components = credentials.components(separatedBy: ":")
            guard components.count == 2 else {
                showErrorMessage("Invalid saved credentials")
                return
            }
            
            loginEmail = components[0]
            loginPassword = components[1]
            
            await login()
            
        } catch {
            if let keychainError = error as? KeychainError {
                switch keychainError {
                case .userCancel:
                    // User cancelled, don't show error
                    break
                case .biometricLockout:
                    showErrorMessage("Biometric authentication is locked. Please use your passcode.")
                case .authenticationFailed:
                    showErrorMessage("Biometric authentication failed")
                default:
                    showErrorMessage(keychainError.localizedDescription)
                }
            } else {
                showErrorMessage("Biometric login failed")
            }
        }
    }
    
    func logout() async {
        print("🔐 AuthViewModel: logout() called")

        // Use AuthenticationStateManager for centralized logout
        await authStateManager.logout()

        // State will be updated automatically via observers
        // No need to manually update isAuthenticated and currentUser

        clearAllForms()

        // Clear saved credentials if needed
        if !rememberMe {
            UserDefaults.standard.removeObject(forKey: "saved_email")
            try? keychainManager.delete(key: "saved_credentials")
        }

        print("🔐 AuthViewModel: logout completed")
    }

    func checkAuthState() {
        print("🔐 AuthViewModel: Checking authentication state")

        // Check if we have a valid token and user
        if TokenManager.shared.getToken() != nil,
           let user = TokenManager.shared.getUser(User.self) {
            print("🔐 AuthViewModel: Found saved token and user")
            isAuthenticated = true
            currentUser = user
        } else {
            print("🔐 AuthViewModel: No saved token or user found")
            isAuthenticated = false
            currentUser = nil
        }
    }

    func refreshUserData() async {
        print("🔐 AuthViewModel: Refresh user data requested")
        checkAuthState()
    }
    
    // MARK: - Form Management
    func clearLoginForm() {
        loginPassword = ""
        if !rememberMe {
            loginEmail = ""
        }
    }
    
    func clearRegisterForm() {
        registerEmail = ""
        registerPassword = ""
        confirmPassword = ""
        firstName = ""
        lastName = ""
        phone = ""
        acceptTerms = false
    }
    
    func clearAllForms() {
        clearLoginForm()
        clearRegisterForm()
        loginEmail = ""
    }
    
    // MARK: - Error Handling
    private func showErrorMessage(_ message: String) {
        errorMessage = message
        showError = true
        
        // Auto-hide error after 3 seconds
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            self.hideError()
        }
    }
    
    func hideError() {
        showError = false
        errorMessage = nil
    }
    
    // MARK: - Validation Helpers
    func validateEmail(_ email: String) -> String? {
        if email.isEmpty {
            return "Email is required"
        } else if !email.isValidEmail {
            return "Please enter a valid email address"
        }
        return nil
    }
    
    func validatePassword(_ password: String) -> String? {
        if password.isEmpty {
            return "Password is required"
        } else if !password.isValidPassword {
            return "Password must be at least \(AppConstants.Validation.minPasswordLength) characters"
        }
        return nil
    }
    
    func validateConfirmPassword() -> String? {
        if confirmPassword.isEmpty {
            return "Please confirm your password"
        } else if !passwordsMatch {
            return "Passwords do not match"
        }
        return nil
    }
    
    func validateName(_ name: String, fieldName: String) -> String? {
        if name.isEmpty {
            return "\(fieldName) is required"
        } else if name.count < AppConstants.Validation.minNameLength {
            return "\(fieldName) must be at least \(AppConstants.Validation.minNameLength) characters"
        }
        return nil
    }
    
    func validatePhone(_ phone: String) -> String? {
        if !phone.isEmpty && !phone.isValidPhone {
            return "Please enter a valid phone number"
        }
        return nil
    }
    
    // MARK: - Biometric Settings
    func toggleBiometricLogin() {
        biometricEnabled.toggle()
        UserDefaults.standard.set(biometricEnabled, forKey: AppConstants.StorageKeys.biometricEnabled)
        
        if !biometricEnabled {
            // Clear saved biometric credentials
            try? keychainManager.delete(key: "saved_credentials")
        }
    }
    
    // MARK: - Social Login (Future Implementation)
    func loginWithGoogle() async {
        // TODO: Implement Google Sign-In
        showErrorMessage("Google Sign-In coming soon")
    }
    
    func loginWithApple() async {
        // TODO: Implement Apple Sign-In
        showErrorMessage("Apple Sign-In coming soon")
    }
    
    func loginWithFacebook() async {
        // TODO: Implement Facebook Login
        showErrorMessage("Facebook Login coming soon")
    }

    // MARK: - Helper Methods
    // TEMPORARILY DISABLED - InstructorUser not available
    // private func convertInstructorToUser(_ instructor: InstructorUser) -> User {
    //     let dateFormatter = ISO8601DateFormatter()
    //
    //     return User(
    //         id: String(instructor.id),
    //         email: instructor.email,
    //         firstName: instructor.firstName,
    //         lastName: instructor.lastName,
    //         phone: instructor.phone,
    //         walletAddress: nil,
    //         role: .admin, // Map instructor to admin role
    //         isActive: instructor.isActive,
    //         avatar: instructor.avatar,
    //         dateOfBirth: nil,
    //         lastLoginAt: instructor.lastLoginAt != nil ? dateFormatter.date(from: instructor.lastLoginAt!) : nil,
    //         createdAt: dateFormatter.date(from: instructor.createdAt) ?? Date(),
    //         updatedAt: instructor.updatedAt != nil ? dateFormatter.date(from: instructor.updatedAt!) : nil,
    //         businessName: nil,
    //         businessId: nil,
    //         category: nil,
    //         businessPhone: nil,
    //         website: nil,
    //         businessDescription: nil,
    //         businessStatus: nil,
    //         onboardedAt: nil
    //     )
    // }
}
