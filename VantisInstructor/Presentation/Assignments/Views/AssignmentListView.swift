import SwiftUI

struct AssignmentListView: View {
    @StateObject private var assignmentManager = AssignmentManager()
    @Environment(\.dismiss) private var dismiss
    @Environment(\.presentationMode) var presentationMode
    @State private var searchText = ""
    @State private var showingCreateAssignment = false
    @State private var selectedAssignment: Assignment?
    @State private var showingAssignmentDetail = false
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 0) {
                // Modern Header Section
                modernHeaderSection

                // Stats Section
                statsSection

                // Search and filters
                searchAndFilterSection

                // Content
                assignmentContentSection
            }
            .padding(.horizontal, 20)
            .padding(.top, 16)
        }
        .navigationBarHidden(true)
        .background(
            Color.white
                .ignoresSafeArea()
        )
        .sheet(isPresented: $showingCreateAssignment) {
            CreateAssignmentView()
        }
        .sheet(isPresented: $showingAssignmentDetail) {
            if let selectedAssignment = selectedAssignment {
                Text("Assignment Detail - Coming Soon")
                    .font(.title2)
                    .padding()
            }
        }
        .task {
            await assignmentManager.fetchAssignments()
        }
    }
    
    // MARK: - Modern Header Section
    private var modernHeaderSection: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("Bài tập")
                    .font(.beVietnamPro(.bold, size: 24))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Text("Quản lý và chấm điểm bài tập")
                    .font(.beVietnamPro(.medium, size: 14))
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }

            Spacer()

            Button(action: {
                showingCreateAssignment = true
            }) {
                HStack(spacing: 6) {
                    Image(systemName: "plus")
                        .font(.system(size: 14, weight: .medium))
                    Text("Tạo mới")
                        .font(.beVietnamPro(.medium, size: 14))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [AppConstants.Colors.primary, AppConstants.Colors.primaryDark]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .cornerRadius(20)
                .shadow(color: AppConstants.Colors.primary.opacity(0.3), radius: 8, x: 0, y: 4)
            }
        }
        .padding(.bottom, 24)
    }

    // MARK: - Stats Section
    private var statsSection: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 16) {
                AssignmentStatCard(
                    title: "Tổng số",
                    value: "\(assignmentManager.assignments.count)",
                    subtitle: "bài tập",
                    color: .blue,
                    icon: "doc.text"
                )
                
                AssignmentStatCard(
                    title: "Cần chấm",
                    value: "\(assignmentManager.totalPendingGrades)",
                    subtitle: "bài nộp",
                    color: .orange,
                    icon: "doc.text.magnifyingglass"
                )
                
                AssignmentStatCard(
                    title: "Quá hạn",
                    value: "\(assignmentManager.overdueAssignments.count)",
                    subtitle: "bài tập",
                    color: .red,
                    icon: "clock.badge.exclamationmark"
                )
                
                AssignmentStatCard(
                    title: "Hoàn thành",
                    value: String(format: "%.1f%%", assignmentManager.averageGradingProgress),
                    subtitle: "tỷ lệ",
                    color: .green,
                    icon: "checkmark.circle"
                )
            }
        }
        .padding(.bottom, 24)
    }
    
    // MARK: - Search and Filter Section
    private var searchAndFilterSection: some View {
        VStack(spacing: 16) {
            // Search bar
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(AppConstants.Colors.textSecondary)
                
                TextField("Tìm kiếm bài tập...", text: $searchText)
                    .textFieldStyle(PlainTextFieldStyle())
                
                if !searchText.isEmpty {
                    Button(action: { searchText = "" }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(AppConstants.Colors.surface)
            .cornerRadius(12)
        }
        .padding(.horizontal)
        .padding(.bottom, 8)
    }
    
    // MARK: - Assignment Content Section
    private var assignmentContentSection: some View {
        LazyVStack(spacing: 12) {
            ForEach(filteredAssignments) { assignment in
                AssignmentCard(
                    assignment: assignment,
                    onTap: {
                        selectedAssignment = assignment
                        showingAssignmentDetail = true
                    },
                    onGrade: {
                        // Navigate to grading view
                    }
                )
            }

            Spacer(minLength: 80)
        }
    }
    
    // MARK: - Computed Properties
    private var filteredAssignments: [Assignment] {
        var assignments = assignmentManager.assignments
        
        // Apply search filter
        if !searchText.isEmpty {
            assignments = assignments.filter { assignment in
                assignment.title.localizedCaseInsensitiveContains(searchText) ||
                assignment.description.localizedCaseInsensitiveContains(searchText) ||
                assignment.title.localizedCaseInsensitiveContains(searchText)
            }
        }
        
        return assignments
    }
}

#Preview {
    AssignmentListView()
}
