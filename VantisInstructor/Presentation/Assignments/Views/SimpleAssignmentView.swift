import SwiftUI

struct SimpleAssignmentView: View {
    var body: some View {
        VStack(spacing: 20) {
            // Header
            VStack(spacing: 8) {
                Image(systemName: "list.clipboard.fill")
                    .font(.system(size: 60))
                    .foregroundColor(AppConstants.Colors.primary)

                Text("Bài tập")
                    .font(AppConstants.Typography.largeTitle)
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Text("Quản lý và chấm điểm bài tập của học sinh")
                    .font(AppConstants.Typography.subheadline)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .multilineTextAlignment(.center)
            }
            .padding(.top, 40)
            
            // Stats Cards
            VStack(spacing: 16) {
                HStack(spacing: 16) {
                    AssignmentStatCard(
                        title: "Tổng số",
                        value: "12",
                        subtitle: "bài tập",
                        color: .blue,
                        icon: "doc.text"
                    )

                    AssignmentStatCard(
                        title: "Cần chấm",
                        value: "8",
                        subtitle: "bài nộp",
                        color: .orange,
                        icon: "pencil.circle"
                    )
                }

                HStack(spacing: 16) {
                    AssignmentStatCard(
                        title: "Hoàn thành",
                        value: "4",
                        subtitle: "bài tập",
                        color: .green,
                        icon: "checkmark.circle"
                    )

                    AssignmentStatCard(
                        title: "Quá hạn",
                        value: "2",
                        subtitle: "bài tập",
                        color: .red,
                        icon: "clock.badge.exclamationmark"
                    )
                }
            }
            .padding(.horizontal, 20)
            
            // Action Buttons
            VStack(spacing: 12) {
                Button(action: {
                    print("🎯 Tạo bài tập mới clicked!")
                }) {
                    HStack {
                        Image(systemName: "plus.circle.fill")
                        Text("Tạo bài tập mới")
                    }
                    .font(AppConstants.Typography.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(AppConstants.Colors.primary)
                    .cornerRadius(12)
                }
                
                Button(action: {
                    print("🎯 Xem tất cả bài tập clicked!")
                }) {
                    HStack {
                        Image(systemName: "list.bullet")
                        Text("Xem tất cả bài tập")
                    }
                    .font(AppConstants.Typography.headline)
                    .foregroundColor(AppConstants.Colors.primary)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(AppConstants.Colors.primary.opacity(0.1))
                    .cornerRadius(12)
                }
            }
            .padding(.horizontal, 20)
            
            Spacer()
        }
        .background(AppConstants.Colors.background.ignoresSafeArea())
        .onAppear {
            print("🎯 SimpleAssignmentView appeared!")
        }
    }
}



#Preview {
    SimpleAssignmentView()
}
