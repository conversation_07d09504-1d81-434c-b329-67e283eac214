//
//  GradingView.swift
//  mobile-app-template
//
//  Created by In<PERSON><PERSON><PERSON> App on 23/7/25.
//

import SwiftUI

// TEMPORARILY COMMENTED OUT DUE TO BUILD ERRORS
/*
struct GradingView: View {
    let assignment: Assignment
    @StateObject private var assignmentManager = AssignmentManager()
    @Environment(\.dismiss) private var dismiss
    
    @State private var currentSubmissionIndex = 0
    @State private var currentScore = 0.0
    @State private var feedback = ""
    @State private var rubricScores: [String: Double] = [:]
    @State private var showingRubric = false
    @State private var isLoading = false
    @State private var showingNextSubmission = false
    
    private var pendingSubmissions: [AssignmentSubmission] {
        assignment.submissions.filter { $0.score == nil }
    }
    
    private var currentSubmission: AssignmentSubmission? {
        guard currentSubmissionIndex < pendingSubmissions.count else { return nil }
        return pendingSubmissions[currentSubmissionIndex]
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                if let submission = currentSubmission {
                    // Progress header
                    progressHeader
                    
                    <PERSON><PERSON>View {
                        VStack(spacing: 20) {
                            // Student info
                            studentInfoSection(submission)
                            
                            // Submission content
                            submissionContentSection(submission)
                            
                            // Grading section
                            gradingSection
                            
                            // Feedback section
                            feedbackSection
                            
                            // Action buttons
                            actionButtonsSection
                        }
                        .padding(.horizontal)
                        .padding(.bottom, 32)
                    }
                } else {
                    // No submissions to grade
                    emptyStateView
                }
            }
            .navigationTitle("Chấm điểm")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Đóng") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Rubric") {
                        showingRubric = true
                    }
                    .disabled(currentSubmission == nil)
                }
            }
        }
        .sheet(isPresented: $showingRubric) {
            RubricView(
                assignment: assignment,
                rubricScores: $rubricScores,
                totalScore: $currentScore
            )
        }
        .onAppear {
            loadSubmissionData()
        }
    }
    
    // MARK: - Progress Header
    private var progressHeader: some View {
        VStack(spacing: 12) {
            // Progress bar
            VStack(spacing: 8) {
                HStack {
                    Text("Tiến độ chấm điểm")
                        .font(AppConstants.Typography.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textPrimary)

                    Spacer()

                    Text("\(currentSubmissionIndex + 1)/\(pendingSubmissions.count)")
                        .font(AppConstants.Typography.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.primary)
                }
                
                ProgressView(value: Double(currentSubmissionIndex + 1), total: Double(pendingSubmissions.count))
                    .progressViewStyle(LinearProgressViewStyle(tint: AppConstants.Colors.primary))
            }
            
            // Navigation buttons
            HStack(spacing: 16) {
                Button(action: previousSubmission) {
                    HStack(spacing: 6) {
                        Image(systemName: "chevron.left")
                        Text("Trước")
                    }
                    .font(AppConstants.Typography.subheadline)
                    .foregroundColor(currentSubmissionIndex > 0 ? AppConstants.Colors.primary : .gray)
                }
                .disabled(currentSubmissionIndex <= 0)
                
                Spacer()
                
                Button(action: nextSubmission) {
                    HStack(spacing: 6) {
                        Text("Sau")
                        Image(systemName: "chevron.right")
                    }
                    .font(AppConstants.Typography.subheadline)
                    .foregroundColor(currentSubmissionIndex < pendingSubmissions.count - 1 ? AppConstants.Colors.primary : .gray)
                }
                .disabled(currentSubmissionIndex >= pendingSubmissions.count - 1)
            }
        }
        .padding(.horizontal)
        .padding(.vertical, 16)
        .background(AppConstants.Colors.surface)
    }
    
    // MARK: - Student Info Section
    private func studentInfoSection(_ submission: AssignmentSubmission) -> some View {
        VStack(spacing: 16) {
            // Student header
            HStack(spacing: 12) {
                // Student avatar
                Circle()
                    .fill(AppConstants.Colors.primary.opacity(0.2))
                    .frame(width: 50, height: 50)
                    .overlay(
                        Text(submission.studentName.prefix(1))
                            .font(AppConstants.Typography.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(AppConstants.Colors.primary)
                    )
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(submission.studentName)
                        .font(AppConstants.Typography.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(AppConstants.Colors.textPrimary)

                    Text("ID: \(submission.studentId)")
                        .font(AppConstants.Typography.subheadline)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text("Nộp lúc")
                        .font(AppConstants.Typography.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)

                    Text(formatSubmissionTime(submission.submittedAt))
                        .font(AppConstants.Typography.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(submission.isLate ? .red : AppConstants.Colors.textPrimary)

                    if submission.isLate {
                        Text("Trễ hạn")
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(.red)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(.red.opacity(0.1))
                            .cornerRadius(4)
                    }
                }
            }
            
            // Assignment info
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(assignment.title)
                        .font(AppConstants.Typography.headline)
                        .foregroundColor(AppConstants.Colors.textPrimary)

                    Text(assignment.courseName)
                        .font(AppConstants.Typography.subheadline)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 4) {
                    Text("Điểm tối đa")
                        .font(AppConstants.Typography.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)

                    Text("\(assignment.maxPoints)")
                        .font(AppConstants.Typography.title2)
                        .fontWeight(.bold)
                        .foregroundColor(AppConstants.Colors.primary)
                }
            }
        }
        .padding(16)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(12)
    }
    
    // MARK: - Submission Content Section
    private func submissionContentSection(_ submission: AssignmentSubmission) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Bài làm")
                .font(AppConstants.Typography.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)

            if let content = submission.content {
                Text(content)
                    .font(AppConstants.Typography.body)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                    .padding(12)
                    .background(AppConstants.Colors.surface)
                    .cornerRadius(8)
            }
            
            // Attachments
            if !submission.attachments.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Tệp đính kèm")
                        .font(AppConstants.Typography.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textPrimary)

                    ForEach(submission.attachments, id: \.self) { attachment in
                        HStack(spacing: 12) {
                            Image(systemName: "doc.text")
                                .foregroundColor(AppConstants.Colors.primary)

                            Text(attachment)
                                .font(AppConstants.Typography.body)
                                .foregroundColor(AppConstants.Colors.textPrimary)

                            Spacer()

                            Button("Xem") {
                                // TODO: Open attachment
                                print("Open attachment: \(attachment)")
                            }
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.primary)
                        }
                        .padding(.vertical, 8)
                    }
                }
            }
        }
        .padding(16)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(12)
    }
    
    // MARK: - Grading Section
    private var gradingSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Chấm điểm")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            // Score input
            VStack(spacing: 12) {
                HStack {
                    Text("Điểm số")
                        .font(.body)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                    
                    Spacer()
                    
                    HStack(spacing: 8) {
                        TextField("Điểm", value: $currentScore, format: .number)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .frame(width: 80)
                            .keyboardType(.decimalPad)
                        
                        Text("/ \(assignment.maxPoints)")
                            .font(.body)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                }
                
                // Score slider
                VStack(spacing: 8) {
                    Slider(value: $currentScore, in: 0...Double(assignment.maxPoints), step: 0.5)
                        .accentColor(scoreColor)
                    
                    HStack {
                        Text("0")
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                        
                        Spacer()
                        
                        Text("\(assignment.maxPoints)")
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                }
                
                // Score percentage
                Text("\(Int((currentScore / Double(assignment.maxPoints)) * 100))%")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(scoreColor)
                    .frame(maxWidth: .infinity)
            }
            
            // Quick score buttons
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 4), spacing: 8) {
                ForEach([25, 50, 75, 100], id: \.self) { percentage in
                    Button(action: {
                        currentScore = Double(assignment.maxPoints) * Double(percentage) / 100.0
                    }) {
                        Text("\(percentage)%")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(AppConstants.Colors.primary)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 8)
                            .background(AppConstants.Colors.primary.opacity(0.1))
                            .cornerRadius(6)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
        .padding(16)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(12)
    }
    
    // MARK: - Feedback Section
    private var feedbackSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Nhận xét")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            TextField("Nhập nhận xét cho học sinh...", text: $feedback, axis: .vertical)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .lineLimit(5...10)
            
            // Quick feedback buttons
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 8) {
                ForEach(quickFeedbacks, id: \.self) { feedbackText in
                    Button(action: {
                        if feedback.isEmpty {
                            feedback = feedbackText
                        } else {
                            feedback += "\n\(feedbackText)"
                        }
                    }) {
                        Text(feedbackText)
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 8)
                            .background(AppConstants.Colors.surface)
                            .cornerRadius(6)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
        .padding(16)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(12)
    }
    
    // MARK: - Action Buttons Section
    private var actionButtonsSection: some View {
        VStack(spacing: 12) {
            // Save and next button
            Button(action: saveAndNext) {
                HStack {
                    if isLoading {
                        ProgressView()
                            .scaleEffect(0.8)
                            .foregroundColor(.white)
                    } else {
                        Image(systemName: "checkmark")
                        Text(currentSubmissionIndex < pendingSubmissions.count - 1 ? "Lưu & Tiếp theo" : "Hoàn thành")
                    }
                }
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 16)
                .background(AppConstants.Colors.primary)
                .cornerRadius(12)
            }
            .disabled(isLoading)
            
            // Save draft button
            Button(action: saveDraft) {
                HStack {
                    Image(systemName: "square.and.arrow.down")
                    Text("Lưu nháp")
                }
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(AppConstants.Colors.primary)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .background(AppConstants.Colors.primary.opacity(0.1))
                .cornerRadius(8)
            }
            .disabled(isLoading)
        }
    }
    
    // MARK: - Empty State View
    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "checkmark.circle")
                .font(.system(size: 60))
                .foregroundColor(.green)
            
            Text("Đã chấm xong!")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Text("Tất cả bài nộp đã được chấm điểm")
                .font(.subheadline)
                .foregroundColor(AppConstants.Colors.textSecondary)
                .multilineTextAlignment(.center)
        }
        .padding(40)
    }
    
    // MARK: - Computed Properties
    private var scoreColor: Color {
        let percentage = (currentScore / Double(assignment.maxPoints)) * 100
        switch percentage {
        case 80...100: return .green
        case 60..<80: return .orange
        default: return .red
        }
    }
    
    private let quickFeedbacks = [
        "Bài làm tốt!",
        "Cần cải thiện thêm",
        "Đáp án chính xác",
        "Thiếu chi tiết",
        "Trình bày rõ ràng",
        "Cần xem lại lý thuyết"
    ]
    
    // MARK: - Methods
    private func loadSubmissionData() {
        // Load current submission data if editing
        if let submission = currentSubmission {
            currentScore = submission.score ?? 0.0
            feedback = submission.feedback ?? ""
        }
    }
    
    private func previousSubmission() {
        if currentSubmissionIndex > 0 {
            currentSubmissionIndex -= 1
            loadSubmissionData()
        }
    }
    
    private func nextSubmission() {
        if currentSubmissionIndex < pendingSubmissions.count - 1 {
            currentSubmissionIndex += 1
            loadSubmissionData()
        }
    }
    
    private func saveAndNext() {
        guard let submission = currentSubmission else { return }
        
        isLoading = true
        
        Task {
            await assignmentManager.gradeSubmission(
                submissionId: submission.id,
                score: currentScore,
                feedback: feedback,
                rubricScores: rubricScores
            )
            
            await MainActor.run {
                isLoading = false
                
                if currentSubmissionIndex < pendingSubmissions.count - 1 {
                    nextSubmission()
                } else {
                    dismiss()
                }
            }
        }
    }
    
    private func saveDraft() {
        guard let submission = currentSubmission else { return }
        
        Task {
            await assignmentManager.saveDraftGrade(
                submissionId: submission.id,
                score: currentScore,
                feedback: feedback,
                rubricScores: rubricScores
            )
        }
    }
    
    private func formatSubmissionTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "dd/MM HH:mm"
        return formatter.string(from: date)
    }
}

// MARK: - Rubric View (Placeholder)
struct RubricView: View {
    let assignment: Assignment
    @Binding var rubricScores: [String: Double]
    @Binding var totalScore: Double
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Image(systemName: "list.clipboard")
                    .font(.system(size: 60))
                    .foregroundColor(AppConstants.Colors.primary)
                
                Text("Rubric Grading")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text("Tính năng chấm điểm theo rubric sẽ được phát triển trong phiên bản tiếp theo")
                    .font(.subheadline)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
            .navigationTitle("Rubric")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Đóng") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Submissions View (Placeholder)
struct SubmissionsView: View {
    let assignment: Assignment
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Image(systemName: "doc.text")
                    .font(.system(size: 60))
                    .foregroundColor(AppConstants.Colors.primary)
                
                Text("All Submissions")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text("Danh sách tất cả bài nộp sẽ được hiển thị ở đây")
                    .font(.subheadline)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
            .navigationTitle("Bài nộp")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Đóng") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Assignment Analytics View (Placeholder)
struct AssignmentAnalyticsView: View {
    let assignment: Assignment
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Image(systemName: "chart.bar")
                    .font(.system(size: 60))
                    .foregroundColor(AppConstants.Colors.primary)
                
                Text("Analytics")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text("Thống kê chi tiết về bài tập sẽ được hiển thị ở đây")
                    .font(.subheadline)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
            .navigationTitle("Thống kê")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Đóng") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Preview
struct GradingView_Previews: PreviewProvider {
    static var previews: some View {
        GradingView(assignment: Assignment.mockAssignments[0])
    }
}
*/

// TEMPORARY REPLACEMENT VIEW
struct GradingView: View {
    let assignment: Assignment

    var body: some View {
        VStack {
            Text("🚧 GradingView Temporarily Disabled")
                .font(.title2)
                .foregroundColor(.orange)

            Text("Build errors being fixed...")
                .font(.caption)
                .foregroundColor(.gray)
        }
        .padding()
    }
}
