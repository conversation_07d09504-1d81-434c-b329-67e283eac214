//
//  CreateAssignmentView.swift
//  mobile-app-template
//
//  Created by Instructor App on 23/7/25.
//

import SwiftUI

struct CreateAssignmentView: View {
    let editingAssignment: Assignment?
    @StateObject private var assignmentManager = AssignmentManager()
    @Environment(\.dismiss) private var dismiss
    
    // Form fields
    @State private var title = ""
    @State private var description = ""
    @State private var selectedCourse: Course?
    @State private var selectedType: AssignmentType = .homework
    @State private var maxScore = 100.0
    @State private var estimatedHours = 2
    @State private var difficulty = DifficultyLevel.intermediate
    @State private var dueDate = Date().addingTimeInterval(7 * 24 * 3600) // 1 week from now
    @State private var instructions = ""
    @State private var allowLateSubmission = true
    @State private var latePenalty = 10.0
    @State private var showGradeToStudents = true
    @State private var enablePlagiarismCheck = false
    
    // UI state
    @State private var showingCoursePicker = false
    @State private var showingQuizBuilder = false
    @State private var isLoading = false
    @State private var showingError = false
    @State private var errorMessage = ""
    
    // Mock courses
    private let availableCourses = Course.mockCourses
    
    init(editingAssignment: Assignment? = nil) {
        self.editingAssignment = editingAssignment
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Custom header
            headerSection

            // Form content
            ScrollView {
                VStack(spacing: 24) {
                // Basic info section
                basicInfoSection
                
                // Course and type section
                courseAndTypeSection
                
                // Scoring section
                scoringSection
                
                // Timing section
                timingSection
                
                // Instructions section
                instructionsSection
                
                // Settings section
                settingsSection
                
                    // Quiz builder section (if quiz type)
                    if selectedType == .quiz || selectedType == .exam {
                        quizBuilderSection
                    }
                }
                .padding(.horizontal)
                .padding(.bottom, 32)
            }
        }
        .background(AppConstants.Colors.background)
        .navigationBarHidden(true)
        .sheet(isPresented: $showingCoursePicker) {
            CoursePickerView(selectedCourse: $selectedCourse)
        }
        .sheet(isPresented: $showingQuizBuilder) {
            QuizBuilderView()
        }
        .alert("Lỗi", isPresented: $showingError) {
            Button("OK") { }
        } message: {
            Text(errorMessage)
        }
        .onAppear {
            loadEditingData()
        }
    }

    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            // Back button
            Button(action: { dismiss() }) {
                HStack(spacing: 8) {
                    Image(systemName: "chevron.left")
                        .font(.title3)
                        .fontWeight(.medium)

                    Text("Hủy")
                        .font(AppConstants.Typography.body)
                }
                .foregroundColor(AppConstants.Colors.primary)
            }

            Spacer()

            VStack(spacing: 2) {
                Text(editingAssignment == nil ? "Tạo bài tập mới" : "Chỉnh sửa bài tập")
                    .font(AppConstants.Typography.headline)
                    .foregroundColor(AppConstants.Colors.textPrimary)
            }

            Spacer()

            // Save button
            Button(editingAssignment == nil ? "Tạo" : "Lưu") {
                saveAssignment()
            }
            .font(AppConstants.Typography.body)
            .fontWeight(.semibold)
            .foregroundColor(isFormValid && !isLoading ? AppConstants.Colors.primary : .gray)
            .disabled(!isFormValid || isLoading)
        }
        .padding(.horizontal)
        .padding(.vertical, 16)
        .background(AppConstants.Colors.background)
    }

    // MARK: - Basic Info Section
    private var basicInfoSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Thông tin cơ bản")
                .font(AppConstants.Typography.headline)
                .foregroundColor(AppConstants.Colors.textPrimary)

            VStack(spacing: 16) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Tiêu đề bài tập")
                        .font(AppConstants.Typography.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textSecondary)

                    TextField("Nhập tiêu đề bài tập", text: $title)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                }

                VStack(alignment: .leading, spacing: 8) {
                    Text("Mô tả ngắn")
                        .font(AppConstants.Typography.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textSecondary)

                    TextField("Nhập mô tả ngắn", text: $description, axis: .vertical)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .lineLimit(3...6)
                }
            }
        }
        .padding(16)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(12)
    }
    
    // MARK: - Course and Type Section
    private var courseAndTypeSection: some View {
        Section("Môn học và loại") {
            // Course picker
            HStack {
                Text("Môn học")
                Spacer()
                Button(selectedCourse?.name ?? "Chọn môn học") {
                    showingCoursePicker = true
                }
                .foregroundColor(selectedCourse == nil ? .gray : AppConstants.Colors.primary)
            }
            
            // Type picker
            Picker("Loại bài tập", selection: $selectedType) {
                ForEach(AssignmentType.allCases, id: \.self) { type in
                    Text(type.displayName).tag(type)
                }
            }
            .pickerStyle(MenuPickerStyle())
        }
    }
    
    // MARK: - Scoring Section
    private var scoringSection: some View {
        Section(header: Text("Điểm số")) {
            HStack {
                Text("Điểm tối đa")
                Spacer()
                TextField("Điểm", value: $maxScore, format: .number)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .frame(width: 80)
                    .keyboardType(.decimalPad)
            }
            
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Độ khó")
                    Spacer()
                    Text(difficulty.displayName)
                        .foregroundColor(AppConstants.Colors.primary)
                }

                Picker("Độ khó", selection: $difficulty) {
                    ForEach(DifficultyLevel.allCases, id: \.self) { level in
                        Text(level.displayName).tag(level)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
            }
        }
    }
    
    // MARK: - Timing Section
    private var timingSection: some View {
        Section("Thời gian") {
            DatePicker("Hạn nộp", selection: $dueDate, displayedComponents: [.date, .hourAndMinute])
            
            HStack {
                Text("Thời gian ước tính")
                Spacer()
                TextField("Giờ", value: $estimatedHours, format: .number)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .frame(width: 80)
                    .keyboardType(.numberPad)
                Text("giờ")
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
        }
    }
    
    // MARK: - Instructions Section
    private var instructionsSection: some View {
        Section("Hướng dẫn") {
            TextField("Hướng dẫn chi tiết cho học sinh", text: $instructions, axis: .vertical)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .lineLimit(5...10)
        }
    }
    
    // MARK: - Settings Section
    private var settingsSection: some View {
        Section("Cài đặt") {
            Toggle("Cho phép nộp trễ", isOn: $allowLateSubmission)
            
            if allowLateSubmission {
                HStack {
                    Text("Phạt trễ hạn")
                    Spacer()
                    TextField("Phần trăm", value: $latePenalty, format: .number)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .frame(width: 80)
                        .keyboardType(.decimalPad)
                    Text("%/ngày")
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
            }
            
            Toggle("Hiển thị điểm cho học sinh", isOn: $showGradeToStudents)
            Toggle("Kiểm tra đạo văn", isOn: $enablePlagiarismCheck)
        }
    }
    
    // MARK: - Quiz Builder Section
    private var quizBuilderSection: some View {
        Section("Câu hỏi") {
            Button(action: { showingQuizBuilder = true }) {
                HStack {
                    Image(systemName: "questionmark.circle")
                        .foregroundColor(AppConstants.Colors.primary)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("Tạo câu hỏi")
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        
                        Text("Thêm câu hỏi trắc nghiệm, tự luận")
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
    
    // MARK: - Computed Properties
    private var isFormValid: Bool {
        !title.isEmpty && selectedCourse != nil && maxScore > 0 && estimatedHours > 0
    }
    
    // MARK: - Methods
    private func loadEditingData() {
        guard let assignment = editingAssignment else { return }
        
        title = assignment.title
        description = assignment.description
        selectedCourse = availableCourses.first { $0.id == assignment.courseId }
        selectedType = assignment.type
        maxScore = assignment.maxScore
        estimatedHours = assignment.estimatedHours ?? 2
        difficulty = assignment.difficulty
        dueDate = assignment.dueDate
        instructions = assignment.instructions ?? ""
        // Load other settings...
    }
    
    private func saveAssignment() {
        guard isFormValid else { return }
        
        isLoading = true
        
        Task {
            do {
                if let editingAssignment = editingAssignment {
                    // Update existing assignment
                    let updatedAssignment = Assignment(
                        id: editingAssignment.id,
                        courseId: selectedCourse?.id ?? "",
                        courseName: selectedCourse?.name ?? "",
                        courseCode: selectedCourse?.code ?? "",
                        classId: editingAssignment.classId,
                        title: title,
                        description: description,
                        instructions: instructions.isEmpty ? nil : instructions,
                        type: selectedType,
                        status: editingAssignment.status,
                        maxScore: maxScore,
                        weight: editingAssignment.weight,
                        difficulty: difficulty,
                        estimatedHours: estimatedHours,
                        assignedDate: editingAssignment.assignedDate,
                        dueDate: dueDate,
                        submissionMethod: editingAssignment.submissionMethod,
                        allowLateSubmission: allowLateSubmission,
                        latePenalty: allowLateSubmission ? latePenalty : nil,
                        maxLateDays: editingAssignment.maxLateDays,
                        rubric: editingAssignment.rubric,
                        attachments: editingAssignment.attachments,
                        tags: editingAssignment.tags,
                        totalSubmissions: editingAssignment.totalSubmissions,
                        gradedSubmissions: editingAssignment.gradedSubmissions,
                        averageScore: editingAssignment.averageScore,
                        instructorId: editingAssignment.instructorId,
                        instructorName: editingAssignment.instructorName,
                        metadata: editingAssignment.metadata,
                        createdAt: editingAssignment.createdAt,
                        updatedAt: Date()
                    )
                    await assignmentManager.updateAssignment(updatedAssignment)
                } else {
                    // Create new assignment
                    let assignmentData = CreateAssignmentData(
                        courseId: selectedCourse?.id ?? "",
                        courseName: selectedCourse?.name ?? "",
                        courseCode: selectedCourse?.code ?? "",
                        classId: nil,
                        title: title,
                        description: description,
                        instructions: instructions.isEmpty ? nil : instructions,
                        type: selectedType,
                        maxScore: maxScore,
                        weight: 1.0, // Default weight
                        difficulty: difficulty,
                        estimatedHours: estimatedHours,
                        dueDate: dueDate,
                        submissionMethod: .online, // Default submission method
                        allowLateSubmission: allowLateSubmission,
                        latePenalty: allowLateSubmission ? latePenalty : nil,
                        maxLateDays: allowLateSubmission ? 7 : nil, // Default 7 days
                        rubric: nil,
                        attachments: nil,
                        tags: nil
                    )
                    await assignmentManager.createAssignment(assignmentData)
                }
                
                await MainActor.run {
                    isLoading = false
                    dismiss()
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    errorMessage = error.localizedDescription
                    showingError = true
                }
            }
        }
    }
}

// MARK: - Course Picker View
struct CoursePickerView: View {
    @Binding var selectedCourse: Course?
    @Environment(\.dismiss) private var dismiss
    
    private let courses = Course.mockCourses
    
    var body: some View {
        NavigationView {
            List(courses) { course in
                Button(action: {
                    selectedCourse = course
                    dismiss()
                }) {
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(course.name)
                                .font(AppConstants.Typography.headline)
                                .foregroundColor(AppConstants.Colors.textPrimary)

                            Text(course.code)
                                .font(AppConstants.Typography.subheadline)
                                .foregroundColor(AppConstants.Colors.textSecondary)
                        }
                        
                        Spacer()
                        
                        if selectedCourse?.id == course.id {
                            Image(systemName: "checkmark")
                                .foregroundColor(AppConstants.Colors.primary)
                        }
                    }
                }
                .buttonStyle(PlainButtonStyle())
            }
            .navigationTitle("Chọn môn học")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Xong") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Quiz Builder View (Placeholder)
struct QuizBuilderView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Image(systemName: "questionmark.circle")
                    .font(.system(size: 60))
                    .foregroundColor(AppConstants.Colors.primary)
                
                Text("Quiz Builder")
                    .font(AppConstants.Typography.title2)
                    .fontWeight(.semibold)

                Text("Tính năng tạo câu hỏi sẽ được phát triển trong phiên bản tiếp theo")
                    .font(AppConstants.Typography.subheadline)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
            .navigationTitle("Tạo câu hỏi")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Đóng") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Preview
struct CreateAssignmentView_Previews: PreviewProvider {
    static var previews: some View {
        CreateAssignmentView()
    }
}
