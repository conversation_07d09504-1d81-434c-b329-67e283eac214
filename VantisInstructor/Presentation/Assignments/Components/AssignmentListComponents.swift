//
//  AssignmentListComponents.swift
//  mobile-app-template
//
//  Created by Instructor <PERSON>pp on 23/7/25.
//

import SwiftUI

// MARK: - Assignment Filter Enum
enum AssignmentFilter: String, CaseIterable {
    case all = "all"
    case dueToday = "due_today"
    case dueThisWeek = "due_this_week"
    case overdue = "overdue"
    case needsGrading = "needs_grading"

    var displayName: String {
        switch self {
        case .all:
            return "Tất cả"
        case .dueToday:
            return "Hạn hôm nay"
        case .dueThisWeek:
            return "Hạn tuần này"
        case .overdue:
            return "Quá hạn"
        case .needsGrading:
            return "Cần chấm"
        }
    }

    var icon: String {
        switch self {
        case .all:
            return "list.bullet"
        case .dueToday:
            return "calendar.badge.clock"
        case .dueThisWeek:
            return "calendar"
        case .overdue:
            return "exclamationmark.triangle"
        case .needsGrading:
            return "doc.text.magnifyingglass"
        }
    }
}

// MARK: - Assignment Stat Card
struct AssignmentStatCard: View {
    let title: String
    let value: String
    let subtitle: String
    let color: Color
    let icon: String
    
    var body: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(color)
                
                Spacer()
            }
            
            VStack(alignment: .leading, spacing: 2) {
                Text(value)
                    .font(AppConstants.Typography.title2)
                    .foregroundColor(color)

                Text(title)
                    .font(AppConstants.Typography.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)

                Text(subtitle)
                    .font(AppConstants.Typography.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(12)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(color.opacity(0.2), lineWidth: 1)
        )
    }
}

// MARK: - Assignment Card
struct AssignmentCard: View {
    let assignment: Assignment
    let onTap: () -> Void
    let onGrade: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 12) {
                // Header
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(assignment.title)
                            .font(AppConstants.Typography.headline)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                            .multilineTextAlignment(.leading)

                        Text(assignment.courseName)
                            .font(AppConstants.Typography.subheadline)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .trailing, spacing: 4) {
                        AssignmentTypeBadge(type: assignment.type)
                        AssignmentStatusBadge(status: assignment.status)
                    }
                }
                
                // Due date and progress
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        HStack(spacing: 6) {
                            Image(systemName: "calendar")
                                .font(.caption)
                                .foregroundColor(dueDateColor)
                            
                            Text("Hạn nộp: \(formatDueDate(assignment.dueDate))")
                                .font(AppConstants.Typography.caption)
                                .foregroundColor(dueDateColor)
                        }
                        
                        if assignment.isOverdue {
                            HStack(spacing: 6) {
                                Image(systemName: "exclamationmark.triangle.fill")
                                    .font(.caption)
                                    .foregroundColor(.red)
                                
                                Text("Quá hạn \(overdueDays) ngày")
                                    .font(AppConstants.Typography.caption)
                                    .foregroundColor(.red)
                            }
                        }
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .trailing, spacing: 4) {
                        Text("0/0") // Placeholder for submission count
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textPrimary)

                        Text("đã nộp")
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                }
                
                // Progress bar
                VStack(spacing: 6) {
                    HStack {
                        Text("Tiến độ nộp bài")
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)

                        Spacer()

                        Text("\(Int(assignment.submissionRate))%")
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                    }
                    
                    ProgressView(value: assignment.submissionRate / 100.0)
                        .progressViewStyle(LinearProgressViewStyle(tint: progressColor))
                        .scaleEffect(y: 0.8)
                }
                
                // Action buttons
                if assignment.totalSubmissions > assignment.gradedSubmissions {
                    HStack(spacing: 12) {
                        Button(action: onGrade) {
                            HStack(spacing: 6) {
                                Image(systemName: "pencil.and.ruler")
                                    .font(.caption)
                                
                                Text("Chấm điểm (\(assignment.totalSubmissions - assignment.gradedSubmissions))")
                                    .font(.caption)
                                    .fontWeight(.medium)
                            }
                            .foregroundColor(.white)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(AppConstants.Colors.primary)
                            .cornerRadius(8)
                        }
                        .buttonStyle(PlainButtonStyle())
                        
                        Spacer()
                        
                        if assignment.difficulty != .beginner {
                            HStack(spacing: 2) {
                                ForEach(0..<5, id: \.self) { index in
                                    let difficultyLevel = difficultyToInt(assignment.difficulty)
                                    Image(systemName: index < difficultyLevel ? "star.fill" : "star")
                                        .font(.caption2)
                                        .foregroundColor(index < difficultyLevel ? .orange : .gray)
                                }
                            }
                        }
                    }
                }
            }
            .padding(16)
            .background(AppConstants.Colors.cardBackground)
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(borderColor, lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Computed Properties
    private var dueDateColor: Color {
        if assignment.isOverdue {
            return .red
        } else if Calendar.current.isDateInToday(assignment.dueDate) {
            return .orange
        } else if Calendar.current.isDateInTomorrow(assignment.dueDate) {
            return .yellow
        } else {
            return AppConstants.Colors.textSecondary
        }
    }
    
    private var progressColor: Color {
        switch assignment.submissionRate {
        case 80...100: return .green
        case 50..<80: return .orange
        default: return .red
        }
    }

    private func difficultyToInt(_ difficulty: DifficultyLevel) -> Int {
        switch difficulty {
        case .beginner: return 1
        case .intermediate: return 2
        case .advanced: return 3
        case .expert: return 4
        }
    }
    
    private var borderColor: Color {
        if assignment.isOverdue {
            return .red.opacity(0.3)
        } else if assignment.totalSubmissions > assignment.gradedSubmissions {
            return .orange.opacity(0.3)
        } else {
            return AppConstants.Colors.surface
        }
    }
    
    private var overdueDays: Int {
        Calendar.current.dateComponents([.day], from: assignment.dueDate, to: Date()).day ?? 0
    }
    
    // MARK: - Helper Methods
    private func formatDueDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        
        if Calendar.current.isDateInToday(date) {
            formatter.timeStyle = .short
            return "Hôm nay, \(formatter.string(from: date))"
        } else if Calendar.current.isDateInTomorrow(date) {
            formatter.timeStyle = .short
            return "Ngày mai, \(formatter.string(from: date))"
        } else {
            formatter.dateFormat = "dd/MM/yyyy HH:mm"
            return formatter.string(from: date)
        }
    }
}

// MARK: - Assignment Type Badge
struct AssignmentTypeBadge: View {
    let type: AssignmentType
    
    var body: some View {
        Text(type.displayName)
            .font(AppConstants.Typography.caption)
            .foregroundColor(.white)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(colorForType(type))
            .cornerRadius(6)
    }
    
    private func colorForType(_ type: AssignmentType) -> Color {
        switch type {
        case .homework: return .blue
        case .quiz: return .purple
        case .exam: return .red
        case .project: return .green
        case .lab: return .orange
        case .presentation: return .indigo
        case .essay: return .teal
        case .research: return .cyan
        case .groupWork: return .mint
        case .practicum: return .brown
        }
    }
}

// MARK: - Assignment Status Badge
struct AssignmentStatusBadge: View {
    let status: AssignmentStatus
    
    var body: some View {
        HStack(spacing: 4) {
            Circle()
                .fill(colorForStatus(status))
                .frame(width: 6, height: 6)
            
            Text(status.displayName)
                .font(AppConstants.Typography.caption)
                .foregroundColor(colorForStatus(status))
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(colorForStatus(status).opacity(0.1))
        .cornerRadius(6)
    }
    
    private func colorForStatus(_ status: AssignmentStatus) -> Color {
        switch status {
        case .draft: return .gray
        case .published: return .blue
        case .active: return .green
        case .completed: return .purple
        case .cancelled: return .red
        case .archived: return .indigo
        }
    }
}

// MARK: - Quick Action Button
struct QuickActionButton: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 6) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(color)
                
                Text(title)
                    .font(AppConstants.Typography.caption)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(AppConstants.Colors.cardBackground)
            .cornerRadius(10)
            .overlay(
                RoundedRectangle(cornerRadius: 10)
                    .stroke(color.opacity(0.2), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Filter Chip View
struct FilterChipView: View {
    let title: String
    let onRemove: () -> Void
    
    var body: some View {
        HStack(spacing: 6) {
            Text(title)
                .font(.caption)
                .fontWeight(.medium)
            
            Button(action: onRemove) {
                Image(systemName: "xmark")
                    .font(.caption2)
            }
        }
        .foregroundColor(AppConstants.Colors.primary)
        .padding(.horizontal, 10)
        .padding(.vertical, 6)
        .background(AppConstants.Colors.primary.opacity(0.1))
        .cornerRadius(12)
    }
}

// MARK: - Assignment Filter Sheet
struct AssignmentFilterSheet: View {
    @Binding var selectedFilter: AssignmentFilter
    @Binding var selectedStatus: AssignmentStatus?
    @Binding var selectedType: AssignmentType?
    @Binding var selectedCourse: String?
    @Environment(\.dismiss) private var dismiss
    
    // Mock courses - in real app, this would come from a service
    private let availableCourses = [
        ("course_1", "CS301 - Lập trình iOS"),
        ("course_2", "CS302 - Android Development"),
        ("course_3", "CS303 - Web Development")
    ]
    
    var body: some View {
        NavigationView {
            Form {
                // Date filter section
                Section("Lọc theo thời gian") {
                    ForEach(AssignmentFilter.allCases, id: \.self) { filter in
                        HStack {
                            Text(filter.rawValue)
                            Spacer()
                            if selectedFilter == filter {
                                Image(systemName: "checkmark")
                                    .foregroundColor(AppConstants.Colors.primary)
                            }
                        }
                        .contentShape(Rectangle())
                        .onTapGesture {
                            selectedFilter = filter
                        }
                    }
                }
                
                // Status filter section
                Section("Lọc theo trạng thái") {
                    HStack {
                        Text("Tất cả trạng thái")
                        Spacer()
                        if selectedStatus == nil {
                            Image(systemName: "checkmark")
                                .foregroundColor(AppConstants.Colors.primary)
                        }
                    }
                    .contentShape(Rectangle())
                    .onTapGesture {
                        selectedStatus = nil
                    }
                    
                    ForEach(AssignmentStatus.allCases, id: \.self) { status in
                        HStack {
                            Text(status.displayName)
                            Spacer()
                            if selectedStatus == status {
                                Image(systemName: "checkmark")
                                    .foregroundColor(AppConstants.Colors.primary)
                            }
                        }
                        .contentShape(Rectangle())
                        .onTapGesture {
                            selectedStatus = status
                        }
                    }
                }
                
                // Type filter section
                Section("Lọc theo loại") {
                    HStack {
                        Text("Tất cả loại")
                        Spacer()
                        if selectedType == nil {
                            Image(systemName: "checkmark")
                                .foregroundColor(AppConstants.Colors.primary)
                        }
                    }
                    .contentShape(Rectangle())
                    .onTapGesture {
                        selectedType = nil
                    }
                    
                    ForEach(AssignmentType.allCases, id: \.self) { type in
                        HStack {
                            Text(type.displayName)
                            Spacer()
                            if selectedType == type {
                                Image(systemName: "checkmark")
                                    .foregroundColor(AppConstants.Colors.primary)
                            }
                        }
                        .contentShape(Rectangle())
                        .onTapGesture {
                            selectedType = type
                        }
                    }
                }
                
                // Course filter section
                Section("Lọc theo môn học") {
                    HStack {
                        Text("Tất cả môn học")
                        Spacer()
                        if selectedCourse == nil {
                            Image(systemName: "checkmark")
                                .foregroundColor(AppConstants.Colors.primary)
                        }
                    }
                    .contentShape(Rectangle())
                    .onTapGesture {
                        selectedCourse = nil
                    }
                    
                    ForEach(availableCourses, id: \.0) { course in
                        HStack {
                            Text(course.1)
                            Spacer()
                            if selectedCourse == course.0 {
                                Image(systemName: "checkmark")
                                    .foregroundColor(AppConstants.Colors.primary)
                            }
                        }
                        .contentShape(Rectangle())
                        .onTapGesture {
                            selectedCourse = course.0
                        }
                    }
                }
                
                // Reset section
                Section {
                    Button("Đặt lại bộ lọc") {
                        selectedFilter = .all
                        selectedStatus = nil
                        selectedType = nil
                        selectedCourse = nil
                    }
                    .foregroundColor(AppConstants.Colors.error)
                }
            }
            .navigationTitle("Bộ lọc")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Hủy") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Xong") {
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
    }
}

// MARK: - Preview
struct AssignmentListComponents_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            HStack(spacing: 12) {
                AssignmentStatCard(
                    title: "Cần chấm",
                    value: "12",
                    subtitle: "bài nộp",
                    color: .orange,
                    icon: "doc.text.magnifyingglass"
                )
                
                AssignmentStatCard(
                    title: "Quá hạn",
                    value: "3",
                    subtitle: "bài tập",
                    color: .red,
                    icon: "clock.badge.exclamationmark"
                )
            }
            
            AssignmentCard(
                assignment: Assignment.mockAssignments[0],
                onTap: { print("Tap assignment") },
                onGrade: { print("Grade assignment") }
            )
        }
        .padding()
        .background(AppConstants.Colors.background)
    }
}
