//
//  LessonAssignmentViewModel.swift
//  mobile-app-template
//
//  Created by Instructor App on 27/7/25.
//

import Foundation
import Combine

// MARK: - Assignment Step
enum AssignmentStep: Int, CaseIterable {
    case lessonSelection = 0
    case configuration = 1
    case preview = 2
    
    var title: String {
        switch self {
        case .lessonSelection: return "Chọn bài học"
        case .configuration: return "Cấu hình"
        case .preview: return "Xem trước"
        }
    }
    
    var description: String {
        switch self {
        case .lessonSelection: return "Chọn bài học để gán quiz"
        case .configuration: return "Thiết lập thời gian và cài đặt"
        case .preview: return "Xem lại thông tin trước khi gán"
        }
    }
}

// MARK: - Assignment Configuration
struct AssignmentConfiguration {
    var startDate: Date?
    var endDate: Date?
    var instructions: String = ""
    var allowLateSubmission: Bool = false
    var showCorrectAnswers: Bool = false
    var maxAttempts: Int = 1
    var timeLimit: Int? // minutes
    
    var isValid: Bool {
        // Basic validation
        if let start = startDate, let end = endDate {
            return start < end
        }
        return startDate != nil || endDate != nil
    }
}

// MARK: - Lesson Assignment ViewModel
@MainActor
class LessonAssignmentViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var currentStep: AssignmentStep = .lessonSelection
    @Published var selectedLesson: Lesson?
    @Published var configuration = AssignmentConfiguration()
    @Published var availableLessons: [Lesson] = []
    @Published var filteredLessons: [Lesson] = []
    @Published var searchText: String = ""
    @Published var selectedCourseFilter: Int?
    @Published var selectedStatusFilter: LessonStatus?
    
    // MARK: - Loading States
    @Published var isLoadingLessons = false
    @Published var isAssigning = false
    @Published var isPublishing = false
    
    // MARK: - Error Handling
    @Published var errorMessage: String?
    @Published var showingError = false
    
    // MARK: - Success States
    @Published var assignmentCompleted = false
    @Published var publishCompleted = false
    
    // MARK: - Dependencies
    private let quiz: Quiz
    private let lessonRepository: LessonRepositoryProtocol
    private let quizRepository: QuizRepositoryProtocol
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Computed Properties
    var canProceedToConfiguration: Bool {
        selectedLesson != nil
    }
    
    var canProceedToPreview: Bool {
        selectedLesson != nil && configuration.isValid
    }
    
    var canCompleteAssignment: Bool {
        selectedLesson != nil && configuration.isValid
    }
    
    var progressPercentage: Double {
        Double(currentStep.rawValue + 1) / Double(AssignmentStep.allCases.count)
    }
    
    // MARK: - Initialization
    init(quiz: Quiz, 
         lessonRepository: LessonRepositoryProtocol = MockLessonRepository(),
         quizRepository: QuizRepositoryProtocol = MockQuizRepository()) {
        self.quiz = quiz
        self.lessonRepository = lessonRepository
        self.quizRepository = quizRepository
        
        setupSearchBinding()
        loadAvailableLessons()
    }
    
    // MARK: - Setup Methods
    private func setupSearchBinding() {
        $searchText
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
            .removeDuplicates()
            .sink { [weak self] searchText in
                self?.filterLessons()
            }
            .store(in: &cancellables)
        
        Publishers.CombineLatest($selectedCourseFilter, $selectedStatusFilter)
            .sink { [weak self] _, _ in
                self?.filterLessons()
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Data Loading
    func loadAvailableLessons() {
        Task {
            isLoadingLessons = true
            errorMessage = nil
            
            do {
                let response = try await lessonRepository.getAvailableLessonsForQuiz(quizId: quiz.id)
                availableLessons = response.data.lessons
                filterLessons()
            } catch {
                errorMessage = "Không thể tải danh sách bài học: \(error.localizedDescription)"
                showingError = true
            }
            
            isLoadingLessons = false
        }
    }
    
    private func filterLessons() {
        var filtered = availableLessons
        
        // Apply search filter
        if !searchText.isEmpty {
            filtered = filtered.filter { lesson in
                lesson.title.localizedCaseInsensitiveContains(searchText) ||
                lesson.courseName.localizedCaseInsensitiveContains(searchText) ||
                lesson.courseCode.localizedCaseInsensitiveContains(searchText)
            }
        }
        
        // Apply course filter
        if let courseId = selectedCourseFilter {
            filtered = filtered.filter { $0.courseId == courseId }
        }
        
        // Apply status filter
        if let status = selectedStatusFilter {
            filtered = filtered.filter { $0.status == status }
        }
        
        filteredLessons = filtered
    }
    
    // MARK: - Navigation Methods
    func proceedToConfiguration() {
        guard canProceedToConfiguration else { return }
        currentStep = .configuration
    }
    
    func proceedToPreview() {
        guard canProceedToPreview else { return }
        currentStep = .preview
    }
    
    func goBackToPreviousStep() {
        switch currentStep {
        case .lessonSelection:
            break // Already at first step
        case .configuration:
            currentStep = .lessonSelection
        case .preview:
            currentStep = .configuration
        }
    }
    
    // MARK: - Selection Methods
    func selectLesson(_ lesson: Lesson) {
        selectedLesson = lesson
        
        // Auto-configure some settings based on lesson
        if configuration.startDate == nil {
            configuration.startDate = lesson.startTime ?? lesson.scheduledDate
        }
        if configuration.endDate == nil {
            configuration.endDate = lesson.endTime ?? Calendar.current.date(byAdding: .hour, value: 2, to: lesson.scheduledDate)
        }
    }
    
    func clearSelection() {
        selectedLesson = nil
        configuration = AssignmentConfiguration()
    }
    
    // MARK: - Assignment Methods
    func assignQuizToLesson() {
        guard let lesson = selectedLesson else { return }
        
        Task {
            isAssigning = true
            errorMessage = nil
            
            do {
                let request = AssignQuizRequest(
                    quizId: quiz.id,
                    startDate: configuration.startDate,
                    endDate: configuration.endDate,
                    instructions: configuration.instructions.isEmpty ? nil : configuration.instructions
                )
                
                let _ = try await quizRepository.assignQuizToLesson(lessonId: lesson.id, request: request)
                assignmentCompleted = true
                
            } catch {
                errorMessage = "Không thể gán quiz vào bài học: \(error.localizedDescription)"
                showingError = true
            }
            
            isAssigning = false
        }
    }
    
    func publishQuizToLesson() {
        guard let lesson = selectedLesson else { return }
        
        Task {
            isPublishing = true
            errorMessage = nil
            
            do {
                let _ = try await quizRepository.publishQuiz(lessonId: lesson.id, quizId: quiz.id)
                publishCompleted = true
                
            } catch {
                errorMessage = "Không thể phát hành quiz: \(error.localizedDescription)"
                showingError = true
            }
            
            isPublishing = false
        }
    }
    
    // MARK: - Validation Methods
    func validateConfiguration() -> [String] {
        var errors: [String] = []
        
        if let startDate = configuration.startDate,
           let endDate = configuration.endDate,
           startDate >= endDate {
            errors.append("Thời gian bắt đầu phải trước thời gian kết thúc")
        }
        
        if configuration.maxAttempts < 1 {
            errors.append("Số lần làm bài phải lớn hơn 0")
        }
        
        if let timeLimit = configuration.timeLimit, timeLimit < 1 {
            errors.append("Thời gian làm bài phải lớn hơn 0")
        }
        
        return errors
    }
    
    // MARK: - Helper Methods
    func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "vi_VN")
        return formatter.string(from: date)
    }
    
    func formatDuration(_ minutes: Int) -> String {
        let hours = minutes / 60
        let remainingMinutes = minutes % 60
        
        if hours > 0 {
            return "\(hours)h \(remainingMinutes)m"
        } else {
            return "\(minutes)m"
        }
    }
    
    // MARK: - Reset Methods
    func reset() {
        currentStep = .lessonSelection
        selectedLesson = nil
        configuration = AssignmentConfiguration()
        searchText = ""
        selectedCourseFilter = nil
        selectedStatusFilter = nil
        assignmentCompleted = false
        publishCompleted = false
        errorMessage = nil
        showingError = false
    }
}
