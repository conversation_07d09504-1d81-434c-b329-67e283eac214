//
//  QuizManagementViewModel.swift
//  mobile-app-template
//
//  Created by Instructor App on 24/7/25.
//

import Foundation
import SwiftUI

// MARK: - Debug Helpers
extension DateFormatter {
    static let debugTimestamp: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss.SSS"
        return formatter
    }()
}

@MainActor
class QuizManagementViewModel: ObservableObject, LazyLoadableViewModel {
    // MARK: - Published Properties
    @Published var quizzes: [Quiz] = []
    @Published var isLoading = false
    @Published var isRefreshing = false
    @Published var errorMessage: String?
    @Published var showingError = false
    @Published var currentPage = 1
    @Published var hasMorePages = true
    @Published var totalCount = 0

    // MARK: - LazyLoadableViewModel Properties
    @Published var hasInitialized = false
    @Published var isInitializing = false

    // MARK: - Private Properties for Better State Management
    private var currentTask: Task<Void, Never>?
    
    // MARK: - Filters
    @Published var selectedQuizType: QuizType?
    @Published var selectedState: QuizState?
    @Published var searchText = ""
    @Published var showingFilters = false
    
    // MARK: - Dependencies
    private let quizRepository: QuizRepositoryProtocol
    private let limit = 20
    
    // MARK: - Dependencies
    private let authManager = UnifiedAuthManager.shared
    private let errorHandler = AuthErrorHandler.shared

    // MARK: - Initialization
    init(quizRepository: QuizRepositoryProtocol = QuizRepository()) {
        self.quizRepository = quizRepository

        NSLog("📱 🚀 QuizManagementViewModel INIT - Using lazy loading pattern")
        NSLog("📱 Repository type: %@", "\(type(of: self.quizRepository))")

        // Ensure LMS API configuration
        APIConfiguration.shared.setupLMS()

        // Note: No longer loading data in init - will be loaded lazily when view appears
        NSLog("📱 🚀 QuizManagementViewModel initialized, waiting for lazy load trigger")
    }

    // MARK: - LazyLoadableViewModel Implementation
    func initializeData() async {
        NSLog("📱 🚀 QuizManagementViewModel: initializeData called")
        AuthenticationDebugger.shared.log(
            component: "QuizManagementViewModel",
            event: "Initialize Data Started"
        )
        await loadQuizzes(refresh: false)
    }

    func refreshData() async {
        NSLog("📱 🚀 QuizManagementViewModel: refreshData called")
        await loadQuizzes(refresh: true)
    }
    
    // MARK: - Public Methods
    func loadQuizzes(refresh: Bool = false) async {
        NSLog("📱 ===== QUIZ VIEWMODEL LOAD =====")
        NSLog("📱 loadQuizzes called")
        NSLog("📱 Refresh: %@", refresh ? "true" : "false")
        NSLog("📱 Current page: %d", currentPage)
        NSLog("📱 Current quiz count: %d", quizzes.count)
        NSLog("📱 Current token: %@", TokenManager.shared.getToken() ?? "NO TOKEN")
        NSLog("📱 Timestamp: %@", "\(Date())")
        NSLog("📱 =================================")

        // 🎯 BYPASS TOKEN VALIDATION - Use token directly from login
        // Token will be automatically added by APIClient if available
        print("📱 🎯 Bypassing token validation - calling Quiz API directly")
        NSLog("📱 🎯 Bypassing token validation - calling Quiz API directly")

        // Check if we have a token at all
        guard let token = TokenManager.shared.getToken() else {
            print("📱 ❌ No token found - user needs to login")
            NSLog("📱 ❌ No token found - user needs to login")
            handleError(AuthError.tokenExpired)
            return
        }

        print("📱 ✅ Token found: \(String(token.prefix(20)))...")
        NSLog("📱 ✅ Token found: %@", String(token.prefix(20)) + "...")
        print("📱 🚀 Proceeding with Quiz API call")
        NSLog("📱 🚀 Proceeding with Quiz API call")

        // 🎯 Simple approach: Always call API to get fresh data
        if refresh {
            isRefreshing = true
            currentPage = 1
            hasMorePages = true
        } else {
            isLoading = true
        }

        errorMessage = nil
        showingError = false

        do {
            print("🎯 DEBUG: Creating filters...")
            let filters = QuizFilters(
                quizType: selectedQuizType,
                state: selectedState,
                search: searchText.isEmpty ? nil : searchText
            )

            print("🎯 DEBUG: Calling quizRepository.getQuizzes...")
            let response = try await quizRepository.getQuizzes(
                page: currentPage,
                limit: limit,
                filters: filters
            )

            NSLog("📱 ===== QUIZ VIEWMODEL SUCCESS =====")
            NSLog("📱 API call successful!")
            NSLog("📱 Quiz count received: %d", response.data.quizzes.count)
            NSLog("📱 Total count: %d", response.data.totalCount)
            NSLog("📱 Current page: %d", response.data.currentPage)
            NSLog("📱 Has next page: %@", response.data.hasNextPage ? "true" : "false")
            NSLog("📱 =====================================")

            NSLog("📱 🔍 QUIZ ASSIGNMENT DEBUG:")
            NSLog("📱 refresh: %@", refresh ? "true" : "false")
            NSLog("📱 currentPage: %d", currentPage)
            NSLog("📱 Current quizzes.count before assignment: %d", quizzes.count)

            // 🎯 Always replace with fresh data from API
            if refresh || currentPage == 1 {
                NSLog("📱 ✅ REPLACING quizzes array with fresh data")
                quizzes = response.data.quizzes
                NSLog("📱 ✅ After replacement - quizzes.count: %d", quizzes.count)
            } else {
                NSLog("📱 ➕ APPENDING to quizzes array for pagination")
                quizzes.append(contentsOf: response.data.quizzes)
                NSLog("📱 ➕ After append - quizzes.count: %d", quizzes.count)
            }

            totalCount = response.data.totalCount
            hasMorePages = response.data.hasNextPage

            if hasMorePages {
                currentPage += 1
            }

        } catch {
            print("📱 ===== QUIZ VIEWMODEL ERROR =====")
            print("📱 Error in loadQuizzes: \(error.localizedDescription)")
            print("📱 Error type: \(type(of: error))")
            print("📱 Full error: \(error)")
            print("📱 ==================================")

            NSLog("📱 ===== QUIZ VIEWMODEL ERROR =====")
            NSLog("📱 Error in loadQuizzes: %@", error.localizedDescription)
            NSLog("📱 Error type: %@", "\(type(of: error))")
            NSLog("📱 Full error: %@", "\(error)")
            NSLog("📱 ==================================")

            // 🚨 Check for cancelled request (NSURLError -999)
            if let urlError = error as? URLError, urlError.code == .cancelled {
                print("📱 🔄 Request was cancelled - likely due to app lifecycle or navigation")
                print("📱 🔄 Will retry automatically...")
                NSLog("📱 🔄 Request was cancelled - likely due to app lifecycle or navigation")
                NSLog("📱 🔄 Will retry automatically...")

                // Don't show error dialog for cancelled requests
                // Just retry after a short delay
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    Task {
                        await self.loadQuizzes(refresh: refresh)
                    }
                }
                return
            }

            // 🚨 Enhanced error handling with analysis
            let errorAnalysis = errorHandler.analyzeError(error)

            print("📱 ⚠️ Error category: \(errorAnalysis.category)")
            print("📱 ⚠️ Should logout: \(errorAnalysis.shouldLogout)")
            print("📱 ⚠️ Current quiz count: \(quizzes.count)")

            NSLog("📱 ⚠️ Error category: %@", "\(errorAnalysis.category)")
            NSLog("📱 ⚠️ Should logout: %@", errorAnalysis.shouldLogout ? "YES" : "NO")
            NSLog("📱 ⚠️ Current quiz count: %d", quizzes.count)

            // Handle logout if needed
            if errorAnalysis.shouldLogout {
                await authManager.logout()
            }

            // Show error dialog to user with enhanced message
            handleEnhancedError(errorAnalysis)
        }

        isLoading = false
        isRefreshing = false
        NSLog("📱 ===== QUIZ VIEWMODEL COMPLETED =====")
        NSLog("📱 loadQuizzes completed")
        NSLog("📱 Final quiz count: %d", quizzes.count)
        NSLog("📱 Is loading: %@", isLoading ? "true" : "false")
        NSLog("📱 Is refreshing: %@", isRefreshing ? "true" : "false")
        NSLog("📱 ======================================")
    }
    
    func loadMoreQuizzes() async {
        guard hasMorePages && !isLoading else { return }
        await loadQuizzes()
    }
    
    func createQuiz(_ request: CreateQuizRequest) async -> Bool {
        isLoading = true
        errorMessage = nil
        showingError = false
        
        do {
            let newQuiz = try await quizRepository.createQuiz(request)
            quizzes.insert(newQuiz, at: 0)
            totalCount += 1
            return true
        } catch {
            handleError(error)
            isLoading = false
            return false
        }
    }
    
    func markQuizReady(quizId: Int) async -> Bool {
        errorMessage = nil
        showingError = false
        
        do {
            let response = try await quizRepository.markQuizReady(quizId: quizId)
            
            // Update local quiz state
            if let index = quizzes.firstIndex(where: { $0.id == quizId }) {
                var updatedQuiz = quizzes[index]
                updatedQuiz = Quiz(
                    id: updatedQuiz.id,
                    name: updatedQuiz.name,
                    code: updatedQuiz.code,
                    description: updatedQuiz.description,
                    quizType: updatedQuiz.quizType,
                    subjectId: updatedQuiz.subjectId,
                    subjectName: updatedQuiz.subjectName,
                    classId: updatedQuiz.classId,
                    className: updatedQuiz.className,
                    maxScore: updatedQuiz.maxScore,
                    passingScore: updatedQuiz.passingScore,
                    timeLimit: updatedQuiz.timeLimit,
                    maxAttempts: updatedQuiz.maxAttempts,
                    isRandomized: updatedQuiz.isRandomized,
                    showCorrectAnswers: updatedQuiz.showCorrectAnswers,
                    state: response.data.state,
                    questionCount: updatedQuiz.questionCount,
                    studentCount: updatedQuiz.studentCount,
                    attemptCount: updatedQuiz.attemptCount,
                    averageScore: updatedQuiz.averageScore,
                    passRate: updatedQuiz.passRate,
                    pendingGradingCount: updatedQuiz.pendingGradingCount,
                    startDate: updatedQuiz.startDate,
                    endDate: updatedQuiz.endDate,
                    createdAt: updatedQuiz.createdAt,
                    updatedAt: Date()
                )
                quizzes[index] = updatedQuiz
            }
            
            return true
        } catch {
            handleError(error)
            return false
        }
    }
    
    func applyFilters() async {
        currentPage = 1
        quizzes.removeAll()
        hasMorePages = true
        showingFilters = false
        await loadQuizzes()
    }

    // 🔍 DEBUG: Test API connectivity and token
    func debugAPIConnection() {
        let timestamp = DateFormatter.debugTimestamp.string(from: Date())

        // Start debug session logging to file
        APILogger.shared.logDebugSessionStart()

        print("🔍 ===== DEBUG API CONNECTION =====")
        print("🔍 DEBUG BUTTON TAPPED!")
        print("🔍 Timestamp: \(timestamp)")
        print("🔍 Log file: \(APILogger.shared.getLogFilePath())")

        NSLog("🔍 ===== DEBUG API CONNECTION =====")
        NSLog("🔍 DEBUG BUTTON TAPPED!")
        NSLog("🔍 Timestamp: %@", timestamp)
        NSLog("🔍 Log file: %@", APILogger.shared.getLogFilePath())

        // 🎯 FORCE QUIZ API CALL - BYPASS TOKEN VALIDATION
        print("🎯 ===== FORCE QUIZ API CALL =====")
        NSLog("🎯 ===== FORCE QUIZ API CALL =====")

        Task {
            do {
                print("🎯 Attempting direct quiz API call...")
                NSLog("🎯 Attempting direct quiz API call...")

                let result = try await quizRepository.getQuizzes(page: 1, limit: 20, filters: nil)

                print("🎯 ✅ FORCE QUIZ CALL SUCCESS!")
                print("🎯 Quiz count: \(result.data.quizzes.count)")
                print("🎯 Total count: \(result.data.totalCount)")

                NSLog("🎯 ✅ FORCE QUIZ CALL SUCCESS!")
                NSLog("🎯 Quiz count: %d", result.data.quizzes.count)
                NSLog("🎯 Total count: %d", result.data.totalCount)

                await MainActor.run {
                    self.quizzes = result.data.quizzes
                    self.totalCount = result.data.totalCount
                    self.currentPage = result.data.page
                    self.hasMorePages = result.data.page < result.data.totalPages
                    self.isLoading = false
                    self.errorMessage = nil
                }

            } catch {
                print("🎯 ❌ FORCE QUIZ CALL ERROR: \(error)")
                NSLog("🎯 ❌ FORCE QUIZ CALL ERROR: %@", "\(error)")

                await MainActor.run {
                    self.errorMessage = "Force Quiz Call Error: \(error.localizedDescription)"
                    self.isLoading = false
                }
            }
        }

        // Check authentication state
        let isLoggedIn = TokenManager.shared.isLoggedIn
        print("🔍 Is logged in: \(isLoggedIn ? "YES" : "NO")")
        NSLog("🔍 Is logged in: %@", isLoggedIn ? "YES" : "NO")

        // Check token
        let token = TokenManager.shared.getToken()
        print("🔍 Token exists: \(token != nil ? "YES" : "NO")")
        NSLog("🔍 Token exists: %@", token != nil ? "YES" : "NO")

        if let token = token {
            print("🔍 Token length: \(token.count) characters")
            print("🔍 Token preview: \(String(token.prefix(20)))...")
            NSLog("🔍 Token length: %d characters", token.count)
            NSLog("🔍 Token preview: %@...", String(token.prefix(20)))

            // Log full token for debugging (remove in production)
            NSLog("🔍 FULL TOKEN (DEBUG ONLY): %@", token)
        } else {
            print("🔍 ❌ NO TOKEN FOUND - User needs to login!")
            NSLog("🔍 ❌ NO TOKEN FOUND - User needs to login!")
        }

        // Check user data
        if let user = TokenManager.shared.getUser(User.self) {
            print("🔍 User found: \(user.displayName) (ID: \(user.id))")
            print("🔍 User role: \(user.role.rawValue)")
            NSLog("🔍 User found: %@ (ID: %@)", user.displayName, user.id)
            NSLog("🔍 User role: %@", user.role.rawValue)
        } else {
            print("🔍 ❌ NO USER DATA FOUND")
            NSLog("🔍 ❌ NO USER DATA FOUND")
        }

        // Check API configuration
        let baseURL = APIConfiguration.shared.baseURL
        print("🔍 Base URL: \(baseURL)")
        NSLog("🔍 Base URL: %@", baseURL)

        let endpoint = "instructors/quizzes?page=1&limit=20"
        if let fullURL = APIConfiguration.shared.buildURL(endpoint: endpoint) {
            print("🔍 Full URL: \(fullURL.absoluteString)")
            NSLog("🔍 Full URL: %@", fullURL.absoluteString)
        } else {
            print("🔍 ❌ Failed to build URL for endpoint: \(endpoint)")
            NSLog("🔍 ❌ Failed to build URL for endpoint: %@", endpoint)
        }

        // Device and app information
        print("🔍 Device: \(UIDevice.current.model)")
        print("🔍 iOS Version: \(UIDevice.current.systemVersion)")
        print("🔍 App Version: \(Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "Unknown")")
        print("🔍 Is Simulator: \(isSimulator ? "YES" : "NO")")

        NSLog("🔍 Device: %@", UIDevice.current.model)
        NSLog("🔍 iOS Version: %@", UIDevice.current.systemVersion)
        NSLog("🔍 App Version: %@", Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "Unknown")
        NSLog("🔍 Is Simulator: %@", isSimulator ? "YES" : "NO")

        // Log authentication details to file
        let userInfo = if let user = TokenManager.shared.getUser(User.self) {
            "\(user.displayName) (ID: \(user.id), Role: \(user.role.rawValue))"
        } else {
            "No user data"
        }

        APILogger.shared.logAuthDetails(
            isLoggedIn: isLoggedIn,
            tokenExists: token != nil,
            tokenPreview: token != nil ? String(token!.prefix(20)) + "..." : nil,
            userInfo: userInfo
        )

        print("🔍 ================================")
        NSLog("🔍 ================================")

        // Trigger a test API call to capture full request/response
        Task {
            await testAPICall()
        }
    }

    // Test API call to capture full request/response details
    private func testAPICall() async {
        print("🧪 ===== TEST API CALL =====")
        NSLog("🧪 ===== TEST API CALL =====")

        do {
            let startTime = Date()
            print("🧪 Starting test API call at: \(DateFormatter.debugTimestamp.string(from: startTime))")
            NSLog("🧪 Starting test API call at: %@", DateFormatter.debugTimestamp.string(from: startTime))

            let response = try await quizRepository.getQuizzes(page: 1, limit: 20, filters: QuizFilters())

            let endTime = Date()
            let duration = endTime.timeIntervalSince(startTime) * 1000 // milliseconds

            print("🧪 ✅ TEST API CALL SUCCESSFUL")
            print("🧪 Duration: \(String(format: "%.2f", duration))ms")
            print("🧪 Quiz count: \(response.data.quizzes.count)")
            print("🧪 Total count: \(response.data.totalCount)")
            print("🧪 Success: \(response.success)")
            print("🧪 Message: \(response.message)")

            NSLog("🧪 ✅ TEST API CALL SUCCESSFUL")
            NSLog("🧪 Duration: %.2fms", duration)
            NSLog("🧪 Quiz count: %d", response.data.quizzes.count)
            NSLog("🧪 Total count: %d", response.data.totalCount)
            NSLog("🧪 Success: %@", response.success ? "YES" : "NO")
            NSLog("🧪 Message: %@", response.message)

        } catch {
            print("🧪 ❌ TEST API CALL FAILED")
            print("🧪 Error: \(error)")
            print("🧪 Error type: \(type(of: error))")
            print("🧪 Error description: \(error.localizedDescription)")

            NSLog("🧪 ❌ TEST API CALL FAILED")
            NSLog("🧪 Error: %@", "\(error)")
            NSLog("🧪 Error type: %@", "\(type(of: error))")
            NSLog("🧪 Error description: %@", error.localizedDescription)

            // Analyze the error
            let errorAnalysis = errorHandler.analyzeError(error)
            print("🧪 Error category: \(errorAnalysis.category)")
            print("🧪 User message: \(errorAnalysis.userMessage)")
            print("🧪 Should logout: \(errorAnalysis.shouldLogout)")

            NSLog("🧪 Error category: %@", "\(errorAnalysis.category)")
            NSLog("🧪 User message: %@", errorAnalysis.userMessage)
            NSLog("🧪 Should logout: %@", errorAnalysis.shouldLogout ? "YES" : "NO")
        }

        print("🧪 ===========================")
        NSLog("🧪 ===========================")
    }

    private var isSimulator: Bool {
        #if targetEnvironment(simulator)
        return true
        #else
        return false
        #endif
    }
    
    func clearFilters() async {
        selectedQuizType = nil
        selectedState = nil
        searchText = ""
        await applyFilters()
    }
    
    func refreshQuizzes() async {
        NSLog("📱 🔄 Force refresh - getting latest data from API")
        await loadQuizzes(refresh: true)
    }

    // 🎯 Method to ensure fresh data is always fetched with proper task management
    func fetchLatestQuizzes() async {
        NSLog("📱 🆕 Fetching latest quizzes from API")

        // Cancel any existing task to prevent overlapping calls
        currentTask?.cancel()

        currentTask = Task {
            await performFetch(refresh: true)
        }

        await currentTask?.value
    }

    // 🎯 Safe fetch method that prevents overlapping calls
    private func performFetch(refresh: Bool) async {
        // Prevent multiple simultaneous calls
        guard !Task.isCancelled else {
            NSLog("📱 🚫 Fetch cancelled - another call in progress")
            return
        }

        currentPage = 1
        hasMorePages = true
        await loadQuizzes(refresh: refresh)
        hasInitialized = true
    }

    // MARK: - Debug Helper
    func exportDebugLogs() -> String? {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let logFile = documentsPath.appendingPathComponent("quiz_debug.log")

        if FileManager.default.fileExists(atPath: logFile.path) {
            return try? String(contentsOf: logFile)
        }
        return nil
    }
    
    // MARK: - Computed Properties
    var hasActiveFilters: Bool {
        return selectedQuizType != nil || selectedState != nil || !searchText.isEmpty
    }
    
    var filteredQuizzesCount: String {
        if hasActiveFilters {
            return "\(totalCount) kết quả"
        } else {
            return "\(totalCount) quiz"
        }
    }
    
    // MARK: - Private Methods
    private func handleError(_ error: Error) {
        print("🚨 QuizManagementViewModel Error: \(error)")

        // 🚨 Check for cancelled request (NSURLError -999) - Don't show error for these
        if let urlError = error as? URLError, urlError.code == .cancelled {
            print("🚨 🔄 Request was cancelled - not showing error dialog")
            NSLog("🚨 🔄 Request was cancelled - not showing error dialog")
            return
        }

        // 🚨 Check for Task cancellation - Don't show error for these
        if error is CancellationError {
            print("🚨 🔄 Task was cancelled - not showing error dialog")
            NSLog("🚨 🔄 Task was cancelled - not showing error dialog")
            return
        }

        // 🚨 Only show error if we don't have any data yet (first load)
        // For subsequent refreshes, just log the error but don't show popup
        if hasInitialized && !quizzes.isEmpty {
            print("🚨 📱 Error during refresh - not showing popup (user has data)")
            NSLog("🚨 📱 Error during refresh - not showing popup (user has data)")
            return
        }

        // 🔍 ENHANCED ERROR DEBUGGING - Following Gemini's recommendations
        NSLog("🚨 ===== ENHANCED ERROR ANALYSIS =====")
        NSLog("🚨 Error: %@", "\(error)")
        NSLog("🚨 Error type: %@", "\(type(of: error))")
        NSLog("🚨 Error localized: %@", error.localizedDescription)

        if let nsError = error as NSError? {
            NSLog("🚨 NSError domain: %@", nsError.domain)
            NSLog("🚨 NSError code: %d", nsError.code)
            NSLog("🚨 NSError userInfo: %@", "\(nsError.userInfo)")
        }

        if let urlError = error as? URLError {
            NSLog("🚨 URLError code: %@", "\(urlError.code)")
            NSLog("🚨 URLError description: %@", urlError.localizedDescription)
        }

        // Check token status
        let hasToken = TokenManager.shared.getToken() != nil
        NSLog("🚨 Has auth token: %@", hasToken ? "YES" : "NO")
        if hasToken {
            NSLog("🚨 Token: %@", TokenManager.shared.getToken() ?? "nil")
        }
        NSLog("🚨 =====================================")

        // 🎯 Simple error handling - encourage retry to get fresh data
        if let decodingError = error as? DecodingError {
            print("🚨 Decoding Error: \(decodingError)")

            // Enhanced decoding error analysis
            switch decodingError {
            case .keyNotFound(let key, let context):
                NSLog("🚨 DecodingError - Key not found: %@ at path: %@", key.stringValue, "\(context.codingPath)")
                errorMessage = "Dữ liệu không đúng định dạng (thiếu key: \(key.stringValue)). Vui lòng thử lại."
            case .typeMismatch(let type, let context):
                NSLog("🚨 DecodingError - Type mismatch: %@ at path: %@", "\(type)", "\(context.codingPath)")
                errorMessage = "Dữ liệu không đúng định dạng (sai kiểu dữ liệu). Vui lòng thử lại."
            case .valueNotFound(let type, let context):
                NSLog("🚨 DecodingError - Value not found: %@ at path: %@", "\(type)", "\(context.codingPath)")
                errorMessage = "Dữ liệu không đúng định dạng (thiếu giá trị). Vui lòng thử lại."
            case .dataCorrupted(let context):
                NSLog("🚨 DecodingError - Data corrupted at path: %@", "\(context.codingPath)")
                errorMessage = "Dữ liệu bị lỗi. Vui lòng thử lại."
            @unknown default:
                errorMessage = "Không thể tải danh sách quiz. Vui lòng thử lại để lấy dữ liệu mới nhất."
            }
        } else if let apiError = error as? APIError {
            print("🚨 API Error: \(apiError.message)")
            errorMessage = "\(apiError.message). Vui lòng thử lại để lấy dữ liệu mới nhất."
        } else if let networkError = error as? NetworkError {
            print("🚨 Network Error: \(networkError.localizedDescription)")
            switch networkError {
            case .unauthorized:
                errorMessage = "Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại."
            case .networkUnavailable:
                errorMessage = "Không có kết nối internet. Vui lòng kiểm tra và thử lại."
            case .serverError(let code, let message):
                errorMessage = "\(message ?? "Lỗi server (\(code))"). Vui lòng thử lại sau."
            case .decodingError(let decodingError):
                NSLog("🚨 NetworkError.decodingError: %@", "\(decodingError)")
                errorMessage = "Lỗi xử lý dữ liệu từ server. Vui lòng thử lại."
            default:
                errorMessage = "Lỗi kết nối. Vui lòng thử lại để lấy dữ liệu mới nhất."
            }
        } else if (error as NSError).code == NSURLErrorTimedOut {
            print("🚨 Timeout Error")
            errorMessage = "Kết nối quá chậm. Vui lòng thử lại để lấy dữ liệu mới nhất."
        } else {
            print("🚨 Unknown Error: \(error.localizedDescription)")
            NSLog("🚨 This is the 'unknown error' case that shows generic message!")
            errorMessage = "Đã xảy ra lỗi không xác định. Vui lòng thử lại sau."
        }
        showingError = true
    }

    private func handleEnhancedError(_ analysis: ErrorAnalysis) {
        print("🚨 ===== ENHANCED ERROR HANDLER =====")
        print("🚨 Category: \(analysis.category)")
        print("🚨 User Message: \(analysis.userMessage)")
        print("🚨 Technical Message: \(analysis.technicalMessage)")
        print("🚨 Should Retry: \(analysis.shouldRetry)")
        print("🚨 Should Logout: \(analysis.shouldLogout)")

        NSLog("🚨 ===== ENHANCED ERROR HANDLER =====")
        NSLog("🚨 Category: %@", "\(analysis.category)")
        NSLog("🚨 User Message: %@", analysis.userMessage)
        NSLog("🚨 Technical Message: %@", analysis.technicalMessage)
        NSLog("🚨 Should Retry: %@", analysis.shouldRetry ? "YES" : "NO")
        NSLog("🚨 Should Logout: %@", analysis.shouldLogout ? "YES" : "NO")

        // Set user-friendly error message
        errorMessage = analysis.userMessage
        print("🚨 🔥 SHOWING ERROR DIALOG: \(analysis.userMessage)")
        NSLog("🚨 🔥 SHOWING ERROR DIALOG: %@", analysis.userMessage)
        showingError = true
    }
}

// MARK: - Quiz Statistics
extension QuizManagementViewModel {
    var draftQuizzesCount: Int {
        return quizzes.filter { $0.state == .draft }.count
    }
    
    var readyQuizzesCount: Int {
        return quizzes.filter { $0.state == .ready }.count
    }
    
    var publishedQuizzesCount: Int {
        return quizzes.filter { $0.state == .published }.count
    }
    
    var pendingGradingCount: Int {
        return quizzes.reduce(0) { $0 + $1.pendingGradingCount }
    }
    
    var averageCompletionRate: Double {
        let rates = quizzes.compactMap { quiz in
            quiz.studentCount > 0 ? quiz.completionRate : nil
        }
        guard !rates.isEmpty else { return 0 }
        return rates.reduce(0, +) / Double(rates.count)
    }
}
