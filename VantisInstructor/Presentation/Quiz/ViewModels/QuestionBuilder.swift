//
//  QuestionBuilder.swift
//  mobile-app-template
//
//  Created by Inst<PERSON>ctor App on 27/7/25.
//

import Foundation
import Combine

// MARK: - Question Builder Protocol
protocol QuestionBuilderProtocol: ObservableObject {
    var id: UUID { get }
    var questionType: QuestionType { get }
    var text: String { get set }
    var score: Double { get set }
    var isValid: Bool { get }
    
    func validate() -> [ValidationError]
    func toCreateQuestionRequest() -> CreateQuestionRequest
}

// MARK: - Base Question Builder
class QuestionBuilder: QuestionBuilderProtocol, ObservableObject {
    let id = UUID()
    let questionType: QuestionType
    @Published var text: String = ""
    @Published var score: Double = 10.0
    
    var isValid: Bool {
        return validate().isEmpty
    }
    
    init(questionType: QuestionType) {
        self.questionType = questionType
    }
    
    func validate() -> [ValidationError] {
        var errors: [ValidationError] = []
        
        if text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            errors.append(.requiredFieldEmpty("Câu hỏi"))
        }
        
        if text.count > 1000 {
            errors.append(.invalidFormat("Câu hỏi không được vượt quá 1000 ký tự"))
        }
        
        if score <= 0 {
            errors.append(.invalidFormat("Điểm số phải lớn hơn 0"))
        }
        
        if score > 100 {
            errors.append(.invalidFormat("Điểm số không được vượt quá 100"))
        }
        
        return errors
    }
    
    func toCreateQuestionRequest() -> CreateQuestionRequest {
        fatalError("Subclasses must implement toCreateQuestionRequest()")
    }
}

// MARK: - Essay Question Builder
class EssayQuestionBuilder: QuestionBuilder {
    @Published var expectedLength: Int = 100 // Expected word count
    @Published var gradingCriteria: String = ""
    
    override init(questionType: QuestionType = .essay) {
        super.init(questionType: questionType)
    }
    
    override func validate() -> [ValidationError] {
        var errors = super.validate()
        
        if expectedLength <= 0 {
            errors.append(.invalidFormat("Độ dài mong đợi phải lớn hơn 0"))
        }
        
        return errors
    }
    
    override func toCreateQuestionRequest() -> CreateQuestionRequest {
        return CreateQuestionRequest(
            text: text.trimmingCharacters(in: .whitespacesAndNewlines),
            questionType: questionType,
            score: score,
            explanation: nil, // Can be added later if needed
            answers: [] // Essay questions don't have predefined answers
        )
    }
}

// MARK: - Answer Builder
class AnswerBuilder: ObservableObject {
    let id = UUID()
    @Published var text: String = ""
    @Published var isCorrect: Bool = false
    
    var isValid: Bool {
        return !text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    
    func toCreateAnswerRequest() -> CreateAnswerRequest {
        return CreateAnswerRequest(
            text: text.trimmingCharacters(in: .whitespacesAndNewlines),
            isCorrect: isCorrect,
            explanation: nil // Can be added later if needed
        )
    }
}

// MARK: - Single Choice Question Builder
class SingleChoiceQuestionBuilder: QuestionBuilder {
    @Published var answers: [AnswerBuilder] = []
    @Published var correctAnswerIndex: Int = 0
    
    override init(questionType: QuestionType = .singleChoice) {
        super.init(questionType: questionType)
        setupDefaultAnswers()
    }
    
    private func setupDefaultAnswers() {
        // Create 4 default answer options
        for i in 0..<4 {
            let answer = AnswerBuilder()
            answer.text = ""
            answer.isCorrect = (i == 0) // First option is correct by default
            answers.append(answer)
        }
    }
    
    override func validate() -> [ValidationError] {
        var errors = super.validate()
        
        // Check if we have at least 2 answers
        let validAnswers = answers.filter { $0.isValid }
        if validAnswers.count < 2 {
            errors.append(.invalidFormat("Phải có ít nhất 2 đáp án"))
        }
        
        // Check if exactly one answer is correct
        let correctAnswers = answers.filter { $0.isCorrect && $0.isValid }
        if correctAnswers.count != 1 {
            errors.append(.invalidFormat("Phải có đúng 1 đáp án đúng"))
        }
        
        return errors
    }
    
    func addAnswer() {
        let newAnswer = AnswerBuilder()
        answers.append(newAnswer)
    }
    
    func removeAnswer(at index: Int) {
        guard index >= 0 && index < answers.count && answers.count > 2 else { return }
        
        // If removing the correct answer, set the first remaining answer as correct
        if answers[index].isCorrect {
            answers.removeAll { $0.isCorrect }
            if index > 0 {
                answers[0].isCorrect = true
            } else if answers.count > 1 {
                answers[1].isCorrect = true
            }
        }
        
        answers.remove(at: index)
    }
    
    func setCorrectAnswer(at index: Int) {
        guard index >= 0 && index < answers.count else { return }
        
        // Clear all correct answers
        answers.forEach { $0.isCorrect = false }
        
        // Set the selected answer as correct
        answers[index].isCorrect = true
        correctAnswerIndex = index
    }
    
    override func toCreateQuestionRequest() -> CreateQuestionRequest {
        let validAnswers = answers.filter { $0.isValid }
        let answerRequests = validAnswers.map { $0.toCreateAnswerRequest() }

        return CreateQuestionRequest(
            text: text.trimmingCharacters(in: .whitespacesAndNewlines),
            questionType: questionType,
            score: score,
            explanation: nil, // Can be added later if needed
            answers: answerRequests
        )
    }
}

// MARK: - Multiple Choice Question Builder
class MultipleChoiceQuestionBuilder: QuestionBuilder {
    @Published var answers: [AnswerBuilder] = []
    @Published var minCorrectAnswers: Int = 1
    @Published var maxCorrectAnswers: Int = 3
    
    override init(questionType: QuestionType = .multipleChoice) {
        super.init(questionType: questionType)
        setupDefaultAnswers()
    }
    
    private func setupDefaultAnswers() {
        // Create 4 default answer options
        for i in 0..<4 {
            let answer = AnswerBuilder()
            answer.text = ""
            answer.isCorrect = (i < 2) // First two options are correct by default
            answers.append(answer)
        }
    }
    
    override func validate() -> [ValidationError] {
        var errors = super.validate()
        
        // Check if we have at least 2 answers
        let validAnswers = answers.filter { $0.isValid }
        if validAnswers.count < 2 {
            errors.append(.invalidFormat("Phải có ít nhất 2 đáp án"))
        }
        
        // Check if we have at least one correct answer
        let correctAnswers = answers.filter { $0.isCorrect && $0.isValid }
        if correctAnswers.isEmpty {
            errors.append(.invalidFormat("Phải có ít nhất 1 đáp án đúng"))
        }
        
        // Check if we don't have too many correct answers
        if correctAnswers.count > maxCorrectAnswers {
            errors.append(.invalidFormat("Không được có quá \(maxCorrectAnswers) đáp án đúng"))
        }
        
        return errors
    }
    
    func addAnswer() {
        let newAnswer = AnswerBuilder()
        answers.append(newAnswer)
    }
    
    func removeAnswer(at index: Int) {
        guard index >= 0 && index < answers.count && answers.count > 2 else { return }
        answers.remove(at: index)
    }
    
    func toggleAnswerCorrectness(at index: Int) {
        guard index >= 0 && index < answers.count else { return }
        answers[index].isCorrect.toggle()
    }
    
    override func toCreateQuestionRequest() -> CreateQuestionRequest {
        let validAnswers = answers.filter { $0.isValid }
        let answerRequests = validAnswers.map { $0.toCreateAnswerRequest() }

        return CreateQuestionRequest(
            text: text.trimmingCharacters(in: .whitespacesAndNewlines),
            questionType: questionType,
            score: score,
            explanation: nil, // Can be added later if needed
            answers: answerRequests
        )
    }
}

// MARK: - True/False Question Builder
class TrueFalseQuestionBuilder: QuestionBuilder {
    @Published var correctAnswer: Bool = true
    @Published var explanation: String = ""
    
    override init(questionType: QuestionType = .trueFalse) {
        super.init(questionType: questionType)
    }
    
    override func toCreateQuestionRequest() -> CreateQuestionRequest {
        let trueAnswer = CreateAnswerRequest(text: "Đúng", isCorrect: correctAnswer, explanation: nil)
        let falseAnswer = CreateAnswerRequest(text: "Sai", isCorrect: !correctAnswer, explanation: nil)

        return CreateQuestionRequest(
            text: text.trimmingCharacters(in: .whitespacesAndNewlines),
            questionType: questionType,
            score: score,
            explanation: nil, // Can be added later if needed
            answers: [trueAnswer, falseAnswer]
        )
    }
}

// MARK: - Question Builder Factory
class QuestionBuilderFactory {
    static func createQuestionBuilder(for type: QuestionType) -> QuestionBuilder {
        switch type {
        case .essay:
            return EssayQuestionBuilder()
        case .singleChoice:
            return SingleChoiceQuestionBuilder()
        case .multipleChoice:
            return MultipleChoiceQuestionBuilder()
        case .trueFalse:
            return TrueFalseQuestionBuilder()
        }
    }
}
