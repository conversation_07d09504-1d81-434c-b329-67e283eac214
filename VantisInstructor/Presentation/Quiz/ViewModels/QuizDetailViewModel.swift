//
//  QuizDetailViewModel.swift
//  mobile-app-template
//
//  Created by Instructor App on 27/7/25.
//

import Foundation
import Combine

// MARK: - Quiz Detail Tab
enum QuizDetailTab: String, CaseIterable {
    case overview = "Tổng quan"
    case analytics = "Thống kê"
    case questions = "Câu hỏi"
    case attempts = "Bài làm"
    
    var icon: String {
        switch self {
        case .overview: return "doc.text"
        case .analytics: return "chart.bar"
        case .questions: return "questionmark.circle"
        case .attempts: return "person.2"
        }
    }
}

// MARK: - Quiz Action
enum QuizAction: String, CaseIterable {
    case edit = "Chỉnh sửa"
    case assign = "Gán vào bài học"
    case markReady = "Đánh dấu sẵn sàng"
    case publish = "Phát hành"
    case unpublish = "Hủy phát hành"
    case archive = "Lưu trữ"
    case delete = "Xóa"
    
    var icon: String {
        switch self {
        case .edit: return "pencil"
        case .assign: return "plus.circle"
        case .markReady: return "checkmark.circle"
        case .publish: return "paperplane"
        case .unpublish: return "paperplane.fill"
        case .archive: return "archivebox"
        case .delete: return "trash"
        }
    }
    
    var color: String {
        switch self {
        case .edit: return "blue"
        case .assign: return "green"
        case .markReady: return "green"
        case .publish: return "blue"
        case .unpublish: return "orange"
        case .archive: return "gray"
        case .delete: return "red"
        }
    }
    
    func isAvailable(for state: QuizState) -> Bool {
        switch self {
        case .edit:
            return state == .draft || state == .ready
        case .assign:
            return state == .ready || state == .published
        case .markReady:
            return state == .draft
        case .publish:
            return state == .ready
        case .unpublish:
            return state == .published
        case .archive:
            return state == .published || state == .ready
        case .delete:
            return state == .draft || state == .archived
        }
    }
}

// MARK: - Quiz Detail ViewModel
@MainActor
class QuizDetailViewModel: ObservableObject {
    // MARK: - Published Properties
    
    // Core Data
    @Published var quiz: Quiz
    @Published var analytics: QuizAnalytics?
    @Published var questions: [Question] = []
    @Published var attempts: [QuizAttempt] = []
    
    // UI State
    @Published var selectedTab: QuizDetailTab = .overview
    @Published var isLoading = false
    @Published var isLoadingAnalytics = false
    @Published var isLoadingQuestions = false
    @Published var isLoadingAttempts = false
    
    // Error Handling
    @Published var showingError = false
    @Published var errorMessage: String?
    
    // Action States
    @Published var isPerformingAction = false
    @Published var showingActionConfirmation = false
    @Published var pendingAction: QuizAction?
    
    // Editing
    @Published var showingEditQuiz = false

    // Assignment
    @Published var showingAssignment = false
    
    // MARK: - Dependencies
    private let quizRepository: QuizRepositoryProtocol
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Computed Properties
    var availableActions: [QuizAction] {
        return QuizAction.allCases.filter { $0.isAvailable(for: quiz.state) }
    }
    
    var canEdit: Bool {
        return quiz.state == .draft || quiz.state == .ready
    }
    
    var canMarkReady: Bool {
        return quiz.state == .draft && quiz.questionCount > 0
    }
    
    var canPublish: Bool {
        return quiz.state == .ready
    }
    
    var canUnpublish: Bool {
        return quiz.state == .published
    }
    
    var statusColor: String {
        return quiz.state.color
    }
    
    var statusIcon: String {
        switch quiz.state {
        case .draft: return "doc.text"
        case .ready: return "checkmark.circle"
        case .published: return "paperplane.fill"
        case .archived: return "archivebox.fill"
        }
    }
    
    // Analytics computed properties
    var hasAnalytics: Bool {
        return analytics != nil && quiz.attemptCount > 0
    }
    
    var completionRate: Double {
        return analytics?.completionRate ?? quiz.completionRate
    }
    
    var averageScore: Double {
        return analytics?.averageScore ?? quiz.averageScore
    }
    
    var passRate: Double {
        return analytics?.passRate ?? quiz.passRate
    }
    
    // MARK: - Initialization
    init(quiz: Quiz, quizRepository: QuizRepositoryProtocol = QuizRepository()) {
        self.quiz = quiz
        self.quizRepository = quizRepository
        setupObservers()
    }
    
    // MARK: - Setup
    private func setupObservers() {
        // Auto-refresh analytics when tab changes to analytics
        $selectedTab
            .filter { $0 == .analytics }
            .sink { [weak self] _ in
                Task {
                    await self?.loadAnalytics()
                }
            }
            .store(in: &cancellables)
        
        // Auto-refresh questions when tab changes to questions
        $selectedTab
            .filter { $0 == .questions }
            .sink { [weak self] _ in
                Task {
                    await self?.loadQuestions()
                }
            }
            .store(in: &cancellables)
        
        // Auto-refresh attempts when tab changes to attempts
        $selectedTab
            .filter { $0 == .attempts }
            .sink { [weak self] _ in
                Task {
                    await self?.loadAttempts()
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Data Loading Methods
    func loadInitialData() async {
        isLoading = true
        defer { isLoading = false }
        
        // Load basic analytics for overview
        if quiz.attemptCount > 0 {
            await loadAnalytics()
        }
    }
    
    func loadAnalytics() async {
        guard quiz.attemptCount > 0 else { return }
        
        isLoadingAnalytics = true
        errorMessage = nil
        showingError = false
        
        do {
            let analyticsData = try await quizRepository.getQuizAnalytics(quizId: quiz.id)
            analytics = analyticsData
        } catch {
            handleError(error, context: "Không thể tải thống kê quiz")
        }
        
        isLoadingAnalytics = false
    }
    
    func loadQuestions() async {
        isLoadingQuestions = true
        errorMessage = nil
        showingError = false
        
        // TODO: Implement getQuizQuestions method in repository
        // For now, we'll simulate loading
        try? await Task.sleep(nanoseconds: 500_000_000)
        
        isLoadingQuestions = false
    }
    
    func loadAttempts() async {
        guard quiz.attemptCount > 0 else { return }
        
        isLoadingAttempts = true
        errorMessage = nil
        showingError = false
        
        // TODO: Implement getQuizAttempts method in repository
        // For now, we'll simulate loading
        try? await Task.sleep(nanoseconds: 500_000_000)
        
        isLoadingAttempts = false
    }
    
    func refreshData() async {
        switch selectedTab {
        case .overview:
            await loadInitialData()
        case .analytics:
            await loadAnalytics()
        case .questions:
            await loadQuestions()
        case .attempts:
            await loadAttempts()
        }
    }
    
    // MARK: - Error Handling
    private func handleError(_ error: Error, context: String? = nil) {
        if let appError = error as? AppError {
            errorMessage = appError.message
        } else {
            errorMessage = context ?? "Đã xảy ra lỗi không xác định"
        }
        showingError = true
    }
    
    // MARK: - Action Methods
    func performAction(_ action: QuizAction) {
        pendingAction = action
        showingActionConfirmation = true
    }
    
    func confirmAction() async -> Bool {
        guard let action = pendingAction else { return false }
        
        isPerformingAction = true
        defer { 
            isPerformingAction = false
            pendingAction = nil
            showingActionConfirmation = false
        }
        
        switch action {
        case .edit:
            showingEditQuiz = true
            return true
        case .assign:
            showingAssignment = true
            return true
        case .markReady:
            return await markQuizReady()
        case .publish:
            return await publishQuiz()
        case .unpublish:
            return await unpublishQuiz()
        case .archive:
            return await archiveQuiz()
        case .delete:
            return await deleteQuiz()
        }
    }
    
    private func markQuizReady() async -> Bool {
        do {
            let response = try await quizRepository.markQuizReady(quizId: quiz.id)
            // Update quiz state
            quiz = Quiz(
                id: quiz.id,
                name: quiz.name,
                code: quiz.code,
                description: quiz.description,
                quizType: quiz.quizType,
                subjectId: quiz.subjectId,
                subjectName: quiz.subjectName,
                classId: quiz.classId,
                className: quiz.className,
                maxScore: quiz.maxScore,
                passingScore: quiz.passingScore,
                timeLimit: quiz.timeLimit,
                maxAttempts: quiz.maxAttempts,
                isRandomized: quiz.isRandomized ?? false,
                showCorrectAnswers: quiz.showCorrectAnswers,
                state: .ready,
                questionCount: quiz.questionCount,
                studentCount: quiz.studentCount,
                attemptCount: quiz.attemptCount,
                averageScore: quiz.averageScore,
                passRate: quiz.passRate,
                pendingGradingCount: quiz.pendingGradingCount,
                startDate: quiz.startDate,
                endDate: quiz.endDate,
                createdAt: quiz.createdAt,
                updatedAt: Date()
            )
            return true
        } catch {
            handleError(error, context: "Không thể đánh dấu quiz sẵn sàng")
            return false
        }
    }
    
    private func publishQuiz() async -> Bool {
        // Note: This requires lessonId which we don't have in this context
        // In real implementation, this would be handled differently
        do {
            // For now, simulate success
            try await Task.sleep(nanoseconds: 500_000_000)

            // Update quiz state
            updateQuizState(.published)
            return true
        } catch {
            handleError(error, context: "Không thể phát hành quiz")
            return false
        }
    }

    private func unpublishQuiz() async -> Bool {
        // Note: This requires lessonId which we don't have in this context
        do {
            // For now, simulate success
            try await Task.sleep(nanoseconds: 500_000_000)

            // Update quiz state
            updateQuizState(.ready)
            return true
        } catch {
            handleError(error, context: "Không thể hủy phát hành quiz")
            return false
        }
    }

    private func archiveQuiz() async -> Bool {
        do {
            let response = try await quizRepository.archiveQuiz(quizId: quiz.id)

            // Update quiz state
            updateQuizState(.archived)
            return true
        } catch {
            handleError(error, context: "Không thể lưu trữ quiz")
            return false
        }
    }

    private func deleteQuiz() async -> Bool {
        do {
            let response = try await quizRepository.deleteQuiz(quizId: quiz.id)

            // In real implementation, this would navigate back to quiz list
            // For now, just return success
            return true
        } catch {
            handleError(error, context: "Không thể xóa quiz")
            return false
        }
    }

    // MARK: - Helper Methods
    private func updateQuizState(_ newState: QuizState) {
        quiz = Quiz(
            id: quiz.id,
            name: quiz.name,
            code: quiz.code,
            description: quiz.description,
            quizType: quiz.quizType,
            subjectId: quiz.subjectId,
            subjectName: quiz.subjectName,
            classId: quiz.classId,
            className: quiz.className,
            maxScore: quiz.maxScore,
            passingScore: quiz.passingScore,
            timeLimit: quiz.timeLimit,
            maxAttempts: quiz.maxAttempts,
            isRandomized: quiz.isRandomized ?? false,
            showCorrectAnswers: quiz.showCorrectAnswers,
            state: newState,
            questionCount: quiz.questionCount,
            studentCount: quiz.studentCount,
            attemptCount: quiz.attemptCount,
            averageScore: quiz.averageScore,
            passRate: quiz.passRate,
            pendingGradingCount: quiz.pendingGradingCount,
            startDate: quiz.startDate,
            endDate: quiz.endDate,
            createdAt: quiz.createdAt,
            updatedAt: Date()
        )
    }

    // MARK: - Tab Management
    func selectTab(_ tab: QuizDetailTab) {
        selectedTab = tab
    }

    // MARK: - Refresh Methods
    func refreshCurrentTab() async {
        await refreshData()
    }

    // MARK: - Analytics Helpers
    func getScoreDistributionData() -> [ScoreRange] {
        return analytics?.scoreDistribution ?? []
    }

    func getAttemptsByDateData() -> [AttemptsByDate] {
        return analytics?.attemptsByDate ?? []
    }

    func getQuestionAnalyticsData() -> [QuestionAnalytics] {
        return analytics?.questionAnalytics ?? []
    }

    // MARK: - Formatting Helpers
    func formatScore(_ score: Double) -> String {
        return String(format: "%.1f", score)
    }

    func formatPercentage(_ percentage: Double) -> String {
        return String(format: "%.1f%%", percentage)
    }

    func formatTimeSpent(_ seconds: Int) -> String {
        let formatter = DateComponentsFormatter()
        formatter.allowedUnits = [.hour, .minute]
        formatter.unitsStyle = .abbreviated
        return formatter.string(from: TimeInterval(seconds)) ?? "0m"
    }

    func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}
