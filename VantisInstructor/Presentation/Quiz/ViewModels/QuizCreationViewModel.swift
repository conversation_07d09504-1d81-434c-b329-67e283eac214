//
//  QuizCreationViewModel.swift
//  mobile-app-template
//
//  Created by Instructor App on 27/7/25.
//

import Foundation
import Combine

// MARK: - Creation Step Enum
enum CreationStep: String, CaseIterable {
    case basicInfo = "Thông tin cơ bản"
    case questions = "Câu hỏi"
    case settings = "Cài đặt"
    case preview = "Xem trước"
    
    var title: String {
        return self.rawValue
    }
    
    var progress: Double {
        switch self {
        case .basicInfo: return 0.25
        case .questions: return 0.5
        case .settings: return 0.75
        case .preview: return 1.0
        }
    }
    
    var stepNumber: Int {
        switch self {
        case .basicInfo: return 1
        case .questions: return 2
        case .settings: return 3
        case .preview: return 4
        }
    }
}

// MARK: - Quiz Basic Info Model
struct QuizBasicInfo {
    var name: String = ""
    var description: String = ""
    var quizType: QuizType = .quiz
    var subjectId: Int? = nil
    var classId: Int? = nil
    
    var isValid: Bool {
        return !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    
    var validationErrors: [String] {
        var errors: [String] = []
        
        if name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            errors.append("Tên quiz không được để trống")
        }
        
        if name.count > 100 {
            errors.append("Tên quiz không được vượt quá 100 ký tự")
        }
        
        if description.count > 500 {
            errors.append("Mô tả không được vượt quá 500 ký tự")
        }
        
        return errors
    }
}

// MARK: - Quiz Settings Model
struct QuizSettings {
    var maxScore: Double = 100.0
    var passingScore: Double = 50.0
    var timeLimit: Int? = nil
    var maxAttempts: Int = 1
    var isRandomized: Bool = false
    var showCorrectAnswers: Bool = false
    
    var isValid: Bool {
        return maxScore > 0 && 
               passingScore >= 0 && 
               passingScore <= maxScore &&
               maxAttempts > 0 &&
               (timeLimit == nil || timeLimit! > 0)
    }
    
    var validationErrors: [String] {
        var errors: [String] = []
        
        if maxScore <= 0 {
            errors.append("Điểm tối đa phải lớn hơn 0")
        }
        
        if passingScore < 0 {
            errors.append("Điểm đạt không được âm")
        }
        
        if passingScore > maxScore {
            errors.append("Điểm đạt không được lớn hơn điểm tối đa")
        }
        
        if maxAttempts <= 0 {
            errors.append("Số lần làm bài phải lớn hơn 0")
        }
        
        if let timeLimit = timeLimit, timeLimit <= 0 {
            errors.append("Thời gian làm bài phải lớn hơn 0")
        }
        
        return errors
    }
}

// MARK: - Quiz Creation ViewModel
@MainActor
class QuizCreationViewModel: ObservableObject {
    // MARK: - Published Properties
    
    // Multi-step Flow
    @Published var currentStep: CreationStep = .basicInfo
    @Published var navigationPath: [CreationStep] = []
    
    // Form Data
    @Published var basicInfo = QuizBasicInfo()
    @Published var questions: [QuestionBuilder] = []
    @Published var settings = QuizSettings()
    
    // Validation & State
    @Published var validationErrors: [String: [String]] = [:]
    @Published var isLoading = false
    @Published var canProceed = false
    @Published var showingError = false
    @Published var errorMessage: String?
    @Published var showingSuccess = false
    @Published var successMessage: String?
    
    // UI State
    @Published var showingPreview = false
    @Published var showingQuestionBuilder = false
    
    // MARK: - Dependencies
    private let quizRepository: QuizRepositoryProtocol
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Computed Properties
    var totalSteps: Int {
        return CreationStep.allCases.count
    }
    
    var currentStepIndex: Int {
        return CreationStep.allCases.firstIndex(of: currentStep) ?? 0
    }
    
    var canGoNext: Bool {
        switch currentStep {
        case .basicInfo:
            return basicInfo.isValid
        case .questions:
            return !questions.isEmpty && questions.allSatisfy(\.isValid)
        case .settings:
            return settings.isValid
        case .preview:
            return false // No next step after preview
        }
    }
    
    var canGoPrevious: Bool {
        return currentStepIndex > 0
    }

    var actualMaxScore: Double {
        return questions.reduce(0) { $0 + $1.score }
    }

    var isFormValid: Bool {
        return basicInfo.isValid &&
               !questions.isEmpty &&
               questions.allSatisfy(\.isValid) &&
               settings.isValid &&
               actualMaxScore > 0 &&
               settings.passingScore <= actualMaxScore
    }
    
    // MARK: - Initialization
    init(quizRepository: QuizRepositoryProtocol = QuizRepository()) {
        self.quizRepository = quizRepository
        setupValidation()
    }
    
    // MARK: - Private Methods
    private func setupValidation() {
        // Real-time validation using Combine
        Publishers.CombineLatest3(
            $basicInfo,
            $questions,
            $settings
        )
        .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
        .sink { [weak self] basicInfo, questions, settings in
            self?.validateForm(basicInfo: basicInfo, questions: questions, settings: settings)
        }
        .store(in: &cancellables)
    }
    
    private func validateForm(basicInfo: QuizBasicInfo, questions: [QuestionBuilder], settings: QuizSettings) {
        var errors: [String: [String]] = [:]
        
        // Validate basic info
        let basicInfoErrors = basicInfo.validationErrors
        if !basicInfoErrors.isEmpty {
            errors["basicInfo"] = basicInfoErrors
        }
        
        // Validate questions
        var questionErrors: [String] = []
        if questions.isEmpty {
            questionErrors.append("Phải có ít nhất một câu hỏi")
        }
        
        for (index, question) in questions.enumerated() {
            let questionValidationErrors = question.validate()
            if !questionValidationErrors.isEmpty {
                questionErrors.append("Câu hỏi \(index + 1): \(questionValidationErrors.map { $0.message }.joined(separator: ", "))")
            }
        }
        
        if !questionErrors.isEmpty {
            errors["questions"] = questionErrors
        }
        
        // Validate settings
        var settingsErrors = settings.validationErrors

        // Additional validation: passing score vs actual max score
        if actualMaxScore > 0 && settings.passingScore > actualMaxScore {
            settingsErrors.append("Điểm đạt (\(Int(settings.passingScore))) không được lớn hơn tổng điểm câu hỏi (\(Int(actualMaxScore)))")
        }

        if !settingsErrors.isEmpty {
            errors["settings"] = settingsErrors
        }

        validationErrors = errors
        canProceed = errors.isEmpty
    }

    // MARK: - Navigation Methods
    func goToNextStep() {
        guard canGoNext else { return }

        let currentIndex = currentStepIndex
        if currentIndex < CreationStep.allCases.count - 1 {
            currentStep = CreationStep.allCases[currentIndex + 1]
        }
    }

    func goToPreviousStep() {
        guard canGoPrevious else { return }

        let currentIndex = currentStepIndex
        if currentIndex > 0 {
            currentStep = CreationStep.allCases[currentIndex - 1]
        }
    }

    func goToStep(_ step: CreationStep) {
        currentStep = step
    }

    // MARK: - Question Management
    func addQuestion(_ questionBuilder: QuestionBuilder) {
        questions.append(questionBuilder)
        adjustPassingScoreIfNeeded()
    }

    func removeQuestion(at index: Int) {
        guard index >= 0 && index < questions.count else { return }
        questions.remove(at: index)
        adjustPassingScoreIfNeeded()
    }

    private func adjustPassingScoreIfNeeded() {
        // If passing score is higher than actual max score, adjust it to 50% of actual max score
        if settings.passingScore > actualMaxScore && actualMaxScore > 0 {
            settings.passingScore = actualMaxScore * 0.5
        }
    }

    func moveQuestion(from source: IndexSet, to destination: Int) {
        questions.move(fromOffsets: source, toOffset: destination)
    }

    func updateQuestion(at index: Int, with questionBuilder: QuestionBuilder) {
        guard index >= 0 && index < questions.count else { return }
        questions[index] = questionBuilder
    }

    // MARK: - API Methods
    func createQuiz() async -> Bool {
        guard isFormValid else {
            errorMessage = "Vui lòng kiểm tra lại thông tin đã nhập"
            showingError = true
            return false
        }

        isLoading = true
        errorMessage = nil
        showingError = false

        do {
            let request = buildCreateQuizRequest()
            let newQuiz = try await quizRepository.createQuiz(request)

            // Success - show success message
            isLoading = false
            successMessage = "Quiz '\(newQuiz.name)' đã được tạo thành công!"
            showingSuccess = true

            return true
        } catch {
            handleError(error)
            return false
        }
    }

    private func buildCreateQuizRequest() -> CreateQuizRequest {
        let questionRequests = questions.map { questionBuilder in
            questionBuilder.toCreateQuestionRequest()
        }

        return CreateQuizRequest(
            name: basicInfo.name.trimmingCharacters(in: .whitespacesAndNewlines),
            description: basicInfo.description.isEmpty ? nil : basicInfo.description.trimmingCharacters(in: .whitespacesAndNewlines),
            instruction: nil, // Can be added to UI later
            quizType: basicInfo.quizType,
            subjectId: basicInfo.subjectId,
            classId: basicInfo.classId,
            maxScore: actualMaxScore,
            passingScore: settings.passingScore,
            timeLimit: settings.timeLimit,
            maxAttempts: settings.maxAttempts,
            startDate: nil, // Can be added to UI later
            endDate: nil, // Can be added to UI later
            isRandomized: settings.isRandomized,
            showCorrectAnswers: settings.showCorrectAnswers,
            showResultImmediately: true, // Default to true
            questions: questionRequests
        )
    }

    private func handleError(_ error: Error) {
        isLoading = false

        if let appError = error as? AppError {
            errorMessage = appError.message
        } else {
            errorMessage = "Đã xảy ra lỗi không xác định. Vui lòng thử lại."
        }

        showingError = true
    }

    // MARK: - Form Management
    func resetForm() {
        currentStep = .basicInfo
        basicInfo = QuizBasicInfo()
        questions.removeAll()
        settings = QuizSettings()
        validationErrors.removeAll()
        isLoading = false
        canProceed = false
        showingError = false
        errorMessage = nil
        showingSuccess = false
        successMessage = nil
        showingPreview = false
        showingQuestionBuilder = false
    }

    func saveAsDraft() async -> Bool {
        // For now, just create the quiz as draft
        // In the future, this could be a separate API endpoint
        return await createQuiz()
    }

    // MARK: - Validation Helpers
    func getValidationErrors(for section: String) -> [String] {
        return validationErrors[section] ?? []
    }

    func hasValidationErrors(for section: String) -> Bool {
        return !getValidationErrors(for: section).isEmpty
    }

    func clearValidationErrors(for section: String) {
        validationErrors.removeValue(forKey: section)
    }

    // MARK: - Preview Methods
    func generatePreviewData() -> CreateQuizRequest {
        return buildCreateQuizRequest()
    }

    func getEstimatedDuration() -> String {
        let questionCount = questions.count
        let estimatedMinutes = questionCount * 2 // Estimate 2 minutes per question

        if let timeLimit = settings.timeLimit {
            return "\(timeLimit) phút (giới hạn)"
        } else {
            return "~\(estimatedMinutes) phút (ước tính)"
        }
    }

    func getTotalScore() -> Double {
        return questions.reduce(0) { total, question in
            total + question.score
        }
    }
}
