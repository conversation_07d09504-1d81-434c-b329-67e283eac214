//
//  LessonAssignmentView.swift
//  mobile-app-template
//
//  Created by Instructor App on 27/7/25.
//

import SwiftUI

struct LessonAssignmentView: View {
    @StateObject private var viewModel: LessonAssignmentViewModel
    @Environment(\.dismiss) private var dismiss
    
    let quiz: Quiz
    
    init(quiz: Quiz) {
        self.quiz = quiz
        self._viewModel = StateObject(wrappedValue: LessonAssignmentViewModel(quiz: quiz))
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Progress Header
                progressHeader
                
                // Content
                contentView
                
                // Bottom Actions
                bottomActions
            }
            .navigationTitle("Gán Quiz vào Bài học")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Hủy") {
                        dismiss()
                    }
                }
            }
            .alert("Lỗi", isPresented: $viewModel.showingError) {
                But<PERSON>("OK") { }
            } message: {
                Text(viewModel.errorMessage ?? "Đ<PERSON> xảy ra lỗi")
            }
            .alert("Thành công", isPresented: $viewModel.assignmentCompleted) {
                But<PERSON>("Đóng") {
                    dismiss()
                }
                But<PERSON>("Phát hành ngay") {
                    viewModel.publishQuizToLesson()
                }
            } message: {
                Text("Quiz đã được gán vào bài học thành công!")
            }
            .alert("Phát hành thành công", isPresented: $viewModel.publishCompleted) {
                Button("Đóng") {
                    dismiss()
                }
            } message: {
                Text("Quiz đã được phát hành và học sinh có thể làm bài!")
            }
        }
    }
    
    // MARK: - Progress Header
    private var progressHeader: some View {
        VStack(spacing: 16) {
            // Progress Bar
            ProgressView(value: viewModel.progressPercentage)
                .progressViewStyle(LinearProgressViewStyle(tint: AppConstants.Colors.primary))
                .scaleEffect(x: 1, y: 2, anchor: .center)
            
            // Step Info
            VStack(spacing: 4) {
                Text(viewModel.currentStep.title)
                    .font(AppConstants.Typography.headline)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Text(viewModel.currentStep.description)
                    .font(AppConstants.Typography.body)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding()
        .background(AppConstants.Colors.cardBackground)
        .overlay(
            Rectangle()
                .frame(height: 0.5)
                .foregroundColor(AppConstants.Colors.border),
            alignment: .bottom
        )
    }
    
    // MARK: - Content View
    private var contentView: some View {
        Group {
            switch viewModel.currentStep {
            case .lessonSelection:
                LessonSelectionStepView(viewModel: viewModel)
            case .configuration:
                AssignmentConfigurationStepView(viewModel: viewModel)
            case .preview:
                AssignmentPreviewStepView(viewModel: viewModel, quiz: quiz)
            }
        }
    }
    
    // MARK: - Bottom Actions
    private var bottomActions: some View {
        VStack(spacing: 12) {
            Divider()
            
            HStack(spacing: 16) {
                // Back Button
                if viewModel.currentStep != .lessonSelection {
                    Button("Quay lại") {
                        viewModel.goBackToPreviousStep()
                    }
                    .assignmentSecondaryButtonStyle()
                    .frame(maxWidth: .infinity)
                }
                
                // Next/Complete Button
                Button(nextButtonTitle) {
                    handleNextAction()
                }
                .assignmentPrimaryButtonStyle()
                .disabled(!canProceed)
                .frame(maxWidth: .infinity)
            }
            .padding(.horizontal)
            .padding(.bottom)
        }
        .background(AppConstants.Colors.cardBackground)
    }
    
    // MARK: - Computed Properties
    private var nextButtonTitle: String {
        switch viewModel.currentStep {
        case .lessonSelection:
            return "Tiếp tục"
        case .configuration:
            return "Xem trước"
        case .preview:
            return viewModel.isAssigning ? "Đang gán..." : "Gán Quiz"
        }
    }
    
    private var canProceed: Bool {
        switch viewModel.currentStep {
        case .lessonSelection:
            return viewModel.canProceedToConfiguration
        case .configuration:
            return viewModel.canProceedToPreview
        case .preview:
            return viewModel.canCompleteAssignment && !viewModel.isAssigning
        }
    }
    
    // MARK: - Actions
    private func handleNextAction() {
        switch viewModel.currentStep {
        case .lessonSelection:
            viewModel.proceedToConfiguration()
        case .configuration:
            viewModel.proceedToPreview()
        case .preview:
            viewModel.assignQuizToLesson()
        }
    }
}

// MARK: - Assignment Button Styles
extension View {
    func assignmentPrimaryButtonStyle() -> some View {
        self
            .font(AppConstants.Typography.subheadline)
            .fontWeight(.semibold)
            .foregroundColor(.white)
            .padding(.vertical, 12)
            .padding(.horizontal, 24)
            .background(AppConstants.Colors.primary)
            .cornerRadius(8)
    }

    func assignmentSecondaryButtonStyle() -> some View {
        self
            .font(AppConstants.Typography.subheadline)
            .fontWeight(.medium)
            .foregroundColor(AppConstants.Colors.primary)
            .padding(.vertical, 12)
            .padding(.horizontal, 24)
            .background(AppConstants.Colors.primary.opacity(0.1))
            .cornerRadius(8)
    }
}

// MARK: - Preview
struct LessonAssignmentView_Previews: PreviewProvider {
    static var previews: some View {
        LessonAssignmentView(quiz: Quiz(
            id: 1,
            name: "Kiểm tra Toán học",
            code: "QUIZ001",
            description: "Kiểm tra chương 1: Đại số",
            quizType: .quiz,
            subjectId: 1,
            subjectName: "Toán học",
            classId: 1,
            className: "Lớp 10A1",
            maxScore: 100,
            passingScore: 70,
            timeLimit: 60,
            maxAttempts: 2,
            isRandomized: true,
            showCorrectAnswers: false,
            state: .ready,
            questionCount: 10,
            studentCount: 30,
            attemptCount: 0,
            averageScore: 0,
            passRate: 0,
            pendingGradingCount: 0,
            startDate: nil,
            endDate: nil,
            createdAt: Date(),
            updatedAt: nil
        ))
    }
}
