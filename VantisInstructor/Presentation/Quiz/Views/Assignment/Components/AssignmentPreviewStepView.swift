//
//  AssignmentPreviewStepView.swift
//  mobile-app-template
//
//  Created by Instructor <PERSON>pp on 27/7/25.
//

import SwiftUI

struct AssignmentPreviewStepView: View {
    @ObservedObject var viewModel: LessonAssignmentViewModel
    let quiz: Quiz
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // Assignment Summary
                assignmentSummarySection
                
                // Quiz Information
                quizInformationSection
                
                // Lesson Information
                lessonInformationSection
                
                // Configuration Summary
                configurationSummarySection
                
                // Final Confirmation
                finalConfirmationSection
            }
            .padding()
        }
        .background(AppConstants.Colors.background)
    }
    
    // MARK: - Assignment Summary Section
    private var assignmentSummarySection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "checkmark.circle.fill")
                    .font(.title2)
                    .foregroundColor(AppConstants.Colors.success)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("Sẵn sàng gán Quiz")
                        .font(AppConstants.Typography.headline)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    Text("Xem lại thông tin trước khi hoàn tất")
                        .font(AppConstants.Typography.body)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
                
                Spacer()
            }
            
            Divider()
            
            // Quick Stats
            HStack(spacing: 20) {
                AssignmentStatItem(
                    icon: "questionmark.circle",
                    title: "Câu hỏi",
                    value: "\(quiz.questionCount)"
                )

                AssignmentStatItem(
                    icon: "clock",
                    title: "Thời gian",
                    value: timeDisplayText
                )

                AssignmentStatItem(
                    icon: "person.2",
                    title: "Học sinh",
                    value: "\(viewModel.selectedLesson?.totalStudents ?? 0)"
                )

                AssignmentStatItem(
                    icon: "repeat",
                    title: "Lần làm",
                    value: "\(viewModel.configuration.maxAttempts)"
                )
            }
        }
        .padding()
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(AppConstants.Colors.border, lineWidth: 0.5)
        )
    }
    
    // MARK: - Quiz Information Section
    private var quizInformationSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Thông tin Quiz")
                .font(AppConstants.Typography.headline)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            VStack(spacing: 12) {
                PreviewInfoRow(
                    icon: "doc.text",
                    title: "Tên Quiz",
                    value: quiz.name
                )

                PreviewInfoRow(
                    icon: "number",
                    title: "Mã Quiz",
                    value: quiz.code
                )

                if let description = quiz.description {
                    PreviewInfoRow(
                        icon: "text.alignleft",
                        title: "Mô tả",
                        value: description
                    )
                }

                PreviewInfoRow(
                    icon: "tag",
                    title: "Loại",
                    value: quiz.quizType.displayName
                )

                PreviewInfoRow(
                    icon: "chart.bar",
                    title: "Điểm số",
                    value: "\(Int(quiz.passingScore))/\(Int(quiz.maxScore)) điểm để đạt"
                )
            }
        }
        .padding()
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(AppConstants.Colors.border, lineWidth: 0.5)
        )
    }
    
    // MARK: - Lesson Information Section
    private var lessonInformationSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Thông tin Bài học")
                .font(AppConstants.Typography.headline)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            if let lesson = viewModel.selectedLesson {
                VStack(spacing: 12) {
                    PreviewInfoRow(
                        icon: "book",
                        title: "Tên bài học",
                        value: lesson.title
                    )

                    PreviewInfoRow(
                        icon: "graduationcap",
                        title: "Môn học",
                        value: "\(lesson.courseCode) - \(lesson.courseName)"
                    )

                    PreviewInfoRow(
                        icon: "calendar",
                        title: "Thời gian",
                        value: viewModel.formatDate(lesson.scheduledDate)
                    )

                    PreviewInfoRow(
                        icon: "person.2",
                        title: "Số học sinh",
                        value: "\(lesson.totalStudents) học sinh"
                    )

                    PreviewInfoRow(
                        icon: "tag",
                        title: "Loại bài học",
                        value: lesson.type.displayName
                    )
                }
            }
        }
        .padding()
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(AppConstants.Colors.border, lineWidth: 0.5)
        )
    }
    
    // MARK: - Configuration Summary Section
    private var configurationSummarySection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Cấu hình gán Quiz")
                .font(AppConstants.Typography.headline)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            VStack(spacing: 12) {
                if let startDate = viewModel.configuration.startDate {
                    PreviewInfoRow(
                        icon: "play.circle",
                        title: "Thời gian bắt đầu",
                        value: viewModel.formatDate(startDate)
                    )
                }

                if let endDate = viewModel.configuration.endDate {
                    PreviewInfoRow(
                        icon: "stop.circle",
                        title: "Thời gian kết thúc",
                        value: viewModel.formatDate(endDate)
                    )
                }

                PreviewInfoRow(
                    icon: "repeat",
                    title: "Số lần làm bài",
                    value: "\(viewModel.configuration.maxAttempts) lần"
                )

                if let timeLimit = viewModel.configuration.timeLimit {
                    PreviewInfoRow(
                        icon: "timer",
                        title: "Giới hạn thời gian",
                        value: "\(timeLimit) phút"
                    )
                } else {
                    PreviewInfoRow(
                        icon: "timer",
                        title: "Giới hạn thời gian",
                        value: "Không giới hạn"
                    )
                }

                PreviewInfoRow(
                    icon: viewModel.configuration.allowLateSubmission ? "checkmark.circle" : "xmark.circle",
                    title: "Cho phép nộp muộn",
                    value: viewModel.configuration.allowLateSubmission ? "Có" : "Không"
                )

                PreviewInfoRow(
                    icon: viewModel.configuration.showCorrectAnswers ? "eye" : "eye.slash",
                    title: "Hiển thị đáp án",
                    value: viewModel.configuration.showCorrectAnswers ? "Có" : "Không"
                )

                if !viewModel.configuration.instructions.isEmpty {
                    PreviewInfoRow(
                        icon: "text.bubble",
                        title: "Hướng dẫn",
                        value: viewModel.configuration.instructions
                    )
                }
            }
        }
        .padding()
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(AppConstants.Colors.border, lineWidth: 0.5)
        )
    }
    
    // MARK: - Final Confirmation Section
    private var finalConfirmationSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "info.circle")
                    .foregroundColor(AppConstants.Colors.primary)
                
                Text("Lưu ý")
                    .font(AppConstants.Typography.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(AppConstants.Colors.primary)
            }
            
            VStack(alignment: .leading, spacing: 8) {
                Text("• Quiz sẽ được gán vào bài học và học sinh có thể thấy trong danh sách bài tập")
                Text("• Bạn có thể phát hành ngay hoặc phát hành sau")
                Text("• Sau khi gán, bạn vẫn có thể chỉnh sửa cấu hình")
            }
            .font(AppConstants.Typography.body)
            .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .padding()
        .background(AppConstants.Colors.primary.opacity(0.1))
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(AppConstants.Colors.primary.opacity(0.3), lineWidth: 1)
        )
    }
    
    // MARK: - Computed Properties
    private var timeDisplayText: String {
        if let timeLimit = viewModel.configuration.timeLimit {
            return "\(timeLimit)m"
        } else {
            return "Không giới hạn"
        }
    }
}

// MARK: - Supporting Views
struct AssignmentStatItem: View {
    let icon: String
    let title: String
    let value: String
    
    var body: some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(AppConstants.Colors.primary)
            
            Text(value)
                .font(AppConstants.Typography.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Text(title)
                .font(AppConstants.Typography.caption)
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity)
    }
}

struct PreviewInfoRow: View {
    let icon: String
    let title: String
    let value: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Image(systemName: icon)
                .font(.body)
                .foregroundColor(AppConstants.Colors.textSecondary)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(AppConstants.Typography.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                
                Text(value)
                    .font(AppConstants.Typography.body)
                    .foregroundColor(AppConstants.Colors.textPrimary)
            }
            
            Spacer()
        }
    }
}

// MARK: - Preview
struct AssignmentPreviewStepView_Previews: PreviewProvider {
    static var previews: some View {
        AssignmentPreviewStepView(
            viewModel: LessonAssignmentViewModel(quiz: Quiz(
                id: 1,
                name: "Test Quiz",
                code: "QUIZ001",
                description: "Test Description",
                quizType: .quiz,
                subjectId: 1,
                subjectName: "Toán học",
                classId: 1,
                className: "Lớp 10A1",
                maxScore: 100,
                passingScore: 70,
                timeLimit: 60,
                maxAttempts: 2,
                isRandomized: true,
                showCorrectAnswers: false,
                state: .ready,
                questionCount: 10,
                studentCount: 30,
                attemptCount: 0,
                averageScore: 0,
                passRate: 0,
                pendingGradingCount: 0,
                startDate: nil,
                endDate: nil,
                createdAt: Date(),
                updatedAt: nil
            )),
            quiz: Quiz(
                id: 1,
                name: "Test Quiz",
                code: "QUIZ001",
                description: "Test Description",
                quizType: .quiz,
                subjectId: 1,
                subjectName: "Toán học",
                classId: 1,
                className: "Lớp 10A1",
                maxScore: 100,
                passingScore: 70,
                timeLimit: 60,
                maxAttempts: 2,
                isRandomized: true,
                showCorrectAnswers: false,
                state: .ready,
                questionCount: 10,
                studentCount: 30,
                attemptCount: 0,
                averageScore: 0,
                passRate: 0,
                pendingGradingCount: 0,
                startDate: nil,
                endDate: nil,
                createdAt: Date(),
                updatedAt: nil
            )
        )
    }
}
