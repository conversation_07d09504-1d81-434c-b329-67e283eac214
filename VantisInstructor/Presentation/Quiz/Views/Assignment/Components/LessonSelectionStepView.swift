//
//  LessonSelectionStepView.swift
//  mobile-app-template
//
//  Created by Instructor App on 27/7/25.
//

import SwiftUI

struct LessonSelectionStepView: View {
    @ObservedObject var viewModel: LessonAssignmentViewModel
    
    var body: some View {
        VStack(spacing: 0) {
            // Search and Filters
            searchAndFiltersSection
            
            // Lessons List
            lessonsListSection
        }
    }
    
    // MARK: - Search and Filters Section
    private var searchAndFiltersSection: some View {
        VStack(spacing: 16) {
            // Search Bar
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(AppConstants.Colors.textSecondary)
                
                TextField("Tìm kiếm bài học...", text: $viewModel.searchText)
                    .textFieldStyle(PlainTextFieldStyle())
                
                if !viewModel.searchText.isEmpty {
                    Button(action: {
                        viewModel.searchText = ""
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 10)
            .background(AppConstants.Colors.background)
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(AppConstants.Colors.border, lineWidth: 1)
            )
            
            // Filters
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    // Course Filter
                    LessonFilterChip(
                        title: "Tất cả môn học",
                        isSelected: viewModel.selectedCourseFilter == nil,
                        action: {
                            viewModel.selectedCourseFilter = nil
                        }
                    )

                    // Mock course filters
                    ForEach(uniqueCourses, id: \.id) { course in
                        LessonFilterChip(
                            title: course.name,
                            isSelected: viewModel.selectedCourseFilter == course.id,
                            action: {
                                viewModel.selectedCourseFilter = course.id
                            }
                        )
                    }

                    Divider()
                        .frame(height: 20)

                    // Status Filter
                    LessonFilterChip(
                        title: "Tất cả trạng thái",
                        isSelected: viewModel.selectedStatusFilter == nil,
                        action: {
                            viewModel.selectedStatusFilter = nil
                        }
                    )

                    ForEach(LessonStatus.allCases, id: \.self) { status in
                        LessonFilterChip(
                            title: status.displayName,
                            isSelected: viewModel.selectedStatusFilter == status,
                            action: {
                                viewModel.selectedStatusFilter = status
                            }
                        )
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding()
        .background(AppConstants.Colors.cardBackground)
        .overlay(
            Rectangle()
                .frame(height: 0.5)
                .foregroundColor(AppConstants.Colors.border),
            alignment: .bottom
        )
    }
    
    // MARK: - Lessons List Section
    private var lessonsListSection: some View {
        Group {
            if viewModel.isLoadingLessons {
                loadingView
            } else if viewModel.filteredLessons.isEmpty {
                emptyStateView
            } else {
                lessonsListView
            }
        }
    }
    
    // MARK: - Loading View
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text("Đang tải danh sách bài học...")
                .font(AppConstants.Typography.body)
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(AppConstants.Colors.background)
    }
    
    // MARK: - Empty State View
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "book.closed")
                .font(.system(size: 60))
                .foregroundColor(AppConstants.Colors.textSecondary)
            
            VStack(spacing: 8) {
                Text("Không tìm thấy bài học")
                    .font(AppConstants.Typography.headline)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Text("Thử thay đổi bộ lọc hoặc tạo bài học mới")
                    .font(AppConstants.Typography.body)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .multilineTextAlignment(.center)
            }
            
            Button("Tải lại") {
                viewModel.loadAvailableLessons()
            }
            .assignmentPrimaryButtonStyle()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(AppConstants.Colors.background)
    }
    
    // MARK: - Lessons List View
    private var lessonsListView: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(viewModel.filteredLessons) { lesson in
                    LessonSelectionCard(
                        lesson: lesson,
                        isSelected: viewModel.selectedLesson?.id == lesson.id,
                        onSelect: {
                            viewModel.selectLesson(lesson)
                        }
                    )
                }
            }
            .padding()
        }
        .background(AppConstants.Colors.background)
    }
    
    // MARK: - Computed Properties
    private var uniqueCourses: [(id: Int, name: String)] {
        let courses = viewModel.availableLessons.map { (id: $0.courseId, name: $0.courseName) }
        let uniqueCoursesDict = Dictionary(courses, uniquingKeysWith: { first, _ in first })
        return Array(uniqueCoursesDict.map { (id: $0.key, name: $0.value) })
            .sorted { $0.name < $1.name }
    }
}

// MARK: - Lesson Filter Chip
struct LessonFilterChip: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(AppConstants.Typography.caption)
                .fontWeight(.medium)
                .foregroundColor(isSelected ? .white : AppConstants.Colors.textSecondary)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(isSelected ? AppConstants.Colors.primary : AppConstants.Colors.background)
                .cornerRadius(16)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(AppConstants.Colors.border, lineWidth: isSelected ? 0 : 1)
                )
        }
    }
}

// MARK: - Lesson Selection Card
struct LessonSelectionCard: View {
    let lesson: Lesson
    let isSelected: Bool
    let onSelect: () -> Void
    
    private var dateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "vi_VN")
        return formatter
    }
    
    var body: some View {
        Button(action: onSelect) {
            VStack(alignment: .leading, spacing: 12) {
                // Header
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(lesson.title)
                            .font(AppConstants.Typography.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                            .multilineTextAlignment(.leading)
                        
                        Text("\(lesson.courseCode) - \(lesson.courseName)")
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    
                    Spacer()
                    
                    // Selection Indicator
                    Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                        .font(.title2)
                        .foregroundColor(isSelected ? AppConstants.Colors.primary : AppConstants.Colors.border)
                }
                
                // Description
                if let description = lesson.description {
                    Text(description)
                        .font(AppConstants.Typography.body)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .lineLimit(2)
                }
                
                // Info Row
                HStack(spacing: 16) {
                    // Type
                    HStack(spacing: 4) {
                        Image(systemName: lesson.type.icon)
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                        
                        Text(lesson.type.displayName)
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    
                    // Status
                    AssignmentLessonStatusBadge(status: lesson.status)
                    
                    Spacer()
                    
                    // Students
                    HStack(spacing: 4) {
                        Image(systemName: "person.2")
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                        
                        Text("\(lesson.totalStudents)")
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                }
                
                // Schedule Info
                HStack {
                    Image(systemName: "calendar")
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                    
                    Text(dateFormatter.string(from: lesson.scheduledDate))
                        .font(AppConstants.Typography.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                    
                    if let duration = lesson.duration {
                        Text("• \(duration) phút")
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                }
            }
            .padding()
            .background(AppConstants.Colors.cardBackground)
            .cornerRadius(AppConstants.UI.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                    .stroke(isSelected ? AppConstants.Colors.primary : AppConstants.Colors.border, 
                           lineWidth: isSelected ? 2 : 0.5)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Status Badge
struct AssignmentLessonStatusBadge: View {
    let status: LessonStatus

    var body: some View {
        Text(status.displayName)
            .font(AppConstants.Typography.caption)
            .fontWeight(.medium)
            .foregroundColor(.white)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(Color(status.color))
            .cornerRadius(8)
    }
}

// MARK: - Preview
struct LessonSelectionStepView_Previews: PreviewProvider {
    static var previews: some View {
        LessonSelectionStepView(viewModel: LessonAssignmentViewModel(quiz: Quiz(
            id: 1,
            name: "Test Quiz",
            code: "QUIZ001",
            description: nil,
            quizType: .quiz,
            subjectId: 1,
            subjectName: "Toán học",
            classId: 1,
            className: "Lớp 10A1",
            maxScore: 100,
            passingScore: 70,
            timeLimit: 60,
            maxAttempts: 2,
            isRandomized: true,
            showCorrectAnswers: false,
            state: .ready,
            questionCount: 10,
            studentCount: 30,
            attemptCount: 0,
            averageScore: 0,
            passRate: 0,
            pendingGradingCount: 0,
            startDate: nil,
            endDate: nil,
            createdAt: Date(),
            updatedAt: nil
        )))
    }
}
