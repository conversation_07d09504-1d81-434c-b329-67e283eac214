//
//  AssignmentConfigurationStepView.swift
//  mobile-app-template
//
//  Created by Instructor <PERSON>pp on 27/7/25.
//

import SwiftUI

struct AssignmentConfigurationStepView: View {
    @ObservedObject var viewModel: LessonAssignmentViewModel
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // Selected Lesson Summary
                selectedLessonSummary
                
                // Time Configuration
                timeConfigurationSection
                
                // Quiz Settings
                quizSettingsSection
                
                // Instructions
                instructionsSection
                
                // Validation Errors
                if !validationErrors.isEmpty {
                    validationErrorsSection
                }
            }
            .padding()
        }
        .background(AppConstants.Colors.background)
    }
    
    // MARK: - Selected Lesson Summary
    private var selectedLessonSummary: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Bài học đã chọn")
                .font(AppConstants.Typography.headline)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            if let lesson = viewModel.selectedLesson {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(lesson.title)
                            .font(AppConstants.Typography.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        
                        Text("\(lesson.courseCode) - \(lesson.courseName)")
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                        
                        Text(viewModel.formatDate(lesson.scheduledDate))
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    
                    Spacer()
                    
                    Button("Thay đổi") {
                        viewModel.goBackToPreviousStep()
                    }
                    .font(AppConstants.Typography.caption)
                    .foregroundColor(AppConstants.Colors.primary)
                }
            }
        }
        .padding()
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(AppConstants.Colors.border, lineWidth: 0.5)
        )
    }
    
    // MARK: - Time Configuration Section
    private var timeConfigurationSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Thời gian")
                .font(AppConstants.Typography.headline)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            VStack(spacing: 16) {
                // Start Date
                DatePickerField(
                    title: "Thời gian bắt đầu",
                    date: Binding(
                        get: { viewModel.configuration.startDate ?? Date() },
                        set: { viewModel.configuration.startDate = $0 }
                    ),
                    isOptional: true,
                    hasValue: viewModel.configuration.startDate != nil,
                    onToggle: { enabled in
                        if enabled {
                            viewModel.configuration.startDate = Date()
                        } else {
                            viewModel.configuration.startDate = nil
                        }
                    }
                )
                
                // End Date
                DatePickerField(
                    title: "Thời gian kết thúc",
                    date: Binding(
                        get: { viewModel.configuration.endDate ?? Date().addingTimeInterval(3600) },
                        set: { viewModel.configuration.endDate = $0 }
                    ),
                    isOptional: true,
                    hasValue: viewModel.configuration.endDate != nil,
                    onToggle: { enabled in
                        if enabled {
                            viewModel.configuration.endDate = Date().addingTimeInterval(3600)
                        } else {
                            viewModel.configuration.endDate = nil
                        }
                    }
                )
            }
        }
        .padding()
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(AppConstants.Colors.border, lineWidth: 0.5)
        )
    }
    
    // MARK: - Quiz Settings Section
    private var quizSettingsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Cài đặt Quiz")
                .font(AppConstants.Typography.headline)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            VStack(spacing: 16) {
                // Max Attempts
                HStack {
                    Text("Số lần làm bài tối đa")
                        .font(AppConstants.Typography.body)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    Spacer()
                    
                    Stepper(
                        value: $viewModel.configuration.maxAttempts,
                        in: 1...10
                    ) {
                        Text("\(viewModel.configuration.maxAttempts)")
                            .font(AppConstants.Typography.body)
                            .fontWeight(.medium)
                            .foregroundColor(AppConstants.Colors.primary)
                    }
                }
                
                Divider()
                
                // Time Limit
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("Giới hạn thời gian")
                            .font(AppConstants.Typography.body)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        
                        Spacer()
                        
                        Toggle("", isOn: Binding(
                            get: { viewModel.configuration.timeLimit != nil },
                            set: { enabled in
                                if enabled {
                                    viewModel.configuration.timeLimit = 60
                                } else {
                                    viewModel.configuration.timeLimit = nil
                                }
                            }
                        ))
                    }
                    
                    if viewModel.configuration.timeLimit != nil {
                        HStack {
                            Text("Thời gian (phút)")
                                .font(AppConstants.Typography.caption)
                                .foregroundColor(AppConstants.Colors.textSecondary)
                            
                            Spacer()
                            
                            Stepper(
                                value: Binding(
                                    get: { viewModel.configuration.timeLimit ?? 60 },
                                    set: { viewModel.configuration.timeLimit = $0 }
                                ),
                                in: 5...300,
                                step: 5
                            ) {
                                Text("\(viewModel.configuration.timeLimit ?? 60) phút")
                                    .font(AppConstants.Typography.caption)
                                    .foregroundColor(AppConstants.Colors.primary)
                            }
                        }
                        .padding(.leading, 16)
                    }
                }
                
                Divider()
                
                // Allow Late Submission
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Cho phép nộp muộn")
                            .font(AppConstants.Typography.body)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        
                        Text("Học sinh có thể làm bài sau thời hạn")
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    
                    Spacer()
                    
                    Toggle("", isOn: $viewModel.configuration.allowLateSubmission)
                }
                
                Divider()
                
                // Show Correct Answers
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Hiển thị đáp án")
                            .font(AppConstants.Typography.body)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        
                        Text("Hiển thị đáp án đúng sau khi hoàn thành")
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    
                    Spacer()
                    
                    Toggle("", isOn: $viewModel.configuration.showCorrectAnswers)
                }
            }
        }
        .padding()
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(AppConstants.Colors.border, lineWidth: 0.5)
        )
    }
    
    // MARK: - Instructions Section
    private var instructionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Hướng dẫn cho học sinh")
                .font(AppConstants.Typography.headline)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            TextField("Nhập hướng dẫn (tùy chọn)...", text: $viewModel.configuration.instructions, axis: .vertical)
                .textFieldStyle(PlainTextFieldStyle())
                .padding()
                .background(AppConstants.Colors.background)
                .cornerRadius(8)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(AppConstants.Colors.border, lineWidth: 1)
                )
                .lineLimit(3...6)
        }
        .padding()
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(AppConstants.Colors.border, lineWidth: 0.5)
        )
    }
    
    // MARK: - Validation Errors Section
    private var validationErrorsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "exclamationmark.triangle.fill")
                    .foregroundColor(AppConstants.Colors.error)
                
                Text("Cần kiểm tra lại")
                    .font(AppConstants.Typography.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(AppConstants.Colors.error)
            }
            
            VStack(alignment: .leading, spacing: 8) {
                ForEach(validationErrors, id: \.self) { error in
                    HStack(alignment: .top, spacing: 8) {
                        Circle()
                            .fill(AppConstants.Colors.error)
                            .frame(width: 4, height: 4)
                            .padding(.top, 6)
                        
                        Text(error)
                            .font(AppConstants.Typography.body)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                    }
                }
            }
        }
        .padding()
        .background(AppConstants.Colors.error.opacity(0.1))
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(AppConstants.Colors.error.opacity(0.3), lineWidth: 1)
        )
    }
    
    // MARK: - Computed Properties
    private var validationErrors: [String] {
        viewModel.validateConfiguration()
    }
}

// MARK: - Date Picker Field
struct DatePickerField: View {
    let title: String
    @Binding var date: Date
    let isOptional: Bool
    let hasValue: Bool
    let onToggle: ((Bool) -> Void)?
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(title)
                    .font(AppConstants.Typography.body)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Spacer()
                
                if isOptional {
                    Toggle("", isOn: Binding(
                        get: { hasValue },
                        set: { onToggle?($0) }
                    ))
                }
            }
            
            if hasValue {
                DatePicker(
                    "",
                    selection: $date,
                    displayedComponents: [.date, .hourAndMinute]
                )
                .datePickerStyle(CompactDatePickerStyle())
                .labelsHidden()
            }
        }
    }
}

// MARK: - Preview
struct AssignmentConfigurationStepView_Previews: PreviewProvider {
    static var previews: some View {
        AssignmentConfigurationStepView(viewModel: LessonAssignmentViewModel(quiz: Quiz(
            id: 1,
            name: "Test Quiz",
            code: "QUIZ001",
            description: nil,
            quizType: .quiz,
            subjectId: 1,
            subjectName: "Toán học",
            classId: 1,
            className: "Lớp 10A1",
            maxScore: 100,
            passingScore: 70,
            timeLimit: 60,
            maxAttempts: 2,
            isRandomized: true,
            showCorrectAnswers: false,
            state: .ready,
            questionCount: 10,
            studentCount: 30,
            attemptCount: 0,
            averageScore: 0,
            passRate: 0,
            pendingGradingCount: 0,
            startDate: nil,
            endDate: nil,
            createdAt: Date(),
            updatedAt: nil
        )))
    }
}
