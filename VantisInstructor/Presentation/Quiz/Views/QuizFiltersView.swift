//
//  QuizFiltersView.swift
//  mobile-app-template
//
//  Created by Instructor <PERSON>pp on 24/7/25.
//

import SwiftUI

struct QuizFiltersView: View {
    @ObservedObject var viewModel: QuizManagementViewModel
    @Environment(\.dismiss) private var dismiss
    
    @State private var tempQuizType: QuizType?
    @State private var tempState: QuizState?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                VStack(alignment: .leading, spacing: 8) {
                    Text("Lọc danh sách quiz")
                        .font(AppConstants.Typography.title2)
                        .fontWeight(.bold)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    Text("Chọn các tiêu chí để lọc danh sách quiz")
                        .font(AppConstants.Typography.body)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(AppConstants.UI.screenPadding)
                .background(AppConstants.Colors.background)
                
                ScrollView {
                    VStack(spacing: AppConstants.UI.sectionSpacing) {
                        // Quiz Type Filter
                        FilterSectionView(title: "Loại quiz") {
                            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                                ForEach(QuizType.allCases, id: \.self) { type in
                                    FilterOptionCard(
                                        title: type.displayName,
                                        icon: type.icon,
                                        isSelected: tempQuizType == type,
                                        color: colorForQuizType(type)
                                    ) {
                                        if tempQuizType == type {
                                            tempQuizType = nil
                                        } else {
                                            tempQuizType = type
                                        }
                                    }
                                }
                            }
                        }
                        
                        // Quiz State Filter
                        FilterSectionView(title: "Trạng thái") {
                            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                                ForEach(QuizState.allCases, id: \.self) { state in
                                    FilterOptionCard(
                                        title: state.displayName,
                                        icon: iconForState(state),
                                        isSelected: tempState == state,
                                        color: colorForState(state)
                                    ) {
                                        if tempState == state {
                                            tempState = nil
                                        } else {
                                            tempState = state
                                        }
                                    }
                                }
                            }
                        }
                        
                        Spacer(minLength: 100) // Space for bottom buttons
                    }
                    .padding(AppConstants.UI.screenPadding)
                }
                
                // Bottom buttons
                VStack(spacing: 12) {
                    Divider()
                    
                    HStack(spacing: 12) {
                        // Clear filters button
                        Button {
                            tempQuizType = nil
                            tempState = nil
                        } label: {
                            Text("Xóa bộ lọc")
                                .font(AppConstants.Typography.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 12)
                                .background(AppConstants.Colors.surface)
                                .cornerRadius(AppConstants.UI.cornerRadius)
                        }
                        
                        // Apply filters button
                        Button {
                            applyFilters()
                        } label: {
                            Text("Áp dụng")
                                .font(AppConstants.Typography.subheadline)
                                .fontWeight(.semibold)
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 12)
                                .background(AppConstants.Colors.primary)
                                .cornerRadius(AppConstants.UI.cornerRadius)
                        }
                    }
                    .padding(.horizontal, AppConstants.UI.screenPadding)
                    .padding(.bottom, AppConstants.UI.screenPadding)
                }
                .background(AppConstants.Colors.background)
            }
            .background(AppConstants.Colors.background)
            .navigationBarHidden(true)
        }
        .onAppear {
            tempQuizType = viewModel.selectedQuizType
            tempState = viewModel.selectedState
        }
    }
    
    private func applyFilters() {
        viewModel.selectedQuizType = tempQuizType
        viewModel.selectedState = tempState
        
        Task {
            await viewModel.applyFilters()
        }
        
        dismiss()
    }
    
    private func colorForQuizType(_ type: QuizType) -> Color {
        switch type {
        case .quiz: return AppConstants.Colors.primary
        case .assignment: return AppConstants.Colors.success
        case .midterm: return AppConstants.Colors.warning
        case .final: return AppConstants.Colors.error
        case .project: return AppConstants.Colors.accent
        }
    }
    
    private func colorForState(_ state: QuizState) -> Color {
        switch state {
        case .draft: return AppConstants.Colors.textSecondary
        case .ready: return AppConstants.Colors.primary
        case .published: return AppConstants.Colors.success
        case .archived: return AppConstants.Colors.warning
        }
    }
    
    private func iconForState(_ state: QuizState) -> String {
        switch state {
        case .draft: return "doc.text"
        case .ready: return "checkmark.circle"
        case .published: return "checkmark.circle.fill"
        case .archived: return "archivebox"
        }
    }
}

// MARK: - Filter Section View
struct FilterSectionView<Content: View>: View {
    let title: String
    let content: Content
    
    init(title: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.content = content()
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(title)
                .font(AppConstants.Typography.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            content
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
}

// MARK: - Filter Option Card
struct FilterOptionCard: View {
    let title: String
    let icon: String
    let isSelected: Bool
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(isSelected ? .white : color)
                
                Text(title)
                    .font(AppConstants.Typography.caption)
                    .fontWeight(.medium)
                    .foregroundColor(isSelected ? .white : AppConstants.Colors.textPrimary)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .padding(.horizontal, 8)
            .background(isSelected ? color : AppConstants.Colors.cardBackground)
            .cornerRadius(AppConstants.UI.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                    .stroke(isSelected ? color : AppConstants.Colors.border, lineWidth: isSelected ? 2 : 1)
            )
            .shadow(
                color: isSelected ? color.opacity(0.3) : AppConstants.Colors.shadow.opacity(0.1),
                radius: isSelected ? 4 : 2,
                x: 0,
                y: isSelected ? 2 : 1
            )
        }
        .buttonStyle(PlainButtonStyle())
        .animation(.easeInOut(duration: 0.2), value: isSelected)
    }
}

// MARK: - Preview
struct QuizFiltersView_Previews: PreviewProvider {
    static var previews: some View {
        QuizFiltersView(viewModel: QuizManagementViewModel())
    }
}
