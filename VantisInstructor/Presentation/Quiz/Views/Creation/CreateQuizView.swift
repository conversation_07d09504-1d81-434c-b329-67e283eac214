//
//  CreateQuizView.swift
//  mobile-app-template
//
//  Created by Instructor App on 27/7/25.
//

import SwiftUI

struct CreateQuizView: View {
    @StateObject private var viewModel = QuizCreationViewModel()
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        VStack(spacing: 0) {
            // Custom Header
            headerSection

            // Progress Header
            progressHeader

            // Content
            ScrollView {
                VStack(spacing: AppConstants.UI.sectionSpacing) {
                    currentStepView
                }
                .padding(.horizontal, AppConstants.UI.screenPadding)
                .padding(.bottom, 100) // Space for navigation buttons
            }

            // Navigation Buttons
            navigationButtons
        }
        .background(AppConstants.Colors.background)
        .navigationBarHidden(true)
        .preferredColorScheme(.light)
        .alert("Lỗi", isPresented: $viewModel.showingError) {
            Button("OK") {
                viewModel.showingError = false
            }
        } message: {
            Text(viewModel.errorMessage ?? "Đ<PERSON> x<PERSON>y ra lỗi không xác đ<PERSON>nh")
        }
        .overlay {
            // Loading overlay
            if viewModel.isLoading {
                LoadingOverlay(message: "Đang tạo quiz...")
                    .zIndex(999)
            }

            // Success modal
            if viewModel.showingSuccess {
                SuccessModal(
                    title: "Thành công!",
                    message: viewModel.successMessage ?? "Quiz đã được tạo thành công!",
                    buttonTitle: "Hoàn tất"
                ) {
                    viewModel.showingSuccess = false
                    viewModel.resetForm()
                    dismiss()
                }
                .zIndex(1000)
            }
        }
    }
    
    // MARK: - Progress Header
    private var progressHeader: some View {
        VStack(spacing: 12) {
            // Progress Bar
            HStack {
                ForEach(CreationStep.allCases, id: \.self) { step in
                    Circle()
                        .fill(stepColor(for: step))
                        .frame(width: 12, height: 12)
                    
                    if step != CreationStep.allCases.last {
                        Rectangle()
                            .fill(stepColor(for: step))
                            .frame(height: 2)
                    }
                }
            }
            .padding(.horizontal, AppConstants.UI.screenPadding)
            
            // Step Info
            VStack(spacing: 4) {
                Text("Bước \(viewModel.currentStep.stepNumber) / \(viewModel.totalSteps)")
                    .font(AppConstants.Typography.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                
                Text(viewModel.currentStep.title)
                    .font(AppConstants.Typography.headline)
                    .foregroundColor(AppConstants.Colors.textPrimary)
            }
        }
        .padding(.vertical, 16)
        .background(Color.white)
        .overlay(
            Rectangle()
                .fill(AppConstants.Colors.border)
                .frame(height: 0.5),
            alignment: .bottom
        )
    }
    
    private func stepColor(for step: CreationStep) -> Color {
        let currentIndex = viewModel.currentStepIndex
        let stepIndex = CreationStep.allCases.firstIndex(of: step) ?? 0
        
        if stepIndex < currentIndex {
            return AppConstants.Colors.primary // Completed
        } else if stepIndex == currentIndex {
            return AppConstants.Colors.primary // Current
        } else {
            return AppConstants.Colors.border // Future
        }
    }
    
    // MARK: - Current Step View
    @ViewBuilder
    private var currentStepView: some View {
        switch viewModel.currentStep {
        case .basicInfo:
            BasicInfoStepView(viewModel: viewModel)
        case .questions:
            QuestionsStepView(viewModel: viewModel)
        case .settings:
            SettingsStepView(viewModel: viewModel)
        case .preview:
            PreviewStepView(viewModel: viewModel)
        }
    }
    
    // MARK: - Navigation Buttons
    private var navigationButtons: some View {
        VStack(spacing: 0) {
            Rectangle()
                .fill(AppConstants.Colors.border)
                .frame(height: 0.5)
            
            HStack(spacing: 16) {
                // Previous Button
                if viewModel.canGoPrevious {
                    Button("Quay lại") {
                        viewModel.goToPreviousStep()
                    }
                    .secondaryButtonStyle()
                    .frame(maxWidth: .infinity)
                }
                
                // Next/Create Button
                Button(nextButtonTitle) {
                    if viewModel.currentStep == .preview {
                        Task {
                            await viewModel.createQuiz()
                            // Success alert will handle dismiss
                        }
                    } else {
                        viewModel.goToNextStep()
                    }
                }
                .primaryButtonStyle()
                .frame(maxWidth: .infinity)
                .disabled(!viewModel.canGoNext && viewModel.currentStep != .preview)
                .opacity((!viewModel.canGoNext && viewModel.currentStep != .preview) ? 0.6 : 1.0)
            }
            .padding(.horizontal, AppConstants.UI.screenPadding)
            .padding(.vertical, 16)
            .background(AppConstants.Colors.cardBackground)
        }
    }
    
    private var nextButtonTitle: String {
        switch viewModel.currentStep {
        case .basicInfo, .questions, .settings:
            return "Tiếp theo"
        case .preview:
            return viewModel.isLoading ? "Đang tạo..." : "Tạo Quiz"
        }
    }

    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            // Back button
            Button(action: {
                dismiss()
            }) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color.gray.opacity(0.8))
                    .frame(width: 36, height: 36)
                    .background(
                        Circle()
                            .fill(Color.white)
                            .overlay(
                                Circle()
                                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                            )
                            .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
                    )
            }
            .buttonStyle(PlainButtonStyle())

            Spacer()

            VStack(alignment: .center, spacing: 4) {
                Text("Tạo Quiz")
                    .font(.beVietnamPro(.bold, size: 24))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Text("Tạo quiz mới cho học sinh")
                    .font(.beVietnamPro(.medium, size: 14))
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }

            Spacer()

            // Save/Cancel buttons
            HStack(spacing: 8) {
                if viewModel.currentStep == .preview {
                    Button("Lưu nháp") {
                        Task {
                            if await viewModel.saveAsDraft() {
                                dismiss()
                            }
                        }
                    }
                    .font(.beVietnamPro(.medium, size: 14))
                    .foregroundColor(AppConstants.Colors.primary)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(AppConstants.Colors.primary.opacity(0.1))
                    )
                }

                Button("Hủy") {
                    dismiss()
                }
                .font(.beVietnamPro(.medium, size: 14))
                .foregroundColor(AppConstants.Colors.textSecondary)
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(Color.white)
        .shadow(color: Color.black.opacity(0.05), radius: 1, x: 0, y: 1)
    }
}

// MARK: - Preview
struct CreateQuizView_Previews: PreviewProvider {
    static var previews: some View {
        CreateQuizView()
    }
}
