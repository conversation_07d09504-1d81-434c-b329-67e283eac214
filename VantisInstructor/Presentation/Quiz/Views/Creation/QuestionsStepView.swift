//
//  QuestionsStepView.swift
//  mobile-app-template
//
//  Created by Inst<PERSON>ctor <PERSON>pp on 27/7/25.
//

import SwiftUI

struct QuestionsStepView: View {
    @ObservedObject var viewModel: QuizCreationViewModel
    @State private var showingQuestionBuilder = false
    @State private var selectedQuestionType: QuestionType = .singleChoice
    @State private var editingQuestionIndex: Int?
    
    var body: some View {
        VStack(spacing: AppConstants.UI.sectionSpacing) {
            // Header
            headerSection
            
            // Questions List
            if viewModel.questions.isEmpty {
                emptyQuestionsView
            } else {
                questionsListView
            }
            
            // Add Question Button
            addQuestionSection
            
            Spacer()
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("Câu hỏi")
                    .font(AppConstants.Typography.title2)
                    .fontWeight(.bold)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Spacer()
                
                Text("\(viewModel.questions.count) câu hỏi")
                    .font(AppConstants.Typography.subheadline)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            
            Text("Thêm câu hỏi cho quiz của bạn")
                .font(AppConstants.Typography.body)
                .foregroundColor(AppConstants.Colors.textSecondary)
            
            // Validation errors
            if viewModel.hasValidationErrors(for: "questions") {
                ValidationErrorView(errors: viewModel.getValidationErrors(for: "questions"))
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    // MARK: - Empty Questions View
    private var emptyQuestionsView: some View {
        VStack(spacing: 20) {
            Image(systemName: "questionmark.circle")
                .font(.system(size: 60))
                .foregroundColor(AppConstants.Colors.textSecondary)
            
            VStack(spacing: 8) {
                Text("Chưa có câu hỏi nào")
                    .font(AppConstants.Typography.headline)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Text("Thêm câu hỏi đầu tiên để bắt đầu tạo quiz")
                    .font(AppConstants.Typography.body)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 40)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(AppConstants.Colors.border, lineWidth: 0.5)
        )
    }
    
    // MARK: - Questions List View
    private var questionsListView: some View {
        VStack(spacing: 12) {
            ForEach(Array(viewModel.questions.enumerated()), id: \.offset) { index, question in
                QuestionRowView(
                    question: question,
                    index: index,
                    onEdit: {
                        editingQuestionIndex = index
                        showingQuestionBuilder = true
                    },
                    onDelete: {
                        viewModel.removeQuestion(at: index)
                    }
                )
            }
        }
    }
    
    // MARK: - Add Question Section
    private var addQuestionSection: some View {
        VStack(spacing: 16) {
            // Question Type Selector
            FormSectionView(
                title: "Loại câu hỏi",
                subtitle: "Chọn loại câu hỏi bạn muốn thêm"
            ) {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                    ForEach(QuestionType.allCases, id: \.self) { type in
                        QuestionTypeCard(
                            type: type,
                            isSelected: selectedQuestionType == type
                        ) {
                            selectedQuestionType = type
                        }
                    }
                }
            }
            
            // Add Question Button
            Button {
                editingQuestionIndex = nil
                showingQuestionBuilder = true
            } label: {
                HStack {
                    Image(systemName: "plus")
                    Text("Thêm câu hỏi")
                }
                .font(AppConstants.Typography.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .background(AppConstants.Colors.primary)
                .cornerRadius(AppConstants.UI.cornerRadius)
            }
        }
        .sheet(isPresented: $showingQuestionBuilder) {
            QuestionBuilderView(
                questionType: selectedQuestionType,
                existingQuestion: editingQuestionIndex != nil ? viewModel.questions[editingQuestionIndex!] : nil,
                onSave: { questionBuilder in
                    if let editIndex = editingQuestionIndex {
                        viewModel.updateQuestion(at: editIndex, with: questionBuilder)
                    } else {
                        viewModel.addQuestion(questionBuilder)
                    }
                    showingQuestionBuilder = false
                },
                onCancel: {
                    showingQuestionBuilder = false
                }
            )
        }
    }
}

// MARK: - Question Row View
struct QuestionRowView: View {
    let question: QuestionBuilder
    let index: Int
    let onEdit: () -> Void
    let onDelete: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Câu hỏi \(index + 1)")
                        .font(AppConstants.Typography.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    HStack {
                        Image(systemName: question.questionType.icon)
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.primary)
                        
                        Text(question.questionType.displayName)
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                        
                        Spacer()
                        
                        Text("\(Int(question.score)) điểm")
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                }
                
                Spacer()
                
                HStack(spacing: 8) {
                    Button(action: onEdit) {
                        Image(systemName: "pencil")
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.primary)
                    }
                    
                    Button(action: onDelete) {
                        Image(systemName: "trash")
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.error)
                    }
                }
            }
            
            // Question Text Preview
            Text(question.text.isEmpty ? "Chưa có nội dung câu hỏi" : question.text)
                .font(AppConstants.Typography.body)
                .foregroundColor(question.text.isEmpty ? AppConstants.Colors.textSecondary : AppConstants.Colors.textPrimary)
                .lineLimit(3)
                .multilineTextAlignment(.leading)
            
            // Validation Status
            let validationErrors = question.validate()
            if !validationErrors.isEmpty {
                HStack {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.error)
                    
                    Text("Câu hỏi chưa hoàn chỉnh")
                        .font(AppConstants.Typography.caption)
                        .foregroundColor(AppConstants.Colors.error)
                    
                    Spacer()
                }
            } else {
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.primary)
                    
                    Text("Câu hỏi hợp lệ")
                        .font(AppConstants.Typography.caption)
                        .foregroundColor(AppConstants.Colors.primary)
                    
                    Spacer()
                }
            }
        }
        .padding(AppConstants.UI.cardPadding)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(
                    question.validate().isEmpty ? AppConstants.Colors.border : AppConstants.Colors.error,
                    lineWidth: 0.5
                )
        )
    }
}

// MARK: - Question Type Card
struct QuestionTypeCard: View {
    let type: QuestionType
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: type.icon)
                    .font(.title3)
                    .foregroundColor(isSelected ? .white : AppConstants.Colors.primary)
                
                Text(type.displayName)
                    .font(AppConstants.Typography.caption)
                    .fontWeight(.medium)
                    .foregroundColor(isSelected ? .white : AppConstants.Colors.textPrimary)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(isSelected ? AppConstants.Colors.primary : AppConstants.Colors.cardBackground)
            .cornerRadius(AppConstants.UI.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                    .stroke(
                        isSelected ? AppConstants.Colors.primary : AppConstants.Colors.border,
                        lineWidth: isSelected ? 2 : 0.5
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Preview
struct QuestionsStepView_Previews: PreviewProvider {
    static var previews: some View {
        QuestionsStepView(viewModel: QuizCreationViewModel())
            .padding()
            .background(AppConstants.Colors.background)
    }
}
