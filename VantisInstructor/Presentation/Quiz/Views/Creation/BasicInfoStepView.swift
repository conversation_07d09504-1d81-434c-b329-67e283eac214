//
//  BasicInfoStepView.swift
//  mobile-app-template
//
//  Created by Inst<PERSON>ctor <PERSON>pp on 27/7/25.
//

import SwiftUI

struct BasicInfoStepView: View {
    @ObservedObject var viewModel: QuizCreationViewModel
    @FocusState private var focusedField: Field?
    
    enum Field: Hashable {
        case name
        case description
    }
    
    var body: some View {
        VStack(spacing: AppConstants.UI.sectionSpacing) {
            // Header
            headerSection
            
            // Quiz Name
            quizNameSection
            
            // Quiz Description
            quizDescriptionSection
            
            // Quiz Type
            quizTypeSection
            
            // Subject & Class (Optional)
            subjectClassSection
            
            Spacer()
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Thông tin cơ bản")
                .font(AppConstants.Typography.title2)
                .fontWeight(.bold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Text("Nhập thông tin cơ bản cho quiz của bạn")
                .font(AppConstants.Typography.body)
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    // MARK: - Quiz Name Section
    private var quizNameSection: some View {
        FormSectionView(
            title: "Tên Quiz",
            subtitle: "Tên quiz sẽ hiển thị cho học sinh",
            isRequired: true,
            hasError: viewModel.hasValidationErrors(for: "basicInfo")
        ) {
            VStack(alignment: .leading, spacing: 8) {
                TextField("Nhập tên quiz", text: $viewModel.basicInfo.name)
                    .textFieldStyle()
                    .focused($focusedField, equals: .name)
                    .onSubmit {
                        focusedField = .description
                    }
                
                // Character count
                HStack {
                    Spacer()
                    Text("\(viewModel.basicInfo.name.count)/100")
                        .font(AppConstants.Typography.caption)
                        .foregroundColor(
                            viewModel.basicInfo.name.count > 100 ? 
                            AppConstants.Colors.error : 
                            AppConstants.Colors.textSecondary
                        )
                }
                
                // Validation errors
                if viewModel.hasValidationErrors(for: "basicInfo") {
                    ForEach(viewModel.getValidationErrors(for: "basicInfo"), id: \.self) { error in
                        Text(error)
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.error)
                    }
                }
            }
        }
    }
    
    // MARK: - Quiz Description Section
    private var quizDescriptionSection: some View {
        FormSectionView(
            title: "Mô tả",
            subtitle: "Mô tả ngắn gọn về nội dung quiz (tùy chọn)"
        ) {
            VStack(alignment: .leading, spacing: 8) {
                TextField("Nhập mô tả quiz", text: $viewModel.basicInfo.description, axis: .vertical)
                    .textFieldStyle()
                    .lineLimit(3...6)
                    .focused($focusedField, equals: .description)
                
                // Character count
                HStack {
                    Spacer()
                    Text("\(viewModel.basicInfo.description.count)/500")
                        .font(AppConstants.Typography.caption)
                        .foregroundColor(
                            viewModel.basicInfo.description.count > 500 ? 
                            AppConstants.Colors.error : 
                            AppConstants.Colors.textSecondary
                        )
                }
            }
        }
    }
    
    // MARK: - Quiz Type Section
    private var quizTypeSection: some View {
        FormSectionView(
            title: "Loại Quiz",
            subtitle: "Chọn loại quiz phù hợp"
        ) {
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                ForEach(QuizType.allCases, id: \.self) { type in
                    QuizTypeCard(
                        type: type,
                        isSelected: viewModel.basicInfo.quizType == type
                    ) {
                        viewModel.basicInfo.quizType = type
                    }
                }
            }
        }
    }
    
    // MARK: - Subject & Class Section
    private var subjectClassSection: some View {
        FormSectionView(
            title: "Môn học & Lớp",
            subtitle: "Chọn môn học và lớp (tùy chọn)"
        ) {
            VStack(spacing: 12) {
                // Subject Picker
                HStack {
                    Text("Môn học:")
                        .font(AppConstants.Typography.subheadline)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    Spacer()
                    
                    Button(viewModel.basicInfo.subjectId != nil ? "Đã chọn" : "Chọn môn học") {
                        // TODO: Implement subject picker
                    }
                    .font(AppConstants.Typography.subheadline)
                    .foregroundColor(AppConstants.Colors.primary)
                }
                .padding()
                .background(Color.white)
                .cornerRadius(AppConstants.UI.cornerRadius)
                .overlay(
                    RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                        .stroke(AppConstants.Colors.border, lineWidth: 0.5)
                )
                
                // Class Picker
                HStack {
                    Text("Lớp học:")
                        .font(AppConstants.Typography.subheadline)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    Spacer()
                    
                    Button(viewModel.basicInfo.classId != nil ? "Đã chọn" : "Chọn lớp học") {
                        // TODO: Implement class picker
                    }
                    .font(AppConstants.Typography.subheadline)
                    .foregroundColor(AppConstants.Colors.primary)
                }
                .padding()
                .background(Color.white)
                .cornerRadius(AppConstants.UI.cornerRadius)
                .overlay(
                    RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                        .stroke(AppConstants.Colors.border, lineWidth: 0.5)
                )
            }
        }
    }
}

// MARK: - Quiz Type Card
struct QuizTypeCard: View {
    let type: QuizType
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: type.icon)
                    .font(.title2)
                    .foregroundColor(isSelected ? .white : AppConstants.Colors.primary)
                
                Text(type.displayName)
                    .font(AppConstants.Typography.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(isSelected ? .white : AppConstants.Colors.textPrimary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(isSelected ? AppConstants.Colors.primary : Color.white)
            .cornerRadius(AppConstants.UI.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                    .stroke(
                        isSelected ? AppConstants.Colors.primary : AppConstants.Colors.border,
                        lineWidth: isSelected ? 2 : 0.5
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Preview
struct BasicInfoStepView_Previews: PreviewProvider {
    static var previews: some View {
        BasicInfoStepView(viewModel: QuizCreationViewModel())
            .padding()
            .background(AppConstants.Colors.background)
    }
}
