//
//  FormSectionView.swift
//  mobile-app-template
//
//  Created by Instructor App on 27/7/25.
//

import SwiftUI

struct FormSectionView<Content: View>: View {
    let title: String
    let subtitle: String?
    let isRequired: Bool
    let hasError: Bool
    @ViewBuilder let content: () -> Content
    
    init(
        title: String,
        subtitle: String? = nil,
        isRequired: Bool = false,
        hasError: Bool = false,
        @ViewBuilder content: @escaping () -> Content
    ) {
        self.title = title
        self.subtitle = subtitle
        self.isRequired = isRequired
        self.hasError = hasError
        self.content = content
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: AppConstants.UI.itemSpacing) {
            // Header
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(title)
                        .font(AppConstants.Typography.headline)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    if isRequired {
                        Text("*")
                            .font(AppConstants.Typography.headline)
                            .foregroundColor(AppConstants.Colors.error)
                    }
                    
                    Spacer()
                }
                
                if let subtitle = subtitle {
                    Text(subtitle)
                        .font(AppConstants.Typography.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
            }
            
            // Content
            content()
        }
        .padding(AppConstants.UI.cardPadding)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(
                    hasError ? AppConstants.Colors.error : AppConstants.Colors.border,
                    lineWidth: hasError ? 1 : 0.5
                )
        )
    }
}

// MARK: - Validation Error View
struct ValidationErrorView: View {
    let errors: [String]
    
    var body: some View {
        if !errors.isEmpty {
            VStack(alignment: .leading, spacing: 4) {
                ForEach(errors, id: \.self) { error in
                    HStack(alignment: .top, spacing: 8) {
                        Image(systemName: "exclamationmark.circle.fill")
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.error)
                        
                        Text(error)
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.error)
                            .multilineTextAlignment(.leading)
                        
                        Spacer()
                    }
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(AppConstants.Colors.error.opacity(0.1))
            .cornerRadius(8)
        }
    }
}

// MARK: - Form Field View
struct FormFieldView<Content: View>: View {
    let label: String
    let isRequired: Bool
    let errorMessage: String?
    @ViewBuilder let content: () -> Content
    
    init(
        label: String,
        isRequired: Bool = false,
        errorMessage: String? = nil,
        @ViewBuilder content: @escaping () -> Content
    ) {
        self.label = label
        self.isRequired = isRequired
        self.errorMessage = errorMessage
        self.content = content
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Label
            HStack {
                Text(label)
                    .font(AppConstants.Typography.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                if isRequired {
                    Text("*")
                        .font(AppConstants.Typography.subheadline)
                        .foregroundColor(AppConstants.Colors.error)
                }
                
                Spacer()
            }
            
            // Content
            content()
            
            // Error Message
            if let errorMessage = errorMessage {
                HStack(alignment: .top, spacing: 6) {
                    Image(systemName: "exclamationmark.circle.fill")
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.error)
                    
                    Text(errorMessage)
                        .font(AppConstants.Typography.caption)
                        .foregroundColor(AppConstants.Colors.error)
                    
                    Spacer()
                }
            }
        }
    }
}

// MARK: - Progress Indicator View
struct ProgressIndicatorView: View {
    let currentStep: Int
    let totalSteps: Int
    let stepTitles: [String]
    
    var body: some View {
        VStack(spacing: 16) {
            // Progress Bar
            HStack(spacing: 0) {
                ForEach(0..<totalSteps, id: \.self) { index in
                    Circle()
                        .fill(stepColor(for: index))
                        .frame(width: 24, height: 24)
                        .overlay(
                            Text("\(index + 1)")
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(stepTextColor(for: index))
                        )
                    
                    if index < totalSteps - 1 {
                        Rectangle()
                            .fill(stepColor(for: index))
                            .frame(height: 2)
                            .frame(maxWidth: .infinity)
                    }
                }
            }
            
            // Step Title
            if currentStep < stepTitles.count {
                Text(stepTitles[currentStep])
                    .font(AppConstants.Typography.headline)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding(.horizontal, AppConstants.UI.screenPadding)
    }
    
    private func stepColor(for index: Int) -> Color {
        if index < currentStep {
            return AppConstants.Colors.primary // Completed
        } else if index == currentStep {
            return AppConstants.Colors.primary // Current
        } else {
            return AppConstants.Colors.border // Future
        }
    }
    
    private func stepTextColor(for index: Int) -> Color {
        if index <= currentStep {
            return .white
        } else {
            return AppConstants.Colors.textSecondary
        }
    }
}

// MARK: - Preview
struct FormSectionView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            FormSectionView(
                title: "Quiz Name",
                subtitle: "Enter a descriptive name for your quiz",
                isRequired: true,
                hasError: false
            ) {
                TextField("Quiz name", text: .constant("Sample Quiz"))
                    .textFieldStyle()
            }
            
            FormSectionView(
                title: "Description",
                subtitle: "Optional description",
                hasError: true
            ) {
                TextField("Description", text: .constant(""))
                    .textFieldStyle()
                
                ValidationErrorView(errors: ["Description is required"])
            }
            
            ProgressIndicatorView(
                currentStep: 1,
                totalSteps: 4,
                stepTitles: ["Basic Info", "Questions", "Settings", "Preview"]
            )
        }
        .padding()
        .background(AppConstants.Colors.background)
    }
}
