//
//  QuestionBuilderView.swift
//  mobile-app-template
//
//  Created by Instructor App on 27/7/25.
//

import SwiftUI

struct QuestionBuilderView: View {
    let questionType: QuestionType
    let existingQuestion: QuestionBuilder?
    let onSave: (QuestionBuilder) -> Void
    let onCancel: () -> Void
    
    @StateObject private var questionBuilder: QuestionBuilder
    @FocusState private var focusedField: Field?
    
    enum Field: Hashable {
        case questionText
        case score
        case answer(Int)
    }
    
    init(
        questionType: QuestionType,
        existingQuestion: QuestionBuilder? = nil,
        onSave: @escaping (QuestionBuilder) -> Void,
        onCancel: @escaping () -> Void
    ) {
        self.questionType = questionType
        self.existingQuestion = existingQuestion
        self.onSave = onSave
        self.onCancel = onCancel
        
        // Initialize with existing question or create new one
        if let existing = existingQuestion {
            self._questionBuilder = StateObject(wrappedValue: existing)
        } else {
            self._questionBuilder = StateObject(wrappedValue: QuestionBuilderFactory.createQuestionBuilder(for: questionType))
        }
    }
    
    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: AppConstants.UI.sectionSpacing) {
                    // Question Text Section
                    questionTextSection
                    
                    // Score Section
                    scoreSection
                    
                    // Type-specific sections
                    typeSpecificSection
                    
                    // Validation Summary
                    validationSection
                }
                .padding(.horizontal, AppConstants.UI.screenPadding)
                .padding(.bottom, 100)
            }
            .background(AppConstants.Colors.background)
            .navigationTitle(existingQuestion != nil ? "Chỉnh sửa câu hỏi" : "Thêm câu hỏi")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Hủy") {
                        onCancel()
                    }
                    .foregroundColor(AppConstants.Colors.textSecondary)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Lưu") {
                        onSave(questionBuilder)
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                    .disabled(!questionBuilder.isValid)
                }
            }
        }
    }
    
    // MARK: - Question Text Section
    private var questionTextSection: some View {
        FormSectionView(
            title: "Nội dung câu hỏi",
            subtitle: "Nhập nội dung câu hỏi của bạn",
            isRequired: true,
            hasError: questionBuilder.text.isEmpty
        ) {
            VStack(alignment: .leading, spacing: 8) {
                TextField("Nhập nội dung câu hỏi", text: $questionBuilder.text, axis: .vertical)
                    .textFieldStyle()
                    .lineLimit(3...10)
                    .focused($focusedField, equals: .questionText)
                
                // Character count
                HStack {
                    Spacer()
                    Text("\(questionBuilder.text.count)/1000")
                        .font(AppConstants.Typography.caption)
                        .foregroundColor(
                            questionBuilder.text.count > 1000 ? 
                            AppConstants.Colors.error : 
                            AppConstants.Colors.textSecondary
                        )
                }
            }
        }
    }
    
    // MARK: - Score Section
    private var scoreSection: some View {
        FormSectionView(
            title: "Điểm số",
            subtitle: "Điểm số cho câu hỏi này"
        ) {
            HStack {
                TextField("Điểm", value: $questionBuilder.score, format: .number)
                    .textFieldStyle()
                    .keyboardType(.decimalPad)
                    .focused($focusedField, equals: .score)
                    .frame(width: 80)
                
                Text("điểm")
                    .font(AppConstants.Typography.body)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                
                Spacer()
            }
        }
    }
    
    // MARK: - Type-specific Section
    @ViewBuilder
    private var typeSpecificSection: some View {
        switch questionType {
        case .essay:
            if let essayBuilder = questionBuilder as? EssayQuestionBuilder {
                EssayQuestionSection(questionBuilder: essayBuilder)
            }
        case .singleChoice:
            if let singleChoiceBuilder = questionBuilder as? SingleChoiceQuestionBuilder {
                SingleChoiceQuestionSection(questionBuilder: singleChoiceBuilder, focusedField: $focusedField)
            }
        case .multipleChoice:
            if let multipleChoiceBuilder = questionBuilder as? MultipleChoiceQuestionBuilder {
                MultipleChoiceQuestionSection(questionBuilder: multipleChoiceBuilder, focusedField: $focusedField)
            }
        case .trueFalse:
            if let trueFalseBuilder = questionBuilder as? TrueFalseQuestionBuilder {
                TrueFalseQuestionSection(questionBuilder: trueFalseBuilder)
            }
        }
    }
    
    // MARK: - Validation Section
    @ViewBuilder
    private var validationSection: some View {
        let validationErrors = questionBuilder.validate()

        if !validationErrors.isEmpty {
            FormSectionView(
                title: "Lỗi cần khắc phục",
                hasError: true
            ) {
                ValidationErrorView(errors: validationErrors.map { $0.message })
            }
        } else {
            FormSectionView(
                title: "Trạng thái",
                hasError: false
            ) {
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(AppConstants.Colors.primary)
                    
                    Text("Câu hỏi hợp lệ")
                        .font(AppConstants.Typography.body)
                        .foregroundColor(AppConstants.Colors.primary)
                    
                    Spacer()
                }
            }
        }
    }
}

// MARK: - Essay Question Section
struct EssayQuestionSection: View {
    @ObservedObject var questionBuilder: EssayQuestionBuilder
    
    var body: some View {
        VStack(spacing: AppConstants.UI.itemSpacing) {
            FormSectionView(
                title: "Độ dài mong đợi",
                subtitle: "Số từ mong đợi trong câu trả lời"
            ) {
                HStack {
                    TextField("Số từ", value: $questionBuilder.expectedLength, format: .number)
                        .textFieldStyle()
                        .keyboardType(.numberPad)
                        .frame(width: 80)
                    
                    Text("từ")
                        .font(AppConstants.Typography.body)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                    
                    Spacer()
                }
            }
            
            FormSectionView(
                title: "Tiêu chí chấm điểm",
                subtitle: "Hướng dẫn chấm điểm cho câu hỏi tự luận (tùy chọn)"
            ) {
                TextField("Nhập tiêu chí chấm điểm", text: $questionBuilder.gradingCriteria, axis: .vertical)
                    .textFieldStyle()
                    .lineLimit(3...6)
            }
        }
    }
}

// MARK: - Single Choice Question Section
struct SingleChoiceQuestionSection: View {
    @ObservedObject var questionBuilder: SingleChoiceQuestionBuilder
    var focusedField: FocusState<QuestionBuilderView.Field?>.Binding
    
    var body: some View {
        FormSectionView(
            title: "Đáp án",
            subtitle: "Thêm các lựa chọn và chọn đáp án đúng",
            isRequired: true
        ) {
            VStack(spacing: 12) {
                ForEach(Array(questionBuilder.answers.enumerated()), id: \.offset) { index, answer in
                    AnswerRowView(
                        answer: answer,
                        index: index,
                        isCorrect: answer.isCorrect,
                        canDelete: questionBuilder.answers.count > 2,
                        onToggleCorrect: {
                            questionBuilder.setCorrectAnswer(at: index)
                        },
                        onDelete: {
                            questionBuilder.removeAnswer(at: index)
                        },
                        focusedField: focusedField
                    )
                }
                
                if questionBuilder.answers.count < 6 {
                    Button {
                        questionBuilder.addAnswer()
                    } label: {
                        HStack {
                            Image(systemName: "plus.circle")
                            Text("Thêm đáp án")
                        }
                        .font(AppConstants.Typography.subheadline)
                        .foregroundColor(AppConstants.Colors.primary)
                    }
                }
            }
        }
    }
}

// MARK: - Multiple Choice Question Section
struct MultipleChoiceQuestionSection: View {
    @ObservedObject var questionBuilder: MultipleChoiceQuestionBuilder
    var focusedField: FocusState<QuestionBuilderView.Field?>.Binding
    
    var body: some View {
        FormSectionView(
            title: "Đáp án",
            subtitle: "Thêm các lựa chọn và chọn các đáp án đúng",
            isRequired: true
        ) {
            VStack(spacing: 12) {
                ForEach(Array(questionBuilder.answers.enumerated()), id: \.offset) { index, answer in
                    AnswerRowView(
                        answer: answer,
                        index: index,
                        isCorrect: answer.isCorrect,
                        canDelete: questionBuilder.answers.count > 2,
                        isMultipleChoice: true,
                        onToggleCorrect: {
                            questionBuilder.toggleAnswerCorrectness(at: index)
                        },
                        onDelete: {
                            questionBuilder.removeAnswer(at: index)
                        },
                        focusedField: focusedField
                    )
                }
                
                if questionBuilder.answers.count < 6 {
                    Button {
                        questionBuilder.addAnswer()
                    } label: {
                        HStack {
                            Image(systemName: "plus.circle")
                            Text("Thêm đáp án")
                        }
                        .font(AppConstants.Typography.subheadline)
                        .foregroundColor(AppConstants.Colors.primary)
                    }
                }
            }
        }
    }
}

// MARK: - True/False Question Section
struct TrueFalseQuestionSection: View {
    @ObservedObject var questionBuilder: TrueFalseQuestionBuilder
    
    var body: some View {
        VStack(spacing: AppConstants.UI.itemSpacing) {
            FormSectionView(
                title: "Đáp án đúng",
                subtitle: "Chọn đáp án đúng cho câu hỏi"
            ) {
                HStack(spacing: 16) {
                    Button {
                        questionBuilder.correctAnswer = true
                    } label: {
                        HStack {
                            Image(systemName: questionBuilder.correctAnswer ? "checkmark.circle.fill" : "circle")
                                .foregroundColor(questionBuilder.correctAnswer ? AppConstants.Colors.primary : AppConstants.Colors.textSecondary)
                            Text("Đúng")
                                .foregroundColor(AppConstants.Colors.textPrimary)
                        }
                    }
                    .buttonStyle(PlainButtonStyle())
                    
                    Button {
                        questionBuilder.correctAnswer = false
                    } label: {
                        HStack {
                            Image(systemName: !questionBuilder.correctAnswer ? "checkmark.circle.fill" : "circle")
                                .foregroundColor(!questionBuilder.correctAnswer ? AppConstants.Colors.primary : AppConstants.Colors.textSecondary)
                            Text("Sai")
                                .foregroundColor(AppConstants.Colors.textPrimary)
                        }
                    }
                    .buttonStyle(PlainButtonStyle())
                    
                    Spacer()
                }
            }
            
            FormSectionView(
                title: "Giải thích",
                subtitle: "Giải thích cho đáp án (tùy chọn)"
            ) {
                TextField("Nhập giải thích", text: $questionBuilder.explanation, axis: .vertical)
                    .textFieldStyle()
                    .lineLimit(2...4)
            }
        }
    }
}

// MARK: - Answer Row View
struct AnswerRowView: View {
    @ObservedObject var answer: AnswerBuilder
    let index: Int
    let isCorrect: Bool
    let canDelete: Bool
    let isMultipleChoice: Bool
    let onToggleCorrect: () -> Void
    let onDelete: () -> Void
    var focusedField: FocusState<QuestionBuilderView.Field?>.Binding

    init(
        answer: AnswerBuilder,
        index: Int,
        isCorrect: Bool,
        canDelete: Bool,
        isMultipleChoice: Bool = false,
        onToggleCorrect: @escaping () -> Void,
        onDelete: @escaping () -> Void,
        focusedField: FocusState<QuestionBuilderView.Field?>.Binding
    ) {
        self.answer = answer
        self.index = index
        self.isCorrect = isCorrect
        self.canDelete = canDelete
        self.isMultipleChoice = isMultipleChoice
        self.onToggleCorrect = onToggleCorrect
        self.onDelete = onDelete
        self.focusedField = focusedField
    }

    var body: some View {
        HStack(spacing: 12) {
            // Correct Answer Indicator
            Button(action: onToggleCorrect) {
                Image(systemName: correctnessIcon)
                    .font(.title3)
                    .foregroundColor(isCorrect ? AppConstants.Colors.primary : AppConstants.Colors.textSecondary)
            }
            .buttonStyle(PlainButtonStyle())

            // Answer Text Field
            TextField("Nhập đáp án \(index + 1)", text: $answer.text)
                .textFieldStyle()
                .focused(focusedField, equals: .answer(index))

            // Delete Button
            if canDelete {
                Button(action: onDelete) {
                    Image(systemName: "trash")
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.error)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }

    private var correctnessIcon: String {
        if isMultipleChoice {
            return isCorrect ? "checkmark.square.fill" : "square"
        } else {
            return isCorrect ? "checkmark.circle.fill" : "circle"
        }
    }
}

// MARK: - Preview
struct QuestionBuilderView_Previews: PreviewProvider {
    static var previews: some View {
        QuestionBuilderView(
            questionType: .singleChoice,
            onSave: { _ in },
            onCancel: { }
        )
    }
}
