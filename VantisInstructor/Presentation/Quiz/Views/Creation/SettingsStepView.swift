//
//  SettingsStepView.swift
//  mobile-app-template
//
//  Created by Instructor <PERSON>pp on 27/7/25.
//

import SwiftUI

struct SettingsStepView: View {
    @ObservedObject var viewModel: QuizCreationViewModel
    @FocusState private var focusedField: Field?
    
    enum Field: Hashable {
        case maxScore
        case passingScore
        case timeLimit
        case maxAttempts
    }
    
    var body: some View {
        VStack(spacing: AppConstants.UI.sectionSpacing) {
            // Header
            headerSection
            
            // Scoring Settings
            scoringSection
            
            // Time & Attempts Settings
            timeAttemptsSection
            
            // Quiz Behavior Settings
            behaviorSection
            
            Spacer()
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Cài đặt Quiz")
                .font(AppConstants.Typography.title2)
                .fontWeight(.bold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Text("<PERSON><PERSON><PERSON> hình các thiế<PERSON> lập cho quiz của bạn")
                .font(AppConstants.Typography.body)
                .foregroundColor(AppConstants.Colors.textSecondary)
            
            // Validation errors
            if viewModel.hasValidationErrors(for: "settings") {
                ValidationErrorView(errors: viewModel.getValidationErrors(for: "settings"))
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    // MARK: - Scoring Section
    private var scoringSection: some View {
        FormSectionView(
            title: "Điểm số",
            subtitle: "Cài đặt điểm số cho quiz"
        ) {
            VStack(spacing: 16) {
                // Max Score (Auto-calculated)
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Điểm tối đa")
                            .font(AppConstants.Typography.subheadline)
                            .foregroundColor(AppConstants.Colors.textPrimary)

                        Text("Tự động tính từ tổng điểm câu hỏi")
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }

                    Spacer()

                    HStack {
                        Text("\(Int(viewModel.actualMaxScore))")
                            .font(AppConstants.Typography.body)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(AppConstants.Colors.background)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 8)
                                            .stroke(AppConstants.Colors.border, lineWidth: 1)
                                    )
                            )

                        Text("điểm")
                            .font(AppConstants.Typography.body)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                }
                
                Divider()
                
                // Passing Score
                VStack(spacing: 8) {
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Điểm đạt")
                                .font(AppConstants.Typography.subheadline)
                                .foregroundColor(AppConstants.Colors.textPrimary)

                            Text("Điểm tối thiểu để đạt quiz (tối đa \(Int(viewModel.actualMaxScore)))")
                                .font(AppConstants.Typography.caption)
                                .foregroundColor(AppConstants.Colors.textSecondary)
                        }

                        Spacer()

                        HStack {
                            TextField("0", value: $viewModel.settings.passingScore, format: .number)
                                .textFieldStyle()
                                .keyboardType(.decimalPad)
                                .focused($focusedField, equals: .passingScore)
                                .frame(width: 80)

                            Text("điểm")
                                .font(AppConstants.Typography.body)
                                .foregroundColor(AppConstants.Colors.textSecondary)
                        }
                    }

                    // Show percentage if max score > 0
                    if viewModel.actualMaxScore > 0 {
                        HStack {
                            Text("Tương đương \(Int((viewModel.settings.passingScore / viewModel.actualMaxScore) * 100))%")
                                .font(AppConstants.Typography.caption)
                                .foregroundColor(AppConstants.Colors.textSecondary)

                            Spacer()
                        }
                    }
                }
                
                // Passing Percentage
                if viewModel.settings.maxScore > 0 {
                    HStack {
                        Spacer()
                        Text("(\(Int((viewModel.settings.passingScore / viewModel.settings.maxScore) * 100))% điểm tối đa)")
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                }
            }
        }
    }
    
    // MARK: - Time & Attempts Section
    private var timeAttemptsSection: some View {
        FormSectionView(
            title: "Thời gian & Lần làm bài",
            subtitle: "Cài đặt thời gian và số lần làm bài"
        ) {
            VStack(spacing: 16) {
                // Time Limit
                VStack(spacing: 12) {
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Giới hạn thời gian")
                                .font(AppConstants.Typography.subheadline)
                                .foregroundColor(AppConstants.Colors.textPrimary)
                            
                            Text("Thời gian tối đa để hoàn thành quiz")
                                .font(AppConstants.Typography.caption)
                                .foregroundColor(AppConstants.Colors.textSecondary)
                        }
                        
                        Spacer()
                        
                        Toggle("", isOn: Binding(
                            get: { viewModel.settings.timeLimit != nil },
                            set: { enabled in
                                if enabled {
                                    viewModel.settings.timeLimit = 60
                                } else {
                                    viewModel.settings.timeLimit = nil
                                }
                            }
                        ))
                    }
                    
                    if viewModel.settings.timeLimit != nil {
                        HStack {
                            Spacer()
                            
                            TextField("60", value: Binding(
                                get: { viewModel.settings.timeLimit ?? 60 },
                                set: { viewModel.settings.timeLimit = $0 }
                            ), format: .number)
                            .textFieldStyle()
                            .keyboardType(.numberPad)
                            .focused($focusedField, equals: .timeLimit)
                            .frame(width: 80)
                            
                            Text("phút")
                                .font(AppConstants.Typography.body)
                                .foregroundColor(AppConstants.Colors.textSecondary)
                        }
                    }
                }
                
                Divider()
                
                // Max Attempts
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Số lần làm bài")
                            .font(AppConstants.Typography.subheadline)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        
                        Text("Số lần tối đa học sinh có thể làm bài")
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    
                    Spacer()
                    
                    HStack {
                        TextField("1", value: $viewModel.settings.maxAttempts, format: .number)
                            .textFieldStyle()
                            .keyboardType(.numberPad)
                            .focused($focusedField, equals: .maxAttempts)
                            .frame(width: 80)
                        
                        Text("lần")
                            .font(AppConstants.Typography.body)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                }
            }
        }
    }
    
    // MARK: - Behavior Section
    private var behaviorSection: some View {
        FormSectionView(
            title: "Hành vi Quiz",
            subtitle: "Cài đặt cách thức hoạt động của quiz"
        ) {
            VStack(spacing: 16) {
                // Randomize Questions
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Xáo trộn câu hỏi")
                            .font(AppConstants.Typography.subheadline)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        
                        Text("Hiển thị câu hỏi theo thứ tự ngẫu nhiên")
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    
                    Spacer()
                    
                    Toggle("", isOn: $viewModel.settings.isRandomized)
                }
                
                Divider()
                
                // Show Correct Answers
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Hiển thị đáp án đúng")
                            .font(AppConstants.Typography.subheadline)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        
                        Text("Cho phép học sinh xem đáp án sau khi hoàn thành")
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    
                    Spacer()
                    
                    Toggle("", isOn: $viewModel.settings.showCorrectAnswers)
                }
            }
        }
    }
}

// MARK: - Preview
struct SettingsStepView_Previews: PreviewProvider {
    static var previews: some View {
        SettingsStepView(viewModel: QuizCreationViewModel())
            .padding()
            .background(AppConstants.Colors.background)
    }
}
