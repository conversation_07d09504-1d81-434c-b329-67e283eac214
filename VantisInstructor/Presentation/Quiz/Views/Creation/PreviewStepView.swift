//
//  PreviewStepView.swift
//  mobile-app-template
//
//  Created by Inst<PERSON><PERSON> <PERSON><PERSON> on 27/7/25.
//

import SwiftUI

struct PreviewStepView: View {
    @ObservedObject var viewModel: QuizCreationViewModel
    
    var body: some View {
        VStack(spacing: AppConstants.UI.sectionSpacing) {
            // Header
            headerSection
            
            // Quiz Summary
            quizSummarySection
            
            // Questions Preview
            questionsPreviewSection
            
            // Settings Summary
            settingsSummarySection
            
            Spacer()
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Xem trước Quiz")
                .font(AppConstants.Typography.title2)
                .fontWeight(.bold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Text("Kiểm tra lại thông tin trước khi tạo quiz")
                .font(AppConstants.Typography.body)
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    // MARK: - Quiz Summary Section
    private var quizSummarySection: some View {
        FormSectionView(
            title: "Thông tin Quiz",
            subtitle: "Tổng quan về quiz của bạn"
        ) {
            VStack(spacing: 16) {
                // Quiz Name
                HStack {
                    Text("Tên:")
                        .font(AppConstants.Typography.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                    
                    Spacer()
                    
                    Text(viewModel.basicInfo.name)
                        .font(AppConstants.Typography.subheadline)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                        .multilineTextAlignment(.trailing)
                }
                
                if !viewModel.basicInfo.description.isEmpty {
                    Divider()
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Mô tả:")
                            .font(AppConstants.Typography.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                        
                        Text(viewModel.basicInfo.description)
                            .font(AppConstants.Typography.body)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                    }
                }
                
                Divider()
                
                // Quiz Type
                HStack {
                    Text("Loại:")
                        .font(AppConstants.Typography.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                    
                    Spacer()
                    
                    HStack {
                        Image(systemName: viewModel.basicInfo.quizType.icon)
                            .foregroundColor(AppConstants.Colors.primary)
                        
                        Text(viewModel.basicInfo.quizType.displayName)
                            .font(AppConstants.Typography.subheadline)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                    }
                }
                
                Divider()
                
                // Statistics
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("\(viewModel.questions.count)")
                            .font(AppConstants.Typography.title3)
                            .fontWeight(.bold)
                            .foregroundColor(AppConstants.Colors.primary)
                        
                        Text("Câu hỏi")
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .center, spacing: 4) {
                        Text("\(Int(viewModel.getTotalScore()))")
                            .font(AppConstants.Typography.title3)
                            .fontWeight(.bold)
                            .foregroundColor(AppConstants.Colors.primary)
                        
                        Text("Tổng điểm")
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .trailing, spacing: 4) {
                        Text(viewModel.getEstimatedDuration())
                            .font(AppConstants.Typography.title3)
                            .fontWeight(.bold)
                            .foregroundColor(AppConstants.Colors.primary)
                        
                        Text("Thời gian")
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                }
            }
        }
    }
    
    // MARK: - Questions Preview Section
    private var questionsPreviewSection: some View {
        FormSectionView(
            title: "Câu hỏi (\(viewModel.questions.count))",
            subtitle: "Danh sách câu hỏi trong quiz"
        ) {
            if viewModel.questions.isEmpty {
                Text("Chưa có câu hỏi nào")
                    .font(AppConstants.Typography.body)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 20)
            } else {
                VStack(spacing: 12) {
                    ForEach(Array(viewModel.questions.enumerated()), id: \.offset) { index, question in
                        QuestionPreviewRow(question: question, index: index)
                    }
                }
            }
        }
    }
    
    // MARK: - Settings Summary Section
    private var settingsSummarySection: some View {
        FormSectionView(
            title: "Cài đặt",
            subtitle: "Cấu hình quiz"
        ) {
            VStack(spacing: 12) {
                // Scoring
                HStack {
                    Text("Điểm số:")
                        .font(AppConstants.Typography.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                    
                    Spacer()
                    
                    Text("\(Int(viewModel.settings.passingScore))/\(Int(viewModel.settings.maxScore)) điểm để đạt")
                        .font(AppConstants.Typography.subheadline)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                }
                
                Divider()
                
                // Time Limit
                HStack {
                    Text("Thời gian:")
                        .font(AppConstants.Typography.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                    
                    Spacer()
                    
                    Text(viewModel.settings.timeLimit != nil ? "\(viewModel.settings.timeLimit!) phút" : "Không giới hạn")
                        .font(AppConstants.Typography.subheadline)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                }
                
                Divider()
                
                // Max Attempts
                HStack {
                    Text("Số lần làm bài:")
                        .font(AppConstants.Typography.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                    
                    Spacer()
                    
                    Text("\(viewModel.settings.maxAttempts) lần")
                        .font(AppConstants.Typography.subheadline)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                }
                
                if viewModel.settings.isRandomized || viewModel.settings.showCorrectAnswers {
                    Divider()
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Tùy chọn:")
                            .font(AppConstants.Typography.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                        
                        VStack(alignment: .leading, spacing: 4) {
                            if viewModel.settings.isRandomized {
                                HStack {
                                    Image(systemName: "checkmark.circle.fill")
                                        .font(.caption)
                                        .foregroundColor(AppConstants.Colors.primary)
                                    
                                    Text("Xáo trộn câu hỏi")
                                        .font(AppConstants.Typography.caption)
                                        .foregroundColor(AppConstants.Colors.textPrimary)
                                }
                            }
                            
                            if viewModel.settings.showCorrectAnswers {
                                HStack {
                                    Image(systemName: "checkmark.circle.fill")
                                        .font(.caption)
                                        .foregroundColor(AppConstants.Colors.primary)
                                    
                                    Text("Hiển thị đáp án đúng")
                                        .font(AppConstants.Typography.caption)
                                        .foregroundColor(AppConstants.Colors.textPrimary)
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

// MARK: - Question Preview Row
struct QuestionPreviewRow: View {
    let question: QuestionBuilder
    let index: Int
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            // Question Number
            Text("\(index + 1)")
                .font(AppConstants.Typography.subheadline)
                .fontWeight(.bold)
                .foregroundColor(AppConstants.Colors.primary)
                .frame(width: 24, height: 24)
                .background(AppConstants.Colors.primary.opacity(0.1))
                .cornerRadius(12)
            
            // Question Content
            VStack(alignment: .leading, spacing: 8) {
                // Question Text
                Text(question.text.isEmpty ? "Chưa có nội dung" : question.text)
                    .font(AppConstants.Typography.body)
                    .foregroundColor(question.text.isEmpty ? AppConstants.Colors.textSecondary : AppConstants.Colors.textPrimary)
                    .lineLimit(3)
                
                // Question Info
                HStack {
                    HStack(spacing: 4) {
                        Image(systemName: question.questionType.icon)
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.primary)
                        
                        Text(question.questionType.displayName)
                            .font(AppConstants.Typography.caption)
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    
                    Spacer()
                    
                    Text("\(Int(question.score)) điểm")
                        .font(AppConstants.Typography.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
            }
        }
        .padding(.vertical, 8)
    }
}

// MARK: - Preview
struct PreviewStepView_Previews: PreviewProvider {
    static var previews: some View {
        PreviewStepView(viewModel: QuizCreationViewModel())
            .padding()
            .background(AppConstants.Colors.background)
    }
}
