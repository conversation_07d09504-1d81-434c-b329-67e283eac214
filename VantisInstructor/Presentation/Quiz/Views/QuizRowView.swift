//
//  QuizRowView.swift
//  mobile-app-template
//
//  Created by Inst<PERSON>ctor <PERSON><PERSON> on 24/7/25.
//

import SwiftUI

struct QuizRowView: View {
    let quiz: Quiz
    @ObservedObject var viewModel: QuizManagementViewModel
    @State private var showingMarkReady = false
    
    var body: some View {
        VStack(spacing: 0) {
            // Main content
            VStack(alignment: .leading, spacing: 16) {
                // Header with title and status
                HStack(alignment: .top, spacing: 12) {
                    VStack(alignment: .leading, spacing: 6) {
                        Text(quiz.name)
                            .font(.beVietnamPro(.semiBold, size: 18))
                            .foregroundColor(AppConstants.Colors.textPrimary)
                            .lineLimit(2)
                            .multilineTextAlignment(.leading)

                        Text(quiz.code)
                            .font(.beVietnamPro(.medium, size: 12))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 2)
                            .background(AppConstants.Colors.textSecondary.opacity(0.1))
                            .cornerRadius(4)
                    }

                    Spacer()

                    QuizStatusBadge(state: quiz.state)
                }

                // Quiz type and subject info in a clean row
                HStack(spacing: 16) {
                    HStack(spacing: 6) {
                        Image(systemName: quiz.quizType.icon)
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(colorForQuizType(quiz.quizType))

                        Text(quiz.quizType.displayName)
                            .font(.beVietnamPro(.medium, size: 14))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }

                    if let subjectName = quiz.subjectName {
                        HStack(spacing: 6) {
                            Image(systemName: "book.fill")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(AppConstants.Colors.textSecondary)

                            Text(subjectName)
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                        }
                    }

                    Spacer()
                }

                // Stats in a grid layout
                HStack(spacing: 0) {
                    ModernQuizStatItem(
                        icon: "questionmark.circle.fill",
                        value: "\(quiz.questionCount)",
                        label: "câu hỏi",
                        color: AppConstants.Colors.primary
                    )

                    ModernQuizStatItem(
                        icon: "person.2.fill",
                        value: "\(quiz.studentCount)",
                        label: "học sinh",
                        color: AppConstants.Colors.secondary
                    )

                    if quiz.attemptCount > 0 {
                        ModernQuizStatItem(
                            icon: "chart.bar.fill",
                            value: String(format: "%.1f%%", quiz.passRate),
                            label: "đạt",
                            color: AppConstants.Colors.success
                        )
                    }

                    if quiz.pendingGradingCount > 0 {
                        ModernQuizStatItem(
                            icon: "clock.fill",
                            value: "\(quiz.pendingGradingCount)",
                            label: "cần chấm",
                            color: AppConstants.Colors.warning
                        )
                    }

                    Spacer()
                }

                // Time info for published quizzes
                if quiz.state == .published {
                    QuizTimeInfoView(quiz: quiz)
                }

                // Description if available
                if let description = quiz.description, !description.isEmpty {
                    Text(description)
                        .font(.beVietnamPro(.medium, size: 14))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .lineLimit(2)
                        .padding(.top, 4)
                }
            }
            .padding(20)
            
            // Action buttons
            if quiz.state == .draft {
                Divider()
                
                HStack {
                    Button {
                        showingMarkReady = true
                    } label: {
                        HStack(spacing: 6) {
                            Image(systemName: "checkmark.circle")
                                .font(.caption)
                            Text("Đánh dấu sẵn sàng")
                                .font(AppConstants.Typography.caption)
                                .fontWeight(.medium)
                        }
                        .foregroundColor(AppConstants.Colors.primary)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(AppConstants.Colors.primary.opacity(0.1))
                        .cornerRadius(8)
                    }
                    .disabled(viewModel.isLoading)
                    
                    Spacer()
                    
                    Text("Nháp")
                        .font(AppConstants.Typography.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
                .padding(.horizontal, AppConstants.UI.cardPadding)
                .padding(.vertical, 12)
            }
        }
        .background(Color.white)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.04), radius: 8, x: 0, y: 4)
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(Color.black.opacity(0.06), lineWidth: 1)
        )
        .alert("Đánh dấu sẵn sàng", isPresented: $showingMarkReady) {
            Button("Hủy", role: .cancel) { }
            Button("Xác nhận") {
                Task {
                    await viewModel.markQuizReady(quizId: quiz.id)
                }
            }
        } message: {
            Text("Quiz sẽ được đánh dấu là sẵn sàng và có thể gán vào bài học. Bạn có chắc chắn?")
        }
    }
    
    private func colorForQuizType(_ type: QuizType) -> Color {
        switch type {
        case .quiz: return AppConstants.Colors.primary
        case .assignment: return AppConstants.Colors.success
        case .midterm: return AppConstants.Colors.warning
        case .final: return AppConstants.Colors.error
        case .project: return AppConstants.Colors.accent
        }
    }
}

// MARK: - Quiz Status Badge
struct QuizStatusBadge: View {
    let state: QuizState

    var body: some View {
        Text(state.displayName)
            .font(.beVietnamPro(.semiBold, size: 12))
            .foregroundColor(.white)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(colorForState(state))
            .cornerRadius(12)
            .shadow(color: colorForState(state).opacity(0.3), radius: 2, x: 0, y: 1)
    }

    private func colorForState(_ state: QuizState) -> Color {
        switch state {
        case .draft: return Color.gray
        case .ready: return AppConstants.Colors.primary
        case .published: return AppConstants.Colors.success
        case .archived: return AppConstants.Colors.warning
        }
    }
}

// MARK: - Modern Quiz Stat Item
struct ModernQuizStatItem: View {
    let icon: String
    let value: String
    let label: String
    let color: Color

    var body: some View {
        VStack(spacing: 6) {
            HStack(spacing: 4) {
                Image(systemName: icon)
                    .font(.system(size: 12, weight: .semibold))
                    .foregroundColor(color)

                Text(value)
                    .font(.beVietnamPro(.bold, size: 16))
                    .foregroundColor(AppConstants.Colors.textPrimary)
            }

            Text(label)
                .font(.beVietnamPro(.medium, size: 12))
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .frame(minWidth: 60)
    }
}

// MARK: - Quiz Stat Item (Legacy)
struct QuizStatItem: View {
    let icon: String
    let value: String
    let label: String
    var color: Color = AppConstants.Colors.textSecondary

    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: icon)
                .font(.caption2)
                .foregroundColor(color)

            VStack(alignment: .leading, spacing: 0) {
                Text(value)
                    .font(AppConstants.Typography.caption)
                    .fontWeight(.medium)
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Text(label)
                    .font(AppConstants.Typography.caption2)
                    .foregroundColor(color)
            }
        }
    }
}

// MARK: - Quiz Time Info
struct QuizTimeInfoView: View {
    let quiz: Quiz

    var body: some View {
        HStack(spacing: 8) {
            if quiz.isActive {
                HStack(spacing: 4) {
                    Image(systemName: "clock.fill")
                        .font(.system(size: 12, weight: .semibold))
                        .foregroundColor(AppConstants.Colors.success)

                    Text("Đang diễn ra")
                        .font(.beVietnamPro(.semiBold, size: 12))
                        .foregroundColor(AppConstants.Colors.success)
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(AppConstants.Colors.success.opacity(0.1))
                .cornerRadius(8)
            } else if quiz.isExpired {
                HStack(spacing: 4) {
                    Image(systemName: "clock.badge.xmark.fill")
                        .font(.system(size: 12, weight: .semibold))
                        .foregroundColor(AppConstants.Colors.error)

                    Text("Đã hết hạn")
                        .font(.beVietnamPro(.semiBold, size: 12))
                        .foregroundColor(AppConstants.Colors.error)
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(AppConstants.Colors.error.opacity(0.1))
                .cornerRadius(8)
            } else if let startDate = quiz.startDate {
                HStack(spacing: 4) {
                    Image(systemName: "calendar")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(AppConstants.Colors.textSecondary)

                    Text("Bắt đầu: \(startDate, style: .date)")
                        .font(.beVietnamPro(.medium, size: 12))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
            }

            Spacer()

            if !quiz.isExpired && quiz.endDate != nil {
                Text(quiz.timeRemainingText)
                    .font(.beVietnamPro(.medium, size: 12))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
            }
        }
    }
}

// MARK: - Preview
struct QuizRowView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 16) {
            QuizRowView(
                quiz: Quiz(
                    id: 1,
                    name: "Kiểm tra Toán học - Chương 1",
                    code: "QUIZ001",
                    description: "Kiểm tra kiến thức về đại số và hình học cơ bản",
                    quizType: .quiz,
                    subjectId: 1,
                    subjectName: "Toán học",
                    classId: 1,
                    className: "Lớp 10A1",
                    maxScore: 100,
                    passingScore: 70,
                    timeLimit: 60,
                    maxAttempts: 2,
                    isRandomized: true,
                    showCorrectAnswers: false,
                    state: .published,
                    questionCount: 10,
                    studentCount: 30,
                    attemptCount: 25,
                    averageScore: 78.5,
                    passRate: 80.0,
                    pendingGradingCount: 3,
                    startDate: Date().addingTimeInterval(-86400),
                    endDate: Date().addingTimeInterval(86400),
                    createdAt: Date().addingTimeInterval(-172800),
                    updatedAt: Date().addingTimeInterval(-86400)
                ),
                viewModel: QuizManagementViewModel()
            )
            
            QuizRowView(
                quiz: Quiz(
                    id: 2,
                    name: "Bài tập Văn học",
                    code: "QUIZ002",
                    description: nil,
                    quizType: .assignment,
                    subjectId: 2,
                    subjectName: "Văn học",
                    classId: 1,
                    className: "Lớp 10A1",
                    maxScore: 50,
                    passingScore: 30,
                    timeLimit: nil,
                    maxAttempts: 1,
                    isRandomized: false,
                    showCorrectAnswers: true,
                    state: .draft,
                    questionCount: 5,
                    studentCount: 30,
                    attemptCount: 0,
                    averageScore: 0,
                    passRate: 0,
                    pendingGradingCount: 0,
                    startDate: nil,
                    endDate: nil,
                    createdAt: Date().addingTimeInterval(-86400),
                    updatedAt: nil
                ),
                viewModel: QuizManagementViewModel()
            )
        }
        .padding()
        .background(AppConstants.Colors.background)
    }
}
