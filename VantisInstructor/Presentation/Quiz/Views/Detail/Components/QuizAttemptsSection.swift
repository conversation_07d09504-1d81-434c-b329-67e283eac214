//
//  QuizAttemptsSection.swift
//  mobile-app-template
//
//  Created by Inst<PERSON><PERSON> <PERSON>pp on 27/7/25.
//

import SwiftUI

struct QuizAttemptsSection: View {
    @ObservedObject var viewModel: QuizDetailViewModel
    
    var body: some View {
        VStack(spacing: AppConstants.UI.sectionSpacing) {
            if viewModel.isLoadingAttempts {
                loadingView
            } else if viewModel.attempts.isEmpty && viewModel.quiz.attemptCount == 0 {
                noAttemptsView
            } else {
                attemptsContent
            }
        }
    }
    
    // MARK: - Loading View
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text("Đang tải bài làm...")
                .font(AppConstants.Typography.body)
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 40)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
    }
    
    // MARK: - No Attempts View
    private var noAttemptsView: some View {
        VStack(spacing: 20) {
            Image(systemName: "doc.text")
                .font(.system(size: 60))
                .foregroundColor(AppConstants.Colors.textSecondary)
            
            VStack(spacing: 8) {
                Text("Chưa có bài làm")
                    .font(AppConstants.Typography.headline)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Text("Bài làm của học sinh sẽ xuất hiện ở đây sau khi họ hoàn thành quiz")
                    .font(AppConstants.Typography.body)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 40)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(AppConstants.Colors.border, lineWidth: 0.5)
        )
    }
    
    // MARK: - Attempts Content
    private var attemptsContent: some View {
        VStack(spacing: AppConstants.UI.sectionSpacing) {
            // Attempts Summary
            attemptsSummary
            
            // Attempts List
            attemptsList
        }
    }
    
    // MARK: - Attempts Summary
    private var attemptsSummary: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Tổng quan bài làm")
                .font(AppConstants.Typography.headline)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                SummaryCard(
                    title: "Tổng bài làm",
                    value: "\(viewModel.quiz.attemptCount)",
                    icon: "doc.text",
                    color: AppConstants.Colors.primary
                )
                
                SummaryCard(
                    title: "Chờ chấm điểm",
                    value: "\(viewModel.quiz.pendingGradingCount)",
                    icon: "clock",
                    color: AppConstants.Colors.warning
                )
                
                SummaryCard(
                    title: "Đã hoàn thành",
                    value: "\(viewModel.quiz.attemptCount - viewModel.quiz.pendingGradingCount)",
                    icon: "checkmark.circle",
                    color: AppConstants.Colors.success
                )
                
                SummaryCard(
                    title: "Tỷ lệ tham gia",
                    value: String(format: "%.1f%%", Double(viewModel.quiz.attemptCount) / Double(viewModel.quiz.studentCount) * 100),
                    icon: "person.2",
                    color: AppConstants.Colors.accent
                )
            }
        }
        .padding(AppConstants.UI.cardPadding)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(AppConstants.Colors.border, lineWidth: 0.5)
        )
    }
    
    // MARK: - Attempts List
    private var attemptsList: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Danh sách bài làm")
                    .font(AppConstants.Typography.headline)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Spacer()
                
                // Filter/Sort options could go here
                Button("Lọc") {
                    // TODO: Implement filtering
                }
                .font(AppConstants.Typography.caption)
                .foregroundColor(AppConstants.Colors.primary)
            }
            
            // Mock attempts list
            VStack(spacing: 12) {
                ForEach(1...min(viewModel.quiz.attemptCount, 10), id: \.self) { index in
                    AttemptRow(
                        studentName: mockStudentName(for: index),
                        submittedAt: mockSubmissionDate(for: index),
                        score: mockScore(for: index),
                        status: mockStatus(for: index),
                        timeSpent: mockTimeSpent(for: index),
                        passingScore: viewModel.quiz.passingScore
                    )
                }
                
                if viewModel.quiz.attemptCount > 10 {
                    Button("Xem thêm \(viewModel.quiz.attemptCount - 10) bài làm") {
                        // TODO: Load more attempts
                    }
                    .font(AppConstants.Typography.caption)
                    .foregroundColor(AppConstants.Colors.primary)
                    .frame(maxWidth: .infinity)
                    .padding(.top, 8)
                }
            }
        }
        .padding(AppConstants.UI.cardPadding)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(AppConstants.Colors.border, lineWidth: 0.5)
        )
    }
    
    // MARK: - Mock Data Helpers
    private func mockStudentName(for index: Int) -> String {
        let names = [
            "Nguyễn Văn An", "Trần Thị Bình", "Lê Hoàng Cường", "Phạm Thị Dung",
            "Hoàng Văn Em", "Vũ Thị Phương", "Đặng Minh Giang", "Bùi Thị Hoa",
            "Ngô Văn Inh", "Đinh Thị Kim"
        ]
        return names[(index - 1) % names.count]
    }
    
    private func mockSubmissionDate(for index: Int) -> Date {
        return Date().addingTimeInterval(-Double(index * 3600)) // Hours ago
    }
    
    private func mockScore(for index: Int) -> Double? {
        // Some attempts are still pending
        if index <= viewModel.quiz.pendingGradingCount {
            return nil
        }
        return Double.random(in: 45...95)
    }
    
    private func mockStatus(for index: Int) -> AttemptStatus {
        if index <= viewModel.quiz.pendingGradingCount {
            return .pending
        }
        return .completed
    }
    
    private func mockTimeSpent(for index: Int) -> Int {
        return Int.random(in: 1200...3600) // 20-60 minutes
    }
}

// MARK: - Supporting Views
struct SummaryCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text(value)
                .font(AppConstants.Typography.title3)
                .fontWeight(.bold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Text(title)
                .font(AppConstants.Typography.caption)
                .foregroundColor(AppConstants.Colors.textSecondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(color.opacity(0.1))
        .cornerRadius(12)
    }
}

struct AttemptRow: View {
    let studentName: String
    let submittedAt: Date
    let score: Double?
    let status: AttemptStatus
    let timeSpent: Int
    let passingScore: Double
    
    private var dateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        return formatter
    }
    
    var body: some View {
        HStack(spacing: 12) {
            // Student Avatar
            Circle()
                .fill(AppConstants.Colors.primary.opacity(0.2))
                .frame(width: 40, height: 40)
                .overlay(
                    Text(String(studentName.prefix(1)))
                        .font(AppConstants.Typography.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(AppConstants.Colors.primary)
                )
            
            // Student Info
            VStack(alignment: .leading, spacing: 4) {
                Text(studentName)
                    .font(AppConstants.Typography.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Text(dateFormatter.string(from: submittedAt))
                    .font(AppConstants.Typography.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            
            Spacer()
            
            // Score and Status
            VStack(alignment: .trailing, spacing: 4) {
                if let score = score {
                    Text(String(format: "%.1f", score))
                        .font(AppConstants.Typography.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(scoreColor(score))
                } else {
                    Text("Chờ chấm")
                        .font(AppConstants.Typography.caption)
                        .foregroundColor(AppConstants.Colors.warning)
                }
                
                Text(formatTimeSpent(timeSpent))
                    .font(AppConstants.Typography.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            
            // Status Badge
            AttemptStatusBadge(status: status)
        }
        .padding(12)
        .background(AppConstants.Colors.background)
        .cornerRadius(8)
    }
    
    private func scoreColor(_ score: Double) -> Color {
        if score >= passingScore {
            return AppConstants.Colors.success
        } else {
            return AppConstants.Colors.error
        }
    }
    
    private func formatTimeSpent(_ seconds: Int) -> String {
        let minutes = seconds / 60
        let hours = minutes / 60
        let remainingMinutes = minutes % 60
        
        if hours > 0 {
            return "\(hours)h \(remainingMinutes)m"
        } else {
            return "\(minutes)m"
        }
    }
}

struct AttemptStatusBadge: View {
    let status: AttemptStatus

    var body: some View {
        Text(status.displayName)
            .font(AppConstants.Typography.caption)
            .fontWeight(.medium)
            .foregroundColor(.white)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(status.color)
            .cornerRadius(8)
    }
}

// MARK: - Attempt Status Enum
enum AttemptStatus: String, CaseIterable {
    case pending = "pending"
    case completed = "completed"
    case inProgress = "in_progress"
    
    var displayName: String {
        switch self {
        case .pending: return "Chờ chấm"
        case .completed: return "Hoàn thành"
        case .inProgress: return "Đang làm"
        }
    }
    
    var color: Color {
        switch self {
        case .pending: return AppConstants.Colors.warning
        case .completed: return AppConstants.Colors.success
        case .inProgress: return AppConstants.Colors.primary
        }
    }
}

// MARK: - Preview
struct QuizAttemptsSection_Previews: PreviewProvider {
    static var previews: some View {
        QuizAttemptsSection(viewModel: QuizDetailViewModel(quiz: Quiz(
            id: 1,
            name: "Kiểm tra Toán học",
            code: "QUIZ001",
            description: "Kiểm tra chương 1: Đại số",
            quizType: .quiz,
            subjectId: 1,
            subjectName: "Toán học",
            classId: 1,
            className: "Lớp 10A1",
            maxScore: 100,
            passingScore: 70,
            timeLimit: 60,
            maxAttempts: 2,
            isRandomized: true,
            showCorrectAnswers: false,
            state: .published,
            questionCount: 10,
            studentCount: 30,
            attemptCount: 15,
            averageScore: 78.5,
            passRate: 80.0,
            pendingGradingCount: 3,
            startDate: Date().addingTimeInterval(-86400),
            endDate: Date().addingTimeInterval(86400),
            createdAt: Date().addingTimeInterval(-172800),
            updatedAt: Date().addingTimeInterval(-86400)
        )))
        .padding()
        .background(AppConstants.Colors.background)
    }
}
