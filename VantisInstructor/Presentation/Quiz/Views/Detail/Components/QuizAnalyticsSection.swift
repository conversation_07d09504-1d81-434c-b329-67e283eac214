//
//  QuizAnalyticsSection.swift
//  mobile-app-template
//
//  Created by Inst<PERSON><PERSON> <PERSON>pp on 27/7/25.
//

import SwiftUI

struct QuizAnalyticsSection: View {
    @ObservedObject var viewModel: QuizDetailViewModel
    
    var body: some View {
        VStack(spacing: AppConstants.UI.sectionSpacing) {
            if viewModel.isLoadingAnalytics {
                loadingView
            } else if !viewModel.hasAnalytics {
                noAnalyticsView
            } else {
                analyticsContent
            }
        }
    }
    
    // MARK: - Loading View
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text("Đang tải thống kê...")
                .font(AppConstants.Typography.body)
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 40)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
    }
    
    // MARK: - No Analytics View
    private var noAnalyticsView: some View {
        VStack(spacing: 20) {
            Image(systemName: "chart.bar")
                .font(.system(size: 60))
                .foregroundColor(AppConstants.Colors.textSecondary)
            
            VStack(spacing: 8) {
                Text("Chưa có dữ liệu thống kê")
                    .font(AppConstants.Typography.headline)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Text("Thống kê sẽ xuất hiện sau khi có học sinh làm bài")
                    .font(AppConstants.Typography.body)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 40)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(AppConstants.Colors.border, lineWidth: 0.5)
        )
    }
    
    // MARK: - Analytics Content
    private var analyticsContent: some View {
        VStack(spacing: AppConstants.UI.sectionSpacing) {
            // Performance Overview
            performanceOverviewCard
            
            // Score Distribution
            scoreDistributionCard
            
            // Attempts Timeline
            attemptsTimelineCard
            
            // Question Analytics
            questionAnalyticsCard
        }
    }
    
    // MARK: - Performance Overview Card
    private var performanceOverviewCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Tổng quan hiệu suất")
                .font(AppConstants.Typography.headline)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                PerformanceStatCard(
                    title: "Tỷ lệ hoàn thành",
                    value: viewModel.formatPercentage(viewModel.completionRate),
                    icon: "checkmark.circle",
                    color: AppConstants.Colors.success
                )
                
                PerformanceStatCard(
                    title: "Điểm trung bình",
                    value: viewModel.formatScore(viewModel.averageScore),
                    icon: "chart.bar",
                    color: AppConstants.Colors.primary
                )
                
                PerformanceStatCard(
                    title: "Tỷ lệ đạt",
                    value: viewModel.formatPercentage(viewModel.passRate),
                    icon: "trophy",
                    color: AppConstants.Colors.accent
                )
                
                PerformanceStatCard(
                    title: "Thời gian TB",
                    value: viewModel.formatTimeSpent(viewModel.analytics?.averageTimeSpent ?? 0),
                    icon: "clock",
                    color: AppConstants.Colors.secondary
                )
            }
        }
        .padding(AppConstants.UI.cardPadding)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(AppConstants.Colors.border, lineWidth: 0.5)
        )
    }
    
    // MARK: - Score Distribution Card
    private var scoreDistributionCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Phân bố điểm số")
                .font(AppConstants.Typography.headline)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            let scoreData = viewModel.getScoreDistributionData()
            
            if scoreData.isEmpty {
                Text("Chưa có dữ liệu phân bố điểm")
                    .font(AppConstants.Typography.body)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 20)
            } else {
                VStack(spacing: 12) {
                    ForEach(scoreData, id: \.id) { range in
                        ScoreDistributionRow(
                            range: range.range,
                            count: range.count,
                            percentage: range.percentage
                        )
                    }
                }
            }
        }
        .padding(AppConstants.UI.cardPadding)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(AppConstants.Colors.border, lineWidth: 0.5)
        )
    }
    
    // MARK: - Attempts Timeline Card
    private var attemptsTimelineCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Lượt làm bài theo thời gian")
                .font(AppConstants.Typography.headline)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            let attemptsData = viewModel.getAttemptsByDateData()
            
            if attemptsData.isEmpty {
                Text("Chưa có dữ liệu lượt làm bài")
                    .font(AppConstants.Typography.body)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 20)
            } else {
                VStack(spacing: 8) {
                    ForEach(attemptsData, id: \.id) { attempt in
                        AttemptTimelineRow(
                            date: attempt.date,
                            count: attempt.count
                        )
                    }
                }
            }
        }
        .padding(AppConstants.UI.cardPadding)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(AppConstants.Colors.border, lineWidth: 0.5)
        )
    }
    
    // MARK: - Question Analytics Card
    private var questionAnalyticsCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Phân tích câu hỏi")
                    .font(AppConstants.Typography.headline)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Spacer()
                
                Button("Xem chi tiết") {
                    viewModel.selectTab(.questions)
                }
                .font(AppConstants.Typography.caption)
                .foregroundColor(AppConstants.Colors.primary)
            }
            
            let questionData = viewModel.getQuestionAnalyticsData()
            
            if questionData.isEmpty {
                Text("Chưa có dữ liệu phân tích câu hỏi")
                    .font(AppConstants.Typography.body)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 20)
            } else {
                VStack(spacing: 12) {
                    ForEach(Array(questionData.prefix(3).enumerated()), id: \.offset) { index, question in
                        QuestionAnalyticsRow(
                            questionNumber: index + 1,
                            questionText: question.questionText,
                            correctRate: Double(question.correctAnswers) / Double(question.totalAnswers) * 100,
                            difficulty: question.difficulty
                        )
                    }
                    
                    if questionData.count > 3 {
                        Button("Xem tất cả \(questionData.count) câu hỏi") {
                            viewModel.selectTab(.questions)
                        }
                        .font(AppConstants.Typography.caption)
                        .foregroundColor(AppConstants.Colors.primary)
                        .frame(maxWidth: .infinity)
                        .padding(.top, 8)
                    }
                }
            }
        }
        .padding(AppConstants.UI.cardPadding)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(AppConstants.Colors.border, lineWidth: 0.5)
        )
    }
}

// MARK: - Supporting Views
struct PerformanceStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text(value)
                .font(AppConstants.Typography.title3)
                .fontWeight(.bold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Text(title)
                .font(AppConstants.Typography.caption)
                .foregroundColor(AppConstants.Colors.textSecondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(color.opacity(0.1))
        .cornerRadius(12)
    }
}

struct ScoreDistributionRow: View {
    let range: String
    let count: Int
    let percentage: Double
    
    var body: some View {
        HStack {
            Text(range)
                .font(AppConstants.Typography.subheadline)
                .fontWeight(.medium)
                .foregroundColor(AppConstants.Colors.textPrimary)
                .frame(width: 60, alignment: .leading)
            
            // Progress Bar
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(AppConstants.Colors.border)
                        .frame(height: 8)
                        .cornerRadius(4)
                    
                    Rectangle()
                        .fill(AppConstants.Colors.primary)
                        .frame(width: geometry.size.width * (percentage / 100), height: 8)
                        .cornerRadius(4)
                }
            }
            .frame(height: 8)
            
            VStack(alignment: .trailing, spacing: 2) {
                Text("\(count)")
                    .font(AppConstants.Typography.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Text(String(format: "%.1f%%", percentage))
                    .font(AppConstants.Typography.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            .frame(width: 40)
        }
    }
}

struct AttemptTimelineRow: View {
    let date: Date
    let count: Int
    
    private var dateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateFormat = "dd/MM"
        return formatter
    }
    
    var body: some View {
        HStack {
            Text(dateFormatter.string(from: date))
                .font(AppConstants.Typography.caption)
                .foregroundColor(AppConstants.Colors.textSecondary)
                .frame(width: 40, alignment: .leading)
            
            // Bar Chart
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(AppConstants.Colors.border)
                        .frame(height: 6)
                        .cornerRadius(3)
                    
                    if count > 0 {
                        Rectangle()
                            .fill(AppConstants.Colors.accent)
                            .frame(width: max(8, geometry.size.width * (Double(count) / 5.0)), height: 6)
                            .cornerRadius(3)
                    }
                }
            }
            .frame(height: 6)
            
            Text("\(count)")
                .font(AppConstants.Typography.caption)
                .fontWeight(.medium)
                .foregroundColor(AppConstants.Colors.textPrimary)
                .frame(width: 20, alignment: .trailing)
        }
    }
}

struct QuestionAnalyticsRow: View {
    let questionNumber: Int
    let questionText: String
    let correctRate: Double
    let difficulty: QuestionDifficulty
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("Câu \(questionNumber)")
                    .font(AppConstants.Typography.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(AppConstants.Colors.primary)
                
                Spacer()
                
                DifficultyBadge(difficulty: difficulty)
            }
            
            Text(questionText)
                .font(AppConstants.Typography.body)
                .foregroundColor(AppConstants.Colors.textPrimary)
                .lineLimit(2)
            
            HStack {
                Text("Tỷ lệ đúng:")
                    .font(AppConstants.Typography.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                
                Spacer()
                
                Text(String(format: "%.1f%%", correctRate))
                    .font(AppConstants.Typography.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(correctRateColor)
            }
        }
        .padding(12)
        .background(AppConstants.Colors.background)
        .cornerRadius(8)
    }
    
    private var correctRateColor: Color {
        if correctRate >= 80 {
            return AppConstants.Colors.success
        } else if correctRate >= 60 {
            return AppConstants.Colors.warning
        } else {
            return AppConstants.Colors.error
        }
    }
}

struct DifficultyBadge: View {
    let difficulty: QuestionDifficulty
    
    var body: some View {
        Text(difficulty.displayName)
            .font(AppConstants.Typography.caption)
            .fontWeight(.medium)
            .foregroundColor(.white)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(Color(difficulty.color))
            .cornerRadius(8)
    }
}

// MARK: - Preview
struct QuizAnalyticsSection_Previews: PreviewProvider {
    static var previews: some View {
        QuizAnalyticsSection(viewModel: QuizDetailViewModel(quiz: Quiz(
            id: 1,
            name: "Kiểm tra Toán học",
            code: "QUIZ001",
            description: "Kiểm tra chương 1: Đại số",
            quizType: .quiz,
            subjectId: 1,
            subjectName: "Toán học",
            classId: 1,
            className: "Lớp 10A1",
            maxScore: 100,
            passingScore: 70,
            timeLimit: 60,
            maxAttempts: 2,
            isRandomized: true,
            showCorrectAnswers: false,
            state: .published,
            questionCount: 10,
            studentCount: 30,
            attemptCount: 25,
            averageScore: 78.5,
            passRate: 80.0,
            pendingGradingCount: 3,
            startDate: Date().addingTimeInterval(-86400),
            endDate: Date().addingTimeInterval(86400),
            createdAt: Date().addingTimeInterval(-172800),
            updatedAt: Date().addingTimeInterval(-86400)
        )))
        .padding()
        .background(AppConstants.Colors.background)
    }
}
