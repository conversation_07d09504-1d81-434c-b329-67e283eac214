//
//  PublicationStatusView.swift
//  mobile-app-template
//
//  Created by Instructor App on 27/7/25.
//

import SwiftUI

struct PublicationStatusView: View {
    @ObservedObject var viewModel: QuizDetailViewModel
    
    var body: some View {
        VStack(spacing: 16) {
            // Current Status
            currentStatusSection
            
            // Publication Actions
            if viewModel.quiz.state == .ready || viewModel.quiz.state == .published {
                publicationActionsSection
            }
            
            // Publication History (if published)
            if viewModel.quiz.state == .published {
                publicationHistorySection
            }
        }
        .padding()
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(AppConstants.Colors.border, lineWidth: 0.5)
        )
    }
    
    // MARK: - Current Status Section
    private var currentStatusSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Trạng thái phát hành")
                .font(AppConstants.Typography.headline)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            HStack(spacing: 12) {
                // Status Icon
                Image(systemName: statusIcon)
                    .font(.title2)
                    .foregroundColor(Color(statusColor))
                    .frame(width: 40, height: 40)
                    .background(Color(statusColor).opacity(0.1))
                    .cornerRadius(20)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(statusTitle)
                        .font(AppConstants.Typography.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    Text(statusDescription)
                        .font(AppConstants.Typography.body)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
                
                Spacer()
            }
        }
    }
    
    // MARK: - Publication Actions Section
    private var publicationActionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Hành động")
                .font(AppConstants.Typography.headline)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            VStack(spacing: 12) {
                if viewModel.quiz.state == .ready {
                    // Publish Actions
                    PublicationActionCard(
                        icon: "paperplane",
                        title: "Phát hành Quiz",
                        description: "Học sinh có thể thấy và làm bài quiz",
                        actionTitle: "Phát hành",
                        actionColor: AppConstants.Colors.success,
                        isLoading: viewModel.isPerformingAction,
                        action: {
                            Task {
                                await viewModel.performAction(.publish)
                            }
                        }
                    )
                    
                    PublicationActionCard(
                        icon: "plus.circle",
                        title: "Gán vào Bài học",
                        description: "Gán quiz vào bài học cụ thể trước khi phát hành",
                        actionTitle: "Gán",
                        actionColor: AppConstants.Colors.primary,
                        isLoading: false,
                        action: {
                            viewModel.showingAssignment = true
                        }
                    )
                } else if viewModel.quiz.state == .published {
                    // Unpublish Action
                    PublicationActionCard(
                        icon: "paperplane.fill",
                        title: "Hủy phát hành",
                        description: "Học sinh sẽ không thể làm bài quiz nữa",
                        actionTitle: "Hủy phát hành",
                        actionColor: AppConstants.Colors.warning,
                        isLoading: viewModel.isPerformingAction,
                        action: {
                            Task {
                                await viewModel.performAction(.unpublish)
                            }
                        }
                    )
                }
            }
        }
    }
    
    // MARK: - Publication History Section
    private var publicationHistorySection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Lịch sử phát hành")
                .font(AppConstants.Typography.headline)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            VStack(spacing: 12) {
                if let startDate = viewModel.quiz.startDate {
                    PublicationHistoryItem(
                        icon: "play.circle",
                        title: "Bắt đầu phát hành",
                        date: startDate,
                        status: .completed
                    )
                }
                
                if let endDate = viewModel.quiz.endDate {
                    PublicationHistoryItem(
                        icon: "stop.circle",
                        title: "Kết thúc phát hành",
                        date: endDate,
                        status: endDate > Date() ? .scheduled : .completed
                    )
                }
                
                // Current stats
                PublicationStatsRow(
                    icon: "person.2",
                    title: "Học sinh tham gia",
                    value: "\(viewModel.quiz.attemptCount)/\(viewModel.quiz.studentCount)"
                )
                
                if viewModel.quiz.attemptCount > 0 {
                    PublicationStatsRow(
                        icon: "chart.bar",
                        title: "Điểm trung bình",
                        value: String(format: "%.1f", viewModel.quiz.averageScore)
                    )
                    
                    PublicationStatsRow(
                        icon: "checkmark.circle",
                        title: "Tỷ lệ đạt",
                        value: String(format: "%.1f%%", viewModel.quiz.passRate)
                    )
                }
            }
        }
    }
    
    // MARK: - Computed Properties
    private var statusIcon: String {
        switch viewModel.quiz.state {
        case .draft: return "doc.text"
        case .ready: return "checkmark.circle"
        case .published: return "paperplane.fill"
        case .archived: return "archivebox"
        }
    }
    
    private var statusColor: String {
        switch viewModel.quiz.state {
        case .draft: return "gray"
        case .ready: return "blue"
        case .published: return "green"
        case .archived: return "orange"
        }
    }
    
    private var statusTitle: String {
        switch viewModel.quiz.state {
        case .draft: return "Bản nháp"
        case .ready: return "Sẵn sàng phát hành"
        case .published: return "Đã phát hành"
        case .archived: return "Đã lưu trữ"
        }
    }
    
    private var statusDescription: String {
        switch viewModel.quiz.state {
        case .draft: return "Quiz đang được chỉnh sửa"
        case .ready: return "Quiz sẵn sàng để phát hành cho học sinh"
        case .published: return "Học sinh có thể thấy và làm bài quiz"
        case .archived: return "Quiz đã được lưu trữ"
        }
    }
}

// MARK: - Supporting Views
struct PublicationActionCard: View {
    let icon: String
    let title: String
    let description: String
    let actionTitle: String
    let actionColor: Color
    let isLoading: Bool
    let action: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(actionColor)
                .frame(width: 32, height: 32)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(AppConstants.Typography.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Text(description)
                    .font(AppConstants.Typography.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            
            Spacer()
            
            Button(action: action) {
                if isLoading {
                    ProgressView()
                        .scaleEffect(0.8)
                } else {
                    Text(actionTitle)
                        .font(AppConstants.Typography.caption)
                        .fontWeight(.semibold)
                }
            }
            .foregroundColor(.white)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(actionColor)
            .cornerRadius(8)
            .disabled(isLoading)
        }
        .padding()
        .background(AppConstants.Colors.background)
        .cornerRadius(8)
    }
}

struct PublicationHistoryItem: View {
    let icon: String
    let title: String
    let date: Date
    let status: PublicationHistoryStatus
    
    private var dateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "vi_VN")
        return formatter
    }
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.body)
                .foregroundColor(status.color)
                .frame(width: 24, height: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(AppConstants.Typography.body)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Text(dateFormatter.string(from: date))
                    .font(AppConstants.Typography.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            
            Spacer()
            
            Text(status.displayName)
                .font(AppConstants.Typography.caption)
                .fontWeight(.medium)
                .foregroundColor(status.color)
        }
    }
}

struct PublicationStatsRow: View {
    let icon: String
    let title: String
    let value: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.body)
                .foregroundColor(AppConstants.Colors.textSecondary)
                .frame(width: 24, height: 24)
            
            Text(title)
                .font(AppConstants.Typography.body)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Spacer()
            
            Text(value)
                .font(AppConstants.Typography.body)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.primary)
        }
    }
}

// MARK: - Publication History Status
enum PublicationHistoryStatus {
    case scheduled
    case completed
    case cancelled
    
    var displayName: String {
        switch self {
        case .scheduled: return "Đã lên lịch"
        case .completed: return "Hoàn thành"
        case .cancelled: return "Đã hủy"
        }
    }
    
    var color: Color {
        switch self {
        case .scheduled: return AppConstants.Colors.primary
        case .completed: return AppConstants.Colors.success
        case .cancelled: return AppConstants.Colors.error
        }
    }
}

// MARK: - Preview
struct PublicationStatusView_Previews: PreviewProvider {
    static var previews: some View {
        PublicationStatusView(viewModel: QuizDetailViewModel(quiz: Quiz(
            id: 1,
            name: "Kiểm tra Toán học",
            code: "QUIZ001",
            description: "Kiểm tra chương 1: Đại số",
            quizType: .quiz,
            subjectId: 1,
            subjectName: "Toán học",
            classId: 1,
            className: "Lớp 10A1",
            maxScore: 100,
            passingScore: 70,
            timeLimit: 60,
            maxAttempts: 2,
            isRandomized: true,
            showCorrectAnswers: false,
            state: .ready,
            questionCount: 10,
            studentCount: 30,
            attemptCount: 0,
            averageScore: 0,
            passRate: 0,
            pendingGradingCount: 0,
            startDate: nil,
            endDate: nil,
            createdAt: Date(),
            updatedAt: nil
        )))
        .padding()
        .background(AppConstants.Colors.background)
    }
}
