//
//  QuizOverviewSection.swift
//  mobile-app-template
//
//  Created by In<PERSON><PERSON><PERSON> <PERSON><PERSON> on 27/7/25.
//

import SwiftUI

struct QuizOverviewSection: View {
    @ObservedObject var viewModel: QuizDetailViewModel
    
    var body: some View {
        VStack(spacing: AppConstants.UI.sectionSpacing) {
            // Quiz Status Card
            quizStatusCard
            
            // Quick Stats
            quickStatsSection
            
            // Quiz Information
            quizInformationSection
            
            // Settings Overview
            settingsOverviewSection

            // Publication Status
            PublicationStatusView(viewModel: viewModel)

            // Recent Activity (if available)
            if viewModel.quiz.attemptCount > 0 {
                recentActivitySection
            }
        }
    }
    
    // MARK: - Quiz Status Card
    private var quizStatusCard: some View {
        VStack(spacing: 16) {
            // Status Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Trạng thái Quiz")
                        .font(AppConstants.Typography.headline)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    Text("Cập nhật lần cuối: \(viewModel.formatDate(viewModel.quiz.updatedAt ?? viewModel.quiz.createdAt ?? Date()))")
                        .font(AppConstants.Typography.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
                
                Spacer()
                
                // Status Badge
                HStack(spacing: 6) {
                    Image(systemName: viewModel.statusIcon)
                        .font(.caption)
                        .foregroundColor(.white)
                    
                    Text(viewModel.quiz.state.displayName)
                        .font(AppConstants.Typography.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color(viewModel.statusColor))
                .cornerRadius(16)
            }
            
            // Status Description
            VStack(alignment: .leading, spacing: 8) {
                Text(statusDescription)
                    .font(AppConstants.Typography.body)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                if !statusActions.isEmpty {
                    Text("Hành động có thể thực hiện:")
                        .font(AppConstants.Typography.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    VStack(alignment: .leading, spacing: 4) {
                        ForEach(statusActions, id: \.self) { action in
                            HStack {
                                Image(systemName: "checkmark.circle.fill")
                                    .font(.caption)
                                    .foregroundColor(AppConstants.Colors.primary)
                                
                                Text(action)
                                    .font(AppConstants.Typography.caption)
                                    .foregroundColor(AppConstants.Colors.textSecondary)
                            }
                        }
                    }
                }
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(AppConstants.UI.cardPadding)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(AppConstants.Colors.border, lineWidth: 0.5)
        )
    }
    
    private var statusDescription: String {
        switch viewModel.quiz.state {
        case .draft:
            return "Quiz đang ở trạng thái nháp. Bạn có thể chỉnh sửa và thêm câu hỏi."
        case .ready:
            return "Quiz đã sẵn sàng để phát hành. Tất cả câu hỏi đã được kiểm tra."
        case .published:
            return "Quiz đã được phát hành và học sinh có thể làm bài."
        case .archived:
            return "Quiz đã được lưu trữ và không còn hoạt động."
        }
    }
    
    private var statusActions: [String] {
        return viewModel.availableActions.map { $0.rawValue }
    }
    
    // MARK: - Quick Stats Section
    private var quickStatsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Thống kê tổng quan")
                .font(AppConstants.Typography.headline)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                QuizStatCard(
                    title: "Câu hỏi",
                    value: "\(viewModel.quiz.questionCount)",
                    icon: "questionmark.circle",
                    color: AppConstants.Colors.primary
                )

                QuizStatCard(
                    title: "Học sinh",
                    value: "\(viewModel.quiz.studentCount)",
                    icon: "person.2",
                    color: AppConstants.Colors.secondary
                )
                
                if viewModel.quiz.attemptCount > 0 {
                    QuizStatCard(
                        title: "Lượt làm bài",
                        value: "\(viewModel.quiz.attemptCount)",
                        icon: "doc.text",
                        color: AppConstants.Colors.accent
                    )

                    QuizStatCard(
                        title: "Điểm trung bình",
                        value: viewModel.formatScore(viewModel.averageScore),
                        icon: "chart.bar",
                        color: AppConstants.Colors.success
                    )
                }
            }
        }
        .padding(AppConstants.UI.cardPadding)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(AppConstants.Colors.border, lineWidth: 0.5)
        )
    }
    
    // MARK: - Quiz Information Section
    private var quizInformationSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Thông tin Quiz")
                .font(AppConstants.Typography.headline)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            VStack(spacing: 12) {
                QuizInfoRow(label: "Mã Quiz", value: viewModel.quiz.code)

                if let description = viewModel.quiz.description, !description.isEmpty {
                    QuizInfoRow(label: "Mô tả", value: description, isMultiline: true)
                }

                QuizInfoRow(label: "Loại", value: viewModel.quiz.quizType.displayName)

                if let subjectName = viewModel.quiz.subjectName {
                    QuizInfoRow(label: "Môn học", value: subjectName)
                }

                if let className = viewModel.quiz.className {
                    QuizInfoRow(label: "Lớp", value: className)
                }

                QuizInfoRow(label: "Ngày tạo", value: viewModel.formatDate(viewModel.quiz.createdAt ?? Date()))
            }
        }
        .padding(AppConstants.UI.cardPadding)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(AppConstants.Colors.border, lineWidth: 0.5)
        )
    }
    
    // MARK: - Settings Overview Section
    private var settingsOverviewSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Cài đặt")
                .font(AppConstants.Typography.headline)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            VStack(spacing: 12) {
                QuizInfoRow(
                    label: "Điểm số",
                    value: "\(Int(viewModel.quiz.passingScore))/\(Int(viewModel.quiz.maxScore)) điểm để đạt"
                )

                QuizInfoRow(
                    label: "Thời gian",
                    value: viewModel.quiz.timeLimit != nil ? "\(viewModel.quiz.timeLimit!) phút" : "Không giới hạn"
                )

                QuizInfoRow(
                    label: "Số lần làm bài",
                    value: "\(viewModel.quiz.maxAttempts ?? 1) lần"
                )
                
                if (viewModel.quiz.isRandomized ?? false) || (viewModel.quiz.showCorrectAnswers ?? false) {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Tùy chọn:")
                            .font(AppConstants.Typography.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        
                        VStack(alignment: .leading, spacing: 4) {
                            if viewModel.quiz.isRandomized ?? false {
                                FeatureRow(text: "Xáo trộn câu hỏi", isEnabled: true)
                            }
                            
                            if viewModel.quiz.showCorrectAnswers ?? false {
                                FeatureRow(text: "Hiển thị đáp án đúng", isEnabled: true)
                            }
                        }
                    }
                }
            }
        }
        .padding(AppConstants.UI.cardPadding)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(AppConstants.Colors.border, lineWidth: 0.5)
        )
    }
    
    // MARK: - Recent Activity Section
    private var recentActivitySection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Hoạt động gần đây")
                    .font(AppConstants.Typography.headline)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Spacer()
                
                Button("Xem tất cả") {
                    viewModel.selectTab(.attempts)
                }
                .font(AppConstants.Typography.caption)
                .foregroundColor(AppConstants.Colors.primary)
            }
            
            VStack(spacing: 12) {
                if viewModel.quiz.pendingGradingCount > 0 {
                    ActivityRow(
                        icon: "clock",
                        title: "Chờ chấm điểm",
                        subtitle: "\(viewModel.quiz.pendingGradingCount) bài làm",
                        color: AppConstants.Colors.warning
                    )
                }
                
                ActivityRow(
                    icon: "chart.line.uptrend.xyaxis",
                    title: "Tỷ lệ đạt",
                    subtitle: viewModel.formatPercentage(viewModel.passRate),
                    color: AppConstants.Colors.success
                )
                
                ActivityRow(
                    icon: "person.2.fill",
                    title: "Đã tham gia",
                    subtitle: "\(viewModel.quiz.attemptCount)/\(viewModel.quiz.studentCount) học sinh",
                    color: AppConstants.Colors.primary
                )
            }
        }
        .padding(AppConstants.UI.cardPadding)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(AppConstants.Colors.border, lineWidth: 0.5)
        )
    }
}

// MARK: - Supporting Views
struct QuizStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text(value)
                .font(AppConstants.Typography.title3)
                .fontWeight(.bold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Text(title)
                .font(AppConstants.Typography.caption)
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(color.opacity(0.1))
        .cornerRadius(12)
    }
}

struct QuizInfoRow: View {
    let label: String
    let value: String
    let isMultiline: Bool
    
    init(label: String, value: String, isMultiline: Bool = false) {
        self.label = label
        self.value = value
        self.isMultiline = isMultiline
    }
    
    var body: some View {
        if isMultiline {
            VStack(alignment: .leading, spacing: 4) {
                Text(label)
                    .font(AppConstants.Typography.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                
                Text(value)
                    .font(AppConstants.Typography.body)
                    .foregroundColor(AppConstants.Colors.textPrimary)
            }
        } else {
            HStack {
                Text(label)
                    .font(AppConstants.Typography.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                
                Spacer()
                
                Text(value)
                    .font(AppConstants.Typography.body)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                    .multilineTextAlignment(.trailing)
            }
        }
    }
}

struct FeatureRow: View {
    let text: String
    let isEnabled: Bool
    
    var body: some View {
        HStack {
            Image(systemName: isEnabled ? "checkmark.circle.fill" : "xmark.circle.fill")
                .font(.caption)
                .foregroundColor(isEnabled ? AppConstants.Colors.success : AppConstants.Colors.error)
            
            Text(text)
                .font(AppConstants.Typography.caption)
                .foregroundColor(AppConstants.Colors.textPrimary)
        }
    }
}

struct ActivityRow: View {
    let icon: String
    let title: String
    let subtitle: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color)
                .frame(width: 24, height: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(AppConstants.Typography.subheadline)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Text(subtitle)
                    .font(AppConstants.Typography.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            
            Spacer()
        }
    }
}

// MARK: - Preview
struct QuizOverviewSection_Previews: PreviewProvider {
    static var previews: some View {
        QuizOverviewSection(viewModel: QuizDetailViewModel(quiz: Quiz(
            id: 1,
            name: "Kiểm tra Toán học",
            code: "QUIZ001",
            description: "Kiểm tra chương 1: Đại số",
            quizType: .quiz,
            subjectId: 1,
            subjectName: "Toán học",
            classId: 1,
            className: "Lớp 10A1",
            maxScore: 100,
            passingScore: 70,
            timeLimit: 60,
            maxAttempts: 2,
            isRandomized: true,
            showCorrectAnswers: false,
            state: .published,
            questionCount: 10,
            studentCount: 30,
            attemptCount: 25,
            averageScore: 78.5,
            passRate: 80.0,
            pendingGradingCount: 3,
            startDate: Date().addingTimeInterval(-86400),
            endDate: Date().addingTimeInterval(86400),
            createdAt: Date().addingTimeInterval(-172800),
            updatedAt: Date().addingTimeInterval(-86400)
        )))
        .padding()
        .background(AppConstants.Colors.background)
    }
}
