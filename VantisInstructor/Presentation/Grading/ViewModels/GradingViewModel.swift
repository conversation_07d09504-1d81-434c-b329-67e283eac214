//
//  GradingViewModel.swift
//  mobile-app-template
//
//  Created by Instructor App on 24/7/25.
//

import Foundation
import SwiftUI

@MainActor
class GradingViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var pendingAttempts: [QuizAttempt] = []
    @Published var currentAttempt: AttemptDetail?
    @Published var isLoading = false
    @Published var isRefreshing = false
    @Published var errorMessage: String?
    @Published var showingError = false
    @Published var currentPage = 1
    @Published var hasMorePages = true
    @Published var totalCount = 0
    
    // MARK: - Grading State
    @Published var gradingScores: [Int: Double] = [:]
    @Published var gradingFeedbacks: [Int: String] = [:]
    @Published var overallFeedback = ""
    @Published var isSubmittingGrade = false
    @Published var showingGradingSuccess = false
    
    // MARK: - Filters
    @Published var selectedQuizId: Int?
    @Published var showingFilters = false
    
    // MARK: - Dependencies
    private let gradingRepository: GradingRepositoryProtocol
    private let limit = 50
    
    // MARK: - Initialization
    init(gradingRepository: GradingRepositoryProtocol = GradingRepository()) {
        self.gradingRepository = gradingRepository
    }
    
    // MARK: - Public Methods
    func loadPendingGrading(refresh: Bool = false) async {
        if refresh {
            isRefreshing = true
            currentPage = 1
            pendingAttempts.removeAll()
            hasMorePages = true
        } else {
            isLoading = true
        }
        
        errorMessage = nil
        showingError = false
        
        do {
            let response = try await gradingRepository.getPendingGrading(
                page: currentPage,
                limit: limit,
                quizId: selectedQuizId
            )
            
            if refresh || currentPage == 1 {
                pendingAttempts = response.data.attempts
            } else {
                pendingAttempts.append(contentsOf: response.data.attempts)
            }
            
            totalCount = response.data.totalCount
            hasMorePages = response.data.hasNextPage
            
            if hasMorePages {
                currentPage += 1
            }
            
        } catch {
            handleError(error)
        }
        
        isLoading = false
        isRefreshing = false
    }
    
    func loadMorePendingGrading() async {
        guard hasMorePages && !isLoading else { return }
        await loadPendingGrading()
    }
    
    func loadAttemptDetail(attemptId: Int) async {
        isLoading = true
        errorMessage = nil
        showingError = false
        
        do {
            let response = try await gradingRepository.getAttemptDetail(attemptId: attemptId)
            currentAttempt = response.data
            
            // Initialize grading data
            gradingScores.removeAll()
            gradingFeedbacks.removeAll()
            
            for answer in response.data.answers {
                gradingScores[answer.id] = answer.score ?? 0
                gradingFeedbacks[answer.id] = answer.instructorFeedback ?? ""
            }
            
            overallFeedback = response.data.attempt.feedback ?? ""
            
        } catch {
            handleError(error)
        }
        
        isLoading = false
    }
    
    func submitGrading(attemptId: Int) async -> Bool {
        guard let attempt = currentAttempt else { return false }
        
        isSubmittingGrade = true
        errorMessage = nil
        showingError = false
        
        let answers = attempt.answers.map { answer in
            GradeAnswerRequest(
                answerId: answer.id,
                score: gradingScores[answer.id] ?? 0,
                isCorrect: (gradingScores[answer.id] ?? 0) > 0,
                feedback: gradingFeedbacks[answer.id] ?? ""
            )
        }
        
        let request = GradeAttemptRequest(
            attemptId: attemptId,
            answers: answers,
            feedback: overallFeedback
        )
        
        do {
            let response = try await gradingRepository.gradeAttempt(attemptId: attemptId, request: request)
            
            // Remove from pending list
            pendingAttempts.removeAll { $0.id == attemptId }
            totalCount = max(0, totalCount - 1)
            
            // Clear current attempt
            currentAttempt = nil
            gradingScores.removeAll()
            gradingFeedbacks.removeAll()
            overallFeedback = ""
            
            showingGradingSuccess = true
            
            return true
        } catch {
            handleError(error)
            return false
        }
        
        isSubmittingGrade = false
    }
    
    func applyFilters() async {
        currentPage = 1
        pendingAttempts.removeAll()
        hasMorePages = true
        showingFilters = false
        await loadPendingGrading()
    }
    
    func clearFilters() async {
        selectedQuizId = nil
        await applyFilters()
    }
    
    func refreshPendingGrading() async {
        await loadPendingGrading(refresh: true)
    }
    
    // MARK: - Grading Helpers
    func updateScore(for answerId: Int, score: Double) {
        gradingScores[answerId] = score
    }
    
    func updateFeedback(for answerId: Int, feedback: String) {
        gradingFeedbacks[answerId] = feedback
    }
    
    func getTotalScore() -> Double {
        return gradingScores.values.reduce(0, +)
    }
    
    func getMaxScore() -> Double {
        guard let attempt = currentAttempt else { return 0 }
        return attempt.answers.reduce(0) { $0 + $1.maxScore }
    }
    
    func getPercentage() -> Double {
        let maxScore = getMaxScore()
        guard maxScore > 0 else { return 0 }
        return (getTotalScore() / maxScore) * 100
    }
    
    func isPassing() -> Bool {
        return getPercentage() >= 70 // Assuming 70% is passing
    }
    
    func getGradeLetter() -> String {
        let percentage = getPercentage()
        if percentage >= 90 { return "A" }
        else if percentage >= 80 { return "B" }
        else if percentage >= 70 { return "C" }
        else if percentage >= 60 { return "D" }
        else { return "F" }
    }
    
    // MARK: - Computed Properties
    var hasActiveFilters: Bool {
        return selectedQuizId != nil
    }
    
    var filteredAttemptsCount: String {
        if hasActiveFilters {
            return "\(totalCount) kết quả"
        } else {
            return "\(totalCount) bài cần chấm"
        }
    }
    
    var canSubmitGrading: Bool {
        guard let attempt = currentAttempt else { return false }
        
        // Check if all questions that need grading have scores
        for answer in attempt.answers where answer.needsGrading {
            if gradingScores[answer.id] == nil {
                return false
            }
        }
        
        return !isSubmittingGrade
    }
    
    // MARK: - Private Methods
    private func handleError(_ error: Error) {
        let errorManager = ErrorManager.shared

        if let apiError = error as? APIError {
            let errorInfo = ErrorInfo(
                type: .server,
                message: apiError.message,
                details: "API Error: \(apiError.code) - \(apiError.message)"
            )
            errorManager.showError(errorInfo)
        } else if let urlError = error as? URLError {
            errorManager.showNetworkError(
                message: "Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối internet.",
                details: "URLError: \(urlError.code.rawValue) - \(urlError.localizedDescription)"
            )
        } else {
            errorManager.showUnknownError(
                message: "Đã xảy ra lỗi khi tải dữ liệu chấm điểm.",
                details: "Error: \(error.localizedDescription)"
            )
        }

        // Keep the old properties for backward compatibility
        errorMessage = error.localizedDescription
        showingError = true
    }
}

// MARK: - Grading Statistics
extension GradingViewModel {
    var averageGradingTime: String {
        // This would be calculated based on actual grading data
        return "15 phút"
    }
    
    var todayGradedCount: Int {
        // This would be calculated based on actual grading data
        return 5
    }
    
    var weeklyGradedCount: Int {
        // This would be calculated based on actual grading data
        return 23
    }
}
