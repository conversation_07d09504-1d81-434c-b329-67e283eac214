//
//  GradingDetailView.swift
//  mobile-app-template
//
//  Created by Instructor App on 24/7/25.
//

import SwiftUI

struct GradingDetailView: View {
    let attempt: QuizAttempt
    @ObservedObject var viewModel: GradingViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var showingSubmitConfirmation = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                if viewModel.isLoading {
                    GradingLoadingView(message: "Đang tải chi tiết bài thi...")
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if let attemptDetail = viewModel.currentAttempt {
                    ScrollView {
                        VStack(spacing: AppConstants.UI.sectionSpacing) {
                            // Student and Quiz Info
                            StudentQuizInfoCard(attempt: attempt, quiz: attemptDetail.quiz)
                            
                            // Grading Summary
                            GradingSummaryCard(viewModel: viewModel)
                            
                            // Questions and Answers
                            VStack(spacing: 16) {
                                ForEach(attemptDetail.answers) { answer in
                                    AnswerGradingCard(
                                        answer: answer,
                                        score: Binding(
                                            get: { viewModel.gradingScores[answer.id] ?? 0 },
                                            set: { viewModel.updateScore(for: answer.id, score: $0) }
                                        ),
                                        feedback: Binding(
                                            get: { viewModel.gradingFeedbacks[answer.id] ?? "" },
                                            set: { viewModel.updateFeedback(for: answer.id, feedback: $0) }
                                        )
                                    )
                                }
                            }
                            
                            // Overall Feedback
                            OverallFeedbackCard(feedback: $viewModel.overallFeedback)
                            
                            Spacer(minLength: 100) // Space for bottom button
                        }
                        .padding(AppConstants.UI.screenPadding)
                    }
                    
                    // Submit button
                    VStack(spacing: 0) {
                        Divider()
                        
                        Button {
                            showingSubmitConfirmation = true
                        } label: {
                            HStack {
                                if viewModel.isSubmittingGrade {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                        .foregroundColor(.white)
                                } else {
                                    Image(systemName: "checkmark.circle")
                                    Text("Hoàn thành chấm điểm")
                                }
                            }
                            .font(AppConstants.Typography.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 16)
                            .background(viewModel.canSubmitGrading ? AppConstants.Colors.success : AppConstants.Colors.textSecondary)
                            .cornerRadius(AppConstants.UI.cornerRadius)
                        }
                        .disabled(!viewModel.canSubmitGrading)
                        .padding(AppConstants.UI.screenPadding)
                        .background(AppConstants.Colors.background)
                    }
                } else {
                    Text("Không thể tải chi tiết bài thi")
                        .font(AppConstants.Typography.body)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                }
            }
            .background(AppConstants.Colors.background)
            .navigationTitle("Chấm điểm")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Đóng") {
                        dismiss()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
            }
            .alert("Xác nhận chấm điểm", isPresented: $showingSubmitConfirmation) {
                Button("Hủy", role: .cancel) { }
                Button("Xác nhận") {
                    Task {
                        let success = await viewModel.submitGrading(attemptId: attempt.id)
                        if success {
                            dismiss()
                        }
                    }
                }
            } message: {
                Text("Điểm số sẽ được gửi cho học sinh. Bạn có chắc chắn muốn hoàn thành chấm điểm?")
            }
        }
        .task {
            await viewModel.loadAttemptDetail(attemptId: attempt.id)
        }
    }
}

// MARK: - Student Quiz Info Card
struct StudentQuizInfoCard: View {
    let attempt: QuizAttempt
    let quiz: Quiz
    
    var body: some View {
        VStack(spacing: 16) {
            // Student info
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(attempt.studentName)
                        .font(AppConstants.Typography.title3)
                        .fontWeight(.bold)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    Text(attempt.studentCode)
                        .font(AppConstants.Typography.body)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text("Lần \(attempt.attemptNumber)")
                        .font(AppConstants.Typography.caption)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.primary)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(AppConstants.Colors.primary.opacity(0.1))
                        .cornerRadius(8)
                    
                    Text(attempt.timeSpentText)
                        .font(AppConstants.Typography.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
            }
            
            Divider()
            
            // Quiz info
            VStack(alignment: .leading, spacing: 8) {
                Text(quiz.name)
                    .font(AppConstants.Typography.headline)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                HStack(spacing: 16) {
                    InfoItem(icon: "doc.text", text: quiz.code)
                    InfoItem(icon: "questionmark.circle", text: "\(quiz.questionCount) câu")
                    InfoItem(icon: "star", text: "\(Int(quiz.maxScore)) điểm")
                }
            }
        }
        .padding(AppConstants.UI.cardPadding)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .shadow(
            color: AppConstants.Colors.shadow.opacity(0.1),
            radius: AppConstants.UI.shadowRadius,
            x: 0,
            y: 2
        )
    }
}

// MARK: - Grading Summary Card
struct GradingSummaryCard: View {
    @ObservedObject var viewModel: GradingViewModel
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Text("Tổng kết điểm")
                    .font(AppConstants.Typography.headline)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Spacer()
                
                Text(viewModel.getGradeLetter())
                    .font(AppConstants.Typography.title2)
                    .fontWeight(.bold)
                    .foregroundColor(viewModel.isPassing() ? AppConstants.Colors.success : AppConstants.Colors.error)
            }
            
            HStack(spacing: 24) {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Điểm số")
                        .font(AppConstants.Typography.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                    
                    Text(String(format: "%.1f/%.1f", viewModel.getTotalScore(), viewModel.getMaxScore()))
                        .font(AppConstants.Typography.title3)
                        .fontWeight(.semibold)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                }
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("Phần trăm")
                        .font(AppConstants.Typography.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                    
                    Text(String(format: "%.1f%%", viewModel.getPercentage()))
                        .font(AppConstants.Typography.title3)
                        .fontWeight(.semibold)
                        .foregroundColor(viewModel.isPassing() ? AppConstants.Colors.success : AppConstants.Colors.error)
                }
                
                Spacer()
            }
            
            // Progress bar
            ProgressView(value: viewModel.getPercentage(), total: 100)
                .progressViewStyle(LinearProgressViewStyle(tint: viewModel.isPassing() ? AppConstants.Colors.success : AppConstants.Colors.error))
        }
        .padding(AppConstants.UI.cardPadding)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .shadow(
            color: AppConstants.Colors.shadow.opacity(0.1),
            radius: AppConstants.UI.shadowRadius,
            x: 0,
            y: 2
        )
    }
}

// MARK: - Info Item
struct InfoItem: View {
    let icon: String
    let text: String
    
    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: icon)
                .font(.caption)
                .foregroundColor(AppConstants.Colors.textSecondary)
            
            Text(text)
                .font(AppConstants.Typography.caption)
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
    }
}

// MARK: - Preview
struct GradingDetailView_Previews: PreviewProvider {
    static var previews: some View {
        GradingDetailView(
            attempt: QuizAttempt(
                id: 1,
                studentId: 101,
                studentName: "Nguyễn Văn An",
                studentCode: "SV001",
                studentEmail: "<EMAIL>",
                quizId: 1,
                quizName: "Kiểm tra Toán học",
                quizCode: "QUIZ001",
                attemptNumber: 1,
                startTime: Date().addingTimeInterval(-3600),
                endTime: Date().addingTimeInterval(-1800),
                timeSpent: 1800,
                score: 0,
                maxScore: 100,
                percentage: 0,
                isPassed: false,
                state: .completed,
                needsGrading: true,
                feedback: nil,
                submittedAt: Date().addingTimeInterval(-1800),
                gradedAt: nil,
                gradedBy: nil
            ),
            viewModel: GradingViewModel()
        )
    }
}
