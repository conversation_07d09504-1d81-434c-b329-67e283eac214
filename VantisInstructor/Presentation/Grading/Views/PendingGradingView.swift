//
//  PendingGradingView.swift
//  mobile-app-template
//
//  Created by Inst<PERSON>ctor <PERSON>pp on 24/7/25.
//

import SwiftUI

struct PendingGradingView: View {
    @StateObject private var viewModel = GradingViewModel()
    @State private var showingGradingDetail: QuizAttempt?
    
    var body: some View {
        NavigationStack {
            ScrollView {
                LazyVStack(spacing: 32) {
                    // Header Section
                    headerSection

                    // Header with stats
                    if !viewModel.pendingAttempts.isEmpty {
                        GradingStatsHeaderView(viewModel: viewModel)
                    }

                    // Filters
                    if viewModel.hasActiveFilters {
                        GradingFiltersView(viewModel: viewModel)
                            .padding(.horizontal, AppConstants.UI.screenPadding)
                    }

                    // Content
                    if viewModel.isLoading && viewModel.pendingAttempts.isEmpty {
                        GradingLoadingView(message: "<PERSON>ang tải danh sách cần chấm...")
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                    } else if viewModel.pendingAttempts.isEmpty {
                        EmptyGradingView()
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                    } else {
                        PendingGradingListView(
                            viewModel: viewModel,
                            onAttemptTap: { attempt in
                                showingGradingDetail = attempt
                            }
                        )
                    }

                    Spacer(minLength: 80)
                }
                .padding(.horizontal, 20)
                .padding(.top, 16)
            }
            .navigationBarHidden(true)
            .background(
                Color.white
                    .ignoresSafeArea()
            )
            .refreshable {
                await viewModel.refreshPendingGrading()
            }
            .sheet(item: $showingGradingDetail) { attempt in
                GradingDetailView(attempt: attempt, viewModel: viewModel)
            }

            .alert("Thành công", isPresented: $viewModel.showingGradingSuccess) {
                Button("OK") {
                    viewModel.showingGradingSuccess = false
                }
            } message: {
                Text("Đã chấm điểm thành công!")
            }
        }
        .task {
            await viewModel.loadPendingGrading(refresh: true)
        }
    }

    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("Chấm điểm")
                    .font(.beVietnamPro(.bold, size: 24))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Text("Chấm điểm bài tập và quiz")
                    .font(.beVietnamPro(.medium, size: 14))
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }

            Spacer()

            // Test Error Button (for development)
            Button(action: {
                testErrorModal()
            }) {
                Image(systemName: "exclamationmark.triangle")
                    .font(.title3)
                    .foregroundColor(AppConstants.Colors.error)
            }

            // Filter Menu
            Menu {
                Button {
                    viewModel.selectedQuizId = nil
                    Task {
                        await viewModel.applyFilters()
                    }
                } label: {
                    Label("Tất cả quiz", systemImage: "list.bullet")
                }

                // Add specific quiz filters here if needed

            } label: {
                Image(systemName: "line.3.horizontal.decrease.circle")
                    .font(.title3)
                    .foregroundColor(AppConstants.Colors.primary)
            }
        }
    }

    // MARK: - Test Methods
    private func testErrorModal() {
        let errorManager = ErrorManager.shared

        // Test different types of errors
        let errors = [
            ErrorInfo(
                type: .network,
                message: "Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối internet của bạn.",
                details: "URLError: -1009 - The Internet connection appears to be offline."
            ),
            ErrorInfo(
                type: .validation,
                message: "Dữ liệu nhập vào không hợp lệ. Vui lòng kiểm tra lại thông tin.",
                details: "ValidationError: Email format is invalid"
            ),
            ErrorInfo(
                type: .server,
                message: "Máy chủ đang gặp sự cố. Vui lòng thử lại sau.",
                details: "ServerError: 500 - Internal Server Error"
            )
        ]

        // Show multiple errors
        errorManager.showErrors(errors)
    }
}

// MARK: - Grading Stats Header
struct GradingStatsHeaderView: View {
    @ObservedObject var viewModel: GradingViewModel
    
    var body: some View {
        HStack(spacing: 12) {
            QuickStatsCard(
                title: "Cần chấm",
                value: "\(viewModel.totalCount)",
                subtitle: viewModel.filteredAttemptsCount,
                icon: "clock.fill",
                color: AppConstants.Colors.warning,
                trend: nil
            )
            
            QuickStatsCard(
                title: "Đã chấm hôm nay",
                value: "\(viewModel.todayGradedCount)",
                subtitle: "bài thi",
                icon: "checkmark.circle.fill",
                color: AppConstants.Colors.success,
                trend: nil
            )
        }
    }
}

// MARK: - Grading Filters
struct GradingFiltersView: View {
    @ObservedObject var viewModel: GradingViewModel
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 8) {
                if let quizId = viewModel.selectedQuizId {
                    GradingFilterChip(
                        title: "Quiz #\(quizId)",
                        onRemove: {
                            viewModel.selectedQuizId = nil
                            Task {
                                await viewModel.applyFilters()
                            }
                        }
                    )
                }
                
                Button("Xóa tất cả") {
                    Task {
                        await viewModel.clearFilters()
                    }
                }
                .font(.caption)
                .foregroundColor(AppConstants.Colors.error)
            }
            .padding(.horizontal, AppConstants.UI.screenPadding)
        }
    }
}

// MARK: - Pending Grading List
struct PendingGradingListView: View {
    @ObservedObject var viewModel: GradingViewModel
    let onAttemptTap: (QuizAttempt) -> Void
    
    var body: some View {
        List {
            ForEach(viewModel.pendingAttempts) { attempt in
                PendingAttemptRowView(attempt: attempt)
                    .onTapGesture {
                        onAttemptTap(attempt)
                    }
                    .listRowInsets(EdgeInsets(
                        top: 8,
                        leading: AppConstants.UI.screenPadding,
                        bottom: 8,
                        trailing: AppConstants.UI.screenPadding
                    ))
                    .listRowSeparator(.hidden)
                    .listRowBackground(Color.clear)
            }
            
            // Load more indicator
            if viewModel.hasMorePages {
                HStack {
                    Spacer()
                    if viewModel.isLoading {
                        ProgressView()
                            .scaleEffect(0.8)
                    } else {
                        Button("Tải thêm") {
                            Task {
                                await viewModel.loadMorePendingGrading()
                            }
                        }
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.primary)
                    }
                    Spacer()
                }
                .padding(.vertical, 8)
                .listRowInsets(EdgeInsets())
                .listRowSeparator(.hidden)
                .listRowBackground(Color.clear)
                .onAppear {
                    Task {
                        await viewModel.loadMorePendingGrading()
                    }
                }
            }
        }
        .listStyle(PlainListStyle())
    }
}

// MARK: - Empty Grading View
struct EmptyGradingView: View {
    var body: some View {
        VStack(spacing: 24) {
            Image(systemName: "checkmark.circle")
                .font(.system(size: 64))
                .foregroundColor(AppConstants.Colors.success)
            
            VStack(spacing: 8) {
                Text("Không có bài cần chấm")
                    .font(AppConstants.Typography.headline)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Text("Tất cả bài thi đã được chấm điểm hoặc chưa có học sinh nào nộp bài")
                    .font(AppConstants.Typography.body)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding(AppConstants.UI.screenPadding)
    }
}

// MARK: - Grading Filter Chip
struct GradingFilterChip: View {
    let title: String
    let onRemove: () -> Void

    var body: some View {
        HStack(spacing: 4) {
            Text(title)
                .font(.caption)
                .foregroundColor(AppConstants.Colors.textPrimary)

            Button(action: onRemove) {
                Image(systemName: "xmark")
                    .font(.caption2)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(AppConstants.Colors.surface)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(AppConstants.Colors.border, lineWidth: 1)
        )
    }
}

// MARK: - Grading Loading View
struct GradingLoadingView: View {
    let message: String

    var body: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)

            Text(message)
                .font(AppConstants.Typography.body)
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
    }
}

// MARK: - Preview
struct PendingGradingView_Previews: PreviewProvider {
    static var previews: some View {
        PendingGradingView()
    }
}
