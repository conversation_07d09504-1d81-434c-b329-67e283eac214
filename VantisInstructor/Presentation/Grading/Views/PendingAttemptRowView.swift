//
//  PendingAttemptRowView.swift
//  mobile-app-template
//
//  Created by Instructor <PERSON>pp on 24/7/25.
//

import SwiftUI

struct PendingAttemptRowView: View {
    let attempt: QuizAttempt
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header with student info and urgency
            HStack(alignment: .top) {
                VStack(alignment: .leading, spacing: 4) {
                    Text(attempt.studentName)
                        .font(AppConstants.Typography.headline)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    Text(attempt.studentCode)
                        .font(AppConstants.Typography.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    UrgencyBadge(submittedAt: attempt.submittedAt ?? Date())
                    
                    Text("Lần \(attempt.attemptNumber)")
                        .font(AppConstants.Typography.caption2)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
            }
            
            // Quiz info
            HStack(spacing: 8) {
                Image(systemName: "doc.text")
                    .font(.caption)
                    .foregroundColor(AppConstants.Colors.primary)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(attempt.quizName)
                        .font(AppConstants.Typography.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                        .lineLimit(1)
                    
                    Text(attempt.quizCode)
                        .font(AppConstants.Typography.caption2)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
                
                Spacer()
            }
            
            // Submission details
            HStack(spacing: 16) {
                AttemptDetailItem(
                    icon: "clock",
                    label: "Thời gian làm",
                    value: attempt.timeSpentText
                )
                
                AttemptDetailItem(
                    icon: "calendar",
                    label: "Nộp lúc",
                    value: formatSubmissionTime(attempt.submittedAt ?? Date())
                )
                
                Spacer()
            }
            
            // Action area
            HStack {
                HStack(spacing: 6) {
                    Image(systemName: "pencil.circle")
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.primary)
                    
                    Text("Chấm điểm")
                        .font(AppConstants.Typography.caption)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.primary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption2)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
        }
        .padding(AppConstants.UI.cardPadding)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .shadow(
            color: AppConstants.Colors.shadow.opacity(0.1),
            radius: AppConstants.UI.shadowRadius,
            x: 0,
            y: 2
        )
    }
    
    private func formatSubmissionTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        let calendar = Calendar.current
        
        if Calendar.current.isDateInToday(date) {
            formatter.dateFormat = "HH:mm"
            return "Hôm nay \(formatter.string(from: date))"
        } else if Calendar.current.isDateInYesterday(date) {
            formatter.dateFormat = "HH:mm"
            return "Hôm qua \(formatter.string(from: date))"
        } else {
            formatter.dateFormat = "dd/MM HH:mm"
            return formatter.string(from: date)
        }
    }
}

// MARK: - Urgency Badge
struct UrgencyBadge: View {
    let submittedAt: Date
    
    private var urgencyLevel: UrgencyLevel {
        let hoursSinceSubmission = Date().timeIntervalSince(submittedAt) / 3600
        
        if hoursSinceSubmission < 2 {
            return .low
        } else if hoursSinceSubmission < 24 {
            return .medium
        } else {
            return .high
        }
    }
    
    var body: some View {
        HStack(spacing: 4) {
            Circle()
                .fill(urgencyLevel.color)
                .frame(width: 6, height: 6)
            
            Text(urgencyLevel.text)
                .font(AppConstants.Typography.caption2)
                .fontWeight(.medium)
                .foregroundColor(urgencyLevel.color)
        }
        .padding(.horizontal, 6)
        .padding(.vertical, 2)
        .background(urgencyLevel.color.opacity(0.1))
        .cornerRadius(8)
    }
}

// MARK: - Urgency Level
enum UrgencyLevel {
    case low, medium, high
    
    var color: Color {
        switch self {
        case .low: return AppConstants.Colors.success
        case .medium: return AppConstants.Colors.warning
        case .high: return AppConstants.Colors.error
        }
    }
    
    var text: String {
        switch self {
        case .low: return "Mới"
        case .medium: return "Cần chấm"
        case .high: return "Khẩn cấp"
        }
    }
}

// MARK: - Attempt Detail Item
struct AttemptDetailItem: View {
    let icon: String
    let label: String
    let value: String
    
    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: icon)
                .font(.caption2)
                .foregroundColor(AppConstants.Colors.textSecondary)
            
            VStack(alignment: .leading, spacing: 0) {
                Text(value)
                    .font(AppConstants.Typography.caption)
                    .fontWeight(.medium)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Text(label)
                    .font(AppConstants.Typography.caption2)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
        }
    }
}

// MARK: - Preview
struct PendingAttemptRowView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 16) {
            PendingAttemptRowView(
                attempt: QuizAttempt(
                    id: 1,
                    studentId: 101,
                    studentName: "Nguyễn Văn An",
                    studentCode: "SV001",
                    studentEmail: "<EMAIL>",
                    quizId: 1,
                    quizName: "Kiểm tra Toán học - Chương 1",
                    quizCode: "QUIZ001",
                    attemptNumber: 1,
                    startTime: Date().addingTimeInterval(-3600),
                    endTime: Date().addingTimeInterval(-1800),
                    timeSpent: 1800,
                    score: 0,
                    maxScore: 100,
                    percentage: 0,
                    isPassed: false,
                    state: .completed,
                    needsGrading: true,
                    feedback: nil,
                    submittedAt: Date().addingTimeInterval(-1800),
                    gradedAt: nil,
                    gradedBy: nil
                )
            )
            
            PendingAttemptRowView(
                attempt: QuizAttempt(
                    id: 2,
                    studentId: 102,
                    studentName: "Trần Thị Bình",
                    studentCode: "SV002",
                    studentEmail: "<EMAIL>",
                    quizId: 1,
                    quizName: "Bài tập Văn học - Phân tích tác phẩm",
                    quizCode: "QUIZ002",
                    attemptNumber: 2,
                    startTime: Date().addingTimeInterval(-7200),
                    endTime: Date().addingTimeInterval(-5400),
                    timeSpent: 1800,
                    score: 0,
                    maxScore: 50,
                    percentage: 0,
                    isPassed: false,
                    state: .completed,
                    needsGrading: true,
                    feedback: nil,
                    submittedAt: Date().addingTimeInterval(-86400), // 1 day ago
                    gradedAt: nil,
                    gradedBy: nil
                )
            )
        }
        .padding()
        .background(AppConstants.Colors.background)
    }
}
