//
//  AnswerGradingCard.swift
//  mobile-app-template
//
//  Created by In<PERSON><PERSON><PERSON> <PERSON>pp on 24/7/25.
//

import SwiftUI

struct AnswerGradingCard: View {
    let answer: StudentAnswer
    @Binding var score: Double
    @Binding var feedback: String
    @State private var showingScoreInput = false
    @State private var tempScore: String = ""
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Question header
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Câu hỏi")
                        .font(AppConstants.Typography.caption)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                    
                    Spacer()
                    
                    QuestionTypeBadge(type: answer.questionType)
                }
                
                Text(answer.questionText)
                    .font(AppConstants.Typography.body)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                    .fixedSize(horizontal: false, vertical: true)
            }
            
            // Student answer
            VStack(alignment: .leading, spacing: 8) {
                Text("Câu trả lời của học sinh")
                    .font(AppConstants.Typography.caption)
                    .fontWeight(.medium)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                
                if answer.questionType == .essay {
                    Text(answer.answerText.isEmpty ? "Không có câu trả lời" : answer.answerText)
                        .font(AppConstants.Typography.body)
                        .foregroundColor(answer.answerText.isEmpty ? AppConstants.Colors.textSecondary : AppConstants.Colors.textPrimary)
                        .padding(12)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .background(AppConstants.Colors.surface)
                        .cornerRadius(8)
                } else {
                    Text(answer.answerText.isEmpty ? "Không có câu trả lời" : answer.answerText)
                        .font(AppConstants.Typography.body)
                        .foregroundColor(answer.answerText.isEmpty ? AppConstants.Colors.textSecondary : AppConstants.Colors.textPrimary)
                        .padding(12)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .background(AppConstants.Colors.surface)
                        .cornerRadius(8)
                }
            }
            
            // Grading section
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    Text("Chấm điểm")
                        .font(AppConstants.Typography.caption)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                    
                    Spacer()
                    
                    Text("Tối đa: \(String(format: "%.1f", answer.maxScore)) điểm")
                        .font(AppConstants.Typography.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
                
                // Score input
                HStack(spacing: 12) {
                    Button {
                        showingScoreInput = true
                        tempScore = String(format: "%.1f", score)
                    } label: {
                        HStack {
                            Text(String(format: "%.1f", score))
                                .font(AppConstants.Typography.title3)
                                .fontWeight(.semibold)
                                .foregroundColor(AppConstants.Colors.textPrimary)
                            
                            Text("điểm")
                                .font(AppConstants.Typography.body)
                                .foregroundColor(AppConstants.Colors.textSecondary)
                            
                            Image(systemName: "pencil")
                                .font(.caption)
                                .foregroundColor(AppConstants.Colors.primary)
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(AppConstants.Colors.surface)
                        .cornerRadius(8)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(AppConstants.Colors.primary, lineWidth: 1)
                        )
                    }
                    
                    // Quick score buttons
                    HStack(spacing: 8) {
                        QuickScoreButton(score: 0, maxScore: answer.maxScore, currentScore: $score)
                        QuickScoreButton(score: answer.maxScore * 0.5, maxScore: answer.maxScore, currentScore: $score)
                        QuickScoreButton(score: answer.maxScore, maxScore: answer.maxScore, currentScore: $score)
                    }
                    
                    Spacer()
                }
                
                // Feedback input
                VStack(alignment: .leading, spacing: 8) {
                    Text("Nhận xét")
                        .font(AppConstants.Typography.caption)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                    
                    TextField("Nhận xét cho câu trả lời này...", text: $feedback, axis: .vertical)
                        .textFieldStyle(PlainTextFieldStyle())
                        .padding(12)
                        .background(AppConstants.Colors.surface)
                        .cornerRadius(8)
                        .lineLimit(3...6)
                }
            }
        }
        .padding(AppConstants.UI.cardPadding)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .shadow(
            color: AppConstants.Colors.shadow.opacity(0.1),
            radius: AppConstants.UI.shadowRadius,
            x: 0,
            y: 2
        )
        .alert("Nhập điểm", isPresented: $showingScoreInput) {
            TextField("Điểm", text: $tempScore)
                .keyboardType(.decimalPad)
            
            Button("Hủy", role: .cancel) { }
            
            Button("OK") {
                if let newScore = Double(tempScore) {
                    score = min(max(newScore, 0), answer.maxScore)
                }
            }
        } message: {
            Text("Nhập điểm cho câu trả lời này (0 - \(String(format: "%.1f", answer.maxScore)))")
        }
    }
}

// MARK: - Question Type Badge
struct QuestionTypeBadge: View {
    let type: QuestionType
    
    var body: some View {
        Text(type.displayName)
            .font(AppConstants.Typography.caption2)
            .fontWeight(.medium)
            .foregroundColor(AppConstants.Colors.primary)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(AppConstants.Colors.primary.opacity(0.1))
            .cornerRadius(6)
    }
}

// MARK: - Quick Score Button
struct QuickScoreButton: View {
    let score: Double
    let maxScore: Double
    @Binding var currentScore: Double
    
    private var displayText: String {
        if score == 0 {
            return "0"
        } else if score == maxScore {
            return "Max"
        } else {
            return "50%"
        }
    }
    
    private var isSelected: Bool {
        abs(currentScore - score) < 0.1
    }
    
    var body: some View {
        Button {
            currentScore = score
        } label: {
            Text(displayText)
                .font(AppConstants.Typography.caption)
                .fontWeight(.medium)
                .foregroundColor(isSelected ? .white : AppConstants.Colors.textSecondary)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(isSelected ? AppConstants.Colors.primary : AppConstants.Colors.surface)
                .cornerRadius(6)
        }
        .animation(.easeInOut(duration: 0.2), value: isSelected)
    }
}

// MARK: - Overall Feedback Card
struct OverallFeedbackCard: View {
    @Binding var feedback: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Nhận xét tổng quan")
                .font(AppConstants.Typography.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            TextField("Nhận xét tổng quan về bài làm của học sinh...", text: $feedback, axis: .vertical)
                .textFieldStyle(PlainTextFieldStyle())
                .padding(12)
                .background(AppConstants.Colors.surface)
                .cornerRadius(8)
                .lineLimit(4...8)
        }
        .padding(AppConstants.UI.cardPadding)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .shadow(
            color: AppConstants.Colors.shadow.opacity(0.1),
            radius: AppConstants.UI.shadowRadius,
            x: 0,
            y: 2
        )
    }
}

// MARK: - Preview
struct AnswerGradingCard_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 16) {
            AnswerGradingCard(
                answer: StudentAnswer(
                    id: 1,
                    questionId: 1,
                    questionText: "Giải phương trình: 2x + 5 = 13",
                    questionType: .essay,
                    questionScore: 30,
                    answerText: "2x = 13 - 5\n2x = 8\nx = 4",
                    selectedAnswerIds: [],
                    score: nil,
                    maxScore: 30,
                    isCorrect: nil,
                    needsGrading: true,
                    instructorFeedback: nil,
                    submittedAt: Date(),
                    gradedAt: nil
                ),
                score: .constant(25.0),
                feedback: .constant("Làm đúng phương pháp, kết quả chính xác.")
            )
            
            OverallFeedbackCard(feedback: .constant("Bài làm tốt, cần chú ý thêm về cách trình bày."))
        }
        .padding()
        .background(AppConstants.Colors.background)
    }
}
