//
//  SecuritySettingsView.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import SwiftUI
import LocalAuthentication

struct SecuritySettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var authViewModel = AuthViewModel()
    @State private var showChangePassword = false
    @State private var showDeleteAccount = false
    @State private var biometricEnabled = false
    @State private var showBiometricError = false
    @State private var biometricErrorMessage = ""

    var body: some View {
        ScrollView {
            VStack(spacing: AppConstants.UI.sectionSpacing) {
                // Custom Header
                headerSection

                // Security Content
                securityContent

                Spacer(minLength: 100)
            }
            .padding(.horizontal, AppConstants.UI.screenPadding)
            .padding(.top, 20)
        }
        .navigationBarHidden(true)
        .background(AppConstants.Colors.background.ignoresSafeArea())
        .alert("Lỗi sinh trắc học", isPresented: $showBiometricError) {
            But<PERSON>("OK") { }
        } message: {
            Text(biometricErrorMessage)
        }
    }

    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            // Back button
            Button(action: {
                dismiss()
            }) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color.gray.opacity(0.8))
                    .frame(width: 36, height: 36)
                    .background(
                        Circle()
                            .fill(Color.white)
                            .overlay(
                                Circle()
                                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                            )
                            .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
                    )
            }
            .buttonStyle(PlainButtonStyle())

            Spacer()

            VStack(alignment: .center, spacing: 4) {
                Text("Bảo mật")
                    .font(.beVietnamPro(.bold, size: 24))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Text("Bảo vệ tài khoản của bạn")
                    .font(.beVietnamPro(.medium, size: 14))
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }

            Spacer()

            // Empty space for balance
            Color.clear
                .frame(width: 36, height: 36)
        }
        .padding(.bottom, 8)
    }

    // MARK: - Security Content
    private var securityContent: some View {
        VStack(spacing: AppConstants.UI.sectionSpacing) {
            // Authentication Section
            authenticationSection

            // Privacy Section
            privacySection

            // Account Security Section
            accountSecuritySection
        }
    }
    // MARK: - Helper Methods
    private var biometricType: String {
        return KeychainManager.shared.biometricTypeString()
    }

    private func toggleBiometric(_ enabled: Bool) {
        // For now, just update the state
        // TODO: Implement actual biometric enable/disable logic
        biometricEnabled = enabled
        HapticManager.shared.trigger(.light)
    }
}

// MARK: - Authentication Section Extension
extension SecuritySettingsView {
    private var authenticationSection: some View {
        VStack(spacing: 0) {
            // Section Header
            HStack {
                Text("XÁC THỰC")
                    .font(.beVietnamPro(.semiBold, size: 12))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .tracking(0.5)
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 12)

            // Section Content
            VStack(spacing: 0) {
                // Biometric Authentication
                if KeychainManager.shared.isBiometricAvailable() {
                    VStack(spacing: 0) {
                        HStack(spacing: 16) {
                            // Icon
                            Image(systemName: biometricType == "Face ID" ? "faceid" : "touchid")
                                .font(.system(size: 20))
                                .foregroundColor(AppConstants.Colors.primary)
                                .frame(width: 24, height: 24)

                            // Content
                            VStack(alignment: .leading, spacing: 4) {
                                Text(biometricType)
                                    .font(.beVietnamPro(.semiBold, size: 16))
                                    .foregroundColor(AppConstants.Colors.textPrimary)

                                Text("Sử dụng \(biometricType) để truy cập nhanh")
                                    .font(.beVietnamPro(.medium, size: 14))
                                    .foregroundColor(AppConstants.Colors.textSecondary)
                                    .multilineTextAlignment(.leading)
                            }

                            Spacer()

                            // Toggle
                            Toggle("", isOn: $biometricEnabled)
                                .toggleStyle(SwitchToggleStyle(tint: AppConstants.Colors.success))
                        }
                        .padding(.horizontal, 20)
                        .padding(.vertical, 16)
                    }
                    .onChange(of: biometricEnabled) { _, enabled in
                        toggleBiometric(enabled)
                    }

                    // Divider
                    Divider()
                        .background(AppConstants.Colors.border)
                        .padding(.horizontal, 20)
                }

                // Change Password
                Button(action: {
                    showChangePassword = true
                }) {
                    HStack(spacing: 16) {
                        // Icon
                        Image(systemName: "key")
                            .font(.system(size: 20))
                            .foregroundColor(AppConstants.Colors.primary)
                            .frame(width: 24, height: 24)

                        // Content
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Đổi mật khẩu")
                                .font(.beVietnamPro(.semiBold, size: 16))
                                .foregroundColor(AppConstants.Colors.textPrimary)

                            Text("Cập nhật mật khẩu tài khoản của bạn")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .multilineTextAlignment(.leading)
                        }

                        Spacer()

                        // Chevron
                        Image(systemName: "chevron.right")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
                .buttonStyle(PlainButtonStyle())
            }
            .background(Color.white)
            .cornerRadius(AppConstants.UI.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                    .stroke(AppConstants.Colors.border, lineWidth: 0.5)
            )
        }
    }
}

// MARK: - Privacy Section Extension
extension SecuritySettingsView {
    private var privacySection: some View {
        VStack(spacing: 0) {
            // Section Header
            HStack {
                Text("QUYỀN RIÊNG TƯ")
                    .font(.beVietnamPro(.semiBold, size: 12))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .tracking(0.5)
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 12)

            // Section Content
            VStack(spacing: 0) {
                // Privacy Settings
                Button(action: {
                    // TODO: Navigate to privacy settings
                }) {
                    HStack(spacing: 16) {
                        // Icon
                        Image(systemName: "eye.slash")
                            .font(.system(size: 20))
                            .foregroundColor(AppConstants.Colors.primary)
                            .frame(width: 24, height: 24)

                        // Content
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Cài đặt quyền riêng tư")
                                .font(.beVietnamPro(.semiBold, size: 16))
                                .foregroundColor(AppConstants.Colors.textPrimary)

                            Text("Kiểm soát dữ liệu và quyền riêng tư của bạn")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .multilineTextAlignment(.leading)
                        }

                        Spacer()

                        // Chevron
                        Image(systemName: "chevron.right")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
                .buttonStyle(PlainButtonStyle())

                // Divider
                Divider()
                    .background(AppConstants.Colors.border)
                    .padding(.horizontal, 20)

                // Export Data
                Button(action: {
                    // TODO: Export data action
                }) {
                    HStack(spacing: 16) {
                        // Icon
                        Image(systemName: "square.and.arrow.up")
                            .font(.system(size: 20))
                            .foregroundColor(AppConstants.Colors.primary)
                            .frame(width: 24, height: 24)

                        // Content
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Xuất dữ liệu")
                                .font(.beVietnamPro(.semiBold, size: 16))
                                .foregroundColor(AppConstants.Colors.textPrimary)

                            Text("Tải xuống dữ liệu tài khoản của bạn")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .multilineTextAlignment(.leading)
                        }

                        Spacer()

                        // Chevron
                        Image(systemName: "chevron.right")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
                .buttonStyle(PlainButtonStyle())
            }
            .background(Color.white)
            .cornerRadius(AppConstants.UI.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                    .stroke(AppConstants.Colors.border, lineWidth: 0.5)
            )
        }
    }
}

// MARK: - Account Security Section Extension
extension SecuritySettingsView {
    private var accountSecuritySection: some View {
        VStack(spacing: 0) {
            // Section Header
            HStack {
                Text("BẢO MẬT TÀI KHOẢN")
                    .font(.beVietnamPro(.semiBold, size: 12))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .tracking(0.5)
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 12)

            // Section Content
            VStack(spacing: 0) {
                // Active Sessions
                Button(action: {
                    // TODO: Navigate to active sessions
                }) {
                    HStack(spacing: 16) {
                        // Icon
                        Image(systemName: "desktopcomputer")
                            .font(.system(size: 20))
                            .foregroundColor(AppConstants.Colors.primary)
                            .frame(width: 24, height: 24)

                        // Content
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Phiên hoạt động")
                                .font(.beVietnamPro(.semiBold, size: 16))
                                .foregroundColor(AppConstants.Colors.textPrimary)

                            Text("Quản lý các thiết bị đã đăng nhập")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .multilineTextAlignment(.leading)
                        }

                        Spacer()

                        // Chevron
                        Image(systemName: "chevron.right")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
                .buttonStyle(PlainButtonStyle())

                // Divider
                Divider()
                    .background(AppConstants.Colors.border)
                    .padding(.horizontal, 20)

                // Security Log
                Button(action: {
                    // TODO: Navigate to security log
                }) {
                    HStack(spacing: 16) {
                        // Icon
                        Image(systemName: "list.bullet.clipboard")
                            .font(.system(size: 20))
                            .foregroundColor(AppConstants.Colors.primary)
                            .frame(width: 24, height: 24)

                        // Content
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Nhật ký bảo mật")
                                .font(.beVietnamPro(.semiBold, size: 16))
                                .foregroundColor(AppConstants.Colors.textPrimary)

                            Text("Xem các sự kiện bảo mật gần đây")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .multilineTextAlignment(.leading)
                        }

                        Spacer()

                        // Chevron
                        Image(systemName: "chevron.right")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
                .buttonStyle(PlainButtonStyle())

                // Divider
                Divider()
                    .background(AppConstants.Colors.border)
                    .padding(.horizontal, 20)

                // Delete Account (Danger Zone)
                Button(action: {
                    showDeleteAccount = true
                }) {
                    HStack(spacing: 16) {
                        // Icon
                        Image(systemName: "trash")
                            .font(.system(size: 20))
                            .foregroundColor(AppConstants.Colors.error)
                            .frame(width: 24, height: 24)

                        // Content
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Xóa tài khoản")
                                .font(.beVietnamPro(.semiBold, size: 16))
                                .foregroundColor(AppConstants.Colors.error)

                            Text("Xóa vĩnh viễn tài khoản của bạn")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .multilineTextAlignment(.leading)
                        }

                        Spacer()

                        // Warning Icon instead of chevron
                        Image(systemName: "exclamationmark.triangle")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppConstants.Colors.error)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
                .buttonStyle(PlainButtonStyle())
            }
            .background(Color.white)
            .cornerRadius(AppConstants.UI.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                    .stroke(AppConstants.Colors.border, lineWidth: 0.5)
            )
        }
    }
}

// MARK: - Supporting Views
struct ChangePasswordView: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack {
                Text("Tính năng đổi mật khẩu sẽ có sớm")
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            .navigationTitle("Đổi mật khẩu")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Hủy") {
                        dismiss()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
            }
        }
    }
}

// MARK: - Preview
#Preview {
    SecuritySettingsView()
}
