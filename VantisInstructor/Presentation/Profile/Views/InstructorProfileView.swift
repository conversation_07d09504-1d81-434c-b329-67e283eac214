//
//  InstructorProfileView.swift
//  mobile-app-template
//
//  Created by Instructor App on 27/7/25.
//

import SwiftUI

struct InstructorProfileView: View {
    @StateObject private var viewModel = InstructorProfileViewModel()
    @EnvironmentObject var authViewModel: AuthViewModel
    @State private var showingEditProfile = false
    
    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: AppConstants.UI.sectionSpacing) {
                    // Profile Header
                    profileHeaderSection
                    
                    // Statistics Cards
                    statisticsSection
                    
                    // Contact Information
                    contactInfoSection
                    
                    // Employment Information
                    employmentInfoSection
                    
                    // Skills & Qualifications
                    if viewModel.hasProfileData {
                        skillsSection
                        qualificationsSection
                    }
                    
                    // Data Status Info
                    dataStatusSection
                }
                .padding(.horizontal, AppConstants.UI.screenPadding)
                .padding(.bottom, 20)
            }
            .background(AppConstants.Colors.background.ignoresSafeArea())
            .navigationTitle("<PERSON><PERSON> sơ cá nhân")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Chỉnh sửa") {
                        showingEditProfile = true
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
            }
            .refreshable {
                await viewModel.refreshProfile()
            }
            .task {
                await viewModel.loadProfile()
            }
            .alert("Lỗi", isPresented: $viewModel.showError) {
                Button("OK") {
                    viewModel.clearError()
                }
            } message: {
                Text(viewModel.errorMessage ?? "Đã xảy ra lỗi không xác định")
            }
            .sheet(isPresented: $showingEditProfile) {
                EditProfileView()
                    .environmentObject(authViewModel)
            }
        }
    }
    
    // MARK: - Profile Header
    private var profileHeaderSection: some View {
        VStack(spacing: 16) {
            // Avatar
            Circle()
                .fill(AppConstants.Colors.primary.opacity(0.1))
                .frame(width: 100, height: 100)
                .overlay(
                    Text(viewModel.displayName.prefix(2).uppercased())
                        .font(AppConstants.Typography.title)
                        .fontWeight(.bold)
                        .foregroundColor(AppConstants.Colors.primary)
                )
            
            // Name and Title
            VStack(spacing: 4) {
                Text(viewModel.displayName)
                    .font(AppConstants.Typography.title2)
                    .fontWeight(.bold)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Text(viewModel.position)
                    .font(AppConstants.Typography.subheadline)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                
                if !viewModel.department.contains("Chưa có") {
                    Text(viewModel.department)
                        .font(AppConstants.Typography.caption)
                        .foregroundColor(AppConstants.Colors.textTertiary)
                }
            }
        }
        .padding(.vertical, 20)
        .frame(maxWidth: .infinity)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .shadow(color: AppConstants.Colors.shadow.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    // MARK: - Statistics Section
    private var statisticsSection: some View {
        let stats = viewModel.getStatistics()
        
        return VStack(alignment: .leading, spacing: 12) {
            Text("Thống kê")
                .font(AppConstants.Typography.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                StatCard(title: "Học sinh", value: "\(stats.totalStudents)", icon: "person.3.fill")
                StatCard(title: "Khóa học", value: "\(stats.totalCourses)", icon: "book.fill")
                StatCard(title: "Đánh giá", value: stats.formattedRating, icon: "star.fill")
                StatCard(title: "Hoàn thành", value: stats.formattedCompletionRate, icon: "checkmark.circle.fill")
            }
        }
    }
    
    // MARK: - Contact Info Section
    private var contactInfoSection: some View {
        let contactInfo = viewModel.getContactInfo()
        
        return VStack(alignment: .leading, spacing: 12) {
            Text("Thông tin liên hệ")
                .font(AppConstants.Typography.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            VStack(spacing: 8) {
                ProfileInfoRow(icon: "envelope.fill", title: "Email", value: contactInfo.email)
                ProfileInfoRow(icon: "phone.fill", title: "Điện thoại", value: contactInfo.phone)
                ProfileInfoRow(icon: "location.fill", title: "Địa chỉ", value: contactInfo.address)
            }
            .padding(16)
            .background(AppConstants.Colors.cardBackground)
            .cornerRadius(AppConstants.UI.cornerRadius)
        }
    }
    
    // MARK: - Employment Info Section
    private var employmentInfoSection: some View {
        let employmentInfo = viewModel.getEmploymentInfo()
        
        return VStack(alignment: .leading, spacing: 12) {
            Text("Thông tin công việc")
                .font(AppConstants.Typography.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            VStack(spacing: 8) {
                ProfileInfoRow(icon: "building.2.fill", title: "Khoa", value: employmentInfo.department)
                ProfileInfoRow(icon: "person.badge.key.fill", title: "Chức vụ", value: employmentInfo.position)
                ProfileInfoRow(icon: "briefcase.fill", title: "Loại hợp đồng", value: employmentInfo.employmentType)
            }
            .padding(16)
            .background(AppConstants.Colors.cardBackground)
            .cornerRadius(AppConstants.UI.cornerRadius)
        }
    }
    
    // MARK: - Skills Section
    private var skillsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Kỹ năng")
                .font(AppConstants.Typography.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            if let skills = viewModel.profile?.skills, !skills.isEmpty {
                VStack(spacing: 8) {
                    ForEach(skills) { skill in
                        SkillRow(skill: skill)
                    }
                }
                .padding(16)
                .background(AppConstants.Colors.cardBackground)
                .cornerRadius(AppConstants.UI.cornerRadius)
            } else {
                Text("Chưa có thông tin kỹ năng")
                    .font(AppConstants.Typography.body)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .padding(16)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .background(AppConstants.Colors.cardBackground)
                    .cornerRadius(AppConstants.UI.cornerRadius)
            }
        }
    }
    
    // MARK: - Qualifications Section
    private var qualificationsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Bằng cấp & Chứng chỉ")
                .font(AppConstants.Typography.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            if let qualifications = viewModel.profile?.qualifications, !qualifications.isEmpty {
                VStack(spacing: 8) {
                    ForEach(qualifications) { qualification in
                        QualificationRow(qualification: qualification)
                    }
                }
                .padding(16)
                .background(AppConstants.Colors.cardBackground)
                .cornerRadius(AppConstants.UI.cornerRadius)
            } else {
                Text("Chưa có thông tin bằng cấp")
                    .font(AppConstants.Typography.body)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .padding(16)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .background(AppConstants.Colors.cardBackground)
                    .cornerRadius(AppConstants.UI.cornerRadius)
            }
        }
    }
    
    // MARK: - Data Status Section
    private var dataStatusSection: some View {
        VStack(spacing: 8) {
            if viewModel.hasProfileData {
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(AppConstants.Colors.success)
                    Text("Dữ liệu hồ sơ đã được tải")
                        .font(AppConstants.Typography.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                    Spacer()
                }
            } else {
                HStack {
                    Image(systemName: "info.circle.fill")
                        .foregroundColor(AppConstants.Colors.warning)
                    Text("Hiển thị thông tin cơ bản từ tài khoản")
                        .font(AppConstants.Typography.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                    Spacer()
                }
            }
            
            if let errorMessage = viewModel.errorMessage, !viewModel.showError {
                HStack {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(AppConstants.Colors.warning)
                    Text(errorMessage)
                        .font(AppConstants.Typography.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                    Spacer()
                }
            }
        }
        .padding(.horizontal, 4)
    }
}

// MARK: - Supporting Views
struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(AppConstants.Colors.primary)
            
            Text(value)
                .font(AppConstants.Typography.title3)
                .fontWeight(.bold)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Text(title)
                .font(AppConstants.Typography.caption)
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .padding(16)
        .frame(maxWidth: .infinity)
        .background(AppConstants.Colors.cardBackground)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .shadow(color: AppConstants.Colors.shadow.opacity(0.05), radius: 1, x: 0, y: 1)
    }
}

struct ProfileInfoRow: View {
    let icon: String
    let title: String
    let value: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.body)
                .foregroundColor(AppConstants.Colors.primary)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(AppConstants.Typography.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
                
                Text(value)
                    .font(AppConstants.Typography.body)
                    .foregroundColor(AppConstants.Colors.textPrimary)
            }
            
            Spacer()
        }
    }
}

struct SkillRow: View {
    let skill: InstructorSkill
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text(skill.name)
                    .font(AppConstants.Typography.body)
                    .fontWeight(.medium)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                if let level = skill.proficiencyLevel {
                    Text(level.capitalized)
                        .font(AppConstants.Typography.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
            }
            
            Spacer()
            
            if let rating = skill.rating {
                HStack(spacing: 4) {
                    Image(systemName: "star.fill")
                        .font(.caption)
                        .foregroundColor(AppConstants.Colors.warning)
                    Text(String(format: "%.1f", rating))
                        .font(AppConstants.Typography.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
            }
        }
    }
}

struct QualificationRow: View {
    let qualification: InstructorQualification
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text(qualification.name)
                    .font(AppConstants.Typography.body)
                    .fontWeight(.medium)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                if let organization = qualification.issuingOrganization {
                    Text(organization)
                        .font(AppConstants.Typography.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
            }
            
            Spacer()
            
            if qualification.isVerified == true {
                Image(systemName: "checkmark.seal.fill")
                    .font(.caption)
                    .foregroundColor(AppConstants.Colors.success)
            }
        }
    }
}

// MARK: - Preview
struct InstructorProfileView_Previews: PreviewProvider {
    static var previews: some View {
        InstructorProfileView()
            .environmentObject(AuthViewModel())
    }
}
