//
//  InstructorProfileViewModel.swift
//  mobile-app-template
//
//  Created by Instructor App on 27/7/25.
//

import Foundation
import Combine

@MainActor
class InstructorProfileViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var profile: InstructorProfile?
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var showError = false
    @Published var hasProfileData = false
    
    // MARK: - Dependencies
    private let profileService = InstructorProfileService.shared
    private var authViewModel: AuthViewModel?
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Computed Properties
    var displayName: String {
        return profile?.displayName ?? authViewModel?.currentUser?.displayName ?? "Giảng viên"
    }
    
    var userEmail: String {
        return profile?.contactInfo.email ?? authViewModel?.currentUser?.email ?? ""
    }

    var initials: String {
        let name = profile?.name ?? authViewModel?.currentUser?.displayName ?? "U"
        let components = name.components(separatedBy: " ")
        if components.count >= 2 {
            let firstInitial = String(components.first?.prefix(1) ?? "")
            let lastInitial = String(components.last?.prefix(1) ?? "")
            return (firstInitial + lastInitial).uppercased()
        } else {
            return String(name.prefix(2)).uppercased()
        }
    }
    
    var department: String {
        return profile?.employmentInfo.department ?? "Chưa có thông tin"
    }
    
    var position: String {
        return profile?.employmentInfo.position ?? "Giảng viên"
    }
    
    var totalStudents: String {
        guard let count = profile?.statistics.totalStudents else { return "0" }
        return "\(count)"
    }
    
    var totalCourses: String {
        guard let count = profile?.statistics.totalCourses else { return "0" }
        return "\(count)"
    }
    
    var averageRating: String {
        guard let rating = profile?.statistics.averageRating else { return "0.0" }
        return String(format: "%.1f", rating)
    }
    
    var completionRate: String {
        return profile?.formattedCompletionRate ?? "0.0%"
    }
    
    // MARK: - Initialization
    init(authViewModel: AuthViewModel? = nil) {
        self.authViewModel = authViewModel
        setupBindings()
    }

    // MARK: - Configuration
    func setAuthViewModel(_ authViewModel: AuthViewModel) {
        self.authViewModel = authViewModel
    }
    
    // MARK: - Private Methods
    private func setupBindings() {
        // Observe profile service changes
        profileService.$currentProfile
            .receive(on: DispatchQueue.main)
            .sink { [weak self] profile in
                self?.profile = profile
                self?.hasProfileData = profile != nil
            }
            .store(in: &cancellables)
        
        profileService.$isLoading
            .receive(on: DispatchQueue.main)
            .assign(to: \.isLoading, on: self)
            .store(in: &cancellables)
        
        profileService.$errorMessage
            .receive(on: DispatchQueue.main)
            .sink { [weak self] errorMessage in
                self?.errorMessage = errorMessage
                self?.showError = errorMessage != nil
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Public Methods
    
    /// Load instructor profile
    func loadProfile(forceRefresh: Bool = false) async {
        print("👤 InstructorProfileViewModel: Loading profile...")
        
        do {
            let profile = try await profileService.getProfile(forceRefresh: forceRefresh)
            print("✅ InstructorProfileViewModel: Profile loaded successfully - \(profile.name)")
            
        } catch {
            print("❌ InstructorProfileViewModel: Failed to load profile - \(error)")
            
            // Handle specific errors gracefully
            if let profileError = error as? InstructorProfileError {
                switch profileError {
                case .serverDataIssue(_):
                    // For server data issues, show a user-friendly message but don't block the UI
                    errorMessage = "Một số thông tin hồ sơ tạm thời không khả dụng. Các tính năng khác vẫn hoạt động bình thường."
                    showError = false // Don't show error popup for this case
                    print("⚠️ InstructorProfileViewModel: Server data issue handled gracefully")
                    
                case .unauthorized:
                    errorMessage = "Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại."
                    showError = true
                    
                case .networkError:
                    errorMessage = "Không thể kết nối đến server. Vui lòng kiểm tra kết nối mạng."
                    showError = true
                    
                default:
                    errorMessage = error.localizedDescription
                    showError = true
                }
            } else {
                errorMessage = "Không thể tải thông tin hồ sơ. Vui lòng thử lại sau."
                showError = false // Don't block UI for profile loading issues
            }
        }
    }
    
    /// Refresh profile data
    func refreshProfile() async {
        await loadProfile(forceRefresh: true)
    }
    
    /// Clear error state
    func clearError() {
        errorMessage = nil
        showError = false
        profileService.errorMessage = nil
    }
    
    /// Get fallback profile data from auth user
    func getFallbackProfileData() -> (name: String, email: String, role: String) {
        let name = authViewModel?.currentUser?.displayName ?? "Giảng viên"
        let email = authViewModel?.currentUser?.email ?? ""
        let role = authViewModel?.currentUser?.role.displayName ?? "Instructor"
        
        return (name: name, email: email, role: role)
    }
    
    /// Check if we have enough data to show profile
    var canShowProfile: Bool {
        return profile != nil || authViewModel?.currentUser != nil
    }
    
    /// Get profile statistics for dashboard
    func getStatistics() -> ProfileStatistics {
        return ProfileStatistics(
            totalStudents: profile?.statistics.totalStudents ?? 0,
            totalCourses: profile?.statistics.totalCourses ?? 0,
            activeCourses: profile?.statistics.activeCourses ?? 0,
            averageRating: profile?.statistics.averageRating ?? 0.0,
            completedLessons: profile?.statistics.completedLessons ?? 0,
            totalLessons: profile?.statistics.totalLessons ?? 0,
            thisMonthHours: profile?.statistics.thisMonthHours ?? 0
        )
    }
    
    /// Get contact information
    func getContactInfo() -> ContactDisplayInfo {
        let contactInfo = profile?.contactInfo
        return ContactDisplayInfo(
            phone: contactInfo?.phone ?? "Chưa có thông tin",
            email: contactInfo?.email ?? userEmail,
            address: contactInfo?.address ?? "Chưa có thông tin",
            emergencyContact: contactInfo?.emergencyContact ?? "Chưa có thông tin",
            emergencyPhone: contactInfo?.emergencyPhone ?? "Chưa có thông tin"
        )
    }
    
    /// Get employment information
    func getEmploymentInfo() -> EmploymentDisplayInfo {
        let employmentInfo = profile?.employmentInfo
        return EmploymentDisplayInfo(
            department: employmentInfo?.department ?? "Chưa có thông tin",
            position: employmentInfo?.position ?? "Giảng viên",
            employmentType: employmentInfo?.employmentType ?? "Chưa có thông tin",
            salaryLevel: employmentInfo?.salaryLevel ?? "Chưa có thông tin"
        )
    }
}

// MARK: - Display Models
struct ProfileStatistics {
    let totalStudents: Int
    let totalCourses: Int
    let activeCourses: Int
    let averageRating: Double
    let completedLessons: Int
    let totalLessons: Int
    let thisMonthHours: Int
    
    var completionRate: Double {
        guard totalLessons > 0 else { return 0.0 }
        return Double(completedLessons) / Double(totalLessons)
    }
    
    var formattedCompletionRate: String {
        return String(format: "%.1f%%", completionRate * 100)
    }
    
    var formattedRating: String {
        return String(format: "%.1f", averageRating)
    }
}

struct ContactDisplayInfo {
    let phone: String
    let email: String
    let address: String
    let emergencyContact: String
    let emergencyPhone: String
}

struct EmploymentDisplayInfo {
    let department: String
    let position: String
    let employmentType: String
    let salaryLevel: String
}
