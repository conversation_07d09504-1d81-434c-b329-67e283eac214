import Foundation
import UserNotifications
import UIKit

class NotificationService: NSObject, ObservableObject {
    static let shared = NotificationService()
    
    @Published var isAuthorized = false
    @Published var authorizationStatus: UNAuthorizationStatus = .notDetermined
    
    private override init() {
        super.init()
        UNUserNotificationCenter.current().delegate = self
        checkAuthorizationStatus()
    }
    
    // MARK: - Authorization
    func requestPermission() async -> Bool {
        return await requestAuthorization()
    }

    func requestAuthorization() async -> Bool {
        do {
            let granted = try await UNUserNotificationCenter.current().requestAuthorization(
                options: [.alert, .badge, .sound, .provisional]
            )
            
            await MainActor.run {
                self.isAuthorized = granted
                self.checkAuthorizationStatus()
            }
            
            return granted
        } catch {
            print("Failed to request notification authorization: \(error)")
            return false
        }
    }
    
    func checkAuthorizationStatus() {
        UNUserNotificationCenter.current().getNotificationSettings { settings in
            DispatchQueue.main.async {
                self.authorizationStatus = settings.authorizationStatus
                self.isAuthorized = settings.authorizationStatus == .authorized || 
                                   settings.authorizationStatus == .provisional
            }
        }
    }
    
    // MARK: - Local Notifications
    func scheduleLocalNotification(
        id: String = UUID().uuidString,
        title: String,
        body: String,
        timeInterval: TimeInterval = 1,
        repeats: Bool = false,
        userInfo: [AnyHashable: Any] = [:]
    ) {
        let content = UNMutableNotificationContent()
        content.title = title
        content.body = body
        content.sound = .default
        content.userInfo = userInfo
        
        let trigger = UNTimeIntervalNotificationTrigger(
            timeInterval: timeInterval,
            repeats: repeats
        )
        
        let request = UNNotificationRequest(
            identifier: id,
            content: content,
            trigger: trigger
        )
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Failed to schedule notification: \(error)")
            }
        }
    }
    
    func scheduleNotificationAt(
        id: String = UUID().uuidString,
        title: String,
        body: String,
        date: Date,
        repeats: Bool = false,
        userInfo: [AnyHashable: Any] = [:]
    ) {
        let content = UNMutableNotificationContent()
        content.title = title
        content.body = body
        content.sound = .default
        content.userInfo = userInfo
        
        let calendar = Calendar.current
        let components = calendar.dateComponents([.year, .month, .day, .hour, .minute], from: date)
        
        let trigger = UNCalendarNotificationTrigger(
            dateMatching: components,
            repeats: repeats
        )
        
        let request = UNNotificationRequest(
            identifier: id,
            content: content,
            trigger: trigger
        )
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Failed to schedule notification: \(error)")
            }
        }
    }
    
    // MARK: - Notification Management
    func cancelNotification(withId id: String) {
        UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: [id])
    }
    
    func cancelAllNotifications() {
        UNUserNotificationCenter.current().removeAllPendingNotificationRequests()
    }
    
    func getPendingNotifications() async -> [UNNotificationRequest] {
        return await UNUserNotificationCenter.current().pendingNotificationRequests()
    }
    
    func getDeliveredNotifications() async -> [UNNotification] {
        return await UNUserNotificationCenter.current().deliveredNotifications()
    }
    
    // MARK: - Badge Management
    func setBadgeCount(_ count: Int) {
        DispatchQueue.main.async {
            if #available(iOS 16.0, *) {
                UNUserNotificationCenter.current().setBadgeCount(count)
            } else {
                UIApplication.shared.applicationIconBadgeNumber = count
            }
        }
    }
    
    func clearBadge() {
        setBadgeCount(0)
    }

    func updateBadgeCount() {
        // Update badge count based on unread notifications
        // This would typically get the count from your data source
        // For now, we'll just clear it
        clearBadge()
    }

    // MARK: - Device Token Management
    func setDeviceToken(_ deviceToken: Data) {
        let tokenString = deviceToken.map { String(format: "%02.2hhx", $0) }.joined()
        print("Device token: \(tokenString)")
        // Here you would typically send the token to your server
        // UserDefaults.standard.set(tokenString, forKey: "deviceToken")
    }
    
    // MARK: - Notification Categories and Actions
    func setupNotificationCategories() {
        // Transaction category
        let viewTransactionAction = UNNotificationAction(
            identifier: "VIEW_TRANSACTION",
            title: "View Transaction",
            options: [.foreground]
        )
        
        let transactionCategory = UNNotificationCategory(
            identifier: "TRANSACTION",
            actions: [viewTransactionAction],
            intentIdentifiers: [],
            options: []
        )
        
        // Reward category
        let viewRewardAction = UNNotificationAction(
            identifier: "VIEW_REWARD",
            title: "View Reward",
            options: [.foreground]
        )
        
        let rewardCategory = UNNotificationCategory(
            identifier: "REWARD",
            actions: [viewRewardAction],
            intentIdentifiers: [],
            options: []
        )
        
        // Security category
        let secureAccountAction = UNNotificationAction(
            identifier: "SECURE_ACCOUNT",
            title: "Secure Account",
            options: [.foreground]
        )
        
        let dismissAction = UNNotificationAction(
            identifier: "DISMISS",
            title: "Dismiss",
            options: []
        )
        
        let securityCategory = UNNotificationCategory(
            identifier: "SECURITY",
            actions: [secureAccountAction, dismissAction],
            intentIdentifiers: [],
            options: []
        )
        
        // Register categories
        UNUserNotificationCenter.current().setNotificationCategories([
            transactionCategory,
            rewardCategory,
            securityCategory
        ])
    }
    
    // MARK: - Utility Methods
    func createNotificationFromAppNotification(_ appNotification: AppNotification) {
        var categoryIdentifier: String?
        var userInfo: [AnyHashable: Any] = [
            "notificationId": appNotification.id,
            "type": appNotification.type.rawValue
        ]
        
        if let actionData = appNotification.actionData {
            userInfo["actionType"] = actionData.actionType.rawValue
            if let targetId = actionData.targetId {
                userInfo["targetId"] = targetId
            }
            if let url = actionData.url {
                userInfo["url"] = url
            }
        }
        
        switch appNotification.type {
        case .transaction:
            categoryIdentifier = "TRANSACTION"
        case .itemUpdated:
            categoryIdentifier = "REWARD"
        case .security:
            categoryIdentifier = "SECURITY"
        default:
            break
        }
        
        let content = UNMutableNotificationContent()
        content.title = appNotification.title
        content.body = appNotification.message
        content.sound = .default
        content.userInfo = userInfo
        
        if let categoryIdentifier = categoryIdentifier {
            content.categoryIdentifier = categoryIdentifier
        }
        
        let request = UNNotificationRequest(
            identifier: appNotification.id,
            content: content,
            trigger: nil // Immediate delivery
        )
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Failed to deliver notification: \(error)")
            }
        }
    }
}

// MARK: - UNUserNotificationCenterDelegate
extension NotificationService: UNUserNotificationCenterDelegate {
    // Handle notification when app is in foreground
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
    ) {
        // Show notification even when app is in foreground
        completionHandler([.banner, .sound, .badge])
    }
    
    // Handle notification tap
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        didReceive response: UNNotificationResponse,
        withCompletionHandler completionHandler: @escaping () -> Void
    ) {
        let userInfo = response.notification.request.content.userInfo
        
        // Handle different action identifiers
        switch response.actionIdentifier {
        case "VIEW_TRANSACTION":
            handleTransactionAction(userInfo: userInfo)
        case "VIEW_REWARD":
            handleRewardAction(userInfo: userInfo)
        case "SECURE_ACCOUNT":
            handleSecurityAction(userInfo: userInfo)
        case UNNotificationDefaultActionIdentifier:
            // User tapped the notification
            handleDefaultAction(userInfo: userInfo)
        default:
            break
        }
        
        completionHandler()
    }
    
    // MARK: - Action Handlers
    private func handleTransactionAction(userInfo: [AnyHashable: Any]) {
        if let targetId = userInfo["targetId"] as? String {
            // Navigate to transaction detail
            NotificationCenter.default.post(
                name: .navigateToTransaction,
                object: nil,
                userInfo: ["transactionId": targetId]
            )
        }
    }
    
    private func handleRewardAction(userInfo: [AnyHashable: Any]) {
        if let targetId = userInfo["targetId"] as? String {
            // Navigate to reward detail
            NotificationCenter.default.post(
                name: .navigateToReward,
                object: nil,
                userInfo: ["rewardId": targetId]
            )
        }
    }
    
    private func handleSecurityAction(userInfo: [AnyHashable: Any]) {
        // Navigate to security settings
        NotificationCenter.default.post(
            name: .navigateToSecurity,
            object: nil
        )
    }
    
    private func handleDefaultAction(userInfo: [AnyHashable: Any]) {
        if let actionTypeString = userInfo["actionType"] as? String,
           let actionType = NotificationActionType(rawValue: actionTypeString) {
            
            switch actionType {
            case .openTransaction:
                handleTransactionAction(userInfo: userInfo)
            case .openReward:
                handleRewardAction(userInfo: userInfo)
            case .openSettings:
                handleSecurityAction(userInfo: userInfo)
            case .openUrl:
                if let urlString = userInfo["url"] as? String,
                   let url = URL(string: urlString) {
                    UIApplication.shared.open(url)
                }
            default:
                break
            }
        }
    }
}

// MARK: - Notification Names
extension Notification.Name {
    static let navigateToTransaction = Notification.Name("navigateToTransaction")
    static let navigateToReward = Notification.Name("navigateToReward")
    static let navigateToSecurity = Notification.Name("navigateToSecurity")
}
