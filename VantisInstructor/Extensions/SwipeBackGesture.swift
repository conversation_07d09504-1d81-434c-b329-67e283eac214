//
//  SwipeBackGesture.swift
//  VantisInstructor
//
//  Created by VantisInstructor Team on 30/7/25.
//

import SwiftUI
import Foundation

// MARK: - SwiftUI View Modifier for Swipe Back Gesture
struct SwipeBackGestureModifier: ViewModifier {
    @Environment(\.dismiss) private var dismiss

    func body(content: Content) -> some View {
        content
            .gesture(
                DragGesture(minimumDistance: 20, coordinateSpace: .global)
                    .onEnded { value in
                        // Check if it's a swipe from left edge to right
                        // More generous detection for better UX
                        if value.startLocation.x < 80 && value.translation.width > 80 && abs(value.translation.height) < 100 {
                            dismiss()
                        }
                    }
            )
    }
}

// MARK: - View Extension for Easy Usage
extension View {
    /// Enables swipe back gesture for views with hidden navigation bar
    /// Provides intuitive swipe-from-left-edge gesture to go back
    func enableSwipeBack() -> some View {
        self.modifier(SwipeBackGestureModifier())
    }
}
