# Instructor Checkin Feature

## Tổng quan

Tính năng Checkin cho phép giảng viên xác nhận có mặt tại buổi học thông qua việc gọi API và hiển thị kết quả cho người dùng.

## Kiến trúc

### 1. Service Layer
- **InstructorCheckinService**: Xử lý các API calls cho checkin/checkout
- **Endpoint**: `POST /instructors/lessons/{lesson_id}/checkin`
- **Response format**: 
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {},
  "meta": {}
}
```

### 2. ViewModel Layer
- **LessonCheckinViewModel**: Quản lý state và business logic
- **States**: loading, success/error, result message
- **Location**: Tự động lấy vị trí hiện tại (nếu có quyền)

### 3. UI Layer
- **CheckinResultBottomSheet**: Hiển thị kết quả checkin
- **Loading overlay**: Disable UI và hiển thị loading trong khi chờ API
- **Integration**: Tích hợp vào các lesson detail bottom sheets

## Cách sử dụng

### 1. Trong Lesson Detail Bottom Sheet

```swift
struct LessonDetailBottomSheet: View {
    @StateObject private var checkinViewModel = LessonCheckinViewModel()
    
    var body: some View {
        // ... existing UI
        
        ModernActionButton(
            icon: "location.badge.plus",
            title: "Check-in",
            subtitle: "Xác nhận có mặt tại buổi học",
            color: .blue,
            isPrimary: true
        ) {
            Task {
                await checkinViewModel.checkinToLesson(lessonId: lesson.id)
            }
        }
        
        // Loading overlay
        .disabled(checkinViewModel.isLoading)
        .overlay(loadingOverlay)
        
        // Result bottom sheet
        .sheet(isPresented: $checkinViewModel.showingResultBottomSheet) {
            CheckinResultBottomSheet(
                isSuccess: checkinViewModel.isSuccess,
                message: checkinViewModel.resultMessage,
                onDismiss: { checkinViewModel.dismissResult() }
            )
        }
    }
}
```

### 2. API Request Format

```swift
// Request body
{
    "location_lat": 21.0285,
    "location_lng": 105.8542,
    "device_info": "iPhone - iOS 17.0",
    "notes": "Optional notes"
}

// Response
{
    "success": true,
    "message": "Check-in thành công!",
    "data": {},
    "meta": {}
}
```

## Tính năng chính

### 1. Loading State
- Disable toàn bộ UI khi đang gọi API
- Hiển thị loading indicator với message "Đang xử lý..."
- Overlay màu đen mờ để ngăn user interaction

### 2. Location Tracking
- Tự động lấy vị trí hiện tại nếu có quyền
- Gửi latitude/longitude trong request
- Fallback gracefully nếu không có location

### 3. Error Handling
- Network errors: "Không thể kết nối đến server"
- Unauthorized: "Phiên đăng nhập đã hết hạn"
- Server errors: Hiển thị message từ server
- Generic errors: "Đã xảy ra lỗi không xác định"

### 4. Result Display
- Success: Icon xanh + message từ API
- Error: Icon đỏ + error message
- Bottom sheet với button "Đóng"
- Auto-dismiss khi user tap outside

## Files được tạo/sửa đổi

### Tạo mới:
1. `Core/Services/InstructorCheckinService.swift`
2. `Presentation/Common/ViewModels/LessonCheckinViewModel.swift`
3. `Presentation/Common/Components/CheckinResultBottomSheet.swift`
4. `Presentation/Common/Components/CheckinPreview.swift`
5. `Tests/CheckinTests.swift`

### Sửa đổi:
1. `Core/Network/APIClient.swift` - Thêm body parameter
2. `Presentation/Common/Components/LessonDetailBottomSheet.swift`
3. `Presentation/Common/Components/SimpleLessonDetailBottomSheet.swift`
4. `Presentation/Common/Components/DemoLessonDetailBottomSheet.swift`

## Testing

### Unit Tests
```bash
# Chạy tests
xcodebuild test -scheme mobile-app-template -destination 'platform=iOS Simulator,name=iPhone 15'
```

### Manual Testing
1. Mở app và navigate đến lesson detail
2. Tap button "Check-in"
3. Verify loading state hiển thị
4. Verify result bottom sheet hiển thị với đúng message
5. Test với network error (airplane mode)
6. Test với unauthorized error

### Preview Testing
- Sử dụng `CheckinPreview.swift` để test UI components
- Test success/error states
- Test loading overlay

## API Integration

### Endpoint
```
POST /instructors/lessons/{lesson_id}/checkin
```

### Headers
```
Authorization: Bearer {token}
Content-Type: application/json
```

### Request Body
```json
{
    "location_lat": 21.0285,
    "location_lng": 105.8542,
    "device_info": "iPhone - iOS 17.0",
    "notes": "Optional notes"
}
```

### Response
```json
{
    "success": true,
    "message": "Check-in thành công!",
    "data": {},
    "meta": {}
}
```

## Permissions

### Location Permission
- Yêu cầu `NSLocationWhenInUseUsageDescription` trong Info.plist
- Graceful fallback nếu user từ chối permission
- Không block checkin nếu không có location

## Error Scenarios

1. **Network Error**: Hiển thị "Không thể kết nối đến server"
2. **Unauthorized**: Clear token và redirect to login
3. **Server Error**: Hiển thị message từ server
4. **Invalid Lesson ID**: "ID buổi học không hợp lệ"
5. **Location Error**: Vẫn cho phép checkin, chỉ không gửi location

## Future Enhancements

1. **Offline Support**: Cache checkin requests khi offline
2. **Geofencing**: Chỉ cho phép checkin trong phạm vi trường học
3. **QR Code**: Checkin bằng QR code
4. **Biometric**: Xác thực sinh trắc học trước khi checkin
5. **Analytics**: Track checkin patterns và statistics
