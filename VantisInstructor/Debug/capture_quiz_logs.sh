#!/bin/bash

# 🔍 Quiz Management API Debug Log Capture Script
# This script captures comprehensive logs when testing the Quiz Management screen

echo "🔍 ===== QUIZ MANAGEMENT DEBUG LOG CAPTURE ====="
echo "📅 Timestamp: $(date)"
echo "👤 Captured by: $(whoami)"
echo "🎯 Purpose: Debug Quiz Management error dialog issue"
echo ""

# Create log directory if it doesn't exist
LOG_DIR="mobile-app-template/Debug/logs"
mkdir -p "$LOG_DIR"

# Generate timestamp for log files
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="$LOG_DIR/quiz_debug_$TIMESTAMP.log"

echo "📁 Log file: $LOG_FILE"
echo ""

# Function to capture logs
capture_logs() {
    echo "🚀 Starting log capture..."
    echo "📱 Please perform the following actions in the app:"
    echo "   1. Navigate to Quiz Management screen"
    echo "   2. Tap the '🔍 Debug API' button"
    echo "   3. Pull to refresh to trigger quiz loading"
    echo "   4. Note any error dialogs that appear"
    echo ""
    echo "⏱️  Capturing logs for 60 seconds..."
    echo "   Press Ctrl+C to stop early"
    echo ""

    # Capture all app logs
    xcrun simctl spawn "iPhone 16 Pro" log show \
        --predicate 'subsystem == "com.vantisedu.vantisinstructor" OR category == "network" OR subsystem CONTAINS "auth"' \
        --info \
        --start "$(date -v-10S '+%Y-%m-%d %H:%M:%S')" \
        > "$LOG_FILE" 2>&1 &
    
    LOG_PID=$!
    
    # Wait for user input or timeout
    timeout 60 bash -c 'read -p "Press Enter when you have completed the test actions..."'
    
    # Stop log capture
    kill $LOG_PID 2>/dev/null
    
    echo ""
    echo "✅ Log capture completed!"
    echo "📁 Logs saved to: $LOG_FILE"
}

# Function to analyze logs
analyze_logs() {
    echo ""
    echo "🔍 ===== LOG ANALYSIS ====="
    
    if [ ! -f "$LOG_FILE" ]; then
        echo "❌ Log file not found: $LOG_FILE"
        return 1
    fi
    
    echo "📊 Log Statistics:"
    echo "   Total lines: $(wc -l < "$LOG_FILE")"
    echo "   File size: $(du -h "$LOG_FILE" | cut -f1)"
    echo ""
    
    echo "🔍 Key Events Found:"
    
    # Check for debug button taps
    DEBUG_TAPS=$(grep -c "DEBUG BUTTON TAPPED" "$LOG_FILE" 2>/dev/null || echo "0")
    echo "   Debug button taps: $DEBUG_TAPS"
    
    # Check for API requests
    API_REQUESTS=$(grep -c "API REQUEST" "$LOG_FILE" 2>/dev/null || echo "0")
    echo "   API requests: $API_REQUESTS"
    
    # Check for errors
    ERRORS=$(grep -c "ERROR\|❌\|FAILED" "$LOG_FILE" 2>/dev/null || echo "0")
    echo "   Errors found: $ERRORS"
    
    # Check for authentication events
    AUTH_EVENTS=$(grep -c "🔐\|Token\|Login" "$LOG_FILE" 2>/dev/null || echo "0")
    echo "   Authentication events: $AUTH_EVENTS"
    
    echo ""
    
    # Show recent errors if any
    if [ "$ERRORS" -gt 0 ]; then
        echo "🚨 Recent Errors:"
        grep -n "ERROR\|❌\|FAILED" "$LOG_FILE" | tail -5
        echo ""
    fi
    
    # Show authentication status
    echo "🔐 Authentication Status:"
    grep -n "Is logged in\|Token exists\|User found" "$LOG_FILE" | tail -3
    echo ""
    
    # Show API endpoints called
    echo "📡 API Endpoints:"
    grep -n "Full URL\|🌐 URL" "$LOG_FILE" | tail -3
    echo ""
}

# Function to generate summary
generate_summary() {
    echo "📋 ===== SUMMARY REPORT ====="
    
    SUMMARY_FILE="$LOG_DIR/quiz_debug_summary_$TIMESTAMP.md"
    
    cat > "$SUMMARY_FILE" << EOF
# 🔍 Quiz Management Debug Summary

## Test Information
- **Date**: $(date)
- **Tester**: $(whoami)
- **Log File**: $LOG_FILE
- **App Process**: $(pgrep -f "com.vantisedu.vantisinstructor" || echo "Not running")

## Test Results

### Authentication Status
$(grep "Is logged in\|Token exists\|User found" "$LOG_FILE" 2>/dev/null | tail -3 || echo "No authentication logs found")

### API Calls
$(grep "🌐 URL\|Full URL" "$LOG_FILE" 2>/dev/null | tail -5 || echo "No API calls found")

### Errors Detected
$(grep "ERROR\|❌\|FAILED" "$LOG_FILE" 2>/dev/null | tail -10 || echo "No errors found")

### Performance Metrics
$(grep "Duration\|ms" "$LOG_FILE" 2>/dev/null | tail -3 || echo "No performance metrics found")

## Recommendations
- [ ] Review authentication token validity
- [ ] Check API endpoint configuration
- [ ] Verify network connectivity
- [ ] Analyze error patterns

## Next Steps
1. Review detailed logs in: $LOG_FILE
2. Test with different network conditions
3. Verify backend API status
4. Check token expiration handling

---
*Generated automatically by capture_quiz_logs.sh*
EOF

    echo "📄 Summary report generated: $SUMMARY_FILE"
    echo ""
    echo "📋 Quick Summary:"
    cat "$SUMMARY_FILE"
}

# Main execution
main() {
    # Check if simulator is running
    if ! xcrun simctl list devices | grep -q "iPhone 16 Pro.*Booted"; then
        echo "❌ iPhone 16 Pro simulator is not running"
        echo "   Please start the simulator and launch the app first"
        exit 1
    fi
    
    # Check if app is running
    if ! pgrep -f "com.vantisedu.vantisinstructor" > /dev/null; then
        echo "⚠️  App doesn't appear to be running"
        echo "   Please launch the app first"
        echo "   Continuing anyway..."
    fi
    
    # Capture logs
    capture_logs
    
    # Analyze logs
    analyze_logs
    
    # Generate summary
    generate_summary
    
    echo ""
    echo "🎉 Debug log capture completed successfully!"
    echo "📁 Files generated:"
    echo "   - Detailed logs: $LOG_FILE"
    echo "   - Summary report: $SUMMARY_FILE"
    echo ""
    echo "💡 To view logs in real-time, run:"
    echo "   tail -f $LOG_FILE"
    echo ""
    echo "🔍 To search for specific patterns:"
    echo "   grep 'pattern' $LOG_FILE"
}

# Run main function
main "$@"
