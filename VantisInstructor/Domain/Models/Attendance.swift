//
//  Attendance.swift
//  mobile-app-template
//
//  Created by Instructor App on 23/7/25.
//

import Foundation

// MARK: - Attendance Model
struct Attendance: Codable, Identifiable {
    let id: String
    let classId: String
    let courseId: String
    let courseName: String
    let courseCode: String
    let studentId: String
    let studentName: String
    let studentNumber: String
    let status: AttendanceStatus
    let checkInTime: Date?
    let checkOutTime: Date?
    let notes: String?
    let location: String?
    let deviceInfo: String?
    let ipAddress: String?
    let isExcused: Bool
    let excuseReason: String?
    let excuseDocument: String?
    let markedBy: String? // Instructor ID who marked attendance
    let markedAt: Date?
    let lastModifiedBy: String?
    let lastModifiedAt: Date?
    let metadata: [String: AnyCodable]?
    let createdAt: Date
    let updatedAt: Date?
    
    // Computed properties
    var isPresent: Bool {
        return status == .present || status == .late
    }
    
    var duration: TimeInterval? {
        guard let checkIn = checkInTime, let checkOut = checkOutTime else { return nil }
        return checkOut.timeIntervalSince(checkIn)
    }
    
    var formattedDuration: String {
        guard let duration = duration else { return "N/A" }
        let hours = Int(duration) / 3600
        let minutes = Int(duration) % 3600 / 60
        return String(format: "%dh %dm", hours, minutes)
    }
    
    var formattedCheckInTime: String {
        guard let checkInTime = checkInTime else { return "N/A" }
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: checkInTime)
    }
}

// MARK: - Attendance Status
enum AttendanceStatus: String, Codable, CaseIterable {
    case present = "PRESENT"
    case absent = "ABSENT"
    case late = "LATE"
    case excused = "EXCUSED"
    case sick = "SICK"
    case emergency = "EMERGENCY"
    case unknown = "UNKNOWN"
    
    var displayName: String {
        switch self {
        case .present: return "Có mặt"
        case .absent: return "Vắng mặt"
        case .late: return "Đi muộn"
        case .excused: return "Có phép"
        case .sick: return "Ốm"
        case .emergency: return "Khẩn cấp"
        case .unknown: return "Chưa xác định"
        }
    }
    
    var color: String {
        switch self {
        case .present: return "green"
        case .absent: return "red"
        case .late: return "orange"
        case .excused: return "blue"
        case .sick: return "purple"
        case .emergency: return "pink"
        case .unknown: return "gray"
        }
    }
    
    var icon: String {
        switch self {
        case .present: return "checkmark.circle.fill"
        case .absent: return "xmark.circle.fill"
        case .late: return "clock.fill"
        case .excused: return "checkmark.shield.fill"
        case .sick: return "cross.fill"
        case .emergency: return "exclamationmark.triangle.fill"
        case .unknown: return "questionmark.circle.fill"
        }
    }
    
    var weight: Double {
        // For calculating attendance percentage
        switch self {
        case .present: return 1.0
        case .late: return 0.8
        case .excused, .sick, .emergency: return 1.0
        case .absent: return 0.0
        case .unknown: return 0.0
        }
    }
}

// MARK: - Class Attendance Summary
struct ClassAttendanceSummary: Codable, Identifiable {
    let id: String
    let classId: String
    let courseId: String
    let courseName: String
    let courseCode: String
    let classDate: Date
    let classTitle: String
    let totalStudents: Int
    let presentStudents: Int
    let absentStudents: Int
    let lateStudents: Int
    let excusedStudents: Int
    let attendanceRate: Double
    let attendanceRecords: [Attendance]
    let createdAt: Date
    let updatedAt: Date?
    
    // Computed properties
    var statusCounts: [AttendanceStatus: Int] {
        var counts: [AttendanceStatus: Int] = [:]
        for record in attendanceRecords {
            counts[record.status, default: 0] += 1
        }
        return counts
    }
    
    var formattedAttendanceRate: String {
        return String(format: "%.1f%%", attendanceRate)
    }
}

// MARK: - Student Attendance Summary
struct StudentAttendanceSummary: Codable, Identifiable {
    let id: String
    let studentId: String
    let studentName: String
    let studentNumber: String
    let courseId: String
    let courseName: String
    let courseCode: String
    let totalClasses: Int
    let attendedClasses: Int
    let absentClasses: Int
    let lateClasses: Int
    let excusedClasses: Int
    let attendanceRate: Double
    let attendanceRecords: [Attendance]
    let lastAttendance: Date?
    let createdAt: Date
    let updatedAt: Date?
    
    // Computed properties
    var attendanceGrade: AttendanceGrade {
        switch attendanceRate {
        case 95...100: return .excellent
        case 85..<95: return .good
        case 75..<85: return .satisfactory
        case 60..<75: return .poor
        default: return .failing
        }
    }
    
    var formattedAttendanceRate: String {
        return String(format: "%.1f%%", attendanceRate)
    }
    
    var recentAttendancePattern: [AttendanceStatus] {
        return Array(attendanceRecords
            .sorted { $0.createdAt > $1.createdAt }
            .prefix(10)
            .map { $0.status })
    }
}

// MARK: - Attendance Grade
enum AttendanceGrade: String, Codable, CaseIterable {
    case excellent = "EXCELLENT"
    case good = "GOOD"
    case satisfactory = "SATISFACTORY"
    case poor = "POOR"
    case failing = "FAILING"
    
    var displayName: String {
        switch self {
        case .excellent: return "Xuất sắc"
        case .good: return "Tốt"
        case .satisfactory: return "Đạt"
        case .poor: return "Yếu"
        case .failing: return "Kém"
        }
    }
    
    var color: String {
        switch self {
        case .excellent: return "green"
        case .good: return "blue"
        case .satisfactory: return "orange"
        case .poor: return "red"
        case .failing: return "gray"
        }
    }
    
    var minPercentage: Double {
        switch self {
        case .excellent: return 95
        case .good: return 85
        case .satisfactory: return 75
        case .poor: return 60
        case .failing: return 0
        }
    }
}

// MARK: - Attendance Extensions
extension Attendance {
    static let mockAttendanceRecords: [Attendance] = [
        Attendance(
            id: "attendance_1",
            classId: "class_1",
            courseId: "course_1",
            courseName: "Lập trình iOS với Swift",
            courseCode: "CS301",
            studentId: "student_1",
            studentName: "Nguyễn Văn B",
            studentNumber: "2024001",
            status: .present,
            checkInTime: Date().addingTimeInterval(-2 * 3600), // 2 hours ago
            checkOutTime: Date(),
            notes: nil,
            location: "Tòa A - A301",
            deviceInfo: "iPhone 15 Pro",
            ipAddress: "*************",
            isExcused: false,
            excuseReason: nil,
            excuseDocument: nil,
            markedBy: "instructor_1",
            markedAt: Date().addingTimeInterval(-2 * 3600),
            lastModifiedBy: nil,
            lastModifiedAt: nil,
            metadata: nil,
            createdAt: Date().addingTimeInterval(-2 * 3600),
            updatedAt: nil
        ),
        Attendance(
            id: "attendance_2",
            classId: "class_1",
            courseId: "course_1",
            courseName: "Lập trình iOS với Swift",
            courseCode: "CS301",
            studentId: "student_2",
            studentName: "Trần Thị D",
            studentNumber: "2024002",
            status: .late,
            checkInTime: Date().addingTimeInterval(-90 * 60), // 90 minutes ago (30 min late)
            checkOutTime: Date(),
            notes: "Đến muộn do kẹt xe",
            location: "Tòa A - A301",
            deviceInfo: "iPhone 14",
            ipAddress: "*************",
            isExcused: false,
            excuseReason: nil,
            excuseDocument: nil,
            markedBy: "instructor_1",
            markedAt: Date().addingTimeInterval(-90 * 60),
            lastModifiedBy: nil,
            lastModifiedAt: nil,
            metadata: nil,
            createdAt: Date().addingTimeInterval(-90 * 60),
            updatedAt: nil
        )
    ]
}
