//
//  Mission.swift
//  mobile-app-template
//
//  Created for Instructor Mission System
//

import Foundation
import SwiftUI

// MARK: - Mission Model
struct Mission: Codable, Identifiable {
    let id: String
    let title: String
    let description: String
    let category: MissionCategory
    let type: MissionType
    let status: MissionStatus
    let difficulty: MissionDifficulty
    let xpReward: Int
    let coinReward: Int?
    let badgeReward: String?
    let requirements: [MissionRequirement]
    let progress: MissionProgress
    let startDate: Date
    let endDate: Date?
    let isRecurring: Bool
    let recurringPattern: RecurringPattern?
    let tags: [String]
    let metadata: [String: AnyCodable]?
    let createdAt: Date
    let updatedAt: Date?
    
    // Computed properties
    var isActive: Bool {
        status == .active && (endDate == nil || Date() < endDate!)
    }
    
    var progressPercentage: Double {
        guard !requirements.isEmpty else { return 0 }
        let completedRequirements = requirements.filter { req in
            progress.completedRequirements.contains(req.id)
        }.count
        return Double(completedRequirements) / Double(requirements.count) * 100
    }
    
    var isCompleted: Bool {
        progressPercentage >= 100
    }
    
    var timeRemaining: String? {
        guard let endDate = endDate else { return nil }
        let formatter = DateComponentsFormatter()
        formatter.allowedUnits = [.day, .hour, .minute]
        formatter.unitsStyle = .abbreviated
        
        if Date() < endDate {
            return formatter.string(from: Date(), to: endDate)
        }
        return "Hết hạn"
    }
}

// MARK: - Mission Category
enum MissionCategory: String, Codable, CaseIterable {
    case teaching = "TEACHING"
    case engagement = "ENGAGEMENT"
    case assessment = "ASSESSMENT"
    case innovation = "INNOVATION"
    case community = "COMMUNITY"
    case professional = "PROFESSIONAL"
    
    var displayName: String {
        switch self {
        case .teaching: return "Giảng dạy"
        case .engagement: return "Tương tác"
        case .assessment: return "Đánh giá"
        case .innovation: return "Đổi mới"
        case .community: return "Cộng đồng"
        case .professional: return "Chuyên môn"
        }
    }
    
    var icon: String {
        switch self {
        case .teaching: return "book.fill"
        case .engagement: return "person.3.fill"
        case .assessment: return "checkmark.seal.fill"
        case .innovation: return "lightbulb.fill"
        case .community: return "person.2.wave.2.fill"
        case .professional: return "star.fill"
        }
    }
    
    var color: Color {
        switch self {
        case .teaching: return .blue
        case .engagement: return .green
        case .assessment: return .orange
        case .innovation: return .purple
        case .community: return .pink
        case .professional: return .indigo
        }
    }
}

// MARK: - Mission Type
enum MissionType: String, Codable, CaseIterable {
    case daily = "DAILY"
    case weekly = "WEEKLY"
    case monthly = "MONTHLY"
    case semester = "SEMESTER"
    case achievement = "ACHIEVEMENT"
    case special = "SPECIAL"
    
    var displayName: String {
        switch self {
        case .daily: return "Hàng ngày"
        case .weekly: return "Hàng tuần"
        case .monthly: return "Hàng tháng"
        case .semester: return "Học kỳ"
        case .achievement: return "Thành tựu"
        case .special: return "Đặc biệt"
        }
    }
}

// MARK: - Mission Status
enum MissionStatus: String, Codable {
    case locked = "LOCKED"
    case active = "ACTIVE"
    case inProgress = "IN_PROGRESS"
    case completed = "COMPLETED"
    case claimed = "CLAIMED"
    case expired = "EXPIRED"
}

// MARK: - Mission Difficulty
enum MissionDifficulty: String, Codable, CaseIterable {
    case easy = "EASY"
    case medium = "MEDIUM"
    case hard = "HARD"
    case legendary = "LEGENDARY"
    
    var displayName: String {
        switch self {
        case .easy: return "Dễ"
        case .medium: return "Trung bình"
        case .hard: return "Khó"
        case .legendary: return "Huyền thoại"
        }
    }
    
    var color: Color {
        switch self {
        case .easy: return .green
        case .medium: return .blue
        case .hard: return .orange
        case .legendary: return .purple
        }
    }
    
    var stars: Int {
        switch self {
        case .easy: return 1
        case .medium: return 2
        case .hard: return 3
        case .legendary: return 4
        }
    }
}

// MARK: - Mission Requirement
struct MissionRequirement: Codable, Identifiable {
    let id: String
    let type: RequirementType
    let description: String
    let targetValue: Int
    let currentValue: Int
    let unit: String?
    
    var isCompleted: Bool {
        currentValue >= targetValue
    }
    
    var progressPercentage: Double {
        min(Double(currentValue) / Double(targetValue) * 100, 100)
    }
}

// MARK: - Requirement Type
enum RequirementType: String, Codable {
    case createAssignment = "CREATE_ASSIGNMENT"
    case gradeSubmission = "GRADE_SUBMISSION"
    case conductClass = "CONDUCT_CLASS"
    case markAttendance = "MARK_ATTENDANCE"
    case createQuiz = "CREATE_QUIZ"
    case postAnnouncement = "POST_ANNOUNCEMENT"
    case replyStudent = "REPLY_STUDENT"
    case uploadMaterial = "UPLOAD_MATERIAL"
    case receiveFeedback = "RECEIVE_FEEDBACK"
    case attendanceRate = "ATTENDANCE_RATE"
    case submissionRate = "SUBMISSION_RATE"
    case averageScore = "AVERAGE_SCORE"
    case useNewFeature = "USE_NEW_FEATURE"
    case completeProfile = "COMPLETE_PROFILE"
    case collaborateTeacher = "COLLABORATE_TEACHER"
    
    var icon: String {
        switch self {
        case .createAssignment: return "doc.text.badge.plus"
        case .gradeSubmission: return "checkmark.circle.fill"
        case .conductClass: return "person.3.fill"
        case .markAttendance: return "person.badge.clock"
        case .createQuiz: return "questionmark.circle.fill"
        case .postAnnouncement: return "megaphone.fill"
        case .replyStudent: return "bubble.left.and.bubble.right.fill"
        case .uploadMaterial: return "arrow.up.doc.fill"
        case .receiveFeedback: return "star.fill"
        case .attendanceRate: return "chart.line.uptrend.xyaxis"
        case .submissionRate: return "doc.text.fill"
        case .averageScore: return "chart.bar.fill"
        case .useNewFeature: return "sparkles"
        case .completeProfile: return "person.crop.circle.fill"
        case .collaborateTeacher: return "person.2.fill"
        }
    }
}

// MARK: - Mission Progress
struct MissionProgress: Codable {
    let missionId: String
    let userId: String
    var completedRequirements: Set<String>
    var startedAt: Date
    var lastUpdatedAt: Date
    var completedAt: Date?
    var claimedAt: Date?
}

// MARK: - Recurring Pattern
struct RecurringPattern: Codable {
    let frequency: RecurringFrequency
    let interval: Int // e.g., every 2 days, every 1 week
    let daysOfWeek: Set<Int>? // 0 = Sunday, 6 = Saturday
    let dayOfMonth: Int? // 1-31
    let endAfterOccurrences: Int?
}

enum RecurringFrequency: String, Codable {
    case daily = "DAILY"
    case weekly = "WEEKLY"
    case monthly = "MONTHLY"
}

// MARK: - Mission Badge
struct MissionBadge: Codable, Identifiable {
    let id: String
    let name: String
    let description: String
    let iconName: String
    let rarity: BadgeRarity
    let category: MissionCategory
    let unlockedAt: Date?
    
    var isUnlocked: Bool {
        unlockedAt != nil
    }
}

enum BadgeRarity: String, Codable {
    case common = "COMMON"
    case rare = "RARE"
    case epic = "EPIC"
    case legendary = "LEGENDARY"
    
    var color: Color {
        switch self {
        case .common: return .gray
        case .rare: return .blue
        case .epic: return .purple
        case .legendary: return .orange
        }
    }
}

// MARK: - Mission Streak
struct MissionStreak: Codable {
    let userId: String
    let type: MissionType
    var currentStreak: Int
    var longestStreak: Int
    var lastCompletedDate: Date?
    var streakMultiplier: Double // Bonus rewards for maintaining streaks
    
    var isActive: Bool {
        guard let lastDate = lastCompletedDate else { return false }
        let daysSince = Calendar.current.dateComponents([.day], from: lastDate, to: Date()).day ?? 0
        
        switch type {
        case .daily:
            return daysSince <= 1
        case .weekly:
            return daysSince <= 7
        default:
            return true
        }
    }
}

// MARK: - Mission Leaderboard Entry
struct MissionLeaderboardEntry: Codable, Identifiable {
    let id: String
    let userId: String
    let userName: String
    let userAvatar: String?
    let totalXP: Int
    let completedMissions: Int
    let badgesEarned: Int
    let rank: Int
    let previousRank: Int?
    let department: String?
    
    var rankChange: Int? {
        guard let prev = previousRank else { return nil }
        return prev - rank
    }
}

// MARK: - Mock Data
extension Mission {
    static let mockMissions: [Mission] = [
        Mission(
            id: "mission_1",
            title: "Người hướng dẫn tận tâm",
            description: "Chấm điểm và phản hồi 10 bài tập của sinh viên trong tuần",
            category: .assessment,
            type: .weekly,
            status: .active,
            difficulty: .easy,
            xpReward: 100,
            coinReward: 50,
            badgeReward: nil,
            requirements: [
                MissionRequirement(
                    id: "req_1",
                    type: .gradeSubmission,
                    description: "Chấm điểm bài tập",
                    targetValue: 10,
                    currentValue: 3,
                    unit: "bài"
                )
            ],
            progress: MissionProgress(
                missionId: "mission_1",
                userId: "user_1",
                completedRequirements: [],
                startedAt: Date(),
                lastUpdatedAt: Date(),
                completedAt: nil,
                claimedAt: nil
            ),
            startDate: Date(),
            endDate: Date().addingTimeInterval(7 * 24 * 3600),
            isRecurring: true,
            recurringPattern: RecurringPattern(
                frequency: .weekly,
                interval: 1,
                daysOfWeek: nil,
                dayOfMonth: nil,
                endAfterOccurrences: nil
            ),
            tags: ["grading", "weekly"],
            metadata: nil,
            createdAt: Date(),
            updatedAt: nil
        ),
        Mission(
            id: "mission_2",
            title: "Người kết nối",
            description: "Tương tác với sinh viên qua thông báo và phản hồi",
            category: .engagement,
            type: .daily,
            status: .active,
            difficulty: .easy,
            xpReward: 50,
            coinReward: 20,
            badgeReward: nil,
            requirements: [
                MissionRequirement(
                    id: "req_2",
                    type: .postAnnouncement,
                    description: "Đăng thông báo",
                    targetValue: 1,
                    currentValue: 0,
                    unit: "thông báo"
                ),
                MissionRequirement(
                    id: "req_3",
                    type: .replyStudent,
                    description: "Phản hồi câu hỏi",
                    targetValue: 3,
                    currentValue: 1,
                    unit: "câu trả lời"
                )
            ],
            progress: MissionProgress(
                missionId: "mission_2",
                userId: "user_1",
                completedRequirements: [],
                startedAt: Date(),
                lastUpdatedAt: Date(),
                completedAt: nil,
                claimedAt: nil
            ),
            startDate: Date(),
            endDate: Date().addingTimeInterval(24 * 3600),
            isRecurring: true,
            recurringPattern: RecurringPattern(
                frequency: .daily,
                interval: 1,
                daysOfWeek: nil,
                dayOfMonth: nil,
                endAfterOccurrences: nil
            ),
            tags: ["engagement", "daily"],
            metadata: nil,
            createdAt: Date(),
            updatedAt: nil
        ),
        Mission(
            id: "mission_3",
            title: "Nhà đổi mới sáng tạo",
            description: "Tạo quiz tương tác và sử dụng tính năng mới trong giảng dạy",
            category: .innovation,
            type: .monthly,
            status: .active,
            difficulty: .medium,
            xpReward: 300,
            coinReward: 150,
            badgeReward: "innovator_badge",
            requirements: [
                MissionRequirement(
                    id: "req_4",
                    type: .createQuiz,
                    description: "Tạo quiz mới",
                    targetValue: 3,
                    currentValue: 1,
                    unit: "quiz"
                ),
                MissionRequirement(
                    id: "req_5",
                    type: .useNewFeature,
                    description: "Sử dụng tính năng mới",
                    targetValue: 5,
                    currentValue: 2,
                    unit: "lần"
                )
            ],
            progress: MissionProgress(
                missionId: "mission_3",
                userId: "user_1",
                completedRequirements: [],
                startedAt: Date(),
                lastUpdatedAt: Date(),
                completedAt: nil,
                claimedAt: nil
            ),
            startDate: Date(),
            endDate: Date().addingTimeInterval(30 * 24 * 3600),
            isRecurring: false,
            recurringPattern: nil,
            tags: ["innovation", "monthly"],
            metadata: nil,
            createdAt: Date(),
            updatedAt: nil
        )
    ]
}
