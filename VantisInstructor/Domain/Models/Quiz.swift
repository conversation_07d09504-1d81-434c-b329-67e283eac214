//
//  Quiz.swift
//  mobile-app-template
//
//  Created by Inst<PERSON>ctor App on 24/7/25.
//

import Foundation

// MARK: - Quiz Model
struct Quiz: Codable, Identifiable {
    let id: Int
    let name: String
    let code: String
    let description: String?
    let quizType: QuizType
    let subjectId: Int?
    let subjectName: String?
    let classId: Int?
    let className: String?
    let maxScore: Double
    let passingScore: Double
    let timeLimit: Int?
    let maxAttempts: Int?
    let isRandomized: Bool?
    let showCorrectAnswers: Bool?
    let state: QuizState
    let questionCount: Int
    let studentCount: Int
    let attemptCount: Int
    let averageScore: Double
    let passRate: Double
    let pendingGradingCount: Int
    let startDate: Date?
    let endDate: Date?
    let createdAt: Date?
    let updatedAt: Date?

    // Memberwise initializer for creating instances manually
    init(
        id: Int,
        name: String,
        code: String,
        description: String? = nil,
        quizType: QuizType,
        subjectId: Int? = nil,
        subjectName: String? = nil,
        classId: Int? = nil,
        className: String? = nil,
        maxScore: Double,
        passingScore: Double,
        timeLimit: Int? = nil,
        maxAttempts: Int? = nil,
        isRandomized: Bool? = nil,
        showCorrectAnswers: Bool? = nil,
        state: QuizState,
        questionCount: Int,
        studentCount: Int,
        attemptCount: Int,
        averageScore: Double,
        passRate: Double,
        pendingGradingCount: Int,
        startDate: Date? = nil,
        endDate: Date? = nil,
        createdAt: Date? = nil,
        updatedAt: Date? = nil
    ) {
        self.id = id
        self.name = name
        self.code = code
        self.description = description
        self.quizType = quizType
        self.subjectId = subjectId
        self.subjectName = subjectName
        self.classId = classId
        self.className = className
        self.maxScore = maxScore
        self.passingScore = passingScore
        self.timeLimit = timeLimit
        self.maxAttempts = maxAttempts
        self.isRandomized = isRandomized
        self.showCorrectAnswers = showCorrectAnswers
        self.state = state
        self.questionCount = questionCount
        self.studentCount = studentCount
        self.attemptCount = attemptCount
        self.averageScore = averageScore
        self.passRate = passRate
        self.pendingGradingCount = pendingGradingCount
        self.startDate = startDate
        self.endDate = endDate
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }

    // Custom date decoder to handle server date format
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        id = try container.decode(Int.self, forKey: .id)
        name = try container.decode(String.self, forKey: .name)
        code = try container.decode(String.self, forKey: .code)
        description = try container.decodeIfPresent(String.self, forKey: .description)
        quizType = try container.decode(QuizType.self, forKey: .quizType)
        subjectId = try container.decodeIfPresent(Int.self, forKey: .subjectId)
        subjectName = try container.decodeIfPresent(String.self, forKey: .subjectName)
        classId = try container.decodeIfPresent(Int.self, forKey: .classId)
        className = try container.decodeIfPresent(String.self, forKey: .className)
        maxScore = try container.decode(Double.self, forKey: .maxScore)
        passingScore = try container.decode(Double.self, forKey: .passingScore)
        timeLimit = try container.decodeIfPresent(Int.self, forKey: .timeLimit)
        maxAttempts = try container.decodeIfPresent(Int.self, forKey: .maxAttempts)
        isRandomized = try container.decodeIfPresent(Bool.self, forKey: .isRandomized)
        showCorrectAnswers = try container.decodeIfPresent(Bool.self, forKey: .showCorrectAnswers)
        state = try container.decode(QuizState.self, forKey: .state)
        questionCount = try container.decode(Int.self, forKey: .questionCount)
        studentCount = try container.decode(Int.self, forKey: .studentCount)
        attemptCount = try container.decode(Int.self, forKey: .attemptCount)
        averageScore = try container.decode(Double.self, forKey: .averageScore)
        passRate = try container.decode(Double.self, forKey: .passRate)
        pendingGradingCount = try container.decode(Int.self, forKey: .pendingGradingCount)

        // Custom date parsing
        startDate = Self.decodeDate(from: container, forKey: .startDate)
        endDate = Self.decodeDate(from: container, forKey: .endDate)
        createdAt = Self.decodeDate(from: container, forKey: .createdAt)
        updatedAt = Self.decodeDate(from: container, forKey: .updatedAt)
    }

    private static func decodeDate(from container: KeyedDecodingContainer<CodingKeys>, forKey key: CodingKeys) -> Date? {
        guard let dateString = try? container.decodeIfPresent(String.self, forKey: key) else {
            return nil
        }

        // Try multiple date formats
        // Server format: "2025-06-18T18:04:34.887000"
        let serverFormatter = DateFormatter()
        serverFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSSSS"
        serverFormatter.timeZone = TimeZone(secondsFromGMT: 0)

        if let date = serverFormatter.date(from: dateString) {
            return date
        }

        // ISO8601 standard
        let iso8601Formatter = ISO8601DateFormatter()
        iso8601Formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]

        if let date = iso8601Formatter.date(from: dateString) {
            return date
        }

        // Fallback without fractional seconds
        let fallbackFormatter = DateFormatter()
        fallbackFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
        fallbackFormatter.timeZone = TimeZone(secondsFromGMT: 0)

        if let date = fallbackFormatter.date(from: dateString) {
            return date
        }

        print("⚠️ Failed to parse date: \(dateString)")
        return nil
    }

    enum CodingKeys: String, CodingKey {
        case id, name, code, description
        case quizType = "quiz_type"
        case subjectId = "subject_id"
        case subjectName = "subject_name"
        case classId = "class_id"
        case className = "class_name"
        case maxScore = "max_score"
        case passingScore = "passing_score"
        case timeLimit = "time_limit"
        case maxAttempts = "max_attempts"
        case isRandomized = "is_randomized"
        case showCorrectAnswers = "show_correct_answers"
        case state
        case questionCount = "question_count"
        case studentCount = "student_count"
        case attemptCount = "attempt_count"
        case averageScore = "average_score"
        case passRate = "pass_rate"
        case pendingGradingCount = "pending_grading_count"
        case startDate = "start_date"
        case endDate = "end_date"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
    
    // Computed properties
    var isActive: Bool {
        guard let startDate = startDate, let endDate = endDate else {
            return state == .published
        }
        let now = Date()
        return state == .published && now >= startDate && now <= endDate
    }
    
    var isExpired: Bool {
        guard let endDate = endDate else { return false }
        return Date() > endDate
    }
    
    var timeRemainingText: String {
        guard let endDate = endDate, !isExpired else { return "Đã hết hạn" }
        
        let formatter = DateComponentsFormatter()
        formatter.allowedUnits = [.day, .hour, .minute]
        formatter.unitsStyle = .abbreviated
        
        return formatter.string(from: Date(), to: endDate) ?? "Đã hết hạn"
    }
    
    var completionRate: Double {
        guard studentCount > 0 else { return 0 }
        return Double(attemptCount) / Double(studentCount) * 100
    }
    
    var gradingProgress: Double {
        guard attemptCount > 0 else { return 0 }
        let gradedCount = attemptCount - pendingGradingCount
        return Double(gradedCount) / Double(attemptCount) * 100
    }
}

// MARK: - Quiz Type
enum QuizType: String, Codable, CaseIterable {
    case quiz = "quiz"
    case assignment = "assignment"
    case midterm = "midterm"
    case final = "final"
    case project = "project"
    
    var displayName: String {
        switch self {
        case .quiz: return "Kiểm tra"
        case .assignment: return "Bài tập"
        case .midterm: return "Giữa kỳ"
        case .final: return "Cuối kỳ"
        case .project: return "Dự án"
        }
    }
    
    var icon: String {
        switch self {
        case .quiz: return "questionmark.circle"
        case .assignment: return "doc.text"
        case .midterm: return "doc.text.fill"
        case .final: return "graduationcap"
        case .project: return "folder"
        }
    }
    
    var color: String {
        switch self {
        case .quiz: return "blue"
        case .assignment: return "green"
        case .midterm: return "orange"
        case .final: return "red"
        case .project: return "purple"
        }
    }
}

// MARK: - Quiz State
enum QuizState: String, Codable, CaseIterable {
    case draft = "draft"
    case ready = "ready"
    case published = "published"
    case archived = "archived"
    
    var displayName: String {
        switch self {
        case .draft: return "Nháp"
        case .ready: return "Sẵn sàng"
        case .published: return "Đã phát hành"
        case .archived: return "Lưu trữ"
        }
    }
    
    var color: String {
        switch self {
        case .draft: return "gray"
        case .ready: return "blue"
        case .published: return "green"
        case .archived: return "brown"
        }
    }
}

// MARK: - Question Model
struct Question: Codable, Identifiable {
    let id: Int?
    let text: String
    let questionType: QuestionType
    let score: Double
    let answers: [Answer]
    
    var hasCorrectAnswer: Bool {
        return answers.contains { $0.isCorrect }
    }
}

// MARK: - Question Type
enum QuestionType: String, Codable, CaseIterable {
    case singleChoice = "single_choice"
    case multipleChoice = "multiple_choice"
    case trueFalse = "true_false"
    case essay = "essay"
    
    var displayName: String {
        switch self {
        case .singleChoice: return "Trắc nghiệm đơn"
        case .multipleChoice: return "Trắc nghiệm nhiều đáp án"
        case .trueFalse: return "Đúng/Sai"
        case .essay: return "Tự luận"
        }
    }
    
    var icon: String {
        switch self {
        case .singleChoice: return "circle"
        case .multipleChoice: return "checkmark.square"
        case .trueFalse: return "checkmark.circle"
        case .essay: return "text.alignleft"
        }
    }
    
    var requiresAnswers: Bool {
        return self != .essay
    }
}

// MARK: - Answer Model
struct Answer: Codable, Identifiable {
    let id: Int?
    let text: String
    let isCorrect: Bool
}
