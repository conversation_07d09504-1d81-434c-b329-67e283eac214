//
//  Student.swift
//  mobile-app-template
//
//  Created by In<PERSON><PERSON><PERSON> App on 23/7/25.
//

import Foundation

// MARK: - Student Model
struct Student: Codable, Identifiable {
    let id: String
    let studentId: String // Student ID number
    let email: String
    let firstName: String
    let lastName: String
    let phone: String?
    let avatar: String?
    let dateOfBirth: Date?
    let gender: Gender?
    let address: String?
    let emergencyContact: EmergencyContact?
    let enrollmentDate: Date
    let status: StudentStatus
    let gpa: Double?
    let totalCredits: Int
    let completedCredits: Int
    let major: String?
    let year: AcademicYear?
    let courses: [StudentCourse]?
    let metadata: [String: AnyCodable]?
    let createdAt: Date
    let updatedAt: Date?
    
    // Computed properties
    var displayName: String {
        return "\(firstName) \(lastName)"
    }
    
    var isActive: Bool {
        return status == .active
    }
    
    var progressPercentage: Double {
        guard totalCredits > 0 else { return 0 }
        return Double(completedCredits) / Double(totalCredits) * 100
    }
    
    var academicStanding: AcademicStanding {
        guard let gpa = gpa else { return .unknown }
        
        switch gpa {
        case 3.5...4.0: return .excellent
        case 3.0..<3.5: return .good
        case 2.5..<3.0: return .satisfactory
        case 2.0..<2.5: return .probation
        default: return .failing
        }
    }
}

// MARK: - Gender
enum Gender: String, Codable, CaseIterable {
    case male = "MALE"
    case female = "FEMALE"
    case other = "OTHER"
    case preferNotToSay = "PREFER_NOT_TO_SAY"
    
    var displayName: String {
        switch self {
        case .male: return "Nam"
        case .female: return "Nữ"
        case .other: return "Khác"
        case .preferNotToSay: return "Không muốn tiết lộ"
        }
    }
}

// MARK: - Student Status
enum StudentStatus: String, Codable, CaseIterable {
    case active = "ACTIVE"
    case inactive = "INACTIVE"
    case graduated = "GRADUATED"
    case suspended = "SUSPENDED"
    case transferred = "TRANSFERRED"
    case dropped = "DROPPED"
    
    var displayName: String {
        switch self {
        case .active: return "Đang học"
        case .inactive: return "Tạm nghỉ"
        case .graduated: return "Đã tốt nghiệp"
        case .suspended: return "Bị đình chỉ"
        case .transferred: return "Chuyển trường"
        case .dropped: return "Bỏ học"
        }
    }
    
    var color: String {
        switch self {
        case .active: return "green"
        case .inactive: return "orange"
        case .graduated: return "blue"
        case .suspended: return "red"
        case .transferred: return "purple"
        case .dropped: return "gray"
        }
    }
}

// MARK: - Academic Year
enum AcademicYear: String, Codable, CaseIterable {
    case freshman = "FRESHMAN"
    case sophomore = "SOPHOMORE"
    case junior = "JUNIOR"
    case senior = "SENIOR"
    case graduate = "GRADUATE"
    
    var displayName: String {
        switch self {
        case .freshman: return "Năm 1"
        case .sophomore: return "Năm 2"
        case .junior: return "Năm 3"
        case .senior: return "Năm 4"
        case .graduate: return "Sau đại học"
        }
    }
    
    var order: Int {
        switch self {
        case .freshman: return 1
        case .sophomore: return 2
        case .junior: return 3
        case .senior: return 4
        case .graduate: return 5
        }
    }
}

// MARK: - Academic Standing
enum AcademicStanding: String, Codable, CaseIterable {
    case excellent = "EXCELLENT"
    case good = "GOOD"
    case satisfactory = "SATISFACTORY"
    case probation = "PROBATION"
    case failing = "FAILING"
    case unknown = "UNKNOWN"
    
    var displayName: String {
        switch self {
        case .excellent: return "Xuất sắc"
        case .good: return "Giỏi"
        case .satisfactory: return "Khá"
        case .probation: return "Cảnh báo"
        case .failing: return "Yếu"
        case .unknown: return "Chưa xác định"
        }
    }
    
    var color: String {
        switch self {
        case .excellent: return "green"
        case .good: return "blue"
        case .satisfactory: return "orange"
        case .probation: return "yellow"
        case .failing: return "red"
        case .unknown: return "gray"
        }
    }
}

// MARK: - Emergency Contact
struct EmergencyContact: Codable {
    let name: String
    let relationship: String
    let phone: String
    let email: String?
    let address: String?
}

// MARK: - Student Course
struct StudentCourse: Codable, Identifiable {
    let id: String
    let courseId: String
    let courseName: String
    let courseCode: String
    let credits: Int
    let semester: String
    let academicYear: String
    let enrollmentDate: Date
    let status: CourseEnrollmentStatus
    let currentGrade: Double?
    let finalGrade: String?
    let attendanceRate: Double?
    let assignmentScores: [AssignmentScore]?
    let examScores: [ExamScore]?
}

// MARK: - Course Enrollment Status
enum CourseEnrollmentStatus: String, Codable, CaseIterable {
    case enrolled = "ENROLLED"
    case completed = "COMPLETED"
    case dropped = "DROPPED"
    case failed = "FAILED"
    case incomplete = "INCOMPLETE"
    
    var displayName: String {
        switch self {
        case .enrolled: return "Đang học"
        case .completed: return "Hoàn thành"
        case .dropped: return "Bỏ học"
        case .failed: return "Trượt"
        case .incomplete: return "Chưa hoàn thành"
        }
    }
}

// MARK: - Assignment Score
struct AssignmentScore: Codable, Identifiable {
    let id: String
    let assignmentId: String
    let assignmentName: String
    let score: Double
    let maxScore: Double
    let submittedAt: Date?
    let gradedAt: Date?
    let feedback: String?
    
    var percentage: Double {
        guard maxScore > 0 else { return 0 }
        return (score / maxScore) * 100
    }
}

// MARK: - Exam Score
struct ExamScore: Codable, Identifiable {
    let id: String
    let examId: String
    let examName: String
    let examType: ExamType
    let score: Double
    let maxScore: Double
    let examDate: Date
    let gradedAt: Date?
    
    var percentage: Double {
        guard maxScore > 0 else { return 0 }
        return (score / maxScore) * 100
    }
}

// MARK: - Exam Type
enum ExamType: String, Codable, CaseIterable {
    case quiz = "QUIZ"
    case midterm = "MIDTERM"
    case final = "FINAL"
    case practical = "PRACTICAL"
    
    var displayName: String {
        switch self {
        case .quiz: return "Kiểm tra"
        case .midterm: return "Giữa kỳ"
        case .final: return "Cuối kỳ"
        case .practical: return "Thực hành"
        }
    }
}

// MARK: - Student Extensions
extension Student {
    static let mockStudents: [Student] = [
        Student(
            id: "student_1",
            studentId: "2024001",
            email: "<EMAIL>",
            firstName: "Nguyễn",
            lastName: "Văn B",
            phone: "+84901234567",
            avatar: nil,
            dateOfBirth: Date().addingTimeInterval(-20 * 365 * 24 * 3600), // 20 years ago
            gender: .male,
            address: "123 Đường XYZ, Quận 1, TP.HCM",
            emergencyContact: EmergencyContact(
                name: "Nguyễn Thị C",
                relationship: "Mẹ",
                phone: "+84907654321",
                email: "<EMAIL>",
                address: "123 Đường XYZ, Quận 1, TP.HCM"
            ),
            enrollmentDate: Date().addingTimeInterval(-2 * 365 * 24 * 3600), // 2 years ago
            status: .active,
            gpa: 3.75,
            totalCredits: 120,
            completedCredits: 80,
            major: "Computer Science",
            year: .junior,
            courses: nil,
            metadata: nil,
            createdAt: Date().addingTimeInterval(-2 * 365 * 24 * 3600),
            updatedAt: Date()
        ),
        Student(
            id: "student_2",
            studentId: "2024002",
            email: "<EMAIL>",
            firstName: "Trần",
            lastName: "Thị D",
            phone: "+84902345678",
            avatar: nil,
            dateOfBirth: Date().addingTimeInterval(-19 * 365 * 24 * 3600), // 19 years ago
            gender: .female,
            address: "456 Đường ABC, Quận 2, TP.HCM",
            emergencyContact: EmergencyContact(
                name: "Trần Văn E",
                relationship: "Bố",
                phone: "+84908765432",
                email: "<EMAIL>",
                address: "456 Đường ABC, Quận 2, TP.HCM"
            ),
            enrollmentDate: Date().addingTimeInterval(-2 * 365 * 24 * 3600), // 2 years ago
            status: .active,
            gpa: 3.85,
            totalCredits: 120,
            completedCredits: 85,
            major: "Computer Science",
            year: .junior,
            courses: nil,
            metadata: nil,
            createdAt: Date().addingTimeInterval(-2 * 365 * 24 * 3600),
            updatedAt: Date()
        )
    ]
}
