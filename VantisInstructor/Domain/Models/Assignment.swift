//
//  Assignment.swift
//  mobile-app-template
//
//  Created by Instructor App on 23/7/25.
//

import Foundation

// MARK: - Assignment Model
struct Assignment: Codable, Identifiable {
    let id: String
    let courseId: String
    let courseName: String
    let courseCode: String
    let classId: String?
    let title: String
    let description: String
    let instructions: String?
    let type: AssignmentType
    let status: AssignmentStatus
    let maxScore: Double
    let weight: Double // Percentage of total grade
    let difficulty: DifficultyLevel
    let estimatedHours: Int?
    let assignedDate: Date
    let dueDate: Date
    let submissionMethod: SubmissionMethod
    let allowLateSubmission: Bool
    let latePenalty: Double? // Percentage deduction per day
    let maxLateDays: Int?
    let rubric: AssignmentRubric?
    let attachments: [AssignmentAttachment]?
    let tags: [String]?
    let totalSubmissions: Int
    let gradedSubmissions: Int
    let averageScore: Double?
    let instructorId: String
    let instructorName: String
    let metadata: [String: AnyCodable]?
    let createdAt: Date
    let updatedAt: Date?
    
    // Computed properties
    var isOverdue: Bool {
        return Date() > dueDate && status != .completed
    }
    
    var isDueSoon: Bool {
        let timeUntilDue = dueDate.timeIntervalSince(Date())
        return timeUntilDue > 0 && timeUntilDue <= 24 * 3600 // 24 hours
    }
    
    var submissionRate: Double {
        guard totalSubmissions > 0 else { return 0 }
        return Double(gradedSubmissions) / Double(totalSubmissions) * 100
    }
    
    var timeRemaining: String {
        let formatter = DateComponentsFormatter()
        formatter.allowedUnits = [.day, .hour, .minute]
        formatter.unitsStyle = .abbreviated
        
        if Date() < dueDate {
            return formatter.string(from: Date(), to: dueDate) ?? ""
        }
        return "Quá hạn"
    }
    
    var formattedDueDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: dueDate)
    }
}

// MARK: - Assignment Type
enum AssignmentType: String, Codable, CaseIterable {
    case homework = "HOMEWORK"
    case project = "PROJECT"
    case essay = "ESSAY"
    case quiz = "QUIZ"
    case exam = "EXAM"
    case presentation = "PRESENTATION"
    case lab = "LAB"
    case research = "RESEARCH"
    case groupWork = "GROUP_WORK"
    case practicum = "PRACTICUM"
    
    var displayName: String {
        switch self {
        case .homework: return "Bài tập về nhà"
        case .project: return "Dự án"
        case .essay: return "Tiểu luận"
        case .quiz: return "Kiểm tra"
        case .exam: return "Thi"
        case .presentation: return "Thuyết trình"
        case .lab: return "Thực hành"
        case .research: return "Nghiên cứu"
        case .groupWork: return "Nhóm"
        case .practicum: return "Thực tập"
        }
    }
    
    var icon: String {
        switch self {
        case .homework: return "book"
        case .project: return "folder"
        case .essay: return "doc.text"
        case .quiz: return "questionmark.circle"
        case .exam: return "doc.text.fill"
        case .presentation: return "presentation"
        case .lab: return "flask"
        case .research: return "magnifyingglass"
        case .groupWork: return "person.3"
        case .practicum: return "briefcase"
        }
    }
    
    var color: String {
        switch self {
        case .homework: return "blue"
        case .project: return "green"
        case .essay: return "purple"
        case .quiz: return "orange"
        case .exam: return "red"
        case .presentation: return "indigo"
        case .lab: return "teal"
        case .research: return "brown"
        case .groupWork: return "pink"
        case .practicum: return "gray"
        }
    }
}

// MARK: - Assignment Status
enum AssignmentStatus: String, Codable, CaseIterable {
    case draft = "DRAFT"
    case published = "PUBLISHED"
    case active = "ACTIVE"
    case completed = "COMPLETED"
    case cancelled = "CANCELLED"
    case archived = "ARCHIVED"
    
    var displayName: String {
        switch self {
        case .draft: return "Nháp"
        case .published: return "Đã phát hành"
        case .active: return "Đang hoạt động"
        case .completed: return "Hoàn thành"
        case .cancelled: return "Đã hủy"
        case .archived: return "Lưu trữ"
        }
    }
    
    var color: String {
        switch self {
        case .draft: return "gray"
        case .published: return "blue"
        case .active: return "green"
        case .completed: return "purple"
        case .cancelled: return "red"
        case .archived: return "brown"
        }
    }
}

// MARK: - Difficulty Level
enum DifficultyLevel: String, Codable, CaseIterable {
    case beginner = "BEGINNER"
    case intermediate = "INTERMEDIATE"
    case advanced = "ADVANCED"
    case expert = "EXPERT"
    
    var displayName: String {
        switch self {
        case .beginner: return "Cơ bản"
        case .intermediate: return "Trung bình"
        case .advanced: return "Nâng cao"
        case .expert: return "Chuyên gia"
        }
    }
    
    var color: String {
        switch self {
        case .beginner: return "green"
        case .intermediate: return "yellow"
        case .advanced: return "orange"
        case .expert: return "red"
        }
    }
    
    var stars: Int {
        switch self {
        case .beginner: return 1
        case .intermediate: return 2
        case .advanced: return 3
        case .expert: return 4
        }
    }
}

// MARK: - Submission Method
enum SubmissionMethod: String, Codable, CaseIterable {
    case online = "ONLINE"
    case email = "EMAIL"
    case hardCopy = "HARD_COPY"
    case presentation = "PRESENTATION"
    case github = "GITHUB"
    case googleDrive = "GOOGLE_DRIVE"
    
    var displayName: String {
        switch self {
        case .online: return "Trực tuyến"
        case .email: return "Email"
        case .hardCopy: return "Bản cứng"
        case .presentation: return "Thuyết trình"
        case .github: return "GitHub"
        case .googleDrive: return "Google Drive"
        }
    }
    
    var icon: String {
        switch self {
        case .online: return "globe"
        case .email: return "envelope"
        case .hardCopy: return "printer"
        case .presentation: return "presentation"
        case .github: return "chevron.left.forwardslash.chevron.right"
        case .googleDrive: return "icloud"
        }
    }
}

// MARK: - Assignment Rubric
struct AssignmentRubric: Codable {
    let criteria: [RubricCriterion]
    let totalPoints: Double
    let description: String?
}

// MARK: - Rubric Criterion
struct RubricCriterion: Codable, Identifiable {
    let id: String
    let name: String
    let description: String
    let maxPoints: Double
    let levels: [RubricLevel]
}

// MARK: - Rubric Level
struct RubricLevel: Codable, Identifiable {
    let id: String
    let name: String // Excellent, Good, Satisfactory, Needs Improvement
    let description: String
    let points: Double
}

// MARK: - Assignment Attachment
struct AssignmentAttachment: Codable, Identifiable {
    let id: String
    let fileName: String
    let fileType: String
    let fileSize: Int // bytes
    let url: String
    let description: String?
    let isRequired: Bool
    let uploadedAt: Date
}

// MARK: - Assignment Extensions
extension Assignment {
    static let mockAssignments: [Assignment] = [
        Assignment(
            id: "assignment_1",
            courseId: "course_1",
            courseName: "Lập trình iOS với Swift",
            courseCode: "CS301",
            classId: "class_1",
            title: "Tạo ứng dụng Todo List với SwiftUI",
            description: "Phát triển một ứng dụng Todo List hoàn chỉnh sử dụng SwiftUI và Core Data",
            instructions: "1. Tạo giao diện với SwiftUI\n2. Implement Core Data\n3. Add CRUD operations\n4. Test thoroughly",
            type: .project,
            status: .active,
            maxScore: 100.0,
            weight: 15.0,
            difficulty: .intermediate,
            estimatedHours: 20,
            assignedDate: Date().addingTimeInterval(-7 * 24 * 3600), // 7 days ago
            dueDate: Date().addingTimeInterval(7 * 24 * 3600), // 7 days from now
            submissionMethod: .github,
            allowLateSubmission: true,
            latePenalty: 10.0, // 10% per day
            maxLateDays: 3,
            rubric: nil,
            attachments: [
                AssignmentAttachment(
                    id: "attachment_1",
                    fileName: "TodoApp_Requirements.pdf",
                    fileType: "pdf",
                    fileSize: 1024000,
                    url: "https://example.com/requirements.pdf",
                    description: "Yêu cầu chi tiết cho ứng dụng Todo",
                    isRequired: true,
                    uploadedAt: Date().addingTimeInterval(-7 * 24 * 3600)
                )
            ],
            tags: ["SwiftUI", "Core Data", "iOS"],
            totalSubmissions: 35,
            gradedSubmissions: 10,
            averageScore: 85.5,
            instructorId: "instructor_1",
            instructorName: "Nguyễn Văn A",
            metadata: nil,
            createdAt: Date().addingTimeInterval(-7 * 24 * 3600),
            updatedAt: Date()
        )
    ]
}
