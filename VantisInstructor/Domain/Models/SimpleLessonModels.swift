//
//  SimpleLessonModels.swift
//  mobile-app-template
//
//  Created by Instructor App on 28/7/25.
//

import Foundation

// MARK: - Simple Lesson Models (matching OpenAPI spec exactly)
struct SimpleLessonResponse: Codable {
    let success: Bool
    let message: String
    let data: [SimpleLesson]
    let pagination: SimplePagination
}

struct SimpleLesson: Codable, Identifiable {
    let id: Int
    let name: String
    let lessonNumber: Int?
    let state: String
    let className: String
    let classCode: String
    let startDatetime: String
    let durationHours: Double?
    let room: String?
    let totalStudents: Int
    let presentCount: Int
    let attendanceRate: Double?
    let isToday: Bool
    let isUpcoming: Bool
    let isOverdue: Bool
    let needsAttention: Bool
    
    enum CodingKeys: String, CodingKey {
        case id, name, state, room
        case lessonNumber = "lesson_number"
        case className = "class_name"
        case classCode = "class_code"
        case startDatetime = "start_datetime"
        case durationHours = "duration_hours"
        case totalStudents = "total_students"
        case presentCount = "present_count"
        case attendanceRate = "attendance_rate"
        case isToday = "is_today"
        case isUpcoming = "is_upcoming"
        case isOverdue = "is_overdue"
        case needsAttention = "needs_attention"
    }
    
    // MARK: - Computed Properties
    var startDate: Date {
        let formatter = ISO8601DateFormatter()
        formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
        return formatter.date(from: startDatetime) ?? Date()
    }
    
    var timeString: String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: startDate)
    }
    
    var dateString: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        return formatter.string(from: startDate)
    }
    
    var statusColor: String {
        if isToday { return "blue" }
        if isUpcoming { return "green" }
        if isOverdue { return "red" }
        if needsAttention { return "orange" }
        return "gray"
    }
    
    var statusText: String {
        if isToday { return "Hôm nay" }
        if isUpcoming { return "Sắp tới" }
        if isOverdue { return "Quá hạn" }
        if needsAttention { return "Cần chú ý" }
        return state.capitalized
    }
}

struct SimplePagination: Codable {
    let page: Int
    let pageSize: Int
    let totalItems: Int
    let totalPages: Int
    
    enum CodingKeys: String, CodingKey {
        case page
        case pageSize = "page_size"
        case totalItems = "total_items"
        case totalPages = "total_pages"
    }
}

// MARK: - Mock Data
extension SimpleLesson {
    static let mockLessons: [SimpleLesson] = [
        SimpleLesson(
            id: 1,
            name: "Buổi học GIAO TIẾP & TRUYỀN THÔNG CẤP LÃNH ĐẠO",
            lessonNumber: 1,
            state: "scheduled",
            className: "Lớp Lãnh đạo cấp cao",
            classCode: "LEADER_2025",
            startDatetime: "2025-01-28T09:00:00+07:00",
            durationHours: 2.0,
            room: "Phòng A101",
            totalStudents: 25,
            presentCount: 23,
            attendanceRate: 92.0,
            isToday: true,
            isUpcoming: false,
            isOverdue: false,
            needsAttention: false
        ),
        SimpleLesson(
            id: 2,
            name: "SwiftUI Navigation và State Management",
            lessonNumber: 2,
            state: "scheduled",
            className: "Lớp CS301",
            classCode: "CS301",
            startDatetime: "2025-01-29T13:30:00+07:00",
            durationHours: 1.5,
            room: "Phòng B202",
            totalStudents: 30,
            presentCount: 0,
            attendanceRate: nil,
            isToday: false,
            isUpcoming: true,
            isOverdue: false,
            needsAttention: false
        ),
        SimpleLesson(
            id: 3,
            name: "Database Design Fundamentals",
            lessonNumber: 3,
            state: "completed",
            className: "Lớp DB201",
            classCode: "DB201",
            startDatetime: "2025-01-26T10:00:00+07:00",
            durationHours: 2.5,
            room: "Phòng C303",
            totalStudents: 28,
            presentCount: 26,
            attendanceRate: 92.9,
            isToday: false,
            isUpcoming: false,
            isOverdue: false,
            needsAttention: false
        ),
        SimpleLesson(
            id: 4,
            name: "Mobile App Architecture Patterns",
            lessonNumber: 1,
            state: "scheduled",
            className: "Lớp MA301",
            classCode: "MA301",
            startDatetime: "2025-01-30T14:00:00+07:00",
            durationHours: 3.0,
            room: "Phòng D404",
            totalStudents: 22,
            presentCount: 0,
            attendanceRate: nil,
            isToday: false,
            isUpcoming: true,
            isOverdue: false,
            needsAttention: false
        ),
        SimpleLesson(
            id: 5,
            name: "User Interface Design Principles",
            lessonNumber: 4,
            state: "cancelled",
            className: "Lớp UI201",
            classCode: "UI201",
            startDatetime: "2025-01-25T11:00:00+07:00",
            durationHours: 2.0,
            room: "Phòng E505",
            totalStudents: 20,
            presentCount: 0,
            attendanceRate: nil,
            isToday: false,
            isUpcoming: false,
            isOverdue: true,
            needsAttention: true
        )
    ]
}
