import Foundation

// MARK: - Notification Model
struct AppNotification: Identifiable, Codable {
    let id: String
    let title: String
    let message: String
    let type: NotificationType
    let category: NotificationCategory
    let timestamp: Date
    let isRead: Bool
    let actionData: NotificationActionData?
    let imageUrl: String?
    let priority: NotificationPriority
    
    init(
        id: String = UUID().uuidString,
        title: String,
        message: String,
        type: NotificationType,
        category: NotificationCategory,
        timestamp: Date = Date(),
        isRead: Bool = false,
        actionData: NotificationActionData? = nil,
        imageUrl: String? = nil,
        priority: NotificationPriority = .normal
    ) {
        self.id = id
        self.title = title
        self.message = message
        self.type = type
        self.category = category
        self.timestamp = timestamp
        self.isRead = isRead
        self.actionData = actionData
        self.imageUrl = imageUrl
        self.priority = priority
    }
}

// MARK: - Notification Type
enum NotificationType: String, CaseIterable, Codable {
    case transaction = "transaction"
    case itemUpdated = "item_updated"
    case merchant = "merchant"
    case system = "system"
    case promotion = "promotion"
    case security = "security"
    case update = "update"
    
    var displayName: String {
        switch self {
        case .transaction: return "Transaction"
        case .itemUpdated: return "Item"
        case .merchant: return "Merchant"
        case .system: return "System"
        case .promotion: return "Promotion"
        case .security: return "Security"
        case .update: return "Update"
        }
    }
    
    var icon: String {
        switch self {
        case .transaction: return "creditcard"
        case .itemUpdated: return "gift"
        case .merchant: return "storefront"
        case .system: return "gear"
        case .promotion: return "megaphone"
        case .security: return "shield"
        case .update: return "arrow.down.circle"
        }
    }
    
    var color: String {
        switch self {
        case .transaction: return "blue"
        case .itemUpdated: return "green"
        case .merchant: return "orange"
        case .system: return "gray"
        case .promotion: return "purple"
        case .security: return "red"
        case .update: return "indigo"
        }
    }
}

// MARK: - Notification Category
enum NotificationCategory: String, CaseIterable, Codable {
    case all = "all"
    case unread = "unread"
    case today = "today"
    case thisWeek = "this_week"
    case important = "important"
    
    var displayName: String {
        switch self {
        case .all: return "All"
        case .unread: return "Unread"
        case .today: return "Today"
        case .thisWeek: return "This Week"
        case .important: return "Important"
        }
    }
}

// MARK: - Notification Priority
enum NotificationPriority: String, Codable {
    case low = "low"
    case normal = "normal"
    case high = "high"
    case urgent = "urgent"
    
    var displayName: String {
        switch self {
        case .low: return "Low"
        case .normal: return "Normal"
        case .high: return "High"
        case .urgent: return "Urgent"
        }
    }
}

// MARK: - Notification Action Data
struct NotificationActionData: Codable {
    let actionType: NotificationActionType
    let targetId: String?
    let url: String?
    let parameters: [String: String]?
    
    init(
        actionType: NotificationActionType,
        targetId: String? = nil,
        url: String? = nil,
        parameters: [String: String]? = nil
    ) {
        self.actionType = actionType
        self.targetId = targetId
        self.url = url
        self.parameters = parameters
    }
}

// MARK: - Notification Action Type
enum NotificationActionType: String, Codable {
    case openTransaction = "open_transaction"
    case openReward = "open_reward"
    case openMerchant = "open_merchant"
    case openUrl = "open_url"
    case openSettings = "open_settings"
    case openWallet = "open_wallet"
    case none = "none"
}

// MARK: - Notification Settings
struct NotificationSettings: Codable {
    var pushNotificationsEnabled: Bool
    var transactionNotifications: Bool
    var rewardNotifications: Bool
    var merchantNotifications: Bool
    var promotionNotifications: Bool
    var securityNotifications: Bool
    var systemNotifications: Bool
    var soundEnabled: Bool
    var vibrationEnabled: Bool
    var quietHoursEnabled: Bool
    var quietHoursStart: String // "22:00"
    var quietHoursEnd: String   // "08:00"
    
    init() {
        self.pushNotificationsEnabled = true
        self.transactionNotifications = true
        self.rewardNotifications = true
        self.merchantNotifications = true
        self.promotionNotifications = true
        self.securityNotifications = true
        self.systemNotifications = true
        self.soundEnabled = true
        self.vibrationEnabled = true
        self.quietHoursEnabled = false
        self.quietHoursStart = "22:00"
        self.quietHoursEnd = "08:00"
    }
}

// MARK: - Extensions
extension AppNotification {
    var isToday: Bool {
        Calendar.current.isDateInToday(timestamp)
    }
    
    var isThisWeek: Bool {
        let calendar = Calendar.current
        let now = Date()
        let weekAgo = calendar.date(byAdding: .day, value: -7, to: now) ?? now
        return timestamp >= weekAgo
    }
    
    var isImportant: Bool {
        priority == .high || priority == .urgent || type == .security
    }
    
    var timeAgo: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: timestamp, relativeTo: Date())
    }
    
    var formattedDate: String {
        let formatter = DateFormatter()
        if Calendar.current.isDateInToday(timestamp) {
            formatter.dateFormat = "HH:mm"
        } else if Calendar.current.isDateInYesterday(timestamp) {
            return "Yesterday"
        } else if isThisWeek {
            formatter.dateFormat = "EEEE"
        } else {
            formatter.dateFormat = "MMM d"
        }
        return formatter.string(from: timestamp)
    }
}
