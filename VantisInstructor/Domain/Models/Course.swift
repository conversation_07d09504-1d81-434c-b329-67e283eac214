//
//  Course.swift
//  mobile-app-template
//
//  Created by Instructor App on 23/7/25.
//

import Foundation

// MARK: - Course Model
struct Course: Codable, Identifiable {
    let id: String
    let name: String
    let code: String
    let description: String?
    let credits: Int
    let semester: String
    let academicYear: String
    let department: String?
    let instructorId: String
    let instructorName: String
    let status: CourseStatus
    let startDate: Date
    let endDate: Date
    let totalClasses: Int
    let completedClasses: Int
    let totalStudents: Int
    let activeStudents: Int
    let syllabus: String?
    let objectives: [String]?
    let prerequisites: [String]?
    let textbooks: [Textbook]?
    let gradingPolicy: GradingPolicy?
    let metadata: [String: AnyCodable]?
    let createdAt: Date
    let updatedAt: Date?
    
    // Computed properties
    var progressPercentage: Double {
        guard totalClasses > 0 else { return 0 }
        return Double(completedClasses) / Double(totalClasses) * 100
    }
    
    var isActive: Bool {
        return status == .active
    }
    
    var attendanceRate: Double {
        guard totalStudents > 0 else { return 0 }
        return Double(activeStudents) / Double(totalStudents) * 100
    }
}

// MARK: - Course Status
enum CourseStatus: String, Codable, CaseIterable {
    case draft = "DRAFT"
    case active = "ACTIVE"
    case completed = "COMPLETED"
    case cancelled = "CANCELLED"
    case archived = "ARCHIVED"
    
    var displayName: String {
        switch self {
        case .draft: return "Nháp"
        case .active: return "Đang diễn ra"
        case .completed: return "Hoàn thành"
        case .cancelled: return "Đã hủy"
        case .archived: return "Lưu trữ"
        }
    }
    
    var color: String {
        switch self {
        case .draft: return "gray"
        case .active: return "green"
        case .completed: return "blue"
        case .cancelled: return "red"
        case .archived: return "purple"
        }
    }
}

// MARK: - Textbook
struct Textbook: Codable, Identifiable {
    let id: String
    let title: String
    let author: String
    let isbn: String?
    let edition: String?
    let publisher: String?
    let isRequired: Bool
    let price: Double?
    let description: String?
}

// MARK: - Grading Policy
struct GradingPolicy: Codable {
    let attendanceWeight: Double // %
    let assignmentWeight: Double // %
    let midtermWeight: Double // %
    let finalWeight: Double // %
    let participationWeight: Double // %
    let passingGrade: Double
    let gradingScale: [GradeScale]
}

// MARK: - Grade Scale
struct GradeScale: Codable {
    let letter: String // A, B, C, D, F
    let minPercentage: Double
    let maxPercentage: Double
    let gpa: Double
}

// MARK: - Course Extensions
extension Course {
    static let mockCourses: [Course] = [
        Course(
            id: "course_1",
            name: "Lập trình iOS với Swift",
            code: "CS301",
            description: "Khóa học về phát triển ứng dụng iOS sử dụng Swift và SwiftUI",
            credits: 3,
            semester: "Fall 2024",
            academicYear: "2024-2025",
            department: "Computer Science",
            instructorId: "instructor_1",
            instructorName: "Nguyễn Văn A",
            status: .active,
            startDate: Date().addingTimeInterval(-30 * 24 * 3600), // 30 days ago
            endDate: Date().addingTimeInterval(60 * 24 * 3600), // 60 days from now
            totalClasses: 45,
            completedClasses: 15,
            totalStudents: 35,
            activeStudents: 32,
            syllabus: "Khóa học bao gồm Swift basics, SwiftUI, Core Data, Networking...",
            objectives: [
                "Hiểu cơ bản về Swift programming",
                "Phát triển ứng dụng iOS với SwiftUI",
                "Tích hợp API và Core Data"
            ],
            prerequisites: ["CS101", "CS201"],
            textbooks: [
                Textbook(
                    id: "book_1",
                    title: "iOS Programming: The Big Nerd Ranch Guide",
                    author: "Christian Keur",
                    isbn: "978-0135264027",
                    edition: "7th",
                    publisher: "Big Nerd Ranch",
                    isRequired: true,
                    price: 49.99,
                    description: "Comprehensive guide to iOS development"
                )
            ],
            gradingPolicy: GradingPolicy(
                attendanceWeight: 10,
                assignmentWeight: 30,
                midtermWeight: 25,
                finalWeight: 30,
                participationWeight: 5,
                passingGrade: 60,
                gradingScale: [
                    GradeScale(letter: "A", minPercentage: 90, maxPercentage: 100, gpa: 4.0),
                    GradeScale(letter: "B", minPercentage: 80, maxPercentage: 89, gpa: 3.0),
                    GradeScale(letter: "C", minPercentage: 70, maxPercentage: 79, gpa: 2.0),
                    GradeScale(letter: "D", minPercentage: 60, maxPercentage: 69, gpa: 1.0),
                    GradeScale(letter: "F", minPercentage: 0, maxPercentage: 59, gpa: 0.0)
                ]
            ),
            metadata: nil,
            createdAt: Date().addingTimeInterval(-60 * 24 * 3600),
            updatedAt: Date()
        )
    ]
}
