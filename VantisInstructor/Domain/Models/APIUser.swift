//
//  APIUser.swift
//  mobile-app-template
//
//  Created by AI Assistant on 28/7/25.
//

import Foundation

/// User model specifically for API responses from /auth/me endpoint
struct APIUser: Codable {
    let id: Int
    let name: String
    let login: String
    let email: String?
    let isSystem: Bool?
    let partnerId: Int?
    let isAdmin: Bool?
    let groups: [String]?
    
    enum CodingKeys: String, CodingKey {
        case id, name, login, email, groups
        case isSystem = "is_system"
        case partnerId = "partner_id"
        case isAdmin = "is_admin"
    }
    
    /// Convert APIUser to app's User model
    func toUser() -> User {
        let nameComponents = name.components(separatedBy: " ")
        let firstName = nameComponents.first
        let lastName = nameComponents.count > 1 ? nameComponents.dropFirst().joined(separator: " ") : nil
        
        return User(
            id: String(id),
            email: email ?? login,
            firstName: firstName,
            lastName: lastName,
            phone: nil,
            role: .instructor, // Default to instructor for LMS
            isActive: true,
            avatar: nil,
            dateOfBirth: nil,
            lastLoginAt: Date(),
            createdAt: Date(),
            updatedAt: Date(),
            businessName: nil,
            businessId: nil,
            category: nil,
            businessPhone: nil,
            website: nil,
            businessDescription: nil,
            businessStatus: nil,
            onboardedAt: nil
        )
    }
}
