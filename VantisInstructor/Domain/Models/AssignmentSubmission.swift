import Foundation

// MARK: - Assignment Submission Model
struct AssignmentSubmission: Identifiable, Codable {
    let id: String
    let assignmentId: String
    let studentId: String
    let studentName: String
    let studentEmail: String
    let submissionDate: Date
    let content: String
    let attachments: [SubmissionAttachment]
    let score: Double?
    let feedback: String?
    let isLate: Bool
    let gradedDate: Date?
    let gradedBy: String?
    let status: SubmissionStatus
    
    // Computed properties
    var isGraded: Bool {
        return score != nil
    }
    
    var formattedScore: String {
        guard let score = score else { return "Chưa chấm" }
        return String(format: "%.1f", score)
    }
    
    var submissionTimeText: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: submissionDate)
    }
    
    var isOverdue: Bool {
        return isLate
    }
}

// MARK: - Submission Status
enum SubmissionStatus: String, CaseIterable, Codable {
    case submitted = "submitted"
    case graded = "graded"
    case returned = "returned"
    case draft = "draft"
    
    var displayName: String {
        switch self {
        case .submitted:
            return "Đã nộp"
        case .graded:
            return "Đã chấm"
        case .returned:
            return "Đã trả"
        case .draft:
            return "Nháp"
        }
    }
    
    var color: String {
        switch self {
        case .submitted:
            return "blue"
        case .graded:
            return "green"
        case .returned:
            return "purple"
        case .draft:
            return "gray"
        }
    }
}

// MARK: - Submission Attachment
struct SubmissionAttachment: Identifiable, Codable {
    let id: String
    let fileName: String
    let fileSize: Int64
    let fileType: String
    let downloadURL: String?
    let uploadDate: Date
    
    var fileSizeText: String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: fileSize)
    }
    
    var fileIcon: String {
        switch fileType.lowercased() {
        case "pdf":
            return "doc.fill"
        case "doc", "docx":
            return "doc.text.fill"
        case "jpg", "jpeg", "png", "gif":
            return "photo.fill"
        case "mp4", "mov", "avi":
            return "video.fill"
        case "mp3", "wav":
            return "music.note"
        default:
            return "doc.fill"
        }
    }
}

// MARK: - Sample Data
extension AssignmentSubmission {
    static let sampleSubmissions: [AssignmentSubmission] = [
        AssignmentSubmission(
            id: "sub1",
            assignmentId: "assign1",
            studentId: "student1",
            studentName: "Nguyễn Văn An",
            studentEmail: "<EMAIL>",
            submissionDate: Date().addingTimeInterval(-3600 * 24 * 2), // 2 days ago
            content: "Đây là nội dung bài làm của em. Em đã nghiên cứu kỹ về chủ đề và trình bày theo yêu cầu của thầy/cô.",
            attachments: [
                SubmissionAttachment(
                    id: "att1",
                    fileName: "BaiLam_NguyenVanAn.pdf",
                    fileSize: 2048576, // 2MB
                    fileType: "pdf",
                    downloadURL: "https://example.com/files/BaiLam_NguyenVanAn.pdf",
                    uploadDate: Date().addingTimeInterval(-3600 * 24 * 2)
                )
            ],
            score: nil,
            feedback: nil,
            isLate: false,
            gradedDate: nil,
            gradedBy: nil,
            status: .submitted
        ),
        AssignmentSubmission(
            id: "sub2",
            assignmentId: "assign1",
            studentId: "student2",
            studentName: "Trần Thị Bình",
            studentEmail: "<EMAIL>",
            submissionDate: Date().addingTimeInterval(-3600 * 24 * 1), // 1 day ago
            content: "Bài làm của em về chủ đề được giao. Em đã tham khảo nhiều tài liệu và đưa ra những phân tích chi tiết.",
            attachments: [
                SubmissionAttachment(
                    id: "att2",
                    fileName: "BaiLam_TranThiBinh.docx",
                    fileSize: 1536000, // 1.5MB
                    fileType: "docx",
                    downloadURL: "https://example.com/files/BaiLam_TranThiBinh.docx",
                    uploadDate: Date().addingTimeInterval(-3600 * 24 * 1)
                )
            ],
            score: 8.5,
            feedback: "Bài làm tốt, có phân tích sâu sắc. Cần cải thiện phần kết luận.",
            isLate: false,
            gradedDate: Date().addingTimeInterval(-3600 * 12), // 12 hours ago
            gradedBy: "instructor1",
            status: .graded
        ),
        AssignmentSubmission(
            id: "sub3",
            assignmentId: "assign1",
            studentId: "student3",
            studentName: "Lê Văn Cường",
            studentEmail: "<EMAIL>",
            submissionDate: Date().addingTimeInterval(-3600 * 6), // 6 hours ago
            content: "Em xin lỗi vì nộp bài trễ. Đây là bài làm của em về chủ đề được giao.",
            attachments: [],
            score: nil,
            feedback: nil,
            isLate: true,
            gradedDate: nil,
            gradedBy: nil,
            status: .submitted
        )
    ]
}
