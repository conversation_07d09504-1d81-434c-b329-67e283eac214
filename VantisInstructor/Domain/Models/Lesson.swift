//
//  Lesson.swift
//  mobile-app-template
//
//  Created by Instructor App on 27/7/25.
//

import Foundation

// MARK: - Lesson Model
struct Lesson: Codable, Identifiable {
    let id: Int
    let courseId: Int
    let courseName: String
    let courseCode: String
    let title: String
    let description: String?
    let type: LessonType
    let status: LessonStatus
    let scheduledDate: Date
    let startTime: Date?
    let endTime: Date?
    let duration: Int? // minutes
    let instructorId: String
    let instructorName: String
    let totalStudents: Int
    let attendedStudents: Int
    let materials: [LessonMaterial]?
    let assignments: [String]? // Assignment IDs
    let quizzes: [String]? // Quiz IDs
    let recordingUrl: String?
    let notes: String?
    let createdAt: Date
    let updatedAt: Date?
    
    enum CodingKeys: String, CodingKey {
        case id, title, description, type, status, duration, notes
        case courseId = "course_id"
        case courseName = "course_name"
        case courseCode = "course_code"
        case scheduledDate = "scheduled_date"
        case startTime = "start_time"
        case endTime = "end_time"
        case instructorId = "instructor_id"
        case instructorName = "instructor_name"
        case totalStudents = "total_students"
        case attendedStudents = "attended_students"
        case materials, assignments, quizzes
        case recordingUrl = "recording_url"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// MARK: - Lesson Type
enum LessonType: String, Codable, CaseIterable {
    case lecture = "lecture"
    case lab = "lab"
    case seminar = "seminar"
    case workshop = "workshop"
    case exam = "exam"
    case review = "review"
    
    var displayName: String {
        switch self {
        case .lecture: return "Bài giảng"
        case .lab: return "Thực hành"
        case .seminar: return "Hội thảo"
        case .workshop: return "Workshop"
        case .exam: return "Kiểm tra"
        case .review: return "Ôn tập"
        }
    }
    
    var icon: String {
        switch self {
        case .lecture: return "book"
        case .lab: return "flask"
        case .seminar: return "person.3"
        case .workshop: return "hammer"
        case .exam: return "doc.text"
        case .review: return "arrow.clockwise"
        }
    }
}

// MARK: - Lesson Status
enum LessonStatus: String, Codable, CaseIterable {
    case scheduled = "scheduled"
    case inProgress = "in_progress"
    case completed = "completed"
    case cancelled = "cancelled"
    case postponed = "postponed"
    
    var displayName: String {
        switch self {
        case .scheduled: return "Đã lên lịch"
        case .inProgress: return "Đang diễn ra"
        case .completed: return "Hoàn thành"
        case .cancelled: return "Đã hủy"
        case .postponed: return "Hoãn lại"
        }
    }
    
    var color: String {
        switch self {
        case .scheduled: return "blue"
        case .inProgress: return "green"
        case .completed: return "gray"
        case .cancelled: return "red"
        case .postponed: return "orange"
        }
    }
}

// MARK: - Lesson Material
struct LessonMaterial: Codable, Identifiable {
    let id: String
    let title: String
    let type: LessonMaterialType
    let url: String?
    let fileSize: Int?
    let uploadedAt: Date

    enum CodingKeys: String, CodingKey {
        case id, title, type, url
        case fileSize = "file_size"
        case uploadedAt = "uploaded_at"
    }
}

// MARK: - Lesson Material Type
enum LessonMaterialType: String, Codable, CaseIterable {
    case document = "document"
    case presentation = "presentation"
    case video = "video"
    case audio = "audio"
    case image = "image"
    case link = "link"
    
    var displayName: String {
        switch self {
        case .document: return "Tài liệu"
        case .presentation: return "Bài thuyết trình"
        case .video: return "Video"
        case .audio: return "Audio"
        case .image: return "Hình ảnh"
        case .link: return "Liên kết"
        }
    }
    
    var icon: String {
        switch self {
        case .document: return "doc"
        case .presentation: return "play.rectangle"
        case .video: return "video"
        case .audio: return "speaker.wave.2"
        case .image: return "photo"
        case .link: return "link"
        }
    }
}

// MARK: - Lesson List Response
struct LessonListResponse: Codable {
    let success: Bool
    let message: String
    let data: LessonListData
    let traceId: String
    let requestId: String
}

struct LessonListData: Codable {
    let lessons: [Lesson]
    let totalCount: Int
    let totalPages: Int
    let currentPage: Int
    let hasNextPage: Bool
    let hasPreviousPage: Bool
    
    enum CodingKeys: String, CodingKey {
        case lessons
        case totalCount = "total_count"
        case totalPages = "total_pages"
        case currentPage = "current_page"
        case hasNextPage = "has_next_page"
        case hasPreviousPage = "has_previous_page"
    }
}

// MARK: - Lesson Filters
struct LessonFilters: Codable {
    let courseId: Int?
    let type: LessonType?
    let status: LessonStatus?
    let search: String?
    let startDate: Date?
    let endDate: Date?
    
    enum CodingKeys: String, CodingKey {
        case courseId = "course_id"
        case type, status, search
        case startDate = "start_date"
        case endDate = "end_date"
    }
}

// MARK: - Mock Data Extension
extension Lesson {
    static let mockLessons: [Lesson] = [
        Lesson(
            id: 1,
            courseId: 1,
            courseName: "Toán học 10",
            courseCode: "MATH10",
            title: "Chương 1: Đại số cơ bản",
            description: "Giới thiệu về đại số và các phép toán cơ bản",
            type: .lecture,
            status: .completed,
            scheduledDate: Date().addingTimeInterval(-86400), // 1 day ago
            startTime: Date().addingTimeInterval(-86400),
            endTime: Date().addingTimeInterval(-82800), // 1 hour later
            duration: 60,
            instructorId: "instructor_1",
            instructorName: "Nguyễn Văn A",
            totalStudents: 30,
            attendedStudents: 28,
            materials: [],
            assignments: [],
            quizzes: [],
            recordingUrl: nil,
            notes: nil,
            createdAt: Date().addingTimeInterval(-172800), // 2 days ago
            updatedAt: Date().addingTimeInterval(-86400)
        ),
        Lesson(
            id: 2,
            courseId: 1,
            courseName: "Toán học 10",
            courseCode: "MATH10",
            title: "Chương 2: Hàm số",
            description: "Khái niệm hàm số và đồ thị",
            type: .lecture,
            status: .scheduled,
            scheduledDate: Date().addingTimeInterval(86400), // 1 day from now
            startTime: Date().addingTimeInterval(86400),
            endTime: Date().addingTimeInterval(90000), // 1 hour later
            duration: 60,
            instructorId: "instructor_1",
            instructorName: "Nguyễn Văn A",
            totalStudents: 30,
            attendedStudents: 0,
            materials: [],
            assignments: [],
            quizzes: [],
            recordingUrl: nil,
            notes: nil,
            createdAt: Date().addingTimeInterval(-86400),
            updatedAt: nil
        ),
        Lesson(
            id: 3,
            courseId: 2,
            courseName: "Vật lý 10",
            courseCode: "PHYS10",
            title: "Chương 1: Cơ học",
            description: "Các định luật Newton và ứng dụng",
            type: .lecture,
            status: .scheduled,
            scheduledDate: Date().addingTimeInterval(172800), // 2 days from now
            startTime: Date().addingTimeInterval(172800),
            endTime: Date().addingTimeInterval(176400), // 1 hour later
            duration: 60,
            instructorId: "instructor_2",
            instructorName: "Trần Thị B",
            totalStudents: 25,
            attendedStudents: 0,
            materials: [],
            assignments: [],
            quizzes: [],
            recordingUrl: nil,
            notes: nil,
            createdAt: Date().addingTimeInterval(-86400),
            updatedAt: nil
        ),
        Lesson(
            id: 4,
            courseId: 1,
            courseName: "Toán học 10",
            courseCode: "MATH10",
            title: "Thực hành: Giải phương trình",
            description: "Bài thực hành giải các loại phương trình",
            type: .lab,
            status: .scheduled,
            scheduledDate: Date().addingTimeInterval(259200), // 3 days from now
            startTime: Date().addingTimeInterval(259200),
            endTime: Date().addingTimeInterval(266400), // 2 hours later
            duration: 120,
            instructorId: "instructor_1",
            instructorName: "Nguyễn Văn A",
            totalStudents: 30,
            attendedStudents: 0,
            materials: [],
            assignments: [],
            quizzes: [],
            recordingUrl: nil,
            notes: nil,
            createdAt: Date().addingTimeInterval(-86400),
            updatedAt: nil
        )
    ]
}
