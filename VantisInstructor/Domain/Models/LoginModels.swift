//
//  LoginModels.swift
//  mobile-app-template
//
//  Created by Instructor App on 27/7/25.
//

import Foundation
import UIKit

// MARK: - Login Request
struct InstructorLoginRequest: Codable {
    let username: String
    let password: String
    let deviceInfo: DeviceInfo
    let rememberMe: Bool
    let mfaCode: String?
    
    enum CodingKeys: String, CodingKey {
        case username, password
        case deviceInfo = "device_info"
        case rememberMe = "remember_me"
        case mfaCode = "mfa_code"
    }
}

// MARK: - Device Info
struct DeviceInfo: Codable {
    let deviceId: String
    let deviceName: String
    let deviceType: String
    let osName: String
    let osVersion: String
    let appVersion: String
    let browserName: String
    let browserVersion: String
    let userAgent: String
    let ipAddress: String
    let location: LocationInfo
    
    enum CodingKeys: String, CodingKey {
        case deviceId = "device_id"
        case deviceName = "device_name"
        case deviceType = "device_type"
        case osName = "os_name"
        case osVersion = "os_version"
        case appVersion = "app_version"
        case browserName = "browser_name"
        case browserVersion = "browser_version"
        case userAgent = "user_agent"
        case ipAddress = "ip_address"
        case location
    }
    
    static func current() -> DeviceInfo {
        let device = UIDevice.current
        
        return DeviceInfo(
            deviceId: device.identifierForVendor?.uuidString ?? "unknown-device",
            deviceName: device.name,
            deviceType: "mobile",
            osName: device.systemName,
            osVersion: device.systemVersion,
            appVersion: Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0",
            browserName: "Safari",
            browserVersion: "16.6",
            userAgent: "Mozilla/5.0 (iPhone; CPU iPhone OS \(device.systemVersion.replacingOccurrences(of: ".", with: "_")) like Mac OS X) AppleWebKit/605.1.15",
            ipAddress: "*************",
            location: LocationInfo(
                city: "Hanoi",
                country: "Vietnam",
                latitude: 21.0285,
                longitude: 105.8542
            )
        )
    }
}

// MARK: - Location Info
struct LocationInfo: Codable {
    let city: String
    let country: String
    let latitude: Double
    let longitude: Double
}

// MARK: - API Login Models (matching OpenAPI spec)
struct UnifiedLoginRequest: Codable {
    let username: String // Can be email or username
    let password: String
    let deviceInfo: DeviceInfo?
    let rememberMe: Bool
    let mfaCode: String?

    enum CodingKeys: String, CodingKey {
        case username, password
        case deviceInfo = "device_info"
        case rememberMe = "remember_me"
        case mfaCode = "mfa_code"
    }
}

struct EnhancedTokenResponse: Codable {
    let success: Bool
    let message: String
    let data: TokenData?
    let meta: [String: AnyCodable]?
}

struct TokenData: Codable {
    let accessToken: String
    let refreshToken: String?
    let expiresIn: Int?
    let tokenType: String?
    let user: APIUserInfo?

    enum CodingKeys: String, CodingKey {
        case accessToken = "access_token"
        case refreshToken = "refresh_token"
        case expiresIn = "expires_in"
        case tokenType = "token_type"
        case user
    }
}

struct APIUserInfo: Codable {
    let id: Int
    let email: String
    let name: String?
    let role: String?
}

// MARK: - Legacy Login Response (keep for compatibility)
struct LoginResponse: Codable {
    let success: Bool
    let message: String?
    let data: LoginData?
}

struct LoginData: Codable {
    let accessToken: String
    let refreshToken: String?
    let tokenType: String
    let expiresIn: Int
    let userId: Int
    let deviceRegistered: Bool?
    let scope: String?
    let issuedAt: Int?

    enum CodingKeys: String, CodingKey {
        case accessToken = "access_token"
        case refreshToken = "refresh_token"
        case tokenType = "token_type"
        case expiresIn = "expires_in"
        case userId = "user_id"
        case deviceRegistered = "device_registered"
        case scope
        case issuedAt = "issued_at"
    }
}

// MARK: - JWT Payload (from token)
struct JWTPayload: Codable {
    let sub: String // user id
    let name: String
    let login: String // email/username
    let role: String
    let permissions: [String]?
    let exp: Int
    let iat: Int

    var userId: Int {
        return Int(sub) ?? 0
    }

    var isInstructor: Bool {
        return role.lowercased() == "instructor"
    }

    var displayName: String {
        return name.isEmpty ? login : name
    }
}

// MARK: - JWT Helper
extension String {
    func decodeJWT() -> JWTPayload? {
        let segments = self.components(separatedBy: ".")
        guard segments.count == 3 else { return nil }

        let payloadSegment = segments[1]
        var base64 = payloadSegment
            .replacingOccurrences(of: "-", with: "+")
            .replacingOccurrences(of: "_", with: "/")

        // Add padding if needed
        while base64.count % 4 != 0 {
            base64 += "="
        }

        guard let data = Data(base64Encoded: base64) else { return nil }

        do {
            let payload = try JSONDecoder().decode(JWTPayload.self, from: data)
            return payload
        } catch {
            print("❌ JWT decode error: \(error)")
            return nil
        }
    }
}
