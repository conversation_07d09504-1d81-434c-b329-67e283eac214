//
//  InstructorProfile.swift
//  mobile-app-template
//
//  Created by Instructor App on 27/7/25.
//

import Foundation

// MARK: - Instructor Profile Models

struct InstructorProfile: Codable, Identifiable {
    let id: Int
    let code: String
    let name: String
    let status: String
    let active: Bool
    let userId: Int
    let userName: String
    let birthDate: Date?
    let gender: String?
    let idNumber: String?
    let taxCode: String?
    let contactInfo: ContactInfo
    let employmentInfo: EmploymentInfo
    let skills: [InstructorSkill]
    let qualifications: [InstructorQualification]
    let statistics: InstructorStatistics
    let createdAt: Date?
    let updatedAt: Date?
    
    enum CodingKeys: String, CodingKey {
        case id, code, name, status, active
        case userId = "user_id"
        case userName = "user_name"
        case birthDate = "birth_date"
        case gender
        case idNumber = "id_number"
        case taxCode = "tax_code"
        case contactInfo = "contact_info"
        case employmentInfo = "employment_info"
        case skills, qualifications, statistics
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
    
    // Custom decoder to handle problematic fields
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        id = try container.decode(Int.self, forKey: .id)
        code = try container.decode(String.self, forKey: .code)
        name = try container.decode(String.self, forKey: .name)
        status = try container.decode(String.self, forKey: .status)
        active = try container.decode(Bool.self, forKey: .active)
        userId = try container.decode(Int.self, forKey: .userId)
        userName = try container.decode(String.self, forKey: .userName)
        
        // Handle birth_date safely - can be null, string, or boolean (server bug)
        if let birthDateString = try? container.decode(String.self, forKey: .birthDate) {
            let formatter = ISO8601DateFormatter()
            formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
            birthDate = formatter.date(from: birthDateString) ?? 
                       DateFormatter.yyyyMMdd.date(from: birthDateString)
        } else {
            birthDate = nil
        }
        
        gender = try container.decodeIfPresent(String.self, forKey: .gender)
        idNumber = try container.decodeIfPresent(String.self, forKey: .idNumber)
        taxCode = try container.decodeIfPresent(String.self, forKey: .taxCode)
        
        contactInfo = try container.decode(ContactInfo.self, forKey: .contactInfo)
        employmentInfo = try container.decode(EmploymentInfo.self, forKey: .employmentInfo)
        skills = try container.decode([InstructorSkill].self, forKey: .skills)
        qualifications = try container.decode([InstructorQualification].self, forKey: .qualifications)
        statistics = try container.decode(InstructorStatistics.self, forKey: .statistics)
        
        // Handle dates safely
        if let createdAtString = try? container.decode(String.self, forKey: .createdAt) {
            let formatter = ISO8601DateFormatter()
            formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
            createdAt = formatter.date(from: createdAtString)
        } else {
            createdAt = nil
        }
        
        if let updatedAtString = try? container.decode(String.self, forKey: .updatedAt) {
            let formatter = ISO8601DateFormatter()
            formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
            updatedAt = formatter.date(from: updatedAtString)
        } else {
            updatedAt = nil
        }
    }
}

// MARK: - Contact Info
struct ContactInfo: Codable {
    let phone: String?
    let email: String?
    let address: String?
    let emergencyContact: String?
    let emergencyPhone: String?
    
    enum CodingKeys: String, CodingKey {
        case phone, email, address
        case emergencyContact = "emergency_contact"
        case emergencyPhone = "emergency_phone"
    }
}

// MARK: - Employment Info
struct EmploymentInfo: Codable {
    let employmentType: String?
    let startDate: Date?
    let contractEndDate: Date?
    let salaryLevel: String?
    let department: String?
    let position: String?
    
    enum CodingKeys: String, CodingKey {
        case employmentType = "employment_type"
        case startDate = "start_date"
        case contractEndDate = "contract_end_date"
        case salaryLevel = "salary_level"
        case department, position
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        employmentType = try container.decodeIfPresent(String.self, forKey: .employmentType)
        salaryLevel = try container.decodeIfPresent(String.self, forKey: .salaryLevel)
        department = try container.decodeIfPresent(String.self, forKey: .department)
        position = try container.decodeIfPresent(String.self, forKey: .position)
        
        // Handle dates safely
        if let startDateString = try? container.decode(String.self, forKey: .startDate) {
            startDate = DateFormatter.yyyyMMdd.date(from: startDateString)
        } else {
            startDate = nil
        }
        
        if let endDateString = try? container.decode(String.self, forKey: .contractEndDate) {
            contractEndDate = DateFormatter.yyyyMMdd.date(from: endDateString)
        } else {
            contractEndDate = nil
        }
    }
}

// MARK: - Instructor Skill
struct InstructorSkill: Codable, Identifiable {
    let id: Int
    let name: String
    let subjectId: Int?
    let proficiencyLevel: String?
    let yearsExperience: Double?
    let isPrimary: Bool?
    let lastTaughtDate: Date?
    let rating: Double?
    let lessonCount: Int?
    
    enum CodingKeys: String, CodingKey {
        case id, name
        case subjectId = "subject_id"
        case proficiencyLevel = "proficiency_level"
        case yearsExperience = "years_experience"
        case isPrimary = "is_primary"
        case lastTaughtDate = "last_taught_date"
        case rating
        case lessonCount = "lesson_count"
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        id = try container.decode(Int.self, forKey: .id)
        name = try container.decode(String.self, forKey: .name)
        subjectId = try container.decodeIfPresent(Int.self, forKey: .subjectId)
        proficiencyLevel = try container.decodeIfPresent(String.self, forKey: .proficiencyLevel)
        yearsExperience = try container.decodeIfPresent(Double.self, forKey: .yearsExperience)
        isPrimary = try container.decodeIfPresent(Bool.self, forKey: .isPrimary)
        rating = try container.decodeIfPresent(Double.self, forKey: .rating)
        lessonCount = try container.decodeIfPresent(Int.self, forKey: .lessonCount)
        
        // Handle date safely
        if let dateString = try? container.decode(String.self, forKey: .lastTaughtDate) {
            lastTaughtDate = DateFormatter.yyyyMMdd.date(from: dateString)
        } else {
            lastTaughtDate = nil
        }
    }
}

// MARK: - Instructor Qualification
struct InstructorQualification: Codable, Identifiable {
    let id: Int
    let name: String
    let type: String?
    let level: String?
    let fieldOfStudy: String?
    let issuingOrganization: String?
    let issueDate: Date?
    let expiryDate: Date?
    let credentialId: String?
    let description: String?
    let isVerified: Bool?
    
    enum CodingKeys: String, CodingKey {
        case id, name, type, level, description
        case fieldOfStudy = "field_of_study"
        case issuingOrganization = "issuing_organization"
        case issueDate = "issue_date"
        case expiryDate = "expiry_date"
        case credentialId = "credential_id"
        case isVerified = "is_verified"
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        id = try container.decode(Int.self, forKey: .id)
        name = try container.decode(String.self, forKey: .name)
        type = try container.decodeIfPresent(String.self, forKey: .type)
        level = try container.decodeIfPresent(String.self, forKey: .level)
        fieldOfStudy = try container.decodeIfPresent(String.self, forKey: .fieldOfStudy)
        issuingOrganization = try container.decodeIfPresent(String.self, forKey: .issuingOrganization)
        credentialId = try container.decodeIfPresent(String.self, forKey: .credentialId)
        description = try container.decodeIfPresent(String.self, forKey: .description)
        isVerified = try container.decodeIfPresent(Bool.self, forKey: .isVerified)
        
        // Handle dates safely
        if let issueDateString = try? container.decode(String.self, forKey: .issueDate) {
            issueDate = DateFormatter.yyyyMMdd.date(from: issueDateString)
        } else {
            issueDate = nil
        }
        
        if let expiryDateString = try? container.decode(String.self, forKey: .expiryDate) {
            expiryDate = DateFormatter.yyyyMMdd.date(from: expiryDateString)
        } else {
            expiryDate = nil
        }
    }
}

// MARK: - Instructor Statistics
struct InstructorStatistics: Codable {
    let totalCourses: Int?
    let activeCourses: Int?
    let totalStudents: Int?
    let totalLessons: Int?
    let completedLessons: Int?
    let averageRating: Double?
    let totalTeachingHours: Int?
    let thisMonthHours: Int?
    
    enum CodingKeys: String, CodingKey {
        case totalCourses = "total_courses"
        case activeCourses = "active_courses"
        case totalStudents = "total_students"
        case totalLessons = "total_lessons"
        case completedLessons = "completed_lessons"
        case averageRating = "average_rating"
        case totalTeachingHours = "total_teaching_hours"
        case thisMonthHours = "this_month_hours"
    }
}

// MARK: - API Response Meta
struct APIResponseMeta: Codable {
    let timestamp: String?
    let traceId: String?
    let requestId: String?

    enum CodingKeys: String, CodingKey {
        case timestamp
        case traceId = "trace_id"
        case requestId = "request_id"
    }
}

// MARK: - API Response
struct InstructorProfileResponse: Codable {
    let success: Bool
    let message: String?
    let data: InstructorProfile?
    let meta: APIResponseMeta?
}

// MARK: - Date Formatter Extensions
extension DateFormatter {
    static let yyyyMMdd: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        formatter.locale = Locale(identifier: "en_US_POSIX")
        formatter.timeZone = TimeZone(secondsFromGMT: 0)
        return formatter
    }()
}
