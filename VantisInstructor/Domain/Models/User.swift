//
//  User.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 22/7/25.
//

import Foundation

// MARK: - User Model
struct User: Codable, Identifiable {
    let id: String
    let email: String
    let firstName: String?
    let lastName: String?
    let phone: String?
    let role: UserRole
    let isActive: Bool
    let avatar: String?
    let dateOfBirth: Date?
    let lastLoginAt: Date?
    let createdAt: Date
    let updatedAt: Date?

    // Business information for BUSINESS users
    let businessName: String?
    let businessId: String?
    let category: String?
    let businessPhone: String?
    let website: String?
    let businessDescription: String?
    let businessStatus: String?
    let onboardedAt: Date?

    // Standard init for creating User objects programmatically
    init(
        id: String,
        email: String,
        firstName: String? = nil,
        lastName: String? = nil,
        phone: String? = nil,
        role: UserRole = .instructor,
        isActive: Bool = true,
        avatar: String? = nil,
        dateOfBirth: Date? = nil,
        lastLoginAt: Date? = nil,
        createdAt: Date = Date(),
        updatedAt: Date? = nil,
        businessName: String? = nil,
        businessId: String? = nil,
        category: String? = nil,
        businessPhone: String? = nil,
        website: String? = nil,
        businessDescription: String? = nil,
        businessStatus: String? = nil,
        onboardedAt: Date? = nil
    ) {
        self.id = id
        self.email = email
        self.firstName = firstName
        self.lastName = lastName
        self.phone = phone
        self.role = role
        self.isActive = isActive
        self.avatar = avatar
        self.dateOfBirth = dateOfBirth
        self.lastLoginAt = lastLoginAt
        self.createdAt = createdAt
        self.updatedAt = updatedAt
        self.businessName = businessName
        self.businessId = businessId
        self.category = category
        self.businessPhone = businessPhone
        self.website = website
        self.businessDescription = businessDescription
        self.businessStatus = businessStatus
        self.onboardedAt = onboardedAt
    }
    
    // Computed properties
    var displayName: String {
        if let firstName = firstName, let lastName = lastName {
            return "\(firstName) \(lastName)"
        }
        return email
    }
    
    var initials: String {
        let components = displayName.components(separatedBy: " ")
        let initials = components.compactMap { $0.first }.map { String($0) }
        return initials.prefix(2).joined().uppercased()
    }
    
    var isMerchant: Bool {
        return role == .business
    }
    
    var isAdmin: Bool {
        return role == .admin
    }

    var isInstructor: Bool {
        return role == .instructor
    }
}

// MARK: - User Role
enum UserRole: String, Codable, CaseIterable {
    case user = "USER"
    case business = "BUSINESS"
    case admin = "ADMIN"
    case instructor = "INSTRUCTOR"

    var displayName: String {
        switch self {
        case .user:
            return "User"
        case .business:
            return "Business"
        case .admin:
            return "Admin"
        case .instructor:
            return "Instructor"
        }
    }
}

// MARK: - Auth DTOs
struct LoginRequest: Codable {
    let username: String
    let password: String
    let deviceInfo: DeviceInfo
    let rememberMe: Bool
    let mfaCode: String?

    enum CodingKeys: String, CodingKey {
        case username, password
        case deviceInfo = "device_info"
        case rememberMe = "remember_me"
        case mfaCode = "mfa_code"
    }

    struct DeviceInfo: Codable {
        let deviceId: String
        let deviceName: String
        let deviceType: String
        let osName: String
        let osVersion: String
        let appVersion: String
        let browserName: String
        let browserVersion: String
        let userAgent: String
        let ipAddress: String
        let location: Location

        enum CodingKeys: String, CodingKey {
            case deviceId = "device_id"
            case deviceName = "device_name"
            case deviceType = "device_type"
            case osName = "os_name"
            case osVersion = "os_version"
            case appVersion = "app_version"
            case browserName = "browser_name"
            case browserVersion = "browser_version"
            case userAgent = "user_agent"
            case ipAddress = "ip_address"
            case location
        }

        struct Location: Codable {
            let city: String
            let country: String
            let latitude: Double
            let longitude: Double
        }
    }
}

struct RegisterRequest: Codable {
    let email: String
    let password: String
    let phone: String?
    let firstName: String?
    let lastName: String?
}

// MARK: - Auth Tokens
struct AuthTokens: Codable {
    let accessToken: String
    let refreshToken: String?
    let tokenType: String
    let expiresIn: Int?

    enum CodingKeys: String, CodingKey {
        case accessToken = "access_token"
        case refreshToken = "refresh_token"
        case tokenType = "token_type"
        case expiresIn = "expires_in"
    }
}

struct AuthResponse: Codable {
    let access_token: String
    let user: User
    let tokens: AuthTokens?

    enum CodingKeys: String, CodingKey {
        case access_token
        case user
        case tokens
    }
}

// MARK: - User Profile Update
struct UpdateProfileRequest: Codable {
    let firstName: String?
    let lastName: String?
    let phone: String?
    let dateOfBirth: Date?
    let avatar: String?
}

// MARK: - Password Change
struct ChangePasswordRequest: Codable {
    let currentPassword: String
    let newPassword: String
}


