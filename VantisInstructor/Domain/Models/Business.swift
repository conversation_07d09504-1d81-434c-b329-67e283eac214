//
//  Business.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 22/7/25.
//

import Foundation

// MARK: - Business Model (formerly Merchant)
struct Business: Codable, Identifiable {
    let id: String
    let name: String
    let businessId: String
    let email: String
    let phone: String?
    let category: BusinessCategory
    let status: BusinessStatus
    let description: String?
    let website: String?
    let logoUrl: String?
    let address: BusinessAddress?
    let operatingHours: [OperatingHour]?
    let socialMedia: SocialMediaLinks?
    let totalTransactions: Int
    let metadata: [String: AnyCodable]?
    let notes: String?
    let lastTransactionAt: Date?
    let onboardedAt: Date?
    let createdAt: Date
    let updatedAt: Date?
    
    // Computed properties
    var isActive: Bool {
        return status == .active
    }
    
    var displayAddress: String {
        guard let address = address else { return "No address" }
        var components: [String] = []
        
        if let street = address.street { components.append(street) }
        if let city = address.city { components.append(city) }
        if let state = address.state { components.append(state) }
        
        return components.joined(separator: ", ")
    }
}

// MARK: - Business Category
enum BusinessCategory: String, Codable, CaseIterable {
    case restaurant = "RESTAURANT"
    case retail = "RETAIL"
    case entertainment = "ENTERTAINMENT"
    case services = "SERVICES"
    case ecommerce = "ECOMMERCE"
    case other = "OTHER"
    
    var displayName: String {
        switch self {
        case .restaurant:
            return "Restaurant"
        case .retail:
            return "Retail"
        case .entertainment:
            return "Entertainment"
        case .services:
            return "Services"
        case .ecommerce:
            return "E-commerce"
        case .other:
            return "Other"
        }
    }
    
    var icon: String {
        switch self {
        case .restaurant:
            return "fork.knife"
        case .retail:
            return "bag"
        case .entertainment:
            return "gamecontroller"
        case .services:
            return "wrench.and.screwdriver"
        case .ecommerce:
            return "cart"
        case .other:
            return "building.2"
        }
    }
}

// MARK: - Business Status
enum BusinessStatus: String, Codable, CaseIterable {
    case pending = "PENDING"
    case active = "ACTIVE"
    case suspended = "SUSPENDED"
    case inactive = "INACTIVE"
    
    var displayName: String {
        switch self {
        case .pending:
            return "Pending"
        case .active:
            return "Active"
        case .suspended:
            return "Suspended"
        case .inactive:
            return "Inactive"
        }
    }
    
    var color: String {
        switch self {
        case .pending:
            return "orange"
        case .active:
            return "green"
        case .suspended:
            return "red"
        case .inactive:
            return "gray"
        }
    }
}

// MARK: - Business Address
struct BusinessAddress: Codable {
    let street: String?
    let city: String?
    let state: String?
    let zipCode: String?
    let country: String?
    let latitude: Double?
    let longitude: Double?
}

// MARK: - Operating Hours
struct OperatingHour: Codable {
    let dayOfWeek: Int // 0 = Sunday, 1 = Monday, etc.
    let openTime: String // "09:00"
    let closeTime: String // "22:00"
    let isClosed: Bool
    
    var dayName: String {
        let days = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"]
        return days[dayOfWeek]
    }
    
    var displayTime: String {
        if isClosed {
            return "Closed"
        }
        return "\(openTime) - \(closeTime)"
    }
}

// MARK: - Social Media Links
struct SocialMediaLinks: Codable {
    let facebook: String?
    let instagram: String?
    let twitter: String?
    let linkedin: String?
    let youtube: String?
    let tiktok: String?
}

// MARK: - Business List Response
struct BusinessListResponse: Codable {
    let businesses: [Business]
    let total: Int
    let page: Int
    let limit: Int
    let hasMore: Bool
}

// MARK: - Business Search Filters
struct BusinessSearchFilters: Codable {
    let category: BusinessCategory?
    let status: BusinessStatus?
    let city: String?
    let searchQuery: String?
    let sortBy: BusinessSortBy?
    let sortOrder: SortOrder?
}

enum BusinessSortBy: String, Codable, CaseIterable {
    case name = "name"
    case totalTransactions = "totalTransactions"
    case createdAt = "createdAt"

    var displayName: String {
        switch self {
        case .name:
            return "Name"
        case .totalTransactions:
            return "Total Transactions"
        case .createdAt:
            return "Date Added"
        }
    }
}

enum SortOrder: String, Codable, CaseIterable {
    case asc = "asc"
    case desc = "desc"
    
    var displayName: String {
        switch self {
        case .asc:
            return "Ascending"
        case .desc:
            return "Descending"
        }
    }
}
