//
//  Transaction.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 22/7/25.
//

import Foundation

// MARK: - Transaction Model
struct Transaction: Codable, Identifiable {
    let id: String
    let userId: String?  // Make userId optional since backend doesn't return it
    let type: TransactionType
    let amountUsd: Double?
    let amountTokens: Double
    let businessId: String?
    let businessCommission: Double?
    let commissionRate: Double?
    let platformProfit: Double?
    let blockchainTxHash: String?
    let status: TransactionStatus
    let description: String?
    let metadata: [String: AnyCodable]?
    let errorMessage: String?
    let retryCount: Int?
    let createdAt: Date
    let updatedAt: Date?

    // Computed properties
    var displayAmount: String {
        return String(format: "%.2f TOKENS", amountTokens)
    }

    var displayAmountUsd: String? {
        guard let amountUsd = amountUsd else { return nil }
        return String(format: "%.2f USD", amountUsd)
    }
    
    var statusColor: String {
        switch status {
        case .pending:
            return "orange"
        case .completed:
            return "green"
        case .failed:
            return "red"
        }
    }
    
    var typeIcon: String {
        switch type {
        case .purchase:
            return "plus.circle.fill"
        case .refund:
            return "minus.circle.fill"
        case .transferIn:
            return "arrow.down.circle.fill"
        case .transferOut:
            return "arrow.up.circle.fill"
        }
    }
    
    var typeDisplayName: String {
        switch type {
        case .purchase:
            return "Earned"
        case .refund:
            return "Redeemed"
        case .transferIn:
            return "Received"
        case .transferOut:
            return "Sent"
        }
    }
}

// MARK: - Transaction Type
enum TransactionType: String, Codable, CaseIterable {
    case purchase = "PURCHASE"
    case refund = "REFUND"
    case transferIn = "TRANSFER_IN"
    case transferOut = "TRANSFER_OUT"

    var displayName: String {
        switch self {
        case .purchase:
            return "Purchase"
        case .refund:
            return "Refund"
        case .transferIn:
            return "Transfer In"
        case .transferOut:
            return "Transfer Out"
        }
    }
}

// MARK: - Transaction Status
enum TransactionStatus: String, Codable, CaseIterable {
    case pending = "PENDING"
    case completed = "COMPLETED"
    case failed = "FAILED"
    
    var displayName: String {
        switch self {
        case .pending:
            return "Pending"
        case .completed:
            return "Completed"
        case .failed:
            return "Failed"
        }
    }
}

// MARK: - Transaction DTOs
struct EarnTokensRequest: Codable {
    let businessId: String
    let amountUsd: Double
    let description: String?
}

struct TransferTokensRequest: Codable {
    let recipientAddress: String
    let amountTokens: Double
    let description: String?
}

struct TransactionListResponse: Codable {
    let transactions: [Transaction]
    let total: Int
    let page: Int
    let limit: Int
    let hasMore: Bool
}

// Backend response format for transaction history
struct TransactionHistoryResponse: Codable {
    let transactions: [Transaction]
    let total: Int
    let limit: Int
    let offset: Int
}

// MARK: - AnyCodable for metadata
struct AnyCodable: Codable {
    let value: Any
    
    init<T>(_ value: T?) {
        self.value = value ?? ()
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()
        
        if container.decodeNil() {
            self.value = ()
        } else if let bool = try? container.decode(Bool.self) {
            self.value = bool
        } else if let int = try? container.decode(Int.self) {
            self.value = int
        } else if let double = try? container.decode(Double.self) {
            self.value = double
        } else if let string = try? container.decode(String.self) {
            self.value = string
        } else if let array = try? container.decode([AnyCodable].self) {
            self.value = array.map { $0.value }
        } else if let dictionary = try? container.decode([String: AnyCodable].self) {
            self.value = dictionary.mapValues { $0.value }
        } else {
            throw DecodingError.dataCorruptedError(in: container, debugDescription: "AnyCodable value cannot be decoded")
        }
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()
        
        switch value {
        case is Void:
            try container.encodeNil()
        case let bool as Bool:
            try container.encode(bool)
        case let int as Int:
            try container.encode(int)
        case let double as Double:
            try container.encode(double)
        case let string as String:
            try container.encode(string)
        case let array as [Any]:
            let anyArray = array.map { AnyCodable($0) }
            try container.encode(anyArray)
        case let dictionary as [String: Any]:
            let anyDictionary = dictionary.mapValues { AnyCodable($0) }
            try container.encode(anyDictionary)
        default:
            let context = EncodingError.Context(codingPath: container.codingPath, debugDescription: "AnyCodable value cannot be encoded")
            throw EncodingError.invalidValue(value, context)
        }
    }
}
