//
//  QuizAttempt.swift
//  mobile-app-template
//
//  Created by Instructor App on 24/7/25.
//

import Foundation

// MARK: - Quiz Attempt Model
struct QuizAttempt: Codable, Identifiable {
    let id: Int
    let studentId: Int
    let studentName: String
    let studentCode: String
    let studentEmail: String?
    let quizId: Int
    let quizName: String
    let quizCode: String
    let attemptNumber: Int
    let startTime: Date
    let endTime: Date?
    let timeSpent: Int // in seconds
    let score: Double
    let maxScore: Double
    let percentage: Double
    let isPassed: Bool
    let state: AttemptState
    let needsGrading: Bool
    let feedback: String?
    let submittedAt: Date?
    let gradedAt: Date?
    let gradedBy: String?
    
    // Computed properties
    var isCompleted: Bool {
        return state == .completed || state == .graded
    }
    
    var isInProgress: Bool {
        return state == .inProgress
    }
    
    var timeSpentText: String {
        let formatter = DateComponentsFormatter()
        formatter.allowedUnits = [.hour, .minute, .second]
        formatter.unitsStyle = .abbreviated
        return formatter.string(from: TimeInterval(timeSpent)) ?? "0s"
    }
    
    var scoreText: String {
        return String(format: "%.1f/%.1f", score, maxScore)
    }
    
    var percentageText: String {
        return String(format: "%.1f%%", percentage)
    }
    
    var gradeText: String {
        if percentage >= 90 { return "A" }
        else if percentage >= 80 { return "B" }
        else if percentage >= 70 { return "C" }
        else if percentage >= 60 { return "D" }
        else { return "F" }
    }
    
    var statusColor: String {
        switch state {
        case .inProgress: return "blue"
        case .completed: return needsGrading ? "orange" : "green"
        case .graded: return isPassed ? "green" : "red"
        case .expired: return "gray"
        }
    }
}

// MARK: - Attempt State
enum AttemptState: String, Codable, CaseIterable {
    case inProgress = "in_progress"
    case completed = "completed"
    case graded = "graded"
    case expired = "expired"
    
    var displayName: String {
        switch self {
        case .inProgress: return "Đang làm"
        case .completed: return "Đã nộp"
        case .graded: return "Đã chấm"
        case .expired: return "Hết hạn"
        }
    }
    
    var icon: String {
        switch self {
        case .inProgress: return "clock"
        case .completed: return "checkmark.circle"
        case .graded: return "checkmark.circle.fill"
        case .expired: return "xmark.circle"
        }
    }
}

// MARK: - Attempt Detail Model
struct AttemptDetail: Codable, Identifiable {
    let id: Int
    let attempt: QuizAttempt
    let quiz: Quiz
    let answers: [StudentAnswer]
    
    var totalQuestions: Int {
        return answers.count
    }
    
    var answeredQuestions: Int {
        return answers.filter { !$0.answerText.isEmpty }.count
    }
    
    var gradedQuestions: Int {
        return answers.filter { $0.score != nil }.count
    }
    
    var pendingGradingQuestions: Int {
        return answers.filter { $0.needsGrading }.count
    }
}

// MARK: - Student Answer Model
struct StudentAnswer: Codable, Identifiable {
    let id: Int
    let questionId: Int
    let questionText: String
    let questionType: QuestionType
    let questionScore: Double
    let answerText: String
    let selectedAnswerIds: [Int]
    let score: Double?
    let maxScore: Double
    let isCorrect: Bool?
    let needsGrading: Bool
    let instructorFeedback: String?
    let submittedAt: Date
    let gradedAt: Date?
    
    var scoreText: String {
        guard let score = score else { return "Chưa chấm" }
        return String(format: "%.1f/%.1f", score, maxScore)
    }
    
    var percentageText: String {
        guard let score = score else { return "Chưa chấm" }
        let percentage = (score / maxScore) * 100
        return String(format: "%.1f%%", percentage)
    }
    
    var statusIcon: String {
        if needsGrading { return "clock" }
        guard let isCorrect = isCorrect else { return "questionmark.circle" }
        return isCorrect ? "checkmark.circle.fill" : "xmark.circle.fill"
    }
    
    var statusColor: String {
        if needsGrading { return "orange" }
        guard let isCorrect = isCorrect else { return "gray" }
        return isCorrect ? "green" : "red"
    }
}

// MARK: - Quiz Analytics Model
struct QuizAnalytics: Codable, Identifiable {
    let id: Int
    let quizId: Int
    let quizName: String
    let totalStudents: Int
    let totalAttempts: Int
    let completedAttempts: Int
    let averageScore: Double
    let highestScore: Double
    let lowestScore: Double
    let passRate: Double
    let averageTimeSpent: Int
    let questionAnalytics: [QuestionAnalytics]
    let scoreDistribution: [ScoreRange]
    let attemptsByDate: [AttemptsByDate]
    
    var completionRate: Double {
        guard totalStudents > 0 else { return 0 }
        return Double(completedAttempts) / Double(totalStudents) * 100
    }
    
    var averageTimeSpentText: String {
        let formatter = DateComponentsFormatter()
        formatter.allowedUnits = [.hour, .minute]
        formatter.unitsStyle = .abbreviated
        return formatter.string(from: TimeInterval(averageTimeSpent)) ?? "0m"
    }
}

// MARK: - Question Analytics
struct QuestionAnalytics: Codable, Identifiable {
    let id: Int
    let questionId: Int
    let questionText: String
    let questionType: QuestionType
    let totalAnswers: Int
    let correctAnswers: Int
    let averageScore: Double
    let difficulty: QuestionDifficulty
    
    var correctRate: Double {
        guard totalAnswers > 0 else { return 0 }
        return Double(correctAnswers) / Double(totalAnswers) * 100
    }
}

// MARK: - Question Difficulty
enum QuestionDifficulty: String, Codable {
    case easy = "easy"
    case medium = "medium"
    case hard = "hard"
    
    var displayName: String {
        switch self {
        case .easy: return "Dễ"
        case .medium: return "Trung bình"
        case .hard: return "Khó"
        }
    }
    
    var color: String {
        switch self {
        case .easy: return "green"
        case .medium: return "orange"
        case .hard: return "red"
        }
    }
}

// MARK: - Score Range
struct ScoreRange: Codable, Identifiable {
    let id: String
    let range: String
    let count: Int
    let percentage: Double
}

// MARK: - Attempts By Date
struct AttemptsByDate: Codable, Identifiable {
    let id: String
    let date: Date
    let count: Int
}
