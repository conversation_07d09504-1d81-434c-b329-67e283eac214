//
//  APILesson.swift
//  mobile-app-template
//
//  Created by Instructor App on 28/7/25.
//

import Foundation

// MARK: - API Response Models (matching OpenAPI spec)
struct APILessonListResponse: Codable {
    let success: Bool
    let message: String
    let data: [LessonSummary]
    let pagination: APIPaginationMetadata
}

struct APIPaginationMetadata: Codable {
    let page: Int
    let pageSize: Int
    let totalItems: Int
    let totalPages: Int

    enum CodingKeys: String, CodingKey {
        case page
        case pageSize = "page_size"
        case totalItems = "total_items"
        case totalPages = "total_pages"
    }
}

// MARK: - Lesson Summary (matching OpenAPI spec)
typealias LessonSummary = APILesson

// MARK: - API Lesson Model (matches API response exactly)
struct APILesson: Codable, Identifiable {
    let id: Int
    let name: String
    let lessonNumber: Int
    let state: APILessonState
    let className: String
    let classCode: String
    let startDatetime: String
    let durationHours: Double
    let room: String
    let totalStudents: Int
    let presentCount: Int
    let attendanceRate: Double
    let isToday: Bool
    let isUpcoming: Bool
    let isOverdue: Bool
    let needsAttention: Bool
    
    enum CodingKeys: String, CodingKey {
        case id, name, state, room
        case lessonNumber = "lesson_number"
        case className = "class_name"
        case classCode = "class_code"
        case startDatetime = "start_datetime"
        case durationHours = "duration_hours"
        case totalStudents = "total_students"
        case presentCount = "present_count"
        case attendanceRate = "attendance_rate"
        case isToday = "is_today"
        case isUpcoming = "is_upcoming"
        case isOverdue = "is_overdue"
        case needsAttention = "needs_attention"
    }
}

// MARK: - API Lesson State
enum APILessonState: String, Codable, CaseIterable {
    case scheduled = "scheduled"
    case completed = "completed"
    case cancelled = "cancelled"
    case inProgress = "in_progress"
    
    var displayName: String {
        switch self {
        case .scheduled: return "Đã lên lịch"
        case .completed: return "Hoàn thành"
        case .cancelled: return "Đã hủy"
        case .inProgress: return "Đang diễn ra"
        }
    }
    
    var color: String {
        switch self {
        case .scheduled: return "blue"
        case .completed: return "green"
        case .cancelled: return "red"
        case .inProgress: return "orange"
        }
    }
    
    var icon: String {
        switch self {
        case .scheduled: return "calendar"
        case .completed: return "checkmark.circle.fill"
        case .cancelled: return "xmark.circle.fill"
        case .inProgress: return "play.circle.fill"
        }
    }
}

// MARK: - API Lessons Response
struct APILessonsResponse: Codable {
    let success: Bool
    let message: String
    let data: [APILesson]
    let pagination: APIPagination
}

struct APIPagination: Codable {
    let page: Int
    let pageSize: Int
    let totalCount: Int
    let totalPages: Int
    let hasNext: Bool
    let hasPrevious: Bool
    
    enum CodingKeys: String, CodingKey {
        case page, totalCount, totalPages
        case pageSize = "page_size"
        case hasNext = "has_next"
        case hasPrevious = "has_previous"
    }
}

// MARK: - APILesson Extensions
extension APILesson {
    // Convert API datetime string to Date
    var startDate: Date {
        let formatter = ISO8601DateFormatter()
        formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
        
        // Try with fractional seconds first
        if let date = formatter.date(from: startDatetime) {
            return date
        }
        
        // Try without fractional seconds
        formatter.formatOptions = [.withInternetDateTime]
        if let date = formatter.date(from: startDatetime) {
            return date
        }
        
        // Fallback to simple format
        let simpleFormatter = DateFormatter()
        simpleFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
        return simpleFormatter.date(from: startDatetime) ?? Date()
    }
    
    // Format start time for display
    var formattedStartTime: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: startDate)
    }
    
    // Format date for display
    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "dd/MM/yyyy"
        return formatter.string(from: startDate)
    }
    
    // Get end time based on duration
    var endTime: Date {
        return startDate.addingTimeInterval(durationHours * 3600)
    }
    
    // Format end time for display
    var formattedEndTime: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: endTime)
    }
    
    // Time range string
    var timeRange: String {
        return "\(formattedStartTime) - \(formattedEndTime)"
    }
    
    // Duration in minutes
    var durationMinutes: Int {
        return Int(durationHours * 60)
    }
    
    // Attendance status
    var attendanceStatus: String {
        if attendanceRate >= 90 {
            return "Tốt"
        } else if attendanceRate >= 70 {
            return "Khá"
        } else if attendanceRate >= 50 {
            return "Trung bình"
        } else {
            return "Kém"
        }
    }
    
    // Status badge text
    var statusBadgeText: String {
        if isToday {
            return "Hôm nay"
        } else if isUpcoming {
            return "Sắp tới"
        } else if isOverdue {
            return "Quá hạn"
        } else {
            return state.displayName
        }
    }
    
    // Status badge color
    var statusBadgeColor: String {
        if isToday {
            return "green"
        } else if isUpcoming {
            return "blue"
        } else if isOverdue {
            return "red"
        } else {
            return state.color
        }
    }
    
    // Convert to Class model for compatibility with existing UI
    func toClass() -> Class {
        return Class(
            id: String(id),
            courseId: classCode,
            courseName: className,
            courseCode: classCode,
            classNumber: lessonNumber,
            title: name,
            description: nil,
            type: .lecture, // Default type
            status: state.toClassStatus(),
            scheduledDate: startDate,
            startTime: startDate,
            endTime: endTime,
            duration: durationMinutes,
            location: ClassLocation(
                building: "Tòa học",
                room: room,
                floor: 1,
                capacity: totalStudents,
                equipment: [],
                address: nil
            ),
            instructorId: "current_instructor",
            instructorName: "Giảng viên",
            totalStudents: totalStudents,
            attendedStudents: presentCount,
            absentStudents: totalStudents - presentCount,
            lateStudents: 0,
            materials: nil,
            assignments: nil,
            announcements: nil,
            recordingUrl: nil,
            notes: nil,
            metadata: nil,
            createdAt: Date(),
            updatedAt: nil
        )
    }
}

// MARK: - APILessonState to ClassStatus mapping
extension APILessonState {
    func toClassStatus() -> ClassStatus {
        switch self {
        case .scheduled: return .scheduled
        case .completed: return .completed
        case .cancelled: return .cancelled
        case .inProgress: return .inProgress
        }
    }
}

// MARK: - Mock Data for Testing
extension APILesson {
    static let mockAPILessons: [APILesson] = [
        APILesson(
            id: 1,
            name: "BUỔI 1: TƯ DUY LÃNH ĐẠO TRONG THỜI ĐẠI CHUYỂN HÓA",
            lessonNumber: 1,
            state: .completed,
            className: "TCI052501",
            classCode: "CLASS-00002",
            startDatetime: "2025-05-31T06:00:00",
            durationHours: 4.0,
            room: "101",
            totalStudents: 25,
            presentCount: 23,
            attendanceRate: 92.0,
            isToday: false,
            isUpcoming: false,
            isOverdue: false,
            needsAttention: false
        ),
        APILesson(
            id: 2,
            name: "BUỔI 2: XÂY DỰNG BẢN ĐỒ GIÁ TRỊ VÀ PHONG CÁCH LÃNH ĐẠO",
            lessonNumber: 2,
            state: .scheduled,
            className: "TCI052501",
            classCode: "CLASS-00002",
            startDatetime: "2025-07-28T14:00:00",
            durationHours: 2.0,
            room: "102",
            totalStudents: 30,
            presentCount: 0,
            attendanceRate: 0.0,
            isToday: true,
            isUpcoming: true,
            isOverdue: false,
            needsAttention: true
        ),
        APILesson(
            id: 3,
            name: "BUỔI 3: KIẾN TẠO NIỀM TIN VÀ ẢNH HƯỞNG TRONG ĐỘI NGŨ",
            lessonNumber: 3,
            state: .scheduled,
            className: "TLGD052501",
            classCode: "CLASS-00003",
            startDatetime: "2025-07-29T09:00:00",
            durationHours: 3.0,
            room: "201",
            totalStudents: 28,
            presentCount: 0,
            attendanceRate: 0.0,
            isToday: false,
            isUpcoming: true,
            isOverdue: false,
            needsAttention: true
        )
    ]
}
