//
//  MissionRepository.swift
//  mobile-app-template
//
//  Created for Mission System Repository
//

import Foundation
import Combine

protocol MissionRepositoryProtocol {
    func fetchMissions(for userId: String, filter: MissionFilter?) -> AnyPublisher<[Mission], Error>
    func fetchMissionById(_ id: String) -> AnyPublisher<Mission, Error>
    func updateMissionProgress(missionId: String, requirementId: String, newValue: Int) -> AnyPublisher<MissionProgress, Error>
    func claimMissionRewards(missionId: String, userId: String) -> AnyPublisher<MissionReward, Error>
    func fetchLeaderboard(type: LeaderboardType, limit: Int) -> AnyPublisher<[MissionLeaderboardEntry], Error>
    func fetchUserStats(userId: String) -> AnyPublisher<UserMissionStats, Error>
    func fetchUserBadges(userId: String) -> AnyPublisher<[MissionBadge], Error>
    func fetchStreaks(userId: String) -> AnyPublisher<[MissionStreak], Error>
}

class MissionRepository: MissionRepositoryProtocol {
    private let apiClient = APIClient.shared
    private var cancellables = Set<AnyCancellable>()
    
    func fetchMissions(for userId: String, filter: MissionFilter? = nil) -> AnyPublisher<[Mission], Error> {
        // In production, this would fetch from API
        // For now, return mock data with applied filters
        Just(Mission.mockMissions)
            .map { missions in
                guard let filter = filter else { return missions }
                
                return missions.filter { mission in
                    var matches = true
                    
                    if let category = filter.category {
                        matches = matches && mission.category == category
                    }
                    
                    if let type = filter.type {
                        matches = matches && mission.type == type
                    }
                    
                    if let status = filter.status {
                        matches = matches && mission.status == status
                    }
                    
                    if filter.onlyActive {
                        matches = matches && mission.isActive
                    }
                    
                    return matches
                }
            }
            .setFailureType(to: Error.self)
            .eraseToAnyPublisher()
    }
    
    func fetchMissionById(_ id: String) -> AnyPublisher<Mission, Error> {
        Just(Mission.mockMissions.first { $0.id == id })
            .compactMap { $0 }
            .setFailureType(to: Error.self)
            .eraseToAnyPublisher()
    }
    
    func updateMissionProgress(missionId: String, requirementId: String, newValue: Int) -> AnyPublisher<MissionProgress, Error> {
        // In production, this would update via API
        // For now, return a mock updated progress
        let progress = MissionProgress(
            missionId: missionId,
            userId: "user_1",
            completedRequirements: [requirementId],
            startedAt: Date().addingTimeInterval(-3600),
            lastUpdatedAt: Date(),
            completedAt: nil,
            claimedAt: nil
        )
        
        return Just(progress)
            .setFailureType(to: Error.self)
            .eraseToAnyPublisher()
    }
    
    func claimMissionRewards(missionId: String, userId: String) -> AnyPublisher<MissionReward, Error> {
        // Mock reward claim
        let reward = MissionReward(
            missionId: missionId,
            userId: userId,
            xpEarned: 100,
            coinsEarned: 50,
            badgeEarned: nil,
            claimedAt: Date()
        )
        
        return Just(reward)
            .setFailureType(to: Error.self)
            .eraseToAnyPublisher()
    }
    
    func fetchLeaderboard(type: LeaderboardType, limit: Int = 10) -> AnyPublisher<[MissionLeaderboardEntry], Error> {
        // Mock leaderboard data
        let mockEntries = [
            MissionLeaderboardEntry(
                id: "entry_1",
                userId: "user_1",
                userName: "Nguyễn Văn A",
                userAvatar: nil,
                totalXP: 1250,
                completedMissions: 15,
                badgesEarned: 5,
                rank: 1,
                previousRank: 2,
                department: "Công nghệ thông tin"
            ),
            MissionLeaderboardEntry(
                id: "entry_2",
                userId: "user_2",
                userName: "Trần Thị B",
                userAvatar: nil,
                totalXP: 1180,
                completedMissions: 14,
                badgesEarned: 4,
                rank: 2,
                previousRank: 1,
                department: "Kinh tế"
            ),
            MissionLeaderboardEntry(
                id: "entry_3",
                userId: "user_3",
                userName: "Lê Văn C",
                userAvatar: nil,
                totalXP: 950,
                completedMissions: 12,
                badgesEarned: 3,
                rank: 3,
                previousRank: 3,
                department: "Công nghệ thông tin"
            )
        ]
        
        return Just(Array(mockEntries.prefix(limit)))
            .setFailureType(to: Error.self)
            .eraseToAnyPublisher()
    }
    
    func fetchUserStats(userId: String) -> AnyPublisher<UserMissionStats, Error> {
        let stats = UserMissionStats(
            userId: userId,
            totalXP: 1250,
            totalCoins: 580,
            completedMissions: 15,
            activeMissions: 3,
            totalBadges: 5,
            currentLevel: 8,
            nextLevelXP: 1500,
            weeklyStreak: 5,
            monthlyCompletionRate: 85.5
        )
        
        return Just(stats)
            .setFailureType(to: Error.self)
            .eraseToAnyPublisher()
    }
    
    func fetchUserBadges(userId: String) -> AnyPublisher<[MissionBadge], Error> {
        let mockBadges = [
            MissionBadge(
                id: "badge_1",
                name: "Người hướng dẫn xuất sắc",
                description: "Hoàn thành 10 nhiệm vụ giảng dạy",
                iconName: "star.circle.fill",
                rarity: .rare,
                category: .teaching,
                unlockedAt: Date().addingTimeInterval(-7 * 24 * 3600)
            ),
            MissionBadge(
                id: "badge_2",
                name: "Nhà đổi mới",
                description: "Sử dụng 5 tính năng mới trong tháng",
                iconName: "lightbulb.circle.fill",
                rarity: .epic,
                category: .innovation,
                unlockedAt: Date().addingTimeInterval(-3 * 24 * 3600)
            )
        ]
        
        return Just(mockBadges)
            .setFailureType(to: Error.self)
            .eraseToAnyPublisher()
    }
    
    func fetchStreaks(userId: String) -> AnyPublisher<[MissionStreak], Error> {
        let mockStreaks = [
            MissionStreak(
                userId: userId,
                type: .daily,
                currentStreak: 5,
                longestStreak: 12,
                lastCompletedDate: Date().addingTimeInterval(-3600),
                streakMultiplier: 1.5
            ),
            MissionStreak(
                userId: userId,
                type: .weekly,
                currentStreak: 3,
                longestStreak: 8,
                lastCompletedDate: Date().addingTimeInterval(-2 * 24 * 3600),
                streakMultiplier: 1.3
            )
        ]
        
        return Just(mockStreaks)
            .setFailureType(to: Error.self)
            .eraseToAnyPublisher()
    }
}

// MARK: - Supporting Types
struct MissionFilter {
    let category: MissionCategory?
    let type: MissionType?
    let status: MissionStatus?
    let onlyActive: Bool
    
    static let active = MissionFilter(
        category: nil,
        type: nil,
        status: .active,
        onlyActive: true
    )
}

struct MissionReward: Codable {
    let missionId: String
    let userId: String
    let xpEarned: Int
    let coinsEarned: Int?
    let badgeEarned: String?
    let claimedAt: Date
}

enum LeaderboardType {
    case weekly
    case monthly
    case allTime
}

struct UserMissionStats: Codable {
    let userId: String
    let totalXP: Int
    let totalCoins: Int
    let completedMissions: Int
    let activeMissions: Int
    let totalBadges: Int
    let currentLevel: Int
    let nextLevelXP: Int
    let weeklyStreak: Int
    let monthlyCompletionRate: Double
}
