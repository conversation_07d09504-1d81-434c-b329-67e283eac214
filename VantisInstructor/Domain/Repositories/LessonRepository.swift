//
//  LessonRepository.swift
//  mobile-app-template
//
//  Created by Instructor App on 27/7/25.
//

import Foundation

// MARK: - Lesson Repository Protocol
protocol LessonRepositoryProtocol {
    func getLessons(page: Int, limit: Int, filters: LessonFilters?) async throws -> LessonListResponse
    func getLessonById(_ id: Int) async throws -> Lesson
    func getAvailableLessonsForQuiz(quizId: Int) async throws -> LessonListResponse
}

// MARK: - Lesson Repository Implementation
class LessonRepository: LessonRepositoryProtocol {
    private let apiClient = APIClient.shared
    
    func getLessons(page: Int, limit: Int, filters: LessonFilters?) async throws -> LessonListResponse {
        var queryParams: [String: Any] = [
            "page": page,
            "limit": limit
        ]
        
        // Add filters to query parameters
        if let filters = filters {
            if let courseId = filters.courseId {
                queryParams["course_id"] = courseId
            }
            if let type = filters.type {
                queryParams["type"] = type.rawValue
            }
            if let status = filters.status {
                queryParams["status"] = status.rawValue
            }
            if let search = filters.search, !search.isEmpty {
                queryParams["search"] = search
            }
            if let startDate = filters.startDate {
                queryParams["start_date"] = ISO8601DateFormatter().string(from: startDate)
            }
            if let endDate = filters.endDate {
                queryParams["end_date"] = ISO8601DateFormatter().string(from: endDate)
            }
        }
        
        let queryString = queryParams.map { "\($0.key)=\($0.value)" }.joined(separator: "&")
        let endpoint = "instructors/lessons?\(queryString)"
        
        return try await apiClient.request(
            endpoint: endpoint,
            method: .GET,
            responseType: LessonListResponse.self
        )
    }
    
    func getLessonById(_ id: Int) async throws -> Lesson {
        return try await apiClient.request(
            endpoint: "instructors/lessons/\(id)",
            method: .GET,
            responseType: Lesson.self
        )
    }
    
    func getAvailableLessonsForQuiz(quizId: Int) async throws -> LessonListResponse {
        return try await apiClient.request(
            endpoint: "instructors/lessons/available-for-quiz/\(quizId)",
            method: .GET,
            responseType: LessonListResponse.self
        )
    }
}

// MARK: - Mock Lesson Repository
class MockLessonRepository: LessonRepositoryProtocol {
    private var lessons: [Lesson] = Lesson.mockLessons
    
    func getLessons(page: Int, limit: Int, filters: LessonFilters?) async throws -> LessonListResponse {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        
        var filteredLessons = lessons
        
        // Apply filters
        if let filters = filters {
            if let courseId = filters.courseId {
                filteredLessons = filteredLessons.filter { $0.courseId == courseId }
            }
            if let type = filters.type {
                filteredLessons = filteredLessons.filter { $0.type == type }
            }
            if let status = filters.status {
                filteredLessons = filteredLessons.filter { $0.status == status }
            }
            if let search = filters.search, !search.isEmpty {
                filteredLessons = filteredLessons.filter { 
                    $0.title.localizedCaseInsensitiveContains(search) ||
                    $0.courseName.localizedCaseInsensitiveContains(search) ||
                    $0.courseCode.localizedCaseInsensitiveContains(search)
                }
            }
            if let startDate = filters.startDate {
                filteredLessons = filteredLessons.filter { $0.scheduledDate >= startDate }
            }
            if let endDate = filters.endDate {
                filteredLessons = filteredLessons.filter { $0.scheduledDate <= endDate }
            }
        }
        
        // Apply pagination
        let startIndex = (page - 1) * limit
        let endIndex = min(startIndex + limit, filteredLessons.count)
        let paginatedLessons = Array(filteredLessons[startIndex..<endIndex])
        
        let totalPages = Int(ceil(Double(filteredLessons.count) / Double(limit)))
        
        return LessonListResponse(
            success: true,
            message: "Lessons retrieved successfully",
            data: LessonListData(
                lessons: paginatedLessons,
                totalCount: filteredLessons.count,
                totalPages: totalPages,
                currentPage: page,
                hasNextPage: page < totalPages,
                hasPreviousPage: page > 1
            ),
            traceId: UUID().uuidString,
            requestId: UUID().uuidString
        )
    }
    
    func getLessonById(_ id: Int) async throws -> Lesson {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 300_000_000) // 0.3 seconds
        
        guard let lesson = lessons.first(where: { $0.id == id }) else {
            throw APIError(code: "NOT_FOUND", message: "Lesson not found", field: nil, details: nil)
        }
        
        return lesson
    }
    
    func getAvailableLessonsForQuiz(quizId: Int) async throws -> LessonListResponse {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        
        // Filter lessons that are scheduled or in progress and don't already have this quiz assigned
        let availableLessons = lessons.filter { lesson in
            (lesson.status == .scheduled || lesson.status == .inProgress) &&
            !(lesson.quizzes?.contains(String(quizId)) ?? false)
        }
        
        return LessonListResponse(
            success: true,
            message: "Available lessons retrieved successfully",
            data: LessonListData(
                lessons: availableLessons,
                totalCount: availableLessons.count,
                totalPages: 1,
                currentPage: 1,
                hasNextPage: false,
                hasPreviousPage: false
            ),
            traceId: UUID().uuidString,
            requestId: UUID().uuidString
        )
    }
}
