//
//  AuthRepository.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import Foundation
import Combine
import UIKit

// MARK: - Backend Response Models
struct BackendAuthResponse: Codable {
    let success: Bool
    let message: String
    let data: AuthData

    struct AuthData: Codable {
        let access_token: String
        let token_type: String
        let expires_in: Int
        let refresh_token: String?
        let refresh_token_expires_in: Int?
        let scope: String?
        let issued_at: Int?
        let user_id: Int
        let device_registered: Bool
        let device_info: DeviceInfo?
        let remember_me: Bool?
        let login_method: String

        struct DeviceInfo: Codable {
            let device_id: String?
            let device_name: String?
            let device_type: String?
            let os_name: String?
            let os_version: String?
            let app_version: String?
            let browser_name: String?
            let browser_version: String?
            let user_agent: String?
            let ip_address: String?
            let location: Location?

            struct Location: Codable {
                let city: String?
                let country: String?
                let latitude: Double?
                let longitude: Double?
            }
        }

        enum CodingKeys: String, CodingKey {
            case access_token, token_type, expires_in, refresh_token
            case refresh_token_expires_in, scope, issued_at
            case user_id, device_registered, device_info, remember_me, login_method
        }
    }
}

// MARK: - Auth Repository Protocol
protocol AuthRepositoryProtocol {
    func login(email: String, password: String) async throws -> AuthResponse
    func register(request: RegisterRequest) async throws -> AuthResponse
    func logout() async throws
    func refreshToken() async throws -> AuthResponse
    func getCurrentUser() async throws -> User
    func updateProfile(request: UpdateProfileRequest) async throws -> User
    func changePassword(request: ChangePasswordRequest) async throws
    func deleteAccount() async throws
}

// MARK: - Mock Auth Repository (for demo/template)
class MockAuthRepository: AuthRepositoryProtocol {

    func login(email: String, password: String) async throws -> AuthResponse {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second

        // Mock successful login response
        let mockUser = User(
            id: "demo-user-123",
            email: email,
            firstName: "Demo",
            lastName: "User",
            phone: "+**********",
            role: .user,
            isActive: true,
            avatar: nil,
            dateOfBirth: nil,
            lastLoginAt: Date(),
            createdAt: Date(),
            updatedAt: Date(),
            businessName: nil,
            businessId: nil,
            category: nil,
            businessPhone: nil,
            website: nil,
            businessDescription: nil,
            businessStatus: nil,
            onboardedAt: nil
        )

        return AuthResponse(
            access_token: "mock-jwt-token-demo-123456789",
            user: mockUser,
            tokens: nil
        )
    }

    func register(request: RegisterRequest) async throws -> AuthResponse {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 1_500_000_000) // 1.5 seconds

        // Mock successful registration response
        let mockUser = User(
            id: "demo-user-new-123",
            email: request.email,
            firstName: request.firstName,
            lastName: request.lastName,
            phone: request.phone,
            role: .user,
            isActive: true,
            avatar: nil,
            dateOfBirth: nil,
            lastLoginAt: nil,
            createdAt: Date(),
            updatedAt: Date(),
            businessName: nil,
            businessId: nil,
            category: nil,
            businessPhone: nil,
            website: nil,
            businessDescription: nil,
            businessStatus: nil,
            onboardedAt: nil
        )

        return AuthResponse(
            access_token: "mock-jwt-token-new-demo-123456789",
            user: mockUser,
            tokens: nil
        )
    }

    func logout() async throws {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        // Mock successful logout (no-op)
    }

    func refreshToken() async throws -> AuthResponse {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 800_000_000) // 0.8 seconds

        // Mock refreshed token response
        let mockUser = User(
            id: "demo-user-123",
            email: "<EMAIL>",
            firstName: "Demo",
            lastName: "User",
            phone: "+**********",
            role: .user,
            isActive: true,
            avatar: nil,
            dateOfBirth: nil,
            lastLoginAt: Date(),
            createdAt: Date(),
            updatedAt: Date(),
            businessName: nil,
            businessId: nil,
            category: nil,
            businessPhone: nil,
            website: nil,
            businessDescription: nil,
            businessStatus: nil,
            onboardedAt: nil
        )

        return AuthResponse(
            access_token: "mock-jwt-token-refreshed-123456789",
            user: mockUser,
            tokens: nil
        )
    }

    func getCurrentUser() async throws -> User {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 600_000_000) // 0.6 seconds

        // Mock current user response
        return User(
            id: "demo-user-123",
            email: "<EMAIL>",
            firstName: "Demo",
            lastName: "User",
            phone: "+**********",
            role: .user,
            isActive: true,
            avatar: nil,
            dateOfBirth: nil,
            lastLoginAt: Date(),
            createdAt: Date(),
            updatedAt: Date(),
            businessName: nil,
            businessId: nil,
            category: nil,
            businessPhone: nil,
            website: nil,
            businessDescription: nil,
            businessStatus: nil,
            onboardedAt: nil
        )
    }

    func updateProfile(request: UpdateProfileRequest) async throws -> User {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 1_200_000_000) // 1.2 seconds

        // Mock updated user response
        return User(
            id: "demo-user-123",
            email: "<EMAIL>",
            firstName: request.firstName,
            lastName: request.lastName,
            phone: request.phone,
            role: .user,
            isActive: true,
            avatar: request.avatar,
            dateOfBirth: request.dateOfBirth,
            lastLoginAt: Date(),
            createdAt: Date(),
            updatedAt: Date(),
            businessName: nil,
            businessId: nil,
            category: nil,
            businessPhone: nil,
            website: nil,
            businessDescription: nil,
            businessStatus: nil,
            onboardedAt: nil
        )
    }

    func changePassword(request: ChangePasswordRequest) async throws {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        // Mock successful password change (no-op)
    }

    func deleteAccount() async throws {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 1_500_000_000) // 1.5 seconds
        // Mock successful account deletion (no-op)
    }
}

// MARK: - Auth Repository Implementation
class AuthRepository: AuthRepositoryProtocol {
    private let apiClient = APIClient.shared
    private let tokenManager = TokenManager.shared

    // MARK: - Helper Methods
    private func getDeviceInfo() -> [String: Any] {
        return [
            "platform": "iOS",
            "deviceModel": UIDevice.current.model,
            "systemVersion": UIDevice.current.systemVersion,
            "appVersion": AppConstants.AppInfo.fullVersion,
            "deviceId": UIDevice.current.identifierForVendor?.uuidString ?? "",
            "locale": Locale.current.identifier,
            "timezone": TimeZone.current.identifier
        ]
    }
    
    // MARK: - Login
    func login(email: String, password: String) async throws -> AuthResponse {
        print("🔐 Starting login process for email: \(email)")

        // Create device info
        let deviceInfo = LoginRequest.DeviceInfo(
            deviceId: UIDevice.current.identifierForVendor?.uuidString ?? "unknown-device",
            deviceName: UIDevice.current.name,
            deviceType: "mobile",
            osName: "iOS",
            osVersion: UIDevice.current.systemVersion,
            appVersion: Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0",
            browserName: "Safari",
            browserVersion: UIDevice.current.systemVersion,
            userAgent: "Mozilla/5.0 (iPhone; CPU iPhone OS \(UIDevice.current.systemVersion.replacingOccurrences(of: ".", with: "_")) like Mac OS X) AppleWebKit/605.1.15",
            ipAddress: "*************", // Default IP, could be improved with actual IP detection
            location: LoginRequest.DeviceInfo.Location(
                city: "Hanoi",
                country: "Vietnam",
                latitude: 21.0285,
                longitude: 105.8542
            )
        )

        let request = LoginRequest(
            username: email, // Use email as username
            password: password,
            deviceInfo: deviceInfo,
            rememberMe: false,
            mfaCode: nil
        )

        print("🔐 Sending login request to endpoint: \(APIEndpoints.Auth.signIn)")
        print("🔐 Request parameters: \(try request.toDictionary())")

        // Backend returns direct response, not wrapped in APIResponse
        let backendResponse: BackendAuthResponse = try await apiClient.request(
            endpoint: APIEndpoints.Auth.signIn,
            method: .POST,
            parameters: try request.toDictionary(),
            responseType: BackendAuthResponse.self,
            requiresAuth: false
        )

        // Convert backend response to our AuthResponse format
        // Create user object from available data (API doesn't return full user info)
        let user = User(
            id: String(backendResponse.data.user_id),
            email: email, // Use the email from login request
            firstName: nil,
            lastName: nil,
            phone: nil,
            role: .user, // Default role, could be updated later
            isActive: true,
            avatar: nil,
            dateOfBirth: nil,
            lastLoginAt: Date(),
            createdAt: Date(),
            updatedAt: Date(),
            businessName: nil,
            businessId: nil,
            category: nil,
            businessPhone: nil,
            website: nil,
            businessDescription: nil,
            businessStatus: nil,
            onboardedAt: nil
        )

        let tokens = AuthTokens(
            accessToken: backendResponse.data.access_token,
            refreshToken: backendResponse.data.refresh_token,
            tokenType: backendResponse.data.token_type,
            expiresIn: backendResponse.data.expires_in
        )

        let authResponse = AuthResponse(access_token: backendResponse.data.access_token, user: user, tokens: tokens)

        // Save token and user data
        await tokenManager.saveToken(tokens.accessToken)
        await tokenManager.saveUser(user)

        // Log successful login
        Logger.shared.logAuthentication("login", success: true)

        return authResponse
    }
    
    // MARK: - Register
    func register(request: RegisterRequest) async throws -> AuthResponse {
        // Backend returns direct response, not wrapped in APIResponse
        let backendResponse: BackendAuthResponse = try await apiClient.request(
            endpoint: APIEndpoints.Auth.register,
            method: .POST,
            parameters: try request.toDictionary(),
            responseType: BackendAuthResponse.self,
            requiresAuth: false
        )

        // Convert backend response to our AuthResponse format
        // Create user object from available data (API doesn't return full user info)
        let user = User(
            id: String(backendResponse.data.user_id),
            email: request.email,
            firstName: request.firstName,
            lastName: request.lastName,
            phone: request.phone,
            role: .user, // Default role
            isActive: true,
            avatar: nil,
            dateOfBirth: nil,
            lastLoginAt: Date(),
            createdAt: Date(),
            updatedAt: Date(),
            businessName: nil,
            businessId: nil,
            category: nil,
            businessPhone: nil,
            website: nil,
            businessDescription: nil,
            businessStatus: nil,
            onboardedAt: nil
        )

        let tokens = AuthTokens(
            accessToken: backendResponse.data.access_token,
            refreshToken: backendResponse.data.refresh_token,
            tokenType: backendResponse.data.token_type,
            expiresIn: backendResponse.data.expires_in
        )

        let authResponse = AuthResponse(access_token: backendResponse.data.access_token, user: user, tokens: tokens)

        // Save token and user data
        await tokenManager.saveToken(tokens.accessToken)
        await tokenManager.saveUser(user)

        // Log successful registration
        Logger.shared.logAuthentication("register", success: true)

        return authResponse
    }
    
    // MARK: - Logout
    func logout() async throws {
        // Call logout endpoint to invalidate token on server
        do {
            let _: EmptyResponse = try await apiClient.request(
                endpoint: "/auth/logout",
                method: .POST,
                responseType: EmptyResponse.self
            )
        } catch {
            // Continue with local logout even if server call fails
            print("Server logout failed: \(error)")
        }

        // Clear ALL local data using DataCleanupManager
        DataCleanupManager.shared.clearAuthenticationData()
    }
    
    // MARK: - Refresh Token
    func refreshToken() async throws -> AuthResponse {
        let response: AuthResponse = try await apiClient.request(
            endpoint: "/auth/refresh",
            method: .POST,
            responseType: AuthResponse.self
        )
        
        // Update token and user data
        await tokenManager.saveToken(response.access_token)
        await tokenManager.saveUser(response.user)
        
        return response
    }
    
    // MARK: - Get Current User
    func getCurrentUser() async throws -> User {
        let apiUser: APIUser = try await apiClient.request(
            endpoint: "/auth/me",
            method: .GET,
            responseType: APIUser.self
        )

        // Convert to app User model
        let user = apiUser.toUser()

        // Update cached user data
        await tokenManager.saveUser(user)

        return user
    }
    
    // MARK: - Update Profile
    func updateProfile(request: UpdateProfileRequest) async throws -> User {
        let user: User = try await apiClient.request(
            endpoint: "/users/profile",
            method: .PUT,
            parameters: try request.toDictionary(),
            responseType: User.self
        )
        
        // Update cached user data
        await tokenManager.saveUser(user)
        
        return user
    }
    
    // MARK: - Change Password
    func changePassword(request: ChangePasswordRequest) async throws {
        let _: EmptyResponse = try await apiClient.request(
            endpoint: "/auth/change-password",
            method: .POST,
            parameters: try request.toDictionary(),
            responseType: EmptyResponse.self
        )
    }
    
    // MARK: - Delete Account
    func deleteAccount() async throws {
        let _: EmptyResponse = try await apiClient.request(
            endpoint: "/auth/delete-account",
            method: .DELETE,
            responseType: EmptyResponse.self
        )
        
        // Clear local data
        await tokenManager.clearToken()
    }
}

// MARK: - Helper Extensions
extension Encodable {
    func toDictionary() throws -> [String: Any] {
        let data = try JSONEncoder().encode(self)
        guard let dictionary = try JSONSerialization.jsonObject(with: data, options: .allowFragments) as? [String: Any] else {
            throw NSError(domain: "EncodingError", code: 0, userInfo: [NSLocalizedDescriptionKey: "Failed to convert to dictionary"])
        }
        return dictionary
    }
}



// MARK: - Auth State Manager
class AuthStateManager: ObservableObject {
    @Published var isAuthenticated = false
    @Published var currentUser: User?
    @Published var isLoading = false
    
    private let authRepository: AuthRepositoryProtocol
    private let tokenManager = TokenManager.shared
    
    init(authRepository: AuthRepositoryProtocol = AuthRepository()) {
        self.authRepository = authRepository

        // FORCE clean state on init to prevent any auto-signin
        print("🔐 AuthStateManager: Initializing with FORCED clean state")
        isAuthenticated = false
        currentUser = nil

        // Don't auto-check auth state on init to prevent auto-signin
        // Only check if explicitly requested

        // Ensure UI updates immediately
        DispatchQueue.main.async {
            self.objectWillChange.send()
        }

        // Don't auto-observe instructor auth service to prevent auto-signin
        // setupInstructorAuthObserver()

        print("🔐 AuthStateManager: Initialization complete - isAuthenticated: \(isAuthenticated)")
    }
    
    /// Manually check authentication state - call this when you want to verify auth
    @MainActor func checkAuthState() {
        print("🔐 AuthStateManager: Manually checking auth state")

        // Check if we have a valid token and user data
        guard let token = tokenManager.getToken() else {
            print("🔐 AuthStateManager: No token found - user not authenticated")
            isAuthenticated = false
            currentUser = nil
            return
        }

        // Check if we have cached user data
        guard let cachedUser = tokenManager.getUser(User.self) else {
            print("🔐 AuthStateManager: No cached user data - user not authenticated")
            isAuthenticated = false
            currentUser = nil
            return
        }

        // Restore authentication state
        print("🔐 AuthStateManager: Restoring authentication state from cached data")
        print("🔐 AuthStateManager: Cached user - ID: \(cachedUser.id), Role: \(cachedUser.role), Name: \(cachedUser.displayName)")

        isAuthenticated = true
        currentUser = cachedUser

        print("🔐 AuthStateManager: Authentication state restored successfully")

        // Debug: Print current token
        print("🔐 AuthStateManager: Current token: \(String(token.prefix(50)))...")

        if let user = currentUser {
            print("🔐 AuthStateManager: checkAuthState - currentUser: ID: \(user.id), Role: \(user.role), Name: \(user.displayName)")
        } else {
            print("🔐 AuthStateManager: checkAuthState - currentUser is nil")
        }
    }
    
    @MainActor
    func login(email: String, password: String) async throws {
        isLoading = true
        defer { isLoading = false }

        // Note: Don't clear cached user data here as it will be overwritten anyway
        print("🔐 AuthStateManager: Starting fresh login")

        let response = try await authRepository.login(email: email, password: password)

        print("🔐 AuthStateManager: Setting isAuthenticated = true")
        isAuthenticated = true
        currentUser = response.user
        print("🔐 AuthStateManager: isAuthenticated is now \(isAuthenticated)")
        print("🔐 AuthStateManager: currentUser set to - ID: \(response.user.id), Role: \(response.user.role), Name: \(response.user.displayName)")
    }
    
    @MainActor
    func register(request: RegisterRequest) async throws {
        isLoading = true
        defer { isLoading = false }

        // Clear any cached user data before fresh registration
        DataCleanupManager.shared.clearCachedUserData()
        print("🔐 AuthStateManager: Cleared cached user data before registration")

        let response = try await authRepository.register(request: request)

        isAuthenticated = true
        currentUser = response.user
    }
    
    @MainActor
    func logout() async {
        isLoading = true
        defer { isLoading = false }

        print("🔐 AuthStateManager: Starting logout")

        do {
            try await authRepository.logout()
        } catch {
            print("🔐 AuthStateManager: Repository logout failed: \(error)")
        }

        // Force clear all local state
        isAuthenticated = false
        currentUser = nil

        // Clear TokenManager completely
        tokenManager.clearToken()

        // Clear all auth-related data
        DataCleanupManager.shared.clearAuthenticationData()

        print("🔐 AuthStateManager: Logout completed - isAuthenticated: \(isAuthenticated)")
    }
    
    @MainActor
    func refreshUserData() async {
        guard isAuthenticated else { return }

        do {
            currentUser = try await authRepository.getCurrentUser()
        } catch {
            print("Failed to refresh user data: \(error)")
            // If refresh fails due to invalid token, logout
            if error is NetworkError {
                await logout()
            }
        }
    }

    @MainActor
    func clearCacheAndRecheck() {
        print("🔐 AuthStateManager: Clearing cache - manual auth check required")
        // Clear cached user data but keep token
        let currentToken = tokenManager.getToken()
        tokenManager.clearToken()
        if let token = currentToken {
            tokenManager.saveToken(token)
        }
        // Don't auto-check auth state to prevent auto-signin
        // Call checkAuthState() manually if needed
    }

    @MainActor
    func updateTokenAndUser(token: String, user: User) {
        print("🔐 AuthStateManager: Updating token and user data")
        print("🔐 AuthStateManager: New user - ID: \(user.id), Role: \(user.role), Name: \(user.displayName)")

        tokenManager.saveToken(token)
        tokenManager.saveUser(user)

        isAuthenticated = true
        currentUser = user

        print("🔐 AuthStateManager: Token and user updated successfully")
    }

    @MainActor
    func forceRecheck() {
        print("🔐 AuthStateManager: Force rechecking auth state")
        checkAuthState()
    }

    @MainActor
    func forceClearAllData() {
        print("🔐 AuthStateManager: Force clearing ALL user data")
        DataCleanupManager.shared.clearAllUserData()
        isAuthenticated = false
        currentUser = nil
        // Don't call checkAuthState() to prevent auto-signin
        print("🔐 AuthStateManager: All data cleared and state reset")
    }

    // MARK: - Instructor Auth Integration
    private var cancellables = Set<AnyCancellable>()

    private func setupInstructorAuthObserver() {
        // Observe instructor auth service changes
        // No instructor authentication subscriptions in this version
    }

    // TEMPORARILY DISABLED - InstructorUser not available
    // private func convertInstructorToUser(_ instructor: InstructorUser) -> User {
    //     let dateFormatter = ISO8601DateFormatter()
    //
    //     return User(
    //         id: String(instructor.id),
    //         email: instructor.email,
    //         firstName: instructor.firstName,
    //         lastName: instructor.lastName,
    //         phone: instructor.phone,
    //         walletAddress: nil,
    //         role: .admin, // Map instructor to admin role
    //         isActive: instructor.isActive,
    //         avatar: instructor.avatar,
    //         dateOfBirth: nil,
    //         lastLoginAt: instructor.lastLoginAt != nil ? dateFormatter.date(from: instructor.lastLoginAt!) : nil,
    //         createdAt: dateFormatter.date(from: instructor.createdAt) ?? Date(),
    //         updatedAt: instructor.updatedAt != nil ? dateFormatter.date(from: instructor.updatedAt!) : nil,
    //         businessName: nil,
    //         businessId: nil,
    //         category: nil,
    //         businessPhone: nil,
    //         website: nil,
    //         businessDescription: nil,
    //         businessStatus: nil,
    //         onboardedAt: nil
    //     )
    // }
}
