//
//  TransactionRepository.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import Foundation
import Combine

// MARK: - Transaction Repository Protocol
protocol TransactionRepositoryProtocol {
    func getTransactions(page: Int, limit: Int, type: TransactionType?) async throws -> TransactionListResponse
    func getTransaction(id: String) async throws -> Transaction
    func earnTokens(request: EarnTokensRequest) async throws -> Transaction
    func transferTokens(request: TransferTokensRequest) async throws -> Transaction

    func getTransactionStats() async throws -> TransactionStats
}

// MARK: - Transaction Repository Implementation
class TransactionRepository: TransactionRepositoryProtocol {
    private let apiClient = APIClient.shared
    
    // MARK: - Get Transactions
    func getTransactions(page: Int = 1, limit: Int = 20, type: TransactionType? = nil) async throws -> TransactionListResponse {
        var parameters: [String: Any] = [
            "limit": limit,
            "offset": (page - 1) * limit
        ]

        if let type = type {
            parameters["type"] = type.rawValue
        }

        // Use the correct backend endpoint for transaction history
        print("🔍 [TransactionRepository] Calling /transactions/history with parameters: \(parameters)")

        do {
            let response = try await apiClient.request(
                endpoint: "/transactions/history",
                method: .GET,
                parameters: parameters,
                responseType: TransactionHistoryResponse.self
            )
            print("✅ [TransactionRepository] Received \(response.transactions.count) transactions, total: \(response.total)")

            // Convert backend response to expected format
            return TransactionListResponse(
                transactions: response.transactions,
                total: response.total,
                page: page,
                limit: response.limit,
                hasMore: response.offset + response.transactions.count < response.total
            )
        } catch {
            print("❌ [TransactionRepository] Error calling /transactions/history: \(error)")
            throw error
        }
    }
    
    // MARK: - Get Single Transaction
    func getTransaction(id: String) async throws -> Transaction {
        return try await apiClient.request(
            endpoint: "/transactions/\(id)",
            method: .GET,
            responseType: Transaction.self
        )
    }
    
    // MARK: - Earn Tokens
    func earnTokens(request: EarnTokensRequest) async throws -> Transaction {
        return try await apiClient.request(
            endpoint: "/transactions/earn",
            method: .POST,
            parameters: try request.toDictionary(),
            responseType: Transaction.self
        )
    }
    
    // MARK: - Transfer Tokens
    func transferTokens(request: TransferTokensRequest) async throws -> Transaction {
        return try await apiClient.request(
            endpoint: "/transactions/transfer",
            method: .POST,
            parameters: try request.toDictionary(),
            responseType: Transaction.self
        )
    }
    

    
    // MARK: - Get Transaction Statistics
    func getTransactionStats() async throws -> TransactionStats {
        return try await apiClient.request(
            endpoint: "/transactions/stats",
            method: .GET,
            responseType: TransactionStats.self
        )
    }
}



// MARK: - Transaction Statistics Model
struct TransactionStats: Codable {
    let totalEarned: Double
    let totalRedeemed: Double
    let totalTransferred: Double
    let transactionCount: Int
    let averageTransaction: Double
    let monthlyStats: [MonthlyTransactionStats]
    
    var netBalance: Double {
        return totalEarned - totalRedeemed - totalTransferred
    }
    
    var displayTotalEarned: String {
        return totalEarned.formatAsToken()
    }
    
    var displayTotalRedeemed: String {
        return totalRedeemed.formatAsToken()
    }
    
    var displayNetBalance: String {
        return netBalance.formatAsToken()
    }
}

// MARK: - Monthly Transaction Stats
struct MonthlyTransactionStats: Codable, Identifiable {
    let id = UUID()
    let month: String
    let year: Int
    let totalEarned: Double
    let totalRedeemed: Double
    let transactionCount: Int
    
    private enum CodingKeys: String, CodingKey {
        case month, year, totalEarned, totalRedeemed, transactionCount
    }
    
    var displayMonth: String {
        return "\(month) \(year)"
    }
    
    var netAmount: Double {
        return totalEarned - totalRedeemed
    }
}

// MARK: - Transaction Manager
@MainActor
class TransactionManager: ObservableObject {
    static let shared = TransactionManager()

    @Published var transactions: [Transaction] = []
    @Published var stats: TransactionStats?
    @Published var isLoading = false
    @Published var error: Error?

    private let repository: TransactionRepositoryProtocol
    private var currentPage = 1
    private var hasMorePages = true

    init(repository: TransactionRepositoryProtocol = TransactionRepository()) {
        self.repository = repository
    }
    
    // MARK: - Load Transactions
    func loadTransactions(refresh: Bool = false) async {
        if refresh {
            currentPage = 1
            hasMorePages = true
            transactions.removeAll()
        }
        
        guard hasMorePages && !isLoading else { return }
        
        isLoading = true
        error = nil
        
        do {
            let response = try await repository.getTransactions(
                page: currentPage,
                limit: AppConstants.Pagination.defaultLimit,
                type: nil
            )
            
            if refresh {
                transactions = response.transactions
            } else {
                transactions.append(contentsOf: response.transactions)
            }
            
            hasMorePages = response.hasMore
            currentPage += 1
            
        } catch {
            self.error = error
        }
        
        isLoading = false
    }
    

    
    // MARK: - Load Statistics
    func loadStats() async {
        do {
            stats = try await repository.getTransactionStats()
        } catch {
            self.error = error
        }
    }
    
    // MARK: - Earn Tokens
    func earnTokens(businessId: String, amountUsd: Double, description: String? = nil) async -> Bool {
        isLoading = true
        error = nil
        
        do {
            let request = EarnTokensRequest(
                businessId: businessId,
                amountUsd: amountUsd,
                description: description
            )
            
            let transaction = try await repository.earnTokens(request: request)
            
            // Add new transaction to the beginning of the list
            transactions.insert(transaction, at: 0)
            

            
            isLoading = false
            return true
            
        } catch {
            self.error = error
            isLoading = false
            return false
        }
    }
    
    // MARK: - Transfer Tokens
    func transferTokens(recipientAddress: String, amountTokens: Double, description: String? = nil) async -> Bool {
        isLoading = true
        error = nil
        
        do {
            let request = TransferTokensRequest(
                recipientAddress: recipientAddress,
                amountTokens: amountTokens,
                description: description
            )
            
            let transaction = try await repository.transferTokens(request: request)
            
            // Add new transaction to the beginning of the list
            transactions.insert(transaction, at: 0)
            

            
            isLoading = false
            return true
            
        } catch {
            self.error = error
            isLoading = false
            return false
        }
    }
    
    // MARK: - Refresh All Data
    func refreshAll() async {
        await loadTransactions(refresh: true)
        await loadStats()
    }
    
    // MARK: - Filter Transactions
    func filterTransactions(by type: TransactionType?) -> [Transaction] {
        guard let type = type else { return transactions }
        return transactions.filter { $0.type == type }
    }
    
    // MARK: - Get Transaction by ID
    func getTransaction(id: String) -> Transaction? {
        return transactions.first { $0.id == id }
    }
    
    // MARK: - Clear Error
    func clearError() {
        error = nil
    }
}
