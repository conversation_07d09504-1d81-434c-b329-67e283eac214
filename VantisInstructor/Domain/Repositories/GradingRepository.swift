//
//  GradingRepository.swift
//  mobile-app-template
//
//  Created by Inst<PERSON><PERSON> <PERSON>pp on 24/7/25.
//

import Foundation

// MARK: - Grading Repository Protocol
protocol GradingRepositoryProtocol {
    func getPendingGrading(page: Int, limit: Int, quizId: Int?) async throws -> PendingGradingResponse
    func getAttemptDetail(attemptId: Int) async throws -> AttemptDetailResponse
    func gradeAttempt(attemptId: Int, request: GradeAttemptRequest) async throws -> GradeAttemptResponse
    func getQuizAttempts(quizId: Int, page: Int, limit: Int, filters: AttemptFilters?) async throws -> QuizAttemptsResponse
}

// MARK: - Grading Repository Implementation
class GradingRepository: GradingRepositoryProtocol {
    private let apiClient = APIClient.shared
    
    func getPendingGrading(page: Int = 1, limit: Int = 50, quizId: Int? = nil) async throws -> PendingGradingResponse {
        var queryParams = [
            "page": "\(page)",
            "limit": "\(limit)"
        ]
        
        if let quizId = quizId {
            queryParams["quiz_id"] = "\(quizId)"
        }
        
        let queryString = queryParams.map { "\($0.key)=\($0.value)" }.joined(separator: "&")
        let endpoint = "instructors/quizzes/grading/pending?\(queryString)"
        
        return try await apiClient.request(
            endpoint: endpoint,
            method: .GET,
            responseType: PendingGradingResponse.self
        )
    }
    
    func getAttemptDetail(attemptId: Int) async throws -> AttemptDetailResponse {
        return try await apiClient.request(
            endpoint: "instructors/quizzes/grading/attempts/\(attemptId)",
            method: .GET,
            responseType: AttemptDetailResponse.self
        )
    }
    
    func gradeAttempt(attemptId: Int, request: GradeAttemptRequest) async throws -> GradeAttemptResponse {
        return try await apiClient.request(
            endpoint: "instructors/quizzes/grading/attempts/\(attemptId)/grade",
            method: .POST,
            responseType: GradeAttemptResponse.self
        )
    }
    
    func getQuizAttempts(quizId: Int, page: Int = 1, limit: Int = 20, filters: AttemptFilters? = nil) async throws -> QuizAttemptsResponse {
        var queryParams = [
            "page": "\(page)",
            "limit": "\(limit)"
        ]
        
        // Add filters if provided
        if let filters = filters {
            if let state = filters.state {
                queryParams["state"] = state.rawValue
            }
            if let studentId = filters.studentId {
                queryParams["student_id"] = "\(studentId)"
            }
            if let needsGrading = filters.needsGrading {
                queryParams["needs_grading"] = "\(needsGrading)"
            }
            if let search = filters.search, !search.isEmpty {
                queryParams["search"] = search
            }
        }
        
        let queryString = queryParams.map { "\($0.key)=\($0.value)" }.joined(separator: "&")
        let endpoint = "instructors/quizzes/\(quizId)/attempts?\(queryString)"
        
        return try await apiClient.request(
            endpoint: endpoint,
            method: .GET,
            responseType: QuizAttemptsResponse.self
        )
    }
}

// MARK: - Mock Grading Repository for Testing
class MockGradingRepository: GradingRepositoryProtocol {
    private var pendingAttempts: [QuizAttempt] = []
    
    init() {
        // Add some mock pending grading data
        pendingAttempts = [
            QuizAttempt(
                id: 1,
                studentId: 101,
                studentName: "Nguyễn Văn An",
                studentCode: "SV001",
                studentEmail: "<EMAIL>",
                quizId: 1,
                quizName: "Kiểm tra Toán học",
                quizCode: "QUIZ001",
                attemptNumber: 1,
                startTime: Date().addingTimeInterval(-3600), // 1 hour ago
                endTime: Date().addingTimeInterval(-1800), // 30 minutes ago
                timeSpent: 1800, // 30 minutes
                score: 0, // Not graded yet
                maxScore: 100,
                percentage: 0,
                isPassed: false,
                state: .completed,
                needsGrading: true,
                feedback: nil,
                submittedAt: Date().addingTimeInterval(-1800),
                gradedAt: nil,
                gradedBy: nil
            ),
            QuizAttempt(
                id: 2,
                studentId: 102,
                studentName: "Trần Thị Bình",
                studentCode: "SV002",
                studentEmail: "<EMAIL>",
                quizId: 1,
                quizName: "Kiểm tra Toán học",
                quizCode: "QUIZ001",
                attemptNumber: 1,
                startTime: Date().addingTimeInterval(-7200), // 2 hours ago
                endTime: Date().addingTimeInterval(-5400), // 1.5 hours ago
                timeSpent: 1800, // 30 minutes
                score: 0, // Not graded yet
                maxScore: 100,
                percentage: 0,
                isPassed: false,
                state: .completed,
                needsGrading: true,
                feedback: nil,
                submittedAt: Date().addingTimeInterval(-5400),
                gradedAt: nil,
                gradedBy: nil
            )
        ]
    }
    
    func getPendingGrading(page: Int, limit: Int, quizId: Int?) async throws -> PendingGradingResponse {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        
        var filteredAttempts = pendingAttempts
        
        // Filter by quiz if specified
        if let quizId = quizId {
            filteredAttempts = filteredAttempts.filter { $0.quizId == quizId }
        }
        
        // Apply pagination
        let startIndex = (page - 1) * limit
        let endIndex = min(startIndex + limit, filteredAttempts.count)
        let paginatedAttempts = Array(filteredAttempts[startIndex..<endIndex])
        
        let totalPages = Int(ceil(Double(filteredAttempts.count) / Double(limit)))
        
        return PendingGradingResponse(
            success: true,
            message: "Pending grading retrieved successfully",
            data: PendingGradingData(
                attempts: paginatedAttempts,
                totalCount: filteredAttempts.count,
                totalPages: totalPages,
                currentPage: page,
                hasNextPage: page < totalPages,
                hasPreviousPage: page > 1
            ),
            traceId: UUID().uuidString,
            requestId: UUID().uuidString
        )
    }
    
    func getAttemptDetail(attemptId: Int) async throws -> AttemptDetailResponse {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        
        guard let attempt = pendingAttempts.first(where: { $0.id == attemptId }) else {
            throw APIError(code: "NOT_FOUND", message: "Attempt not found", field: nil, details: nil)
        }
        
        // Mock quiz data
        let quiz = Quiz(
            id: attempt.quizId,
            name: attempt.quizName,
            code: attempt.quizCode,
            description: "Mock quiz description",
            quizType: .quiz,
            subjectId: 1,
            subjectName: "Toán học",
            classId: 1,
            className: "Lớp 10A1",
            maxScore: attempt.maxScore,
            passingScore: 70,
            timeLimit: 60,
            maxAttempts: 2,
            isRandomized: true,
            showCorrectAnswers: false,
            state: .published,
            questionCount: 3,
            studentCount: 30,
            attemptCount: 25,
            averageScore: 78.5,
            passRate: 80.0,
            pendingGradingCount: 3,
            startDate: Date().addingTimeInterval(-86400),
            endDate: Date().addingTimeInterval(86400),
            createdAt: Date().addingTimeInterval(-172800),
            updatedAt: Date().addingTimeInterval(-86400)
        )
        
        // Mock student answers
        let answers = [
            StudentAnswer(
                id: 1,
                questionId: 1,
                questionText: "Giải phương trình: 2x + 5 = 13",
                questionType: .essay,
                questionScore: 30,
                answerText: "2x = 13 - 5\n2x = 8\nx = 4",
                selectedAnswerIds: [],
                score: nil,
                maxScore: 30,
                isCorrect: nil,
                needsGrading: true,
                instructorFeedback: nil,
                submittedAt: attempt.submittedAt ?? Date(),
                gradedAt: nil
            ),
            StudentAnswer(
                id: 2,
                questionId: 2,
                questionText: "Tính đạo hàm của hàm số f(x) = x² + 3x - 2",
                questionType: .essay,
                questionScore: 35,
                answerText: "f'(x) = 2x + 3",
                selectedAnswerIds: [],
                score: nil,
                maxScore: 35,
                isCorrect: nil,
                needsGrading: true,
                instructorFeedback: nil,
                submittedAt: attempt.submittedAt ?? Date(),
                gradedAt: nil
            ),
            StudentAnswer(
                id: 3,
                questionId: 3,
                questionText: "Căn bậc hai của 16 là:",
                questionType: .singleChoice,
                questionScore: 35,
                answerText: "4",
                selectedAnswerIds: [2],
                score: nil,
                maxScore: 35,
                isCorrect: nil,
                needsGrading: true,
                instructorFeedback: nil,
                submittedAt: attempt.submittedAt ?? Date(),
                gradedAt: nil
            )
        ]
        
        let attemptDetail = AttemptDetail(
            id: attemptId,
            attempt: attempt,
            quiz: quiz,
            answers: answers
        )
        
        return AttemptDetailResponse(
            success: true,
            message: "Attempt detail retrieved successfully",
            data: attemptDetail,
            traceId: UUID().uuidString,
            requestId: UUID().uuidString
        )
    }
    
    func gradeAttempt(attemptId: Int, request: GradeAttemptRequest) async throws -> GradeAttemptResponse {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        
        guard let index = pendingAttempts.firstIndex(where: { $0.id == attemptId }) else {
            throw APIError(code: "NOT_FOUND", message: "Attempt not found", field: nil, details: nil)
        }
        
        // Calculate total score
        let totalScore = request.answers.reduce(0) { $0 + $1.score }
        let maxScore = pendingAttempts[index].maxScore
        let percentage = (totalScore / maxScore) * 100
        let isPassed = percentage >= 70 // Assuming 70% is passing
        
        // Update the attempt
        var attempt = pendingAttempts[index]
        attempt = QuizAttempt(
            id: attempt.id,
            studentId: attempt.studentId,
            studentName: attempt.studentName,
            studentCode: attempt.studentCode,
            studentEmail: attempt.studentEmail,
            quizId: attempt.quizId,
            quizName: attempt.quizName,
            quizCode: attempt.quizCode,
            attemptNumber: attempt.attemptNumber,
            startTime: attempt.startTime,
            endTime: attempt.endTime,
            timeSpent: attempt.timeSpent,
            score: totalScore,
            maxScore: maxScore,
            percentage: percentage,
            isPassed: isPassed,
            state: .graded,
            needsGrading: false,
            feedback: request.feedback,
            submittedAt: attempt.submittedAt,
            gradedAt: Date(),
            gradedBy: "Instructor"
        )
        
        // Remove from pending list
        pendingAttempts.remove(at: index)
        
        return GradeAttemptResponse(
            success: true,
            message: "Attempt graded successfully",
            data: GradeAttemptData(
                attemptId: attemptId,
                totalScore: totalScore,
                maxScore: maxScore,
                percentage: percentage,
                isPassed: isPassed,
                gradedAt: Date(),
                gradedBy: "Instructor"
            ),
            traceId: UUID().uuidString,
            requestId: UUID().uuidString
        )
    }
    
    func getQuizAttempts(quizId: Int, page: Int, limit: Int, filters: AttemptFilters?) async throws -> QuizAttemptsResponse {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        
        // Mock data - in real implementation, this would fetch from API
        let mockAttempts: [QuizAttempt] = []
        
        let totalPages = Int(ceil(Double(mockAttempts.count) / Double(limit)))
        
        return QuizAttemptsResponse(
            success: true,
            message: "Quiz attempts retrieved successfully",
            data: QuizAttemptsData(
                attempts: mockAttempts,
                totalCount: mockAttempts.count,
                totalPages: totalPages,
                currentPage: page,
                hasNextPage: page < totalPages,
                hasPreviousPage: page > 1
            ),
            traceId: UUID().uuidString,
            requestId: UUID().uuidString
        )
    }
}
