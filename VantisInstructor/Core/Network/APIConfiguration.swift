import Foundation

/// API Configuration for the mobile app
class APIConfiguration {
    static let shared = APIConfiguration()

    // MARK: - API Systems
    enum APISystem {
        case lms
        case earnbase
    }

    private var currentSystem: APISystem = .lms

    private init() {}

    // MARK: - Configuration Properties

    /// Base URL for the API based on current system
    var baseURL: String {
        switch currentSystem {
        case .lms:
            return "https://lms-dev.ebill.vn/api/v1/"
        case .earnbase:
            return "https://lms-dev.ebill.vn/api/v1/"
        }
    }

    // MARK: - System Configuration

    /// Setup for LMS system (default for instructor app)
    func setupLMS() {
        currentSystem = .lms
        print("🔧 API Configuration: Switched to LMS system")
        print("🔧 Base URL: \(baseURL)")
    }

    /// Setup for EarnBase system (if needed)
    func setupEarnBase() {
        currentSystem = .earnbase
        print("🔧 API Configuration: Switched to EarnBase system")
        print("🔧 Base URL: \(baseURL)")
    }
    
    /// Request timeout in seconds
    var timeout: TimeInterval {
        return 30.0
    }
    
    /// Number of retry attempts for failed requests
    var retryAttempts: Int {
        return 3
    }
    
    /// Delay between retry attempts in seconds
    var retryDelay: TimeInterval {
        return 1.0
    }
    
    /// Default headers for all requests
    var defaultHeaders: [String: String] {
        return [
            "Content-Type": "application/json",
            "Accept": "application/json",
            "User-Agent": "MobileApp/1.0"
        ]
    }
    
    // MARK: - Helper Methods
    
    /// Build full URL from endpoint
    func buildURL(endpoint: String) -> URL? {
        // Remove trailing slash from baseURL and leading slash from endpoint to avoid double slashes
        let cleanBaseURL = baseURL.hasSuffix("/") ? String(baseURL.dropLast()) : baseURL
        let cleanEndpoint = endpoint.hasPrefix("/") ? endpoint : "/\(endpoint)"
        return URL(string: cleanBaseURL + cleanEndpoint)
    }
}
