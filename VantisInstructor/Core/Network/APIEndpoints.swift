//
//  APIEndpoints.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 22/7/25.
//

import Foundation

// MARK: - API Endpoints (Based on OpenAPI Specification)
struct APIEndpoints {

    // MARK: - Core System Endpoints
    struct Core {
        static let apiInfo = "/"
        static let health = "/health"
        static let modules = "/modules"
    }

    // MARK: - Authentication Endpoints
    struct Auth {
        static let signIn = "/auth/sign-in"
        static let register = "/auth/register"
        static let me = "/auth/me"
        static let refresh = "/auth/refresh"
        static let verify = "/auth/verify"
        static let signOut = "/auth/sign-out"
        static let tokens = "/auth/tokens"
        static let revoke = "/auth/revoke"
        static let deviceRegister = "/auth/device/register"
        static let forgotPassword = "/auth/forgot-password"
        static let resetPassword = "/auth/reset-password"
        static let verifyEmail = "/auth/verify-email"
        static let changePassword = "/auth/change-password"
    }

    // MARK: - File Management Endpoints
    struct Files {
        static let upload = "/files/"
        static func getByToken(_ token: String) -> String {
            return "/files/\(token)"
        }
    }

    // MARK: - Public Chatbot Endpoints
    struct PublicChatbot {
        static let sessions = "/public/chatbot/sessions"
        static func session(_ sessionId: String) -> String {
            return "/public/chatbot/sessions/\(sessionId)"
        }
        static func sessionMessages(_ sessionId: String) -> String {
            return "/public/chatbot/sessions/\(sessionId)/messages"
        }
        static func messageFeedback(_ messageId: String) -> String {
            return "/public/chatbot/messages/\(messageId)/feedback"
        }
        static let config = "/public/chatbot/config"
    }
    

    
    // MARK: - Public Classes Endpoints
    struct PublicClasses {
        static let list = "/public/classes"
        static func detail(_ classId: Int) -> String {
            return "/public/classes/\(classId)"
        }
    }

    // MARK: - Public Locations Endpoints
    struct PublicLocations {
        static let list = "/public/locations"
        static func detail(_ locationId: Int) -> String {
            return "/public/public/locations/\(locationId)"
        }
    }

    // MARK: - Public Rooms Endpoints
    struct PublicRooms {
        static let list = "/public/rooms"
    }

    // MARK: - Public Enrollments Endpoints
    struct PublicEnrollments {
        static func enrollmentsByEmail() -> String {
            return "/public/enrollments"
        }
    }

    // MARK: - Public Course Enrollments Endpoints
    struct PublicCourseEnrollments {
        static func create(_ courseId: Int) -> String {
            return "/public/courses/\(courseId)/enrollments"
        }
    }

    // MARK: - Public Class Enrollments Endpoints
    struct PublicClassEnrollments {
        static func create(_ classId: Int) -> String {
            return "/public/classes/\(classId)/enrollments"
        }
    }

    // MARK: - Users Endpoints
    struct Users {
        static let profile = "/users/profile"
        static let updateProfile = "/users/profile"
        static let balance = "/users/balance"
        static let transactions = "/users/transactions"
    }



    // MARK: - System Endpoints
    struct System {
        static let config = "/system/config"
        static let maintenance = "/system/maintenance"
        static let version = "/system/version"
        static let health = "/system/health"
    }

    // MARK: - Analytics Endpoints
    struct Analytics {
        static let events = "/analytics/events"
        static let track = "/analytics/track"
        static let session = "/analytics/session"
    }

    // MARK: - Helper Methods for Dynamic Endpoint Building
    struct EndpointBuilder {
        static func buildURL(endpoint: String, pathParameters: [String: String] = [:]) -> String {
            // For now, just build the endpoint with path parameters manually
            var finalEndpoint = endpoint
            for (key, value) in pathParameters {
                finalEndpoint = finalEndpoint.replacingOccurrences(of: "{\(key)}", with: value)
            }
            return APIConfiguration.shared.buildURL(endpoint: finalEndpoint)?.absoluteString ?? ""
        }

        static func buildURL(endpoint: String) -> String {
            return APIConfiguration.shared.buildURL(endpoint: endpoint)?.absoluteString ?? ""
        }
    }
}

// MARK: - API Request Builder
struct APIRequest {
    let endpoint: String
    let method: HTTPMethod
    let parameters: [String: Any]?
    let headers: [String: String]?
    let requiresAuth: Bool
    
    init(
        endpoint: String,
        method: HTTPMethod = .GET,
        parameters: [String: Any]? = nil,
        headers: [String: String]? = nil,
        requiresAuth: Bool = true
    ) {
        self.endpoint = endpoint
        self.method = method
        self.parameters = parameters
        self.headers = headers
        self.requiresAuth = requiresAuth
    }
}



// MARK: - API Error
struct APIError: Codable, Error {
    let code: String
    let message: String
    let field: String?
    let details: [String: String]?

    enum CodingKeys: String, CodingKey {
        case code, message, field, details
    }
}

// MARK: - Pagination Request
struct PaginationRequest: Codable {
    let page: Int
    let limit: Int
    let sortBy: String?
    let sortOrder: SortOrder?
    
    enum SortOrder: String, Codable {
        case asc = "asc"
        case desc = "desc"
    }
    
    init(
        page: Int = 1,
        limit: Int = AppConstants.Pagination.defaultLimit,
        sortBy: String? = nil,
        sortOrder: SortOrder? = nil
    ) {
        self.page = page
        self.limit = limit
        self.sortBy = sortBy
        self.sortOrder = sortOrder
    }
    
    var queryParameters: [String: Any] {
        var params: [String: Any] = [
            "page": page,
            "limit": limit
        ]
        
        if let sortBy = sortBy {
            params["sortBy"] = sortBy
        }
        
        if let sortOrder = sortOrder {
            params["sortOrder"] = sortOrder.rawValue
        }
        

        
        return params
    }
}

// MARK: - Search Request
struct SearchRequest: Codable {
    let query: String
    let category: String?
    let pagination: PaginationRequest
    
    init(
        query: String,
        category: String? = nil,
        pagination: PaginationRequest = PaginationRequest()
    ) {
        self.query = query
        self.category = category
        self.pagination = pagination
    }
    
    var queryParameters: [String: Any] {
        var params = pagination.queryParameters
        params["q"] = query

        if let category = category {
            params["category"] = category
        }

        return params
    }
}
