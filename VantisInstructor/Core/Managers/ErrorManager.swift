//
//  ErrorManager.swift
//  VantisInstructor
//
//  Created by AI Assistant on 30/7/25.
//

import SwiftUI
import Combine

// MARK: - Error Manager
class ErrorManager: ObservableObject {
    static let shared = ErrorManager()
    
    @Published var currentErrors: [ErrorInfo] = []
    @Published var showErrorModal = false
    
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        // Auto-dismiss errors after a certain time if not critical
        $currentErrors
            .sink { [weak self] errors in
                self?.scheduleAutoDismiss(for: errors)
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Public Methods
    
    /// Show a single error
    func showError(_ error: ErrorInfo) {
        DispatchQueue.main.async {
            self.currentErrors = [error]
            self.showErrorModal = true
        }
    }
    
    /// Show multiple errors
    func showErrors(_ errors: [ErrorInfo]) {
        DispatchQueue.main.async {
            self.currentErrors = errors
            self.showErrorModal = true
        }
    }
    
    /// Add error to current list
    func addError(_ error: ErrorInfo) {
        DispatchQueue.main.async {
            self.currentErrors.append(error)
            if !self.showErrorModal {
                self.showErrorModal = true
            }
        }
    }
    
    /// Clear all errors
    func clearErrors() {
        DispatchQueue.main.async {
            self.currentErrors.removeAll()
            self.showErrorModal = false
        }
    }
    
    /// Remove specific error
    func removeError(_ error: ErrorInfo) {
        DispatchQueue.main.async {
            self.currentErrors.removeAll { $0.id == error.id }
            if self.currentErrors.isEmpty {
                self.showErrorModal = false
            }
        }
    }
    
    // MARK: - Convenience Methods
    
    /// Show network error
    func showNetworkError(message: String = "Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối internet của bạn.", details: String? = nil) {
        let error = ErrorInfo(
            type: .network,
            message: message,
            details: details
        )
        showError(error)
    }
    
    /// Show validation error
    func showValidationError(message: String, details: String? = nil) {
        let error = ErrorInfo(
            type: .validation,
            message: message,
            details: details
        )
        showError(error)
    }
    
    /// Show authentication error
    func showAuthenticationError(message: String = "Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.", details: String? = nil) {
        let error = ErrorInfo(
            type: .authentication,
            message: message,
            details: details
        )
        showError(error)
    }
    
    /// Show server error
    func showServerError(message: String = "Máy chủ đang gặp sự cố. Vui lòng thử lại sau.", details: String? = nil) {
        let error = ErrorInfo(
            type: .server,
            message: message,
            details: details
        )
        showError(error)
    }
    
    /// Show unknown error
    func showUnknownError(message: String = "Đã xảy ra lỗi không xác định. Vui lòng thử lại.", details: String? = nil) {
        let error = ErrorInfo(
            type: .unknown,
            message: message,
            details: details
        )
        showError(error)
    }
    
    // MARK: - Error Parsing
    
    /// Parse and show error from various sources
    func handleError(_ error: Error, context: String? = nil) {
        let errorInfo = parseError(error, context: context)
        showError(errorInfo)
    }
    
    private func parseError(_ error: Error, context: String?) -> ErrorInfo {
        // Check for specific error types
        if let urlError = error as? URLError {
            return parseURLError(urlError, context: context)
        }
        
        if let decodingError = error as? DecodingError {
            return parseDecodingError(decodingError, context: context)
        }
        
        // Check for custom app errors
        if let appError = error as? CustomAppError {
            return parseAppError(appError, context: context)
        }
        
        // Default unknown error
        return ErrorInfo(
            type: .unknown,
            message: error.localizedDescription,
            details: "\(error)"
        )
    }
    
    private func parseURLError(_ error: URLError, context: String?) -> ErrorInfo {
        let message: String
        let type: ErrorType = .network
        
        switch error.code {
        case .notConnectedToInternet:
            message = "Không có kết nối internet. Vui lòng kiểm tra kết nối của bạn."
        case .timedOut:
            message = "Kết nối bị timeout. Vui lòng thử lại."
        case .cannotFindHost, .cannotConnectToHost:
            message = "Không thể kết nối đến máy chủ. Vui lòng thử lại sau."
        case .networkConnectionLost:
            message = "Mất kết nối mạng. Vui lòng kiểm tra kết nối của bạn."
        default:
            message = "Lỗi kết nối mạng: \(error.localizedDescription)"
        }
        
        return ErrorInfo(
            type: type,
            message: message,
            details: "URLError: \(error.code.rawValue) - \(error.localizedDescription)\nContext: \(context ?? "N/A")"
        )
    }
    
    private func parseDecodingError(_ error: DecodingError, context: String?) -> ErrorInfo {
        let message = "Dữ liệu từ máy chủ không hợp lệ. Vui lòng thử lại sau."
        let details: String
        
        switch error {
        case .dataCorrupted(let context):
            details = "Data corrupted: \(context.debugDescription)"
        case .keyNotFound(let key, let context):
            details = "Key '\(key.stringValue)' not found: \(context.debugDescription)"
        case .typeMismatch(let type, let context):
            details = "Type mismatch for \(type): \(context.debugDescription)"
        case .valueNotFound(let type, let context):
            details = "Value not found for \(type): \(context.debugDescription)"
        @unknown default:
            details = "Unknown decoding error: \(error.localizedDescription)"
        }
        
        return ErrorInfo(
            type: .server,
            message: message,
            details: "DecodingError: \(details)\nContext: \(context ?? "N/A")"
        )
    }
    
    private func parseAppError(_ error: CustomAppError, context: String?) -> ErrorInfo {
        let type: ErrorType
        let message: String
        
        switch error {
        case .networkError(let msg):
            type = .network
            message = msg
        case .validationError(let msg):
            type = .validation
            message = msg
        case .authenticationError(let msg):
            type = .authentication
            message = msg
        case .serverError(let msg):
            type = .server
            message = msg
        case .unknownError(let msg):
            type = .unknown
            message = msg
        }
        
        return ErrorInfo(
            type: type,
            message: message,
            details: "AppError: \(error)\nContext: \(context ?? "N/A")"
        )
    }
    
    // MARK: - Auto Dismiss
    
    private func scheduleAutoDismiss(for errors: [ErrorInfo]) {
        // Only auto-dismiss non-critical errors
        let nonCriticalTypes: [ErrorType] = [.validation, .unknown]
        let hasOnlyNonCriticalErrors = errors.allSatisfy { nonCriticalTypes.contains($0.type) }
        
        if hasOnlyNonCriticalErrors && !errors.isEmpty {
            DispatchQueue.main.asyncAfter(deadline: .now() + 10) { [weak self] in
                // Only dismiss if the errors haven't changed
                if self?.currentErrors.map(\.id) == errors.map(\.id) {
                    self?.clearErrors()
                }
            }
        }
    }
}

// MARK: - Custom App Errors
enum CustomAppError: Error, LocalizedError {
    case networkError(String)
    case validationError(String)
    case authenticationError(String)
    case serverError(String)
    case unknownError(String)
    
    var errorDescription: String? {
        switch self {
        case .networkError(let message),
             .validationError(let message),
             .authenticationError(let message),
             .serverError(let message),
             .unknownError(let message):
            return message
        }
    }
}

// MARK: - View Extension for Error Handling
extension View {
    func errorModal() -> some View {
        self.modifier(ErrorModalModifier())
    }
}

struct ErrorModalModifier: ViewModifier {
    @StateObject private var errorManager = ErrorManager.shared
    
    func body(content: Content) -> some View {
        content
            .overlay(
                Group {
                    if errorManager.showErrorModal {
                        ErrorModalView(
                            isPresented: $errorManager.showErrorModal,
                            errors: errorManager.currentErrors,
                            onRetry: {
                                // Default retry action - can be customized
                                print("🔄 Retry action triggered")
                            },
                            onDismiss: {
                                errorManager.clearErrors()
                            }
                        )
                        .zIndex(999)
                    }
                }
            )
    }
}
