//
//  LazyLoadableViewModel.swift
//  VantisInstructor
//
//  Created by AI Assistant on 29/7/25.
//

import Foundation
import SwiftUI

/// Protocol for ViewModels that support lazy loading
/// Ensures ViewModels only load data when authentication is confirmed
protocol LazyLoadableViewModel: ObservableObject {
    /// Indicates if the ViewModel has been initialized with data
    var hasInitialized: Bool { get set }
    
    /// Indicates if the ViewModel is currently loading initial data
    var isInitializing: Bool { get set }
    
    /// Initialize the ViewModel with data
    /// This should only be called after authentication is confirmed
    func initializeData() async
    
    /// Refresh data (can be called anytime after initialization)
    func refreshData() async
}

/// Default implementation for LazyLoadableViewModel
extension LazyLoadableViewModel {
    
    /// Safe initialization that checks authentication state first
    func safeInitialize() async {
        guard !hasInitialized else {
            print("📱 \(type(of: self)): Already initialized, skipping")
            return
        }
        
        guard await AuthenticationStateManager.shared.isReadyForAPICall() else {
            print("📱 \(type(of: self)): Not ready for API call, waiting...")
            await AuthenticationStateManager.shared.waitForAuthenticationCompletion()

            // Double check after waiting
            guard await AuthenticationStateManager.shared.isReadyForAPICall() else {
                print("📱 \(type(of: self)): Still not ready after waiting, aborting initialization")
                return
            }
            return
        }
        
        await MainActor.run {
            isInitializing = true
        }
        
        print("📱 \(type(of: self)): Starting safe initialization")
        await initializeData()
        
        await MainActor.run {
            hasInitialized = true
            isInitializing = false
        }
        
        print("📱 \(type(of: self)): Safe initialization completed")
    }
}

/// Convenience modifier for Views that use LazyLoadableViewModel
struct LazyLoadModifier<ViewModel: LazyLoadableViewModel>: ViewModifier {
    @ObservedObject var viewModel: ViewModel
    @EnvironmentObject var authStateManager: AuthenticationStateManager
    
    func body(content: Content) -> some View {
        content
            .task {
                // Only initialize when view appears and auth is ready
                if authStateManager.authenticationState.isAuthenticated {
                    await viewModel.safeInitialize()
                }
            }
            .onChange(of: authStateManager.authenticationState) { _, newState in
                // Initialize when authentication becomes available
                if newState.isAuthenticated && !viewModel.hasInitialized {
                    Task {
                        await viewModel.safeInitialize()
                    }
                }
            }
    }
}

extension View {
    /// Apply lazy loading behavior to a view with LazyLoadableViewModel
    func lazyLoad<ViewModel: LazyLoadableViewModel>(viewModel: ViewModel) -> some View {
        self.modifier(LazyLoadModifier(viewModel: viewModel))
    }
}
