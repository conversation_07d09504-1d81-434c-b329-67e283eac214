//
//  FontExtensions.swift
//  mobile-app-template
//
//  Created by AI Assistant on 22/07/2025.
//

import SwiftUI

// MARK: - Be Vietnam Pro Font Extension
extension Font {
    
    // MARK: - Be Vietnam Pro Font Family
    enum BeVietnamPro {
        case regular
        case medium
        case semiBold
        case bold
        
        var fontName: String {
            switch self {
            case .regular:
                return "BeVietnamPro-Regular"
            case .medium:
                return "BeVietnamPro-Medium"
            case .semiBold:
                return "BeVietnamPro-SemiBold"
            case .bold:
                return "BeVietnamPro-Bold"
            }
        }
    }
    
    // MARK: - Custom Font Methods
    static func beVietnamPro(_ weight: BeVietnamPro, size: CGFloat) -> Font {
        return Font.custom(weight.fontName, size: size)
    }
    
    // MARK: - Predefined Sizes with Be Vietnam Pro
    static var beVietnamProLargeTitle: Font {
        return .beVietnamPro(.bold, size: 34)
    }
    
    static var beVietnamProTitle: Font {
        return .beVietnamPro(.semiBold, size: 28)
    }
    
    static var beVietnamProTitle2: Font {
        return .beVietnamPro(.semiBold, size: 22)
    }
    
    static var beVietnamProTitle3: Font {
        return .beVietnamPro(.medium, size: 20)
    }
    
    static var beVietnamProHeadline: Font {
        return .beVietnamPro(.semiBold, size: 17)
    }
    
    static var beVietnamProSubheadline: Font {
        return .beVietnamPro(.medium, size: 15)
    }
    
    static var beVietnamProBody: Font {
        return .beVietnamPro(.regular, size: 17)
    }
    
    static var beVietnamProCallout: Font {
        return .beVietnamPro(.regular, size: 16)
    }
    
    static var beVietnamProFootnote: Font {
        return .beVietnamPro(.regular, size: 13)
    }
    
    static var beVietnamProCaption: Font {
        return .beVietnamPro(.regular, size: 12)
    }
    
    static var beVietnamProCaption2: Font {
        return .beVietnamPro(.regular, size: 11)
    }
}

// MARK: - Font Registration Helper
class FontRegistration {
    static func registerFonts() {
        let fontNames = [
            "BeVietnamPro-Regular",
            "BeVietnamPro-Medium", 
            "BeVietnamPro-SemiBold",
            "BeVietnamPro-Bold"
        ]
        
        for fontName in fontNames {
            guard let fontURL = Bundle.main.url(forResource: fontName, withExtension: "ttf") else {
                print("❌ Could not find font file: \(fontName).ttf")
                continue
            }
            
            guard let fontData = NSData(contentsOf: fontURL) else {
                print("❌ Could not load font data for: \(fontName)")
                continue
            }
            
            guard let dataProvider = CGDataProvider(data: fontData) else {
                print("❌ Could not create data provider for: \(fontName)")
                continue
            }
            
            guard let font = CGFont(dataProvider) else {
                print("❌ Could not create font from data provider for: \(fontName)")
                continue
            }
            
            var error: Unmanaged<CFError>?
            if !CTFontManagerRegisterGraphicsFont(font, &error) {
                print("❌ Failed to register font: \(fontName)")
                if let error = error {
                    print("Error: \(error.takeUnretainedValue())")
                }
            } else {
                print("✅ Successfully registered font: \(fontName)")
            }
        }
    }
}

// MARK: - Font Debugging Helper
extension Font {
    static func listAvailableFonts() {
        for family in UIFont.familyNames.sorted() {
            print("Font Family: \(family)")
            for name in UIFont.fontNames(forFamilyName: family) {
                print("  - \(name)")
            }
        }
    }
    
    static func checkBeVietnamProAvailability() {
        let fontNames = [
            "BeVietnamPro-Regular",
            "BeVietnamPro-Medium",
            "BeVietnamPro-SemiBold", 
            "BeVietnamPro-Bold"
        ]
        
        print("🔍 Checking Be Vietnam Pro font availability:")
        for fontName in fontNames {
            if UIFont(name: fontName, size: 12) != nil {
                print("✅ \(fontName) is available")
            } else {
                print("❌ \(fontName) is NOT available")
            }
        }
    }
}
