//
//  APILogger.swift
//  mobile-app-template
//
//  Created by AI Assistant on 28/7/25.
//

import Foundation
import UIKit

/// File-based logger for API requests and responses
class APILogger {
    static let shared = APILogger()
    
    private let fileManager = FileManager.default
    private let logFileName = "quiz_api_debug.log"
    
    private var logFileURL: URL {
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        return documentsPath.appendingPathComponent(logFileName)
    }
    
    private init() {
        setupLogFile()
    }
    
    private func setupLogFile() {
        // Create log file if it doesn't exist
        if !fileManager.fileExists(atPath: logFileURL.path) {
            let initialContent = """
# 🔍 QUIZ MANAGEMENT API DEBUG LOG
# Generated: \(ISO8601DateFormatter().string(from: Date()))
# Purpose: Capture full request/response details for Quiz Management screen

"""
            try? initialContent.write(to: logFileURL, atomically: true, encoding: .utf8)
        }
    }
    
    // MARK: - Public Logging Methods
    
    /// Log API request details
    func logRequest(
        url: URL,
        method: String,
        headers: [String: String]?,
        body: Data?,
        timestamp: Date = Date()
    ) {
        let logEntry = """
        
        ## 📡 API REQUEST
        **Timestamp**: \(ISO8601DateFormatter().string(from: timestamp))
        **URL**: \(url.absoluteString)
        **Method**: \(method)
        
        ### Headers:
        ```
        \(formatHeaders(headers))
        ```
        
        ### Request Body:
        ```json
        \(formatBody(body))
        ```
        
        ---
        
        """
        
        appendToFile(logEntry)
        
        // Also log to console for immediate debugging
        print("📡 [APILogger] Request logged to file: \(logFileURL.path)")
    }
    
    /// Log API response details
    func logResponse(
        url: URL,
        statusCode: Int,
        headers: [AnyHashable: Any]?,
        body: Data?,
        error: Error?,
        duration: TimeInterval,
        timestamp: Date = Date()
    ) {
        let logEntry = """
        
        ## 📥 API RESPONSE
        **Timestamp**: \(ISO8601DateFormatter().string(from: timestamp))
        **URL**: \(url.absoluteString)
        **Status Code**: \(statusCode)
        **Duration**: \(String(format: "%.2f", duration * 1000))ms
        
        ### Response Headers:
        ```
        \(formatResponseHeaders(headers))
        ```
        
        ### Response Body:
        ```json
        \(formatBody(body))
        ```
        
        ### Error (if any):
        ```
        \(error?.localizedDescription ?? "No error")
        ```
        
        ---
        
        """
        
        appendToFile(logEntry)
        
        // Also log to console for immediate debugging
        print("📥 [APILogger] Response logged to file: \(logFileURL.path)")
    }
    
    /// Log authentication details
    func logAuthDetails(
        isLoggedIn: Bool,
        tokenExists: Bool,
        tokenPreview: String?,
        userInfo: String?,
        timestamp: Date = Date()
    ) {
        let logEntry = """
        
        ## 🔐 AUTHENTICATION STATUS
        **Timestamp**: \(ISO8601DateFormatter().string(from: timestamp))
        **Is Logged In**: \(isLoggedIn ? "YES" : "NO")
        **Token Exists**: \(tokenExists ? "YES" : "NO")
        **Token Preview**: \(tokenPreview ?? "N/A")
        **User Info**: \(userInfo ?? "N/A")
        
        ---
        
        """
        
        appendToFile(logEntry)
        print("🔐 [APILogger] Auth details logged to file: \(logFileURL.path)")
    }
    
    /// Log error details
    func logError(
        error: Error,
        context: String,
        timestamp: Date = Date()
    ) {
        let logEntry = """
        
        ## 🚨 ERROR DETAILS
        **Timestamp**: \(ISO8601DateFormatter().string(from: timestamp))
        **Context**: \(context)
        **Error Type**: \(type(of: error))
        **Error Description**: \(error.localizedDescription)
        **Full Error**: \(String(describing: error))
        
        ---
        
        """
        
        appendToFile(logEntry)
        print("🚨 [APILogger] Error logged to file: \(logFileURL.path)")
    }
    
    /// Log debug session start
    func logDebugSessionStart() {
        let logEntry = """
        
        # 🔍 NEW DEBUG SESSION STARTED
        **Timestamp**: \(ISO8601DateFormatter().string(from: Date()))
        **Device**: \(UIDevice.current.model)
        **iOS Version**: \(UIDevice.current.systemVersion)
        **App Version**: \(Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "Unknown")
        
        """
        
        appendToFile(logEntry)
        print("🔍 [APILogger] Debug session started - Log file: \(logFileURL.path)")
    }
    
    // MARK: - File Operations
    
    private func appendToFile(_ content: String) {
        guard let data = content.data(using: .utf8) else { return }
        
        if fileManager.fileExists(atPath: logFileURL.path) {
            if let fileHandle = try? FileHandle(forWritingTo: logFileURL) {
                fileHandle.seekToEndOfFile()
                fileHandle.write(data)
                fileHandle.closeFile()
            }
        } else {
            try? data.write(to: logFileURL)
        }
    }
    
    // MARK: - Formatting Helpers
    
    private func formatHeaders(_ headers: [String: String]?) -> String {
        guard let headers = headers else { return "No headers" }
        
        return headers.map { key, value in
            // Mask sensitive headers
            if key.lowercased() == "authorization" {
                let maskedValue = value.count > 20 ? "\(String(value.prefix(20)))..." : value
                return "\(key): \(maskedValue)"
            } else {
                return "\(key): \(value)"
            }
        }.joined(separator: "\n")
    }
    
    private func formatResponseHeaders(_ headers: [AnyHashable: Any]?) -> String {
        guard let headers = headers else { return "No headers" }
        
        return headers.map { key, value in
            "\(key): \(value)"
        }.joined(separator: "\n")
    }
    
    private func formatBody(_ body: Data?) -> String {
        guard let body = body else { return "No body" }
        
        if let jsonString = String(data: body, encoding: .utf8) {
            // Try to pretty print JSON
            if let jsonData = jsonString.data(using: .utf8),
               let jsonObject = try? JSONSerialization.jsonObject(with: jsonData),
               let prettyData = try? JSONSerialization.data(withJSONObject: jsonObject, options: .prettyPrinted),
               let prettyString = String(data: prettyData, encoding: .utf8) {
                return prettyString
            } else {
                return jsonString
            }
        } else {
            return "Binary data (\(body.count) bytes)"
        }
    }
    
    // MARK: - Public Utilities
    
    /// Get the current log file path
    func getLogFilePath() -> String {
        return logFileURL.path
    }
    
    /// Clear the log file
    func clearLog() {
        try? fileManager.removeItem(at: logFileURL)
        setupLogFile()
        print("🗑️ [APILogger] Log file cleared")
    }
    
    /// Get log file size
    func getLogFileSize() -> String {
        guard let attributes = try? fileManager.attributesOfItem(atPath: logFileURL.path),
              let fileSize = attributes[.size] as? Int64 else {
            return "Unknown"
        }
        
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: fileSize)
    }
    
    /// Check if log file exists
    func logFileExists() -> Bool {
        return fileManager.fileExists(atPath: logFileURL.path)
    }
}
