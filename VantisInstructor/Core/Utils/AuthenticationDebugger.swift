//
//  AuthenticationDebugger.swift
//  VantisInstructor
//
//  Created by AI Assistant on 29/7/25.
//

import Foundation
import SwiftUI

/// Debug utility for monitoring authentication flow during testing
class AuthenticationDebugger {
    static let shared = AuthenticationDebugger()
    
    private var events: [AuthEvent] = []
    private let maxEvents = 100
    
    struct AuthEvent {
        let timestamp: Date
        let component: String
        let event: String
        let details: String?
        
        var formattedTime: String {
            let formatter = DateFormatter()
            formatter.dateFormat = "HH:mm:ss.SSS"
            return formatter.string(from: timestamp)
        }
    }
    
    private init() {}
    
    // MARK: - Event Logging
    
    func log(component: String, event: String, details: String? = nil) {
        let authEvent = AuthEvent(
            timestamp: Date(),
            component: component,
            event: event,
            details: details
        )
        
        events.append(authEvent)
        
        // Keep only recent events
        if events.count > maxEvents {
            events.removeFirst()
        }
        
        // Print to console with emoji for easy identification
        let emoji = getEmojiForComponent(component)
        let detailsStr = details != nil ? " - \(details!)" : ""
        print("\(emoji) [\(authEvent.formattedTime)] \(component): \(event)\(detailsStr)")
    }
    
    private func getEmojiForComponent(_ component: String) -> String {
        switch component {
        case "AuthenticationStateManager":
            return "🔐"
        case "QuizManagementViewModel":
            return "📱"
        case "AuthViewModel":
            return "👤"
        case "UnifiedAuthManager":
            return "🔑"
        case "TokenManager":
            return "🎫"
        case "ContentView":
            return "📺"
        case "LazyLoadableViewModel":
            return "⚡"
        default:
            return "🔍"
        }
    }
    
    // MARK: - State Monitoring
    
    func logAuthStateChange(from oldState: String, to newState: String) {
        log(
            component: "AuthenticationStateManager",
            event: "State Change",
            details: "\(oldState) → \(newState)"
        )
    }
    
    func logViewModelInitialization(viewModel: String, hasAuth: Bool, hasToken: Bool) {
        log(
            component: viewModel,
            event: "Initialization Attempt",
            details: "Auth: \(hasAuth), Token: \(hasToken)"
        )
    }
    
    func logAPICall(viewModel: String, endpoint: String, success: Bool, timing: TimeInterval? = nil) {
        let timingStr = timing != nil ? " (\(String(format: "%.2f", timing!))s)" : ""
        log(
            component: viewModel,
            event: "API Call",
            details: "\(endpoint) - \(success ? "SUCCESS" : "FAILED")\(timingStr)"
        )
    }
    
    func logTokenValidation(isValid: Bool, remainingTime: Int? = nil) {
        let timeStr = remainingTime != nil ? " (expires in \(remainingTime!)s)" : ""
        log(
            component: "TokenManager",
            event: "Token Validation",
            details: "\(isValid ? "VALID" : "INVALID")\(timeStr)"
        )
    }
    
    // MARK: - Race Condition Detection
    
    func detectRaceCondition(viewModel: String, authReady: Bool, apiCalled: Bool) {
        if apiCalled && !authReady {
            log(
                component: "RaceConditionDetector",
                event: "⚠️ RACE CONDITION DETECTED",
                details: "\(viewModel) called API before auth ready"
            )
        }
    }
    
    // MARK: - Report Generation
    
    func generateReport() -> String {
        var report = "🔍 Authentication Debug Report\n"
        report += "Generated: \(Date())\n"
        report += "Total Events: \(events.count)\n\n"
        
        // Recent events
        report += "📋 Recent Events (last 20):\n"
        let recentEvents = Array(events.suffix(20))
        for event in recentEvents {
            report += "[\(event.formattedTime)] \(event.component): \(event.event)"
            if let details = event.details {
                report += " - \(details)"
            }
            report += "\n"
        }
        
        // Statistics
        report += "\n📊 Statistics:\n"
        let componentCounts = Dictionary(grouping: events, by: { $0.component })
            .mapValues { $0.count }
        
        for (component, count) in componentCounts.sorted(by: { $0.value > $1.value }) {
            report += "\(component): \(count) events\n"
        }
        
        // Race condition analysis
        let raceConditions = events.filter { $0.event.contains("RACE CONDITION") }
        if !raceConditions.isEmpty {
            report += "\n⚠️ Race Conditions Detected: \(raceConditions.count)\n"
            for condition in raceConditions {
                report += "- [\(condition.formattedTime)] \(condition.details ?? "Unknown")\n"
            }
        } else {
            report += "\n✅ No Race Conditions Detected\n"
        }
        
        return report
    }
    
    func exportReport() -> URL? {
        let report = generateReport()
        
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let fileName = "auth_debug_report_\(Int(Date().timeIntervalSince1970)).txt"
        let fileURL = documentsPath.appendingPathComponent(fileName)
        
        do {
            try report.write(to: fileURL, atomically: true, encoding: .utf8)
            return fileURL
        } catch {
            print("Failed to export report: \(error)")
            return nil
        }
    }
    
    // MARK: - Clear Data
    
    func clearEvents() {
        events.removeAll()
        log(component: "AuthenticationDebugger", event: "Events Cleared")
    }
    
    // MARK: - SwiftUI Integration
    
    func getRecentEventsForUI() -> [AuthEvent] {
        return Array(events.suffix(50).reversed())
    }
}

// MARK: - SwiftUI Debug View

struct AuthenticationDebugView: View {
    @State private var events: [AuthenticationDebugger.AuthEvent] = []
    @State private var showingReport = false
    @State private var reportText = ""
    
    var body: some View {
        NavigationView {
            VStack {
                // Controls
                HStack {
                    Button("Refresh") {
                        refreshEvents()
                    }
                    .buttonStyle(.bordered)
                    
                    Button("Clear") {
                        AuthenticationDebugger.shared.clearEvents()
                        refreshEvents()
                    }
                    .buttonStyle(.bordered)
                    
                    Button("Export Report") {
                        generateReport()
                    }
                    .buttonStyle(.borderedProminent)
                }
                .padding()
                
                // Events List
                List(events, id: \.timestamp) { event in
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text(event.component)
                                .font(.caption)
                                .foregroundColor(.secondary)
                            
                            Spacer()
                            
                            Text(event.formattedTime)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Text(event.event)
                            .font(.body)
                            .fontWeight(.medium)
                        
                        if let details = event.details {
                            Text(details)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    .padding(.vertical, 2)
                }
            }
            .navigationTitle("Auth Debug")
            .onAppear {
                refreshEvents()
            }
            .sheet(isPresented: $showingReport) {
                NavigationView {
                    ScrollView {
                        Text(reportText)
                            .font(.system(.caption, design: .monospaced))
                            .padding()
                    }
                    .navigationTitle("Debug Report")
                    .navigationBarTitleDisplayMode(.inline)
                    .toolbar {
                        ToolbarItem(placement: .navigationBarTrailing) {
                            Button("Done") {
                                showingReport = false
                            }
                        }
                    }
                }
            }
        }
    }
    
    private func refreshEvents() {
        events = AuthenticationDebugger.shared.getRecentEventsForUI()
    }
    
    private func generateReport() {
        reportText = AuthenticationDebugger.shared.generateReport()
        showingReport = true
    }
}

// MARK: - Convenience Extensions

extension AuthenticationStateManager {
    func debugLog(_ event: String, details: String? = nil) {
        AuthenticationDebugger.shared.log(
            component: "AuthenticationStateManager",
            event: event,
            details: details
        )
    }
}

extension QuizManagementViewModel {
    func debugLog(_ event: String, details: String? = nil) {
        AuthenticationDebugger.shared.log(
            component: "QuizManagementViewModel",
            event: event,
            details: details
        )
    }
}
