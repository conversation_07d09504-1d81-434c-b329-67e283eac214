//
//  ErrorHandling.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import Foundation
import SwiftUI

// MARK: - App Error Protocol
protocol AppError: Error, LocalizedError {
    var title: String { get }
    var message: String { get }
    var code: String { get }
    var isRetryable: Bool { get }
}




// MARK: - Authentication Errors
enum AuthError: AppError {
    case invalidCredentials
    case accountLocked
    case accountNotVerified
    case passwordExpired
    case sessionExpired
    case biometricNotAvailable
    case biometricFailed
    case tokenExpired
    case refreshTokenExpired
    
    var title: String {
        switch self {
        case .invalidCredentials:
            return "Invalid Credentials"
        case .accountLocked:
            return "Account Locked"
        case .accountNotVerified:
            return "Account Not Verified"
        case .passwordExpired:
            return "Password Expired"
        case .sessionExpired:
            return "Session Expired"
        case .biometricNotAvailable:
            return "Biometric Not Available"
        case .biometricFailed:
            return "Biometric Authentication Failed"
        case .tokenExpired, .refreshTokenExpired:
            return "Session Expired"
        }
    }
    
    var message: String {
        switch self {
        case .invalidCredentials:
            return "The email or password you entered is incorrect."
        case .accountLocked:
            return "Your account has been locked. Please contact support."
        case .accountNotVerified:
            return "Please verify your email address to continue."
        case .passwordExpired:
            return "Your password has expired. Please reset your password."
        case .sessionExpired:
            return "Your session has expired. Please sign in again."
        case .biometricNotAvailable:
            return "Biometric authentication is not available on this device."
        case .biometricFailed:
            return "Biometric authentication failed. Please try again."
        case .tokenExpired, .refreshTokenExpired:
            return "Your session has expired. Please sign in again."
        }
    }
    
    var code: String {
        switch self {
        case .invalidCredentials:
            return "AUTH_001"
        case .accountLocked:
            return "AUTH_002"
        case .accountNotVerified:
            return "AUTH_003"
        case .passwordExpired:
            return "AUTH_004"
        case .sessionExpired:
            return "AUTH_005"
        case .biometricNotAvailable:
            return "AUTH_006"
        case .biometricFailed:
            return "AUTH_007"
        case .tokenExpired:
            return "AUTH_008"
        case .refreshTokenExpired:
            return "AUTH_009"
        }
    }
    
    var isRetryable: Bool {
        switch self {
        case .biometricFailed:
            return true
        case .invalidCredentials, .accountLocked, .accountNotVerified, .passwordExpired, .sessionExpired, .biometricNotAvailable, .tokenExpired, .refreshTokenExpired:
            return false
        }
    }
    
    var errorDescription: String? {
        return message
    }
}

// MARK: - Transaction Errors
enum TransactionError: AppError {
    case insufficientBalance
    case invalidAmount
    case invalidRecipient
    case transactionFailed
    case networkFee
    case merchantNotFound
    case rewardNotAvailable
    case redemptionExpired
    
    var title: String {
        switch self {
        case .insufficientBalance:
            return "Insufficient Balance"
        case .invalidAmount:
            return "Invalid Amount"
        case .invalidRecipient:
            return "Invalid Recipient"
        case .transactionFailed:
            return "Transaction Failed"
        case .networkFee:
            return "Network Fee Error"
        case .merchantNotFound:
            return "Merchant Not Found"
        case .rewardNotAvailable:
            return "Reward Not Available"
        case .redemptionExpired:
            return "Redemption Expired"
        }
    }
    
    var message: String {
        switch self {
        case .insufficientBalance:
            return "You don't have enough tokens for this transaction."
        case .invalidAmount:
            return "Please enter a valid amount."
        case .invalidRecipient:
            return "Please enter a valid recipient address."
        case .transactionFailed:
            return "Transaction failed. Please try again."
        case .networkFee:
            return "Unable to calculate network fee. Please try again."
        case .merchantNotFound:
            return "Merchant not found. Please check the merchant code."
        case .rewardNotAvailable:
            return "This reward is no longer available."
        case .redemptionExpired:
            return "This redemption has expired."
        }
    }
    
    var code: String {
        switch self {
        case .insufficientBalance:
            return "TXN_001"
        case .invalidAmount:
            return "TXN_002"
        case .invalidRecipient:
            return "TXN_003"
        case .transactionFailed:
            return "TXN_004"
        case .networkFee:
            return "TXN_005"
        case .merchantNotFound:
            return "TXN_006"
        case .rewardNotAvailable:
            return "TXN_007"
        case .redemptionExpired:
            return "TXN_008"
        }
    }
    
    var isRetryable: Bool {
        switch self {
        case .transactionFailed, .networkFee:
            return true
        case .insufficientBalance, .invalidAmount, .invalidRecipient, .merchantNotFound, .rewardNotAvailable, .redemptionExpired:
            return false
        }
    }
    
    var errorDescription: String? {
        return message
    }
}

// MARK: - Validation Errors
enum ValidationError: AppError {
    case invalidEmail
    case invalidPhone
    case passwordTooShort
    case passwordTooWeak
    case passwordMismatch
    case requiredFieldEmpty(String)
    case invalidFormat(String)
    
    var title: String {
        switch self {
        case .invalidEmail:
            return "Invalid Email"
        case .invalidPhone:
            return "Invalid Phone"
        case .passwordTooShort, .passwordTooWeak:
            return "Invalid Password"
        case .passwordMismatch:
            return "Password Mismatch"
        case .requiredFieldEmpty:
            return "Required Field"
        case .invalidFormat:
            return "Invalid Format"
        }
    }
    
    var message: String {
        switch self {
        case .invalidEmail:
            return "Please enter a valid email address."
        case .invalidPhone:
            return "Please enter a valid phone number."
        case .passwordTooShort:
            return "Password must be at least \(AppConstants.Validation.minPasswordLength) characters long."
        case .passwordTooWeak:
            return "Password must contain uppercase, lowercase, number, and special character."
        case .passwordMismatch:
            return "Passwords do not match."
        case .requiredFieldEmpty(let field):
            return "\(field) is required."
        case .invalidFormat(let field):
            return "\(field) format is invalid."
        }
    }
    
    var code: String {
        switch self {
        case .invalidEmail:
            return "VAL_001"
        case .invalidPhone:
            return "VAL_002"
        case .passwordTooShort:
            return "VAL_003"
        case .passwordTooWeak:
            return "VAL_004"
        case .passwordMismatch:
            return "VAL_005"
        case .requiredFieldEmpty:
            return "VAL_006"
        case .invalidFormat:
            return "VAL_007"
        }
    }
    
    var isRetryable: Bool {
        return false
    }
    
    var errorDescription: String? {
        return message
    }
}

// MARK: - Error Handler
class ErrorHandler: ObservableObject {
    @Published var currentError: AppError?
    @Published var showError = false
    
    static let shared = ErrorHandler()
    
    private init() {}
    
    func handle(_ error: Error) {
        DispatchQueue.main.async {
            if let appError = error as? AppError {
                self.currentError = appError
            } else {
                // Convert generic errors to app errors
                self.currentError = self.convertToAppError(error)
            }
            self.showError = true
        }
    }
    
    func clearError() {
        currentError = nil
        showError = false
    }
    
    private func convertToAppError(_ error: Error) -> AppError {
        // Convert URLError to NetworkError
        if let urlError = error as? URLError {
            switch urlError.code {
            case .notConnectedToInternet, .networkConnectionLost:
                return NetworkError.serverError(0, "No internet connection") as! AppError
            case .timedOut:
                return NetworkError.serverError(-1001, "Request timed out") as! AppError
            case .cannotFindHost, .cannotConnectToHost:
                return NetworkError.serverError(-1005, "Cannot connect to host") as! AppError
            default:
                return NetworkError.decodingError(NSError(domain: "Unknown", code: -1)) as! AppError
            }
        }
        
        // Convert other errors to generic network error
        return NetworkError.decodingError(NSError(domain: "Unknown", code: -1)) as! AppError
    }
}

// MARK: - Error Alert Modifier
struct ErrorAlert: ViewModifier {
    @ObservedObject var errorHandler = ErrorHandler.shared
    
    func body(content: Content) -> some View {
        content
            .alert(errorHandler.currentError?.title ?? "Error", isPresented: $errorHandler.showError) {
                if errorHandler.currentError?.isRetryable == true {
                    Button("Retry") {
                        // TODO: Implement retry logic
                        errorHandler.clearError()
                    }
                }
                Button("OK") {
                    errorHandler.clearError()
                }
            } message: {
                Text(errorHandler.currentError?.message ?? "An unknown error occurred")
            }
    }
}

extension View {
    func errorAlert() -> some View {
        modifier(ErrorAlert())
    }
}
