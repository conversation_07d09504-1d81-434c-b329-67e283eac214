//
//  AuthErrorHandler.swift
//  mobile-app-template
//
//  Created by AI Assistant on 28/7/25.
//

import Foundation
import SwiftUI

/// Enhanced Error Handling for Authentication and API calls
class AuthErrorHandler {
    static let shared = AuthErrorHandler()
    
    private init() {}
    
    // MARK: - Error Analysis
    
    /// Analyze and categorize errors for better user experience
    func analyzeError(_ error: Error) -> ErrorAnalysis {
        print("🚨 AuthErrorHandler: Analyzing error: \(error)")
        
        // Network errors
        if let urlError = error as? URLError {
            return analyzeURLError(urlError)
        }
        
        // Custom network errors
        if let networkError = error as? NetworkError {
            return analyzeNetworkError(networkError)
        }
        
        // Auth errors
        if let authError = error as? AuthError {
            return analyzeAuthError(authError)
        }

        // Login errors
        if let loginError = error as? LoginError {
            return analyzeLoginError(loginError)
        }
        
        // Generic error
        return ErrorAnalysis(
            category: .unknown,
            userMessage: "Đã xảy ra lỗi không xác đ<PERSON>nh. Vui lòng thử lại sau.",
            technicalMessage: error.localizedDescription,
            shouldRetry: true,
            shouldLogout: false
        )
    }
    
    private func analyzeURLError(_ error: URLError) -> ErrorAnalysis {
        switch error.code {
        case .notConnectedToInternet, .networkConnectionLost:
            return ErrorAnalysis(
                category: .network,
                userMessage: "Không có kết nối internet. Vui lòng kiểm tra và thử lại.",
                technicalMessage: "Network connection lost: \(error.localizedDescription)",
                shouldRetry: true,
                shouldLogout: false
            )
            
        case .timedOut:
            return ErrorAnalysis(
                category: .network,
                userMessage: "Kết nối quá chậm. Vui lòng thử lại.",
                technicalMessage: "Request timeout: \(error.localizedDescription)",
                shouldRetry: true,
                shouldLogout: false
            )
            
        case .cannotFindHost, .cannotConnectToHost:
            return ErrorAnalysis(
                category: .server,
                userMessage: "Không thể kết nối đến server. Vui lòng thử lại sau.",
                technicalMessage: "Cannot connect to host: \(error.localizedDescription)",
                shouldRetry: true,
                shouldLogout: false
            )
            
        default:
            return ErrorAnalysis(
                category: .network,
                userMessage: "Lỗi kết nối. Vui lòng thử lại.",
                technicalMessage: "URL Error: \(error.localizedDescription)",
                shouldRetry: true,
                shouldLogout: false
            )
        }
    }
    
    private func analyzeNetworkError(_ error: NetworkError) -> ErrorAnalysis {
        switch error {
        case .unauthorized:
            return ErrorAnalysis(
                category: .authentication,
                userMessage: "Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.",
                technicalMessage: "Unauthorized: Token expired or invalid",
                shouldRetry: false,
                shouldLogout: true
            )
            
        case .serverError(let code, let message):
            if code == 401 {
                return ErrorAnalysis(
                    category: .authentication,
                    userMessage: "Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.",
                    technicalMessage: "Server returned 401: \(message ?? "Unauthorized")",
                    shouldRetry: false,
                    shouldLogout: true
                )
            } else if code == 403 {
                return ErrorAnalysis(
                    category: .authorization,
                    userMessage: "Bạn không có quyền truy cập tính năng này.",
                    technicalMessage: "Server returned 403: \(message ?? "Forbidden")",
                    shouldRetry: false,
                    shouldLogout: false
                )
            } else if code >= 500 {
                return ErrorAnalysis(
                    category: .server,
                    userMessage: "Server đang gặp sự cố. Vui lòng thử lại sau.",
                    technicalMessage: "Server error \(code): \(message ?? "Unknown")",
                    shouldRetry: true,
                    shouldLogout: false
                )
            } else {
                return ErrorAnalysis(
                    category: .api,
                    userMessage: "Có lỗi xảy ra. Vui lòng thử lại.",
                    technicalMessage: "API error \(code): \(message ?? "Unknown")",
                    shouldRetry: true,
                    shouldLogout: false
                )
            }
            
        case .decodingError(let decodingError):
            return ErrorAnalysis(
                category: .data,
                userMessage: "Lỗi xử lý dữ liệu từ server. Vui lòng thử lại.",
                technicalMessage: "Decoding error: \(decodingError.localizedDescription)",
                shouldRetry: true,
                shouldLogout: false
            )
            
        case .networkUnavailable:
            return ErrorAnalysis(
                category: .network,
                userMessage: "Không có kết nối internet. Vui lòng kiểm tra và thử lại.",
                technicalMessage: "Network unavailable",
                shouldRetry: true,
                shouldLogout: false
            )
            
        default:
            return ErrorAnalysis(
                category: .unknown,
                userMessage: "Lỗi kết nối. Vui lòng thử lại.",
                technicalMessage: "Network error: \(error.localizedDescription)",
                shouldRetry: true,
                shouldLogout: false
            )
        }
    }
    
    private func analyzeAuthError(_ error: AuthError) -> ErrorAnalysis {
        switch error {
        case .invalidCredentials:
            return ErrorAnalysis(
                category: .authentication,
                userMessage: "Email hoặc mật khẩu không đúng.",
                technicalMessage: "Invalid credentials",
                shouldRetry: false,
                shouldLogout: false
            )

        case .accountLocked:
            return ErrorAnalysis(
                category: .authentication,
                userMessage: "Tài khoản đã bị khóa. Vui lòng liên hệ hỗ trợ.",
                technicalMessage: "Account locked",
                shouldRetry: false,
                shouldLogout: false
            )

        case .sessionExpired, .tokenExpired, .refreshTokenExpired:
            return ErrorAnalysis(
                category: .authentication,
                userMessage: "Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.",
                technicalMessage: "Session/token expired",
                shouldRetry: false,
                shouldLogout: true
            )

        default:
            return ErrorAnalysis(
                category: .authentication,
                userMessage: "Lỗi xác thực. Vui lòng thử lại.",
                technicalMessage: "Authentication error: \(error.localizedDescription)",
                shouldRetry: false,
                shouldLogout: false
            )
        }
    }

    private func analyzeLoginError(_ error: LoginError) -> ErrorAnalysis {
        switch error {
        case .loginFailed(let message):
            return ErrorAnalysis(
                category: .authentication,
                userMessage: message,
                technicalMessage: "Login failed: \(message)",
                shouldRetry: false,
                shouldLogout: false
            )

        case .accessDenied(let message):
            return ErrorAnalysis(
                category: .authorization,
                userMessage: message,
                technicalMessage: "Access denied: \(message)",
                shouldRetry: false,
                shouldLogout: false
            )

        case .networkError(let message):
            return ErrorAnalysis(
                category: .network,
                userMessage: "Lỗi kết nối. Vui lòng thử lại.",
                technicalMessage: "Network error: \(message)",
                shouldRetry: true,
                shouldLogout: false
            )
        }
    }
}

// MARK: - Error Analysis Model
struct ErrorAnalysis {
    let category: ErrorCategory
    let userMessage: String
    let technicalMessage: String
    let shouldRetry: Bool
    let shouldLogout: Bool
}

enum ErrorCategory {
    case authentication
    case authorization
    case network
    case server
    case api
    case data
    case unknown
}
