//
//  DebugMenu.swift
//  VantisInstructor
//
//  Created by AI Assistant on 29/7/25.
//

import SwiftUI

/// Debug menu for testing and development
struct DebugMenu: View {
    @State private var showingAuthDebug = false
    @State private var showingSystemInfo = false
    @EnvironmentObject var authStateManager: AuthenticationStateManager
    
    var body: some View {
        NavigationView {
            List {
                Section("Authentication Debug") {
                    But<PERSON>("Auth Flow Monitor") {
                        showingAuthDebug = true
                    }
                    
                    But<PERSON>("Force Logout") {
                        Task {
                            await authStateManager.logout()
                        }
                    }
                    
                    Button("Check Auth State") {
                        Task {
                            let isReady = await authStateManager.isReadyForAPICall()
                            AuthenticationDebugger.shared.log(
                                component: "DebugMenu",
                                event: "Manual Auth Check",
                                details: "Ready: \(isReady)"
                            )
                        }
                    }
                }
                
                Section("System Info") {
                    Button("View System Info") {
                        showingSystemInfo = true
                    }
                    
                    But<PERSON>("Clear Debug Logs") {
                        AuthenticationDebugger.shared.clearEvents()
                    }
                    
                    <PERSON><PERSON>("Export Debug Report") {
                        if let url = AuthenticationDebugger.shared.exportReport() {
                            print("Debug report exported to: \(url)")
                        }
                    }
                }
                
                Section("Current State") {
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Text("Auth State:")
                            Spacer()
                            Text("\(authStateManager.authenticationState)")
                                .foregroundColor(.secondary)
                        }
                        
                        HStack {
                            Text("User:")
                            Spacer()
                            Text(authStateManager.currentUser?.displayName ?? "None")
                                .foregroundColor(.secondary)
                        }
                        
                        HStack {
                            Text("Loading:")
                            Spacer()
                            Text(authStateManager.isLoading ? "Yes" : "No")
                                .foregroundColor(.secondary)
                        }
                    }
                }
                
                Section("Quick Actions") {
                    Button("Simulate Race Condition") {
                        simulateRaceCondition()
                    }
                    .foregroundColor(.orange)
                    
                    Button("Test Token Validation") {
                        testTokenValidation()
                    }
                    
                    Button("Test Lazy Loading") {
                        testLazyLoading()
                    }
                }
            }
            .navigationTitle("Debug Menu")
            .sheet(isPresented: $showingAuthDebug) {
                AuthenticationDebugView()
            }
            .sheet(isPresented: $showingSystemInfo) {
                SystemInfoView()
            }
        }
    }
    
    private func simulateRaceCondition() {
        AuthenticationDebugger.shared.log(
            component: "DebugMenu",
            event: "Simulating Race Condition"
        )
        
        // Simulate a ViewModel trying to call API before auth is ready
        AuthenticationDebugger.shared.detectRaceCondition(
            viewModel: "TestViewModel",
            authReady: false,
            apiCalled: true
        )
    }
    
    private func testTokenValidation() {
        Task {
            let isValid = await UnifiedAuthManager.shared.validateToken()
            AuthenticationDebugger.shared.log(
                component: "DebugMenu",
                event: "Token Validation Test",
                details: "Valid: \(isValid)"
            )
        }
    }
    
    private func testLazyLoading() {
        AuthenticationDebugger.shared.log(
            component: "DebugMenu",
            event: "Testing Lazy Loading Pattern"
        )
        
        Task {
            let isReady = await authStateManager.isReadyForAPICall()
            if isReady {
                AuthenticationDebugger.shared.log(
                    component: "DebugMenu",
                    event: "Lazy Loading Test",
                    details: "Auth ready - would initialize ViewModel"
                )
            } else {
                AuthenticationDebugger.shared.log(
                    component: "DebugMenu",
                    event: "Lazy Loading Test",
                    details: "Auth not ready - would wait"
                )
            }
        }
    }
}

struct SystemInfoView: View {
    var body: some View {
        NavigationView {
            List {
                Section("Device Info") {
                    InfoRow(title: "Device", value: UIDevice.current.model)
                    InfoRow(title: "iOS Version", value: UIDevice.current.systemVersion)
                    InfoRow(title: "App Version", value: Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "Unknown")
                    InfoRow(title: "Build", value: Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "Unknown")
                }
                
                Section("Memory Info") {
                    InfoRow(title: "Memory Usage", value: "\(getMemoryUsage()) MB")
                    InfoRow(title: "Available Memory", value: "\(getAvailableMemory()) MB")
                }
                
                Section("Token Info") {
                    if let token = TokenManager.shared.getToken() {
                        InfoRow(title: "Token Length", value: "\(token.count) chars")
                        InfoRow(title: "Token Valid", value: TokenManager.shared.isTokenValid ? "Yes" : "No")
                        
                        if let jwt = token.decodeJWT() {
                            InfoRow(title: "Token Expires", value: Date(timeIntervalSince1970: TimeInterval(jwt.exp)).formatted())
                            InfoRow(title: "Remaining Time", value: "\(jwt.exp - Int(Date().timeIntervalSince1970)) seconds")
                        }
                    } else {
                        InfoRow(title: "Token", value: "Not available")
                    }
                }
            }
            .navigationTitle("System Info")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
    
    private func getMemoryUsage() -> Int {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            return Int(info.resident_size) / 1024 / 1024
        } else {
            return 0
        }
    }
    
    private func getAvailableMemory() -> Int {
        let host = mach_host_self()
        var size = mach_msg_type_number_t(MemoryLayout<vm_statistics64_data_t>.size / MemoryLayout<integer_t>.size)
        var hostInfo = vm_statistics64_data_t()
        
        let result = withUnsafeMutablePointer(to: &hostInfo) {
            $0.withMemoryRebound(to: integer_t.self, capacity: Int(size)) {
                host_statistics64(host, HOST_VM_INFO64, $0, &size)
            }
        }
        
        if result == KERN_SUCCESS {
            let pageSize = vm_kernel_page_size
            let freeMemory = Int64(hostInfo.free_count) * Int64(pageSize)
            return Int(freeMemory / 1024 / 1024)
        } else {
            return 0
        }
    }
}

struct InfoRow: View {
    let title: String
    let value: String
    
    var body: some View {
        HStack {
            Text(title)
            Spacer()
            Text(value)
                .foregroundColor(.secondary)
        }
    }
}

// MARK: - Debug Menu Access

extension View {
    func debugMenu() -> some View {
        self.overlay(
            // Triple tap to show debug menu
            Color.clear
                .contentShape(Rectangle())
                .onTapGesture(count: 3) {
                    showDebugMenu()
                }
        )
    }
    
    private func showDebugMenu() {
        #if DEBUG
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {
            let debugMenu = DebugMenu()
                .environmentObject(AuthenticationStateManager.shared)
            let hostingController = UIHostingController(rootView: debugMenu)
            window.rootViewController?.present(hostingController, animated: true)
        }
        #endif
    }
}

// MARK: - AuthenticationState Extension for Display

extension AuthenticationStateManager.AuthenticationState: CustomStringConvertible {
    var description: String {
        switch self {
        case .checking:
            return "Checking"
        case .unauthenticated:
            return "Unauthenticated"
        case .authenticated:
            return "Authenticated"
        case .error(let message):
            return "Error: \(message)"
        }
    }
}
