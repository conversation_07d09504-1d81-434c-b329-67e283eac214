//
//  SimpleAPITest.swift
//  mobile-app-template
//
//  Created by Instructor App on 28/7/25.
//

import Foundation

// MARK: - Simple API Test for Lessons
@MainActor
class SimpleAPITest: ObservableObject {
    @Published var isLoading = false
    @Published var result = ""
    @Published var lessons: [String] = []
    
    private let baseURL = "https://lms-dev.ebill.vn/api/v1"
    
    // MARK: - Test Login
    func testLogin() async {
        isLoading = true
        result = "🔐 Testing login..."
        
        do {
            guard let url = URL(string: "\(baseURL)/auth/sign-in") else {
                result = "❌ Invalid URL"
                isLoading = false
                return
            }
            
            let loginData = [
                "username": "<EMAIL>",
                "password": "earnbase@X2025",
                "remember_me": false
            ] as [String : Any]
            
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            request.httpBody = try JSONSerialization.data(withJSONObject: loginData)
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            if let httpResponse = response as? HTTPURLResponse {
                result += "\n📡 Status: \(httpResponse.statusCode)"
                
                if let jsonString = String(data: data, encoding: .utf8) {
                    result += "\n📄 Response: \(jsonString.prefix(500))..."
                    
                    // Try to parse token
                    if let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                       let success = json["success"] as? Bool,
                       success,
                       let data = json["data"] as? [String: Any],
                       let accessToken = data["access_token"] as? String {
                        
                        result += "\n✅ Login successful!"
                        result += "\n🔑 Token: \(accessToken.prefix(20))..."
                        
                        // Test lessons API with token
                        await testLessonsAPI(token: accessToken)
                    } else {
                        result += "\n❌ Login failed or invalid response format"
                    }
                } else {
                    result += "\n❌ Could not decode response"
                }
            }
            
        } catch {
            result += "\n❌ Error: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    // MARK: - Test Lessons API
    private func testLessonsAPI(token: String) async {
        result += "\n\n📚 Testing lessons API..."
        
        do {
            guard let url = URL(string: "\(baseURL)/instructors/lessons?page=1&page_size=10") else {
                result += "\n❌ Invalid lessons URL"
                return
            }
            
            var request = URLRequest(url: url)
            request.httpMethod = "GET"
            request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            if let httpResponse = response as? HTTPURLResponse {
                result += "\n📡 Lessons Status: \(httpResponse.statusCode)"
                
                if let jsonString = String(data: data, encoding: .utf8) {
                    result += "\n📄 Lessons Response: \(jsonString.prefix(1000))..."
                    
                    // Try to parse lessons
                    if let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                       let success = json["success"] as? Bool,
                       success,
                       let lessonsData = json["data"] as? [[String: Any]] {
                        
                        result += "\n✅ Lessons API successful!"
                        result += "\n📊 Found \(lessonsData.count) lessons"
                        
                        // Extract lesson names
                        lessons = lessonsData.compactMap { lesson in
                            if let name = lesson["name"] as? String,
                               let className = lesson["class_name"] as? String {
                                return "\(name) - \(className)"
                            }
                            return nil
                        }
                        
                        result += "\n📝 Lessons:"
                        for (index, lesson) in lessons.enumerated() {
                            result += "\n  \(index + 1). \(lesson)"
                        }
                        
                    } else {
                        result += "\n❌ Could not parse lessons data"
                    }
                } else {
                    result += "\n❌ Could not decode lessons response"
                }
            }
            
        } catch {
            result += "\n❌ Lessons Error: \(error.localizedDescription)"
        }
    }
    
    // MARK: - Test Mock Data
    func testMockData() {
        isLoading = true
        result = "🧪 Testing mock data..."
        
        // Simulate delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            self.lessons = [
                "SwiftUI Navigation và State Management - CS301",
                "Advanced iOS Development - CS401", 
                "Database Design Fundamentals - DB201",
                "Mobile App Architecture - MA301",
                "User Interface Design - UI201"
            ]
            
            self.result += "\n✅ Mock data loaded successfully!"
            self.result += "\n📊 Found \(self.lessons.count) mock lessons"
            self.result += "\n📝 Mock Lessons:"
            
            for (index, lesson) in self.lessons.enumerated() {
                self.result += "\n  \(index + 1). \(lesson)"
            }
            
            self.isLoading = false
        }
    }
    
    // MARK: - Clear Results
    func clearResults() {
        result = ""
        lessons = []
    }
}

// MARK: - Simple API Test View
import SwiftUI

struct SimpleAPITestView: View {
    @StateObject private var apiTest = SimpleAPITest()
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header
                    VStack(spacing: 8) {
                        Text("🧪 API Test")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text("Test API calls để lấy danh sách lessons")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.top)
                    
                    // Action Buttons
                    VStack(spacing: 12) {
                        Button("🌐 Test Real API") {
                            Task {
                                await apiTest.testLogin()
                            }
                        }
                        .disabled(apiTest.isLoading)
                        .buttonStyle(.borderedProminent)
                        .controlSize(.large)
                        
                        Button("🧪 Test Mock Data") {
                            apiTest.testMockData()
                        }
                        .disabled(apiTest.isLoading)
                        .buttonStyle(.bordered)
                        .controlSize(.large)
                        
                        Button("🗑️ Clear Results") {
                            apiTest.clearResults()
                        }
                        .buttonStyle(.bordered)
                        .foregroundColor(.red)
                        .controlSize(.large)
                    }
                    
                    // Loading Indicator
                    if apiTest.isLoading {
                        ProgressView("Testing API...")
                            .scaleEffect(0.9)
                    }
                    
                    // Results
                    if !apiTest.result.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("📋 Test Results:")
                                .font(.headline)
                            
                            Text(apiTest.result)
                                .font(.caption)
                                .monospaced()
                                .padding()
                                .background(Color.gray.opacity(0.1))
                                .cornerRadius(8)
                        }
                        .padding(.horizontal)
                    }
                    
                    // Lessons List
                    if !apiTest.lessons.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("📚 Lessons Found:")
                                .font(.headline)
                            
                            ForEach(Array(apiTest.lessons.enumerated()), id: \.offset) { index, lesson in
                                HStack {
                                    Text("\(index + 1)")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                        .frame(width: 20)
                                    
                                    Text(lesson)
                                        .font(.caption)
                                    
                                    Spacer()
                                }
                                .padding(.vertical, 4)
                                .padding(.horizontal, 8)
                                .background(Color.blue.opacity(0.1))
                                .cornerRadius(6)
                            }
                        }
                        .padding(.horizontal)
                    }
                    
                    Spacer()
                }
            }
            .navigationTitle("API Test")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

#Preview {
    SimpleAPITestView()
}
