//
//  LoginService.swift
//  mobile-app-template
//
//  Created by In<PERSON><PERSON><PERSON> <PERSON>pp on 27/7/25.
//

import Foundation

class LoginService {
    static let shared = LoginService()
    private let apiClient = APIClient.shared
    private let tokenManager = TokenManager.shared
    
    private init() {}
    
    func login(username: String, password: String, rememberMe: Bool = false) async throws -> JWTPayload {
        print("🔐 LoginService: Attempting login for username: \(username)")
        
        let request = InstructorLoginRequest(
            username: username,
            password: password,
            deviceInfo: DeviceInfo.current(),
            rememberMe: rememberMe,
            mfaCode: nil
        )
        
        let response: LoginResponse = try await apiClient.request(
            endpoint: "/auth/sign-in",
            method: .POST,
            parameters: try request.toDictionary(),
            responseType: LoginResponse.self,
            requiresAuth: false
        )
        
        guard response.success, let loginData = response.data else {
            let errorMessage = response.message ?? "Login failed"
            print("❌ Login failed: \(errorMessage)")
            throw LoginError.loginFailed(errorMessage)
        }

        // Decode JWT token to get user info
        guard let jwtPayload = loginData.accessToken.decodeJWT() else {
            print("❌ Failed to decode JWT token")
            throw LoginError.loginFailed("Invalid token format")
        }

        // Check if user is instructor
        guard jwtPayload.isInstructor else {
            print("❌ Access denied: User role is \(jwtPayload.role), not instructor")
            throw LoginError.accessDenied("Tài khoản không có quyền truy cập. Chỉ giảng viên mới có thể đăng nhập.")
        }

        // Save token
        await tokenManager.saveToken(loginData.accessToken)

        // Convert and save user
        let appUser = convertToAppUser(jwtPayload)
        await tokenManager.saveUser(appUser)

        print("✅ Login successful for instructor: \(jwtPayload.displayName)")
        return jwtPayload
    }
    
    func logout() async {
        print("🔐 LoginService: Logging out")

        // Clear local data
        await tokenManager.clearToken()
        print("✅ Local logout completed")
    }
    
    private func convertToAppUser(_ jwtPayload: JWTPayload) -> User {
        return User(
            id: String(jwtPayload.userId),
            email: jwtPayload.login,
            firstName: nil,
            lastName: nil,
            phone: nil,
            role: .instructor, // Map to instructor role
            isActive: true,
            avatar: nil,
            dateOfBirth: nil,
            lastLoginAt: Date(),
            createdAt: Date(),
            updatedAt: Date(),
            businessName: nil,
            businessId: nil,
            category: nil,
            businessPhone: nil,
            website: nil,
            businessDescription: nil,
            businessStatus: nil,
            onboardedAt: nil
        )
    }
}

// MARK: - Login Errors
enum LoginError: Error, LocalizedError {
    case loginFailed(String)
    case accessDenied(String)
    case networkError(String)
    
    var errorDescription: String? {
        switch self {
        case .loginFailed(let message):
            return message
        case .accessDenied(let message):
            return message
        case .networkError(let message):
            return message
        }
    }
}


