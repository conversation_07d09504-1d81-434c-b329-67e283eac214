//
//  StudentManager.swift
//  mobile-app-template
//
//  Created by Instructor App on 23/7/25.
//

import Foundation
import SwiftUI

@MainActor
class StudentManager: ObservableObject {
    // MARK: - Published Properties
    @Published var students: [Student] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // Selected student for detail view
    @Published var selectedStudent: Student?
    
    // MARK: - Initialization
    init() {
        loadStudents()
    }
    
    // MARK: - Data Loading
    func loadStudents() {
        Task {
            await fetchStudents()
        }
    }
    
    func fetchStudents() async {
        isLoading = true
        errorMessage = nil
        
        do {
            // Simulate API call
            try await Task.sleep(nanoseconds: 800_000_000) // 0.8 seconds
            
            // Load mock data
            students = Student.mockStudents
            
        } catch {
            errorMessage = "Không thể tải danh sách học sinh: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    // MARK: - Student Operations
    func addStudent(_ studentData: CreateStudentData) async -> Bool {
        isLoading = true
        
        do {
            // Simulate API call
            try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
            
            let newStudent = Student(
                id: UUID().uuidString,
                studentId: studentData.studentId,
                email: studentData.email,
                firstName: studentData.firstName,
                lastName: studentData.lastName,
                phone: studentData.phone,
                avatar: nil,
                dateOfBirth: studentData.dateOfBirth,
                gender: studentData.gender,
                address: studentData.address,
                emergencyContact: studentData.emergencyContact,
                enrollmentDate: Date(),
                status: .active,
                gpa: nil,
                totalCredits: 0,
                completedCredits: 0,
                major: studentData.major,
                year: studentData.year,
                courses: nil,
                metadata: nil,
                createdAt: Date(),
                updatedAt: nil
            )
            
            students.append(newStudent)
            
            isLoading = false
            return true
            
        } catch {
            errorMessage = "Không thể thêm học sinh: \(error.localizedDescription)"
            isLoading = false
            return false
        }
    }
    
    func updateStudent(_ student: Student) async -> Bool {
        isLoading = true
        
        do {
            // Simulate API call
            try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
            
            if let index = students.firstIndex(where: { $0.id == student.id }) {
                students[index] = student
            }
            
            isLoading = false
            return true
            
        } catch {
            errorMessage = "Không thể cập nhật thông tin học sinh: \(error.localizedDescription)"
            isLoading = false
            return false
        }
    }
    
    func deleteStudent(_ studentId: String) async -> Bool {
        isLoading = true
        
        do {
            // Simulate API call
            try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
            
            students.removeAll { $0.id == studentId }
            
            isLoading = false
            return true
            
        } catch {
            errorMessage = "Không thể xóa học sinh: \(error.localizedDescription)"
            isLoading = false
            return false
        }
    }
    
    // MARK: - Utility Methods
    func getStudentsForCourse(_ courseId: String) -> [Student] {
        // In a real app, this would filter by course enrollment
        return students.filter { $0.isActive }
    }
    
    func searchStudents(_ query: String) -> [Student] {
        guard !query.isEmpty else { return students }
        
        return students.filter { student in
            student.displayName.localizedCaseInsensitiveContains(query) ||
            student.studentId.localizedCaseInsensitiveContains(query) ||
            student.email.localizedCaseInsensitiveContains(query)
        }
    }
    
    func getStudentsByYear(_ year: AcademicYear) -> [Student] {
        return students.filter { $0.year == year }
    }
    
    func getStudentsByStatus(_ status: StudentStatus) -> [Student] {
        return students.filter { $0.status == status }
    }
    
    func refreshData() async {
        await fetchStudents()
    }
}

// MARK: - Create Student Data
struct CreateStudentData {
    let studentId: String
    let email: String
    let firstName: String
    let lastName: String
    let phone: String?
    let dateOfBirth: Date?
    let gender: Gender?
    let address: String?
    let emergencyContact: EmergencyContact?
    let major: String?
    let year: AcademicYear?
}

// MARK: - Extensions
extension StudentManager {
    var activeStudents: [Student] {
        students.filter { $0.isActive }
    }
    
    var totalStudents: Int {
        students.count
    }
    
    var averageGPA: Double {
        let studentsWithGPA = students.compactMap { $0.gpa }
        guard !studentsWithGPA.isEmpty else { return 0 }
        
        return studentsWithGPA.reduce(0, +) / Double(studentsWithGPA.count)
    }
    
    var studentsByYear: [AcademicYear: [Student]] {
        Dictionary(grouping: students) { $0.year ?? .freshman }
    }
    
    var topPerformers: [Student] {
        students
            .compactMap { student in
                guard let gpa = student.gpa else { return nil }
                return student
            }
            .sorted { ($0.gpa ?? 0) > ($1.gpa ?? 0) }
            .prefix(10)
            .map { $0 }
    }
}
