//
//  InstructorProfileService.swift
//  mobile-app-template
//
//  Created by Instructor App on 27/7/25.
//

import Foundation
import Combine

// MARK: - Instructor Profile Service
class InstructorProfileService: ObservableObject {
    static let shared = InstructorProfileService()
    
    private let apiClient = APIClient.shared
    private let logger = Logger.shared
    
    @Published var currentProfile: InstructorProfile?
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private init() {}
    
    // MARK: - Public Methods
    
    /// Fetch instructor profile from API
    func fetchProfile() async throws -> InstructorProfile {
        print("👤 InstructorProfileService: Fetching instructor profile...")
        
        isLoading = true
        errorMessage = nil
        
        do {
            // Try to fetch profile from API
            let response: InstructorProfileResponse = try await apiClient.request(
                endpoint: "/instructors/profile",
                method: .GET,
                responseType: InstructorProfileResponse.self,
                requiresAuth: true
            )
            
            guard response.success, let profile = response.data else {
                let error = response.message ?? "Failed to fetch instructor profile"
                print("❌ InstructorProfileService: API error - \(error)")
                throw InstructorProfileError.apiError(error)
            }
            
            // Update published properties on main thread
            await MainActor.run {
                self.currentProfile = profile
                self.isLoading = false
            }
            
            print("✅ InstructorProfileService: Profile fetched successfully - \(profile.name)")
            return profile
            
        } catch {
            await MainActor.run {
                self.isLoading = false
                self.errorMessage = error.localizedDescription
            }
            
            // Handle specific server errors gracefully
            if let apiError = error as? NetworkError {
                switch apiError {
                case .serverError(let statusCode, let message):
                    if statusCode == 500 && message?.contains("birth_date") == true {
                        print("⚠️ InstructorProfileService: Server has data validation issue with birth_date")
                        throw InstructorProfileError.serverDataIssue("Server has data validation issues. Please contact support.")
                    }
                default:
                    break
                }
            }
            
            print("❌ InstructorProfileService: Failed to fetch profile - \(error)")
            throw error
        }
    }
    
    /// Get cached profile or fetch from API
    func getProfile(forceRefresh: Bool = false) async throws -> InstructorProfile {
        if !forceRefresh, let cachedProfile = currentProfile {
            print("📱 InstructorProfileService: Returning cached profile")
            return cachedProfile
        }
        
        return try await fetchProfile()
    }
    
    /// Clear cached profile data
    func clearProfile() {
        print("🧹 InstructorProfileService: Clearing cached profile")
        currentProfile = nil
        errorMessage = nil
    }
    
    /// Update profile (for future implementation)
    func updateProfile(_ updates: InstructorProfileUpdate) async throws -> InstructorProfile {
        print("👤 InstructorProfileService: Updating instructor profile...")
        
        isLoading = true
        errorMessage = nil
        
        do {
            let response: InstructorProfileResponse = try await apiClient.request(
                endpoint: "/instructors/profile",
                method: .PUT,
                parameters: try updates.toDictionary(),
                responseType: InstructorProfileResponse.self,
                requiresAuth: true
            )
            
            guard response.success, let profile = response.data else {
                let error = response.message ?? "Failed to update instructor profile"
                throw InstructorProfileError.apiError(error)
            }
            
            await MainActor.run {
                self.currentProfile = profile
                self.isLoading = false
            }
            
            print("✅ InstructorProfileService: Profile updated successfully")
            return profile
            
        } catch {
            await MainActor.run {
                self.isLoading = false
                self.errorMessage = error.localizedDescription
            }
            
            print("❌ InstructorProfileService: Failed to update profile - \(error)")
            throw error
        }
    }
}

// MARK: - Profile Update Model
struct InstructorProfileUpdate: Codable {
    let birthDate: String?
    let gender: String?
    let email: String?
    let phone: String?
    let address: String?
    let emergencyContact: String?
    let emergencyPhone: String?
    let bio: String?
    let teachingPhilosophy: String?

    enum CodingKeys: String, CodingKey {
        case birthDate = "birth_date"
        case gender
        case email
        case phone
        case address
        case emergencyContact = "emergency_contact"
        case emergencyPhone = "emergency_phone"
        case bio
        case teachingPhilosophy = "teaching_philosophy"
    }

    func toDictionary() throws -> [String: Any] {
        let data = try JSONEncoder().encode(self)
        return try JSONSerialization.jsonObject(with: data) as? [String: Any] ?? [:]
    }
}

// MARK: - Errors
enum InstructorProfileError: Error, LocalizedError {
    case apiError(String)
    case serverDataIssue(String)
    case networkError
    case unauthorized
    case notFound
    
    var errorDescription: String? {
        switch self {
        case .apiError(let message):
            return message
        case .serverDataIssue(let message):
            return message
        case .networkError:
            return "Không thể kết nối đến server. Vui lòng kiểm tra kết nối mạng."
        case .unauthorized:
            return "Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại."
        case .notFound:
            return "Không tìm thấy thông tin hồ sơ."
        }
    }
}

// MARK: - Extensions for UI Display
extension InstructorProfile {
    var displayName: String {
        return name
    }
    
    var formattedBirthDate: String? {
        guard let birthDate = birthDate else { return nil }
        return DateFormatter.displayDate.string(from: birthDate)
    }
    
    var ageString: String? {
        guard let birthDate = birthDate else { return nil }
        let age = Calendar.current.dateComponents([.year], from: birthDate, to: Date()).year ?? 0
        return "\(age) tuổi"
    }
    
    var primarySkill: InstructorSkill? {
        return skills.first { $0.isPrimary == true } ?? skills.first
    }
    
    var experienceYears: String {
        if let primarySkill = primarySkill, let years = primarySkill.yearsExperience {
            return String(format: "%.1f năm", years)
        }
        return "Chưa có thông tin"
    }
    
    var completionRate: Double {
        guard let total = statistics.totalLessons, total > 0,
              let completed = statistics.completedLessons else { return 0.0 }
        return Double(completed) / Double(total)
    }
    
    var formattedCompletionRate: String {
        return String(format: "%.1f%%", completionRate * 100)
    }
}

extension DateFormatter {
    static let displayDate: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.locale = Locale(identifier: "vi_VN")
        return formatter
    }()
}
