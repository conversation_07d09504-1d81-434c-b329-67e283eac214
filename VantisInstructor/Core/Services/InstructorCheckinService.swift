//
//  InstructorCheckinService.swift
//  mobile-app-template
//
//  Created by Instructor App on 29/7/25.
//

import Foundation
import CoreLocation
import UIKit

// MARK: - Request Models
struct InstructorCheckInRequest: Codable {
    let locationLat: Double?
    let locationLng: Double?
    let deviceInfo: String?
    let notes: String?
    
    enum CodingKeys: String, CodingKey {
        case locationLat = "location_lat"
        case locationLng = "location_lng"
        case deviceInfo = "device_info"
        case notes
    }
}

struct InstructorCheckOutRequest: Codable {
    let notes: String?
    let locationLat: Double?
    let locationLng: Double?
    let deviceInfo: String?
    let lessonSummary: String?
    
    enum CodingKeys: String, CodingKey {
        case notes
        case locationLat = "location_lat"
        case locationLng = "location_lng"
        case deviceInfo = "device_info"
        case lessonSummary = "lesson_summary"
    }
}

// MARK: - Response Models
struct InstructorCheckInResponse: Codable {
    let success: Bool
    let message: String
    let data: CheckinData?
    let meta: CheckinMeta?

    enum CodingKeys: String, CodingKey {
        case success, message, data, meta
    }
}

struct CheckinData: Codable {
    // Add specific fields if needed by API
    // For now, keeping it empty as per the API response format
}

struct CheckinMeta: Codable {
    // Add specific fields if needed by API
    // For now, keeping it empty as per the API response format
}

// MARK: - Service
class InstructorCheckinService: ObservableObject {
    private let apiClient: APIClient
    private let tokenManager: TokenManager
    
    init(apiClient: APIClient = APIClient.shared, tokenManager: TokenManager = TokenManager.shared) {
        self.apiClient = apiClient
        self.tokenManager = tokenManager
    }
    
    // MARK: - Check-in
    func checkinToLesson(lessonId: Int, location: CLLocation? = nil, notes: String? = nil) async throws -> InstructorCheckInResponse {
        print("🚀 InstructorCheckinService.checkinToLesson called with lessonId: \(lessonId)")
        let deviceInfo = getDeviceInfo()

        let request = InstructorCheckInRequest(
            locationLat: location?.coordinate.latitude,
            locationLng: location?.coordinate.longitude,
            deviceInfo: deviceInfo,
            notes: notes
        )

        let endpoint = "/instructors/lessons/\(lessonId)/checkin"
        print("🚀 Making API request to: \(endpoint)")
        print("🚀 Request body: \(request)")

        let response = try await apiClient.request(
            endpoint: endpoint,
            method: .POST,
            body: request,
            responseType: InstructorCheckInResponse.self
        )

        print("🚀 API response: \(response)")
        return response
    }
    
    // MARK: - Check-out
    func checkoutFromLesson(lessonId: Int, location: CLLocation? = nil, notes: String? = nil, lessonSummary: String? = nil) async throws -> InstructorCheckInResponse {
        let deviceInfo = getDeviceInfo()
        
        let request = InstructorCheckOutRequest(
            notes: notes,
            locationLat: location?.coordinate.latitude,
            locationLng: location?.coordinate.longitude,
            deviceInfo: deviceInfo,
            lessonSummary: lessonSummary
        )
        
        let endpoint = "/instructors/lessons/\(lessonId)/checkout"
        
        return try await apiClient.request(
            endpoint: endpoint,
            method: .POST,
            body: request,
            responseType: InstructorCheckInResponse.self
        )
    }
    
    // MARK: - Helper Methods
    private func getDeviceInfo() -> String {
        let device = UIDevice.current
        let systemName = device.systemName
        let systemVersion = device.systemVersion
        let model = device.model
        
        return "\(model) - \(systemName) \(systemVersion)"
    }
}

// MARK: - Error Handling
enum CheckinError: LocalizedError {
    case invalidLessonId
    case networkError(String)
    case unauthorized
    case serverError(String)
    
    var errorDescription: String? {
        switch self {
        case .invalidLessonId:
            return "ID buổi học không hợp lệ"
        case .networkError(let message):
            return "Lỗi kết nối: \(message)"
        case .unauthorized:
            return "Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại."
        case .serverError(let message):
            return "Lỗi server: \(message)"
        }
    }
}
