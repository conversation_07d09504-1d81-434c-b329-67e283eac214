//
//  SimpleLessonService.swift
//  mobile-app-template
//
//  Created by Inst<PERSON><PERSON> App on 28/7/25.
//

import Foundation

// MARK: - Simple Lesson Service Protocol
protocol SimpleLessonServiceProtocol {
    func fetchLessons() async throws -> SimpleLessonResponse
}

// MARK: - Simple Lesson Service Implementation
class SimpleLessonService: SimpleLessonServiceProtocol {
    private let baseURL = "https://lms-dev.ebill.vn/api/v1"
    
    func fetchLessons() async throws -> SimpleLessonResponse {
        print("🔥 SimpleLessonService: Fetching lessons from API...")
        
        // Step 1: Login to get token
        let token = try await login()
        
        // Step 2: Fetch lessons with token
        return try await fetchLessonsWithToken(token)
    }
    
    // MARK: - Private Methods
    private func login() async throws -> String {
        print("🔐 Logging in...")
        
        guard let url = URL(string: "\(baseURL)/auth/sign-in") else {
            throw URLError(.badURL)
        }
        
        let loginData = [
            "username": "<EMAIL>",
            "password": "earnbase@X2025",
            "remember_me": false
        ] as [String : Any]
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.httpBody = try JSONSerialization.data(withJSONObject: loginData)
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw URLError(.badServerResponse)
        }
        
        print("📡 Login Status: \(httpResponse.statusCode)")
        
        if httpResponse.statusCode == 200 {
            if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
               let success = json["success"] as? Bool,
               success,
               let data = json["data"] as? [String: Any],
               let accessToken = data["access_token"] as? String {
                
                print("✅ Login successful, token: \(accessToken.prefix(20))...")
                return accessToken
            } else {
                print("❌ Login failed: Invalid response format")
                throw URLError(.cannotParseResponse)
            }
        } else {
            print("❌ Login failed with status: \(httpResponse.statusCode)")
            if let errorData = String(data: data, encoding: .utf8) {
                print("Error response: \(errorData)")
            }
            throw URLError(.userAuthenticationRequired)
        }
    }
    
    private func fetchLessonsWithToken(_ token: String) async throws -> SimpleLessonResponse {
        print("📚 Fetching lessons with token...")
        
        guard let url = URL(string: "\(baseURL)/instructors/lessons?page=1&page_size=20") else {
            throw URLError(.badURL)
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw URLError(.badServerResponse)
        }
        
        print("📡 Lessons Status: \(httpResponse.statusCode)")
        
        if httpResponse.statusCode == 200 {
            do {
                let lessonResponse = try JSONDecoder().decode(SimpleLessonResponse.self, from: data)
                print("✅ Lessons fetched successfully: \(lessonResponse.data.count) lessons")
                return lessonResponse
            } catch {
                print("❌ Failed to decode lessons: \(error)")
                if let jsonString = String(data: data, encoding: .utf8) {
                    print("Raw response: \(jsonString.prefix(500))...")
                }
                throw error
            }
        } else {
            print("❌ Lessons API failed with status: \(httpResponse.statusCode)")
            if let errorData = String(data: data, encoding: .utf8) {
                print("Error response: \(errorData)")
            }
            throw URLError(.badServerResponse)
        }
    }
}

// MARK: - Mock Simple Lesson Service
class MockSimpleLessonService: SimpleLessonServiceProtocol {
    func fetchLessons() async throws -> SimpleLessonResponse {
        print("🧪 MockSimpleLessonService: Returning mock lessons")
        
        // Simulate network delay
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        
        return SimpleLessonResponse(
            success: true,
            message: "Dữ liệu được tải thành công",
            data: SimpleLesson.mockLessons,
            pagination: SimplePagination(
                page: 1,
                pageSize: 20,
                totalItems: SimpleLesson.mockLessons.count,
                totalPages: 1
            )
        )
    }
}

// MARK: - Service Factory
class SimpleLessonServiceFactory {
    static func create(useMockData: Bool = false) -> SimpleLessonServiceProtocol {
        if useMockData {
            return MockSimpleLessonService()
        } else {
            return SimpleLessonService()
        }
    }
}
