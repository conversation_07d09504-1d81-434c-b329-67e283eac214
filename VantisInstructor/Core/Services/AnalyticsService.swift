//
//  AnalyticsService.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 22/7/25.
//

import Foundation
import UIKit

// MARK: - Analytics Service
class AnalyticsService: ObservableObject {
    static let shared = AnalyticsService()
    
    private let apiClient = APIClient.shared
    private let logger = Logger.shared
    private var eventQueue: [AnalyticsEvent] = []
    private var sessionId: String
    private var sessionStartTime: Date
    
    // Configuration
    private let maxQueueSize = 50
    private let flushInterval: TimeInterval = 30 // 30 seconds
    private var flushTimer: Timer?
    
    private init() {
        sessionId = UUID().uuidString
        sessionStartTime = Date()
        setupFlushTimer()
        setupAppLifecycleObservers()
    }
    
    deinit {
        flushTimer?.invalidate()
        Task {
            await flush()
        }
    }
    
    // MARK: - Event Tracking
    func track(event: String, properties: [String: Any] = [:]) {
        Task {
            let userId = await TokenManager.shared.getUser(User.self)?.id
            let analyticsEvent = AnalyticsEvent(
                name: event,
                properties: properties,
                sessionId: sessionId,
                timestamp: Date(),
                userId: userId
            )

            await MainActor.run {
                eventQueue.append(analyticsEvent)
            }
        }
        
        logger.debug("Analytics event tracked: \(event)", category: .general)
        
        // Flush if queue is full
        if eventQueue.count >= maxQueueSize {
            Task {
                await flush()
            }
        }
    }
    
    // MARK: - User Events
    func trackUserLogin(method: String) {
        track(event: "user_login", properties: [
            "method": method,
            "platform": "iOS"
        ])
    }
    
    func trackUserLogout() {
        track(event: "user_logout", properties: [
            "session_duration": Date().timeIntervalSince(sessionStartTime)
        ])
    }
    
    func trackUserRegistration(method: String) {
        track(event: "user_registration", properties: [
            "method": method,
            "platform": "iOS"
        ])
    }
    
    // MARK: - Transaction Events
    func trackTokenEarned(amount: Double, businessId: String) {
        track(event: "token_earned", properties: [
            "amount": amount,
            "business_id": businessId,
            "currency": "LXT"
        ])
    }
    
    func trackTokenTransfer(amount: Double, recipientType: String) {
        track(event: "token_transfer", properties: [
            "amount": amount,
            "recipient_type": recipientType,
            "currency": "LXT"
        ])
    }
    
    func trackTransactionFailed(type: String, error: String) {
        track(event: "transaction_failed", properties: [
            "transaction_type": type,
            "error": error
        ])
    }
    
    // MARK: - Reward Events
    func trackRewardViewed(rewardId: String, category: String) {
        track(event: "reward_viewed", properties: [
            "reward_id": rewardId,
            "category": category
        ])
    }
    
    func trackRewardRedeemed(rewardId: String, pointsCost: Double) {
        track(event: "reward_redeemed", properties: [
            "reward_id": rewardId,
            "points_cost": pointsCost,
            "currency": "LXT"
        ])
    }
    
    func trackRewardSearch(query: String, resultsCount: Int) {
        track(event: "reward_search", properties: [
            "query": query,
            "results_count": resultsCount
        ])
    }
    
    // MARK: - UI Events
    func trackScreenView(screenName: String) {
        track(event: "screen_view", properties: [
            "screen_name": screenName
        ])
    }
    
    func trackButtonTap(buttonName: String, screenName: String) {
        track(event: "button_tap", properties: [
            "button_name": buttonName,
            "screen_name": screenName
        ])
    }
    
    func trackQRCodeScanned(type: String, success: Bool) {
        track(event: "qr_code_scanned", properties: [
            "type": type,
            "success": success
        ])
    }
    
    // MARK: - Error Events
    func trackError(error: Error, context: String) {
        track(event: "error_occurred", properties: [
            "error_message": error.localizedDescription,
            "context": context,
            "error_type": String(describing: type(of: error))
        ])
    }
    
    func trackNetworkError(endpoint: String, statusCode: Int) {
        track(event: "network_error", properties: [
            "endpoint": endpoint,
            "status_code": statusCode
        ])
    }
    
    // MARK: - Performance Events
    func trackPerformance(operation: String, duration: TimeInterval) {
        track(event: "performance_metric", properties: [
            "operation": operation,
            "duration_ms": duration * 1000
        ])
    }
    
    func trackAppLaunch(launchTime: TimeInterval) {
        track(event: "app_launch", properties: [
            "launch_time_ms": launchTime * 1000,
            "app_version": AppConstants.AppInfo.fullVersion
        ])
    }
    
    // MARK: - Session Management
    func startNewSession() {
        sessionId = UUID().uuidString
        sessionStartTime = Date()
        
        track(event: "session_start", properties: [
            "app_version": AppConstants.AppInfo.fullVersion,
            "device_model": UIDevice.current.model,
            "os_version": UIDevice.current.systemVersion
        ])
    }
    
    func endSession() {
        let sessionDuration = Date().timeIntervalSince(sessionStartTime)
        
        track(event: "session_end", properties: [
            "session_duration": sessionDuration
        ])
        
        Task {
            await flush()
        }
    }
    
    // MARK: - Data Flushing
    @MainActor
    private func flush() async {
        // TEMPORARILY DISABLED FOR DEBUGGING QUIZ API
        print("🔧 Analytics flush disabled for debugging")
        eventQueue.removeAll() // Clear queue to prevent memory buildup
        return

        /*
        guard !eventQueue.isEmpty else { return }

        let eventsToSend = eventQueue
        eventQueue.removeAll()

        do {
            let _: APIResponse<EmptyResponse> = try await apiClient.request(
                endpoint: APIEndpoints.Analytics.events,
                method: .POST,
                parameters: [
                    "events": eventsToSend.map { $0.toDictionary() }
                ],
                responseType: APIResponse<EmptyResponse>.self
            )

            logger.debug("Analytics events flushed: \(eventsToSend.count)", category: .general)

        } catch {
            // Re-add events to queue if failed
            eventQueue.insert(contentsOf: eventsToSend, at: 0)
            logger.error("Failed to flush analytics events", error: error, category: .general)
        }
        */
    }
    
    // MARK: - Timer Setup
    private func setupFlushTimer() {
        flushTimer = Timer.scheduledTimer(withTimeInterval: flushInterval, repeats: true) { [weak self] _ in
            Task {
                await self?.flush()
            }
        }
    }
    
    // MARK: - App Lifecycle
    private func setupAppLifecycleObservers() {
        NotificationCenter.default.addObserver(
            forName: UIApplication.didEnterBackgroundNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task {
                await self?.flush()
            }
        }
        
        NotificationCenter.default.addObserver(
            forName: UIApplication.willTerminateNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.endSession()
        }
        
        NotificationCenter.default.addObserver(
            forName: UIApplication.didBecomeActiveNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            // Check if session should be renewed (after 30 minutes of inactivity)
            if let startTime = self?.sessionStartTime,
               Date().timeIntervalSince(startTime) > 1800 {
                self?.startNewSession()
            }
        }
    }
}

// MARK: - Analytics Event Model
struct AnalyticsEvent {
    let name: String
    let properties: [String: Any]
    let sessionId: String
    let timestamp: Date
    let userId: String?
    
    func toDictionary() -> [String: Any] {
        var dict: [String: Any] = [
            "name": name,
            "properties": properties,
            "session_id": sessionId,
            "timestamp": timestamp.timeIntervalSince1970,
            "platform": "iOS",
            "app_version": AppConstants.AppInfo.fullVersion,
            "device_model": UIDevice.current.model,
            "os_version": UIDevice.current.systemVersion
        ]
        
        if let userId = userId {
            dict["user_id"] = userId
        }
        
        return dict
    }
}

// MARK: - Analytics Extensions
extension AnalyticsService {
    // Convenience methods for common events
    func trackFeatureUsage(_ feature: String) {
        track(event: "feature_used", properties: ["feature": feature])
    }
    
    func trackUserEngagement(action: String, value: Double? = nil) {
        var properties: [String: Any] = ["action": action]
        if let value = value {
            properties["value"] = value
        }
        track(event: "user_engagement", properties: properties)
    }
    
    func trackConversion(type: String, value: Double) {
        track(event: "conversion", properties: [
            "type": type,
            "value": value
        ])
    }
}
