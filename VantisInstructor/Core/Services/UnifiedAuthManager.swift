//
//  UnifiedAuthManager.swift
//  mobile-app-template
//
//  Created by AI Assistant on 28/7/25.
//

import Foundation
import SwiftUI

/// Unified Authentication Manager
/// Handles authentication for both LMS and EarnBase systems
class UnifiedAuthManager {
    static let shared = UnifiedAuthManager()
    
    let tokenManager = TokenManager.shared
    private let apiClient = APIClient.shared
    
    // MARK: - Authentication State
    @Published var isAuthenticated = false
    @Published var currentUser: User?
    @Published var authError: String?
    
    private init() {
        Task { @MainActor in
            checkAuthState()
        }
    }
    
    // MARK: - Authentication Methods
    
    /// Login with LMS system (for instructors)
    func loginWithLMS(username: String, password: String) async throws -> User {
        print("🔐 UnifiedAuthManager: Attempting LMS login for: \(username)")
        
        // Configure API for LMS
        APIConfiguration.shared.setupLMS()
        
        let request = InstructorLoginRequest(
            username: username,
            password: password,
            deviceInfo: DeviceInfo.current(),
            rememberMe: false,
            mfaCode: nil
        )
        
        let response: LoginResponse = try await apiClient.request(
            endpoint: "/auth/sign-in",
            method: .POST,
            parameters: try request.toDictionary(),
            responseType: LoginResponse.self,
            requiresAuth: false
        )
        
        guard response.success, let loginData = response.data else {
            let errorMessage = response.message ?? "Login failed"
            print("❌ LMS Login failed: \(errorMessage)")
            throw LoginError.loginFailed(errorMessage)
        }
        
        // Validate JWT and check instructor role
        guard let jwtPayload = loginData.accessToken.decodeJWT() else {
            throw LoginError.loginFailed("Invalid token format")
        }

        guard jwtPayload.isInstructor else {
            throw LoginError.accessDenied("Tài khoản không có quyền truy cập. Chỉ giảng viên mới có thể đăng nhập.")
        }
        
        // Save token and user
        await tokenManager.saveToken(loginData.accessToken)
        let appUser = convertToAppUser(jwtPayload)
        await tokenManager.saveUser(appUser)
        
        // Update state
        await MainActor.run {
            isAuthenticated = true
            currentUser = appUser
        }
        
        print("✅ LMS Login successful for instructor: \(jwtPayload.displayName)")
        return appUser
    }
    
    /// Validate current token with grace period for clock skew
    func validateToken() async -> Bool {
        guard let token = await tokenManager.getToken() else {
            print("🔐 No token found")
            return false
        }

        // Check JWT expiration if it's a JWT
        if let jwtPayload = token.decodeJWT() {
            let currentTime = Int(Date().timeIntervalSince1970)
            let gracePeriod = 300 // 5 minutes grace period for clock skew

            if jwtPayload.exp <= (currentTime - gracePeriod) {
                print("🔐 Token expired (with grace period)")
                await logout()
                return false
            }

            // Log remaining time for debugging
            let remainingTime = jwtPayload.exp - currentTime
            print("🔐 Token valid, remaining time: \(remainingTime) seconds")

            // If token is valid by JWT check, don't call server validation on app start
            // This prevents unnecessary logout when app restarts
            return true
        }

        // Only validate with server if JWT parsing fails or for explicit validation
        // Don't auto-logout on app start if server validation fails
        print("🔐 Token found but JWT parsing failed, assuming valid for app start")
        return true
    }
    
    /// Refresh token if possible
    func refreshTokenIfNeeded() async throws {
        // Implementation for token refresh
        // This depends on your backend's refresh token mechanism
    }
    
    /// Update auth state without server validation (for app restore)
    func updateAuthState(isAuthenticated: Bool, user: User?) async {
        await MainActor.run {
            self.isAuthenticated = isAuthenticated
            self.currentUser = user
        }
    }

    /// Logout
    func logout() async {
        print("🔐 UnifiedAuthManager: Logging out")

        await tokenManager.clearToken()

        await MainActor.run {
            isAuthenticated = false
            currentUser = nil
        }

        print("✅ Logout completed")
    }
    
    // MARK: - Helper Methods
    
    @MainActor private func checkAuthState() {
        if let _ = tokenManager.getToken(),
           let user = tokenManager.getUser(User.self) {
            print("🔐 Found saved token and user")
            isAuthenticated = true
            currentUser = user
        } else {
            print("🔐 No saved authentication found")
            isAuthenticated = false
            currentUser = nil
        }
    }
    
    private func convertToAppUser(_ jwtPayload: JWTPayload) -> User {
        return User(
            id: String(jwtPayload.userId),
            email: jwtPayload.login,
            firstName: nil,
            lastName: nil,
            phone: nil,
            role: .instructor,
            isActive: true,
            avatar: nil,
            dateOfBirth: nil,
            lastLoginAt: Date(),
            createdAt: Date(),
            updatedAt: Date(),
            businessName: nil,
            businessId: nil,
            category: nil,
            businessPhone: nil,
            website: nil,
            businessDescription: nil,
            businessStatus: nil,
            onboardedAt: nil
        )
    }
}


