//
//  AuthenticationStateManager.swift
//  VantisInstructor
//
//  Created by AI Assistant on 29/7/25.
//

import Foundation
import SwiftUI
import Combine

/// Centralized Authentication State Manager
/// Manages authentication state and coordinates between different auth components
@MainActor
class AuthenticationStateManager: ObservableObject {
    static let shared = AuthenticationStateManager()
    
    // MARK: - Published Properties
    @Published var authenticationState: AuthenticationState = .checking
    @Published var currentUser: User?
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    private let unifiedAuthManager = UnifiedAuthManager.shared
    private let tokenManager = TokenManager.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Authentication State Enum
    enum AuthenticationState: Equatable {
        case checking
        case unauthenticated
        case authenticated
        case error(String)
        
        var isAuthenticated: Bool {
            if case .authenticated = self {
                return true
            }
            return false
        }
    }
    
    private init() {
        setupObservers()
        checkInitialAuthState()
    }
    
    // MARK: - Setup
    private func setupObservers() {
        // Observe UnifiedAuthManager changes
        unifiedAuthManager.$isAuthenticated
            .combineLatest(unifiedAuthManager.$currentUser)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isAuth, user in
                self?.handleAuthStateChange(isAuthenticated: isAuth, user: user)
            }
            .store(in: &cancellables)
    }
    
    private func checkInitialAuthState() {
        print("🔐 AuthenticationStateManager: Checking initial auth state")
        AuthenticationDebugger.shared.log(
            component: "AuthenticationStateManager",
            event: "Initial Auth Check Started"
        )
        authenticationState = .checking

        Task {
            // Add small delay to ensure all components are initialized
            try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 second

            // First check if we have stored token and user without server validation
            if let token = await unifiedAuthManager.tokenManager.getToken(),
               let user = await unifiedAuthManager.tokenManager.getUser(User.self) {

                // Check JWT expiration locally first
                if let jwtPayload = token.decodeJWT() {
                    let currentTime = Int(Date().timeIntervalSince1970)
                    let gracePeriod = 300 // 5 minutes grace period

                    if jwtPayload.exp > (currentTime - gracePeriod) {
                        // Token is still valid, restore auth state without server call
                        authenticationState = .authenticated
                        currentUser = user
                        await unifiedAuthManager.updateAuthState(isAuthenticated: true, user: user)
                        print("🔐 AuthenticationStateManager: Restored auth state from storage - \(user.displayName)")
                        AuthenticationDebugger.shared.log(
                            component: "AuthenticationStateManager",
                            event: "Initial Auth Check Complete",
                            details: "Restored from storage - \(user.displayName)"
                        )
                        return
                    }
                }
            }

            // If no valid stored auth, validate with server
            let isValid = await unifiedAuthManager.validateToken()

            if isValid, let user = unifiedAuthManager.currentUser {
                authenticationState = .authenticated
                currentUser = user
                print("🔐 AuthenticationStateManager: User is authenticated - \(user.displayName)")
                AuthenticationDebugger.shared.log(
                    component: "AuthenticationStateManager",
                    event: "Initial Auth Check Complete",
                    details: "Authenticated - \(user.displayName)"
                )
            } else {
                authenticationState = .unauthenticated
                currentUser = nil
                print("🔐 AuthenticationStateManager: User is not authenticated")
                AuthenticationDebugger.shared.log(
                    component: "AuthenticationStateManager",
                    event: "Initial Auth Check Complete",
                    details: "Not authenticated"
                )
            }
        }
    }
    
    // MARK: - Authentication Actions
    func login(username: String, password: String) async throws {
        print("🔐 AuthenticationStateManager: Starting login process")
        AuthenticationDebugger.shared.log(
            component: "AuthenticationStateManager",
            event: "Login Started",
            details: "Username: \(username)"
        )
        isLoading = true
        errorMessage = nil
        authenticationState = .checking
        
        do {
            let user = try await unifiedAuthManager.loginWithLMS(username: username, password: password)
            
            // Ensure token is properly saved before updating state
            try await Task.sleep(nanoseconds: 200_000_000) // 0.2 second grace period
            
            authenticationState = .authenticated
            currentUser = user
            isLoading = false
            
            print("🔐 AuthenticationStateManager: Login successful - \(user.displayName)")
            
        } catch {
            authenticationState = .error(error.localizedDescription)
            errorMessage = error.localizedDescription
            isLoading = false
            
            print("🔐 AuthenticationStateManager: Login failed - \(error)")
            throw error
        }
    }
    
    func logout() async {
        print("🔐 AuthenticationStateManager: Starting logout process")
        isLoading = true
        
        await unifiedAuthManager.logout()
        
        authenticationState = .unauthenticated
        currentUser = nil
        errorMessage = nil
        isLoading = false
        
        print("🔐 AuthenticationStateManager: Logout completed")
    }
    
    // MARK: - Private Methods
    private func handleAuthStateChange(isAuthenticated: Bool, user: User?) {
        print("🔐 AuthenticationStateManager: Auth state changed - isAuth: \(isAuthenticated), user: \(user?.displayName ?? "nil")")
        
        if isAuthenticated, let user = user {
            if case .authenticated = authenticationState {
                // Already authenticated, no change needed
            } else {
                authenticationState = .authenticated
                currentUser = user
            }
        } else {
            if case .unauthenticated = authenticationState {
                // Already unauthenticated, no change needed
            } else {
                authenticationState = .unauthenticated
                currentUser = nil
            }
        }
    }
    
    // MARK: - Utility Methods
    func waitForAuthenticationCompletion() async {
        // Wait until authentication state is determined
        while case .checking = authenticationState {
            try? await Task.sleep(nanoseconds: 50_000_000) // 0.05 second
        }
    }
    
    func isReadyForAPICall() -> Bool {
        return authenticationState.isAuthenticated && 
               currentUser != nil && 
               tokenManager.isTokenValid
    }
}
