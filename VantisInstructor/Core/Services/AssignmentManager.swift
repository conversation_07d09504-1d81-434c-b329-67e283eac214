//
//  AssignmentManager.swift
//  mobile-app-template
//
//  Created by Instructor App on 23/7/25.
//

import Foundation
import SwiftUI

@MainActor
class AssignmentManager: ObservableObject {
    // MARK: - Published Properties
    @Published var assignments: [Assignment] = []
    @Published var upcomingAssignments: [Assignment] = []
    @Published var overdueAssignments: [Assignment] = []
    @Published var pendingGrading: [Assignment] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // Selected assignment for detail view
    @Published var selectedAssignment: Assignment?
    
    // MARK: - Initialization
    init() {
        loadAssignments()
    }
    
    // MARK: - Data Loading
    func loadAssignments() {
        Task {
            await fetchAssignments()
        }
    }
    
    func fetchAssignments() async {
        isLoading = true
        errorMessage = nil
        
        do {
            // Simulate API call
            try await Task.sleep(nanoseconds: 800_000_000) // 0.8 seconds
            
            // Load mock data
            assignments = Assignment.mockAssignments
            
            // Filter assignments
            filterAssignments()
            
        } catch {
            errorMessage = "Không thể tải danh sách bài tập: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    private func filterAssignments() {
        let now = Date()
        let nextWeek = Calendar.current.date(byAdding: .day, value: 7, to: now) ?? now
        
        // Upcoming assignments (due within next 7 days)
        upcomingAssignments = assignments.filter { assignment in
            assignment.dueDate > now && assignment.dueDate <= nextWeek && assignment.status == .active
        }.sorted { $0.dueDate < $1.dueDate }
        
        // Overdue assignments
        overdueAssignments = assignments.filter { assignment in
            assignment.isOverdue && assignment.status == .active
        }.sorted { $0.dueDate < $1.dueDate }
        
        // Assignments pending grading
        pendingGrading = assignments.filter { assignment in
            assignment.gradedSubmissions < assignment.totalSubmissions && assignment.status == .active
        }.sorted { $0.dueDate < $1.dueDate }
    }
    
    // MARK: - Assignment Operations
    func createAssignment(_ assignmentData: CreateAssignmentData) async -> Bool {
        isLoading = true
        
        do {
            // Simulate API call
            try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
            
            let newAssignment = Assignment(
                id: UUID().uuidString,
                courseId: assignmentData.courseId,
                courseName: assignmentData.courseName,
                courseCode: assignmentData.courseCode,
                classId: assignmentData.classId,
                title: assignmentData.title,
                description: assignmentData.description,
                instructions: assignmentData.instructions,
                type: assignmentData.type,
                status: .published,
                maxScore: assignmentData.maxScore,
                weight: assignmentData.weight,
                difficulty: assignmentData.difficulty,
                estimatedHours: assignmentData.estimatedHours,
                assignedDate: Date(),
                dueDate: assignmentData.dueDate,
                submissionMethod: assignmentData.submissionMethod,
                allowLateSubmission: assignmentData.allowLateSubmission,
                latePenalty: assignmentData.latePenalty,
                maxLateDays: assignmentData.maxLateDays,
                rubric: assignmentData.rubric,
                attachments: assignmentData.attachments,
                tags: assignmentData.tags,
                totalSubmissions: 0,
                gradedSubmissions: 0,
                averageScore: nil,
                instructorId: "instructor_1",
                instructorName: "Nguyễn Văn A",
                metadata: nil,
                createdAt: Date(),
                updatedAt: nil
            )
            
            assignments.append(newAssignment)
            filterAssignments()
            
            isLoading = false
            return true
            
        } catch {
            errorMessage = "Không thể tạo bài tập: \(error.localizedDescription)"
            isLoading = false
            return false
        }
    }
    
    func updateAssignment(_ assignment: Assignment) async -> Bool {
        isLoading = true
        
        do {
            // Simulate API call
            try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
            
            if let index = assignments.firstIndex(where: { $0.id == assignment.id }) {
                assignments[index] = assignment
                filterAssignments()
            }
            
            isLoading = false
            return true
            
        } catch {
            errorMessage = "Không thể cập nhật bài tập: \(error.localizedDescription)"
            isLoading = false
            return false
        }
    }
    
    func deleteAssignment(_ assignmentId: String) async -> Bool {
        isLoading = true
        
        do {
            // Simulate API call
            try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
            
            assignments.removeAll { $0.id == assignmentId }
            filterAssignments()
            
            isLoading = false
            return true
            
        } catch {
            errorMessage = "Không thể xóa bài tập: \(error.localizedDescription)"
            isLoading = false
            return false
        }
    }
    
    func publishAssignment(_ assignmentId: String) async -> Bool {
        guard let index = assignments.firstIndex(where: { $0.id == assignmentId }) else {
            return false
        }
        
        var updatedAssignment = assignments[index]
        // Create new assignment with updated status
        updatedAssignment = Assignment(
            id: updatedAssignment.id,
            courseId: updatedAssignment.courseId,
            courseName: updatedAssignment.courseName,
            courseCode: updatedAssignment.courseCode,
            classId: updatedAssignment.classId,
            title: updatedAssignment.title,
            description: updatedAssignment.description,
            instructions: updatedAssignment.instructions,
            type: updatedAssignment.type,
            status: .published,
            maxScore: updatedAssignment.maxScore,
            weight: updatedAssignment.weight,
            difficulty: updatedAssignment.difficulty,
            estimatedHours: updatedAssignment.estimatedHours,
            assignedDate: updatedAssignment.assignedDate,
            dueDate: updatedAssignment.dueDate,
            submissionMethod: updatedAssignment.submissionMethod,
            allowLateSubmission: updatedAssignment.allowLateSubmission,
            latePenalty: updatedAssignment.latePenalty,
            maxLateDays: updatedAssignment.maxLateDays,
            rubric: updatedAssignment.rubric,
            attachments: updatedAssignment.attachments,
            tags: updatedAssignment.tags,
            totalSubmissions: updatedAssignment.totalSubmissions,
            gradedSubmissions: updatedAssignment.gradedSubmissions,
            averageScore: updatedAssignment.averageScore,
            instructorId: updatedAssignment.instructorId,
            instructorName: updatedAssignment.instructorName,
            metadata: updatedAssignment.metadata,
            createdAt: updatedAssignment.createdAt,
            updatedAt: Date()
        )
        
        return await updateAssignment(updatedAssignment)
    }
    
    func gradeSubmission(_ assignmentId: String, studentId: String, score: Double, feedback: String?) async -> Bool {
        // Simulate grading process
        isLoading = true
        
        do {
            // Simulate API call
            try await Task.sleep(nanoseconds: 800_000_000) // 0.8 seconds
            
            // Update assignment graded submissions count
            if let index = assignments.firstIndex(where: { $0.id == assignmentId }) {
                var updatedAssignment = assignments[index]
                
                // Increment graded submissions
                let newGradedCount = updatedAssignment.gradedSubmissions + 1
                
                // Recalculate average score (simplified)
                let currentTotal = (updatedAssignment.averageScore ?? 0) * Double(updatedAssignment.gradedSubmissions)
                let newAverage = (currentTotal + score) / Double(newGradedCount)
                
                updatedAssignment = Assignment(
                    id: updatedAssignment.id,
                    courseId: updatedAssignment.courseId,
                    courseName: updatedAssignment.courseName,
                    courseCode: updatedAssignment.courseCode,
                    classId: updatedAssignment.classId,
                    title: updatedAssignment.title,
                    description: updatedAssignment.description,
                    instructions: updatedAssignment.instructions,
                    type: updatedAssignment.type,
                    status: updatedAssignment.status,
                    maxScore: updatedAssignment.maxScore,
                    weight: updatedAssignment.weight,
                    difficulty: updatedAssignment.difficulty,
                    estimatedHours: updatedAssignment.estimatedHours,
                    assignedDate: updatedAssignment.assignedDate,
                    dueDate: updatedAssignment.dueDate,
                    submissionMethod: updatedAssignment.submissionMethod,
                    allowLateSubmission: updatedAssignment.allowLateSubmission,
                    latePenalty: updatedAssignment.latePenalty,
                    maxLateDays: updatedAssignment.maxLateDays,
                    rubric: updatedAssignment.rubric,
                    attachments: updatedAssignment.attachments,
                    tags: updatedAssignment.tags,
                    totalSubmissions: updatedAssignment.totalSubmissions,
                    gradedSubmissions: newGradedCount,
                    averageScore: newAverage,
                    instructorId: updatedAssignment.instructorId,
                    instructorName: updatedAssignment.instructorName,
                    metadata: updatedAssignment.metadata,
                    createdAt: updatedAssignment.createdAt,
                    updatedAt: Date()
                )
                
                assignments[index] = updatedAssignment
                filterAssignments()
            }
            
            isLoading = false
            return true
            
        } catch {
            errorMessage = "Không thể chấm điểm: \(error.localizedDescription)"
            isLoading = false
            return false
        }
    }
    
    // MARK: - Utility Methods
    func getAssignmentsForCourse(_ courseId: String) -> [Assignment] {
        return assignments.filter { $0.courseId == courseId }
            .sorted { $0.dueDate < $1.dueDate }
    }
    
    func getAssignmentsByType(_ type: AssignmentType) -> [Assignment] {
        return assignments.filter { $0.type == type }
            .sorted { $0.dueDate < $1.dueDate }
    }
    
    func refreshData() async {
        await fetchAssignments()
    }
}

// MARK: - Create Assignment Data
struct CreateAssignmentData {
    let courseId: String
    let courseName: String
    let courseCode: String
    let classId: String?
    let title: String
    let description: String
    let instructions: String?
    let type: AssignmentType
    let maxScore: Double
    let weight: Double
    let difficulty: DifficultyLevel
    let estimatedHours: Int?
    let dueDate: Date
    let submissionMethod: SubmissionMethod
    let allowLateSubmission: Bool
    let latePenalty: Double?
    let maxLateDays: Int?
    let rubric: AssignmentRubric?
    let attachments: [AssignmentAttachment]?
    let tags: [String]?
}

// MARK: - Extensions
extension AssignmentManager {
    var hasUpcomingAssignments: Bool {
        !upcomingAssignments.isEmpty
    }
    
    var hasOverdueAssignments: Bool {
        !overdueAssignments.isEmpty
    }
    
    var hasPendingGrading: Bool {
        !pendingGrading.isEmpty
    }
    
    var urgentAssignments: [Assignment] {
        assignments.filter { $0.isDueSoon || $0.isOverdue }
    }
    
    var totalPendingGrades: Int {
        pendingGrading.reduce(0) { sum, assignment in
            sum + (assignment.totalSubmissions - assignment.gradedSubmissions)
        }
    }
    
    var averageGradingProgress: Double {
        guard !assignments.isEmpty else { return 0 }
        
        let totalProgress = assignments.reduce(0.0) { sum, assignment in
            sum + assignment.submissionRate
        }
        
        return totalProgress / Double(assignments.count)
    }
}
