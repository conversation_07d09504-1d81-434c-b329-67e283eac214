//
//  AttendanceManager.swift
//  mobile-app-template
//
//  Created by Instructor App on 23/7/25.
//

import Foundation
import SwiftUI

@MainActor
class AttendanceManager: ObservableObject {
    // MARK: - Published Properties
    @Published var attendanceRecords: [Attendance] = []
    @Published var classSummaries: [ClassAttendanceSummary] = []
    @Published var studentSummaries: [StudentAttendanceSummary] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // Current attendance session
    @Published var currentClassId: String?
    @Published var isMarkingAttendance = false
    
    // MARK: - Initialization
    init() {
        loadAttendanceData()
    }
    
    // MARK: - Data Loading
    func loadAttendanceData() {
        Task {
            await fetchAttendanceRecords()
            await fetchClassSummaries()
            await fetchStudentSummaries()
        }
    }
    
    func fetchAttendanceRecords() async {
        isLoading = true
        errorMessage = nil
        
        do {
            // Simulate API call
            try await Task.sleep(nanoseconds: 600_000_000) // 0.6 seconds
            
            // Load mock data
            attendanceRecords = Attendance.mockAttendanceRecords
            
        } catch {
            errorMessage = "Không thể tải dữ liệu điểm danh: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    func fetchClassSummaries() async {
        do {
            // Simulate API call
            try await Task.sleep(nanoseconds: 400_000_000) // 0.4 seconds
            
            // Create mock class summaries
            classSummaries = [
                ClassAttendanceSummary(
                    id: "summary_1",
                    classId: "class_1",
                    courseId: "course_1",
                    courseName: "Lập trình iOS với Swift",
                    courseCode: "CS301",
                    classDate: Date().addingTimeInterval(-24 * 3600), // Yesterday
                    classTitle: "SwiftUI Navigation",
                    totalStudents: 35,
                    presentStudents: 32,
                    absentStudents: 2,
                    lateStudents: 1,
                    excusedStudents: 0,
                    attendanceRate: 91.4,
                    attendanceRecords: attendanceRecords,
                    createdAt: Date().addingTimeInterval(-24 * 3600),
                    updatedAt: Date()
                )
            ]
            
        } catch {
            errorMessage = "Không thể tải tóm tắt điểm danh: \(error.localizedDescription)"
        }
    }
    
    func fetchStudentSummaries() async {
        do {
            // Simulate API call
            try await Task.sleep(nanoseconds: 300_000_000) // 0.3 seconds
            
            // Create mock student summaries
            studentSummaries = [
                StudentAttendanceSummary(
                    id: "student_summary_1",
                    studentId: "student_1",
                    studentName: "Nguyễn Văn B",
                    studentNumber: "2024001",
                    courseId: "course_1",
                    courseName: "Lập trình iOS với Swift",
                    courseCode: "CS301",
                    totalClasses: 15,
                    attendedClasses: 14,
                    absentClasses: 1,
                    lateClasses: 0,
                    excusedClasses: 0,
                    attendanceRate: 93.3,
                    attendanceRecords: attendanceRecords,
                    lastAttendance: Date().addingTimeInterval(-24 * 3600),
                    createdAt: Date().addingTimeInterval(-30 * 24 * 3600),
                    updatedAt: Date()
                )
            ]
            
        } catch {
            errorMessage = "Không thể tải tóm tắt học sinh: \(error.localizedDescription)"
        }
    }
    
    // MARK: - Attendance Operations
    func startAttendanceSession(for classId: String) async -> Bool {
        isMarkingAttendance = true
        currentClassId = classId
        
        do {
            // Simulate API call to start session
            try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
            
            return true
            
        } catch {
            errorMessage = "Không thể bắt đầu phiên điểm danh: \(error.localizedDescription)"
            isMarkingAttendance = false
            currentClassId = nil
            return false
        }
    }
    
    func markAttendance(
        classId: String,
        studentId: String,
        status: AttendanceStatus,
        notes: String? = nil
    ) async -> Bool {
        do {
            // Simulate API call
            try await Task.sleep(nanoseconds: 300_000_000) // 0.3 seconds
            
            let newAttendance = Attendance(
                id: UUID().uuidString,
                classId: classId,
                courseId: "course_1", // Would be fetched from class
                courseName: "Lập trình iOS với Swift",
                courseCode: "CS301",
                studentId: studentId,
                studentName: "Student Name", // Would be fetched from student
                studentNumber: "2024XXX",
                status: status,
                checkInTime: status == .present || status == .late ? Date() : nil,
                checkOutTime: nil,
                notes: notes,
                location: "Tòa A - A301",
                deviceInfo: "iPhone",
                ipAddress: "*************",
                isExcused: status == .excused,
                excuseReason: status == .excused ? notes : nil,
                excuseDocument: nil,
                markedBy: "instructor_1",
                markedAt: Date(),
                lastModifiedBy: nil,
                lastModifiedAt: nil,
                metadata: nil,
                createdAt: Date(),
                updatedAt: nil
            )
            
            attendanceRecords.append(newAttendance)
            
            return true
            
        } catch {
            errorMessage = "Không thể điểm danh: \(error.localizedDescription)"
            return false
        }
    }
    
    func bulkMarkAttendance(
        classId: String,
        attendanceData: [(studentId: String, status: AttendanceStatus)]
    ) async -> Bool {
        isLoading = true
        
        do {
            // Simulate API call
            try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
            
            for data in attendanceData {
                _ = await markAttendance(
                    classId: classId,
                    studentId: data.studentId,
                    status: data.status
                )
            }
            
            isLoading = false
            return true
            
        } catch {
            errorMessage = "Không thể điểm danh hàng loạt: \(error.localizedDescription)"
            isLoading = false
            return false
        }
    }
    
    func endAttendanceSession() async -> Bool {
        do {
            // Simulate API call to end session
            try await Task.sleep(nanoseconds: 300_000_000) // 0.3 seconds
            
            isMarkingAttendance = false
            currentClassId = nil
            
            // Refresh summaries
            await fetchClassSummaries()
            await fetchStudentSummaries()
            
            return true
            
        } catch {
            errorMessage = "Không thể kết thúc phiên điểm danh: \(error.localizedDescription)"
            return false
        }
    }
    
    // MARK: - Utility Methods
    func getAttendanceForClass(_ classId: String) -> [Attendance] {
        return attendanceRecords.filter { $0.classId == classId }
    }
    
    func getAttendanceForStudent(_ studentId: String) -> [Attendance] {
        return attendanceRecords.filter { $0.studentId == studentId }
    }
    
    func getAttendanceRate(for classId: String) -> Double {
        let classAttendance = getAttendanceForClass(classId)
        guard !classAttendance.isEmpty else { return 0 }
        
        let presentCount = classAttendance.filter { $0.isPresent }.count
        return Double(presentCount) / Double(classAttendance.count) * 100
    }
    
    func getStudentAttendanceRate(_ studentId: String, courseId: String) -> Double {
        let studentAttendance = attendanceRecords.filter { 
            $0.studentId == studentId && $0.courseId == courseId 
        }
        guard !studentAttendance.isEmpty else { return 0 }
        
        let presentCount = studentAttendance.filter { $0.isPresent }.count
        return Double(presentCount) / Double(studentAttendance.count) * 100
    }
    
    func refreshData() async {
        await loadAttendanceData()
    }
}

// MARK: - Extensions
extension AttendanceManager {
    var recentAttendance: [ClassAttendanceSummary] {
        classSummaries
            .sorted { $0.classDate > $1.classDate }
            .prefix(5)
            .map { $0 }
    }
    
    var averageAttendanceRate: Double {
        guard !classSummaries.isEmpty else { return 0 }
        
        let totalRate = classSummaries.reduce(0.0) { sum, summary in
            sum + summary.attendanceRate
        }
        
        return totalRate / Double(classSummaries.count)
    }
    
    var todayAttendanceRate: Double {
        let today = Date()
        let todaySummaries = classSummaries.filter { 
            Calendar.current.isDate($0.classDate, inSameDayAs: today)
        }
        
        guard !todaySummaries.isEmpty else { return 0 }
        
        let totalRate = todaySummaries.reduce(0.0) { sum, summary in
            sum + summary.attendanceRate
        }
        
        return totalRate / Double(todaySummaries.count)
    }
}
