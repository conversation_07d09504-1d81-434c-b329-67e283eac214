//
//  LessonService.swift
//  mobile-app-template
//
//  Created by Inst<PERSON><PERSON> App on 28/7/25.
//

import Foundation

// MARK: - Lesson Service Protocol
protocol LessonServiceProtocol {
    func fetchLessons(page: Int, pageSize: Int, state: String?, classId: Int?, dateFrom: String?, dateTo: String?) async throws -> APILessonListResponse
    func fetchLessonById(_ id: Int) async throws -> APILesson
}

// MARK: - Lesson Service Implementation
class LessonService: LessonServiceProtocol {
    private let apiClient: APIClient
    private let tokenManager = TokenManager.shared
    private let baseURL = "https://lms-dev.ebill.vn/api/v1"

    init(apiClient: APIClient = APIClient.shared) {
        self.apiClient = apiClient
    }

    // MARK: - Fetch Lessons
    func fetchLessons(page: Int = 1, pageSize: Int = 20, state: String? = nil, classId: Int? = nil, dateFrom: String? = nil, dateTo: String? = nil) async throws -> APILessonListResponse {
        let endpoint = "/instructors/lessons"

        print("🔥 LessonService.fetchLessons() called!")
        print("📍 Endpoint: \(baseURL)\(endpoint)")
        print("📊 Parameters: page=\(page), pageSize=\(pageSize), state=\(state ?? "nil")")

        // Build query parameters
        var parameters: [String: Any] = [
            "page": page,
            "page_size": pageSize
        ]

        if let state = state {
            parameters["state"] = state
        }
        if let classId = classId {
            parameters["class_id"] = classId
        }
        if let dateFrom = dateFrom {
            parameters["date_from"] = dateFrom
        }
        if let dateTo = dateTo {
            parameters["date_to"] = dateTo
        }

        // Check if we have a valid token
        let isTokenValid = await MainActor.run { tokenManager.isTokenValid }
        guard isTokenValid else {
            print("❌ No valid token available")
            throw NetworkError.unauthorized
        }

        // Add authorization header
        var headers: [String: String] = [:]
        let authHeader = await MainActor.run { tokenManager.authorizationHeader }
        if let authHeader = authHeader {
            headers["Authorization"] = authHeader
            print("🔑 Using auth header: \(authHeader.prefix(20))...")
        }

        do {
            let response: APILessonListResponse = try await apiClient.request(
                endpoint: endpoint,
                method: .GET,
                parameters: parameters,
                headers: headers,
                responseType: APILessonListResponse.self,
                requiresAuth: true
            )
            print("✅ API call successful: \(response.data.count) lessons")
            return response
        } catch {
            print("❌ LessonService.fetchLessons error: \(error)")

            // If unauthorized, clear token and throw error
            if case NetworkError.unauthorized = error {
                print("⚠️ API Unauthorized - clearing token")
                await MainActor.run {
                    tokenManager.clearToken()
                }
                throw NetworkError.unauthorized
            }

            throw error
        }
    }
    
    // MARK: - Fetch Lesson by ID
    func fetchLessonById(_ id: Int) async throws -> APILesson {
        let endpoint = "/instructors/lessons/\(id)"

        do {
            let lesson: APILesson = try await apiClient.request(
                endpoint: endpoint,
                method: .GET,
                parameters: nil,
                headers: nil,
                responseType: APILesson.self,
                requiresAuth: true
            )
            return lesson
        } catch {
            print("❌ LessonService.fetchLessonById error: \(error)")
            throw error
        }
    }
}

// MARK: - Mock Lesson Service (for testing)
class MockLessonService: LessonServiceProtocol {
    private var shouldFail = false
    private var delay: TimeInterval = 1.0
    
    init(shouldFail: Bool = false, delay: TimeInterval = 1.0) {
        self.shouldFail = shouldFail
        self.delay = delay
    }
    
    func fetchLessons(page: Int = 1, pageSize: Int = 20, state: String? = nil, classId: Int? = nil, dateFrom: String? = nil, dateTo: String? = nil) async throws -> APILessonListResponse {
        // Simulate network delay
        try await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
        
        if shouldFail {
            throw NetworkError.serverError(500, "Mock error")
        }
        
        // Return mock data
        let mockLessons = APILesson.mockAPILessons
        let totalCount = mockLessons.count
        let totalPages = (totalCount + pageSize - 1) / pageSize
        
        // Simulate pagination
        let startIndex = (page - 1) * pageSize
        let endIndex = min(startIndex + pageSize, totalCount)
        let paginatedLessons = Array(mockLessons[startIndex..<endIndex])
        
        return APILessonListResponse(
            success: true,
            message: "Lessons fetched successfully",
            data: paginatedLessons,
            pagination: APIPaginationMetadata(
                page: page,
                pageSize: pageSize,
                totalItems: totalCount,
                totalPages: totalPages
            )
        )
    }
    
    func fetchLessonById(_ id: Int) async throws -> APILesson {
        // Simulate network delay
        try await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
        
        if shouldFail {
            throw NetworkError.serverError(500, "Mock error")
        }
        
        // Find lesson by ID
        guard let lesson = APILesson.mockAPILessons.first(where: { $0.id == id }) else {
            throw NetworkError.notFound
        }
        
        return lesson
    }
}

// MARK: - Network Error Extension
extension NetworkError {
    static let notFound = NetworkError.serverError(404, "Lesson not found")
}

// MARK: - Lesson Service Factory
class LessonServiceFactory {
    static func create(useMockData: Bool = false) -> LessonServiceProtocol {
        if useMockData {
            return MockLessonService()
        } else {
            return LessonService()
        }
    }
}
