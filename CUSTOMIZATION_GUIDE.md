# Customization Guide

This guide provides step-by-step instructions for customizing the Mobile App Template for your specific application needs.

## 🎯 Quick Start Checklist

- [ ] Update app name and bundle identifier
- [ ] Configure API endpoints and base URL
- [ ] Customize colors and branding
- [ ] Update user roles and permissions
- [ ] Configure authentication flow
- [ ] Customize tab navigation
- [ ] Update data models
- [ ] Configure analytics and tracking

## 📝 Step-by-Step Customization

### 1. Basic App Configuration

#### Update App Constants
File: `Core/Utils/AppConstants.swift`

```swift
struct AppConstants {
    // 1. Update API Configuration
    struct API {
        static let baseURL = "https://api.yourapp.com/v1"  // ← Change this
        // Keep timeout and retry settings or adjust as needed
    }
    
    // 2. Update App Information
    struct AppInfo {
        static let name = "YourApp"  // ← Your app name
        static let bundleId = "com.yourapp.mobile"  // ← Your bundle ID
    }
    
    // 3. Update Contact Information
    struct Contact {
        static let supportEmail = "<EMAIL>"  // ← Your support email
        static let supportPhone = "+1234567890"  // ← Your phone
        static let feedbackEmail = "<EMAIL>"
        static let businessEmail = "<EMAIL>"
    }
    
    // 4. Update URLs
    struct URLs {
        static let website = "https://yourapp.com"
        static let support = "https://support.yourapp.com"
        static let termsOfService = "https://yourapp.com/terms"
        static let privacyPolicy = "https://yourapp.com/privacy"
        static let appStore = "https://apps.apple.com/app/yourapp"
    }
}
```

#### Update Environment URLs
```swift
enum Environment {
    // Update API URLs for each environment
    var apiBaseURL: String {
        switch self {
        case .development:
            return "https://api-dev.yourapp.com/api"
        case .staging:
            return "https://api-staging.yourapp.com/api"
        case .production:
            return "https://api.yourapp.com/api"
        }
    }
}
```

### 2. Xcode Project Configuration

#### Update Bundle Identifier
1. Open `VantisInstructor.xcodeproj`
2. Select the project in navigator
3. Go to **Build Settings**
4. Search for "Product Bundle Identifier"
5. Update to your bundle ID (e.g., `com.yourapp.mobile`)

#### Update App Display Name
1. In **Build Settings**, search for "Bundle Display Name"
2. Update to your app's display name
3. Or update in Info.plist: `CFBundleDisplayName`

#### Update App Icon
1. Replace icons in `Assets.xcassets/AppIcon.appiconset/`
2. Use Xcode's asset catalog to manage different sizes
3. Ensure you have all required icon sizes

### 3. Customize Colors and Branding

#### Update Color Scheme
File: `Core/Utils/AppConstants.swift`

```swift
struct Colors {
    // Primary brand colors
    static let primary = Color(red: 0.2, green: 0.6, blue: 1.0)  // ← Your primary color
    static let primaryDark = Color(red: 0.1, green: 0.4, blue: 0.8)
    static let primaryLight = Color(red: 0.6, green: 0.8, blue: 1.0)
    
    // Secondary colors
    static let secondary = Color(red: 1.0, green: 0.6, blue: 0.2)  // ← Your secondary color
    static let accent = Color(red: 0.8, green: 0.2, blue: 0.8)
    
    // Status colors (customize if needed)
    static let success = Color(red: 0.2, green: 0.8, blue: 0.4)
    static let warning = Color(red: 1.0, green: 0.8, blue: 0.0)
    static let error = Color(red: 1.0, green: 0.3, blue: 0.3)
}
```

### 4. Configure User Roles and Permissions

#### Update User Roles
File: `Domain/Models/User.swift`

```swift
enum UserRole: String, Codable, CaseIterable {
    case user = "USER"
    case business = "BUSINESS"  // Rename or add roles as needed
    case admin = "ADMIN"
    // Add custom roles:
    // case moderator = "MODERATOR"
    // case premium = "PREMIUM"
    
    var displayName: String {
        switch self {
        case .user:
            return "User"
        case .business:
            return "Business"  // Update display names
        case .admin:
            return "Admin"
        }
    }
}
```

#### Update Role-Based Navigation
File: `Presentation/Common/Views/RoleBasedTabView.swift`

```swift
// Update tab items for each role
private var tabs: [TabItem] {
    [
        TabItem(id: 0, title: "Dashboard", icon: "chart.bar.xaxis", selectedIcon: "chart.bar.fill"),
        TabItem(id: 1, title: "Items", icon: "star.circle", selectedIcon: "star.circle.fill"),
        // Add/remove/modify tabs as needed
    ]
}
```

### 5. Customize Data Models

#### Update User Model
File: `Domain/Models/User.swift`

```swift
struct User: Codable, Identifiable {
    let id: String
    let email: String
    let firstName: String?
    let lastName: String?
    let phone: String?
    let walletAddress: String?  // Remove if not needed
    let role: UserRole
    let isActive: Bool
    let avatar: String?
    
    // Add custom fields:
    // let subscriptionType: String?
    // let preferences: UserPreferences?
    // let metadata: [String: String]?
}
```

#### Update Transaction Model
File: `Domain/Models/Transaction.swift`

```swift
struct Transaction: Codable, Identifiable {
    let id: String
    let userId: String?
    let type: TransactionType
    let amountUsd: Double?  // Change currency as needed
    let amountTokens: Double  // Rename or remove if not applicable
    
    // Customize transaction fields:
    // let productId: String?
    // let orderId: String?
    // let paymentMethod: String?
}

enum TransactionType: String, Codable, CaseIterable {
    case purchase = "PURCHASE"  // Customize transaction types
    case refund = "REFUND"
    case transferIn = "TRANSFER_IN"
    case transferOut = "TRANSFER_OUT"
    // Add custom types:
    // case subscription = "SUBSCRIPTION"
    // case reward = "REWARD"
}
```

### 6. Configure API Endpoints

#### Update API Endpoints
File: `Core/Network/APIEndpoints.swift`

```swift
struct APIEndpoints {
    // Authentication
    static let login = "/auth/login"
    static let register = "/auth/register"
    static let refresh = "/auth/refresh"
    
    // User management
    static let users = "/users"
    static let profile = "/users/profile"
    
    // Add your specific endpoints:
    // static let products = "/products"
    // static let orders = "/orders"
    // static let subscriptions = "/subscriptions"
}
```

### 7. Customize Authentication Flow

#### Update Authentication Messages
File: `Core/Utils/AppConstants.swift`

```swift
struct Biometric {
    static let reason = "Authenticate to access your account"  // ← Customize message
    static let fallbackTitle = "Use Passcode"
    static let cancelTitle = "Cancel"
}
```

#### Update Keychain Service
```swift
struct KeychainKeys {
    static let service = "com.yourapp.mobile"  // ← Match your bundle ID
    static let accessToken = "access_token"
    static let refreshToken = "refresh_token"
    // Add custom keys as needed
}
```

### 8. Configure Analytics and Tracking

#### Update Analytics Events
File: `Core/Services/AnalyticsService.swift`

Add custom event tracking methods:

```swift
// Add custom tracking methods
func trackPurchase(productId: String, amount: Double) {
    track(event: "purchase", properties: [
        "product_id": productId,
        "amount": amount
    ])
}

func trackUserAction(action: String, screen: String) {
    track(event: "user_action", properties: [
        "action": action,
        "screen": screen
    ])
}
```

### 9. Update Feature Flags

#### Configure Feature Flags
File: `Core/Utils/AppConstants.swift`

```swift
struct FeatureFlags {
    static let biometricLoginEnabled = true
    static let socialLoginEnabled = false  // Enable if needed
    static let darkModeEnabled = true
    static let analyticsEnabled = true
    
    // Add custom feature flags:
    // static let premiumFeaturesEnabled = true
    // static let betaFeaturesEnabled = false
    // static let pushNotificationsEnabled = true
}
```

### 10. Customize Notification Types

#### Update Notification Types
File: `Core/Utils/AppConstants.swift`

```swift
enum NotificationType: String, CaseIterable, Codable {
    case itemUpdated = "item_updated"
    case orderCompleted = "order_completed"
    case transactionCompleted = "transaction_completed"
    case promotionalOffer = "promotional_offer"
    case systemUpdate = "system_update"
    case securityAlert = "security_alert"
    
    // Add custom notification types:
    // case subscriptionExpiring = "subscription_expiring"
    // case newMessage = "new_message"
    // case friendRequest = "friend_request"
}
```

## 🧪 Testing Your Customizations

1. **Build the project** to ensure no compilation errors
2. **Run on simulator** to test basic functionality
3. **Test authentication flow** with your API
4. **Verify role-based navigation** works correctly
5. **Test on real device** for biometric authentication
6. **Validate API integration** with your backend

## 📋 Final Checklist

- [ ] App builds without errors
- [ ] Authentication works with your API
- [ ] All tabs and navigation work correctly
- [ ] Colors and branding match your design
- [ ] User roles and permissions are correct
- [ ] Analytics tracking is configured
- [ ] Push notifications are set up (if needed)
- [ ] App icons and assets are updated
- [ ] Bundle identifier is correct
- [ ] App Store metadata is ready

## 🚨 Common Issues

### Build Errors
- Check bundle identifier matches in all places
- Ensure all model references are updated
- Verify API endpoint URLs are correct

### Authentication Issues
- Check API base URL configuration
- Verify keychain service name matches bundle ID
- Test with your actual API endpoints

### Navigation Issues
- Ensure all view references are updated
- Check role-based navigation logic
- Verify tab items match available views

---

Need help? Check the main README.md for additional resources and support information.
