# Instructor Grading API - Frontend Developer Guide

## 📋 Overview

This comprehensive guide provides frontend developers with everything needed to integrate instructor grading functionality. The system supports complete grading workflow from viewing pending assignments to providing detailed feedback and analytics.

## 🔐 Authentication

All endpoints require JWT authentication with instructor role.

```javascript
const API_BASE_URL = 'https://lms-dev.ebill.vn/api/v1/instructors/quizzes';
const authToken = 'your_jwt_token_here';

const headers = {
  'Authorization': `Bearer ${authToken}`,
  'Content-Type': 'application/json'
};
```

## 🎯 Complete Grading Workflow

### **Step 1: Get Pending Grading Items**

#### **Endpoint**: `GET /grading/pending`

**Purpose**: Get list of quiz attempts that need grading from instructor.

**Query Parameters**:
- `page` (integer, default: 1): Page number
- `limit` (integer, default: 50, max: 200): Items per page
- `quiz_id` (integer, optional): Filter by specific quiz

#### **Curl Example**:
```bash
curl -X 'GET' \
  'https://lms-dev.ebill.vn/api/v1/instructors/quizzes/grading/pending?page=1&limit=50&quiz_id=18' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN'
```

#### **Success Response (200)**:
```json
{
  "success": true,
  "message": "Tìm thấy 2 bài cần chấm điểm",
  "data": {
    "attempts": [
      {
        "id": 6,
        "student_id": 7,
        "student_name": "Lê Văn Trang",
        "student_code": "HV000007",
        "quiz_id": 18,
        "quiz_name": "Quiz Kiểm tra Lãnh đạo Kiến tạo",
        "quiz_code": "QUIZ00019",
        "submitted_at": "2025-07-23T16:18:50",
        "essay_count": 1,
        "pending_answers": 2,
        "time_spent": 45,
        "max_score": 35.0
      }
    ],
    "total_count": 2,
    "by_quiz": {
      "Quiz Kiểm tra Lãnh đạo Kiến tạo": 2
    }
  },
  "meta": {
    "timestamp": "2025-07-23 17:16"
  }
}
```

#### **React Native Example**:
```javascript
const getPendingGrading = async (page = 1, limit = 50, quizId = null) => {
  try {
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...(quizId && { quiz_id: quizId.toString() })
    });

    const response = await fetch(`${API_BASE_URL}/grading/pending?${queryParams}`, {
      method: 'GET',
      headers: headers
    });

    const result = await response.json();
    
    if (result.success) {
      return {
        success: true,
        attempts: result.data.attempts,
        totalCount: result.data.total_count,
        byQuiz: result.data.by_quiz
      };
    } else {
      throw new Error(result.error || 'Failed to get pending grading');
    }
  } catch (error) {
    console.error('Get pending grading error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Usage example
const loadPendingGrading = async () => {
  const result = await getPendingGrading(1, 50);
  
  if (result.success) {
    setPendingAttempts(result.attempts);
    setTotalCount(result.totalCount);
  } else {
    Alert.alert('Error', result.error);
  }
};
```

### **Step 2: Get Attempt Detail for Grading**

#### **Endpoint**: `GET /attempts/{attempt_id}`

**Purpose**: Get detailed information about a specific attempt including all answers for grading.

**Parameters**:
- `attempt_id` (path, required): ID of the attempt to grade

#### **Curl Example**:
```bash
curl -X 'GET' \
  'https://lms-dev.ebill.vn/api/v1/instructors/quizzes/attempts/6' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN'
```

#### **Success Response (200)**:
```json
{
  "success": true,
  "message": "Lấy chi tiết attempt thành công",
  "data": {
    "attempt": {
      "id": 6,
      "student_id": 7,
      "student_name": "Lê Văn Trang",
      "student_code": "HV000007",
      "attempt_number": 1,
      "start_time": "2025-07-23T15:18:50.872138",
      "end_time": "2025-07-23T16:18:50.872138",
      "time_spent": 45,
      "score": 0.0,
      "max_score": 35.0,
      "percentage": 0.0,
      "is_passed": false,
      "state": "completed",
      "needs_grading": true,
      "feedback": null
    },
    "answers": [
      {
        "id": 8,
        "question_id": 89,
        "question_name": "Tư duy lãnh đạo trong thời đại chuyển hóa có những đặc điểm gì?",
        "question_type": "essay",
        "question_score": 20.0,
        "answer_id": null,
        "answer_text": null,
        "text_answer": "Lãnh đạo trong thời đại chuyển hóa cần phải có khả năng thích ứng nhanh, tư duy sáng tạo và khả năng truyền cảm hứng cho đội ngũ.",
        "correct_answer_id": null,
        "correct_answer_text": null,
        "score": 0.0,
        "is_correct": false,
        "needs_grading": true,
        "instructor_feedback": null,
        "answered_at": "2025-07-23T15:18:50.872138"
      },
      {
        "id": 9,
        "question_id": 90,
        "question_name": "Lãnh đạo kiến tạo khác gì với lãnh đạo truyền thống?",
        "question_type": "single_choice",
        "question_score": 15.0,
        "answer_id": 49,
        "answer_text": "Tập trung vào việc duy trì hiện trạng",
        "text_answer": null,
        "correct_answer_id": 50,
        "correct_answer_text": "Tạo ra những thay đổi tích cực và bền vững",
        "score": 0.0,
        "is_correct": false,
        "needs_grading": true,
        "instructor_feedback": null,
        "answered_at": "2025-07-23T15:18:50.872138"
      }
    ],
    "quiz_info": {
      "id": 18,
      "name": "Quiz Kiểm tra Lãnh đạo Kiến tạo",
      "code": "QUIZ00019",
      "max_score": 35.0,
      "passing_score": 25.0,
      "show_correct_answers": false
    }
  },
  "meta": {
    "timestamp": "2025-07-23 17:20"
  }
}
```

#### **React Native Example**:
```javascript
const getAttemptDetail = async (attemptId) => {
  try {
    const response = await fetch(`${API_BASE_URL}/attempts/${attemptId}`, {
      method: 'GET',
      headers: headers
    });

    const result = await response.json();
    
    if (result.success) {
      return {
        success: true,
        attempt: result.data.attempt,
        answers: result.data.answers,
        quizInfo: result.data.quiz_info
      };
    } else {
      throw new Error(result.error || 'Failed to get attempt detail');
    }
  } catch (error) {
    console.error('Get attempt detail error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Usage example
const loadAttemptForGrading = async (attemptId) => {
  setLoading(true);
  const result = await getAttemptDetail(attemptId);
  
  if (result.success) {
    setAttempt(result.attempt);
    setAnswers(result.answers);
    setQuizInfo(result.quizInfo);
  } else {
    Alert.alert('Error', result.error);
  }
  setLoading(false);
};
```

### **Step 3: Grade Attempt**

#### **Endpoint**: `POST /attempts/{attempt_id}/grade`

**Purpose**: Grade an attempt by providing scores and feedback for individual answers.

**Parameters**:
- `attempt_id` (path, required): ID of the attempt to grade
- `attempt_id` (body, required): ID of the attempt (confirmation)
- `answers` (body, required): Array of answer grading objects
- `feedback` (body, optional): Overall feedback for the attempt

**Answer Grading Object**:
- `answer_id` (integer, required): ID of the answer to grade
- `score` (float, required): Score to assign (0 to question max score)
- `is_correct` (boolean, required): Whether the answer is correct
- `feedback` (string, optional): Specific feedback for this answer

#### **Curl Example**:
```bash
curl -X 'POST' \
  'https://lms-dev.ebill.vn/api/v1/instructors/quizzes/attempts/6/grade' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "attempt_id": 6,
    "answers": [
      {
        "answer_id": 8,
        "score": 12.0,
        "is_correct": false,
        "feedback": "Câu trả lời ngắn gọn nhưng thiếu chi tiết. Cần bổ sung thêm về tầm nhìn dài hạn và khả năng dự đoán xu hướng."
      },
      {
        "answer_id": 9,
        "score": 0.0,
        "is_correct": false,
        "feedback": "Đáp án không chính xác. Lãnh đạo kiến tạo tập trung vào tạo ra thay đổi tích cực, không phải duy trì hiện trạng."
      }
    ],
    "feedback": "Bài làm cần cải thiện. Hãy đọc thêm tài liệu về lãnh đạo kiến tạo."
  }'
```

#### **Success Response (200)**:
```json
{
  "success": true,
  "message": "Chấm điểm thành công",
  "data": {
    "attempt_id": 6,
    "final_score": 12.0,
    "percentage": 34.285714285714285,
    "is_passed": false,
    "state": "graded"
  },
  "meta": {
    "timestamp": "2025-07-23 17:20"
  }
}
```

#### **Error Responses**:
```json
// Attempt not found (404)
{
  "success": false,
  "error": "Không tìm thấy attempt"
}

// Permission denied (403)
{
  "success": false,
  "error": "Bạn không có quyền chấm điểm attempt này"
}

// Validation error (400)
{
  "success": false,
  "error": "Request validation error",
  "details": [
    {
      "field": "score",
      "message": "Score must be between 0 and question max score"
    }
  ]
}
```

#### **React Native Example**:
```javascript
const gradeAttempt = async (attemptId, answers, feedback = '') => {
  try {
    const response = await fetch(`${API_BASE_URL}/attempts/${attemptId}/grade`, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify({
        attempt_id: attemptId,
        answers: answers,
        feedback: feedback
      })
    });

    const result = await response.json();

    if (result.success) {
      return {
        success: true,
        gradedAttempt: result.data
      };
    } else {
      throw new Error(result.error || 'Failed to grade attempt');
    }
  } catch (error) {
    console.error('Grade attempt error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Usage example
const handleGradeSubmit = async () => {
  const gradingData = answers.map(answer => ({
    answer_id: answer.id,
    score: answer.gradedScore || 0,
    is_correct: answer.gradedCorrect || false,
    feedback: answer.instructorFeedback || ''
  }));

  const result = await gradeAttempt(
    attemptId,
    gradingData,
    overallFeedback
  );

  if (result.success) {
    Alert.alert(
      'Grading Complete',
      `Final Score: ${result.gradedAttempt.final_score}/${attempt.max_score} (${result.gradedAttempt.percentage.toFixed(1)}%)`,
      [
        {
          text: 'OK',
          onPress: () => navigation.goBack()
        }
      ]
    );
  } else {
    Alert.alert('Error', result.error);
  }
};
```

### **Step 4: Get Quiz Attempts**

#### **Endpoint**: `GET /quizzes/{quiz_id}/attempts`

**Purpose**: Get all attempts for a specific quiz with filtering options.

**Parameters**:
- `quiz_id` (path, required): ID of the quiz
- `page` (query, default: 1): Page number
- `limit` (query, default: 50, max: 200): Items per page
- `student_name` (query, optional): Filter by student name
- `state` (query, optional): Filter by attempt state
- `needs_grading` (query, optional): Filter attempts needing grading

#### **Curl Example**:
```bash
curl -X 'GET' \
  'https://lms-dev.ebill.vn/api/v1/instructors/quizzes/18/attempts?page=1&limit=50&state=graded&student_name=Đoàn' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN'
```

#### **React Native Example**:
```javascript
const getQuizAttempts = async (quizId, page = 1, limit = 50, filters = {}) => {
  try {
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...filters
    });

    const response = await fetch(`${API_BASE_URL}/quizzes/${quizId}/attempts?${queryParams}`, {
      method: 'GET',
      headers: headers
    });

    const result = await response.json();

    if (result.success) {
      return {
        success: true,
        attempts: result.data.attempts,
        totalCount: result.data.total_count,
        quizInfo: result.data.quiz_info
      };
    } else {
      throw new Error(result.error || 'Failed to get quiz attempts');
    }
  } catch (error) {
    console.error('Get quiz attempts error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
```

## 🎨 Complete React Native Grading Component

### **Grading Screen Implementation**

```javascript
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  StyleSheet
} from 'react-native';

const GradingScreen = ({ route, navigation }) => {
  const { attemptId } = route.params;
  const [attempt, setAttempt] = useState(null);
  const [answers, setAnswers] = useState([]);
  const [quizInfo, setQuizInfo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [grading, setGrading] = useState(false);
  const [overallFeedback, setOverallFeedback] = useState('');

  useEffect(() => {
    loadAttemptForGrading();
  }, []);

  const loadAttemptForGrading = async () => {
    setLoading(true);
    const result = await getAttemptDetail(attemptId);

    if (result.success) {
      setAttempt(result.attempt);
      setAnswers(result.answers.map(answer => ({
        ...answer,
        gradedScore: answer.score || 0,
        gradedCorrect: answer.is_correct || false,
        instructorFeedback: answer.instructor_feedback || ''
      })));
      setQuizInfo(result.quizInfo);
      setOverallFeedback(result.attempt.feedback || '');
    } else {
      Alert.alert('Error', result.error);
      navigation.goBack();
    }
    setLoading(false);
  };

  const updateAnswerGrading = (answerId, field, value) => {
    setAnswers(prev => prev.map(answer =>
      answer.id === answerId
        ? { ...answer, [field]: value }
        : answer
    ));
  };

  const handleSubmitGrading = async () => {
    setGrading(true);

    const gradingData = answers.map(answer => ({
      answer_id: answer.id,
      score: parseFloat(answer.gradedScore) || 0,
      is_correct: answer.gradedCorrect,
      feedback: answer.instructorFeedback
    }));

    const result = await gradeAttempt(attemptId, gradingData, overallFeedback);

    if (result.success) {
      Alert.alert(
        'Grading Complete',
        `Final Score: ${result.gradedAttempt.final_score}/${attempt.max_score} (${result.gradedAttempt.percentage.toFixed(1)}%)`,
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack()
          }
        ]
      );
    } else {
      Alert.alert('Error', result.error);
    }
    setGrading(false);
  };

  const renderAnswerGrading = (answer) => (
    <View key={answer.id} style={styles.answerContainer}>
      <View style={styles.questionHeader}>
        <Text style={styles.questionText}>{answer.question_name}</Text>
        <Text style={styles.questionType}>{answer.question_type}</Text>
        <Text style={styles.questionScore}>Max: {answer.question_score} pts</Text>
      </View>

      {/* Student Answer */}
      <View style={styles.studentAnswer}>
        <Text style={styles.sectionTitle}>Student Answer:</Text>
        {answer.question_type === 'essay' ? (
          <Text style={styles.essayAnswer}>{answer.text_answer}</Text>
        ) : (
          <View style={styles.choiceAnswer}>
            <Text style={styles.selectedAnswer}>
              Selected: {answer.answer_text}
            </Text>
            <Text style={styles.correctAnswer}>
              Correct: {answer.correct_answer_text}
            </Text>
          </View>
        )}
      </View>

      {/* Grading Section */}
      <View style={styles.gradingSection}>
        <View style={styles.scoreRow}>
          <Text style={styles.scoreLabel}>Score:</Text>
          <TextInput
            style={styles.scoreInput}
            value={answer.gradedScore.toString()}
            onChangeText={(text) => updateAnswerGrading(answer.id, 'gradedScore', text)}
            keyboardType="numeric"
            placeholder="0"
          />
          <Text style={styles.maxScore}>/ {answer.question_score}</Text>
        </View>

        <TextInput
          style={styles.feedbackInput}
          value={answer.instructorFeedback}
          onChangeText={(text) => updateAnswerGrading(answer.id, 'instructorFeedback', text)}
          placeholder="Feedback for this answer..."
          multiline
          numberOfLines={3}
        />
      </View>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Loading attempt for grading...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {/* Student Info */}
        <View style={styles.studentInfo}>
          <Text style={styles.studentName}>{attempt.student_name}</Text>
          <Text style={styles.studentCode}>{attempt.student_code}</Text>
          <Text style={styles.timeSpent}>Time: {attempt.time_spent} minutes</Text>
        </View>

        {/* Quiz Info */}
        <View style={styles.quizInfo}>
          <Text style={styles.quizName}>{quizInfo.name}</Text>
          <Text style={styles.quizDetails}>
            Max Score: {quizInfo.max_score} | Passing: {quizInfo.passing_score}
          </Text>
        </View>

        {/* Answers */}
        {answers.map(renderAnswerGrading)}

        {/* Overall Feedback */}
        <View style={styles.overallFeedback}>
          <Text style={styles.sectionTitle}>Overall Feedback:</Text>
          <TextInput
            style={styles.overallFeedbackInput}
            value={overallFeedback}
            onChangeText={setOverallFeedback}
            placeholder="Overall feedback for the student..."
            multiline
            numberOfLines={4}
          />
        </View>
      </ScrollView>

      {/* Submit Button */}
      <TouchableOpacity
        style={[styles.submitButton, grading && styles.submitButtonDisabled]}
        onPress={handleSubmitGrading}
        disabled={grading}
      >
        <Text style={styles.submitButtonText}>
          {grading ? 'Submitting...' : 'Submit Grading'}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  studentInfo: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  studentName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  studentCode: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  timeSpent: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  quizInfo: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  quizName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  quizDetails: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  answerContainer: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  questionHeader: {
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    paddingBottom: 12,
    marginBottom: 12,
  },
  questionText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  questionType: {
    fontSize: 12,
    color: '#007AFF',
    textTransform: 'uppercase',
    marginBottom: 4,
  },
  questionScore: {
    fontSize: 14,
    color: '#666',
  },
  studentAnswer: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  essayAnswer: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderRadius: 6,
  },
  choiceAnswer: {
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderRadius: 6,
  },
  selectedAnswer: {
    fontSize: 14,
    color: '#333',
    marginBottom: 4,
  },
  correctAnswer: {
    fontSize: 14,
    color: '#34C759',
    fontWeight: '500',
  },
  gradingSection: {
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingTop: 16,
  },
  scoreRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  scoreLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginRight: 8,
  },
  scoreInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 6,
    padding: 8,
    fontSize: 16,
    textAlign: 'center',
    width: 60,
    marginRight: 8,
  },
  maxScore: {
    fontSize: 14,
    color: '#666',
  },
  feedbackInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 6,
    padding: 12,
    fontSize: 14,
    textAlignVertical: 'top',
  },
  overallFeedback: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  overallFeedbackInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 6,
    padding: 12,
    fontSize: 14,
    textAlignVertical: 'top',
  },
  submitButton: {
    backgroundColor: '#007AFF',
    margin: 16,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  submitButtonDisabled: {
    backgroundColor: '#ccc',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
});

export default GradingScreen;
```

## ⚠️ Error Handling Best Practices

### **Common Error Scenarios**

```javascript
const handleGradingError = (error, context = '') => {
  console.error(`${context} error:`, error);

  // Network errors
  if (error.message.includes('Network request failed')) {
    Alert.alert('Network Error', 'Please check your internet connection and try again.');
    return;
  }

  // Authentication errors
  if (error.message.includes('401') || error.message.includes('Unauthorized')) {
    Alert.alert('Session Expired', 'Please log in again.', [
      { text: 'OK', onPress: () => navigation.navigate('Login') }
    ]);
    return;
  }

  // Permission errors
  if (error.message.includes('403') || error.message.includes('không có quyền')) {
    Alert.alert('Permission Denied', 'You do not have permission to grade this attempt.');
    return;
  }

  // Validation errors
  if (error.message.includes('validation')) {
    Alert.alert('Validation Error', 'Please check your grading input and try again.');
    return;
  }

  // Generic error
  Alert.alert('Error', error.message || 'Something went wrong. Please try again.');
};

// Usage in grading functions
const gradeAttemptWithErrorHandling = async (attemptId, answers, feedback) => {
  try {
    const result = await gradeAttempt(attemptId, answers, feedback);

    if (!result.success) {
      handleGradingError(new Error(result.error), 'Grade Attempt');
      return null;
    }

    return result.gradedAttempt;
  } catch (error) {
    handleGradingError(error, 'Grade Attempt');
    return null;
  }
};
```

## 📊 Analytics Integration

### **Quiz Analytics for Graded Attempts**

```javascript
const getQuizAnalytics = async (quizId) => {
  try {
    const response = await fetch(`${API_BASE_URL}/quizzes/${quizId}/analytics`, {
      method: 'GET',
      headers: headers
    });

    const result = await response.json();

    if (result.success) {
      return {
        success: true,
        analytics: result.data
      };
    } else {
      throw new Error(result.error || 'Failed to get quiz analytics');
    }
  } catch (error) {
    console.error('Get quiz analytics error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Analytics display component
const QuizAnalyticsCard = ({ analytics }) => (
  <View style={styles.analyticsCard}>
    <Text style={styles.analyticsTitle}>Quiz Performance</Text>

    <View style={styles.statsRow}>
      <View style={styles.statItem}>
        <Text style={styles.statValue}>{analytics.total_attempts}</Text>
        <Text style={styles.statLabel}>Total Attempts</Text>
      </View>
      <View style={styles.statItem}>
        <Text style={styles.statValue}>{analytics.average_score.toFixed(1)}</Text>
        <Text style={styles.statLabel}>Avg Score</Text>
      </View>
      <View style={styles.statItem}>
        <Text style={styles.statValue}>{analytics.pass_rate.toFixed(1)}%</Text>
        <Text style={styles.statLabel}>Pass Rate</Text>
      </View>
    </View>
  </View>
);
```

## 📋 Implementation Checklist

### **Backend Requirements**
- ✅ Instructor authentication working
- ✅ Grading endpoints available
- ✅ Permission validation implemented
- ✅ Database migration completed

### **Frontend Implementation**
- [ ] Install required dependencies (react-navigation, etc.)
- [ ] Implement authentication flow
- [ ] Create pending grading list screen
- [ ] Implement grading detail screen
- [ ] Add score input validation
- [ ] Implement feedback system
- [ ] Add analytics dashboard
- [ ] Implement error handling
- [ ] Add loading states
- [ ] Test complete grading workflow

### **Testing Checklist**
- [ ] Test pending grading list loading
- [ ] Test attempt detail loading
- [ ] Test grading submission with various scores
- [ ] Test feedback submission
- [ ] Test error scenarios (network, permission, validation)
- [ ] Test offline/network error handling
- [ ] Test grading workflow end-to-end
- [ ] Test analytics data display

## 🎯 Key Benefits

1. **Complete Grading Workflow**: From pending list to final submission
2. **Rich Feedback System**: Question-level and overall feedback
3. **Flexible Scoring**: Support for partial credit and custom scores
4. **Real-time Validation**: Immediate feedback on grading input
5. **Professional UI**: Mobile-optimized grading interface
6. **Analytics Integration**: Performance insights and statistics

## 🚀 Production Features

### **Advanced Grading Features**
- **Batch Grading**: Grade multiple attempts efficiently
- **Rubric Support**: Structured grading criteria
- **Auto-save**: Prevent data loss during grading
- **Offline Support**: Grade when network is unavailable
- **Export Results**: Download grading reports

### **Performance Optimizations**
- **Lazy Loading**: Load attempts on demand
- **Caching**: Cache graded attempts locally
- **Pagination**: Handle large numbers of attempts
- **Background Sync**: Sync grading data in background

---

**🚀 Ready for Implementation!** This grading system provides everything instructors need to efficiently grade student quiz attempts with comprehensive feedback and analytics.
