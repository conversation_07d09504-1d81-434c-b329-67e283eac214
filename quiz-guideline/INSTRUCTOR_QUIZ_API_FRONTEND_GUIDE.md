# Instructor Quiz API - Frontend Developer Guide

## 📋 Overview

This comprehensive guide provides frontend developers with everything needed to integrate instructor quiz management functionality. The system supports complete quiz lifecycle management from creation to publishing and analytics.

## 🔐 Authentication

All endpoints require JWT authentication with instructor role.

```javascript
const API_BASE_URL = 'https://lms-dev.ebill.vn/api/v1/instructors';
const authToken = 'your_jwt_token_here';

const headers = {
  'Authorization': `Bearer ${authToken}`,
  'Content-Type': 'application/json'
};
```

## 🎯 Complete Quiz Management Workflow

### **Step 1: Create Quiz**

#### **Endpoint**: `POST /quizzes`

**Purpose**: Create a new quiz with questions and answers.

**Parameters**:
- `name` (string, required): Quiz name
- `description` (string, optional): Quiz description  
- `quiz_type` (string, required): Type - "quiz", "assignment", "midterm", "final", "project"
- `subject_id` (integer, optional): Subject ID
- `class_id` (integer, optional): Class ID
- `max_score` (float, default: 100.0): Maximum score
- `passing_score` (float, default: 50.0): Passing score
- `time_limit` (integer, optional): Time limit in minutes
- `max_attempts` (integer, default: 1): Maximum attempts allowed
- `is_randomized` (boolean, default: false): Randomize question order
- `show_correct_answers` (boolean, default: false): Show correct answers to students
- `questions` (array): Array of question objects

**Question Object**:
- `text` (string, required): Question content
- `question_type` (string, required): "single_choice", "multiple_choice", "true_false", "essay"
- `score` (float, required): Points for this question
- `answers` (array): Array of answer objects (empty for essay questions)

**Answer Object**:
- `text` (string, required): Answer content
- `is_correct` (boolean, required): Whether this is the correct answer

#### **Curl Example**:
```bash
curl -X 'POST' \
  'https://lms-dev.ebill.vn/api/v1/instructors/quizzes' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "name": "Quiz Kiểm tra Lãnh đạo Kiến tạo",
    "description": "Bài kiểm tra về tư duy lãnh đạo trong thời đại chuyển hóa",
    "quiz_type": "midterm",
    "subject_id": 1,
    "max_score": 35.0,
    "passing_score": 25.0,
    "max_attempts": 2,
    "time_limit": 60,
    "show_correct_answers": false,
    "is_randomized": true,
    "questions": [
      {
        "text": "Tư duy lãnh đạo trong thời đại chuyển hóa có những đặc điểm gì?",
        "question_type": "essay",
        "score": 20.0,
        "answers": []
      },
      {
        "text": "Lãnh đạo kiến tạo khác gì với lãnh đạo truyền thống?",
        "question_type": "single_choice",
        "score": 15.0,
        "answers": [
          {"text": "Tập trung vào việc duy trì hiện trạng", "is_correct": false},
          {"text": "Tạo ra những thay đổi tích cực và bền vững", "is_correct": true},
          {"text": "Chỉ quan tâm đến lợi nhuận ngắn hạn", "is_correct": false},
          {"text": "Không có sự khác biệt", "is_correct": false}
        ]
      }
    ]
  }'
```

#### **Success Response (201)**:
```json
{
  "success": true,
  "message": "Tạo quiz thành công",
  "data": {
    "id": 18,
    "name": "Quiz Kiểm tra Lãnh đạo Kiến tạo",
    "code": "QUIZ00019",
    "state": "draft",
    "question_count": 2,
    "max_score": 35.0,
    "created_at": "2025-07-23T17:03:24.966363"
  },
  "meta": {
    "timestamp": "2025-07-23 17:03"
  }
}
```

#### **Error Responses**:
```json
// Validation Error (400)
{
  "success": false,
  "error": "Request validation error",
  "error_code": "VALIDATION_ERROR",
  "details": [
    {
      "field": "text",
      "code": "required_field", 
      "message": "Field required"
    }
  ]
}

// Invalid quiz_type (500)
{
  "success": false,
  "error": "Wrong value for eb.quiz.quiz_type: 'exam'",
  "error_code": "INTERNAL_SERVER_ERROR"
}

// Score validation (400)
{
  "detail": "Điểm đạt phải từ 0 đến điểm tối đa."
}
```

#### **React Native Example**:
```javascript
const createQuiz = async (quizData) => {
  try {
    const response = await fetch(`${API_BASE_URL}/quizzes`, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(quizData)
    });

    const result = await response.json();
    
    if (result.success) {
      return {
        success: true,
        quiz: result.data
      };
    } else {
      throw new Error(result.error || 'Failed to create quiz');
    }
  } catch (error) {
    console.error('Create quiz error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Usage example
const handleCreateQuiz = async () => {
  const quizData = {
    name: "Quiz Kiểm tra Lãnh đạo Kiến tạo",
    description: "Bài kiểm tra về tư duy lãnh đạo",
    quiz_type: "midterm",
    subject_id: 1,
    max_score: 35.0,
    passing_score: 25.0,
    max_attempts: 2,
    time_limit: 60,
    show_correct_answers: false,
    is_randomized: true,
    questions: [
      {
        text: "Tư duy lãnh đạo trong thời đại chuyển hóa có những đặc điểm gì?",
        question_type: "essay",
        score: 20.0,
        answers: []
      },
      {
        text: "Lãnh đạo kiến tạo khác gì với lãnh đạo truyền thống?",
        question_type: "single_choice",
        score: 15.0,
        answers: [
          {text: "Tập trung vào việc duy trì hiện trạng", is_correct: false},
          {text: "Tạo ra những thay đổi tích cực và bền vững", is_correct: true},
          {text: "Chỉ quan tâm đến lợi nhuận ngắn hạn", is_correct: false},
          {text: "Không có sự khác biệt", is_correct: false}
        ]
      }
    ]
  };

  const result = await createQuiz(quizData);
  
  if (result.success) {
    Alert.alert('Success', 'Quiz created successfully!');
    setQuizId(result.quiz.id);
  } else {
    Alert.alert('Error', result.error);
  }
};
```

### **Step 2: Mark Quiz Ready**

#### **Endpoint**: `POST /quizzes/{quiz_id}/ready`

**Purpose**: Mark quiz as ready for assignment to lessons.

**Parameters**:
- `quiz_id` (path, required): ID of the quiz to mark ready

#### **Curl Example**:
```bash
curl -X 'POST' \
  'https://lms-dev.ebill.vn/api/v1/instructors/quizzes/18/ready' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json'
```

#### **Success Response (200)**:
```json
{
  "success": true,
  "message": "Quiz 'Quiz Kiểm tra Lãnh đạo Kiến tạo' đã sẵn sàng để gán vào buổi học",
  "data": {
    "quiz_id": 18,
    "quiz_name": "Quiz Kiểm tra Lãnh đạo Kiến tạo",
    "state": "ready",
    "is_currently_published": false,
    "published_lesson_count": 0,
    "note": "Để công bố cho học viên, vui lòng gán quiz vào buổi học và publish từ lesson management."
  },
  "meta": {
    "timestamp": "2025-07-23 17:03"
  }
}
```

#### **React Native Example**:
```javascript
const markQuizReady = async (quizId) => {
  try {
    const response = await fetch(`${API_BASE_URL}/quizzes/${quizId}/ready`, {
      method: 'POST',
      headers: headers
    });

    const result = await response.json();
    
    if (result.success) {
      return {
        success: true,
        data: result.data
      };
    } else {
      throw new Error(result.error || 'Failed to mark quiz ready');
    }
  } catch (error) {
    console.error('Mark quiz ready error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Usage
const handleMarkReady = async () => {
  const result = await markQuizReady(quizId);
  
  if (result.success) {
    Alert.alert('Success', 'Quiz is now ready for assignment!');
    setQuizState('ready');
  } else {
    Alert.alert('Error', result.error);
  }
};
```

### **Step 3: Assign Quiz to Lesson**

#### **Endpoint**: `POST /lessons/{lesson_id}/quizzes`

**Purpose**: Assign a ready quiz to a specific lesson.

**Parameters**:
- `lesson_id` (path, required): ID of the lesson
- `quiz_id` (body, required): ID of the quiz to assign
- `sequence` (body, optional, default: 10): Display order in lesson
- `notes` (body, optional): Notes about quiz usage in this lesson

#### **Curl Example**:
```bash
curl -X 'POST' \
  'https://lms-dev.ebill.vn/api/v1/instructors/lessons/4/quizzes' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "quiz_id": 18,
    "sequence": 10,
    "notes": "Quiz kiểm tra hiểu biết về lãnh đạo kiến tạo"
  }'
```

#### **Success Response (200)**:
```json
{
  "success": true,
  "message": "Gán quiz cho buổi học thành công",
  "data": {
    "id": 17,
    "lesson_id": 4,
    "lesson_name": "BUỔI 3: KIẾN TẠO NIỀM TIN VÀ ẢNH HƯỞNG TRONG ĐỘI NGŨ",
    "quiz_id": 18,
    "quiz_name": "Quiz Kiểm tra Lãnh đạo Kiến tạo",
    "quiz_type": "midterm",
    "sequence": 10,
    "is_active": false,
    "status": "inactive",
    "published_at": null,
    "published_by_name": null,
    "unpublished_at": null,
    "unpublished_by_name": null,
    "notes": "Quiz kiểm tra hiểu biết về lãnh đạo kiến tạo",
    "can_publish": true,
    "can_unpublish": false,
    "lesson_stage": "scheduled",
    "lesson_start_datetime": "2025-06-14T13:00:00",
    "quiz_question_count": 2,
    "quiz_max_score": 35.0,
    "quiz_time_limit": 60,
    "quiz_passing_score": 25.0
  },
  "meta": {
    "timestamp": "2025-07-23 17:05"
  }
}
```

#### **React Native Example**:
```javascript
const assignQuizToLesson = async (lessonId, quizId, sequence = 10, notes = '') => {
  try {
    const response = await fetch(`${API_BASE_URL}/lessons/${lessonId}/quizzes`, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify({
        quiz_id: quizId,
        sequence: sequence,
        notes: notes
      })
    });

    const result = await response.json();

    if (result.success) {
      return {
        success: true,
        lessonQuiz: result.data
      };
    } else {
      throw new Error(result.error || 'Failed to assign quiz to lesson');
    }
  } catch (error) {
    console.error('Assign quiz to lesson error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Usage
const handleAssignQuiz = async () => {
  const result = await assignQuizToLesson(
    lessonId,
    quizId,
    10,
    'Quiz kiểm tra hiểu biết về lãnh đạo kiến tạo'
  );

  if (result.success) {
    Alert.alert('Success', 'Quiz assigned to lesson successfully!');
    setLessonQuizId(result.lessonQuiz.id);
  } else {
    Alert.alert('Error', result.error);
  }
};
```

### **Step 4: Publish Quiz for Students**

#### **Endpoint**: `POST /lessons/{lesson_id}/quizzes/{quiz_id}/publish`

**Purpose**: Publish quiz to make it available for students in the lesson.

**Parameters**:
- `lesson_id` (path, required): ID of the lesson
- `quiz_id` (path, required): ID of the quiz to publish

#### **Curl Example**:
```bash
curl -X 'POST' \
  'https://lms-dev.ebill.vn/api/v1/instructors/lessons/4/quizzes/18/publish' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json'
```

#### **Success Response (200)**:
```json
{
  "success": true,
  "message": "Công bố quiz thành công",
  "data": {
    "lesson_quiz_id": 17,
    "lesson_id": 4,
    "quiz_id": 18,
    "published_at": "2025-07-23T17:06:09",
    "is_active": true
  },
  "meta": {
    "timestamp": "2025-07-23 17:06"
  }
}
```

#### **React Native Example**:
```javascript
const publishQuiz = async (lessonId, quizId) => {
  try {
    const response = await fetch(`${API_BASE_URL}/lessons/${lessonId}/quizzes/${quizId}/publish`, {
      method: 'POST',
      headers: headers
    });

    const result = await response.json();

    if (result.success) {
      return {
        success: true,
        data: result.data
      };
    } else {
      throw new Error(result.error || 'Failed to publish quiz');
    }
  } catch (error) {
    console.error('Publish quiz error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Usage
const handlePublishQuiz = async () => {
  const result = await publishQuiz(lessonId, quizId);

  if (result.success) {
    Alert.alert('Success', 'Quiz published successfully! Students can now access it.');
    setIsQuizPublished(true);
  } else {
    Alert.alert('Error', result.error);
  }
};
```

### **Step 5: Unpublish Quiz**

#### **Endpoint**: `POST /lessons/{lesson_id}/quizzes/{quiz_id}/unpublish`

**Purpose**: Unpublish quiz to hide it from students.

#### **Curl Example**:
```bash
curl -X 'POST' \
  'https://lms-dev.ebill.vn/api/v1/instructors/lessons/4/quizzes/18/unpublish' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json'
```

#### **Success Response (200)**:
```json
{
  "success": true,
  "message": "Hủy công bố quiz thành công",
  "data": {
    "lesson_quiz_id": 17,
    "lesson_id": 4,
    "quiz_id": 18,
    "unpublished_at": "2025-07-23T17:07:46",
    "is_active": false
  },
  "meta": {
    "timestamp": "2025-07-23 17:07"
  }
}
```

#### **React Native Example**:
```javascript
const unpublishQuiz = async (lessonId, quizId) => {
  try {
    const response = await fetch(`${API_BASE_URL}/lessons/${lessonId}/quizzes/${quizId}/unpublish`, {
      method: 'POST',
      headers: headers
    });

    const result = await response.json();

    if (result.success) {
      return {
        success: true,
        data: result.data
      };
    } else {
      throw new Error(result.error || 'Failed to unpublish quiz');
    }
  } catch (error) {
    console.error('Unpublish quiz error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Usage
const handleUnpublishQuiz = async () => {
  Alert.alert(
    'Confirm Unpublish',
    'Are you sure you want to unpublish this quiz? Students will no longer be able to access it.',
    [
      { text: 'Cancel', style: 'cancel' },
      {
        text: 'Unpublish',
        style: 'destructive',
        onPress: async () => {
          const result = await unpublishQuiz(lessonId, quizId);

          if (result.success) {
            Alert.alert('Success', 'Quiz unpublished successfully!');
            setIsQuizPublished(false);
          } else {
            Alert.alert('Error', result.error);
          }
        }
      }
    ]
  );
};
```

## 📊 Additional Quiz Management APIs

### **Get Quiz List**

#### **Endpoint**: `GET /quizzes`

**Purpose**: Get list of instructor's quizzes with filtering and pagination.

**Query Parameters**:
- `page` (integer, default: 1): Page number
- `limit` (integer, default: 20, max: 100): Items per page
- `quiz_type` (string, optional): Filter by quiz type
- `subject_id` (integer, optional): Filter by subject
- `class_id` (integer, optional): Filter by class
- `state` (string, optional): Filter by state (draft, ready, published, archived)

#### **Curl Example**:
```bash
curl -X 'GET' \
  'https://lms-dev.ebill.vn/api/v1/instructors/quizzes?page=1&limit=20&state=ready' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN'
```

#### **React Native Example**:
```javascript
const getQuizList = async (page = 1, limit = 20, filters = {}) => {
  try {
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...filters
    });

    const response = await fetch(`${API_BASE_URL}/quizzes?${queryParams}`, {
      method: 'GET',
      headers: headers
    });

    const result = await response.json();

    if (result.success) {
      return {
        success: true,
        quizzes: result.data.quizzes,
        totalCount: result.data.total_count,
        totalPages: result.data.total_pages
      };
    } else {
      throw new Error(result.error || 'Failed to get quiz list');
    }
  } catch (error) {
    console.error('Get quiz list error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Usage
const loadQuizzes = async () => {
  const result = await getQuizList(1, 20, { state: 'ready' });

  if (result.success) {
    setQuizzes(result.quizzes);
    setTotalPages(result.totalPages);
  } else {
    Alert.alert('Error', result.error);
  }
};
```

### **Get Quiz Analytics**

#### **Endpoint**: `GET /quizzes/{quiz_id}/analytics`

**Purpose**: Get detailed analytics for a quiz.

#### **Curl Example**:
```bash
curl -X 'GET' \
  'https://lms-dev.ebill.vn/api/v1/instructors/quizzes/18/analytics' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN'
```

#### **Success Response (200)**:
```json
{
  "success": true,
  "message": "Lấy thống kê quiz thành công",
  "data": {
    "quiz_id": 18,
    "quiz_name": "Quiz Kiểm tra Lãnh đạo Kiến tạo",
    "total_students": 25,
    "completed_students": 20,
    "completion_rate": 80.0,
    "total_attempts": 22,
    "average_attempts": 1.1,
    "average_score": 28.5,
    "highest_score": 35.0,
    "lowest_score": 15.0,
    "pass_rate": 75.0,
    "average_time": 45.2,
    "fastest_time": 25.0,
    "slowest_time": 60.0,
    "score_distribution": {
      "0-7": 2,
      "7-14": 3,
      "14-21": 5,
      "21-28": 8,
      "28-35": 4
    }
  }
}
```

#### **React Native Example**:
```javascript
const getQuizAnalytics = async (quizId) => {
  try {
    const response = await fetch(`${API_BASE_URL}/quizzes/${quizId}/analytics`, {
      method: 'GET',
      headers: headers
    });

    const result = await response.json();

    if (result.success) {
      return {
        success: true,
        analytics: result.data
      };
    } else {
      throw new Error(result.error || 'Failed to get quiz analytics');
    }
  } catch (error) {
    console.error('Get quiz analytics error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Usage
const loadAnalytics = async () => {
  const result = await getQuizAnalytics(quizId);

  if (result.success) {
    setAnalytics(result.analytics);
  } else {
    Alert.alert('Error', result.error);
  }
};
```

## 🎨 Complete React Native Component Example

### **Quiz Management Screen**

```javascript
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  StyleSheet
} from 'react-native';

const QuizManagementScreen = ({ navigation, route }) => {
  const [quizzes, setQuizzes] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedQuiz, setSelectedQuiz] = useState(null);

  useEffect(() => {
    loadQuizzes();
  }, []);

  const loadQuizzes = async () => {
    setLoading(true);
    const result = await getQuizList(1, 20, { state: 'ready' });

    if (result.success) {
      setQuizzes(result.quizzes);
    } else {
      Alert.alert('Error', result.error);
    }
    setLoading(false);
  };

  const handleCreateQuiz = () => {
    navigation.navigate('CreateQuiz');
  };

  const handlePublishQuiz = async (quiz, lessonId) => {
    const result = await publishQuiz(lessonId, quiz.id);

    if (result.success) {
      Alert.alert('Success', 'Quiz published successfully!');
      loadQuizzes(); // Refresh list
    } else {
      Alert.alert('Error', result.error);
    }
  };

  const renderQuizItem = (quiz) => (
    <View key={quiz.id} style={styles.quizItem}>
      <View style={styles.quizHeader}>
        <Text style={styles.quizName}>{quiz.name}</Text>
        <Text style={styles.quizCode}>{quiz.code}</Text>
      </View>

      <View style={styles.quizDetails}>
        <Text style={styles.detailText}>Type: {quiz.quiz_type}</Text>
        <Text style={styles.detailText}>Questions: {quiz.question_count}</Text>
        <Text style={styles.detailText}>Max Score: {quiz.max_score}</Text>
        <Text style={styles.detailText}>Pass Rate: {quiz.pass_rate}%</Text>
      </View>

      <View style={styles.actionButtons}>
        <TouchableOpacity
          style={[styles.button, styles.analyticsButton]}
          onPress={() => navigation.navigate('QuizAnalytics', { quizId: quiz.id })}
        >
          <Text style={styles.buttonText}>Analytics</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.publishButton]}
          onPress={() => navigation.navigate('AssignQuiz', { quiz })}
        >
          <Text style={styles.buttonText}>Assign to Lesson</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Loading quizzes...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Quiz Management</Text>
        <TouchableOpacity
          style={styles.createButton}
          onPress={handleCreateQuiz}
        >
          <Text style={styles.createButtonText}>+ Create Quiz</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView}>
        {quizzes.map(renderQuizItem)}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  createButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  createButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  quizItem: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  quizHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  quizName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  quizCode: {
    fontSize: 14,
    color: '#666',
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  quizDetails: {
    marginBottom: 16,
  },
  detailText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 4,
  },
  analyticsButton: {
    backgroundColor: '#34C759',
  },
  publishButton: {
    backgroundColor: '#FF9500',
  },
  buttonText: {
    color: '#fff',
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
});

export default QuizManagementScreen;
```

## ⚠️ Error Handling Best Practices

### **Common Error Scenarios**

```javascript
const handleApiError = (error, context = '') => {
  console.error(`${context} error:`, error);

  // Network errors
  if (error.message.includes('Network request failed')) {
    Alert.alert('Network Error', 'Please check your internet connection and try again.');
    return;
  }

  // Authentication errors
  if (error.message.includes('401') || error.message.includes('Unauthorized')) {
    Alert.alert('Session Expired', 'Please log in again.', [
      { text: 'OK', onPress: () => navigation.navigate('Login') }
    ]);
    return;
  }

  // Validation errors
  if (error.message.includes('validation')) {
    Alert.alert('Validation Error', 'Please check your input and try again.');
    return;
  }

  // Generic error
  Alert.alert('Error', error.message || 'Something went wrong. Please try again.');
};

// Usage in API calls
const createQuizWithErrorHandling = async (quizData) => {
  try {
    const result = await createQuiz(quizData);

    if (!result.success) {
      handleApiError(new Error(result.error), 'Create Quiz');
      return null;
    }

    return result.quiz;
  } catch (error) {
    handleApiError(error, 'Create Quiz');
    return null;
  }
};
```

## 📋 Implementation Checklist

### **Backend Requirements**
- ✅ Instructor authentication working
- ✅ Quiz CRUD endpoints available
- ✅ Lesson-quiz management endpoints available
- ✅ Analytics endpoints available

### **Frontend Implementation**
- [ ] Install required dependencies (react-navigation, etc.)
- [ ] Implement authentication flow
- [ ] Create quiz management screens
- [ ] Implement quiz creation form
- [ ] Add lesson assignment functionality
- [ ] Implement publish/unpublish controls
- [ ] Add analytics dashboard
- [ ] Implement error handling
- [ ] Add loading states
- [ ] Test complete workflow

### **Testing Checklist**
- [ ] Test quiz creation with different question types
- [ ] Test quiz state transitions (draft → ready → published)
- [ ] Test lesson assignment and publishing
- [ ] Test analytics data display
- [ ] Test error scenarios
- [ ] Test offline/network error handling
- [ ] Test permission validation

## 🎯 Key Benefits

1. **Complete Quiz Lifecycle**: From creation to analytics
2. **Flexible Question Types**: Essay, multiple choice, true/false
3. **Lesson Integration**: Seamless assignment and publishing
4. **Real-time Analytics**: Detailed performance insights
5. **Robust Error Handling**: Comprehensive validation and feedback
6. **Mobile Optimized**: Responsive design for mobile devices

---

**🚀 Ready for Implementation!** This guide provides everything needed to implement instructor quiz management functionality in your React Native app.


Step 1: Create Quiz với detailed parameters và examples
Step 2: Mark Quiz Ready để chuẩn bị assign
Step 3: Assign Quiz to Lesson với lesson-quiz junction
Step 4: Publish Quiz để students có thể access
Step 5: Unpublish Quiz khi cần thiết