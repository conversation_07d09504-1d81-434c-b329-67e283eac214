# Color Guidelines for Mobile App Template

## Overview
This document defines the color palette and usage guidelines for the mobile app template. All colors are based on a blue gradient theme with specific RGB and CMYK values.

## Primary Color Palette

### Main Colors (Màu chủ đạo)
- **Dark Navy Blue (Primary Dark)**
  - RGB: 19, 33, 57
  - CMYK: 57, 81, 48, 57
  - Hex: #132139
  - Usage: Primary brand color, headers, navigation bars, important buttons

### Supporting Colors (Màu bổ trợ)
- **Medium Blue (Primary)**
  - RGB: 35, 64, 139
  - CMYK: 100, 89, 11, 2
  - Hex: #23408B
  - Usage: Secondary brand color, accent elements, links, active states

- **Light Blue (Primary Light)**
  - RGB: 89, 159, 214
  - CMYK: 65, 26, 0, 0
  - Hex: #599FD6
  - Usage: Backgrounds, subtle accents, hover states, secondary buttons

## Color Usage Rules

### 1. Primary Color Usage
- **Dark Navy Blue (#132139)** MUST be used for:
  - Main navigation bars
  - Primary action buttons
  - App headers and titles
  - Important call-to-action elements
  - Loading screens and splash screens

### 2. Secondary Color Usage
- **Medium Blue (#23408B)** SHOULD be used for:
  - Secondary buttons and actions
  - Links and interactive elements
  - Tab bar active states
  - Form field focus states
  - Progress indicators

### 3. Accent Color Usage
- **Light Blue (#599FD6)** CAN be used for:
  - Background gradients
  - Card hover states
  - Subtle highlights
  - Secondary information displays
  - Disabled state backgrounds

## Gradient Combinations

### Primary Gradient
```swift
LinearGradient(
    colors: [
        Color(red: 19/255, green: 33/255, blue: 57/255),    // #132139
        Color(red: 35/255, green: 64/255, blue: 139/255)    // #23408B
    ],
    startPoint: .topLeading,
    endPoint: .bottomTrailing
)
```

### Secondary Gradient
```swift
LinearGradient(
    colors: [
        Color(red: 35/255, green: 64/255, blue: 139/255),   // #23408B
        Color(red: 89/255, green: 159/255, blue: 214/255)   // #599FD6
    ],
    startPoint: .topLeading,
    endPoint: .bottomTrailing
)
```

## Implementation Guidelines

### AppConstants.Colors Updates Required
```swift
struct Colors {
    // Primary Colors
    static let primary = Color(red: 35/255, green: 64/255, blue: 139/255)           // #23408B
    static let primaryDark = Color(red: 19/255, green: 33/255, blue: 57/255)        // #132139
    static let primaryLight = Color(red: 89/255, green: 159/255, blue: 214/255)     // #599FD6
    
    // Gradient Combinations
    static let primaryGradient = LinearGradient(
        colors: [primaryDark, primary],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )
    
    static let secondaryGradient = LinearGradient(
        colors: [primary, primaryLight],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )
}
```

## Accessibility Considerations

### Contrast Ratios
- **Dark Navy Blue (#132139)** on white: 12.8:1 (AAA compliant)
- **Medium Blue (#23408B)** on white: 8.2:1 (AAA compliant)
- **Light Blue (#599FD6)** on white: 3.1:1 (AA compliant for large text)

### Color Blind Accessibility
- All three colors are distinguishable for users with common color vision deficiencies
- Always pair colors with icons or text labels for critical information
- Use sufficient contrast ratios for text readability

## Usage Examples

### Navigation Bar
```swift
.background(AppConstants.Colors.primaryDark)
.foregroundColor(.white)
```

### Primary Button
```swift
.background(AppConstants.Colors.primaryGradient)
.foregroundColor(.white)
```

### Secondary Button
```swift
.background(AppConstants.Colors.primary)
.foregroundColor(.white)
```

### Card Background with Hover
```swift
.background(AppConstants.Colors.surface)
.onHover { isHovered in
    backgroundColor = isHovered ? AppConstants.Colors.primaryLight.opacity(0.1) : AppConstants.Colors.surface
}
```

## Forbidden Practices

### DO NOT:
- Use colors outside of this defined palette for brand elements
- Mix warm colors (reds, oranges, yellows) with this cool blue palette
- Use the light blue (#599FD6) for primary actions (insufficient contrast)
- Create gradients that don't follow the defined combinations
- Use more than 2 colors from this palette in a single gradient

### DO:
- Always test color combinations for accessibility
- Use the darkest color for the most important elements
- Maintain consistency across all screens and components
- Consider dark mode variations of these colors
- Use opacity variations (0.1, 0.2, 0.5) for subtle effects

## Color Testing Checklist

Before implementing any color changes:
- [ ] Verify RGB values match specification exactly
- [ ] Test on both light and dark backgrounds
- [ ] Validate accessibility contrast ratios
- [ ] Check appearance on different device sizes
- [ ] Test with color vision deficiency simulators
- [ ] Ensure consistency with existing brand elements

## Notes
- All color values are based on the provided design specification
- This palette creates a professional, trustworthy, and modern appearance
- The gradient from dark to light blue provides visual depth and hierarchy
- Colors should be implemented in AppConstants.swift for consistency across the app
