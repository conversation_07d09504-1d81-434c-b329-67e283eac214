# Quản lý tài liệu trong hệ thống LMS

## Giới thiệu

Hệ thống quản lý tài liệu trong LMS được xây dựng dựa trên module `documents` của Odoo Enterprise. Hệ thống này cho phép quản lý tập trung tất cả các tài liệu liên quan đến giảng viên, họ<PERSON> vi<PERSON>, kh<PERSON><PERSON> h<PERSON>, l<PERSON><PERSON> họ<PERSON>, v.v.

## Tích hợp với module Documents

Hệ thống LMS sử dụng mixin `eb.lms.document.mixin` để tích hợp với module `documents` của Odoo. Mixin này cung cấp các phương thức và trường cần thiết để quản lý tài liệu cho các model trong hệ thống LMS.

### Các model sử dụng mixin

Hi<PERSON><PERSON> tại, các model sau đây đã đư<PERSON><PERSON> tích hợp với mixin `eb.lms.document.mixin`:

- `eb.instructor.instructor`: <PERSON><PERSON><PERSON><PERSON> lý tài liệu giảng viên
- (Các model khác sẽ được thêm vào trong tương lai)

## Quản lý tài liệu giảng viên

### Cấu hình

Để sử dụng tính năng quản lý tài liệu giảng viên, bạn cần:

1. Đảm bảo module `documents` đã được cài đặt
2. Bật tính năng quản lý tài liệu LMS trong cấu hình công ty
3. Cấu hình thư mục và thẻ mặc định cho tài liệu giảng viên

### Các loại tài liệu giảng viên

Hệ thống hỗ trợ các loại tài liệu giảng viên sau:

- CV/Sơ yếu lý lịch
- Hợp đồng
- Bằng cấp
- Chứng chỉ
- CMND/CCCD
- Khác

### Cách sử dụng

#### Xem tài liệu giảng viên

1. Mở form chi tiết giảng viên
2. Nhấn vào nút "Tài liệu" trong button box hoặc chuyển đến tab "Tài liệu"
3. Hệ thống sẽ hiển thị danh sách tài liệu của giảng viên

#### Tạo tài liệu mới

1. Mở form chi tiết giảng viên
2. Chuyển đến tab "Tài liệu"
3. Nhấn vào nút "Tạo tài liệu mới"
4. Điền thông tin tài liệu và tải lên file
5. Nhấn "Lưu" để tạo tài liệu

#### Quản lý tài liệu

Từ giao diện quản lý tài liệu, bạn có thể:

- Xem chi tiết tài liệu
- Tải xuống tài liệu
- Cập nhật thông tin tài liệu
- Xóa tài liệu
- Chia sẻ tài liệu qua liên kết

## Quyền truy cập

Quyền truy cập tài liệu được phân quyền theo nhóm người dùng:

- Học viên: Chỉ có quyền xem tài liệu công khai
- Giảng viên: Có quyền xem, tạo và cập nhật tài liệu của mình
- Quản trị viên: Có toàn quyền quản lý tài liệu trong hệ thống

## Lưu ý kỹ thuật

### Mixin `eb.lms.document.mixin`

Mixin `eb.lms.document.mixin` cung cấp các phương thức sau:

- `_compute_document_count`: Tính toán số lượng tài liệu liên quan
- `_compute_document_ids`: Lấy danh sách tài liệu liên quan
- `action_view_documents`: Mở danh sách tài liệu liên quan
- `action_create_document`: Tạo tài liệu mới
- `_check_create_documents`: Kiểm tra xem có tạo tài liệu tự động không
- `_get_document_vals_access_rights`: Trả về quyền truy cập mặc định cho tài liệu
- `_get_document_owner`: Trả về người sở hữu mặc định cho tài liệu

### Các phương thức cần triển khai

Khi một model kế thừa từ mixin `eb.lms.document.mixin`, model đó cần triển khai các phương thức sau:

- `_get_document_folder`: Trả về thư mục mặc định cho tài liệu
- `_get_document_tags`: Trả về các thẻ mặc định cho tài liệu

## Tích hợp với các tính năng khác

Hệ thống quản lý tài liệu được tích hợp với các tính năng khác trong hệ thống LMS:

- **Chatter**: Khi tạo hoặc cập nhật tài liệu, hệ thống sẽ tự động ghi lại hoạt động trong chatter
- **Email**: Có thể gửi email thông báo khi có tài liệu mới hoặc tài liệu được cập nhật
- **Báo cáo**: Có thể tạo báo cáo về tài liệu trong hệ thống

## Kết luận

Hệ thống quản lý tài liệu trong LMS cung cấp một cách tiếp cận tập trung và nhất quán để quản lý tài liệu trong toàn bộ hệ thống. Bằng cách sử dụng mixin `eb.lms.document.mixin`, các model trong hệ thống LMS có thể dễ dàng tích hợp với module `documents` của Odoo và tận dụng các tính năng mạnh mẽ của nó.
