# Hướng dẫn sử dụng các view đặc biệt trong module eb_lms

## Giới thiệu

Tài liệu này hướng dẫn cách sử dụng các view đặc biệt trong module eb_lms, bao gồm Grid view, Gantt view và Cohort view. Các view này cung cấp các cách hiển thị và phân tích dữ liệu khác nhau, giúp người dùng có cái nhìn tổng quan và chi tiết về dữ liệu.

## 1. Grid View

Grid view là một dạng xem dữ liệu dưới dạng lưới hai chiều, cho phép người dùng xem và chỉnh sửa dữ liệu theo hàng và cột. Trong module eb_lms, Grid view được sử dụng để hiển thị bảng điểm theo thời gian.

### 1.1. <PERSON><PERSON><PERSON> cập Grid View

Để truy cập Grid view cho kết quả đánh giá:

1. <PERSON><PERSON> đến **Điểm và kết quả > <PERSON>u<PERSON><PERSON> lý điểm số > Kết quả đánh giá**
2. Nhấn nút **Grid** trên thanh công cụ

### 1.2. Các tính năng của Grid View

#### 1.2.1. Xem dữ liệu theo thời gian

Grid view hiển thị dữ liệu theo thời gian, với các hàng là học viên và loại đánh giá, các cột là các khoảng thời gian (ngày, tuần, tháng, quý).

#### 1.2.2. Thay đổi khoảng thời gian

Bạn có thể thay đổi khoảng thời gian hiển thị bằng cách:

1. Nhấn vào nút dropdown **Ngày** trên thanh công cụ
2. Chọn khoảng thời gian mong muốn: Ngày, Tuần, Tháng, Quý

#### 1.2.3. Chỉnh sửa dữ liệu trực tiếp

Bạn có thể chỉnh sửa dữ liệu trực tiếp trong Grid view bằng cách:

1. Nhấn vào ô cần chỉnh sửa
2. Nhập giá trị mới
3. Nhấn Enter hoặc nhấn vào ô khác để lưu

#### 1.2.4. Lọc và nhóm dữ liệu

Bạn có thể lọc và nhóm dữ liệu trong Grid view bằng cách:

1. Nhấn vào biểu tượng lọc (hình phễu) trên thanh công cụ
2. Chọn các điều kiện lọc
3. Nhấn vào biểu tượng nhóm (hình ba đường ngang) trên thanh công cụ
4. Chọn các trường để nhóm

### 1.3. Ví dụ sử dụng Grid View

- **Xem điểm số của học viên theo tuần**: Chọn khoảng thời gian là Tuần, nhóm theo học viên
- **So sánh điểm số giữa các loại đánh giá**: Nhóm theo loại đánh giá
- **Theo dõi tiến độ của học viên theo thời gian**: Xem sự thay đổi điểm số của học viên qua các khoảng thời gian

## 2. Gantt View

Gantt view là một dạng xem dữ liệu dưới dạng biểu đồ Gantt, cho phép người dùng xem và quản lý các hoạt động theo thời gian. Trong module eb_lms, Gantt view được sử dụng để hiển thị lịch đánh giá.

### 2.1. Truy cập Gantt View

Để truy cập Gantt view cho đánh giá:

1. Đi đến **Điểm và kết quả > Quản lý điểm số > Đánh giá**
2. Nhấn nút **Gantt** trên thanh công cụ

### 2.2. Các tính năng của Gantt View

#### 2.2.1. Xem lịch đánh giá

Gantt view hiển thị lịch đánh giá theo thời gian, với các hàng là các đánh giá, các cột là các khoảng thời gian (ngày, tuần, tháng).

#### 2.2.2. Thay đổi khoảng thời gian

Bạn có thể thay đổi khoảng thời gian hiển thị bằng cách:

1. Nhấn vào nút dropdown **Ngày** trên thanh công cụ
2. Chọn khoảng thời gian mong muốn: Ngày, Tuần, Tháng

#### 2.2.3. Di chuyển và thay đổi kích thước đánh giá

Bạn có thể di chuyển và thay đổi kích thước đánh giá trực tiếp trong Gantt view bằng cách:

1. Kéo và thả đánh giá để di chuyển
2. Kéo các cạnh của đánh giá để thay đổi kích thước (thay đổi ngày bắt đầu hoặc ngày kết thúc)

#### 2.2.4. Lọc và nhóm dữ liệu

Bạn có thể lọc và nhóm dữ liệu trong Gantt view bằng cách:

1. Nhấn vào biểu tượng lọc (hình phễu) trên thanh công cụ
2. Chọn các điều kiện lọc
3. Nhấn vào biểu tượng nhóm (hình ba đường ngang) trên thanh công cụ
4. Chọn các trường để nhóm

### 2.3. Ví dụ sử dụng Gantt View

- **Lập kế hoạch đánh giá**: Xem và điều chỉnh lịch đánh giá để tránh trùng lặp
- **Theo dõi tiến độ đánh giá**: Xem trạng thái của các đánh giá theo thời gian
- **Quản lý đánh giá theo lớp học**: Nhóm đánh giá theo lớp học để xem lịch đánh giá của từng lớp

## 3. Cohort View

Cohort view là một dạng xem dữ liệu dưới dạng biểu đồ phân tích, cho phép người dùng phân tích dữ liệu theo thời gian. Trong module eb_lms, Cohort view được sử dụng để phân tích kết quả học tập và điểm tổng kết.

### 3.1. Truy cập Cohort View

Để truy cập Cohort view cho kết quả đánh giá:

1. Đi đến **Điểm và kết quả > Phân tích điểm số > Phân tích kết quả học tập**

Để truy cập Cohort view cho điểm tổng kết:

1. Đi đến **Điểm và kết quả > Phân tích điểm số > Phân tích điểm tổng kết**

### 3.2. Các tính năng của Cohort View

#### 3.2.1. Phân tích dữ liệu theo thời gian

Cohort view phân tích dữ liệu theo thời gian, hiển thị sự thay đổi của dữ liệu qua các khoảng thời gian.

#### 3.2.2. Thay đổi khoảng thời gian

Bạn có thể thay đổi khoảng thời gian hiển thị bằng cách:

1. Nhấn vào nút dropdown **Tháng** trên thanh công cụ
2. Chọn khoảng thời gian mong muốn: Ngày, Tuần, Tháng, Năm

#### 3.2.3. Thay đổi chế độ hiển thị

Cohort view có hai chế độ hiển thị:

- **Retention (Duy trì)**: Hiển thị tỷ lệ duy trì của dữ liệu qua thời gian
- **Churn (Mất đi)**: Hiển thị tỷ lệ mất đi của dữ liệu qua thời gian

Bạn có thể thay đổi chế độ hiển thị bằng cách:

1. Nhấn vào nút dropdown **Retention** hoặc **Churn** trên thanh công cụ
2. Chọn chế độ hiển thị mong muốn

#### 3.2.4. Thay đổi thước đo

Bạn có thể thay đổi thước đo hiển thị bằng cách:

1. Nhấn vào nút dropdown **Measures** trên thanh công cụ
2. Chọn thước đo mong muốn: Điểm số, Số lượng, v.v.

### 3.3. Ví dụ sử dụng Cohort View

- **Phân tích kết quả học tập theo thời gian**: Xem sự thay đổi của điểm số qua các khoảng thời gian
- **Phân tích số lượng kết quả đánh giá**: Xem số lượng kết quả đánh giá theo thời gian
- **Phân tích điểm tổng kết**: Xem sự thay đổi của điểm tổng kết qua các khoảng thời gian

## 4. Lưu ý quan trọng

### 4.1. Lưu ý chung

- Các view đặc biệt có thể yêu cầu nhiều tài nguyên hệ thống hơn các view thông thường
- Nên giới hạn số lượng dữ liệu hiển thị bằng cách sử dụng các bộ lọc
- Một số tính năng có thể không hoạt động đúng trên các thiết bị di động

### 4.2. Lưu ý cho Grid View

- Grid view chỉ hỗ trợ các trường kiểu date, selection và many2one cho cột
- Không sử dụng trường datetime cho cột trong Grid view

### 4.3. Lưu ý cho Gantt View

- Gantt view yêu cầu các trường date hoặc datetime cho ngày bắt đầu và ngày kết thúc
- Nên sử dụng các trường có ý nghĩa cho việc nhóm dữ liệu

### 4.4. Lưu ý cho Cohort View

- Cohort view yêu cầu các trường date cho ngày bắt đầu và ngày kết thúc
- Không sử dụng trường datetime cho Cohort view
- Nên sử dụng thước đo phù hợp với loại dữ liệu

## 5. Hỗ trợ

Nếu bạn gặp vấn đề khi sử dụng các view đặc biệt, vui lòng liên hệ với đội ngũ hỗ trợ qua email: <EMAIL>
