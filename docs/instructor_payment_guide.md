# Hướng dẫn thanh toán cho giảng viên trên hệ thống LMS

## <PERSON><PERSON><PERSON> lụ<PERSON>

1. [<PERSON><PERSON><PERSON><PERSON> thiệu](#giới-thiệu)
2. [C<PERSON>u hình thanh toán giảng viên](#cấu-hình-thanh-toán-giảng-viên)
3. [Quy trình thanh toán](#quy-trình-thanh-to<PERSON>)
4. [Tạo thanh toán cho giảng viên](#tạo-thanh-toán-cho-giảng-viên)
5. [Tạo hóa đơn nhà cung cấp](#tạo-hóa-đơn-nhà-cung-cấp)
6. [Xem công nợ của giảng viên](#xem-công-nợ-của-giảng-viên)
7. [Báo cáo thanh toán](#báo-cáo-thanh-to<PERSON>)
8. [<PERSON><PERSON><PERSON> hỏi thường gặp](#câu-hỏi-thường-gặp)

> **<PERSON><PERSON><PERSON> ý**: <PERSON><PERSON> hiểu chi tiết về cơ chế quy tắc lương và cách tính lương, vui lòng tham khảo [Hướng dẫn sử dụng quy tắc lương giảng viên](instructor_salary_rule_guide.md).

## Giới thiệu

Tài liệu này hướng dẫn chi tiết về quy trình thanh toán cho giảng viên trên hệ thống LMS. Hệ thống cho phép quản lý các khoản thanh toán, tạo hóa đơn nhà cung cấp và theo dõi công nợ của giảng viên một cách hiệu quả.

### Đối tượng sử dụng

- Quản trị viên hệ thống
- Nhân viên kế toán
- Quản lý đào tạo

### Các khái niệm cơ bản

- **Thanh toán giảng viên**: Khoản thanh toán cho giảng viên dựa trên các lớp học hoặc bài học đã hoàn thành.
- **Hóa đơn nhà cung cấp**: Hóa đơn được tạo để thanh toán cho giảng viên, được liên kết với khoản thanh toán.
- **Công nợ**: Số tiền cần thanh toán cho giảng viên.
- **Mức lương cơ bản**: Mức lương được thiết lập cho từng giảng viên, được sử dụng khi không có quy tắc lương cụ thể.
- **Quy tắc lương**: Quy tắc xác định mức lương cho các trường hợp cụ thể như môn học, khóa học hoặc lớp học.

## Cấu hình thanh toán giảng viên

Trước khi bắt đầu thanh toán cho giảng viên, bạn cần thiết lập cấu hình thanh toán bao gồm mức lương cơ bản và các quy tắc lương.

### Thiết lập mức lương cơ bản

1. Đi đến **LMS > Finance > Instructor Salaries**
2. Nhấp vào **Create** để tạo mức lương mới
3. Điền thông tin:
   - **Instructor**: Chọn giảng viên
   - **Salary Type**: Chọn loại lương
     - **Per Lesson**: Thanh toán theo buổi học
     - **Hourly**: Thanh toán theo giờ
     - **Monthly**: Thanh toán theo tháng
   - **Base Salary**: Nhập mức lương cơ bản
   - **Currency**: Chọn đơn vị tiền tệ
   - **Effective Date**: Ngày bắt đầu có hiệu lực
   - **End Date**: Ngày kết thúc hiệu lực (để trống nếu không có ngày kết thúc)
   - **Contract Reference**: Tham chiếu đến hợp đồng (nếu có)
4. Nhấp vào **Save** để lưu mức lương
5. Nhấp vào **Activate** để kích hoạt mức lương

### Thiết lập quy tắc lương

Quy tắc lương cho phép thiết lập mức lương đặc biệt cho các trường hợp cụ thể như môn học, khóa học hoặc lớp học.

1. Đi đến **LMS > Finance > Salary Rules**
2. Nhấp vào **Create** để tạo quy tắc lương mới
3. Điền thông tin:
   - **Name**: Tên quy tắc lương
   - **Rate Type**: Loại tính lương
     - **Per Lesson**: Thanh toán theo buổi học
     - **Per Subject**: Thanh toán theo môn học
     - **Hourly**: Thanh toán theo giờ
   - **Rate Amount**: Mức lương áp dụng
   - **Currency**: Đơn vị tiền tệ
   - **Priority**: Mức độ ưu tiên (Thấp, Bình thường, Cao)
   - **Effective Date**: Ngày bắt đầu có hiệu lực
   - **End Date**: Ngày kết thúc hiệu lực (để trống nếu không có ngày kết thúc)
4. Trong tab **Scope**, chọn phạm vi áp dụng:
   - **Instructor**: Áp dụng cho giảng viên cụ thể
   - **Subject**: Áp dụng cho môn học cụ thể
   - **Course**: Áp dụng cho khóa học cụ thể
   - **Class**: Áp dụng cho lớp học cụ thể
5. Nhấp vào **Save** để lưu quy tắc lương

> **Lưu ý**: Để biết thêm chi tiết về cách thiết lập và sử dụng quy tắc lương, vui lòng tham khảo [Hướng dẫn sử dụng quy tắc lương giảng viên](instructor_salary_rule_guide.md).

## Quy trình thanh toán

Quy trình thanh toán cho giảng viên bao gồm các bước sau:

1. **Xác định lớp học/bài học đã hoàn thành**: Hệ thống chỉ cho phép thanh toán cho các lớp học và bài học đã hoàn thành.
2. **Tạo khoản thanh toán**: Tạo khoản thanh toán cho giảng viên, chọn các lớp học và bài học đã hoàn thành.
3. **Xác nhận khoản thanh toán**: Xác nhận khoản thanh toán để chuyển sang trạng thái "Đã xác nhận".
4. **Tạo hóa đơn nhà cung cấp**: Tạo hóa đơn nhà cung cấp từ khoản thanh toán đã xác nhận.
5. **Đánh dấu đã thanh toán**: Đánh dấu khoản thanh toán đã được thanh toán sau khi hoàn tất quy trình.

## Tạo thanh toán cho giảng viên

### Cách 1: Từ menu Thanh toán giảng viên

1. Vào menu **Thanh toán giảng viên** (LMS > Finance > Instructor Payments).
2. Nhấn nút **Tạo** để tạo khoản thanh toán mới.
3. Điền thông tin về khoản thanh toán:
   - **Giảng viên**: Chọn giảng viên cần thanh toán.
   - **Ngày thanh toán**: Chọn ngày thanh toán.
   - **Loại thanh toán**: Chọn loại thanh toán (Lesson, Class, hoặc Manual).
   - **Số tiền**: Nhập số tiền cần thanh toán (có thể để trống và tính toán tự động sau).
   - **Phương thức thanh toán**: Chọn phương thức thanh toán.
   - **Tài khoản chi phí**: Chọn tài khoản chi phí sử dụng khi tạo hóa đơn nhà cung cấp.
4. Nếu chọn **Loại thanh toán** là **Lesson**:
   - Điền **Thời gian bắt đầu** và **Thời gian kết thúc** của kỳ thanh toán.
   - Chọn các lớp học đã hoàn thành trong tab **Lớp học**.
   - Chọn các bài học đã hoàn thành trong tab **Bài học**.
5. Nhấn **Lưu** để tạo khoản thanh toán.
6. Nhấn **Tìm quy tắc phù hợp** để tìm quy tắc lương phù hợp nhất.
7. Nhấn **Tính toán số tiền** để tính toán số tiền thanh toán dựa trên quy tắc lương.

### Cách 2: Từ hồ sơ giảng viên

1. Vào menu **Giảng viên** (People > Instructors).
2. Chọn giảng viên cần thanh toán.
3. Nhấn nút **Tạo thanh toán** trong hồ sơ giảng viên.
4. Điền thông tin về khoản thanh toán như hướng dẫn ở Cách 1.
5. Nhấn **Lưu** để tạo khoản thanh toán.

### Xác nhận khoản thanh toán

1. Mở khoản thanh toán đã tạo.
2. Nhấn nút **Confirm** để xác nhận khoản thanh toán.
3. Khoản thanh toán sẽ chuyển sang trạng thái "Đã xác nhận".

## Tạo hóa đơn nhà cung cấp

### Tạo hóa đơn nhà cung cấp từ khoản thanh toán

1. Mở khoản thanh toán đã xác nhận.
2. Nhấn nút **Create Vendor Bill** để tạo hóa đơn nhà cung cấp.
3. Hệ thống sẽ tạo hóa đơn nhà cung cấp và liên kết với khoản thanh toán.
4. Nhấn nút **View Vendor Bill** để xem hóa đơn nhà cung cấp đã tạo.

### Xử lý hóa đơn nhà cung cấp

1. Mở hóa đơn nhà cung cấp đã tạo.
2. Kiểm tra thông tin hóa đơn và chỉnh sửa nếu cần.
3. Nhấn nút **Xác nhận** để xác nhận hóa đơn.
4. Nhấn nút **Đăng ký thanh toán** để đăng ký thanh toán cho hóa đơn.
5. Hoàn tất quy trình thanh toán theo quy trình kế toán của đơn vị.

### Đánh dấu khoản thanh toán đã thanh toán

1. Quay lại khoản thanh toán.
2. Nhấn nút **Mark as Paid** để đánh dấu khoản thanh toán đã được thanh toán.
3. Khoản thanh toán sẽ chuyển sang trạng thái "Đã thanh toán".

## Xem công nợ của giảng viên

### Xem công nợ từ hồ sơ giảng viên

1. Vào menu **Giảng viên** (People > Instructors).
2. Chọn giảng viên cần xem công nợ.
3. Xem thông tin công nợ trong tab **Công nợ**:
   - **Số khoản thanh toán**: Số lượng khoản thanh toán của giảng viên.
   - **Số tiền cần thanh toán**: Tổng số tiền cần thanh toán cho giảng viên.
   - **Số hóa đơn nhà cung cấp**: Số lượng hóa đơn nhà cung cấp của giảng viên.
4. Nhấn nút **Xem thanh toán** để xem danh sách các khoản thanh toán.
5. Nhấn nút **Xem hóa đơn** để xem danh sách các hóa đơn nhà cung cấp.

### Xem danh sách thanh toán

1. Vào menu **Thanh toán giảng viên** (Finance > Instructor Payments).
2. Sử dụng bộ lọc để tìm kiếm các khoản thanh toán theo giảng viên, trạng thái, v.v.
3. Xem danh sách các khoản thanh toán và thông tin liên quan.

### Xem danh sách hóa đơn nhà cung cấp

1. Vào menu **Hóa đơn nhà cung cấp** (Accounting > Vendor Bills).
2. Sử dụng bộ lọc để tìm kiếm các hóa đơn nhà cung cấp theo giảng viên, trạng thái, v.v.
3. Xem danh sách các hóa đơn nhà cung cấp và thông tin liên quan.

## Báo cáo thanh toán

### Báo cáo thanh toán giảng viên

1. Vào menu **Báo cáo** (Reports).
2. Chọn báo cáo **Thanh toán giảng viên**.
3. Sử dụng bộ lọc để tìm kiếm các khoản thanh toán theo giảng viên, thời gian, v.v.
4. Xem báo cáo thanh toán và thông tin liên quan.

### Báo cáo công nợ giảng viên

1. Vào menu **Báo cáo** (Reports).
2. Chọn báo cáo **Công nợ giảng viên**.
3. Sử dụng bộ lọc để tìm kiếm công nợ theo giảng viên, thời gian, v.v.
4. Xem báo cáo công nợ và thông tin liên quan.

### Tạo thanh toán tự động từ buổi học

Hệ thống cung cấp một công cụ để tự động tạo thanh toán cho giảng viên dựa trên các buổi học đã hoàn thành.

1. Đi đến **LMS > Finance > Create Payments from Lessons**
2. Điền thông tin:
   - **From Date**: Ngày bắt đầu
   - **To Date**: Ngày kết thúc
   - **Instructors**: Chọn giảng viên cần tạo thanh toán (để trống nếu muốn tạo cho tất cả giảng viên)
   - **Payment Date**: Ngày thanh toán
   - **Auto Confirm**: Tích chọn nếu muốn tự động xác nhận các khoản thanh toán
3. Nhấp vào **Create Payments** để tạo các khoản thanh toán
4. Hệ thống sẽ tự động:
   - Tìm tất cả các buổi học đã hoàn thành trong khoảng thời gian đã chọn
   - Nhóm các buổi học theo giảng viên
   - Tạo một khoản thanh toán cho mỗi giảng viên
   - Tìm quy tắc lương phù hợp và tính toán số tiền thanh toán
   - Liên kết các buổi học với khoản thanh toán
   - Tự động xác nhận các khoản thanh toán nếu tích chọn **Auto Confirm**

## Câu hỏi thường gặp

### 1. Tại sao tôi không thể chọn một lớp học khi tạo thanh toán?

Hệ thống chỉ cho phép chọn các lớp học đã hoàn thành của giảng viên được chọn. Nếu bạn không thể chọn một lớp học, có thể lớp học đó chưa được đánh dấu là đã hoàn thành hoặc không thuộc về giảng viên được chọn.

### 2. Làm thế nào để đánh dấu một lớp học là đã hoàn thành?

1. Vào menu **Lớp học** (LMS > Classes).
2. Chọn lớp học cần đánh dấu là đã hoàn thành.
3. Nhấn nút **Đánh dấu hoàn thành** để chuyển lớp học sang trạng thái "Đã hoàn thành".

### 3. Tại sao tôi không thể tạo hóa đơn nhà cung cấp?

Để tạo hóa đơn nhà cung cấp, khoản thanh toán phải ở trạng thái "Đã xác nhận" hoặc "Đã thanh toán", và giảng viên phải có liên kết với một đối tác. Ngoài ra, bạn cần chọn tài khoản chi phí trước khi tạo hóa đơn nhà cung cấp.

### 4. Làm thế nào để liên kết giảng viên với đối tác?

1. Vào menu **Giảng viên** (LMS > People > Instructors).
2. Chọn giảng viên cần liên kết với đối tác.
3. Trong trường **Liên hệ**, chọn hoặc tạo một đối tác mới.
4. Nhấn **Lưu** để lưu thay đổi.

### 5. Làm thế nào để hủy một khoản thanh toán?

1. Mở khoản thanh toán cần hủy.
2. Nhấn nút **Cancel** để hủy khoản thanh toán.
3. Khoản thanh toán sẽ chuyển sang trạng thái "Đã hủy".

### 6. Làm thế nào để đặt lại một khoản thanh toán đã hủy?

1. Mở khoản thanh toán đã hủy.
2. Nhấn nút **Set to Draft** để đặt lại khoản thanh toán về trạng thái "Nháp".
3. Khoản thanh toán sẽ chuyển sang trạng thái "Nháp" và có thể được chỉnh sửa lại.

### 7. Tại sao không tìm thấy quy tắc lương phù hợp?

Nếu hệ thống không tìm thấy quy tắc lương phù hợp khi nhấn **Tìm quy tắc phù hợp**, có thể do:
- Không có quy tắc lương nào được thiết lập cho giảng viên, môn học, khóa học hoặc lớp học đó
- Các quy tắc lương đã thiết lập không còn hiệu lực (đã hết hạn)
- Các quy tắc lương đã thiết lập không áp dụng cho phạm vi của buổi học

Trong trường hợp này, hệ thống sẽ sử dụng mức lương cơ bản của giảng viên nếu có.

> **Lưu ý**: Để biết thêm chi tiết về cách khắc phục vấn đề và các trường hợp đặc biệt, vui lòng tham khảo [Hướng dẫn sử dụng quy tắc lương giảng viên](instructor_salary_rule_guide.md#các-trường-hợp-đặc-biệt).
