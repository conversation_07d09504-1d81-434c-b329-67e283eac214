# Tài liệu: <PERSON>ân công môn học cho giảng viên trong module eb_lms

## 1. Tổng quan

Chức năng phân công môn học cho giảng viên trong module eb_lms cho phép thiết lập và quản lý danh sách các môn học mà giảng viên có thể dạy, kèm theo đánh giá mức độ thành thạo cho từng môn giảng viên phụ trách. Chức năng này giúp đảm bảo rằng các giảng viên được phân công dạy những môn học phù hợp với chuyên môn và kinh nghiệm của họ.

## 2. <PERSON><PERSON> hình dữ liệu

### 2.1. <PERSON><PERSON> hình kỹ năng giảng viên (eb.instructor.skill)

<PERSON><PERSON> hình này quản lý mối quan hệ giữa giảng viên và môn học, bao gồm thông tin về mức độ thành thạo và kinh nghiệm.

**Các trường chính:**
- **instructor_id**: Liên kết đến giảng viên
- **subject_id**: Liên kết đến môn học
- **proficiency_level**: Mức độ thành thạo (beginner, intermediate, advanced, expert)
- **years_experience**: Số năm kinh nghiệm giảng dạy môn học
- **is_primary**: Đánh dấu đây có phải là môn học chính của giảng viên không
- **rating**: Đánh giá trung bình của giảng viên khi dạy môn học này
- **certification_ids**: Các chứng chỉ liên quan đến môn học
- **description**: Mô tả chi tiết về kỹ năng và kinh nghiệm giảng dạy
- **lesson_count**: Số buổi học đã dạy với môn học này
- **last_taught_date**: Ngày dạy gần nhất

## 3. Giao diện người dùng

### 3.1. Quản lý kỹ năng trong form giảng viên

Trong form giảng viên, tab "Kỹ năng" cho phép quản lý các môn học mà giảng viên có thể dạy:

1. Truy cập: **Giảng viên > [Chọn giảng viên] > Tab Kỹ năng**
2. Các thao tác:
   - Thêm môn học mới cho giảng viên
   - Cập nhật mức độ thành thạo và kinh nghiệm
   - Đánh dấu môn học chính
   - Liên kết với các chứng chỉ liên quan

### 3.2. Wizard quản lý kỹ năng hàng loạt

Wizard này cho phép thêm nhiều kỹ năng cùng lúc cho một giảng viên, hoặc sao chép kỹ năng từ giảng viên khác.

1. Truy cập: **Giảng viên > [Chọn giảng viên] > Tab Kỹ năng > Quản lý kỹ năng hàng loạt**
2. Các chế độ hoạt động:
   - **Thêm kỹ năng mới**: Thêm nhiều môn học cùng lúc cho giảng viên
   - **Sao chép kỹ năng từ giảng viên khác**: Sao chép kỹ năng từ một giảng viên khác
   - **Nhập kỹ năng từ file**: Nhập danh sách kỹ năng từ file Excel/CSV

### 3.3. Tìm kiếm giảng viên theo môn học

Wizard này giúp tìm kiếm giảng viên phù hợp cho một môn học cụ thể, dựa trên các tiêu chí như mức độ thành thạo, kinh nghiệm, đánh giá, v.v.

1. Truy cập: **Giảng viên > Tìm kiếm giảng viên theo môn học**
2. Các tiêu chí tìm kiếm:
   - Môn học
   - Mức độ thành thạo tối thiểu
   - Số năm kinh nghiệm tối thiểu
   - Đánh giá tối thiểu
   - Loại hợp tác
   - Có chứng chỉ liên quan

### 3.4. Phân công giảng viên hàng loạt

Wizard này cho phép phân công giảng viên cho nhiều buổi học cùng lúc.

1. Truy cập: **Buổi học > Phân công giảng viên hàng loạt**
2. Các bước thực hiện:
   - Chọn tiêu chí lọc buổi học (lớp, khóa học, môn học, thời gian)
   - Tìm kiếm buổi học
   - Chọn giảng viên để phân công
   - Xác nhận phân công

## 4. Báo cáo và phân tích

### 4.1. Báo cáo phân tích phân công môn học

Báo cáo này cung cấp tổng quan về việc phân công môn học cho giảng viên, bao gồm thông tin về mức độ thành thạo, kinh nghiệm, đánh giá và số buổi dạy.

1. Truy cập: **Báo cáo > Giảng viên > Phân tích phân công môn học**
2. Các chế độ xem:
   - **Kanban**: Hiển thị tổng quan về phân công môn học
   - **Pivot**: Phân tích chi tiết theo nhiều chiều
   - **Graph**: Biểu đồ phân tích
   - **List**: Danh sách chi tiết

### 4.2. Biểu đồ phân bố kỹ năng giảng viên

Biểu đồ này hiển thị phân bố kỹ năng của giảng viên theo mức độ thành thạo.

1. Truy cập: **Báo cáo > Giảng viên > Phân bố kỹ năng giảng viên**

### 4.3. Biểu đồ đánh giá giảng viên theo môn học

Biểu đồ này hiển thị đánh giá trung bình của giảng viên theo từng môn học.

1. Truy cập: **Báo cáo > Giảng viên > Đánh giá giảng viên theo môn học**

## 5. Quy trình phân công môn học cho giảng viên

### 5.1. Thiết lập kỹ năng cho giảng viên

1. Truy cập form giảng viên
2. Chuyển đến tab "Kỹ năng"
3. Thêm các môn học mà giảng viên có thể dạy
4. Đánh giá mức độ thành thạo cho từng môn học
5. Nhập số năm kinh nghiệm và thông tin bổ sung
6. Đánh dấu môn học chính nếu cần

### 5.2. Tìm giảng viên phù hợp cho môn học

1. Mở wizard "Tìm kiếm giảng viên theo môn học"
2. Chọn môn học cần tìm giảng viên
3. Nhập các tiêu chí tìm kiếm (mức độ thành thạo, kinh nghiệm, v.v.)
4. Nhấn "Tìm kiếm"
5. Xem danh sách giảng viên phù hợp và thông tin chi tiết

### 5.3. Phân công giảng viên cho buổi học

1. Khi tạo hoặc chỉnh sửa buổi học, chọn môn học trước
2. Hệ thống sẽ tự động lọc danh sách giảng viên có kỹ năng dạy môn học đó
3. Chọn giảng viên từ danh sách đã lọc
4. Lưu buổi học

### 5.4. Phân công hàng loạt

1. Mở wizard "Phân công giảng viên hàng loạt"
2. Chọn tiêu chí lọc buổi học
3. Nhấn "Tìm kiếm" để hiển thị danh sách buổi học
4. Chọn giảng viên để phân công
5. Nhấn "Phân công" để hoàn tất

## 6. Kiểm tra và ràng buộc

### 6.1. Kiểm tra kỹ năng khi phân công

Khi phân công giảng viên cho một buổi học, hệ thống sẽ kiểm tra xem giảng viên có kỹ năng dạy môn học đó không. Nếu không, hệ thống sẽ hiển thị thông báo lỗi.

### 6.2. Kiểm tra xung đột lịch

Hệ thống kiểm tra xem giảng viên có xung đột lịch không khi được phân công cho một buổi học. Xung đột có thể là do:
- Giảng viên đã được phân công dạy buổi học khác cùng thời điểm
- Giảng viên đã đăng ký nghỉ trong thời gian đó

## 7. Lưu ý quan trọng

- Mỗi giảng viên có thể có nhiều kỹ năng (môn học có thể dạy)
- Mỗi kỹ năng có mức độ thành thạo riêng
- Khi phân công giảng viên cho buổi học, nên ưu tiên giảng viên có mức độ thành thạo cao và kinh nghiệm nhiều
- Có thể đánh dấu một số môn học là "môn học chính" của giảng viên để ưu tiên phân công
- Báo cáo phân tích phân công môn học giúp đánh giá hiệu quả của việc phân công và phát hiện các vấn đề tiềm ẩn

## 8. Các trường hợp sử dụng phổ biến

### 8.1. Thêm môn học mới cho giảng viên

1. Truy cập form giảng viên
2. Chuyển đến tab "Kỹ năng"
3. Nhấn "Thêm một dòng"
4. Chọn môn học
5. Đánh giá mức độ thành thạo
6. Nhập số năm kinh nghiệm
7. Lưu

### 8.2. Tìm giảng viên thay thế

1. Mở wizard "Tìm kiếm giảng viên theo môn học"
2. Chọn môn học cần tìm giảng viên
3. Nhập các tiêu chí tìm kiếm
4. Nhấn "Tìm kiếm"
5. Xem danh sách giảng viên phù hợp
6. Chọn giảng viên thay thế

### 8.3. Phân tích nhu cầu đào tạo giảng viên

1. Truy cập báo cáo "Phân tích phân công môn học"
2. Lọc theo môn học hoặc danh mục môn học
3. Xem phân bố mức độ thành thạo của giảng viên
4. Xác định các môn học có ít giảng viên thành thạo
5. Lập kế hoạch đào tạo giảng viên cho các môn học đó

## 9. Tích hợp với các module khác

- **Module Buổi học (eb_lesson)**: Kiểm tra kỹ năng khi phân công giảng viên cho buổi học
- **Module Đánh giá (eb_evaluation)**: Cập nhật đánh giá của giảng viên khi dạy môn học
- **Module Chứng chỉ (eb_certification)**: Liên kết chứng chỉ với kỹ năng giảng viên
- **Module Báo cáo (eb_reporting)**: Cung cấp báo cáo phân tích về phân công môn học

---

Tài liệu này cung cấp hướng dẫn đầy đủ về chức năng phân công môn học cho giảng viên trong module eb_lms. Việc sử dụng đúng chức năng này sẽ giúp đảm bảo rằng các giảng viên được phân công dạy những môn học phù hợp với chuyên môn và kinh nghiệm của họ, từ đó nâng cao chất lượng giảng dạy.
