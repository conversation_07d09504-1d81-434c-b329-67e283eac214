# Hướng dẫn phát triển cho Odoo 18

## Giới thiệu

Tài liệu này cung cấp các hướng dẫn và tiêu chuẩn phát triển cho Odoo 18 trong dự án EB LMS. <PERSON><PERSON><PERSON> đích là đảm bảo tính nhất quán, chất lượng và khả năng bảo trì của mã nguồn.

## Cấu trúc module

### Cấu trúc thư mục

```
module_name/
├── __init__.py
├── __manifest__.py
├── controllers/
│   ├── __init__.py
│   └── controllers.py
├── data/
│   └── module_data.xml
├── demo/
│   └── demo_data.xml
├── i18n/
│   └── vi_VN.po
├── models/
│   ├── __init__.py
│   └── models.py
├── security/
│   ├── ir.model.access.csv
│   └── security_rules.xml
├── static/
│   ├── description/
│   │   └── icon.png
│   ├── src/
│   │   ├── js/
│   │   ├── scss/
│   │   └── xml/
├── views/
│   └── views.xml
├── wizards/
│   ├── __init__.py
│   └── wizards.py
└── report/
    └── report_templates.xml
```

### Manifest

Trong Odoo 18, file `__manifest__.py` nên bao gồm các trường sau:

```python
{
    'name': 'Module Name',
    'summary': 'Short description',
    'description': """
        Detailed description
        over multiple lines
    """,
    'author': 'EarnBase Technology',
    'website': 'https://earnbase.io',
    'category': 'Category',
    'version': '********.0',
    'depends': ['base'],
    'data': [
        'security/security_rules.xml',
        'security/ir.model.access.csv',
        'views/views.xml',
        'data/module_data.xml',
    ],
    'demo': [
        'demo/demo_data.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'module_name/static/src/js/**/*',
            'module_name/static/src/scss/**/*',
            'module_name/static/src/xml/**/*',
        ],
    },
    'license': 'LGPL-3',
    'application': False,
    'installable': True,
    'auto_install': False,
}
```

## Quy tắc đặt tên

### Models

- Tên model nên là danh từ, viết thường, phân tách bằng dấu chấm: `model.name`
- Tên bảng nên là danh từ, viết thường, phân tách bằng dấu gạch dưới: `model_name`
- Tên trường nên là danh từ, viết thường, phân tách bằng dấu gạch dưới: `field_name`
- Tên phương thức nên bắt đầu bằng động từ, viết thường, phân tách bằng dấu gạch dưới: `do_something`

### XML IDs

- Tên XML ID nên là danh từ, viết thường, phân tách bằng dấu gạch dưới: `model_name_action`
- Quy ước đặt tên cho các loại record:
  - Views: `view_model_name_type` (ví dụ: `view_partner_form`)
  - Actions: `action_model_name` (ví dụ: `action_partner`)
  - Menus: `menu_model_name` (ví dụ: `menu_partner`)
  - Security groups: `group_name` (ví dụ: `group_user`)
  - Rules: `rule_model_name` (ví dụ: `rule_partner`)

### JavaScript

- Tên class nên là danh từ, viết theo PascalCase: `ClassName`
- Tên biến và phương thức nên viết theo camelCase: `variableName`, `doSomething()`

## Tiêu chuẩn mã nguồn

### Python

- Tuân thủ PEP 8
- Sử dụng 4 dấu cách cho indentation
- Giới hạn độ dài dòng là 120 ký tự
- Sử dụng docstring cho các class và phương thức
- Sử dụng f-strings thay vì `%` hoặc `.format()`

```python
# Đúng
def calculate_total(self, amount, tax_rate):
    """Calculate total amount including tax.
    
    Args:
        amount: Base amount
        tax_rate: Tax rate percentage
        
    Returns:
        Total amount including tax
    """
    return amount * (1 + tax_rate / 100)

# Sai
def calculate_total(self, amount, tax_rate):
    return amount * (1 + tax_rate / 100)
```

### XML

- Sử dụng 4 dấu cách cho indentation
- Sử dụng dấu ngoặc kép cho các thuộc tính
- Đặt mỗi thuộc tính trên một dòng nếu có nhiều thuộc tính

```xml
<!-- Đúng -->
<record id="view_partner_form" model="ir.ui.view">
    <field name="name">res.partner.form</field>
    <field name="model">res.partner</field>
    <field name="arch" type="xml">
        <form string="Partner">
            <field name="name"/>
        </form>
    </field>
</record>

<!-- Sai -->
<record id="view_partner_form" model="ir.ui.view">
<field name="name">res.partner.form</field><field name="model">res.partner</field>
<field name="arch" type="xml"><form string="Partner"><field name="name"/></form></field>
</record>
```

### JavaScript

- Sử dụng 2 dấu cách cho indentation
- Sử dụng dấu chấm phẩy ở cuối câu lệnh
- Sử dụng dấu ngoặc nhọn cho tất cả các khối lệnh

```javascript
// Đúng
odoo.define('module_name.ClassName', function (require) {
  "use strict";
  
  const core = require('web.core');
  
  class ClassName {
    constructor(options) {
      this.options = options;
    }
    
    doSomething() {
      if (this.options) {
        return true;
      } else {
        return false;
      }
    }
  }
  
  return ClassName;
});

// Sai
odoo.define('module_name.ClassName', function (require) {
"use strict"
var core = require('web.core')
class ClassName {
constructor(options) { this.options = options }
doSomething() { if (this.options) return true; else return false }
}
return ClassName
})
```

## Thay đổi quan trọng trong Odoo 18

### Thay đổi trong model

1. **mail.template**:
   - Loại bỏ trường `report_template`
   - Loại bỏ trường `report_name`
   - Loại bỏ trường `user_signature`

2. **ir.cron**:
   - Loại bỏ trường `numbercall`
   - Loại bỏ trường `doall`

### Thay đổi trong views

1. **Tree view**:
   - Đổi tên từ `tree` thành `list`

2. **Form view**:
   - Không sử dụng `attrs` và `states` trong views
   - Sử dụng `<chatter />` thay vì khối `oe_chatter`

3. **Calendar view**:
   - Cẩn thận khi sử dụng thuộc tính `date_stop`

### Thay đổi trong API

1. **Create method**:
   - Sử dụng `@api.model_create_multi` thay vì ghi đè phương thức `create`

```python
# Đúng
@api.model_create_multi
def create(self, vals_list):
    for vals in vals_list:
        # Xử lý vals
    return super().create(vals_list)

# Sai
def create(self, vals):
    # Xử lý vals
    return super(ModelName, self).create(vals)
```

2. **Compute fields**:
   - Sử dụng `@api.depends` với tham số chính xác

```python
# Đúng
@api.depends('line_ids.price_unit', 'line_ids.quantity')
def _compute_amount(self):
    for record in self:
        record.amount = sum(line.price_unit * line.quantity for line in record.line_ids)

# Sai
@api.depends('line_ids')
def _compute_amount(self):
    for record in self:
        record.amount = sum(line.price_unit * line.quantity for line in record.line_ids)
```

## Thực hành tốt nhất

### Models

1. **Sử dụng ORM methods**:
   - Ưu tiên sử dụng các phương thức ORM như `search`, `browse`, `create`, `write` thay vì SQL trực tiếp
   - Sử dụng `sudo()` một cách cẩn thận và chỉ khi cần thiết

2. **Tổ chức mã nguồn**:
   - Chia nhỏ các model lớn thành nhiều file
   - Sử dụng inheritance đúng cách (`_inherit`, `_inherits`)
   - Nhóm các trường theo chức năng

3. **Bảo mật**:
   - Định nghĩa rõ ràng các quyền truy cập trong `ir.model.access.csv`
   - Sử dụng record rules để kiểm soát quyền truy cập dữ liệu

### Views

1. **Tổ chức views**:
   - Mỗi model nên có một file view riêng
   - Sắp xếp các view theo thứ tự: form, list, search, kanban, calendar, ...

2. **Responsive design**:
   - Sử dụng các class của Bootstrap để tạo giao diện responsive
   - Kiểm tra giao diện trên cả desktop và mobile

3. **UX/UI**:
   - Sử dụng các icon phù hợp
   - Cung cấp tooltips cho các trường phức tạp
   - Sử dụng các widget phù hợp cho từng loại dữ liệu

### JavaScript

1. **Sử dụng ES6+**:
   - Sử dụng `const` và `let` thay vì `var`
   - Sử dụng arrow functions khi phù hợp
   - Sử dụng destructuring, spread operator, template literals

2. **Tổ chức mã nguồn**:
   - Chia nhỏ các component lớn
   - Sử dụng OWL framework cho các component mới

## Debugging và Testing

### Debugging

1. **Logging**:
   - Sử dụng `_logger` thay vì `print`
   - Sử dụng các level log phù hợp: `debug`, `info`, `warning`, `error`

```python
import logging
_logger = logging.getLogger(__name__)

def my_method(self):
    _logger.debug("Debug message")
    _logger.info("Info message")
    _logger.warning("Warning message")
    _logger.error("Error message")
```

2. **Odoo Shell**:
   - Sử dụng Odoo shell để debug và test nhanh

```bash
python -m odoo shell -c odoo.conf -d database_name
```

### Testing

1. **Unit Tests**:
   - Viết unit tests cho tất cả các phương thức quan trọng
   - Sử dụng `TransactionCase` hoặc `SavepointCase`

```python
from odoo.tests.common import TransactionCase

class TestModelName(TransactionCase):
    def setUp(self):
        super().setUp()
        # Setup data
        
    def test_method(self):
        # Test logic
        result = self.env['model.name'].method()
        self.assertEqual(result, expected_result)
```

2. **QWeb Tests**:
   - Sử dụng `HttpCase` để test controllers và website

## Tối ưu hóa hiệu suất

1. **Database**:
   - Tạo index cho các trường thường xuyên được search
   - Sử dụng `prefetch_fields` để tối ưu việc load dữ liệu

2. **Queries**:
   - Sử dụng `search` với domain phù hợp
   - Hạn chế sử dụng `search_read` với nhiều records
   - Sử dụng `read_group` thay vì load tất cả records và tính toán

3. **Caching**:
   - Sử dụng `@api.depends_context` cho các compute fields phụ thuộc vào context
   - Sử dụng `@tools.ormcache` cho các phương thức đọc nhiều lần với cùng tham số

## Kết luận

Tuân thủ các hướng dẫn này sẽ giúp đảm bảo mã nguồn của bạn có chất lượng cao, dễ bảo trì và hiệu quả. Hãy nhớ rằng mã nguồn tốt không chỉ là mã nguồn hoạt động đúng mà còn là mã nguồn dễ đọc, dễ hiểu và dễ mở rộng.

## Tài liệu tham khảo

- [Odoo Guidelines](https://www.odoo.com/documentation/18.0/developer/reference/guidelines.html)
- [Odoo Development Cookbook](https://www.odoo.com/documentation/18.0/developer/howtos.html)
- [OWL Documentation](https://github.com/odoo/owl/blob/master/doc/readme.md)
