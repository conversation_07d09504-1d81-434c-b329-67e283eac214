# Hướng dẫn quản lý Activities trong module eb_lms

## Giớ<PERSON> thiệu

Activities trong Odoo là một công cụ mạnh mẽ để theo dõi và quản lý các hành động cần thực hiện trên các bản ghi. Module eb_lms đã tích hợp hệ thống activities để tự động hóa quy trình làm việc và đảm bảo các tác vụ quan trọng được theo dõi và hoàn thành đúng hạn.

Tài liệu này hướng dẫn cách:
1. Tạo activity types mới
2. Tạo activities tự động trong quy trình nghiệp vụ
3. Đánh dấu activities là đã hoàn thành
4. Hủy activities
5. Tìm kiếm và lọc activities

## 1. Tạo Activity Types mới

### 1.1. Định nghĩa Activity Types trong XML

Activity types đư<PERSON><PERSON> định nghĩa trong file `data/mail_activity_data.xml`. Đ<PERSON> tạo một activity type mới, thêm một record mới vào file này:

```xml
<record id="mail_activity_type_example" model="mail.activity.type">
    <field name="name">Tên Activity Type</field>
    <field name="summary">Tóm tắt ngắn gọn</field>
    <field name="category">default</field>
    <field name="res_model">eb.model.name</field>
    <field name="icon">fa-icon-name</field>
    <field name="delay_count">1</field>
    <field name="delay_unit">days</field>
    <field name="default_note">Ghi chú mặc định khi tạo activity</field>
</record>
```

Trong đó:
- `id`: ID duy nhất của activity type, nên bắt đầu bằng `mail_activity_type_`
- `name`: Tên hiển thị của activity type
- `summary`: Tóm tắt ngắn gọn về mục đích của activity
- `category`: Loại activity (default, upload_file, phonecall)
- `res_model`: Model mà activity type này áp dụng (để trống nếu áp dụng cho tất cả các model)
- `icon`: Biểu tượng Font Awesome (ví dụ: fa-check, fa-phone, fa-file)
- `delay_count`: Số đơn vị thời gian mặc định cho deadline
- `delay_unit`: Đơn vị thời gian (days, weeks, months)
- `default_note`: Ghi chú mặc định khi tạo activity

### 1.2. Cập nhật file __manifest__.py

Đảm bảo file `mail_activity_data.xml` được khai báo trong phần `data` của file `__manifest__.py`:

```python
'data': [
    # Các file data khác
    'data/mail_activity_data.xml',
],
```

### 1.3. Cài đặt/Cập nhật module

Sau khi định nghĩa activity types, cài đặt hoặc cập nhật module để áp dụng thay đổi:

```bash
python odoo-bin -d database_name -u eb_lms
```

## 2. Tạo Activities tự động

### 2.1. Sử dụng phương thức activity_schedule

Phương thức `activity_schedule` được cung cấp bởi `mail.activity.mixin` và có thể được sử dụng để tạo activities:

```python
def action_example(self):
    """Ví dụ về tạo activity."""
    for record in self:
        # Xác định người được giao nhiệm vụ
        responsible_user = record.user_id or self.env.user
        
        # Tạo activity
        record.activity_schedule(
            'eb_lms.mail_activity_type_example',  # XML ID của activity type
            user_id=responsible_user.id,
            date_deadline=fields.Date.context_today(self) + relativedelta(days=1),
            summary=_("Tiêu đề activity: %s") % record.name,
            note=_("Nội dung chi tiết của activity.")
        )
```

Các tham số của `activity_schedule`:
- `act_type_xmlid`: XML ID của activity type
- `user_id`: ID của người dùng được gán activity
- `date_deadline`: Hạn chót để hoàn thành activity
- `summary`: Tiêu đề của activity
- `note`: Nội dung chi tiết của activity

### 2.2. Tạo activities trong phương thức create

Để tự động tạo activities khi tạo bản ghi mới:

```python
@api.model_create_multi
def create(self, vals_list):
    records = super().create(vals_list)
    
    for record in records:
        # Tạo activity
        record.activity_schedule(
            'eb_lms.mail_activity_type_example',
            user_id=record.user_id.id,
            date_deadline=fields.Date.context_today(self) + relativedelta(days=1),
            summary=_("Tiêu đề activity: %s") % record.name,
            note=_("Nội dung chi tiết của activity.")
        )
    
    return records
```

### 2.3. Tạo activities khi chuyển trạng thái

Để tạo activities khi bản ghi chuyển trạng thái:

```python
def write(self, vals):
    # Lưu trạng thái trước khi cập nhật
    old_states = {record.id: record.state for record in self}
    
    result = super().write(vals)
    
    # Xử lý activities khi chuyển trạng thái
    if 'state' in vals:
        for record in self:
            old_state = old_states.get(record.id)
            if old_state and old_state != record.state:
                # Nếu chuyển sang trạng thái cụ thể
                if record.state == 'in_progress':
                    # Tạo activity
                    record.activity_schedule(
                        'eb_lms.mail_activity_type_example',
                        user_id=record.user_id.id,
                        date_deadline=fields.Date.context_today(self) + relativedelta(days=1),
                        summary=_("Tiêu đề activity: %s") % record.name,
                        note=_("Nội dung chi tiết của activity.")
                    )
    
    return result
```

## 3. Đánh dấu Activities là đã hoàn thành

### 3.1. Sử dụng phương thức activity_feedback

Phương thức `activity_feedback` được sử dụng để đánh dấu activities là đã hoàn thành:

```python
def action_complete_task(self):
    """Đánh dấu task là đã hoàn thành và đánh dấu activities liên quan là đã hoàn thành."""
    for record in self:
        # Đánh dấu hoàn thành activities của một loại cụ thể
        record.activity_feedback(
            ['eb_lms.mail_activity_type_example'],
            feedback=_("Task đã được hoàn thành.")
        )
```

Các tham số của `activity_feedback`:
- `act_type_xmlids`: Danh sách XML IDs của các activity types cần đánh dấu hoàn thành
- `feedback`: Phản hồi khi đánh dấu hoàn thành

### 3.2. Đánh dấu hoàn thành tất cả các activities

Để đánh dấu hoàn thành tất cả các activities của một bản ghi:

```python
def action_complete_all(self):
    """Đánh dấu hoàn thành tất cả các activities."""
    for record in self:
        activities = self.env['mail.activity'].search([
            ('res_model', '=', self._name),
            ('res_id', '=', record.id),
            ('state', '!=', 'done')
        ])
        for activity in activities:
            activity.action_feedback(
                feedback=_("Tất cả các tasks đã được hoàn thành.")
            )
```

## 4. Hủy Activities

### 4.1. Sử dụng phương thức action_cancel

Để hủy một activity:

```python
def action_cancel_task(self):
    """Hủy task và hủy activities liên quan."""
    for record in self:
        activities = self.env['mail.activity'].search([
            ('res_model', '=', self._name),
            ('res_id', '=', record.id),
            ('activity_type_id', '=', self.env.ref('eb_lms.mail_activity_type_example').id)
        ])
        for activity in activities:
            activity.unlink()  # Hoặc sử dụng activity.action_cancel()
```

### 4.2. Hủy tất cả các activities khi archive bản ghi

Odoo tự động hủy tất cả các activities khi bản ghi được archive (active = False). Điều này được xử lý trong phương thức `write` của `mail.activity.mixin`.

## 5. Tìm kiếm và lọc Activities

### 5.1. Tìm kiếm activities

```python
def find_activities(self):
    """Tìm kiếm activities theo điều kiện."""
    activities = self.env['mail.activity'].search([
        ('res_model', '=', self._name),
        ('res_id', 'in', self.ids),
        ('activity_type_id', '=', self.env.ref('eb_lms.mail_activity_type_example').id),
        ('state', '=', 'overdue')  # overdue, today, planned
    ])
    return activities
```

### 5.2. Lọc bản ghi theo activities

```python
def find_records_with_activities(self):
    """Tìm các bản ghi có activities đang chờ xử lý."""
    records = self.env[self._name].search([
        ('activity_ids.activity_type_id', '=', self.env.ref('eb_lms.mail_activity_type_example').id),
        ('activity_ids.state', '=', 'overdue')
    ])
    return records
```

## 6. Ví dụ thực tế

### 6.1. Tạo activity khi có sự cố mới

```python
@api.model_create_multi
def create(self, vals_list):
    issues = super().create(vals_list)
    
    for issue in issues:
        # Tạo activity để xem xét sự cố
        responsible_user = issue.user_id or (issue.team_id and issue.team_id.default_user_id) or self.env.user
        issue.activity_schedule(
            'eb_lms.mail_activity_type_issue_review',
            user_id=responsible_user.id,
            date_deadline=fields.Date.context_today(self) + relativedelta(days=1),
            summary=_("Xem xét sự cố: %s") % issue.name,
            note=_("Sự cố mới đã được tạo. Vui lòng xem xét và phân loại mức độ ưu tiên.")
        )
    
    return issues
```

### 6.2. Đánh dấu hoàn thành activity khi chuyển trạng thái

```python
def write(self, vals):
    # Lưu trạng thái trước khi cập nhật
    old_stages = {record.id: record.stage_id for record in self}
    
    result = super().write(vals)
    
    # Xử lý activities khi chuyển trạng thái
    if "stage_id" in vals:
        for record in self:
            old_stage = old_stages.get(record.id)
            if old_stage and old_stage.id != record.stage_id.id:
                # Nếu chuyển sang trạng thái đã giải quyết
                if record.is_resolved:
                    # Đánh dấu hoàn thành tất cả các activities
                    activities = self.env['mail.activity'].search([
                        ('res_model', '=', 'eb.issue.issue'),
                        ('res_id', '=', record.id),
                        ('state', '!=', 'done')
                    ])
                    for activity in activities:
                        activity.action_feedback(
                            feedback=_("Sự cố đã được giải quyết.")
                        )
    
    return result
```

## 7. Tích hợp với Automated Actions

Bạn có thể sử dụng Automated Actions để tự động tạo activities khi các điều kiện được đáp ứng:

1. Đi tới **Settings > Technical > Automation > Automated Actions**
2. Tạo một Automated Action mới:
   - Model: Chọn model cần áp dụng
   - Trigger: Chọn trigger (On Creation, On Update, On Time, ...)
   - Conditions: Thiết lập điều kiện
   - Action To Do: Chọn "Execute Python Code"
   - Python Code:
     ```python
     record.activity_schedule(
         'eb_lms.mail_activity_type_example',
         user_id=record.user_id.id,
         date_deadline=fields.Date.context_today(self) + relativedelta(days=1),
         summary="Tiêu đề activity",
         note="Nội dung chi tiết của activity."
     )
     ```

## 8. Lưu ý quan trọng

1. **Đảm bảo model kế thừa mail.activity.mixin**: Chỉ các model kế thừa `mail.activity.mixin` mới có thể sử dụng activities.
2. **Xử lý ngoại lệ**: Luôn kiểm tra sự tồn tại của người dùng, activity type, và các đối tượng liên quan trước khi tạo activities.
3. **Hiệu suất**: Tránh tạo quá nhiều activities cùng một lúc, đặc biệt là trong các phương thức `create` hoặc `write` xử lý nhiều bản ghi.
4. **Đồng bộ hóa**: Đảm bảo activities được đánh dấu hoàn thành hoặc hủy khi quy trình kết thúc để tránh tích tụ activities không cần thiết.
5. **Quyền truy cập**: Đảm bảo người dùng có quyền truy cập vào model và bản ghi liên quan để xem và xử lý activities.

## 9. Tài liệu tham khảo

- [Odoo Documentation - Mail Activity Mixin](https://www.odoo.com/documentation/18.0/developer/reference/backend/mixins.html#activities-tracking)
- [Odoo Documentation - Mail Activity](https://www.odoo.com/documentation/18.0/applications/essentials/activities.html)
