# Hướng dẫn hoàn tiền trong hệ thống LMS

## 1. Tổng quan

Tài liệu này mô tả quy trình hoàn tiền cho học viên trong hệ thống LMS. <PERSON>hi học viên yêu cầu hoà<PERSON> tiền, hệ thống sẽ tự động cập nhật trạng thái của tất cả các đối tượng liên quan sau khi hoàn thành quy trình hoàn tiền trong module kế toán.

## 1.1. Kiến trúc hệ thống hoàn tiền

Hệ thống hoàn tiền được thiết kế với kiến trúc service layer, giúp tập trung logic xử lý và đảm bảo tính nhất quán trong toàn bộ hệ thống:

- **Service Layer**: Tất cả logic xử lý hoàn tiền được tập trung trong `RefundService`, gi<PERSON><PERSON> đảm bảo các thao tác hoàn tiền được xử lý nhất quán, dù được gọi từ yêu cầu hoàn tiền hay hóa đơn hoàn tiền.

- **Model Layer**: Các model như `eb.refund.request`, `account.move`, và `eb.course.enrollment` sử dụng service để thực hiện các thao tác hoàn tiền, đảm bảo không có mã trùng lặp và dễ bảo trì.

- **API Layer**: Các API endpoints giao tiếp với service layer thông qua các model, đảm bảo tính nhất quán của dữ liệu.

> **Lưu ý**: Để hiểu chi tiết về kiến trúc kỹ thuật và cách triển khai chức năng hoàn tiền, vui lòng tham khảo [Tài liệu kỹ thuật về hoàn tiền](technical/refund_technical_guide_index.md).

## 2. Quy trình xử lý yêu cầu hoàn tiền

### 2.1. Tiếp nhận yêu cầu hoàn tiền

Có hai cách để tiếp nhận yêu cầu hoàn tiền:

1. **Tiếp nhận trực tiếp**:
   - Học viên liên hệ trực tiếp, qua điện thoại hoặc email để yêu cầu hoàn tiền
   - Nhân viên tạo yêu cầu hoàn tiền trong hệ thống

2. **Tiếp nhận qua ứng dụng di động**:
   - Học viên gửi yêu cầu hoàn tiền qua ứng dụng di động
   - Hệ thống tự động tạo yêu cầu hoàn tiền thông qua API

### 2.2. Xem xét và phê duyệt yêu cầu hoàn tiền

1. **Xem xét yêu cầu**:
   - Người quản lý xem xét yêu cầu hoàn tiền
   - Kiểm tra thông tin học viên, khóa học, lý do hoàn tiền
   - Kiểm tra chính sách hoàn tiền áp dụng

2. **Phê duyệt hoặc từ chối yêu cầu**:
   - Nếu đồng ý hoàn tiền: Phê duyệt yêu cầu và xác định số tiền được hoàn
   - Nếu không đồng ý: Từ chối yêu cầu và nêu rõ lý do

3. **Thông báo cho học viên**:
   - Hệ thống tự động gửi email thông báo cho học viên về trạng thái yêu cầu
   - Nếu yêu cầu được phê duyệt, thông báo số tiền sẽ được hoàn và phương thức hoàn tiền

### 2.3. Thực hiện hoàn tiền

1. **Tìm hóa đơn cần hoàn tiền**:
   - Từ yêu cầu hoàn tiền đã được phê duyệt, nhấn vào nút "Hoàn tiền"
   - Hệ thống sẽ hiển thị wizard tạo hóa đơn hoàn tiền với thông tin từ yêu cầu

2. **Tạo hóa đơn hoàn tiền (Credit Note)**:
   - Xác nhận thông tin hoàn tiền (số tiền, lý do, ngày hoàn tiền)
   - Tạo hóa đơn hoàn tiền

3. **Xác nhận hóa đơn hoàn tiền**:
   - Sau khi tạo hóa đơn hoàn tiền, nhấn vào nút "Xác nhận" (Confirm)
   - Hệ thống sẽ tự động cập nhật trạng thái của tất cả các đối tượng liên quan
   - Yêu cầu hoàn tiền sẽ được cập nhật trạng thái thành "Đã hoàn tiền"

## 3. Cập nhật tự động các đối tượng liên quan

Khi hóa đơn hoàn tiền được xác nhận, hệ thống sẽ tự động cập nhật các đối tượng liên quan thông qua service layer, đảm bảo tính nhất quán trong toàn bộ hệ thống:

### 3.1. Đăng ký khóa học (eb.course.enrollment)

- **Hoàn tiền toàn bộ**: Trạng thái đăng ký chuyển thành "refunded" (đã hoàn tiền)
- **Hoàn tiền một phần**: Trạng thái đăng ký chuyển thành "partial" (thanh toán một phần)
- **Ghi nhận thông tin hoàn tiền**: Lưu ngày hoàn tiền, số tiền hoàn, lý do hoàn tiền
- **Cập nhật số tiền đã thanh toán**: Trừ đi số tiền hoàn lại từ tổng số tiền đã thanh toán

### 3.2. Học viên trong lớp học (eb.class.student)

- **Hoàn tiền toàn bộ**: Trạng thái học viên trong lớp chuyển thành "refunded" (đã hoàn tiền)
- **Hoàn tiền một phần**: Giữ nguyên trạng thái "active" (hoạt động)
- **Ghi nhận thông tin rút khỏi lớp**: Lưu ngày rút khỏi lớp, lý do

### 3.3. Điểm danh buổi học (eb.attendance.attendance)

- **Hoàn tiền toàn bộ**: Hủy tất cả các bản ghi điểm danh trong tương lai (trạng thái chuyển thành "cancelled")
- **Hoàn tiền một phần**: Giữ nguyên các bản ghi điểm danh

### 3.4. Tham gia buổi học (eb.lesson.participant)

- **Hoàn tiền toàn bộ**: Hủy tất cả các bản ghi tham gia buổi học trong tương lai (trạng thái chuyển thành "cancelled")
- **Hoàn tiền một phần**: Giữ nguyên các bản ghi tham gia buổi học

### 3.5. Quyền truy cập tài liệu (eb.course.material.access)

- **Hoàn tiền toàn bộ**: Thu hồi quyền truy cập tài liệu (trạng thái chuyển thành "revoked")
- **Hoàn tiền một phần**: Giữ nguyên quyền truy cập tài liệu

## 4. Ghi nhận khoản hoàn tiền

Hệ thống sẽ tự động tạo bản ghi trong `eb.student.payment` với `is_refund=True` để theo dõi khoản hoàn tiền. Thông tin này sẽ được hiển thị trong tab "Payments" của form đăng ký khóa học.

## 4.1. Phương thức tiện ích trong eb.course.enrollment

Model `eb.course.enrollment` cung cấp các phương thức tiện ích để hỗ trợ việc cập nhật trạng thái khi hoàn tiền:

- **update_refund_status**: Cập nhật trạng thái đăng ký khi hoàn tiền
- **get_class_students**: Lấy danh sách học viên trong lớp liên quan đến đăng ký
- **get_future_attendances**: Lấy danh sách điểm danh trong tương lai của học viên
- **get_future_lesson_participants**: Lấy danh sách tham gia buổi học trong tương lai của học viên
- **get_course_material_access**: Lấy danh sách quyền truy cập tài liệu khóa học của học viên

Các phương thức này được sử dụng bởi service layer để cập nhật trạng thái của các đối tượng liên quan khi hoàn tiền.

## 5. Quản lý yêu cầu hoàn tiền

### 5.1. Trạng thái yêu cầu hoàn tiền

Yêu cầu hoàn tiền có các trạng thái sau:

- **Nháp (Draft)**: Yêu cầu mới được tạo, chưa gửi đi
- **Đã tiếp nhận (Submitted)**: Yêu cầu đã được gửi và đang chờ xem xét
- **Đang xem xét (Under Review)**: Yêu cầu đang được xem xét
- **Đã phê duyệt (Approved)**: Yêu cầu đã được phê duyệt, đang chờ hoàn tiền
- **Từ chối (Rejected)**: Yêu cầu đã bị từ chối
- **Đã hoàn tiền (Refunded)**: Yêu cầu đã được hoàn tiền
- **Đã hủy (Cancelled)**: Yêu cầu đã bị hủy

### 5.2. Chính sách hoàn tiền

Hệ thống cho phép thiết lập các chính sách hoàn tiền với các thông số:

- **Số ngày tối đa**: Số ngày tối đa kể từ khi đăng ký khóa học để được hoàn phí
- **Phần trăm hoàn phí**: Phần trăm số tiền được hoàn lại
- **Điều kiện áp dụng**: Các điều kiện áp dụng chính sách hoàn phí
- **Khóa học áp dụng**: Các khóa học áp dụng chính sách này

### 5.3. Lưu ý quan trọng

- Việc hoàn tiền sẽ không xóa bất kỳ dữ liệu nào, mà chỉ cập nhật trạng thái của các đối tượng liên quan.
- Các bản ghi điểm danh và tham gia buổi học đã diễn ra sẽ không bị thay đổi.
- Nếu học viên đã tham gia một số buổi học, thông tin này vẫn được lưu trữ trong hệ thống.
- Hệ thống không tự động tính toán số tiền hoàn lại dựa trên số buổi học đã tham gia, việc này do người dùng quyết định khi tạo hóa đơn hoàn tiền.
- Tất cả các yêu cầu hoàn tiền đều được lưu lại lịch sử đầy đủ, bao gồm người yêu cầu, người xem xét, người phê duyệt và người thực hiện hoàn tiền.

## 6. Ví dụ

### 6.1. Quy trình hoàn tiền toàn bộ

1. Học viên A đăng ký khóa học X với học phí 1,000,000 VND
2. Học viên A gửi yêu cầu hoàn tiền qua ứng dụng di động trước khi khóa học bắt đầu
3. Hệ thống tạo yêu cầu hoàn tiền với trạng thái "Đã tiếp nhận"
4. Người quản lý xem xét yêu cầu và phê duyệt với số tiền 1,000,000 VND
5. Kế toán nhấn nút "Hoàn tiền" trên yêu cầu hoàn tiền để tạo hóa đơn hoàn tiền
6. Sau khi xác nhận hóa đơn hoàn tiền:
   - Trạng thái yêu cầu hoàn tiền chuyển thành "Đã hoàn tiền"
   - Trạng thái đăng ký chuyển thành "refunded"
   - Học viên A bị xóa khỏi danh sách lớp học
   - Tất cả các bản ghi điểm danh và tham gia buổi học trong tương lai bị hủy
   - Quyền truy cập tài liệu bị thu hồi
7. Hệ thống tự động gửi email thông báo cho học viên A về việc hoàn tiền thành công

### 6.2. Quy trình hoàn tiền một phần

1. Học viên B đăng ký khóa học Y với học phí 2,000,000 VND
2. Học viên B đã tham gia 3/10 buổi học và liên hệ qua điện thoại để yêu cầu hoàn tiền phần còn lại
3. Nhân viên tạo yêu cầu hoàn tiền trong hệ thống với trạng thái "Nháp"
4. Nhân viên điền thông tin yêu cầu và gửi đi, trạng thái chuyển thành "Đã tiếp nhận"
5. Người quản lý xem xét yêu cầu và phê duyệt với số tiền 1,400,000 VND (70% học phí)
6. Kế toán nhấn nút "Hoàn tiền" trên yêu cầu hoàn tiền để tạo hóa đơn hoàn tiền
7. Sau khi xác nhận hóa đơn hoàn tiền:
   - Trạng thái yêu cầu hoàn tiền chuyển thành "Đã hoàn tiền"
   - Trạng thái đăng ký chuyển thành "partial"
   - Học viên B vẫn trong danh sách lớp học với trạng thái "active"
   - Các bản ghi điểm danh và tham gia buổi học vẫn giữ nguyên
   - Quyền truy cập tài liệu vẫn được duy trì
8. Hệ thống tự động gửi email thông báo cho học viên B về việc hoàn tiền thành công

### 6.3. Quy trình từ chối yêu cầu hoàn tiền

1. Học viên C đăng ký khóa học Z với học phí 1,500,000 VND
2. Học viên C đã tham gia 8/10 buổi học và gửi yêu cầu hoàn tiền qua ứng dụng di động
3. Hệ thống tạo yêu cầu hoàn tiền với trạng thái "Đã tiếp nhận"
4. Người quản lý xem xét yêu cầu và từ chối với lý do "Đã tham gia quá 70% số buổi học, không đủ điều kiện hoàn tiền theo chính sách"
5. Trạng thái yêu cầu hoàn tiền chuyển thành "Từ chối"
6. Hệ thống tự động gửi email thông báo cho học viên C về việc từ chối yêu cầu hoàn tiền và lý do

## 7. API cho yêu cầu hoàn tiền

Hệ thống cung cấp các API endpoint sau để xử lý yêu cầu hoàn tiền từ ứng dụng di động:

### 7.1. Lấy danh sách yêu cầu hoàn tiền

```
GET /api/v1/lms/refunds
```

Tham số:
- `page`: Số trang (mặc định: 1)
- `per_page`: Số lượng mỗi trang (mặc định: 20)
- `student_id`: Lọc theo học viên
- `course_id`: Lọc theo khóa học
- `state`: Lọc theo trạng thái
- `date_from`: Lọc từ ngày (YYYY-MM-DD)
- `date_to`: Lọc đến ngày (YYYY-MM-DD)

### 7.2. Lấy chi tiết yêu cầu hoàn tiền

```
GET /api/v1/lms/refunds/{id}
```

### 7.3. Tạo yêu cầu hoàn tiền mới

```
POST /api/v1/lms/refunds
```

Dữ liệu gửi lên:
```json
{
  "student_id": 123,
  "enrollment_id": 456,
  "request_amount": 1000000,
  "reason": "Lý do yêu cầu hoàn tiền",
  "source": "app",
  "refund_method": "original",
  "refund_method_details": "Thông tin chi tiết về phương thức hoàn tiền",
  "api_request_id": "ID yêu cầu từ app",
  "supporting_documents": [123, 456]
}
```

### 7.4. Cập nhật yêu cầu hoàn tiền

```
PUT /api/v1/lms/refunds/{id}
```

Dữ liệu gửi lên:
```json
{
  "request_amount": 1000000,
  "reason": "Lý do yêu cầu hoàn tiền cập nhật",
  "refund_method": "bank_transfer",
  "refund_method_details": "Thông tin chi tiết về phương thức hoàn tiền cập nhật",
  "supporting_documents": [123, 456, 789]
}
```

### 7.5. Gửi yêu cầu hoàn tiền

```
POST /api/v1/lms/refunds/{id}/submit
```

### 7.6. Hủy yêu cầu hoàn tiền

```
POST /api/v1/lms/refunds/{id}/cancel
```
