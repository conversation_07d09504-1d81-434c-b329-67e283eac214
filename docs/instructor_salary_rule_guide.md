# Hướng dẫn sử dụng quy tắc lương giảng viên

## <PERSON><PERSON><PERSON> lục

1. [<PERSON><PERSON><PERSON><PERSON> thiệu](#giới-thiệu)
2. [<PERSON><PERSON><PERSON> trúc hệ thống lương](#cấu-tr<PERSON><PERSON>-hệ-thống-<PERSON><PERSON>)
3. [<PERSON>uy tắc lươ<PERSON>](#quy-tắ<PERSON>-<PERSON><PERSON>)
4. [<PERSON><PERSON> chế tìm quy tắc lương phù hợp](#c<PERSON>-chế-tìm-quy-tắc-l<PERSON>-phù-hợp)
5. [<PERSON><PERSON> chế tính lương](#c<PERSON>-ch<PERSON>-t<PERSON><PERSON>-<PERSON><PERSON>)
6. [<PERSON><PERSON> dụ thực tế](#ví-dụ-thực-tế)
7. [<PERSON><PERSON><PERSON> trường hợp đặc biệt](#các-trường-hợp-đặc-biệt)
8. [<PERSON><PERSON><PERSON> hỏi thường gặp](#câu-hỏi-thường-gặp)

## <PERSON><PERSON>ớ<PERSON> thiệu

Tài liệu này hướng dẫn chi tiết về cơ chế quy tắc lương giảng viên trong hệ thống LMS. Quy tắc lương cho phép thiết lập các mức lương khác nhau cho giảng viên dựa trên nhiều tiêu chí như môn học, khóa học, lớp học, và áp dụng tự động khi tính toán thanh toán.

### Đối tượng sử dụng

- Quản trị viên hệ thống
- Nhân viên kế toán
- Quản lý đào tạo

### Lợi ích của quy tắc lương

- **Linh hoạt**: Thiết lập mức lương khác nhau cho từng môn học, khóa học, lớp học
- **Tự động**: Hệ thống tự động tìm quy tắc lương phù hợp và tính toán thanh toán
- **Minh bạch**: Giảng viên và quản lý có thể dễ dàng hiểu cách tính lương
- **Hiệu quả**: Giảm thời gian quản lý và tính toán lương thủ công

## Cấu trúc hệ thống lương

Hệ thống lương trong module eb_lms bao gồm 3 model chính:

### 1. Quy tắc lương (eb.instructor.salary.rule)

Định nghĩa các quy tắc tính lương cụ thể cho giảng viên, có thể áp dụng cho từng môn học, khóa học, lớp học cụ thể.

### 2. Mức lương cơ bản (eb.instructor.salary)

Lưu trữ mức lương cơ bản của giảng viên, có thể theo giờ, theo buổi, hoặc theo tháng.

### 3. Thanh toán (eb.instructor.payment)

Quản lý các khoản thanh toán cho giảng viên dựa trên các buổi học đã dạy.

## Quy tắc lương

### Cấu trúc quy tắc lương

Mỗi quy tắc lương bao gồm các thành phần sau:

#### Thông tin cơ bản

- **Tên quy tắc**: Tên mô tả quy tắc lương
- **Trạng thái**: Đang hoạt động hoặc đã lưu trữ
- **Độ ưu tiên**: Thấp, Bình thường, Cao (quy tắc có độ ưu tiên cao hơn sẽ được áp dụng trước)

#### Phạm vi áp dụng

- **Giảng viên**: Áp dụng cho giảng viên cụ thể hoặc tất cả giảng viên
- **Môn học**: Áp dụng cho môn học cụ thể hoặc tất cả môn học
- **Khóa học**: Áp dụng cho khóa học cụ thể hoặc tất cả khóa học
- **Lớp học**: Áp dụng cho lớp học cụ thể hoặc tất cả lớp học

#### Loại tính lương

- **Per Lesson (Theo buổi học)**: Tính lương theo số buổi học
- **Per Subject (Theo môn học)**: Tính lương theo số môn học
- **Hourly (Theo giờ)**: Tính lương theo số giờ dạy

#### Mức lương

- **Mức lương**: Số tiền cụ thể cho mỗi đơn vị tính lương
- **Đơn vị tiền tệ**: Đơn vị tiền tệ của mức lương

#### Thời gian hiệu lực

- **Ngày bắt đầu**: Ngày bắt đầu áp dụng quy tắc
- **Ngày kết thúc**: Ngày kết thúc áp dụng quy tắc (để trống nếu không có ngày kết thúc)

### Cách tạo quy tắc lương

1. Đi đến **LMS > Finance > Salary Rules**
2. Nhấp vào **Create** để tạo quy tắc lương mới
3. Điền thông tin cơ bản:
   - **Tên quy tắc**: Nhập tên mô tả quy tắc lương
   - **Độ ưu tiên**: Chọn mức độ ưu tiên (Thấp, Bình thường, Cao)
   - **Loại tính lương**: Chọn loại tính lương (Per Lesson, Per Subject, Hourly)
   - **Mức lương**: Nhập số tiền cụ thể
   - **Đơn vị tiền tệ**: Chọn đơn vị tiền tệ
   - **Ngày bắt đầu**: Chọn ngày bắt đầu áp dụng
   - **Ngày kết thúc**: Chọn ngày kết thúc áp dụng (để trống nếu không có ngày kết thúc)
4. Trong tab **Phạm vi áp dụng**, chọn phạm vi áp dụng:
   - **Giảng viên**: Chọn giảng viên cụ thể hoặc để trống nếu áp dụng cho tất cả giảng viên
   - **Môn học**: Chọn môn học cụ thể hoặc để trống nếu áp dụng cho tất cả môn học
   - **Khóa học**: Chọn khóa học cụ thể hoặc để trống nếu áp dụng cho tất cả khóa học
   - **Lớp học**: Chọn lớp học cụ thể hoặc để trống nếu áp dụng cho tất cả lớp học
5. Nhấp vào **Save** để lưu quy tắc lương

### Cách tạo quy tắc lương từ form giảng viên

1. Đi đến **LMS > People > Instructors**
2. Chọn giảng viên cần tạo quy tắc lương
3. Trong tab **Quy tắc lương**, nhấp vào **Tạo quy tắc lương mới**
4. Điền thông tin quy tắc lương như hướng dẫn ở trên
5. Nhấp vào **Create** để tạo quy tắc lương

## Cơ chế tìm quy tắc lương phù hợp

Khi tính toán lương cho giảng viên, hệ thống sẽ tìm quy tắc lương phù hợp nhất dựa trên các tiêu chí sau:

### Tiêu chí tìm kiếm

1. **Trạng thái**: Quy tắc phải đang hoạt động (active = True)
2. **Thời gian hiệu lực**: Ngày áp dụng phải nằm trong khoảng thời gian hiệu lực của quy tắc
3. **Giảng viên**: Quy tắc phải áp dụng cho giảng viên cụ thể hoặc cho tất cả giảng viên
4. **Lớp học**: Nếu có lớp học, quy tắc phải áp dụng cho lớp học đó hoặc cho tất cả lớp học
5. **Môn học**: Nếu có môn học, quy tắc phải áp dụng cho môn học đó hoặc cho tất cả môn học
6. **Khóa học**: Nếu có khóa học, quy tắc phải áp dụng cho khóa học đó hoặc cho tất cả khóa học

### Thứ tự ưu tiên

Khi có nhiều quy tắc lương phù hợp, hệ thống sẽ chọn quy tắc theo thứ tự ưu tiên sau:

1. **Độ ưu tiên**: Quy tắc có độ ưu tiên cao hơn sẽ được chọn trước
2. **Phạm vi cụ thể**: Quy tắc có phạm vi cụ thể hơn sẽ được chọn trước (Lớp học > Khóa học > Môn học > Giảng viên)
3. **Thời gian tạo**: Quy tắc được tạo gần đây nhất sẽ được chọn

### Ví dụ về cơ chế tìm kiếm

Giả sử có các quy tắc lương sau:

1. Quy tắc A: Áp dụng cho tất cả giảng viên, mức lương 300.000 VND/buổi, độ ưu tiên Thấp
2. Quy tắc B: Áp dụng cho giảng viên X, mức lương 350.000 VND/buổi, độ ưu tiên Bình thường
3. Quy tắc C: Áp dụng cho giảng viên X và môn học Y, mức lương 400.000 VND/buổi, độ ưu tiên Bình thường
4. Quy tắc D: Áp dụng cho giảng viên X và lớp học Z, mức lương 450.000 VND/buổi, độ ưu tiên Cao

Khi tính lương cho giảng viên X dạy môn học Y trong lớp học Z:
- Quy tắc A, B, C, D đều phù hợp
- Quy tắc D có độ ưu tiên cao nhất (Cao) nên được chọn
- Mức lương áp dụng: 450.000 VND/buổi

## Cơ chế tính lương

Sau khi tìm được quy tắc lương phù hợp, hệ thống sẽ tính toán lương dựa trên loại tính lương của quy tắc:

### 1. Tính lương theo buổi học (Per Lesson)

```
Tổng lương = Mức lương × Số buổi học
```

**Ví dụ**: Nếu mức lương là 500.000 VND/buổi và giảng viên dạy 5 buổi học, tổng lương sẽ là 2.500.000 VND.

### 2. Tính lương theo khóa học (Per Course)

```
Tổng lương = Mức lương × Số khóa học khác nhau
```

**Ví dụ**: Nếu mức lương là 10.000.000 VND/khóa học và giảng viên dạy 2 khóa học khác nhau, tổng lương sẽ là 20.000.000 VND. Lương được trả khi khóa học hoàn thành.

### Trường hợp không tìm thấy quy tắc lương

Nếu không tìm thấy quy tắc lương phù hợp, hệ thống sẽ sử dụng mức lương cơ bản của giảng viên:

1. **Nếu mức lương cơ bản là theo buổi (Per Lesson)**:
   ```
   Tổng lương = Mức lương cơ bản × Số buổi học
   ```

2. **Nếu mức lương cơ bản là theo giờ (Hourly)**:
   ```
   Tổng lương = Mức lương cơ bản × Tổng số giờ dạy
   ```

3. **Nếu mức lương cơ bản là theo tháng (Monthly)**:
   ```
   Tổng lương = Mức lương cơ bản
   ```

## Ví dụ thực tế

### Ví dụ 1: Tính lương theo buổi học

**Tình huống**: Trung tâm muốn trả lương cho giảng viên dạy lập trình với mức 500.000 VND/buổi.

**Cấu hình**:
1. Tạo quy tắc lương với thông tin:
   - Tên: "Mức lương lập trình cơ bản"
   - Loại tính lương: Per Lesson (Theo buổi học)
   - Mức lương: 500.000 VND
   - Phạm vi: Áp dụng cho môn học "Lập trình Python cơ bản"

**Kết quả**:
- Giảng viên dạy 5 buổi học môn "Lập trình Python cơ bản"
- Hệ thống tìm thấy quy tắc lương phù hợp
- Tính toán: 500.000 VND × 5 buổi = 2.500.000 VND

### Ví dụ 2: Tính lương theo giờ

**Tình huống**: Trung tâm muốn trả lương cho giảng viên thiết kế đồ họa với mức 200.000 VND/giờ.

**Cấu hình**:
1. Tạo quy tắc lương với thông tin:
   - Tên: "Mức lương thiết kế đồ họa"
   - Loại tính lương: Hourly (Theo giờ)
   - Mức lương: 200.000 VND
   - Phạm vi: Áp dụng cho khóa học "Thiết kế đồ họa"

**Kết quả**:
- Giảng viên dạy 3 buổi học, mỗi buổi 2 giờ
- Hệ thống tìm thấy quy tắc lương phù hợp
- Tính toán: 200.000 VND × (3 buổi × 2 giờ) = 1.200.000 VND

### Ví dụ 3: Tính lương theo môn học

**Tình huống**: Trung tâm muốn trả lương cho giảng viên tiếng Anh với mức 2.000.000 VND/môn.

**Cấu hình**:
1. Tạo quy tắc lương với thông tin:
   - Tên: "Mức lương tiếng Anh theo môn"
   - Loại tính lương: Per Subject (Theo môn học)
   - Mức lương: 2.000.000 VND
   - Phạm vi: Áp dụng cho giảng viên "Nguyễn Văn A"

**Kết quả**:
- Giảng viên Nguyễn Văn A dạy 10 buổi học thuộc 2 môn học khác nhau (Tiếng Anh giao tiếp và Tiếng Anh thương mại)
- Hệ thống tìm thấy quy tắc lương phù hợp
- Tính toán: 2.000.000 VND × 2 môn = 4.000.000 VND

### Ví dụ 4: Quy tắc lương ưu tiên

**Tình huống**: Trung tâm muốn áp dụng mức lương khác nhau cho giảng viên dựa trên môn học và lớp học.

**Cấu hình**:
1. Tạo quy tắc lương A với thông tin:
   - Tên: "Mức lương cơ bản cho tất cả giảng viên"
   - Loại tính lương: Per Lesson (Theo buổi học)
   - Mức lương: 300.000 VND
   - Độ ưu tiên: Thấp
   - Phạm vi: Áp dụng cho tất cả giảng viên

2. Tạo quy tắc lương B với thông tin:
   - Tên: "Mức lương cho giảng viên Nguyễn Văn B"
   - Loại tính lương: Per Lesson (Theo buổi học)
   - Mức lương: 350.000 VND
   - Độ ưu tiên: Bình thường
   - Phạm vi: Áp dụng cho giảng viên "Nguyễn Văn B"

3. Tạo quy tắc lương C với thông tin:
   - Tên: "Mức lương cho môn Tiếng Anh nâng cao"
   - Loại tính lương: Per Lesson (Theo buổi học)
   - Mức lương: 400.000 VND
   - Độ ưu tiên: Bình thường
   - Phạm vi: Áp dụng cho giảng viên "Nguyễn Văn B" và môn học "Tiếng Anh nâng cao"

4. Tạo quy tắc lương D với thông tin:
   - Tên: "Mức lương cho lớp VIP"
   - Loại tính lương: Per Lesson (Theo buổi học)
   - Mức lương: 500.000 VND
   - Độ ưu tiên: Cao
   - Phạm vi: Áp dụng cho giảng viên "Nguyễn Văn B" và lớp học "Lớp VIP"

**Kết quả**:
- Giảng viên Nguyễn Văn B dạy môn "Tiếng Anh nâng cao" trong "Lớp VIP"
- Hệ thống tìm thấy tất cả các quy tắc lương A, B, C, D đều phù hợp
- Quy tắc D có độ ưu tiên cao nhất (Cao) nên được chọn
- Tính toán: 500.000 VND/buổi

## Các trường hợp đặc biệt

### 1. Mức lương cho buổi học cuối tuần

**Tình huống**: Trung tâm muốn trả lương cao hơn cho các buổi học vào cuối tuần.

**Cấu hình**:
1. Tạo quy tắc lương thông thường với thông tin:
   - Tên: "Mức lương ngày thường"
   - Loại tính lương: Per Lesson (Theo buổi học)
   - Mức lương: 400.000 VND
   - Độ ưu tiên: Bình thường
   - Phạm vi: Áp dụng cho tất cả giảng viên

2. Tạo quy tắc lương cuối tuần với thông tin:
   - Tên: "Mức lương cuối tuần"
   - Loại tính lương: Per Lesson (Theo buổi học)
   - Mức lương: 600.000 VND
   - Độ ưu tiên: Cao
   - Phạm vi: Áp dụng cho tất cả giảng viên

**Cách áp dụng**:
- Khi tạo thanh toán, tạo hai khoản thanh toán riêng biệt:
  1. Một khoản cho các buổi học ngày thường, áp dụng quy tắc "Mức lương ngày thường"
  2. Một khoản cho các buổi học cuối tuần, áp dụng quy tắc "Mức lương cuối tuần"

### 2. Mức lương theo cấp độ môn học

**Tình huống**: Trung tâm muốn trả lương khác nhau cho các cấp độ môn học khác nhau.

**Cấu hình**:
1. Tạo quy tắc lương cho cấp độ cơ bản:
   - Tên: "Mức lương cấp độ cơ bản"
   - Loại tính lương: Per Lesson (Theo buổi học)
   - Mức lương: 400.000 VND
   - Phạm vi: Áp dụng cho các môn học cơ bản

2. Tạo quy tắc lương cho cấp độ nâng cao:
   - Tên: "Mức lương cấp độ nâng cao"
   - Loại tính lương: Per Lesson (Theo buổi học)
   - Mức lương: 600.000 VND
   - Phạm vi: Áp dụng cho các môn học nâng cao

### 3. Mức lương theo số lượng học viên

**Tình huống**: Trung tâm muốn trả lương cao hơn cho các lớp học có nhiều học viên.

**Cấu hình**:
1. Tạo quy tắc lương cho lớp nhỏ:
   - Tên: "Mức lương lớp nhỏ (dưới 10 học viên)"
   - Loại tính lương: Per Lesson (Theo buổi học)
   - Mức lương: 400.000 VND
   - Phạm vi: Áp dụng cho các lớp học nhỏ

2. Tạo quy tắc lương cho lớp lớn:
   - Tên: "Mức lương lớp lớn (từ 10 học viên trở lên)"
   - Loại tính lương: Per Lesson (Theo buổi học)
   - Mức lương: 600.000 VND
   - Phạm vi: Áp dụng cho các lớp học lớn

## Câu hỏi thường gặp

### 1. Làm thế nào để áp dụng nhiều quy tắc lương cho cùng một giảng viên?

Bạn có thể tạo nhiều quy tắc lương với các phạm vi áp dụng khác nhau cho cùng một giảng viên. Khi tính lương, hệ thống sẽ tự động chọn quy tắc phù hợp nhất dựa trên độ ưu tiên và phạm vi áp dụng.

### 2. Làm thế nào để thay đổi mức lương theo thời gian?

Bạn có thể tạo các quy tắc lương mới với thời gian hiệu lực khác nhau. Ví dụ, tạo một quy tắc lương với ngày kết thúc là 31/12/2023 và một quy tắc lương mới với ngày bắt đầu là 01/01/2024.

### 3. Làm thế nào để xem quy tắc lương nào đã được áp dụng cho một khoản thanh toán?

Khi xem chi tiết khoản thanh toán, bạn sẽ thấy trường "Salary Rule" hiển thị quy tắc lương đã được áp dụng. Nếu không có quy tắc lương nào được áp dụng, trường "Base Salary" sẽ hiển thị mức lương cơ bản đã được sử dụng.

### 4. Làm thế nào để tính lương cho giảng viên dạy nhiều môn học khác nhau?

Nếu giảng viên dạy nhiều môn học khác nhau với mức lương khác nhau, bạn có thể:
1. Tạo các quy tắc lương riêng cho từng môn học
2. Khi tạo thanh toán, tạo các khoản thanh toán riêng cho từng môn học
3. Hoặc sử dụng loại tính lương "Per Subject" để tính lương theo số môn học

### 5. Làm thế nào để tính lương cho giảng viên dạy cả online và offline?

Bạn có thể tạo các quy tắc lương riêng cho các lớp học online và offline:
1. Tạo quy tắc lương cho lớp học online
2. Tạo quy tắc lương cho lớp học offline
3. Khi tạo thanh toán, hệ thống sẽ tự động áp dụng quy tắc phù hợp dựa trên loại lớp học

### 6. Làm thế nào để tính lương cho giảng viên dạy theo hợp đồng dài hạn?

Đối với giảng viên dạy theo hợp đồng dài hạn, bạn có thể:
1. Sử dụng mức lương cơ bản với loại lương "Monthly" (Theo tháng)
2. Tạo các khoản thanh toán định kỳ hàng tháng
3. Hoặc tạo quy tắc lương với loại tính lương "Per Lesson" hoặc "Hourly" và tính toán dựa trên số buổi học hoặc số giờ dạy thực tế

### 7. Làm thế nào để tính lương cho giảng viên thay thế?

Đối với giảng viên thay thế, bạn có thể:
1. Tạo quy tắc lương riêng cho giảng viên thay thế
2. Hoặc sử dụng mức lương cơ bản của giảng viên thay thế
3. Khi tạo thanh toán, chọn giảng viên thay thế và các buổi học đã dạy thay thế

### 8. Làm thế nào để tính lương cho giảng viên dạy buổi học thử?

Đối với buổi học thử, bạn có thể:
1. Tạo quy tắc lương riêng cho buổi học thử
2. Khi tạo thanh toán, chọn quy tắc lương cho buổi học thử
3. Hoặc tạo một loại buổi học riêng cho buổi học thử và áp dụng quy tắc lương tương ứng
