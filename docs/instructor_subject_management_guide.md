# Hướng dẫn sử dụng chức năng quản lý phân công môn học cho giảng viên

**Ng<PERSON><PERSON> cập nhật: 28/04/2025**

## Giới thiệu

Tài liệu này hướng dẫn cách sử dụng hai chức năng mới trong module eb_lms:

1. **Báo cáo phân tích phân công môn học cho giảng viên**: Cung cấp tổng quan về việc phân công môn học cho giảng viên, gi<PERSON><PERSON> quản lý có cái nhìn tổng quan về năng lực giảng dạy của đội ngũ giảng viên.

2. **Tìm kiếm giảng viên theo môn học**: <PERSON><PERSON><PERSON> thiện chức năng tìm kiếm giảng viên theo môn học và mức độ thành thạo, gi<PERSON><PERSON> dễ dàng tìm giảng viên phù hợp cho một môn học cụ thể.

## 1. B<PERSON>o cáo phân tích phân công môn học cho giảng viên

### 1.1. Truy cập báo cáo

Để truy cập báo cáo phân tích phân công môn học cho giảng viên, bạn có thể thực hiện theo các bước sau:

1. Đăng nhập vào hệ thống Odoo với tài khoản có quyền truy cập module LMS.
2. Điều hướng đến menu **LMS > Báo cáo > Giảng viên > Phân tích phân công môn học**.

### 1.2. Các chế độ xem báo cáo

Báo cáo cung cấp ba chế độ xem khác nhau:

#### 1.2.1. Chế độ xem Pivot

Chế độ xem Pivot cho phép bạn phân tích dữ liệu theo nhiều chiều, bao gồm:
- Phân tích theo môn học và mức độ thành thạo (hàng)
- Phân tích theo giảng viên (cột)
- Các chỉ số đo lường: số buổi học, đánh giá, tổng số giờ, số học viên

Để sử dụng chế độ xem Pivot:
1. Nhấp vào nút **Pivot** trên thanh công cụ.
2. Kéo và thả các trường vào vùng hàng hoặc cột để tùy chỉnh báo cáo.
3. Chọn các chỉ số đo lường bạn muốn hiển thị.

#### 1.2.2. Chế độ xem Graph

Chế độ xem Graph cung cấp biểu đồ trực quan về phân công môn học:
- Biểu đồ cột hiển thị số buổi học theo môn học và mức độ thành thạo
- Có thể chuyển đổi giữa biểu đồ cột, đường và tròn
- Hỗ trợ chế độ xem xếp chồng hoặc nhóm

Để sử dụng chế độ xem Graph:
1. Nhấp vào nút **Graph** trên thanh công cụ.
2. Chọn loại biểu đồ (cột, đường, tròn) từ menu thả xuống.
3. Chọn chế độ xem (xếp chồng, nhóm) từ menu thả xuống.

#### 1.2.3. Chế độ xem List

Chế độ xem List hiển thị chi tiết từng bản ghi phân công môn học:
- Thông tin về giảng viên (mã, tên, loại hợp tác, trạng thái)
- Thông tin về môn học (mã, tên, danh mục)
- Thông tin về kỹ năng (mức độ thành thạo, kinh nghiệm, đánh giá)
- Thống kê (số buổi học, số chứng chỉ, số lớp học, số học viên, tổng số giờ)

Để sử dụng chế độ xem List:
1. Nhấp vào nút **List** trên thanh công cụ.
2. Sử dụng các bộ lọc và nhóm để tìm kiếm thông tin cụ thể.

### 1.3. Lọc và nhóm dữ liệu

Báo cáo cung cấp nhiều tùy chọn lọc và nhóm dữ liệu:

#### 1.3.1. Bộ lọc có sẵn

- **Kỹ năng chính**: Chỉ hiển thị các môn học là kỹ năng chính của giảng viên
- **Mức độ thành thạo**: Lọc theo mức độ thành thạo (Chuyên gia, Nâng cao, Trung cấp, Cơ bản)
- **Đã dạy/Chưa dạy**: Lọc giảng viên đã từng dạy hoặc chưa từng dạy môn học
- **Có chứng chỉ**: Chỉ hiển thị giảng viên có chứng chỉ liên quan đến môn học
- **Trạng thái giảng viên**: Lọc theo trạng thái giảng viên (đang hoạt động, tạm nghỉ, v.v.)
- **Loại hợp tác**: Lọc theo loại hợp tác (toàn thời gian, bán thời gian, hợp đồng)

#### 1.3.2. Nhóm dữ liệu

Bạn có thể nhóm dữ liệu theo:
- Giảng viên
- Môn học
- Danh mục môn học
- Mức độ thành thạo
- Loại hợp tác
- Trạng thái

### 1.4. Các hành động

Từ chế độ xem List, bạn có thể thực hiện các hành động sau:

- **Xem buổi học**: Hiển thị danh sách các buổi học giảng viên đã dạy với môn học này
- **Xem chi tiết giảng viên**: Mở form chi tiết của giảng viên
- **Xem chi tiết môn học**: Mở form chi tiết của môn học
- **Xem chi tiết kỹ năng**: Mở form chi tiết của kỹ năng giảng viên với môn học

## 2. Tìm kiếm giảng viên theo môn học

### 2.1. Truy cập chức năng tìm kiếm

Để truy cập chức năng tìm kiếm giảng viên theo môn học, bạn có thể thực hiện theo các bước sau:

1. Đăng nhập vào hệ thống Odoo với tài khoản có quyền truy cập module LMS.
2. Điều hướng đến menu **LMS > Người dùng > Giảng viên > Tìm kiếm giảng viên theo môn học**.

### 2.2. Sử dụng wizard tìm kiếm

#### 2.2.1. Nhập tiêu chí tìm kiếm

Wizard tìm kiếm cung cấp nhiều tiêu chí để tìm giảng viên phù hợp:

- **Môn học**: Chọn môn học cần tìm giảng viên (bắt buộc)
- **Mức độ thành thạo tối thiểu**: Chọn mức độ thành thạo tối thiểu (Bất kỳ, Cơ bản, Trung cấp, Nâng cao, Chuyên gia)
- **Số năm kinh nghiệm tối thiểu**: Nhập số năm kinh nghiệm tối thiểu
- **Đánh giá tối thiểu**: Nhập điểm đánh giá tối thiểu
- **Yêu cầu chứng chỉ**: Chọn nếu yêu cầu giảng viên có chứng chỉ liên quan
- **Yêu cầu kinh nghiệm giảng dạy**: Chọn nếu yêu cầu giảng viên đã từng dạy môn học này
- **Chỉ kỹ năng chính**: Chọn nếu chỉ tìm giảng viên có môn học này là kỹ năng chính

#### 2.2.2. Kiểm tra lịch trống

Bạn có thể kiểm tra lịch trống của giảng viên:

- **Ngày cần giảng viên**: Chọn ngày cần giảng viên
- **Từ giờ**: Nhập giờ bắt đầu
- **Đến giờ**: Nhập giờ kết thúc
- **Loại hợp tác**: Chọn loại hợp tác (Bất kỳ, Toàn thời gian, Bán thời gian, Hợp đồng)
- **Số kết quả tối đa**: Nhập số kết quả tối đa muốn hiển thị

#### 2.2.3. Thực hiện tìm kiếm

1. Sau khi nhập các tiêu chí tìm kiếm, nhấp vào nút **Tìm kiếm**.
2. Hệ thống sẽ hiển thị danh sách giảng viên phù hợp với các tiêu chí đã chọn.

### 2.3. Xem kết quả tìm kiếm

Kết quả tìm kiếm hiển thị danh sách giảng viên phù hợp với các thông tin sau:

- Tên giảng viên
- Mức độ thành thạo
- Số năm kinh nghiệm
- Đánh giá
- Kỹ năng chính
- Số buổi học đã dạy
- Số chứng chỉ
- Ngày dạy gần nhất

### 2.4. Các hành động trên kết quả tìm kiếm

Từ kết quả tìm kiếm, bạn có thể thực hiện các hành động sau:

- **Xem giảng viên**: Mở form chi tiết của giảng viên
- **Xem kỹ năng**: Mở form chi tiết của kỹ năng giảng viên với môn học
- **Xem buổi học**: Hiển thị danh sách các buổi học giảng viên đã dạy với môn học này
- **Xem báo cáo phân tích**: Mở báo cáo phân tích phân công môn học cho môn học đã chọn

## 3. Ví dụ sử dụng

### 3.1. Tìm giảng viên cho khóa học mới

**Tình huống**: Bạn cần tìm giảng viên cho môn "Lập trình Python" cho một khóa học mới bắt đầu vào ngày 15/05/2025.

**Các bước thực hiện**:

1. Điều hướng đến menu **LMS > Người dùng > Giảng viên > Tìm kiếm giảng viên theo môn học**.
2. Nhập các tiêu chí tìm kiếm:
   - Môn học: Lập trình Python
   - Mức độ thành thạo tối thiểu: Nâng cao
   - Số năm kinh nghiệm tối thiểu: 2
   - Đánh giá tối thiểu: 4
   - Yêu cầu kinh nghiệm giảng dạy: Có
   - Ngày cần giảng viên: 15/05/2025
   - Từ giờ: 18:00
   - Đến giờ: 21:00
3. Nhấp vào nút **Tìm kiếm**.
4. Xem danh sách giảng viên phù hợp và chọn giảng viên phù hợp nhất.

### 3.2. Phân tích năng lực giảng dạy theo môn học

**Tình huống**: Bạn muốn phân tích năng lực giảng dạy của đội ngũ giảng viên theo từng môn học để lập kế hoạch đào tạo.

**Các bước thực hiện**:

1. Điều hướng đến menu **LMS > Báo cáo > Giảng viên > Phân tích phân công môn học**.
2. Chuyển sang chế độ xem Pivot.
3. Kéo trường "Môn học" vào vùng hàng.
4. Kéo trường "Mức độ thành thạo" vào vùng hàng (dưới "Môn học").
5. Chọn các chỉ số đo lường: Số buổi học, Đánh giá, Số giảng viên.
6. Phân tích kết quả để xác định các môn học cần bổ sung giảng viên hoặc nâng cao năng lực.

## 4. Lưu ý và mẹo

- **Cập nhật kỹ năng giảng viên**: Đảm bảo thông tin về kỹ năng giảng viên luôn được cập nhật để kết quả tìm kiếm và báo cáo chính xác.
- **Đánh giá sau mỗi buổi học**: Khuyến khích học viên đánh giá sau mỗi buổi học để cập nhật điểm đánh giá của giảng viên.
- **Sử dụng bộ lọc**: Sử dụng các bộ lọc có sẵn để nhanh chóng tìm thấy thông tin cần thiết trong báo cáo.
- **Xuất báo cáo**: Bạn có thể xuất báo cáo sang Excel để phân tích sâu hơn hoặc chia sẻ với các bên liên quan.
- **Lưu tìm kiếm**: Lưu các tiêu chí tìm kiếm thường xuyên sử dụng để tiết kiệm thời gian.

## 5. Xử lý sự cố

### 5.1. Không tìm thấy giảng viên phù hợp

Nếu không tìm thấy giảng viên phù hợp, hãy thử:
- Giảm bớt các tiêu chí tìm kiếm
- Kiểm tra lại thông tin kỹ năng của giảng viên
- Mở rộng phạm vi tìm kiếm (ví dụ: chọn "Bất kỳ" cho mức độ thành thạo)

### 5.2. Báo cáo không hiển thị dữ liệu

Nếu báo cáo không hiển thị dữ liệu, hãy kiểm tra:
- Đảm bảo có dữ liệu về kỹ năng giảng viên trong hệ thống
- Kiểm tra các bộ lọc đang áp dụng
- Đảm bảo bạn có quyền truy cập vào dữ liệu

## 6. Kết luận

Hai chức năng mới này giúp quản lý có cái nhìn tổng quan về năng lực giảng dạy của đội ngũ giảng viên và dễ dàng tìm kiếm giảng viên phù hợp cho một môn học cụ thể. Việc sử dụng hiệu quả các chức năng này sẽ giúp tối ưu hóa quá trình phân công giảng dạy và nâng cao chất lượng đào tạo.

---

**Liên hệ hỗ trợ**: Nếu bạn cần hỗ trợ thêm, vui lòng liên hệ với đội ngũ hỗ trợ kỹ thuật <NAME_EMAIL> hoặc hotline 1900 1234.
