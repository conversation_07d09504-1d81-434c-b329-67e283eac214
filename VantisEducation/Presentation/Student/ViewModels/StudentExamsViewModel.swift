//
//  StudentExamsViewModel.swift
//  VantisEducation
//
//  Created by Student App on 29/7/25.
//

import Foundation
import SwiftUI

// MARK: - Exam Filter
enum ExamFilter: String, CaseIterable {
    case all = "all"
    case active = "active"
    case completed = "completed"
    case upcoming = "upcoming"
    
    var displayName: String {
        switch self {
        case .all: return "Tất cả"
        case .active: return "Đang diễn ra"
        case .completed: return "Đã hoàn thành"
        case .upcoming: return "Sắp tới"
        }
    }
}

// MARK: - Student Exams ViewModel
@MainActor
class StudentExamsViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var exams: [Quiz] = []
    @Published var isLoading = false
    @Published var error: String?
    @Published var isRefreshing = false
    
    // MARK: - Private Properties
    private let examsService: StudentExamsService
    private var lastLoadTime: Date?
    private let cacheTimeout: TimeInterval = 300 // 5 minutes

    init(examsService: StudentExamsService = StudentExamsService()) {
        self.examsService = examsService
    }
    
    // MARK: - Public Methods

    /// Load exams from API with enhanced attempt status (non-blocking)
    func loadExamsAsync() {
        // Don't block if already loading
        guard !isLoading else { return }

        // Check if we have cached data that's still fresh
        if let lastLoad = lastLoadTime,
           Date().timeIntervalSince(lastLoad) < cacheTimeout,
           !exams.isEmpty {
            print("🎯 StudentExamsViewModel: Using cached data (age: \(Int(Date().timeIntervalSince(lastLoad)))s)")
            return
        }

        Task {
            await loadExams()
        }
    }

    /// Load exams from API with enhanced attempt status
    func loadExams() async {
        guard !isLoading else { return }

        isLoading = true
        error = nil

        do {
            print("🎯 StudentExamsViewModel: Loading exams with attempt status...")
            let response = try await examsService.getStudentExams(includeAttemptStatus: true)

            exams = response.data.quizzes
            lastLoadTime = Date()
            print("✅ StudentExamsViewModel: Successfully loaded \(exams.count) exams")

            // Log exam details for debugging with enhanced info
            for (index, exam) in exams.enumerated() {
                print("  \(index + 1). Exam: \(exam.name)")
                print("     🆔 ID: \(exam.id)")
                print("     📊 State: \(exam.state)")
                print("     ⏰ Active: \(exam.isActive)")
                print("     📅 Expired: \(exam.isExpired)")
                print("     🎯 Current Status: \(exam.currentStatus)")
                print("     👤 Can Attempt: \(exam.canAttempt ?? false)")
                print("     📈 Student Attempts: \(exam.studentAttemptCount ?? 0)")
                print("     🏆 Best Score: \(exam.studentBestScore ?? 0)")
                if let attemptStatus = exam.attemptStatus {
                    print("     📊 Has In Progress: \(attemptStatus.hasInProgress)")
                    print("     📊 Total Attempts: \(attemptStatus.totalAttempts)")
                    print("     📊 Completed Attempts: \(attemptStatus.completedAttempts)")
                }
                print("     " + String(repeating: "-", count: 50))
            }

        } catch let studentExamsError as StudentExamsError {
            self.error = studentExamsError.localizedDescription
            print("❌ StudentExamsViewModel: Failed to load exams - \(studentExamsError)")

            // Handle specific error types
            switch studentExamsError {
            case .unauthorized:
                print("🔐 StudentExamsViewModel: Authentication required")
                // Could trigger re-authentication flow here
            case .networkError:
                print("🌐 StudentExamsViewModel: Network error occurred")
            case .serverError:
                print("🖥️ StudentExamsViewModel: Server error occurred")
            default:
                break
            }

            // No fallback to mock data - show error to user
        } catch {
            self.error = error.localizedDescription
            print("❌ StudentExamsViewModel: Unexpected error - \(error)")

            // No fallback to mock data - show error to user
        }

        isLoading = false
    }
    
    /// Refresh exams data with pull-to-refresh support (force reload)
    func refreshExams() async {
        isRefreshing = true
        lastLoadTime = nil // Clear cache to force reload
        print("🔄 StudentExamsViewModel: Refreshing exams data...")
        await loadExams()
        isRefreshing = false
        print("🔄 StudentExamsViewModel: Refresh completed")
    }

    /// Get filtered exams by status
    func getExamsByStatus(_ status: AttemptStatusType) -> [Quiz] {
        return exams.filter { $0.currentStatus == status }
    }

    /// Get count of exams by status
    func getExamCountByStatus(_ status: AttemptStatusType) -> Int {
        return getExamsByStatus(status).count
    }

    /// Get available exams (can be attempted)
    var availableExams: [Quiz] {
        return exams.filter { $0.canAttempt == true && $0.isActive }
    }

    /// Get completed exams
    var completedExams: [Quiz] {
        return exams.filter { $0.currentStatus == .completed }
    }

    /// Get in-progress exams
    var inProgressExams: [Quiz] {
        return exams.filter { $0.currentStatus == .inProgress }
    }

    /// Get upcoming exams
    var upcomingExams: [Quiz] {
        return exams.filter { $0.isUpcoming }
    }
    

}

// MARK: - Student Exams Service (Enhanced for API Integration)
class StudentExamsService {
    private let apiClient = APIClient.shared

    // MARK: - Quiz List API

    /// Get student quizzes from API with enhanced attempt status
    /// - Parameter includeAttemptStatus: Whether to include detailed attempt status
    /// - Returns: StudentQuizzesResponse with quiz data
    func getStudentExams(includeAttemptStatus: Bool = true) async throws -> StudentQuizzesResponse {
        // Ensure authentication token is set
        if let token = SecureTokenManager.shared.getToken() {
            // APIClient should handle auth headers automatically, but we can verify
            print("🔑 StudentExamsService: Using auth token: \(String(token.prefix(20)))...")
        } else {
            print("⚠️ StudentExamsService: No auth token found")
            throw StudentExamsError.unauthorized
        }

        // Build parameters
        var parameters: [String: Any] = [:]
        if includeAttemptStatus {
            parameters["include_attempt_status"] = "true"
        }

        print("📡 StudentExamsService: Calling API endpoint /students/quizzes/")
        print("📡 StudentExamsService: Parameters: \(parameters)")

        do {
            let response: StudentQuizzesResponse = try await apiClient.request(
                endpoint: "/students/quizzes/",
                method: .GET,
                parameters: parameters.isEmpty ? nil : parameters,
                responseType: StudentQuizzesResponse.self,
                requiresAuth: true
            )

            print("✅ StudentExamsService: API call successful")
            print("✅ StudentExamsService: Received \(response.data.quizzes.count) quizzes")

            return response
        } catch {
            print("❌ StudentExamsService: API call failed: \(error)")
            throw mapAPIError(error)
        }
    }

    // MARK: - Quiz Detail API

    /// Get detailed quiz information with questions and attempt status
    /// - Parameters:
    ///   - quizId: The ID of the quiz
    ///   - includeQuestions: Whether to include full question list
    ///   - includeAttemptStatus: Whether to include attempt status
    /// - Returns: QuizDetailResponse with full quiz data
    func getQuizDetail(
        quizId: Int,
        includeQuestions: Bool = true,
        includeAttemptStatus: Bool = true
    ) async throws -> QuizDetailResponse {
        // Ensure authentication
        guard SecureTokenManager.shared.getToken() != nil else {
            print("⚠️ StudentExamsService: No auth token found for quiz detail")
            throw StudentExamsError.unauthorized
        }

        // Build parameters
        var parameters: [String: Any] = [:]
        if includeQuestions {
            parameters["include_questions"] = "true"
        }
        if includeAttemptStatus {
            parameters["include_attempt_status"] = "true"
        }

        print("📡 StudentExamsService: Getting quiz detail for ID: \(quizId)")
        print("📡 StudentExamsService: Parameters: \(parameters)")

        do {
            let response: QuizDetailResponse = try await apiClient.request(
                endpoint: "/students/quizzes/\(quizId)",
                method: .GET,
                parameters: parameters.isEmpty ? nil : parameters,
                responseType: QuizDetailResponse.self,
                requiresAuth: true
            )

            print("✅ StudentExamsService: Quiz detail loaded successfully")
            print("✅ StudentExamsService: Quiz: \(response.data.name)")
            print("📡 StudentExamsService: RAW Response Data: \(response)")
            print("📡 StudentExamsService: Quiz state: \(response.data.state)")

            if let attemptStatus = response.data.attemptStatus {
                print("📡 StudentExamsService: AttemptStatus found:")
                print("  - hasInProgress: \(attemptStatus.hasInProgress)")
                print("  - totalAttempts: \(attemptStatus.totalAttempts)")
                print("  - completedAttempts: \(attemptStatus.completedAttempts)")
                print("  - canStartNew: \(attemptStatus.options.canStartNew)")
                print("  - canContinue: \(attemptStatus.options.canContinue)")
                print("  - canRestart: \(attemptStatus.options.canRestart)")
                if let restartWarning = attemptStatus.options.restartWarning {
                    print("  - restartWarning: \(restartWarning)")
                }
            } else {
                print("📡 StudentExamsService: No AttemptStatus in response")
            }

            if let questions = response.data.questions {
                print("✅ StudentExamsService: Questions count: \(questions.count)")
            }

            return response
        } catch {
            print("❌ StudentExamsService: Failed to get quiz detail: \(error)")
            throw mapAPIError(error)
        }
    }

    // MARK: - Quiz Start API

    /// Start a quiz attempt with enhanced action control and retry logic
    /// - Parameters:
    ///   - quizId: The ID of the quiz to start
    ///   - action: The start action (continue, restart, new) - no default, must be explicit
    /// - Returns: QuizStartResponse with attempt information
    func startQuiz(quizId: Int, action: QuizStartAction) async throws -> QuizStartResponse {
        // Ensure authentication
        guard SecureTokenManager.shared.getToken() != nil else {
            print("⚠️ StudentExamsService: No auth token found for quiz start")
            throw StudentExamsError.unauthorized
        }

        // Build parameters - send as query parameter for POST request
        let parameters: [String: Any] = [:]
        let endpoint = "/students/quizzes/\(quizId)/start?action=\(action.rawValue)"

        print("📡 StudentExamsService: Starting quiz ID: \(quizId) with action: \(action.rawValue)")
        print("📡 StudentExamsService: Endpoint: \(endpoint)")
        print("📡 StudentExamsService: Full URL will be: \(APIConfiguration.shared.baseURL)\(endpoint)")

        // Implement retry logic for transient errors
        return try await withRetry(maxAttempts: 3, delay: 1.0) { [self] in
            do {
                let response: QuizStartResponse = try await self.apiClient.request(
                    endpoint: endpoint,
                    method: .POST,
                    parameters: parameters.isEmpty ? nil : parameters,
                    responseType: QuizStartResponse.self,
                    requiresAuth: true
                )

                // Validate response
                guard response.success else {
                    print("❌ StudentExamsService: Server returned success=false")
                    throw StudentExamsError.serverError("Server returned error: \(response.message)")
                }

                print("✅ StudentExamsService: Quiz started successfully")
                print("✅ StudentExamsService: Attempt ID: \(response.data.id)")
                print("✅ StudentExamsService: Attempt Number: \(response.data.attemptNumber)")
                print("✅ StudentExamsService: Quiz ID: \(response.data.quizId)")
                print("✅ StudentExamsService: Start Time: \(response.data.startTime)")
                print("✅ StudentExamsService: State: \(response.data.state)")
                print("✅ StudentExamsService: Message: \(response.message)")

                return response
            } catch {
                print("❌ StudentExamsService: Failed to start quiz: \(error)")

                // Log more details about the error
                if let networkError = error as? NetworkError {
                    print("❌ StudentExamsService: NetworkError details: \(networkError)")
                    print("❌ StudentExamsService: NetworkError type: \(type(of: networkError))")
                }

                throw self.mapStartQuizError(error)
            }
        }
    }

    // MARK: - Quiz Submission API

    /// Submit quiz answers and complete the attempt
    /// - Parameters:
    ///   - quizId: The ID of the quiz
    ///   - answers: Dictionary mapping question IDs (as strings) to selected answer IDs
    ///   - questions: Array of quiz questions to get question types
    /// - Returns: QuizSubmissionResponse with results
    func submitQuiz(quizId: Int, answers: [String: [Int]], questions: [QuizQuestion]) async throws -> QuizSubmissionResponse {
        // Ensure authentication
        guard SecureTokenManager.shared.getToken() != nil else {
            print("⚠️ StudentExamsService: No auth token found for quiz submission")
            throw StudentExamsError.unauthorized
        }

        // Convert dictionary format to array format expected by backend
        let answerSubmissions = answers.map { (questionIdString, optionIds) in
            let questionId = Int(questionIdString) ?? 0
            let questionType = getQuestionType(for: questionId, in: questions)

            return QuizAnswerSubmission(
                questionId: questionId,
                questionType: questionType,
                selectedOptionIds: optionIds
            )
        }

        // Create submission request with current timestamp
        let submissionRequest = QuizSubmissionRequest(
            answers: answerSubmissions,
            submitTime: Date()
        )

        print("📡 StudentExamsService: Submitting quiz ID: \(quizId)")
        print("📡 StudentExamsService: Answers count: \(answers.count)")
        print("📡 StudentExamsService: Submit time: \(submissionRequest.submitTime)")

        // Convert to dictionary for APIClient
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        let requestData = try encoder.encode(submissionRequest)
        let requestDict = try JSONSerialization.jsonObject(with: requestData) as? [String: Any] ?? [:]

        // Debug: Print the actual request data
        if let jsonString = String(data: requestData, encoding: .utf8) {
            print("🔍 StudentExamsService: Request JSON: \(jsonString)")
        }

        do {
            let response: QuizSubmissionResponse = try await apiClient.request(
                endpoint: "/students/quizzes/\(quizId)/submit",
                method: .POST,
                parameters: requestDict,
                responseType: QuizSubmissionResponse.self,
                requiresAuth: true
            )

            print("✅ StudentExamsService: Quiz submitted successfully")
            print("✅ StudentExamsService: Score: \(response.data.score)")
            print("✅ StudentExamsService: Passed: \(response.data.isPassed)")
            print("✅ StudentExamsService: Message: \(response.message)")

            return response
        } catch {
            print("❌ StudentExamsService: Failed to submit quiz: \(error)")
            throw mapAPIError(error)
        }
    }

    /// Get question type string for a given question ID
    private func getQuestionType(for questionId: Int, in questions: [QuizQuestion]) -> String {
        guard let question = questions.first(where: { $0.id == questionId }) else {
            return "single_choice" // Default fallback
        }

        switch question.type {
        case .singleChoice:
            return "single_choice"
        case .multipleChoice:
            return "multiple_choice"
        case .trueFalse:
            return "true_false"
        case .essay:
            return "essay"
        case .matching:
            return "matching"
        case .fillInBlank:
            return "fill_in_blank"
        }
    }

    /// Map API errors to domain-specific errors
    private func mapAPIError(_ error: Error) -> StudentExamsError {
        // Handle different types of API errors
        if let networkError = error as? NetworkError {
            switch networkError {
            case .unauthorized:
                return .unauthorized
            case .networkUnavailable:
                return .networkError("No internet connection")
            case .serverError(let code, let message):
                return .serverError("Server error \(code): \(message ?? "Unknown")")
            case .decodingError(_):
                return .invalidResponse
            case .invalidURL:
                return .unknown("Invalid API endpoint")
            case .noData:
                return .invalidResponse
            default:
                return .unknown(error.localizedDescription)
            }
        }

        return .unknown(error.localizedDescription)
    }

    /// Map start quiz specific errors with enhanced context
    private func mapStartQuizError(_ error: Error) -> StudentExamsError {
        print("🔍 StudentExamsService: Mapping error: \(error)")
        print("🔍 StudentExamsService: Error type: \(type(of: error))")

        if let networkError = error as? NetworkError {
            print("🔍 StudentExamsService: NetworkError case: \(networkError)")

            switch networkError {
            case .unauthorized:
                return .unauthorized
            case .networkUnavailable:
                return .networkError("Không có kết nối internet")
            case .serverError(let code, let message):
                print("🔍 StudentExamsService: Server error - Code: \(code), Message: \(message ?? "nil")")

                // Handle specific HTTP status codes for quiz start
                switch code {
                case 400:
                    // Parse specific 400 error messages
                    if let message = message {
                        if message.lowercased().contains("attempt đang dở") || message.lowercased().contains("attempt in progress") {
                            // In-progress attempt exists, should use restart action
                            return .invalidQuizState
                        } else if message.lowercased().contains("hết số lần") || message.lowercased().contains("limit reached") {
                            return .attemptLimitReached
                        } else if message.lowercased().contains("expired") || message.lowercased().contains("hết hạn") {
                            return .quizExpired
                        } else if message.lowercased().contains("not started") || message.lowercased().contains("chưa bắt đầu") {
                            return .invalidQuizState
                        } else {
                            return .serverError("Yêu cầu không hợp lệ: \(message)")
                        }
                    } else {
                        return .invalidQuizState
                    }
                case 401:
                    return .unauthorized
                case 403:
                    return .quizNotAvailable
                case 404:
                    return .quizNotFound
                case 409:
                    // Conflict - usually means attempt state conflict
                    return .invalidQuizState
                case 422:
                    // Unprocessable Entity - validation errors
                    return .serverError("Dữ liệu không hợp lệ: \(message ?? "Vui lòng kiểm tra lại thông tin")")
                case 429:
                    // Too Many Requests
                    return .serverError("Quá nhiều yêu cầu. Vui lòng thử lại sau ít phút.")
                case 500...599:
                    // Server errors
                    return .serverError("Lỗi máy chủ (\(code)). Vui lòng thử lại sau.")
                default:
                    return .serverError("Lỗi không xác định (\(code)): \(message ?? "Vui lòng thử lại")")
                }
            case .decodingError(let underlyingError):
                print("🔍 StudentExamsService: Decoding error: \(underlyingError)")
                return .invalidResponse
            case .invalidURL:
                return .unknown("Đường dẫn API không hợp lệ")
            case .noData:
                return .invalidResponse
            case .accessDenied(let message):
                return .serverError("Không có quyền truy cập: \(message)")
            case .unknown(let underlyingError):
                print("🔍 StudentExamsService: Unknown NetworkError: \(underlyingError)")
                return .unknown("Lỗi kết nối: \(underlyingError.localizedDescription)")
            }
        }

        // Handle URLError for timeout and other network issues
        if let urlError = error as? URLError {
            print("🔍 StudentExamsService: URLError code: \(urlError.code.rawValue)")

            switch urlError.code {
            case .timedOut:
                return .networkError("Kết nối quá thời gian chờ. Vui lòng thử lại.")
            case .notConnectedToInternet:
                return .networkError("Không có kết nối internet. Vui lòng kiểm tra kết nối mạng.")
            case .networkConnectionLost:
                return .networkError("Mất kết nối mạng. Vui lòng thử lại.")
            case .cannotFindHost:
                return .networkError("Không thể tìm thấy máy chủ. Vui lòng kiểm tra kết nối.")
            case .cannotConnectToHost:
                return .networkError("Không thể kết nối đến máy chủ. Vui lòng thử lại sau.")
            default:
                return .networkError("Lỗi kết nối: \(urlError.localizedDescription)")
            }
        }

        // Handle other error types
        print("🔍 StudentExamsService: Unmapped error type: \(type(of: error))")
        return .unknown("Lỗi không xác định: \(error.localizedDescription)")
    }

    // MARK: - Retry Logic

    /// Retry a network operation with exponential backoff
    /// - Parameters:
    ///   - maxAttempts: Maximum number of retry attempts
    ///   - delay: Initial delay between retries (will be doubled each attempt)
    ///   - operation: The async operation to retry
    /// - Returns: Result of the operation
    private func withRetry<T>(
        maxAttempts: Int = 3,
        delay: TimeInterval = 1.0,
        operation: @escaping () async throws -> T
    ) async throws -> T {
        var lastError: Error?

        for attempt in 0..<maxAttempts {
            do {
                return try await operation()
            } catch {
                lastError = error

                // Check if error is retryable
                let isRetryable = isRetryableError(error)
                let isLastAttempt = attempt == maxAttempts - 1

                print("🔄 StudentExamsService: Attempt \(attempt + 1)/\(maxAttempts) failed: \(error)")
                print("🔄 StudentExamsService: Error is retryable: \(isRetryable)")

                if !isRetryable || isLastAttempt {
                    print("❌ StudentExamsService: Not retrying - retryable: \(isRetryable), lastAttempt: \(isLastAttempt)")
                    break
                }

                // Wait before retry with exponential backoff
                let waitTime = delay * pow(2.0, Double(attempt))
                print("⏳ StudentExamsService: Waiting \(waitTime)s before retry...")

                try await Task.sleep(nanoseconds: UInt64(waitTime * 1_000_000_000))
            }
        }

        throw lastError ?? StudentExamsError.unknown("Retry failed with unknown error")
    }

    /// Check if an error is retryable (transient network errors)
    /// - Parameter error: The error to check
    /// - Returns: True if the error should be retried
    private func isRetryableError(_ error: Error) -> Bool {
        if let networkError = error as? NetworkError {
            switch networkError {
            case .networkUnavailable:
                return true
            case .serverError(let code, _):
                // Retry on 5xx server errors and some 4xx errors
                return code >= 500 || code == 408 || code == 429
            default:
                return false
            }
        }

        // Check for URLError (underlying network errors)
        if let urlError = error as? URLError {
            switch urlError.code {
            case .timedOut, .networkConnectionLost, .notConnectedToInternet:
                return true
            default:
                return false
            }
        }

        return false
    }
}

// MARK: - Enhanced API Response Models
struct StudentQuizzesResponse: Codable {
    let success: Bool
    let message: String?
    let data: StudentQuizzesData
    let meta: ResponseMeta?
}

struct StudentQuizzesData: Codable {
    let quizzes: [Quiz]
    let totalCount: Int
    let page: Int?
    let limit: Int?

    enum CodingKeys: String, CodingKey {
        case quizzes
        case totalCount = "total_count"
        case page, limit
    }
}

struct ResponseMeta: Codable {
    let timestamp: String
    let requestId: String?
    let traceId: String?

    enum CodingKeys: String, CodingKey {
        case timestamp
        case requestId = "requestId"
        case traceId = "traceId"
    }
}

// MARK: - Student Exams Errors
enum StudentExamsError: Error, LocalizedError {
    case unauthorized
    case networkError(String)
    case serverError(String)
    case invalidResponse
    case unknown(String)

    // Quiz-specific errors
    case quizNotFound
    case quizNotAvailable
    case attemptLimitReached
    case quizExpired
    case invalidQuizState
    case submissionFailed(String)
    case timeLimitExceeded

    var errorDescription: String? {
        switch self {
        case .unauthorized:
            return "Vui lòng đăng nhập lại"
        case .networkError(let message):
            return "Lỗi kết nối: \(message)"
        case .serverError(let message):
            return "Lỗi máy chủ: \(message)"
        case .invalidResponse:
            return "Phản hồi không hợp lệ từ máy chủ"
        case .unknown(let message):
            return "Lỗi không xác định: \(message)"
        case .quizNotFound:
            return "Không tìm thấy bài thi"
        case .quizNotAvailable:
            return "Bài thi không khả dụng"
        case .attemptLimitReached:
            return "Đã hết số lần làm bài"
        case .quizExpired:
            return "Bài thi đã hết hạn"
        case .invalidQuizState:
            return "Trạng thái bài thi không hợp lệ"
        case .submissionFailed(let message):
            return "Không thể nộp bài: \(message)"
        case .timeLimitExceeded:
            return "Đã hết thời gian làm bài"
        }
    }
}

// PaginationInfo is now defined in Core/Network/Models/PaginationInfo.swift
