import Foundation
import SwiftUI

@MainActor
class StudentDashboardViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // Academic Progress
    @Published var overallProgress: Double = 0.0
    @Published var totalCourses = 0
    @Published var activeCourses = 0
    @Published var completedCourses = 0
    @Published var averageGrade: Double = 0.0
    
    // Upcoming Tasks
    @Published var upcomingAssignments: [Assignment] = []
    @Published var upcomingExams: [Quiz] = []
    @Published var overdueCount = 0
    
    // Today's Schedule
    @Published var todayClasses: [Class] = []
    @Published var nextClass: Class?
    
    // Recent Activity
    @Published var recentActivities: [StudentActivity] = []
    
    // Notifications
    @Published var notificationCount = 0
    @Published var hasUnreadAnnouncements = false
    
    // Study Statistics
    @Published var studyHoursThisWeek: Double = 0.0
    @Published var attendanceRate: Double = 0.0
    @Published var completedLessons = 0
    @Published var totalLessons = 0
    
    // MARK: - Computed Properties
    var progressPercentage: String {
        return String(format: "%.1f%%", overallProgress)
    }
    
    var attendancePercentage: String {
        return String(format: "%.1f%%", attendanceRate)
    }
    
    var gradeDisplay: String {
        return String(format: "%.1f", averageGrade)
    }
    
    var hasUpcomingTasks: Bool {
        return !upcomingAssignments.isEmpty || !upcomingExams.isEmpty
    }
    
    var nextClassTime: String? {
        guard let nextClass = nextClass else { return nil }
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: nextClass.startTime)
    }
    
    // MARK: - Methods
    func loadDashboardData() async {
        isLoading = true
        errorMessage = nil
        
        do {
            // Load all dashboard data concurrently
            async let progressTask = loadAcademicProgress()
            async let tasksTask = loadUpcomingTasks()
            async let scheduleTask = loadTodaySchedule()
            async let activityTask = loadRecentActivity()
            async let statsTask = loadStudyStatistics()
            
            let _ = try await (progressTask, tasksTask, scheduleTask, activityTask, statsTask)
            
        } catch {
            errorMessage = "Không thể tải dữ liệu dashboard: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    private func loadAcademicProgress() async throws {
        // Simulate API call
        try await Task.sleep(nanoseconds: 500_000_000)
        
        // Mock data - replace with actual API call
        overallProgress = 78.5
        totalCourses = 5
        activeCourses = 3
        completedCourses = 2
        averageGrade = 8.7
    }
    
    private func loadUpcomingTasks() async throws {
        // Simulate API call
        try await Task.sleep(nanoseconds: 300_000_000)
        
        // Mock data - replace with actual API call
        upcomingAssignments = Assignment.mockUpcomingAssignments
        upcomingExams = Quiz.mockUpcomingExams
        overdueCount = 2
        notificationCount = 5
        hasUnreadAnnouncements = true
    }
    
    private func loadTodaySchedule() async throws {
        // Simulate API call
        try await Task.sleep(nanoseconds: 400_000_000)
        
        // Mock data - replace with actual API call
        let calendar = Calendar.current
        let today = Date()
        
        todayClasses = Class.mockTodayClasses.filter { classItem in
            calendar.isDate(classItem.scheduledDate, inSameDayAs: today)
        }.sorted { $0.startTime < $1.startTime }
        
        // Find next class
        let now = Date()
        nextClass = todayClasses.first { $0.startTime > now }
    }
    
    private func loadRecentActivity() async throws {
        // Simulate API call
        try await Task.sleep(nanoseconds: 200_000_000)
        
        // Mock data - replace with actual API call
        recentActivities = StudentActivity.mockRecentActivities
    }
    
    private func loadStudyStatistics() async throws {
        // Simulate API call
        try await Task.sleep(nanoseconds: 300_000_000)
        
        // Mock data - replace with actual API call
        studyHoursThisWeek = 12.5
        attendanceRate = 92.3
        completedLessons = 28
        totalLessons = 35
    }
    
    func refreshData() async {
        await loadDashboardData()
    }
}

// MARK: - Mock Data Models
struct StudentActivity: Identifiable {
    let id = UUID()
    let title: String
    let description: String
    let timestamp: Date
    let type: ActivityType
    let iconName: String
    let color: Color
    
    enum ActivityType {
        case assignment, grade, attendance, announcement
    }
    
    static let mockRecentActivities: [StudentActivity] = [
        StudentActivity(
            title: "Bài tập đã nộp",
            description: "Bài tập Toán học - Chương 3",
            timestamp: Date().addingTimeInterval(-3600),
            type: .assignment,
            iconName: "doc.text.fill",
            color: .blue
        ),
        StudentActivity(
            title: "Điểm mới",
            description: "Kiểm tra Vật lý: 8.5/10",
            timestamp: Date().addingTimeInterval(-7200),
            type: .grade,
            iconName: "star.fill",
            color: .orange
        ),
        StudentActivity(
            title: "Điểm danh",
            description: "Lớp Hóa học - Có mặt",
            timestamp: Date().addingTimeInterval(-10800),
            type: .attendance,
            iconName: "checkmark.circle.fill",
            color: .green
        )
    ]
}




