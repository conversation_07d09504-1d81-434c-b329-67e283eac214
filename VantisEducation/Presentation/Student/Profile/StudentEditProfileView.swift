//
//  StudentEditProfileView.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 27/7/25.
//

import SwiftUI

struct StudentEditProfileView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var firstName = ""
    @State private var lastName = ""
    @State private var phone = ""
    @State private var dateOfBirth = Date()
    @State private var showDatePicker = false
    @State private var isLoading = false
    @State private var showError = false
    @State private var errorMessage = ""
    @State private var showSuccess = false
    @FocusState private var focusedField: EditProfileField?

    enum EditProfileField {
        case firstName, lastName, phone
    }

    var body: some View {
        ScrollView {
            VStack(spacing: AppConstants.UI.sectionSpacing) {
                // Custom Header
                headerSection

                // Avatar Section
                avatarSection

                // Personal Information
                personalInfoSection

                // Contact Information
                contactInfoSection

                Spacer(minLength: 100)
            }
            .padding(.horizontal, AppConstants.UI.screenPadding)
            .padding(.top, 20)
        }
        .navigationBarHidden(true)
        .background(AppConstants.Colors.background.ignoresSafeArea())
        .onTapGesture {
            hideKeyboard()
        }
        .onAppear {
            loadCurrentUserData()
        }
        .alert("Error", isPresented: $showError) {
            Button("OK") {
                showError = false
                errorMessage = ""
            }
        } message: {
            Text(errorMessage)
        }
        .alert("Success", isPresented: $showSuccess) {
            Button("OK") {
                dismiss()
            }
        } message: {
            Text("Cập nhật hồ sơ thành công!")
        }
    }

    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            // Back Button
            Button(action: {
                dismiss()
            }) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color.gray.opacity(0.8))
                    .frame(width: 36, height: 36)
                    .background(
                        Circle()
                            .fill(Color.white)
                            .overlay(
                                Circle()
                                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                            )
                            .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
                    )
            }
            .buttonStyle(PlainButtonStyle())

            Spacer()

            VStack(alignment: .center, spacing: 4) {
                Text("Chỉnh sửa hồ sơ")
                    .font(.beVietnamPro(.bold, size: 20))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Text("Cập nhật thông tin của bạn")
                    .font(.beVietnamPro(.medium, size: 14))
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }

            Spacer()

            Button(action: {
                Task {
                    await saveProfile()
                }
            }) {
                HStack(spacing: 6) {
                    if isLoading {
                        ProgressView()
                            .scaleEffect(0.8)
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    } else {
                        Image(systemName: "checkmark")
                            .font(.system(size: 14, weight: .medium))
                        Text("Lưu")
                            .font(.beVietnamPro(.medium, size: 14))
                    }
                }
                .foregroundColor(.white)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            hasChanges && !isLoading ? AppConstants.Colors.primary : Color.gray,
                            hasChanges && !isLoading ? AppConstants.Colors.primaryDeep : Color.gray.opacity(0.8)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .cornerRadius(20)
                .shadow(
                    color: hasChanges && !isLoading ? AppConstants.Colors.primary.opacity(0.3) : Color.clear,
                    radius: 8, x: 0, y: 4
                )
            }
            .disabled(isLoading || !hasChanges)
        }
        .padding(.bottom, 8)
    }



    // MARK: - Avatar Section
    private var avatarSection: some View {
        VStack(spacing: 16) {
            Button(action: {
                // TODO: Implement photo picker
            }) {
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [AppConstants.Colors.primary, AppConstants.Colors.primaryDeep]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 120, height: 120)
                        .shadow(color: AppConstants.Colors.primary.opacity(0.3), radius: 15, x: 0, y: 8)

                    if let avatarUrl = authViewModel.currentUser?.avatar {
                        AsyncImage(url: URL(string: avatarUrl)) { image in
                            image
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                                .frame(width: 110, height: 110)
                                .clipShape(Circle())
                        } placeholder: {
                            Text(authViewModel.currentUser?.initials ?? "U")
                                .font(.beVietnamPro(.bold, size: 32))
                                .foregroundColor(.white)
                        }
                    } else {
                        Text(authViewModel.currentUser?.initials ?? "U")
                            .font(.beVietnamPro(.bold, size: 32))
                            .foregroundColor(.white)
                    }

                    // Camera overlay
                    VStack {
                        Spacer()
                        HStack {
                            Spacer()
                            Circle()
                                .fill(Color.white)
                                .frame(width: 36, height: 36)
                                .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                                .overlay(
                                    Image(systemName: "camera.fill")
                                        .font(.system(size: 16, weight: .medium))
                                        .foregroundColor(AppConstants.Colors.primary)
                                )
                                .offset(x: -8, y: -8)
                        }
                    }
                    .frame(width: 120, height: 120)
                }
            }
            .buttonStyle(PlainButtonStyle())

            Text("Nhấn để thay đổi ảnh")
                .font(.caption)
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
    }

    // MARK: - Personal Information Section
    private var personalInfoSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Thông tin cá nhân")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)

            VStack(spacing: AppConstants.UI.itemSpacing) {
                // First Name
                VStack(alignment: .leading, spacing: 8) {
                    Text("Tên")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textPrimary)

                    TextField("Nhập tên của bạn", text: $firstName)
                        .textFieldStyle()
                        .textContentType(.givenName)
                        .focused($focusedField, equals: .firstName)
                        .onSubmit {
                            focusedField = .lastName
                        }
                }

                // Last Name
                VStack(alignment: .leading, spacing: 8) {
                    Text("Họ")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textPrimary)

                    TextField("Nhập họ của bạn", text: $lastName)
                        .textFieldStyle()
                        .textContentType(.familyName)
                        .focused($focusedField, equals: .lastName)
                        .onSubmit {
                            focusedField = .phone
                        }
                }

                // Date of Birth
                VStack(alignment: .leading, spacing: 8) {
                    Text("Ngày sinh")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textPrimary)

                    Button(action: {
                        showDatePicker = true
                    }) {
                        HStack {
                            Text(dateOfBirth.formatted(date: .abbreviated, time: .omitted))
                                .foregroundColor(AppConstants.Colors.textPrimary)

                            Spacer()

                            Image(systemName: "calendar")
                                .foregroundColor(AppConstants.Colors.textSecondary)
                        }
                        .padding()
                        .background(Color.white)
                        .cornerRadius(AppConstants.UI.cornerRadius)
                        .overlay(
                            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                                .stroke(AppConstants.Colors.border, lineWidth: 0.5)
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
        .padding()
        .background(AppConstants.Colors.surfaceSecondary)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(AppConstants.Colors.border, lineWidth: 0.5)
        )
        .sheet(isPresented: $showDatePicker) {
            DatePickerSheet(selectedDate: $dateOfBirth)
        }
    }

    // MARK: - Contact Information Section
    private var contactInfoSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Thông tin liên hệ")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(AppConstants.Colors.textPrimary)

            VStack(spacing: AppConstants.UI.itemSpacing) {
                // Email (Read-only)
                VStack(alignment: .leading, spacing: 8) {
                    Text("Email")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textPrimary)

                    HStack {
                        Text(authViewModel.currentUser?.email ?? "")
                            .foregroundColor(AppConstants.Colors.textSecondary)

                        Spacer()

                        Text("Đã xác minh")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(AppConstants.Colors.success)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(AppConstants.Colors.success.opacity(0.1))
                            .cornerRadius(8)
                    }
                    .padding()
                    .background(Color.white)
                    .cornerRadius(AppConstants.UI.cornerRadius)
                    .overlay(
                        RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                            .stroke(AppConstants.Colors.border, lineWidth: 0.5)
                    )
                }

                // Phone
                VStack(alignment: .leading, spacing: 8) {
                    Text("Số điện thoại")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textPrimary)

                    TextField("Nhập số điện thoại của bạn", text: $phone)
                        .textFieldStyle()
                        .keyboardType(.phonePad)
                        .textContentType(.telephoneNumber)
                        .focused($focusedField, equals: .phone)

                    if !phone.isEmpty && !phone.isValidPhone {
                        Text("Vui lòng nhập số điện thoại hợp lệ")
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.error)
                    }
                }
            }
        }
        .padding()
        .background(AppConstants.Colors.surfaceSecondary)
        .cornerRadius(AppConstants.UI.cornerRadius)
        .overlay(
            RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                .stroke(AppConstants.Colors.border, lineWidth: 0.5)
        )
    }

    // MARK: - Computed Properties
    private var hasChanges: Bool {
        guard let user = authViewModel.currentUser else { return false }

        return firstName != (user.firstName ?? "") ||
               lastName != (user.lastName ?? "") ||
               phone != (user.phone ?? "")
    }

    // MARK: - Helper Methods
    private func loadCurrentUserData() {
        guard let user = authViewModel.currentUser else { return }

        firstName = user.firstName ?? ""
        lastName = user.lastName ?? ""
        phone = user.phone ?? ""

        if let dob = user.dateOfBirth {
            dateOfBirth = dob
        }
    }

    private func saveProfile() async {
        guard hasChanges else {
            dismiss()
            return
        }

        isLoading = true

        do {
            let request = UpdateProfileRequest(
                firstName: firstName.isEmpty ? nil : firstName,
                lastName: lastName.isEmpty ? nil : lastName,
                phone: phone.isEmpty ? nil : phone,
                dateOfBirth: dateOfBirth,
                avatar: nil // TODO: Implement avatar upload
            )

            let authRepository = AuthRepository()
            let _ = try await authRepository.updateProfile(request: request)

            // Update the current user in auth state
            await authViewModel.refreshUserData()

            showSuccess = true

        } catch {
            errorMessage = error.localizedDescription
            showError = true
        }

        isLoading = false
    }
}



#Preview {
    StudentEditProfileView()
        .environmentObject(AuthViewModel())
}
