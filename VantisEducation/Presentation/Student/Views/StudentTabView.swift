//
//  StudentTabView.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 27/7/25.
//

import SwiftUI

struct StudentTabView: View {
    @EnvironmentObject var authViewModel: AuthViewModel

    private var tabs: [TabItem] {
        [
            TabItem(id: 0, title: "Trang chủ", icon: "house", selectedIcon: "house.fill"),
            TabItem(id: 1, title: "<PERSON>h<PERSON><PERSON> học", icon: "book", selectedIcon: "book.fill"),
            TabItem(id: 2, title: "<PERSON>ị<PERSON> học", icon: "calendar.circle", selectedIcon: "calendar.circle.fill"),
            TabItem(id: 3, title: "<PERSON>ài thi", icon: "doc.text", selectedIcon: "doc.text.fill"),
            TabItem(id: 4, title: "<PERSON><PERSON> sơ", icon: "person", selectedIcon: "person.fill")
        ]
    }

    var body: some View {
        ModernTabView(tabs: tabs) { selectedTab in
            switch selectedTab {
            case 0:
                StudentDashboardView()
                    .environmentObject(authViewModel)
            case 1:
                StudentCoursesView()
            case 2:
                StudentScheduleView()
            case 3:
                StudentExamsView()
            case 4:
                StudentProfileView()
                    .environmentObject(authViewModel)
            default:
                StudentDashboardView()
                    .environmentObject(authViewModel)
            }
        }
        .onAppear {
            print("🎯 StudentTabView: onAppear called - ModernTabView should be visible now")
        }
    }

}

// MARK: - Student Courses View
struct StudentCoursesView: View {
    @StateObject private var viewModel = StudentCoursesViewModel()
    @State private var showingSearch = false
    @State private var showingFilters = false

    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 0) {
                    // Header - Use the same modern header as schedule view
                    coursesHeaderSection

                    // Search and Filter Section
                    if showingSearch {
                        searchSection
                    }

                    // Content
                    contentSection
                }
            }
            .navigationBarHidden(true)
            .background(
                Color.white
                    .ignoresSafeArea()
            )
        }
        .task {
            await viewModel.loadCourses(refresh: true)
        }
        .refreshable {
            await viewModel.refreshCourses()
        }
        .alert("Lỗi", isPresented: $viewModel.showError) {
            Button("OK") {
                viewModel.showError = false
            }
        } message: {
            Text(viewModel.errorMessage ?? "Đã xảy ra lỗi")
        }
    }

    // MARK: - Header Section
    private var coursesHeaderSection: some View {
        VStack(spacing: 16) {
            // Title - left aligned
            VStack(alignment: .leading, spacing: 4) {
                Text("Khóa học")
                    .font(.beVietnamPro(.bold, size: 24))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Text("Khóa học của tôi")
                    .font(.beVietnamPro(.medium, size: 14))
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            .frame(maxWidth: .infinity, alignment: .leading)

            // Search button (top right)
            HStack {
                Spacer()

                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        showingSearch.toggle()
                    }
                }) {
                    Image(systemName: showingSearch ? "xmark" : "magnifyingglass")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(AppConstants.Colors.primary)
                        .frame(width: 36, height: 36)
                        .background(
                            Circle()
                                .fill(Color.white)
                                .overlay(
                                    Circle()
                                        .stroke(AppConstants.Colors.primary.opacity(0.3), lineWidth: 1)
                                )
                                .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
                        )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 8)
        .padding(.bottom, 16)
        .background(Color.white)
    }

    // MARK: - Search Section
    private var searchSection: some View {
        VStack(spacing: 12) {
            // Search Bar
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.gray)

                TextField("Tìm kiếm khóa học...", text: $viewModel.searchText)
                    .textFieldStyle(PlainTextFieldStyle())

                if !viewModel.searchText.isEmpty {
                    Button(action: {
                        viewModel.clearSearch()
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.gray)
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(0.1))
            )

            // Filter Tabs
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(CourseFilterStatus.allCases, id: \.self) { filter in
                        Button(action: {
                            viewModel.applyFilter(filter)
                        }) {
                            Text(filter.displayName)
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(
                                    viewModel.selectedFilter == filter
                                    ? .white
                                    : AppConstants.Colors.textSecondary
                                )
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                                .background(
                                    RoundedRectangle(cornerRadius: 20)
                                        .fill(
                                            viewModel.selectedFilter == filter
                                            ? AppConstants.Colors.primary
                                            : Color.gray.opacity(0.1)
                                        )
                                )
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                .padding(.horizontal, 20)
            }
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 16)
        .background(Color.white)
    }

    // MARK: - Content Section
    private var contentSection: some View {
        VStack(spacing: 20) {
            if viewModel.isLoading && viewModel.courses.isEmpty {
                loadingView
            } else if viewModel.filteredCourses.isEmpty {
                emptyStateView
            } else {
                coursesListView
            }
        }
        .padding(.horizontal, 20)
    }

    // MARK: - Loading View
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
                .tint(AppConstants.Colors.primary)

            Text("Đang tải khóa học...")
                .font(.beVietnamPro(.medium, size: 16))
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 60)
    }

    // MARK: - Empty State View
    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "book.closed")
                .font(.system(size: 48))
                .foregroundColor(AppConstants.Colors.textSecondary.opacity(0.6))

            Text("Chưa có khóa học")
                .font(.beVietnamPro(.bold, size: 18))
                .foregroundColor(AppConstants.Colors.textPrimary)

            Text("Bạn chưa đăng ký khóa học nào. Hãy khám phá các khóa học có sẵn.")
                .font(.beVietnamPro(.medium, size: 14))
                .foregroundColor(AppConstants.Colors.textSecondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 20)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 60)
    }

    // MARK: - Courses List View (Enhanced Version)
    private var coursesListView: some View {
        LazyVStack(spacing: 16) {
            ForEach(viewModel.filteredCourses) { course in
                StudentCourseCard(course: course)
                    .onTapGesture {
                        // Handle course tap
                        print("🎯 Tapped course: \(course.name)")
                    }
            }

            // Load more button
            if viewModel.hasMorePages && !viewModel.isLoading {
                Button(action: {
                    Task {
                        await viewModel.loadMoreCourses()
                    }
                }) {
                    HStack {
                        if viewModel.isLoading {
                            ProgressView()
                                .scaleEffect(0.8)
                        }
                        Text("Tải thêm")
                            .font(.beVietnamPro(.medium, size: 16))
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                    .padding(.vertical, 12)
                }
                .buttonStyle(PlainButtonStyle())
            }

            // Add bottom padding to ensure content can be scrolled fully
            Spacer()
                .frame(height: 100)
        }
    }
}

// MARK: - Student Schedule View
struct StudentScheduleView: View {
    @StateObject private var viewModel = StudentScheduleViewModel()
    @State private var activeFilter: ScheduleFilter = .all
    @State private var selectedLesson: StudentLesson?
    @State private var showDetailBottomSheet = false

    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 0) {
                    // Header - Use the new modern header instead of the old blue one
                    modernHeaderSection

                    // Content
                    contentSection
                }
            }
            .navigationBarHidden(true)
            .background(
                Color.white
                    .ignoresSafeArea()
            )
            .overlay(
                // Bottom Sheet
                bottomSheetOverlay
            )
        }
        .task {
            print("🎯 StudentScheduleView: Task started - loading lessons...")
            await viewModel.loadLessons()
            print("🎯 StudentScheduleView: Task completed - lessons count: \(viewModel.lessons.count)")
        }
    }

    // MARK: - Computed Properties

    private var todayLessonsCount: Int {
        viewModel.lessons.filter { lesson in
            lesson.isToday || lesson.isLive
        }.count
    }

    private var upcomingLessonsCount: Int {
        viewModel.lessons.filter { lesson in
            lesson.isUpcoming
        }.count
    }

    // MARK: - Header Section

    private var headerSection: some View {
        VStack(spacing: 16) {
            // Title - left aligned
            VStack(alignment: .leading, spacing: 4) {
                Text("Lịch học")
                    .font(.beVietnamPro(.bold, size: 24))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Text("Theo dõi và quản lý buổi học")
                    .font(.beVietnamPro(.medium, size: 14))
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(.horizontal, 20)
        .padding(.top, 8)
        .padding(.bottom, 16)
        .background(Color.white)
    }



    // MARK: - Tab Switcher Card

    private var tabSwitcherCard: some View {
        VStack(spacing: 20) {
            HStack {
                Text("Lịch học")
                    .font(.beVietnamPro(.bold, size: 20))
                    .foregroundColor(AppConstants.Colors.textPrimary)
                Spacer()
            }

            // Filter tabs - Temporarily disabled
            // ScheduleFilterTabs(
            //     filters: ScheduleFilter.allCases,
            //     activeFilter: viewModel.activeFilter,
            //     onFilterTap: { filter in
            //         viewModel.setActiveFilter(filter)
            //     }
            // )
        }
        .padding(24)
        .background(Color.white)
        .cornerRadius(24)
        .shadow(color: Color.black.opacity(0.08), radius: 20, x: 0, y: 8)
        .overlay(
            RoundedRectangle(cornerRadius: 24)
                .stroke(Color.black.opacity(0.05), lineWidth: 1)
        )
    }

    // MARK: - Content Section

    private var contentSection: some View {
        VStack(spacing: 24) {
            // Simple lessons list
            simpleLessonsListView
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 100) // Add bottom padding for tab bar
    }

    // MARK: - Simple Lessons List View

    private var simpleLessonsListView: some View {
        VStack(spacing: 20) {
            // Header
            HStack {
                Text("Danh sách buổi học")
                    .font(.beVietnamPro(.bold, size: 18))
                    .foregroundColor(AppConstants.Colors.textPrimary)
                Spacer()
            }

            // Content - Simple lesson list
            if viewModel.isLoading && viewModel.lessons.isEmpty {
                loadingView
            } else if viewModel.lessons.isEmpty {
                emptyStateView
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(viewModel.lessons) { lesson in
                        SimpleLessonCard(lesson: lesson) {
                            // viewModel.selectLesson(lesson) - Temporarily disabled
                            print("Selected lesson: \(lesson.name)")
                        }
                    }
                }
            }
        }
    }





    // MARK: - Loading View

    private var loadingView: some View {
        HStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
                .tint(AppConstants.Colors.primary)

            VStack(alignment: .leading, spacing: 4) {
                Text("Đang tải lịch học...")
                    .font(.beVietnamPro(.semiBold, size: 16))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Text("Vui lòng đợi trong giây lát")
                    .font(.beVietnamPro(.medium, size: 14))
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }

            Spacer()
        }
        .padding(20)
        .background(Color.white)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.04), radius: 8, x: 0, y: 4)
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(Color.black.opacity(0.06), lineWidth: 1)
        )
    }

    // MARK: - Empty State View

    private var emptyStateView: some View {
        HStack(spacing: 16) {
            // Modern icon with background
            ZStack {
                RoundedRectangle(cornerRadius: 12)
                    .fill(AppConstants.Colors.secondary.opacity(0.15))
                    .frame(width: 44, height: 44)

                Image(systemName: "calendar.badge.exclamationmark")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(AppConstants.Colors.secondary)
            }

            VStack(alignment: .leading, spacing: 4) {
                Text("Không có buổi học")
                    .font(.beVietnamPro(.semiBold, size: 16))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Text("Bạn chưa được phân vào lớp học nào")
                    .font(.beVietnamPro(.medium, size: 14))
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }

            Spacer()

            Button(action: {
                Task {
                    await viewModel.loadLessons()
                }
            }) {
                Image(systemName: "arrow.clockwise")
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(AppConstants.Colors.primary)
            }
        }
        .padding(20)
        .background(Color.white)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.04), radius: 8, x: 0, y: 4)
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(Color.black.opacity(0.06), lineWidth: 1)
        )
    }

    // MARK: - Bottom Sheets

    private var bottomSheets: some View {
        ZStack {
            // Bottom sheets temporarily disabled for simplification
            EmptyView()

            // Detail bottom sheet - Temporarily disabled
            // if viewModel.showDetailBottomSheet {
            //     Color.black.opacity(0.3)
            //         .ignoresSafeArea()
            //         .onTapGesture {
            //             viewModel.closeDetailBottomSheet()
            //         }
            //
            //     VStack {
            //         Spacer()
            //
            //         ScheduleDetailBottomSheet(
            //             item: viewModel.selectedItem,
            //             isVisible: viewModel.showDetailBottomSheet,
            //             onClose: {
            //                 viewModel.closeDetailBottomSheet()
            //             },
            //             onCheckin: {
            //                 guard let item = viewModel.selectedItem else { return }
            //                 viewModel.closeDetailBottomSheet()
            //                 viewModel.showCheckinConfirmation(for: item)
            //             },
            //             onQuiz: {
            //                 // TODO: Navigate to quiz screen
            //                 print("Navigate to quiz for lesson: \(viewModel.selectedItem?.id ?? "")")
            //             }
            //         )
            //     }
            // }

            // Checkin confirmation - Temporarily disabled
            // if viewModel.showCheckinConfirmation {
            //     Color.black.opacity(0.3)
            //         .ignoresSafeArea()
            //
            //     VStack {
            //         Spacer()
            //
            //         CheckinConfirmationBottomSheet(
            //             item: viewModel.selectedItem,
            //             isVisible: viewModel.showCheckinConfirmation,
            //             isProcessing: viewModel.isProcessingAttendance,
            //             onClose: {
            //                 viewModel.closeCheckinConfirmation()
            //             },
            //             onConfirm: {
            //                 Task {
            //                     await viewModel.confirmCheckin()
            //                 }
            //             }
            //         )
            //     }
            // }

            // Attendance result - Temporarily disabled
            // if viewModel.showAttendanceResult {
            //     Color.black.opacity(0.3)
            //         .ignoresSafeArea()
            //
            //     VStack {
            //         Spacer()
            //
            //         AttendanceResultBottomSheet(
            //             result: viewModel.attendanceResult,
            //             isVisible: viewModel.showAttendanceResult,
            //             onClose: {
            //                 viewModel.closeAttendanceResult()
            //             }
            //         )
            //     }
            // }
        }
    }

    // MARK: - Missing Computed Properties
    private var modernHeaderSection: some View {
        VStack(spacing: 16) {
            // Title - left aligned
            VStack(alignment: .leading, spacing: 4) {
                Text("Lịch học")
                    .font(.beVietnamPro(.bold, size: 24))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Text("Theo dõi và quản lý buổi học")
                    .font(.beVietnamPro(.medium, size: 14))
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(.horizontal, 20)
        .padding(.top, 8)
        .padding(.bottom, 16)
        .background(Color.white)
    }

    private var filterTabsSection: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(["Tất cả", "Hôm nay", "Tuần này", "Tháng này"], id: \.self) { filter in
                    Text(filter)
                        .font(.beVietnamPro(.medium, size: 14))
                        .foregroundColor(filter == "Tất cả" ? .white : AppConstants.Colors.textSecondary)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(filter == "Tất cả" ? AppConstants.Colors.primary : Color.gray.opacity(0.1))
                        .cornerRadius(20)
                }
            }
            .padding(.horizontal, 20)
        }
        .padding(.bottom, 16)
        .background(Color.white)
    }

    private var bottomSheetOverlay: some View {
        Group {
            if showDetailBottomSheet {
                Color.black.opacity(0.3)
                    .edgesIgnoringSafeArea(.all)
                    .onTapGesture {
                        showDetailBottomSheet = false
                    }
            }
        }
    }
}

// MARK: - Student Exams View
struct StudentExamsView: View {
    @StateObject private var viewModel = StudentExamsViewModel()
    @State private var activeFilter: ExamFilter = .all
    @State private var selectedExam: Quiz?
    @State private var hasAppeared = false

    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 0) {
                    // Header - Use the same modern header as schedule view
                    examsHeaderSection

                    // Content
                    contentSection
                }
            }
            .navigationBarHidden(true)
            .background(
                Color.white
                    .ignoresSafeArea()
            )
        }
        .onAppear {
            // Only load once when first appearing
            if !hasAppeared {
                hasAppeared = true
                print("🎯 StudentExamsView: First onAppear - starting async load...")
                viewModel.loadExamsAsync()
            }
        }
        .sheet(item: $selectedExam) { exam in
            let _ = print("🎯 Sheet presentation triggered for exam: \(exam.name) (ID: \(exam.id))")
            ExamDetailView(exam: exam)
        }
    }

    // MARK: - Header Section
    private var examsHeaderSection: some View {
        VStack(spacing: 16) {
            // Title - left aligned
            VStack(alignment: .leading, spacing: 4) {
                Text("Bài thi")
                    .font(.beVietnamPro(.bold, size: 24))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Text("Danh sách bài thi và kết quả")
                    .font(.beVietnamPro(.medium, size: 14))
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(.horizontal, 20)
        .padding(.top, 8)
        .padding(.bottom, 16)
        .background(Color.white)
    }

    // MARK: - Content Section
    private var contentSection: some View {
        VStack(spacing: 20) {
            // Stats Cards - Hidden as requested
            // statsSection

            // Filter Tabs - Hidden as requested
            // filterTabsSection

            // Exams List
            examsListSection
        }
        .padding(.horizontal, 20)
        .padding(.top, 8)
    }

    // MARK: - Stats Section
    private var statsSection: some View {
        LazyVGrid(columns: [
            GridItem(.flexible()),
            GridItem(.flexible())
        ], spacing: 16) {
            ExamStatCard(
                title: "Tổng bài thi",
                value: "\(viewModel.exams.count)",
                icon: "doc.text.fill",
                color: .blue
            )

            ExamStatCard(
                title: "Đã hoàn thành",
                value: "\(completedExamsCount)",
                icon: "checkmark.circle.fill",
                color: .green
            )

            ExamStatCard(
                title: "Đang diễn ra",
                value: "\(activeExamsCount)",
                icon: "clock.fill",
                color: .orange
            )

            ExamStatCard(
                title: "Sắp tới",
                value: "\(upcomingExamsCount)",
                icon: "calendar.badge.clock",
                color: .purple
            )
        }
    }

    // MARK: - Filter Tabs Section
    private var filterTabsSection: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(ExamFilter.allCases, id: \.self) { filter in
                    FilterTab(
                        title: filter.displayName,
                        isSelected: activeFilter == filter,
                        count: getFilterCount(filter)
                    ) {
                        activeFilter = filter
                    }
                }
            }
            .padding(.horizontal, 20)
        }
    }

    // MARK: - Exams List Section
    private var examsListSection: some View {
        VStack(spacing: 16) {
            // Header
            HStack {
                Text("Danh sách bài thi")
                    .font(.beVietnamPro(.bold, size: 18))
                    .foregroundColor(AppConstants.Colors.textPrimary)
                Spacer()
            }

            // Content
            if viewModel.isLoading && viewModel.exams.isEmpty {
                loadingView
            } else if filteredExams.isEmpty {
                emptyStateView
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(filteredExams) { exam in
                        ExamCard(exam: exam) {
                            print("🎯 ExamCard tapped: \(exam.name) (ID: \(exam.id))")
                            selectedExam = exam
                            print("🎯 selectedExam set to: \(selectedExam?.name ?? "nil")")
                        }
                    }
                }
            }
        }
    }

    // MARK: - Helper Views
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
                .tint(AppConstants.Colors.primary)

            Text("Đang tải bài thi...")
                .font(.beVietnamPro(.medium, size: 16))
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 40)
    }

    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "doc.text")
                .font(.system(size: 48))
                .foregroundColor(AppConstants.Colors.textTertiary)

            Text("Không có bài thi nào")
                .font(.beVietnamPro(.semiBold, size: 18))
                .foregroundColor(AppConstants.Colors.textPrimary)

            Text("Hiện tại chưa có bài thi nào được giao cho bạn")
                .font(.beVietnamPro(.medium, size: 14))
                .foregroundColor(AppConstants.Colors.textSecondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 40)
    }

    // MARK: - Computed Properties
    private var filteredExams: [Quiz] {
        switch activeFilter {
        case .all:
            return viewModel.exams
        case .active:
            return viewModel.exams.filter { $0.isActive }
        case .completed:
            return viewModel.exams.filter { $0.isExpired }
        case .upcoming:
            return viewModel.exams.filter { !$0.isActive && !$0.isExpired }
        }
    }

    private var completedExamsCount: Int {
        viewModel.exams.filter { $0.isExpired }.count
    }

    private var activeExamsCount: Int {
        viewModel.exams.filter { $0.isActive }.count
    }

    private var upcomingExamsCount: Int {
        viewModel.exams.filter { !$0.isActive && !$0.isExpired }.count
    }

    private func getFilterCount(_ filter: ExamFilter) -> Int {
        switch filter {
        case .all:
            return viewModel.exams.count
        case .active:
            return activeExamsCount
        case .completed:
            return completedExamsCount
        case .upcoming:
            return upcomingExamsCount
        }
    }
}

// MARK: - Simple Lesson Card

struct SimpleLessonCard: View {
    let lesson: StudentLesson
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            HStack(alignment: .top, spacing: 12) {
                // Thumbnail icon
                thumbnailIcon

                // Content
                VStack(alignment: .leading, spacing: 8) {
                    // Header with title and status
                    HStack(alignment: .top) {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(lesson.name)
                                .font(.beVietnamPro(.bold, size: 16))
                                .foregroundColor(AppConstants.Colors.textPrimary)
                                .multilineTextAlignment(.leading)
                                .lineLimit(2)

                            Text(lesson.className)
                                .font(.beVietnamPro(.medium, size: 13))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .lineLimit(1)
                        }

                        Spacer()

                        // Status badge
                        statusBadge
                    }

                    // Time and instructor info
                    VStack(alignment: .leading, spacing: 6) {
                        // Time info
                        HStack(spacing: 6) {
                            Image(systemName: "clock")
                                .font(.system(size: 11, weight: .medium))
                                .foregroundColor(AppConstants.Colors.textSecondary)

                            Text(timeText)
                                .font(.beVietnamPro(.medium, size: 11))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .lineLimit(1)
                        }

                        // Instructor
                        if !lesson.instructorName.isEmpty {
                            HStack(spacing: 6) {
                                Image(systemName: "person")
                                    .font(.system(size: 11, weight: .medium))
                                    .foregroundColor(AppConstants.Colors.textSecondary)

                                Text(lesson.instructorName)
                                    .font(.beVietnamPro(.medium, size: 11))
                                    .foregroundColor(AppConstants.Colors.textSecondary)
                                    .lineLimit(1)
                            }
                        }
                    }
                }
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.black.opacity(0.05), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    private var thumbnailIcon: some View {
        ZStack {
            // Outer circle (light background)
            Circle()
                .fill(AppConstants.Colors.primary.opacity(0.1))
                .frame(width: 64, height: 64)

            // Inner circle (main background)
            Circle()
                .fill(AppConstants.Colors.primary)
                .frame(width: 48, height: 48)

            // Icon
            Image(systemName: lessonIcon)
                .font(.system(size: 20, weight: .medium))
                .foregroundColor(.white)
        }
    }

    private var lessonIcon: String {
        // Choose icon based on lesson type or name
        let lessonNameLower = lesson.name.lowercased()
        let classNameLower = lesson.className.lowercased()
        let combinedText = "\(lessonNameLower) \(classNameLower)"

        // Tài chính & Kinh doanh
        if combinedText.contains("tài chính") || combinedText.contains("finance") ||
           combinedText.contains("kế toán") || combinedText.contains("accounting") {
            return "books.vertical.fill"
        }
        // Quản lý & Lãnh đạo
        else if combinedText.contains("ceo") || combinedText.contains("quản lý") ||
                combinedText.contains("lãnh đạo") || combinedText.contains("management") {
            return "book.pages.fill"
        }
        // Marketing & Bán hàng
        else if combinedText.contains("marketing") || combinedText.contains("bán hàng") ||
                combinedText.contains("sales") || combinedText.contains("quảng cáo") {
            return "text.book.closed.fill"
        }
        // Công nghệ & IT
        else if combinedText.contains("công nghệ") || combinedText.contains("tech") ||
                combinedText.contains("it") || combinedText.contains("phần mềm") ||
                combinedText.contains("digital") {
            return "book.and.wrench.fill"
        }
        // Nhân sự & HR
        else if combinedText.contains("nhân sự") || combinedText.contains("hr") ||
                combinedText.contains("tuyển dụng") || combinedText.contains("recruitment") {
            return "person.text.rectangle.fill"
        }
        // Đào tạo & Phát triển
        else if combinedText.contains("đào tạo") || combinedText.contains("training") ||
                combinedText.contains("phát triển") || combinedText.contains("development") {
            return "graduationcap.fill"
        }
        // Kiểm tra & Đánh giá
        else if combinedText.contains("test") || combinedText.contains("kiểm tra") ||
                combinedText.contains("đánh giá") || combinedText.contains("exam") {
            return "pencil.and.list.clipboard"
        }
        // Chiến lược & Kế hoạch
        else if combinedText.contains("chiến lược") || combinedText.contains("strategy") ||
                combinedText.contains("kế hoạch") || combinedText.contains("planning") {
            return "book.pages"
        }
        // Giao tiếp & Thuyết trình
        else if combinedText.contains("giao tiếp") || combinedText.contains("thuyết trình") ||
                combinedText.contains("presentation") || combinedText.contains("communication") {
            return "character.book.closed.fill"
        }
        // Học tập & Giáo dục (mặc định)
        else {
            return "book.fill"
        }
    }

    private var statusBadge: some View {
        Group {
            if lesson.isLive {
                HStack(spacing: 4) {
                    Circle()
                        .fill(Color.red)
                        .frame(width: 6, height: 6)
                    Text("Đang diễn ra")
                        .font(.beVietnamPro(.semiBold, size: 10))
                        .foregroundColor(.red)
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color.red.opacity(0.1))
                .cornerRadius(8)
            } else if lesson.isToday {
                Text("Hôm nay")
                    .font(.beVietnamPro(.semiBold, size: 10))
                    .foregroundColor(AppConstants.Colors.primary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(AppConstants.Colors.primary.opacity(0.1))
                    .cornerRadius(8)
            } else if lesson.isUpcoming {
                Text("Sắp tới")
                    .font(.beVietnamPro(.semiBold, size: 10))
                    .foregroundColor(AppConstants.Colors.warning)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(AppConstants.Colors.warning.opacity(0.1))
                    .cornerRadius(8)
            }
        }
    }

    private var timeText: String {
        // Parse the datetime strings to extract time and date components
        let inputFormatter = DateFormatter()
        inputFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss" // Adjust based on your API format

        let timeFormatter = DateFormatter()
        timeFormatter.dateFormat = "HH:mm"

        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "dd/MM/yyyy"

        // Try to parse the datetime strings
        if let startDate = inputFormatter.date(from: lesson.startDatetime),
           let endDate = inputFormatter.date(from: lesson.endDatetime) {
            let startTime = timeFormatter.string(from: startDate)
            let endTime = timeFormatter.string(from: endDate)
            let dateString = dateFormatter.string(from: startDate)
            return "\(startTime) - \(endTime) • \(dateString)"
        } else {
            // Fallback if parsing fails - just return the raw strings
            return "\(lesson.startDatetime) - \(lesson.endDatetime)"
        }
    }


}

#Preview {
    StudentTabView()
        .environmentObject(AuthViewModel())
}
