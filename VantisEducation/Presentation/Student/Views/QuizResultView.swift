//
//  QuizResultView.swift
//  VantisEducation
//
//  Created by AI Assistant on 2025-07-30.
//

import SwiftUI

// MARK: - Quiz Result View

struct QuizResultView: View {
    let result: QuizResult
    let onDismiss: () -> Void

    @State private var showShareSheet = false
    @State private var animateScore = false
    @State private var animateContent = false

    var body: some View {
        ZStack {
            // Background overlay
            Color.black.opacity(0.4)
                .ignoresSafeArea()
                .onTapGesture {
                    onDismiss()
                }
                .opacity(animateContent ? 1 : 0)
                .animation(.easeOut(duration: 0.3), value: animateContent)

            // Bottom sheet content
            VStack(spacing: 0) {
                Spacer()

                VStack(spacing: 0) {
                    // Handle bar
                    handleBar

                    // Content
                    ScrollView {
                        VStack(spacing: 24) {
                            // Hero section with animated result
                            heroSection

                            // Score display
                            scoreSection

                            // Stats grid
                            statsSection

                            // Additional info (if available)
                            if let feedback = result.feedback, !feedback.isEmpty {
                                feedbackSection(feedback)
                            }

                            // Action buttons
                            actionButtonsSection
                        }
                        .padding(.horizontal, 20)
                        .padding(.bottom, 20)
                    }
                }
                .background(
                    RoundedRectangle(cornerRadius: 24, style: .continuous)
                        .fill(AppConstants.Colors.surface)
                        .shadow(color: AppConstants.Colors.shadow.opacity(0.2), radius: 20, x: 0, y: -8)
                )
                .clipped()
                .offset(y: animateContent ? 0 : UIScreen.main.bounds.height)
                .animation(.spring(response: 0.6, dampingFraction: 0.8), value: animateContent)
            }
            .ignoresSafeArea(.container, edges: .bottom)
        }
        .onAppear {
            withAnimation {
                animateContent = true
            }

            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                withAnimation(.easeInOut(duration: 1.2)) {
                    animateScore = true
                }
            }
        }
        .sheet(isPresented: $showShareSheet) {
            ShareSheet(activityItems: [shareText])
        }
    }

    // MARK: - Handle Bar
    private var handleBar: some View {
        VStack(spacing: 16) {
            // Drag handle
            RoundedRectangle(cornerRadius: 2.5)
                .fill(AppConstants.Colors.textSecondary.opacity(0.3))
                .frame(width: 36, height: 5)
                .padding(.top, 12)

            // Close button
            HStack {
                Spacer()

                Button(action: { onDismiss() }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 24))
                        .foregroundColor(AppConstants.Colors.textSecondary.opacity(0.6))
                }
            }
            .padding(.horizontal, 20)
        }
    }

    // MARK: - Hero Section
    private var heroSection: some View {
        VStack(spacing: 20) {
            // Animated icon
            ZStack {
                Circle()
                    .fill(result.isPassed ? AppConstants.Colors.success.opacity(0.1) : AppConstants.Colors.error.opacity(0.1))
                    .frame(width: 80, height: 80)
                    .scaleEffect(animateScore ? 1.0 : 0.8)
                    .animation(.easeOut(duration: 0.8).delay(0.2), value: animateScore)

                Image(systemName: result.isPassed ? "checkmark.circle.fill" : "xmark.circle.fill")
                    .font(.system(size: 40, weight: .medium))
                    .foregroundColor(result.isPassed ? AppConstants.Colors.success : AppConstants.Colors.error)
                    .scaleEffect(animateScore ? 1.0 : 0.5)
                    .animation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.4), value: animateScore)
            }

            VStack(spacing: 8) {
                Text(result.isPassed ? "Xuất sắc!" : "Chưa đạt")
                    .font(.system(size: 28, weight: .bold, design: .rounded))
                    .foregroundColor(result.isPassed ? AppConstants.Colors.success : AppConstants.Colors.error)

                Text(result.quizName)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(AppConstants.Colors.textPrimary)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }
            .opacity(animateContent ? 1 : 0)
            .animation(.easeOut(duration: 0.6).delay(0.6), value: animateContent)
        }
        .padding(.top, 8)
    }

    // MARK: - Score Section
    private var scoreSection: some View {
        HStack(spacing: 20) {
            // Score circle
            ZStack {
                Circle()
                    .stroke(AppConstants.Colors.border.opacity(0.2), lineWidth: 6)
                    .frame(width: 100, height: 100)

                Circle()
                    .trim(from: 0, to: animateScore ? CGFloat(result.percentage / 100) : 0)
                    .stroke(
                        result.isPassed ? AppConstants.Colors.success : AppConstants.Colors.error,
                        style: StrokeStyle(lineWidth: 6, lineCap: .round)
                    )
                    .frame(width: 100, height: 100)
                    .rotationEffect(.degrees(-90))
                    .animation(.easeInOut(duration: 1.5).delay(0.5), value: animateScore)

                VStack(spacing: 2) {
                    Text("\(String(format: "%.0f", result.score))")
                        .font(.system(size: 24, weight: .bold, design: .rounded))
                        .foregroundColor(AppConstants.Colors.textPrimary)

                    Text("\(String(format: "%.1f", result.percentage))%")
                        .font(.system(size: 12, weight: .semibold))
                        .foregroundColor(result.isPassed ? AppConstants.Colors.success : AppConstants.Colors.error)
                }
            }

            // Score details
            VStack(alignment: .leading, spacing: 12) {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Điểm số")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(AppConstants.Colors.textSecondary)

                    Text("\(String(format: "%.1f", result.score)) / \(String(format: "%.0f", result.maxScore))")
                        .font(.system(size: 18, weight: .bold))
                        .foregroundColor(AppConstants.Colors.textPrimary)
                }

                VStack(alignment: .leading, spacing: 4) {
                    Text("Trạng thái")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(AppConstants.Colors.textSecondary)

                    HStack {
                        Image(systemName: result.isPassed ? "checkmark.circle.fill" : "xmark.circle.fill")
                            .font(.system(size: 14))
                            .foregroundColor(result.isPassed ? AppConstants.Colors.success : AppConstants.Colors.error)

                        Text(result.isPassed ? "Đạt" : "Chưa đạt")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(result.isPassed ? AppConstants.Colors.success : AppConstants.Colors.error)
                    }
                }
            }

            Spacer()
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(AppConstants.Colors.surface)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(AppConstants.Colors.border.opacity(0.1), lineWidth: 1)
                )
        )
    }

    // MARK: - Stats Section
    private var statsSection: some View {
        LazyVGrid(columns: [
            GridItem(.flexible()),
            GridItem(.flexible())
        ], spacing: 12) {
            statCard(
                icon: "clock.fill",
                title: "Thời gian",
                value: formatTimeSpent(result.timeSpent),
                color: AppConstants.Colors.info
            )

            statCard(
                icon: "number.circle.fill",
                title: "Lần thử",
                value: "\(result.attemptNumber)",
                color: AppConstants.Colors.warning
            )
        }
    }

    // MARK: - Stat Card
    private func statCard(icon: String, title: String, value: String, color: Color) -> some View {
        VStack(spacing: 8) {
            ZStack {
                Circle()
                    .fill(color.opacity(0.1))
                    .frame(width: 40, height: 40)

                Image(systemName: icon)
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(color)
            }

            VStack(spacing: 4) {
                Text(title)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(AppConstants.Colors.textSecondary)

                Text(value)
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(AppConstants.Colors.textPrimary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(AppConstants.Colors.surface)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(AppConstants.Colors.border.opacity(0.1), lineWidth: 1)
                )
        )
    }

    // MARK: - Feedback Section
    private func feedbackSection(_ feedback: String) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "quote.bubble.fill")
                    .font(.system(size: 16))
                    .foregroundColor(AppConstants.Colors.info)

                Text("Nhận xét")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Spacer()
            }

            Text(feedback)
                .font(.system(size: 14, weight: .regular))
                .foregroundColor(AppConstants.Colors.textSecondary)
                .lineLimit(nil)
                .padding(12)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(AppConstants.Colors.info.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(AppConstants.Colors.info.opacity(0.2), lineWidth: 1)
                        )
                )
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(AppConstants.Colors.surface)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(AppConstants.Colors.border.opacity(0.1), lineWidth: 1)
                )
        )
    }

    // MARK: - Action Buttons Section
    private var actionButtonsSection: some View {
        VStack(spacing: 12) {
            // Primary action button
            Button(action: { onDismiss() }) {
                HStack(spacing: 8) {
                    Text("Hoàn thành")
                        .font(.system(size: 16, weight: .semibold))
                }
                .frame(maxWidth: .infinity)
                .frame(height: 48)
                .background(AppConstants.Colors.primary)
                .foregroundColor(.white)
                .clipShape(RoundedRectangle(cornerRadius: 12))
            }

            // Secondary action button
            Button(action: { showShareSheet = true }) {
                HStack(spacing: 8) {
                    Image(systemName: "square.and.arrow.up")
                        .font(.system(size: 14, weight: .medium))

                    Text("Chia sẻ kết quả")
                        .font(.system(size: 16, weight: .medium))
                }
                .frame(maxWidth: .infinity)
                .frame(height: 48)
                .background(AppConstants.Colors.surface)
                .foregroundColor(AppConstants.Colors.primary)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(AppConstants.Colors.border.opacity(0.3), lineWidth: 1)
                )
                .clipShape(RoundedRectangle(cornerRadius: 12))
            }
        }
        .padding(.bottom, 16)
    }
    
    // MARK: - Helper Methods
    
    private func formatTimeSpent(_ seconds: Int) -> String {
        let hours = seconds / 3600
        let minutes = (seconds % 3600) / 60
        let remainingSeconds = seconds % 60

        if hours > 0 {
            return String(format: "%d giờ %d phút", hours, minutes)
        } else if minutes > 0 {
            return String(format: "%d phút %d giây", minutes, remainingSeconds)
        } else {
            return String(format: "%d giây", remainingSeconds)
        }
    }

    private func formatSubmissionTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "vi_VN")
        return formatter.string(from: date)
    }

    private var shareText: String {
        """
        🎉 Tôi vừa hoàn thành bài kiểm tra!

        📝 Bài thi: \(result.quizName)
        📊 Kết quả: \(String(format: "%.1f", result.percentage))% (\(result.isPassed ? "Đạt" : "Chưa đạt"))
        ⏱️ Thời gian: \(formatTimeSpent(result.timeSpent))
        🔢 Lần thử: \(result.attemptNumber)

        #VantisEducation #OnlineLearning
        """
    }

}

// MARK: - ShareSheet

struct ShareSheet: UIViewControllerRepresentable {
    let activityItems: [Any]

    func makeUIViewController(context: Context) -> UIActivityViewController {
        let controller = UIActivityViewController(
            activityItems: activityItems,
            applicationActivities: nil
        )
        return controller
    }

    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

// MARK: - Preview

#Preview {
    ZStack {
        Color.gray.opacity(0.3)
            .ignoresSafeArea()

        QuizResultView(
            result: QuizResult(
                id: 1,
                quizId: 1,
                quizName: "Bài kiểm tra Toán học - Chương 1",
                attemptNumber: 2,
                startTime: Date().addingTimeInterval(-1800), // 30 minutes ago
                endTime: Date(),
                timeSpent: 1800, // 30 minutes
                remainingTime: 0,
                score: 85.0,
                maxScore: 100.0,
                percentage: 85.0,
                isPassed: true,
                state: "completed",
                feedback: "Bạn đã làm rất tốt! Hãy tiếp tục cố gắng với những bài tập khó hơn.",
                answers: nil
            ),
            onDismiss: {}
        )
    }
}
