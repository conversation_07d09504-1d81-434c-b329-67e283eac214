//
//  HomeView.swift
//  mobile-app-template
//
//  Created by Instructor App on 23/7/25.
//

import SwiftUI

struct HomeView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var instructorViewModel = InstructorHomeViewModel()
    @StateObject private var notificationsViewModel = NotificationsViewModel()
    @State private var navigateToNotifications = false
    @State private var navigateToClassList = false
    @State private var navigateToAssignments = false
    @State private var navigateToQuizzes = false
    @State private var navigateToGrading = false
    @State private var navigateToMissions = false

    @State private var refreshing = false
    
    var body: some View {
        NavigationStack {
            Group {
                ScrollView {
                    VStack(spacing: AppConstants.UI.sectionSpacing) {
                        // Header with instructor greeting
                        instructorHeaderSection

                        // Quick Stats Cards
                        quickStatsSection

                        // Quick Actions for Instructor
                        instructorQuickActionsSection

                        // Today's Classes
                        todayClassesSection

                        // Upcoming Assignments
                        upcomingAssignmentsSection

                        // Recent Attendance Summary
                        recentAttendanceSection
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 20) // Add bottom padding to ensure content is not cut off
                }
                .navigationBarHidden(true)
                .background(AppConstants.Colors.background.ignoresSafeArea())
                .refreshable {
                    await refreshData()
                }
            }
            // IMPORTANT: navigationDestination modifiers MUST be attached to NavigationStack,
            // not to child views. This ensures navigation works correctly from any trigger.
            .navigationDestination(isPresented: $navigateToClassList) {
                ClassListView()
            }
            .navigationDestination(isPresented: $navigateToAssignments) {
                AssignmentListView()
            }
            .navigationDestination(isPresented: $navigateToQuizzes) {
                QuizManagementView()
            }
            .navigationDestination(isPresented: $navigateToGrading) {
                PendingGradingView()
            }
            .navigationDestination(isPresented: $navigateToMissions) {
                MissionsView(userId: authViewModel.currentUser?.id ?? "user_1")
            }
            .navigationDestination(isPresented: $navigateToNotifications) {
                NotificationsView()
                    .environmentObject(notificationsViewModel)
            }
        }
        .task {
            await loadInitialData()
        }
        .sheet(isPresented: $instructorViewModel.showingAttendanceView) {
            // AttendanceView() - To be implemented
            Text("Attendance View")
        }
        .sheet(isPresented: $instructorViewModel.showingCreateAssignment) {
            // CreateAssignmentView() - To be implemented
            VStack(spacing: 20) {
                Text("🎯 CREATE ASSIGNMENT SHEET TRIGGERED!")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.red)

                Text("Create Assignment View")
                    .font(.title3)

                Text("This should NOT appear when clicking Quiz button")
                    .font(.caption)
                    .foregroundColor(.orange)
            }
            .padding()
        }
        .sheet(isPresented: $instructorViewModel.showingAnnouncements) {
            // AnnouncementsView() - To be implemented
            Text("Announcements View")
        }



    }
    
    // MARK: - Instructor Header Section
    private var instructorHeaderSection: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(greetingMessage)
                    .font(AppConstants.Typography.subheadline)
                    .foregroundColor(AppConstants.Colors.textSecondary)

                if let user = authViewModel.currentUser {
                    Text("Giảng viên \(user.displayName)")
                        .font(AppConstants.Typography.title2)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                } else {
                    Text("Giảng viên")
                        .font(AppConstants.Typography.title2)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                }

                // Quick info
                HStack(spacing: 16) {
                    if instructorViewModel.hasClassesToday {
                        HStack(spacing: 4) {
                            Image(systemName: "calendar.badge.clock")
                                .font(.caption)
                                .foregroundColor(AppConstants.Colors.primary)

                            Text("\(instructorViewModel.todayClasses.count) lớp hôm nay")
                                .font(AppConstants.Typography.caption)
                                .foregroundColor(AppConstants.Colors.textSecondary)
                        }
                    }

                    if instructorViewModel.hasUpcomingAssignments {
                        HStack(spacing: 4) {
                            Image(systemName: "doc.text.badge.plus")
                                .font(.caption)
                                .foregroundColor(AppConstants.Colors.warning)

                            Text("\(instructorViewModel.upcomingAssignments.count) bài tập sắp hết hạn")
                                .font(AppConstants.Typography.caption)
                                .foregroundColor(AppConstants.Colors.textSecondary)
                        }
                    }
                }
            }

            Spacer()

            // Notification Button
            Button(action: {
                navigateToNotifications = true
            }) {
                ZStack {
                    IconWithBadge(
                        iconName: "bell",
                        badgeCount: instructorViewModel.quickStats.pendingAssignments,
                        iconColor: AppConstants.Colors.textPrimary,
                        iconSize: 20
                    )
                }
                .frame(width: 44, height: 44)
                .background(AppConstants.Colors.surface)
                .cornerRadius(22)
                .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
            }
        }
    }

    private var greetingMessage: String {
        let hour = Calendar.current.component(.hour, from: Date())
        switch hour {
        case 5..<12: return "Chào buổi sáng!"
        case 12..<17: return "Chào buổi chiều!"
        case 17..<22: return "Chào buổi tối!"
        default: return "Chào bạn!"
        }
    }
    
    // MARK: - Quick Stats Section
    private var quickStatsSection: some View {
        HStack(spacing: 12) {
            QuickStatsCard(
                title: "Tỷ lệ điểm danh",
                value: String(format: "%.1f%%", instructorViewModel.quickStats.averageAttendanceRate),
                subtitle: "Trung bình",
                icon: "person.2.fill",
                color: instructorViewModel.quickStats.attendanceColor,
                trend: StatsTrend(percentage: "+2%", isPositive: true)
            )

            QuickStatsCard(
                title: "Bài tập cần chấm",
                value: "\(instructorViewModel.quickStats.pendingAssignments)",
                subtitle: "Còn lại",
                icon: "doc.text.fill",
                color: .blue,
                trend: nil
            )
        }
    }

    
    // MARK: - Instructor Quick Actions Section
    private var instructorQuickActionsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Thao tác nhanh")
                .font(AppConstants.Typography.headline)
                .foregroundColor(AppConstants.Colors.textPrimary)
                .padding(.top, 8)

InstructorQuickActionsGrid(
                onAttendance: {
                    print("🎯 DEBUG: Attendance button tapped")
                    instructorViewModel.markAttendance(for: instructorViewModel.nextClass ?? Class.mockClasses[0])
                },
                onAssignments: {
                    print("🎯 DEBUG: Assignments button tapped - navigating to AssignmentListView")
                    navigateToAssignments = true
                },
                onQuizzes: {
                    print("🎯 DEBUG: Quiz button tapped - navigating to QuizManagementView")
                    navigateToQuizzes = true
                },
                onAnnouncements: {
                    print("🎯 DEBUG: Announcements button tapped")
                    instructorViewModel.sendAnnouncement()
                },
                onGrades: {
                    print("🎯 DEBUG: Grades button tapped - navigating to PendingGradingView")
                    navigateToGrading = true
                },
                onStudents: {
                    print("🎯 DEBUG: Students button tapped")
                    instructorViewModel.viewStudents()
                },
                onSchedule: {
                    print("🎯 DEBUG: Schedule button tapped")
                    navigateToClassList = true
                },
                onMissions: {
                    print("🎯 DEBUG: Missions button tapped")
                    navigateToMissions = true
                }
            )
        }
    }
    
    // MARK: - Today's Classes Section
    private var todayClassesSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Lớp học hôm nay")
                    .font(AppConstants.Typography.headline)
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Spacer()

                Button("Xem tất cả") {
                    navigateToClassList = true
                }
                .font(AppConstants.Typography.subheadline)
                .foregroundColor(AppConstants.Colors.primary)
            }

            if instructorViewModel.isLoading && instructorViewModel.todayClasses.isEmpty {
                // Loading state
                VStack(spacing: 8) {
                    ForEach(0..<3, id: \.self) { _ in
                        RoundedRectangle(cornerRadius: 12)
                            .fill(AppConstants.Colors.cardBackground)
                            .frame(height: 80)
                            .redacted(reason: .placeholder)
                    }
                }
            } else if instructorViewModel.todayClasses.isEmpty {
                // Empty state
                VStack(spacing: 12) {
                    Image(systemName: "calendar.badge.clock")
                        .font(.system(size: 40))
                        .foregroundColor(AppConstants.Colors.textSecondary)

                    Text("Không có lớp học nào hôm nay")
                        .font(AppConstants.Typography.subheadline)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
                .frame(maxWidth: .infinity)
                .padding(40)
                .background(AppConstants.Colors.cardBackground)
                .cornerRadius(12)
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(instructorViewModel.todayClasses.prefix(3)) { classItem in
                        CompactClassCard(classItem: classItem) {
                            instructorViewModel.viewClassDetails(classItem)
                        }
                    }
                }
            }
        }
    }
    
    // MARK: - Upcoming Assignments Section
    private var upcomingAssignmentsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Bài tập sắp hết hạn")
                    .font(AppConstants.Typography.headline)
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Spacer()

                Button("Xem tất cả") {
                    navigateToAssignments = true
                }
                .font(AppConstants.Typography.subheadline)
                .foregroundColor(AppConstants.Colors.primary)
            }

            if instructorViewModel.isLoading && instructorViewModel.upcomingAssignments.isEmpty {
                // Loading state
                VStack(spacing: 8) {
                    ForEach(0..<2, id: \.self) { _ in
                        RoundedRectangle(cornerRadius: 12)
                            .fill(AppConstants.Colors.cardBackground)
                            .frame(height: 120)
                            .redacted(reason: .placeholder)
                    }
                }
            } else if instructorViewModel.upcomingAssignments.isEmpty {
                // Empty state
                VStack(spacing: 12) {
                    Image(systemName: "doc.text.badge.plus")
                        .font(.system(size: 40))
                        .foregroundColor(AppConstants.Colors.textSecondary)

                    Text("Không có bài tập nào sắp hết hạn")
                        .font(AppConstants.Typography.subheadline)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
                .frame(maxWidth: .infinity)
                .padding(40)
                .background(AppConstants.Colors.cardBackground)
                .cornerRadius(12)
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(instructorViewModel.upcomingAssignments.prefix(2)) { assignment in
                        AssignmentCard(assignment: assignment) {
                            instructorViewModel.viewAssignmentDetails(assignment)
                        } onGrade: {
                            // Handle grade action
                        }
                    }
                }
            }
        }
    }

    // MARK: - Recent Attendance Section
    private var recentAttendanceSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Điểm danh gần đây")
                    .font(AppConstants.Typography.headline)
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Spacer()

                Button("Xem chi tiết") {
                    // Navigate to attendance history
                    print("Navigate to attendance")
                }
                .font(AppConstants.Typography.subheadline)
                .foregroundColor(AppConstants.Colors.primary)
            }

            if instructorViewModel.recentAttendance.isEmpty {
                // Empty state
                VStack(spacing: 12) {
                    Image(systemName: "person.2.badge.plus")
                        .font(.system(size: 40))
                        .foregroundColor(AppConstants.Colors.textSecondary)

                    Text("Chưa có dữ liệu điểm danh")
                        .font(AppConstants.Typography.subheadline)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
                .frame(maxWidth: .infinity)
                .padding(40)
                .background(AppConstants.Colors.cardBackground)
                .cornerRadius(12)
            } else {
                LazyVStack(spacing: 8) {
                    ForEach(instructorViewModel.recentAttendance.prefix(3)) { attendanceSummary in
                        AttendanceSummaryRow(summary: attendanceSummary)
                    }
                }
                .padding(16)
                .background(AppConstants.Colors.cardBackground)
                .cornerRadius(12)
            }
        }
    }
    
    // MARK: - Data Loading
    private func loadInitialData() async {
        await instructorViewModel.loadDashboardData()
    }

    private func refreshData() async {
        refreshing = true
        await instructorViewModel.refreshData()
        refreshing = false
    }
}

// MARK: - Attendance Summary Row
struct AttendanceSummaryRow: View {
    let summary: ClassAttendanceSummary

    var body: some View {
        HStack(spacing: 12) {
            // Course info
            VStack(alignment: .leading, spacing: 4) {
                Text(summary.courseCode)
                    .font(AppConstants.Typography.caption)
                    .fontWeight(.medium)
                    .foregroundColor(AppConstants.Colors.textSecondary)

                Text(summary.classTitle)
                    .font(AppConstants.Typography.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(AppConstants.Colors.textPrimary)
                    .lineLimit(1)

                Text(DateFormatter.dateOnly.string(from: summary.classDate))
                    .font(AppConstants.Typography.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }

            Spacer()

            // Attendance stats
            VStack(alignment: .trailing, spacing: 4) {
                Text(summary.formattedAttendanceRate)
                    .font(AppConstants.Typography.headline)
                    .fontWeight(.bold)
                    .foregroundColor(attendanceColor)

                Text("\(summary.presentStudents)/\(summary.totalStudents)")
                    .font(AppConstants.Typography.caption)
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }

            // Status indicator
            Circle()
                .fill(attendanceColor)
                .frame(width: 8, height: 8)
        }
        .padding(.vertical, 8)
    }

    private var attendanceColor: Color {
        switch summary.attendanceRate {
        case 90...100: return .green
        case 75..<90: return .orange
        default: return .red
        }
    }
}

extension DateFormatter {
    static let dateOnly: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        return formatter
    }()
}

// MARK: - Quick Action Card
struct QuickActionCard: View {
    let icon: String
    let title: String
    let subtitle: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                Image(systemName: icon)
                    .font(AppConstants.Typography.title2)
                    .foregroundColor(color)
                
                VStack(spacing: 2) {
                    Text(title)
                        .font(AppConstants.Typography.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    Text(subtitle)
                        .font(AppConstants.Typography.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
            }
            .frame(maxWidth: .infinity)
            .frame(height: 100)
            .background(AppConstants.Colors.surface)
            .cornerRadius(AppConstants.UI.cornerRadius)
            .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Preview
#Preview {
    HomeView()
        .environmentObject(AuthViewModel())
}
