//
//  TransactionHistoryView.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import SwiftUI

struct TransactionHistoryView: View {
    @StateObject private var transactionManager = TransactionManager.shared
    @State private var selectedTransaction: Transaction?
    @State private var searchText = ""
    @State private var selectedFilter: TransactionFilter = .all
    @FocusState private var isSearchFocused: Bool
    
    enum TransactionFilter: String, CaseIterable {
        case all = "All"
        case purchase = "Purchase"
        case refund = "Refund"
        case transfer = "Transfers"
    }
    
    var filteredTransactions: [Transaction] {
        var transactions = transactionManager.transactions
        
        // Apply filter
        switch selectedFilter {
        case .all:
            break
        case .purchase:
            transactions = transactions.filter { $0.type == .purchase }
        case .refund:
            transactions = transactions.filter { $0.type == .refund }
        case .transfer:
            transactions = transactions.filter { $0.type == .transferIn || $0.type == .transferOut }
        }
        
        // Apply search
        if !searchText.isEmpty {
            transactions = transactions.filter { transaction in
                transaction.typeDisplayName.localizedCaseInsensitiveContains(searchText) ||
                transaction.description?.localizedCaseInsensitiveContains(searchText) == true ||
                transaction.id.localizedCaseInsensitiveContains(searchText)
            }
        }
        
        return transactions.sorted { $0.createdAt > $1.createdAt }
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: AppConstants.UI.sectionSpacing) {
                    // Header
                    headerSection

                    // Search Bar
                    searchSection

                    // Filter Tabs
                    filterSection

                    // Transaction List
                    transactionListSection
                }
                .padding(.horizontal, AppConstants.UI.screenPadding)
            }
            .navigationBarHidden(true)
            .background(AppConstants.Colors.background.ignoresSafeArea())
            .refreshable {
                await transactionManager.loadTransactions(refresh: true)
            }
        }
        .sheet(item: $selectedTransaction) { transaction in
            TransactionDetailView(transaction: transaction)
        }
        .onAppear {
            Task {
                await transactionManager.loadTransactions()
            }
        }
    }

    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            Text("Transaction History")
                .font(AppConstants.Typography.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(AppConstants.Colors.textPrimary)

            Spacer()

            Button(action: {
                // Focus on search field
                isSearchFocused = true
            }) {
                Image(systemName: "magnifyingglass")
                    .font(.title2)
                    .foregroundColor(AppConstants.Colors.primary)
            }
        }
    }

    // MARK: - Search Section
    private var searchSection: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(AppConstants.Colors.textSecondary)
            
            TextField("Search transactions...", text: $searchText)
                .textFieldStyle(PlainTextFieldStyle())
                .focused($isSearchFocused)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(AppConstants.Colors.surface)
        .cornerRadius(12)
    }
    
    // MARK: - Filter Section
    private var filterSection: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(TransactionFilter.allCases, id: \.self) { filter in
                    FilterChip(
                        title: filter.rawValue,
                        isSelected: selectedFilter == filter
                    ) {
                        selectedFilter = filter
                    }
                }
            }
            .padding(.horizontal, AppConstants.UI.screenPadding)
        }
        .padding(.vertical, 16)
    }
    
    // MARK: - Transaction List Section
    private var transactionListSection: some View {
        Group {
            if transactionManager.isLoading && filteredTransactions.isEmpty {
                VStack(spacing: 16) {
                    ProgressView()
                        .scaleEffect(1.2)
                    Text("Loading transactions...")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .frame(minHeight: 200)
            } else if filteredTransactions.isEmpty {
                EmptyStateView(
                    icon: "list.bullet.clipboard",
                    title: searchText.isEmpty ? "No Transactions" : "No Results",
                    subtitle: searchText.isEmpty ?
                        "Start earning tokens by shopping with our merchant partners" :
                        "Try adjusting your search or filter"
                )
                .frame(minHeight: 200)
            } else {
                LazyVStack(spacing: 0) {
                    ForEach(filteredTransactions) { transaction in
                        TransactionRowView(transaction: transaction)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .onTapGesture {
                                selectedTransaction = transaction
                            }

                        if transaction.id != filteredTransactions.last?.id {
                            Divider()
                                .padding(.leading, 64)
                        }
                    }
                }
                .background(AppConstants.Colors.surface)
                .cornerRadius(AppConstants.UI.cornerRadius)
                .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
            }
        }
    }
}





// MARK: - Filter Chip
struct FilterChip: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(isSelected ? .white : AppConstants.Colors.textPrimary)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    isSelected ? AppConstants.Colors.primary : AppConstants.Colors.surface
                )
                .cornerRadius(20)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Preview
#Preview {
    TransactionHistoryView()
}
