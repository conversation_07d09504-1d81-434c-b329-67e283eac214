//
//  SupportView.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import SwiftUI
import MessageUI

struct SupportView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var showContactForm = false
    @State private var showFAQ = false
    @State private var showMailError = false
    
    var body: some View {
        ScrollView {
            VStack(spacing: AppConstants.UI.sectionSpacing) {
                // Custom Header
                headerSection
                
                // Quick Help Section
                quickHelpSection
                
                // Contact Options Section
                contactOptionsSection
                
                // Resources Section
                resourcesSection
                
                Spacer(minLength: 100)
            }
            .padding(.horizontal, AppConstants.UI.screenPadding)
            .padding(.top, 8)
        }
        .navigationBarHidden(true)
        .background(AppConstants.Colors.background.ignoresSafeArea())
        .sheet(isPresented: $showContactForm) {
            ContactFormView()
        }
        .sheet(isPresented: $showFAQ) {
            FAQView()
        }
        .alert("Mail Not Available", isPresented: $showMailError) {
            But<PERSON>("OK") { }
        } message: {
            Text("Mail is not configured on this device. Please contact <NAME_EMAIL>")
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            // Back button
            Button(action: {
                dismiss()
            }) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color.gray.opacity(0.8))
                    .frame(width: 36, height: 36)
                    .background(
                        Circle()
                            .fill(Color.white)
                            .overlay(
                                Circle()
                                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                            )
                            .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
                    )
            }
            .buttonStyle(PlainButtonStyle())

            Spacer()

            VStack(alignment: .center, spacing: 4) {
                Text("Help & Support")
                    .font(.beVietnamPro(.bold, size: 24))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Text("We're here to help")
                    .font(.beVietnamPro(.medium, size: 14))
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }

            Spacer()

            // Empty space for balance
            Color.clear
                .frame(width: 36, height: 36)
        }
        .padding(.bottom, 8)
    }
    
    // MARK: - Quick Help Section
    private var quickHelpSection: some View {
        VStack(spacing: 0) {
            // Section Header
            HStack {
                Text("QUICK HELP")
                    .font(.beVietnamPro(.semiBold, size: 12))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .tracking(0.5)
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 12)
            
            // Section Content
            VStack(spacing: 0) {
                // FAQ
                Button(action: {
                    showFAQ = true
                }) {
                    HStack(spacing: 16) {
                        // Icon
                        Image(systemName: "questionmark.circle")
                            .font(.system(size: 20))
                            .foregroundColor(AppConstants.Colors.primary)
                            .frame(width: 24, height: 24)
                        
                        // Content
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Frequently Asked Questions")
                                .font(.beVietnamPro(.semiBold, size: 16))
                                .foregroundColor(AppConstants.Colors.textPrimary)
                            
                            Text("Find answers to common questions")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .multilineTextAlignment(.leading)
                        }
                        
                        Spacer()
                        
                        // Chevron
                        Image(systemName: "chevron.right")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
                .buttonStyle(PlainButtonStyle())
                
                // Divider
                Divider()
                    .background(AppConstants.Colors.border)
                    .padding(.horizontal, 20)
                
                // User Guide
                Button(action: {
                    // TODO: Open user guide
                }) {
                    HStack(spacing: 16) {
                        // Icon
                        Image(systemName: "book.circle")
                            .font(.system(size: 20))
                            .foregroundColor(AppConstants.Colors.primary)
                            .frame(width: 24, height: 24)
                        
                        // Content
                        VStack(alignment: .leading, spacing: 4) {
                            Text("User Guide")
                                .font(.beVietnamPro(.semiBold, size: 16))
                                .foregroundColor(AppConstants.Colors.textPrimary)
                            
                            Text("Learn how to use LinkX features")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .multilineTextAlignment(.leading)
                        }
                        
                        Spacer()
                        
                        // Chevron
                        Image(systemName: "chevron.right")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
                .buttonStyle(PlainButtonStyle())
                
                // Divider
                Divider()
                    .background(AppConstants.Colors.border)
                    .padding(.horizontal, 20)
                
                // Video Tutorials
                Button(action: {
                    // TODO: Open video tutorials
                }) {
                    HStack(spacing: 16) {
                        // Icon
                        Image(systemName: "play.circle")
                            .font(.system(size: 20))
                            .foregroundColor(AppConstants.Colors.primary)
                            .frame(width: 24, height: 24)
                        
                        // Content
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Video Tutorials")
                                .font(.beVietnamPro(.semiBold, size: 16))
                                .foregroundColor(AppConstants.Colors.textPrimary)
                            
                            Text("Watch step-by-step tutorials")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .multilineTextAlignment(.leading)
                        }
                        
                        Spacer()
                        
                        // Chevron
                        Image(systemName: "chevron.right")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
                .buttonStyle(PlainButtonStyle())
            }
            .background(Color.white)
            .cornerRadius(AppConstants.UI.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                    .stroke(AppConstants.Colors.border, lineWidth: 0.5)
            )
        }
    }

    // MARK: - Contact Options Section
    private var contactOptionsSection: some View {
        VStack(spacing: 0) {
            // Section Header
            HStack {
                Text("CONTACT US")
                    .font(.beVietnamPro(.semiBold, size: 12))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .tracking(0.5)
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 12)

            // Section Content
            VStack(spacing: 0) {
                // Send Feedback
                Button(action: {
                    showContactForm = true
                }) {
                    HStack(spacing: 16) {
                        // Icon
                        Image(systemName: "message.circle")
                            .font(.system(size: 20))
                            .foregroundColor(AppConstants.Colors.primary)
                            .frame(width: 24, height: 24)

                        // Content
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Send Feedback")
                                .font(.beVietnamPro(.semiBold, size: 16))
                                .foregroundColor(AppConstants.Colors.textPrimary)

                            Text("Share your thoughts and suggestions")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .multilineTextAlignment(.leading)
                        }

                        Spacer()

                        // Chevron
                        Image(systemName: "chevron.right")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
                .buttonStyle(PlainButtonStyle())

                // Divider
                Divider()
                    .background(AppConstants.Colors.border)
                    .padding(.horizontal, 20)

                // Email Support
                Button(action: {
                    if MFMailComposeViewController.canSendMail() {
                        // TODO: Show mail composer
                    } else {
                        showMailError = true
                    }
                }) {
                    HStack(spacing: 16) {
                        // Icon
                        Image(systemName: "envelope.circle")
                            .font(.system(size: 20))
                            .foregroundColor(AppConstants.Colors.primary)
                            .frame(width: 24, height: 24)

                        // Content
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Email Support")
                                .font(.beVietnamPro(.semiBold, size: 16))
                                .foregroundColor(AppConstants.Colors.textPrimary)

                            Text("Get help via email")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .multilineTextAlignment(.leading)
                        }

                        Spacer()

                        // Chevron
                        Image(systemName: "chevron.right")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
                .buttonStyle(PlainButtonStyle())

                // Divider
                Divider()
                    .background(AppConstants.Colors.border)
                    .padding(.horizontal, 20)

                // Call Support
                Button(action: {
                    if let phoneURL = URL(string: "tel:+84123456789") {
                        UIApplication.shared.open(phoneURL)
                    }
                }) {
                    HStack(spacing: 16) {
                        // Icon
                        Image(systemName: "phone.circle")
                            .font(.system(size: 20))
                            .foregroundColor(AppConstants.Colors.primary)
                            .frame(width: 24, height: 24)

                        // Content
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Call Support")
                                .font(.beVietnamPro(.semiBold, size: 16))
                                .foregroundColor(AppConstants.Colors.textPrimary)

                            Text("Speak with our support team")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .multilineTextAlignment(.leading)
                        }

                        Spacer()

                        // External Link Icon
                        Image(systemName: "arrow.up.right")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
                .buttonStyle(PlainButtonStyle())
            }
            .background(Color.white)
            .cornerRadius(AppConstants.UI.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                    .stroke(AppConstants.Colors.border, lineWidth: 0.5)
            )
        }
    }

    // MARK: - Resources Section
    private var resourcesSection: some View {
        VStack(spacing: 0) {
            // Section Header
            HStack {
                Text("RESOURCES")
                    .font(.beVietnamPro(.semiBold, size: 12))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .tracking(0.5)
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 12)

            // Section Content
            VStack(spacing: 0) {
                // Visit Website
                Button(action: {
                    if let url = URL(string: "https://linkx.com") {
                        UIApplication.shared.open(url)
                    }
                }) {
                    HStack(spacing: 16) {
                        // Icon
                        Image(systemName: "globe")
                            .font(.system(size: 20))
                            .foregroundColor(AppConstants.Colors.primary)
                            .frame(width: 24, height: 24)

                        // Content
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Visit Our Website")
                                .font(.beVietnamPro(.semiBold, size: 16))
                                .foregroundColor(AppConstants.Colors.textPrimary)

                            Text("Learn more about LinkX")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .multilineTextAlignment(.leading)
                        }

                        Spacer()

                        // External Link Icon
                        Image(systemName: "arrow.up.right")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
                .buttonStyle(PlainButtonStyle())

                // Divider
                Divider()
                    .background(AppConstants.Colors.border)
                    .padding(.horizontal, 20)

                // Terms of Service
                Button(action: {
                    // TODO: Open terms of service
                }) {
                    HStack(spacing: 16) {
                        // Icon
                        Image(systemName: "doc.text.circle")
                            .font(.system(size: 20))
                            .foregroundColor(AppConstants.Colors.primary)
                            .frame(width: 24, height: 24)

                        // Content
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Terms of Service")
                                .font(.beVietnamPro(.semiBold, size: 16))
                                .foregroundColor(AppConstants.Colors.textPrimary)

                            Text("Read our terms and conditions")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .multilineTextAlignment(.leading)
                        }

                        Spacer()

                        // Chevron
                        Image(systemName: "chevron.right")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
                .buttonStyle(PlainButtonStyle())

                // Divider
                Divider()
                    .background(AppConstants.Colors.border)
                    .padding(.horizontal, 20)

                // Privacy Policy
                Button(action: {
                    // TODO: Open privacy policy
                }) {
                    HStack(spacing: 16) {
                        // Icon
                        Image(systemName: "shield.circle")
                            .font(.system(size: 20))
                            .foregroundColor(AppConstants.Colors.primary)
                            .frame(width: 24, height: 24)

                        // Content
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Privacy Policy")
                                .font(.beVietnamPro(.semiBold, size: 16))
                                .foregroundColor(AppConstants.Colors.textPrimary)

                            Text("Learn about our privacy practices")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .multilineTextAlignment(.leading)
                        }

                        Spacer()

                        // Chevron
                        Image(systemName: "chevron.right")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
                .buttonStyle(PlainButtonStyle())
            }
            .background(Color.white)
            .cornerRadius(AppConstants.UI.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                    .stroke(AppConstants.Colors.border, lineWidth: 0.5)
            )
        }
    }
}

// MARK: - Supporting Views
struct FAQView: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack {
                Text("FAQ content coming soon")
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            .navigationTitle("FAQ")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Back") {
                        dismiss()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
            }
        }
    }
}

struct ContactFormView: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack {
                Text("Contact form coming soon")
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
            .navigationTitle("Contact Us")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
            }
        }
    }
}

// MARK: - Preview
#Preview {
    SupportView()
}
