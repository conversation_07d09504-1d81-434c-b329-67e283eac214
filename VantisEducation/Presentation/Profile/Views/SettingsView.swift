//
//  SettingsView.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import SwiftUI

struct SettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @AppStorage(AppConstants.StorageKeys.notificationsEnabled) private var notificationsEnabled = true
    @AppStorage(AppConstants.StorageKeys.biometricEnabled) private var biometricEnabled = false
    @AppStorage(AppConstants.StorageKeys.selectedLanguage) private var selectedLanguage = "en"
    @State private var showLanguagePicker = false
    @State private var showClearDataAlert = false
    @State private var showResetAlert = false

    var body: some View {
        ScrollView {
            VStack(spacing: AppConstants.UI.sectionSpacing) {
                // Custom Header
                headerSection

                // Settings Content
                settingsContent

                Spacer(minLength: 100)
            }
            .padding(.horizontal, AppConstants.UI.screenPadding)
            .padding(.top, 8)
        }
        .navigationBarHidden(true)
        .background(AppConstants.Colors.background.ignoresSafeArea())
        .sheet(isPresented: $showLanguagePicker) {
            LanguagePickerView(selectedLanguage: $selectedLanguage)
        }
        .alert("Clear Cache", isPresented: $showClearDataAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Clear", role: .destructive) {
                clearCache()
            }
        } message: {
            Text("This will clear cached data and free up storage space. Your account data will not be affected.")
        }
        .alert("Reset App", isPresented: $showResetAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Reset", role: .destructive) {
                resetApp()
            }
        } message: {
            Text("This will reset all app settings to their default values. You will need to sign in again.")
        }
    }

    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            // Back button
            Button(action: {
                dismiss()
            }) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color.gray.opacity(0.8))
                    .frame(width: 36, height: 36)
                    .background(
                        Circle()
                            .fill(Color.white)
                            .overlay(
                                Circle()
                                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                            )
                            .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
                    )
            }
            .buttonStyle(PlainButtonStyle())

            Spacer()

            VStack(alignment: .center, spacing: 4) {
                Text("Settings")
                    .font(.beVietnamPro(.bold, size: 24))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Text("Manage your preferences")
                    .font(.beVietnamPro(.medium, size: 14))
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }

            Spacer()

            // Empty space for balance
            Color.clear
                .frame(width: 36, height: 36)
        }
        .padding(.bottom, 8)
    }

    // MARK: - Settings Content
    private var settingsContent: some View {
        VStack(spacing: AppConstants.UI.sectionSpacing) {
            // Notifications Section
            notificationsSection

            // Security Section
            securitySection

            // App Preferences Section
            appPreferencesSection

            // Advanced Section
            advancedSection
        }
    }

    // MARK: - Helper Methods
    private var biometricType: String {
        return KeychainManager.shared.biometricTypeString()
    }

    private func languageDisplayName(for code: String) -> String {
        switch code {
        case "en":
            return "English"
        case "vi":
            return "Tiếng Việt"
        default:
            return "English"
        }
    }

    private func toggleBiometric(_ enabled: Bool) {
        // For now, just update the state
        // TODO: Implement actual biometric enable/disable logic
        biometricEnabled = enabled
        HapticManager.shared.trigger(.light)
    }

    private func clearCache() {
        // TODO: Implement cache clearing
        HapticManager.shared.trigger(.success)
    }

    private func resetApp() {
        // TODO: Implement app reset
        HapticManager.shared.trigger(.warning)
    }
}

// MARK: - Notifications Section Extension
extension SettingsView {
    private var notificationsSection: some View {
        VStack(spacing: 0) {
            // Section Header
            HStack {
                Text("NOTIFICATIONS")
                    .font(.beVietnamPro(.semiBold, size: 12))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .tracking(0.5)
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 12)

            // Section Content
            VStack(spacing: 0) {
                // Push Notifications Toggle
                VStack(spacing: 0) {
                    HStack(spacing: 16) {
                        // Icon
                        Image(systemName: "bell.fill")
                            .font(.system(size: 20))
                            .foregroundColor(AppConstants.Colors.primary)
                            .frame(width: 24, height: 24)

                        // Content
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Push Notifications")
                                .font(.beVietnamPro(.semiBold, size: 16))
                                .foregroundColor(AppConstants.Colors.textPrimary)

                            Text("Receive notifications for transactions and rewards")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .multilineTextAlignment(.leading)
                        }

                        Spacer()

                        // Toggle
                        Toggle("", isOn: $notificationsEnabled)
                            .toggleStyle(SwitchToggleStyle(tint: AppConstants.Colors.success))
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }

                if notificationsEnabled {
                    // Divider
                    Divider()
                        .background(AppConstants.Colors.border)
                        .padding(.horizontal, 20)

                    // Notification Types
                    NavigationLink(destination: ProfileNotificationSettingsView()) {
                        HStack(spacing: 16) {
                            // Icon
                            Image(systemName: "bell.badge")
                                .font(.system(size: 20))
                                .foregroundColor(AppConstants.Colors.primary)
                                .frame(width: 24, height: 24)

                            // Content
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Notification Types")
                                    .font(.beVietnamPro(.semiBold, size: 16))
                                    .foregroundColor(AppConstants.Colors.textPrimary)

                                Text("Customize which notifications you receive")
                                    .font(.beVietnamPro(.medium, size: 14))
                                    .foregroundColor(AppConstants.Colors.textSecondary)
                                    .multilineTextAlignment(.leading)
                            }

                            Spacer()

                            // Chevron
                            Image(systemName: "chevron.right")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                        }
                        .padding(.horizontal, 20)
                        .padding(.vertical, 16)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .background(Color.white)
            .cornerRadius(AppConstants.UI.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                    .stroke(AppConstants.Colors.border, lineWidth: 0.5)
            )
        }
    }
}

// MARK: - Security Section Extension
extension SettingsView {
    private var securitySection: some View {
        VStack(spacing: 0) {
            // Section Header
            HStack {
                Text("SECURITY")
                    .font(.beVietnamPro(.semiBold, size: 12))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .tracking(0.5)
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 12)

            // Section Content
            VStack(spacing: 0) {
                // Biometric Authentication
                if KeychainManager.shared.isBiometricAvailable() {
                    VStack(spacing: 0) {
                        HStack(spacing: 16) {
                            // Icon
                            Image(systemName: biometricType == "Face ID" ? "faceid" : "touchid")
                                .font(.system(size: 20))
                                .foregroundColor(AppConstants.Colors.primary)
                                .frame(width: 24, height: 24)

                            // Content
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Biometric Authentication")
                                    .font(.beVietnamPro(.semiBold, size: 16))
                                    .foregroundColor(AppConstants.Colors.textPrimary)

                                Text("Use \(biometricType) for quick access")
                                    .font(.beVietnamPro(.medium, size: 14))
                                    .foregroundColor(AppConstants.Colors.textSecondary)
                                    .multilineTextAlignment(.leading)
                            }

                            Spacer()

                            // Toggle
                            Toggle("", isOn: $biometricEnabled)
                                .toggleStyle(SwitchToggleStyle(tint: AppConstants.Colors.success))
                        }
                        .padding(.horizontal, 20)
                        .padding(.vertical, 16)
                    }
                    .onChange(of: biometricEnabled) { _, enabled in
                        toggleBiometric(enabled)
                    }

                    // Divider
                    Divider()
                        .background(AppConstants.Colors.border)
                        .padding(.horizontal, 20)
                }

                // Security Settings
                NavigationLink(destination: SecuritySettingsView()) {
                    HStack(spacing: 16) {
                        // Icon
                        Image(systemName: "shield")
                            .font(.system(size: 20))
                            .foregroundColor(AppConstants.Colors.primary)
                            .frame(width: 24, height: 24)

                        // Content
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Security Settings")
                                .font(.beVietnamPro(.semiBold, size: 16))
                                .foregroundColor(AppConstants.Colors.textPrimary)

                            Text("Password, privacy, and security options")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .multilineTextAlignment(.leading)
                        }

                        Spacer()

                        // Chevron
                        Image(systemName: "chevron.right")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
                .buttonStyle(PlainButtonStyle())
            }
            .background(Color.white)
            .cornerRadius(AppConstants.UI.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                    .stroke(AppConstants.Colors.border, lineWidth: 0.5)
            )
        }
    }
}

// MARK: - App Preferences Section Extension
extension SettingsView {
    private var appPreferencesSection: some View {
        VStack(spacing: 0) {
            // Section Header
            HStack {
                Text("APP PREFERENCES")
                    .font(.beVietnamPro(.semiBold, size: 12))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .tracking(0.5)
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 12)

            // Section Content
            VStack(spacing: 0) {
                // Language
                Button(action: {
                    showLanguagePicker = true
                }) {
                    HStack(spacing: 16) {
                        // Icon
                        Image(systemName: "globe")
                            .font(.system(size: 20))
                            .foregroundColor(AppConstants.Colors.primary)
                            .frame(width: 24, height: 24)

                        // Content
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Language")
                                .font(.beVietnamPro(.semiBold, size: 16))
                                .foregroundColor(AppConstants.Colors.textPrimary)

                            Text(languageDisplayName(for: selectedLanguage))
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .multilineTextAlignment(.leading)
                        }

                        Spacer()

                        // Chevron
                        Image(systemName: "chevron.right")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
                .buttonStyle(PlainButtonStyle())

                // Divider
                Divider()
                    .background(AppConstants.Colors.border)
                    .padding(.horizontal, 20)

                // Currency
                NavigationLink(destination: CurrencySettingsView()) {
                    HStack(spacing: 16) {
                        // Icon
                        Image(systemName: "dollarsign.circle")
                            .font(.system(size: 20))
                            .foregroundColor(AppConstants.Colors.primary)
                            .frame(width: 24, height: 24)

                        // Content
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Currency")
                                .font(.beVietnamPro(.semiBold, size: 16))
                                .foregroundColor(AppConstants.Colors.textPrimary)

                            Text("VND (Vietnamese Dong)")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .multilineTextAlignment(.leading)
                        }

                        Spacer()

                        // Chevron
                        Image(systemName: "chevron.right")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
                .buttonStyle(PlainButtonStyle())

                // Divider
                Divider()
                    .background(AppConstants.Colors.border)
                    .padding(.horizontal, 20)


                .buttonStyle(PlainButtonStyle())
            }
            .background(Color.white)
            .cornerRadius(AppConstants.UI.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                    .stroke(AppConstants.Colors.border, lineWidth: 0.5)
            )
        }
    }
}

// MARK: - Advanced Section Extension
extension SettingsView {
    private var advancedSection: some View {
        VStack(spacing: 0) {
            // Section Header
            HStack {
                Text("ADVANCED")
                    .font(.beVietnamPro(.semiBold, size: 12))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .tracking(0.5)
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 12)

            // Section Content
            VStack(spacing: 0) {
                // Developer Options
                NavigationLink(destination: DeveloperSettingsView()) {
                    HStack(spacing: 16) {
                        // Icon
                        Image(systemName: "hammer")
                            .font(.system(size: 20))
                            .foregroundColor(AppConstants.Colors.primary)
                            .frame(width: 24, height: 24)

                        // Content
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Developer Options")
                                .font(.beVietnamPro(.semiBold, size: 16))
                                .foregroundColor(AppConstants.Colors.textPrimary)

                            Text("Advanced settings for developers")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .multilineTextAlignment(.leading)
                        }

                        Spacer()

                        // Chevron
                        Image(systemName: "chevron.right")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
                .buttonStyle(PlainButtonStyle())

                // Divider
                Divider()
                    .background(AppConstants.Colors.border)
                    .padding(.horizontal, 20)

                // Reset App
                Button(action: {
                    showResetAlert = true
                }) {
                    HStack(spacing: 16) {
                        // Icon
                        Image(systemName: "arrow.clockwise")
                            .font(.system(size: 20))
                            .foregroundColor(AppConstants.Colors.error)
                            .frame(width: 24, height: 24)

                        // Content
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Reset App")
                                .font(.beVietnamPro(.semiBold, size: 16))
                                .foregroundColor(AppConstants.Colors.error)

                            Text("Reset all settings to default")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                                .multilineTextAlignment(.leading)
                        }

                        Spacer()

                        // Chevron
                        Image(systemName: "chevron.right")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
                .buttonStyle(PlainButtonStyle())
            }
            .background(Color.white)
            .cornerRadius(AppConstants.UI.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                    .stroke(AppConstants.Colors.border, lineWidth: 0.5)
            )
        }
    }
}

// MARK: - Supporting Views
struct LanguagePickerView: View {
    @Environment(\.dismiss) private var dismiss
    @Binding var selectedLanguage: String

    private let languages = [
        ("en", "English"),
        ("vi", "Tiếng Việt")
    ]

    var body: some View {
        NavigationView {
            List {
                ForEach(languages, id: \.0) { code, name in
                    Button(action: {
                        selectedLanguage = code
                        dismiss()
                    }) {
                        HStack {
                            Text(name)
                                .foregroundColor(AppConstants.Colors.textPrimary)

                            Spacer()

                            if selectedLanguage == code {
                                Image(systemName: "checkmark")
                                    .foregroundColor(AppConstants.Colors.primary)
                            }
                        }
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .navigationTitle("Language")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(AppConstants.Colors.primary)
                }
            }
        }
    }
}

struct CurrencySettingsView: View {
    var body: some View {
        List {
            Text("Currency settings coming soon")
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .navigationTitle("Currency")
        .navigationBarTitleDisplayMode(.inline)
    }
}



struct DeveloperSettingsView: View {
    var body: some View {
        List {
            Text("Developer options coming soon")
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .navigationTitle("Developer Options")
        .navigationBarTitleDisplayMode(.inline)
    }
}

struct ProfileNotificationSettingsView: View {
    var body: some View {
        List {
            Text("Notification settings coming soon")
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .navigationTitle("Notification Types")
        .navigationBarTitleDisplayMode(.inline)
    }
}

// MARK: - Supporting Components
struct ToggleRow: View {
    let icon: String
    let title: String
    let subtitle: String
    @Binding var isOn: Bool

    var body: some View {
        HStack(spacing: 16) {
            // Icon
            Image(systemName: icon)
                .font(.system(size: 20))
                .foregroundColor(AppConstants.Colors.primary)
                .frame(width: 24, height: 24)

            // Content
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.beVietnamPro(.semiBold, size: 16))
                    .foregroundColor(AppConstants.Colors.textPrimary)

                Text(subtitle)
                    .font(.beVietnamPro(.medium, size: 14))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .multilineTextAlignment(.leading)
            }

            Spacer()

            // Toggle
            Toggle("", isOn: $isOn)
                .toggleStyle(SwitchToggleStyle(tint: AppConstants.Colors.success))
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
    }
}

struct SettingsRow: View {
    let icon: String
    let title: String
    let subtitle: String
    let titleColor: Color
    let showChevron: Bool

    init(
        icon: String,
        title: String,
        subtitle: String,
        titleColor: Color = AppConstants.Colors.textPrimary,
        showChevron: Bool = true
    ) {
        self.icon = icon
        self.title = title
        self.subtitle = subtitle
        self.titleColor = titleColor
        self.showChevron = showChevron
    }

    var body: some View {
        HStack(spacing: 16) {
            // Icon
            Image(systemName: icon)
                .font(.system(size: 20))
                .foregroundColor(titleColor == AppConstants.Colors.textPrimary ? AppConstants.Colors.primary : titleColor)
                .frame(width: 24, height: 24)

            // Content
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.beVietnamPro(.semiBold, size: 16))
                    .foregroundColor(titleColor)

                Text(subtitle)
                    .font(.beVietnamPro(.medium, size: 14))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .multilineTextAlignment(.leading)
            }

            Spacer()

            // Chevron
            if showChevron {
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(AppConstants.Colors.textSecondary)
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
    }
}

// MARK: - Preview
#Preview {
    SettingsView()
}