//
//  QuizDetailModels.swift
//  VantisEducation
//
//  Created by AI Assistant on 2025-07-30.
//

import Foundation

// MARK: - Quiz Detail Response Models

/// Response for quiz detail API call
struct QuizDetailResponse: Codable {
    let success: Bool
    let message: String
    let data: QuizDetail
    let meta: ResponseMeta?
}

/// Detailed quiz information with questions and attempt status
struct QuizDetail: Codable, Identifiable {
    let id: Int
    let name: String
    let code: String
    let description: String?
    let quizType: QuizType?
    let subjectName: String?
    let className: String?
    let instructorName: String?
    let maxScore: Double
    let passingScore: Double
    let timeLimit: Int? // in minutes, nil means unlimited
    let isRandomized: Bool?
    let showCorrectAnswers: Bool?
    let state: QuizState
    let questionCount: Int
    let startDate: Date?
    let endDate: Date?
    let createdAt: Date?
    let updatedAt: Date?
    
    // Enhanced fields for student view
    let questions: [QuizQuestion]?
    let attemptStatus: DetailedAttemptStatus?
    
    enum CodingKeys: String, CodingKey {
        case id, name, code, description
        case quizType = "quiz_type"
        case subjectName = "subject_name"
        case className = "class_name"
        case instructorName = "instructor_name"
        case maxScore = "max_score"
        case passingScore = "passing_score"
        case timeLimit = "time_limit"
        case isRandomized = "is_randomized"
        case showCorrectAnswers = "show_correct_answers"
        case state
        case questionCount = "question_count"
        case startDate = "start_date"
        case endDate = "end_date"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case questions
        case attemptStatus = "attempt_status"
    }
    
    // MARK: - Custom Decoder for time_limit handling
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        id = try container.decode(Int.self, forKey: .id)
        name = try container.decode(String.self, forKey: .name)
        code = try container.decode(String.self, forKey: .code)
        description = try container.decodeIfPresent(String.self, forKey: .description)
        quizType = try container.decodeIfPresent(QuizType.self, forKey: .quizType)
        subjectName = try container.decodeIfPresent(String.self, forKey: .subjectName)
        className = try container.decodeIfPresent(String.self, forKey: .className)
        instructorName = try container.decodeIfPresent(String.self, forKey: .instructorName)
        maxScore = try container.decode(Double.self, forKey: .maxScore)
        passingScore = try container.decode(Double.self, forKey: .passingScore)
        
        // Handle time_limit: convert 0 to nil
        let timeLimitValue = try container.decodeIfPresent(Int.self, forKey: .timeLimit)
        timeLimit = (timeLimitValue == 0) ? nil : timeLimitValue
        
        isRandomized = try container.decodeIfPresent(Bool.self, forKey: .isRandomized)
        showCorrectAnswers = try container.decodeIfPresent(Bool.self, forKey: .showCorrectAnswers)
        state = try container.decode(QuizState.self, forKey: .state)
        questionCount = try container.decode(Int.self, forKey: .questionCount)
        startDate = try container.decodeIfPresent(Date.self, forKey: .startDate)
        endDate = try container.decodeIfPresent(Date.self, forKey: .endDate)
        createdAt = try container.decodeIfPresent(Date.self, forKey: .createdAt)
        updatedAt = try container.decodeIfPresent(Date.self, forKey: .updatedAt)
        questions = try container.decodeIfPresent([QuizQuestion].self, forKey: .questions)
        attemptStatus = try container.decodeIfPresent(DetailedAttemptStatus.self, forKey: .attemptStatus)
    }
}

// MARK: - Quiz Question Model

/// Individual question in a quiz
struct QuizQuestion: Codable, Identifiable {
    let id: Int
    let text: String
    let type: QuestionType
    let score: Double
    let order: Int
    let options: [QuestionOption]?
    let correctAnswerIds: [Int]?
    let explanation: String?

    enum CodingKeys: String, CodingKey {
        case id, score, explanation
        case text = "name"                    // Backend uses "name"
        case type = "question_type"           // Backend uses "question_type"
        case order = "sequence"               // Backend uses "sequence"
        case options = "answer_options"       // Backend uses "answer_options"
        case correctAnswerIds = "correct_answer_ids"
    }
}

/// Answer option for a question
struct QuestionOption: Codable, Identifiable {
    let id: Int
    let text: String
    let order: Int
    let isCorrect: Bool?

    enum CodingKeys: String, CodingKey {
        case id, text
        case order = "sequence"               // Backend uses "sequence"
        case isCorrect = "is_correct"
    }
}

// MARK: - Detailed Attempt Status Model

/// Enhanced attempt status information for quiz detail
struct DetailedAttemptStatus: Codable {
    let hasInProgress: Bool
    let inProgressAttempt: InProgressAttempt?
    let totalAttempts: Int
    let completedAttempts: Int
    let bestScore: Double?
    let lastAttemptDate: Date?
    let options: AttemptOptions

    enum CodingKeys: String, CodingKey {
        case hasInProgress = "has_in_progress"
        case inProgressAttempt = "in_progress_attempt"
        case totalAttempts = "total_attempts"
        case completedAttempts = "completed_attempts"
        case bestScore = "best_score"
        case lastAttemptDate = "last_attempt_date"
        case options
    }
}

/// Information about an in-progress attempt
struct InProgressAttempt: Codable {
    let id: Int
    let attemptNumber: Int
    let startTime: Date
    let timeSpent: Int // in seconds

    enum CodingKeys: String, CodingKey {
        case id
        case attemptNumber = "attempt_number"
        case startTime = "start_time"
        case timeSpent = "time_spent"
    }
}

/// Available options for starting quiz
struct AttemptOptions: Codable {
    let canStartNew: Bool
    let canContinue: Bool
    let canRestart: Bool
    let restartWarning: String?

    enum CodingKeys: String, CodingKey {
        case canStartNew = "can_start_new"
        case canContinue = "can_continue"
        case canRestart = "can_restart"
        case restartWarning = "restart_warning"
    }
}

// MARK: - Quiz Start Models

/// Action to take when starting a quiz
enum QuizStartAction: String, Codable, CaseIterable {
    case auto = "auto"
    case continueAttempt = "continue"
    case restart = "restart"
    case new = "new"
}

/// Response for quiz start API call
struct QuizStartResponse: Codable {
    let success: Bool
    let message: String
    let data: QuizAttemptInfo
    let meta: ResponseMeta?
}

/// Information about a started quiz attempt
struct QuizAttemptInfo: Codable, Identifiable {
    let id: Int
    let quizId: Int
    let attemptNumber: Int
    let startTime: Date
    let timeLimit: Int? // in seconds, nil if unlimited
    let state: AttemptState
    
    enum CodingKeys: String, CodingKey {
        case id
        case quizId = "quiz_id"
        case attemptNumber = "attempt_number"
        case startTime = "start_time"
        case timeLimit = "time_limit"
        case state
    }
}

// MARK: - Quiz Submission Models

/// Request for submitting quiz answers
struct QuizSubmissionRequest: Codable {
    let answers: [QuizAnswerSubmission]
    let submitTime: Date

    enum CodingKeys: String, CodingKey {
        case answers
        case submitTime = "submit_time"
    }
}

/// Individual answer submission for a question
struct QuizAnswerSubmission: Codable {
    let questionId: Int
    let questionType: String
    let answerId: Int?           // For single_choice and true_false
    let answerIds: [Int]?        // For multiple_choice
    let textAnswer: String?      // For essay questions

    enum CodingKeys: String, CodingKey {
        case questionId = "question_id"
        case questionType = "question_type"
        case answerId = "answer_id"
        case answerIds = "answer_ids"
        case textAnswer = "text_answer"
    }

    // Convenience initializer for option-based questions
    init(questionId: Int, questionType: String, selectedOptionIds: [Int]) {
        self.questionId = questionId
        self.questionType = questionType
        self.textAnswer = nil

        // Map based on question type
        switch questionType {
        case "single_choice", "true_false":
            self.answerId = selectedOptionIds.first
            self.answerIds = nil
        case "multiple_choice":
            self.answerId = nil
            self.answerIds = selectedOptionIds.isEmpty ? nil : selectedOptionIds
        default:
            self.answerId = nil
            self.answerIds = nil
        }
    }

    // Convenience initializer for text-based questions
    init(questionId: Int, questionType: String, textAnswer: String) {
        self.questionId = questionId
        self.questionType = questionType
        self.answerId = nil
        self.answerIds = nil
        self.textAnswer = textAnswer
    }
}

/// Response for quiz submission API call
struct QuizSubmissionResponse: Codable {
    let success: Bool
    let message: String
    let data: QuizResult
    let meta: ResponseMeta?
}

/// Result of a completed quiz attempt
struct QuizResult: Codable, Identifiable {
    let id: Int
    let quizId: Int
    let quizName: String
    let attemptNumber: Int
    let startTime: Date
    let endTime: Date?
    let timeSpent: Int // in seconds
    let remainingTime: Int?
    let score: Double
    let maxScore: Double
    let percentage: Double
    let isPassed: Bool
    let state: String
    let feedback: String?
    let answers: [String]? // Backend returns null for answers

    enum CodingKeys: String, CodingKey {
        case id
        case quizId = "quiz_id"
        case quizName = "quiz_name"
        case attemptNumber = "attempt_number"
        case startTime = "start_time"
        case endTime = "end_time"
        case timeSpent = "time_spent"
        case remainingTime = "remaining_time"
        case score
        case maxScore = "max_score"
        case percentage
        case isPassed = "is_passed"
        case state
        case feedback
        case answers
    }
}


