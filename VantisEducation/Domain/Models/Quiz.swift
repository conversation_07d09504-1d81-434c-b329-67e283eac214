//
//  Quiz.swift
//  mobile-app-template
//
//  Created by Instructor App on 24/7/25.
//

import Foundation

// MARK: - Attempt Status Model
struct AttemptStatus: Codable {
    let status: AttemptStatusType
    let currentAttemptId: Int?
    let remainingTime: Int?
    let canContinue: Bool?
    let canRestart: Bool?
    let lastQuestionIndex: Int?
    let progressPercentage: Double?

    enum CodingKeys: String, CodingKey {
        case status
        case currentAttemptId = "current_attempt_id"
        case remainingTime = "remaining_time"
        case canContinue = "can_continue"
        case canRestart = "can_restart"
        case lastQuestionIndex = "last_question_index"
        case progressPercentage = "progress_percentage"
    }
}

// MARK: - Attempt Status Type
enum AttemptStatusType: String, Codable, CaseIterable {
    case available = "available"
    case inProgress = "in_progress"
    case completed = "completed"
    case expired = "expired"
}

// MARK: - Quiz Model (Enhanced for API Integration)
struct Quiz: Codable, Identifiable {
    let id: Int
    let name: String
    let code: String
    let description: String?
    let quizType: QuizType?
    let subjectId: Int?
    let subjectName: String?
    let classId: Int?
    let className: String?
    let instructorName: String?
    let maxScore: Double
    let passingScore: Double
    let timeLimit: Int?
    let isRandomized: Bool?
    let showCorrectAnswers: Bool?
    let state: QuizState
    let questionCount: Int
    let studentCount: Int?
    let attemptCount: Int?
    let averageScore: Double?
    let passRate: Double?
    let pendingGradingCount: Int?
    let startDate: Date?
    let endDate: Date?
    let createdAt: Date?
    let updatedAt: Date?

    // Enhanced Student Progress Fields (from API)
    let studentAttemptCount: Int?
    let studentBestScore: Double?
    let studentLastAttempt: Date?
    let canAttempt: Bool?
    let attemptStatus: DetailedAttemptStatus?

    enum CodingKeys: String, CodingKey {
        case id, name, code, description
        case quizType = "quiz_type"
        case subjectId = "subject_id"
        case subjectName = "subject_name"
        case classId = "class_id"
        case className = "class_name"
        case instructorName = "instructor_name"
        case maxScore = "max_score"
        case passingScore = "passing_score"
        case timeLimit = "time_limit"
        case isRandomized = "is_randomized"
        case showCorrectAnswers = "show_correct_answers"
        case state
        case questionCount = "question_count"
        case studentCount = "student_count"
        case attemptCount = "attempt_count"
        case averageScore = "average_score"
        case passRate = "pass_rate"
        case pendingGradingCount = "pending_grading_count"
        case startDate = "start_date"
        case endDate = "end_date"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case studentAttemptCount = "student_attempt_count"
        case studentBestScore = "student_best_score"
        case studentLastAttempt = "student_last_attempt"
        case canAttempt = "can_attempt"
        case attemptStatus = "attempt_status"
    }

    // MARK: - Custom Decoder
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        id = try container.decode(Int.self, forKey: .id)
        name = try container.decode(String.self, forKey: .name)
        code = try container.decode(String.self, forKey: .code)
        description = try container.decodeIfPresent(String.self, forKey: .description)
        quizType = try container.decodeIfPresent(QuizType.self, forKey: .quizType)
        subjectId = try container.decodeIfPresent(Int.self, forKey: .subjectId)
        subjectName = try container.decodeIfPresent(String.self, forKey: .subjectName)
        classId = try container.decodeIfPresent(Int.self, forKey: .classId)
        className = try container.decodeIfPresent(String.self, forKey: .className)
        instructorName = try container.decodeIfPresent(String.self, forKey: .instructorName)
        maxScore = try container.decode(Double.self, forKey: .maxScore)
        passingScore = try container.decode(Double.self, forKey: .passingScore)

        // Handle time_limit: convert 0 to nil
        let timeLimitValue = try container.decodeIfPresent(Int.self, forKey: .timeLimit)
        timeLimit = (timeLimitValue == 0) ? nil : timeLimitValue

        isRandomized = try container.decodeIfPresent(Bool.self, forKey: .isRandomized)
        showCorrectAnswers = try container.decodeIfPresent(Bool.self, forKey: .showCorrectAnswers)
        state = try container.decode(QuizState.self, forKey: .state)
        questionCount = try container.decode(Int.self, forKey: .questionCount)
        studentCount = try container.decodeIfPresent(Int.self, forKey: .studentCount)
        attemptCount = try container.decodeIfPresent(Int.self, forKey: .attemptCount)
        averageScore = try container.decodeIfPresent(Double.self, forKey: .averageScore)
        passRate = try container.decodeIfPresent(Double.self, forKey: .passRate)
        pendingGradingCount = try container.decodeIfPresent(Int.self, forKey: .pendingGradingCount)
        startDate = try container.decodeIfPresent(Date.self, forKey: .startDate)
        endDate = try container.decodeIfPresent(Date.self, forKey: .endDate)
        createdAt = try container.decodeIfPresent(Date.self, forKey: .createdAt)
        updatedAt = try container.decodeIfPresent(Date.self, forKey: .updatedAt)

        // Enhanced Student Progress Fields
        studentAttemptCount = try container.decodeIfPresent(Int.self, forKey: .studentAttemptCount)
        studentBestScore = try container.decodeIfPresent(Double.self, forKey: .studentBestScore)
        studentLastAttempt = try container.decodeIfPresent(Date.self, forKey: .studentLastAttempt)
        canAttempt = try container.decodeIfPresent(Bool.self, forKey: .canAttempt)
        attemptStatus = try container.decodeIfPresent(DetailedAttemptStatus.self, forKey: .attemptStatus)
    }

    // MARK: - Convenience Initializer for Backward Compatibility
    init(
        id: Int,
        name: String,
        code: String,
        description: String?,
        quizType: QuizType,
        subjectId: Int?,
        subjectName: String?,
        classId: Int?,
        className: String?,
        maxScore: Double,
        passingScore: Double,
        timeLimit: Int?,
        isRandomized: Bool,
        showCorrectAnswers: Bool,
        state: QuizState,
        questionCount: Int,
        studentCount: Int,
        attemptCount: Int,
        averageScore: Double,
        passRate: Double,
        pendingGradingCount: Int,
        startDate: Date?,
        endDate: Date?,
        createdAt: Date,
        updatedAt: Date?
    ) {
        self.id = id
        self.name = name
        self.code = code
        self.description = description
        self.quizType = quizType
        self.subjectId = subjectId
        self.subjectName = subjectName
        self.classId = classId
        self.className = className
        self.instructorName = nil // Default value
        self.maxScore = maxScore
        self.passingScore = passingScore
        self.timeLimit = timeLimit
        self.isRandomized = isRandomized
        self.showCorrectAnswers = showCorrectAnswers
        self.state = state
        self.questionCount = questionCount
        self.studentCount = studentCount
        self.attemptCount = attemptCount
        self.averageScore = averageScore
        self.passRate = passRate
        self.pendingGradingCount = pendingGradingCount
        self.startDate = startDate
        self.endDate = endDate
        self.createdAt = createdAt
        self.updatedAt = updatedAt

        // Default values for new fields
        self.studentAttemptCount = nil
        self.studentBestScore = nil
        self.studentLastAttempt = nil
        self.canAttempt = nil
        self.attemptStatus = nil
    }

    // MARK: - Computed Properties

    /// Determines if quiz is currently active based on dates and state
    var isActive: Bool {
        guard let startDate = startDate, let endDate = endDate else {
            return state == .published
        }
        let now = Date()
        return state == .published && now >= startDate && now <= endDate
    }

    /// Determines if quiz has expired
    var isExpired: Bool {
        guard let endDate = endDate else { return false }
        return Date() > endDate
    }

    /// Determines if quiz is upcoming (not started yet)
    var isUpcoming: Bool {
        guard let startDate = startDate else { return false }
        return Date() < startDate
    }

    /// Determines current quiz status for student
    var currentStatus: AttemptStatusType {
        // Priority order based on API guidelines
        if let attemptStatus = attemptStatus {
            if attemptStatus.hasInProgress {
                return .inProgress
            } else if attemptStatus.completedAttempts > 0 {
                return .completed
            } else {
                return .available
            }
        } else if let canAttempt = canAttempt, !canAttempt {
            return .expired
        } else if isExpired {
            return .expired
        } else if isUpcoming {
            return .available // Will be available when time comes
        } else if isActive {
            return .available
        } else {
            return .expired
        }
    }

    /// Student's completion rate for this quiz
    var studentCompletionRate: Double {
        // Calculate progress based on completed vs total attempts
        if let attemptStatus = attemptStatus, attemptStatus.totalAttempts > 0 {
            return Double(attemptStatus.completedAttempts) / Double(attemptStatus.totalAttempts) * 100.0
        }
        return 0.0
    }

    /// Overall completion rate (all students)
    var completionRate: Double {
        guard let studentCount = studentCount, let attemptCount = attemptCount, studentCount > 0 else { return 0 }
        return Double(attemptCount) / Double(studentCount) * 100
    }

    /// Grading progress for instructors
    var gradingProgress: Double {
        guard let attemptCount = attemptCount, let pendingGradingCount = pendingGradingCount, attemptCount > 0 else { return 0 }
        let gradedCount = attemptCount - pendingGradingCount
        return Double(gradedCount) / Double(attemptCount) * 100
    }

    // MARK: - Safe Accessors for Backward Compatibility

    /// Safe accessor for quizType with default value
    var safeQuizType: QuizType {
        return quizType ?? .quiz
    }



    /// Safe accessor for isRandomized with default value
    var safeIsRandomized: Bool {
        return isRandomized ?? false
    }

    /// Safe accessor for showCorrectAnswers with default value
    var safeShowCorrectAnswers: Bool {
        return showCorrectAnswers ?? false
    }

    /// Safe accessor for studentCount with default value
    var safeStudentCount: Int {
        return studentCount ?? 0
    }

    /// Safe accessor for attemptCount with default value
    var safeAttemptCount: Int {
        return attemptCount ?? 0
    }

    /// Safe accessor for averageScore with default value
    var safeAverageScore: Double {
        return averageScore ?? 0.0
    }

    /// Safe accessor for passRate with default value
    var safePassRate: Double {
        return passRate ?? 0.0
    }

    /// Safe accessor for pendingGradingCount with default value
    var safePendingGradingCount: Int {
        return pendingGradingCount ?? 0
    }

    /// Safe accessor for createdAt with default value
    var safeCreatedAt: Date {
        return createdAt ?? Date()
    }
}

// MARK: - Quiz Type
enum QuizType: String, Codable, CaseIterable {
    case quiz = "quiz"
    case assignment = "assignment"
    case midterm = "midterm"
    case final = "final"
    case project = "project"
    
    var displayName: String {
        switch self {
        case .quiz: return "Kiểm tra"
        case .assignment: return "Bài tập"
        case .midterm: return "Giữa kỳ"
        case .final: return "Cuối kỳ"
        case .project: return "Dự án"
        }
    }
    
    var icon: String {
        switch self {
        case .quiz: return "questionmark.circle"
        case .assignment: return "doc.text"
        case .midterm: return "doc.text.fill"
        case .final: return "graduationcap"
        case .project: return "folder"
        }
    }
    
    var color: String {
        switch self {
        case .quiz: return "blue"
        case .assignment: return "green"
        case .midterm: return "orange"
        case .final: return "red"
        case .project: return "purple"
        }
    }
}

// MARK: - Quiz State
enum QuizState: String, Codable, CaseIterable {
    case draft = "draft"
    case ready = "ready"
    case published = "published"
    case archived = "archived"
    
    var displayName: String {
        switch self {
        case .draft: return "Nháp"
        case .ready: return "Sẵn sàng"
        case .published: return "Đã phát hành"
        case .archived: return "Lưu trữ"
        }
    }
    
    var color: String {
        switch self {
        case .draft: return "gray"
        case .ready: return "blue"
        case .published: return "green"
        case .archived: return "brown"
        }
    }
}

// MARK: - Question Model
struct Question: Codable, Identifiable {
    let id: Int?
    let text: String
    let questionType: QuestionType
    let score: Double
    let answers: [Answer]
    
    var hasCorrectAnswer: Bool {
        return answers.contains { $0.isCorrect }
    }
}

// MARK: - Question Type
enum QuestionType: String, Codable, CaseIterable {
    case singleChoice = "single_choice"
    case multipleChoice = "multiple_choice"
    case trueFalse = "true_false"
    case essay = "essay"
    case matching = "matching"
    case fillInBlank = "fill_in_blank"

    var displayName: String {
        switch self {
        case .singleChoice: return "Trắc nghiệm đơn"
        case .multipleChoice: return "Trắc nghiệm nhiều đáp án"
        case .trueFalse: return "Đúng/Sai"
        case .essay: return "Tự luận"
        case .matching: return "Ghép đôi"
        case .fillInBlank: return "Điền vào chỗ trống"
        }
    }

    var icon: String {
        switch self {
        case .singleChoice: return "circle"
        case .multipleChoice: return "checkmark.square"
        case .trueFalse: return "checkmark.circle"
        case .essay: return "text.alignleft"
        case .matching: return "arrow.left.arrow.right"
        case .fillInBlank: return "underline"
        }
    }

    var requiresAnswers: Bool {
        return self != .essay && self != .matching && self != .fillInBlank
    }
}

// MARK: - Answer Model
struct Answer: Codable, Identifiable {
    let id: Int?
    let text: String
    let isCorrect: Bool
}

// MARK: - Quiz Extensions
extension Quiz {
    // MARK: - Computed Properties for UI
    // completionRate is already defined in the main Quiz struct

    var timeRemainingText: String {
        guard let endDate = endDate else { return "Không có thời hạn" }

        let now = Date()
        let timeInterval = endDate.timeIntervalSince(now)

        if timeInterval <= 0 {
            return "Đã hết hạn"
        }

        let days = Int(timeInterval) / (24 * 3600)
        let hours = Int(timeInterval) % (24 * 3600) / 3600
        let minutes = Int(timeInterval) % 3600 / 60

        if days > 0 {
            return "Còn \(days) ngày \(hours) giờ"
        } else if hours > 0 {
            return "Còn \(hours) giờ \(minutes) phút"
        } else {
            return "Còn \(minutes) phút"
        }
    }
}

// MARK: - Quiz Extensions
extension Quiz {
    static let mockUpcomingExams: [Quiz] = [
        Quiz(
            id: 1,
            name: "Kiểm tra giữa kỳ - Vật lý",
            code: "PHYS_MID_01",
            description: "Kiểm tra các chương 1-3",
            quizType: .midterm,
            subjectId: 1,
            subjectName: "Vật lý",
            classId: 1,
            className: "Lớp Vật lý A1",
            maxScore: 10.0,
            passingScore: 5.0,
            timeLimit: 90,
            isRandomized: true,
            showCorrectAnswers: false,
            state: .published,
            questionCount: 30,
            studentCount: 25,
            attemptCount: 0,
            averageScore: 0.0,
            passRate: 0.0,
            pendingGradingCount: 0,
            startDate: Date().addingTimeInterval(86400 * 3), // 3 days
            endDate: Date().addingTimeInterval(86400 * 4), // 4 days
            createdAt: Date(),
            updatedAt: Date()
        )
    ]
}
