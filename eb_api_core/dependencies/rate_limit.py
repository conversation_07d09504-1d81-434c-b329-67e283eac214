# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging
import time
from typing import Annotated, Optional, Dict, Any, Callable

from fastapi import Depends, Request, Response

# Dùng try-except để xử lý khi không có Redis
try:
    from redis import Redis
    REDIS_AVAILABLE = True
except ImportError:
    _logger = logging.getLogger(__name__)
    _logger.warning(
        "Redis package not installed. Rate limiting will use in-memory cache only. "
        "Install 'redis' package for production use: pip install redis"
    )
    REDIS_AVAILABLE = False
    # Dummy class để tránh lỗi khi reference
    class Redis:
        @staticmethod
        def from_url(*args, **kwargs):
            return None

from odoo.addons.base.models.res_users import Users

from odoo.addons.eb_api_core.dependencies.auth import get_optional_user
from odoo.addons.eb_api_core.schemas.error import ErrorCode
from odoo.addons.eb_api_core.utils.exceptions import api_exception

_logger = logging.getLogger(__name__)

# In-memory cache cho demo hoặc development, không sử dụng trong production
_memory_cache = {}


class RateLimiter:
    """Rate limiter implementation."""

    def __init__(
        self,
        limit: int = 100,
        window: int = 60,
        cache_type: str = "memory",
        redis_url: Optional[str] = None,
    ):
        """Initialize rate limiter.

        Args:
            limit: Maximum number of requests per window
            window: Time window in seconds
            cache_type: Type of cache to use, 'memory' or 'redis'
            redis_url: Redis connection URL if using redis cache
        """
        self.limit = limit
        self.window = window
        self.cache_type = cache_type
        self.redis_url = redis_url
        self.redis_client = None

        # Kiểm tra xem Redis có khả dụng không
        if not REDIS_AVAILABLE and cache_type == "redis":
            _logger.warning(
                "Redis selected as cache_type but Redis package is not installed. "
                "Falling back to in-memory cache."
            )
            self.cache_type = "memory"
        elif self.cache_type == "redis" and self.redis_url:
            self._init_redis()

    def _init_redis(self):
        """Initialize Redis client."""
        if not REDIS_AVAILABLE:
            self.cache_type = "memory"
            return
            
        try:
            self.redis_client = Redis.from_url(self.redis_url)
            # Test connection
            self.redis_client.ping()
        except Exception as e:
            _logger.error(f"Redis connection error: {str(e)}")
            _logger.warning("Falling back to in-memory cache for rate limiting")
            self.cache_type = "memory"

    def _get_key(self, identifier: str) -> str:
        """Generate rate limit key for an identifier."""
        timestamp = int(time.time() / self.window) * self.window
        return f"rate_limit:{identifier}:{timestamp}"

    def _check_memory_limit(self, key: str) -> Dict[str, Any]:
        """Check rate limit using in-memory cache."""
        current = _memory_cache.get(key, 0)
        _memory_cache[key] = current + 1
        remaining = max(0, self.limit - current - 1)
        return {
            "limit": self.limit,
            "remaining": remaining,
            "reset": self.window - (int(time.time()) % self.window),
            "allowed": current < self.limit,
        }

    def _check_redis_limit(self, key: str) -> Dict[str, Any]:
        """Check rate limit using Redis."""
        # Ensure connection
        if not REDIS_AVAILABLE or not self.redis_client:
            return self._check_memory_limit(key)

        try:
            # Increment and set expiry in a pipeline
            pipe = self.redis_client.pipeline()
            pipe.incr(key)
            pipe.expire(key, self.window)
            current = pipe.execute()[0]

            remaining = max(0, self.limit - current)
            return {
                "limit": self.limit,
                "remaining": remaining,
                "reset": self.window - (int(time.time()) % self.window),
                "allowed": current <= self.limit,
            }
        except Exception as e:
            _logger.error(f"Redis error during rate limiting: {str(e)}")
            # Fallback to memory cache
            return self._check_memory_limit(key)

    def check_limit(self, identifier: str) -> Dict[str, Any]:
        """Check if the identifier has exceeded rate limits.

        Args:
            identifier: Unique identifier (e.g. IP, user_id)

        Returns:
            Dict with limit info: limit, remaining, reset, allowed
        """
        key = self._get_key(identifier)

        if self.cache_type == "redis" and REDIS_AVAILABLE and self.redis_client:
            return self._check_redis_limit(key)
        else:
            return self._check_memory_limit(key)


# Default rate limiter instance
_default_limiter = RateLimiter()


def get_rate_limiter() -> RateLimiter:
    """Get the default rate limiter instance."""
    return _default_limiter


def rate_limit_dependency(
    limit: Optional[int] = None,
    window: Optional[int] = None,
    limiter: Optional[RateLimiter] = None,
    key_func: Optional[Callable[[Request, Optional[Users]], str]] = None,
):
    """Create a dependency for rate limiting.

    Args:
        limit: Custom request limit (overrides limiter's default)
        window: Custom time window in seconds (overrides limiter's default)
        limiter: Custom RateLimiter instance (defaults to global instance)
        key_func: Function to generate identifier from request

    Returns:
        Dependency function
    """
    # Use default limiter if not provided
    limiter = limiter or _default_limiter

    # Override limiter settings if provided
    if limit is not None:
        limiter.limit = limit
    if window is not None:
        limiter.window = window

    # Default key function uses IP and user ID if available
    if key_func is None:

        def default_key_func(request: Request, user: Optional[Users] = None) -> str:
            client_id = user.id if user else None
            client_ip = request.client.host if request.client else "unknown"
            if client_id:
                return f"user:{client_id}"
            return f"ip:{client_ip}"

        key_func = default_key_func

    async def rate_limit_check(
        request: Request,
        response: Response,
        user: Annotated[Optional[Users], Depends(get_optional_user)],
    ):
        """Check rate limits for the request."""
        # Generate key for this request
        identifier = key_func(request, user)

        # Check limit
        result = limiter.check_limit(identifier)

        # Set rate limit headers
        response.headers["X-RateLimit-Limit"] = str(result["limit"])
        response.headers["X-RateLimit-Remaining"] = str(result["remaining"])
        response.headers["X-RateLimit-Reset"] = str(result["reset"])

        # Raise exception if exceeded
        if not result["allowed"]:
            raise api_exception(
                ErrorCode.RATE_LIMIT_EXCEEDED, "Rate limit exceeded", status_code=429
            )

        return result

    return rate_limit_check


# Per-endpoint rate limiting decorator
def rate_limited(
    limit: int = 100,
    window: int = 60,
    key_func: Optional[Callable] = None,
):
    """Decorator for endpoints needing rate limiting.

    Usage:
        @router.get("/something")
        @rate_limited(limit=10, window=60)
        def get_something(limit_info = Depends()):
            ...
    """

    def decorator(func):
        # Add dependency to function
        dependency = rate_limit_dependency(limit, window, key_func=key_func)

        # Return function with dependency
        return Depends(dependency)(func)

    return decorator
