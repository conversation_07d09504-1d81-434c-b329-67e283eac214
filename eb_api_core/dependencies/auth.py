# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging
from typing import Optional, Annotated, Any, Dict, Union

from fastapi import Depends, Request, Header

from odoo.api import Environment
from odoo.addons.base.models.res_users import Users
from odoo.addons.fastapi.dependencies import odoo_env
from odoo.addons.eb_api_core.schemas.error import ErrorCode
from odoo.addons.eb_api_core.utils.exceptions import api_exception
from odoo.addons.eb_api_core.utils.logging import get_request_logger

_logger = get_request_logger(__name__)

try:
    from odoo.addons.fastapi_auth_jwt.dependencies import (
        auth_jwt_authenticated_payload,
    )
except ImportError:
    _logger.warning(
        "fastapi_auth_jwt module not available. JWT authentication will not work. "
        "This is expected if eb_api_auth module is not installed yet."
    )
    # Define dummy function to avoid reference errors
    async def auth_jwt_authenticated_payload(
        authorization: Optional[str] = Header(None)
    ) -> Optional[Dict[str, Any]]:
        return None

# Override default validator name for the module
def auth_jwt_validator_name() -> str:
    """Return the default JWT validator name for this module"""
    return "eb_api_default"


# Get JWT validator record with additional fallback logic
def get_jwt_validator(
    validator_name: str = "eb_api_default",
    env: Annotated[Environment, Depends(odoo_env)] = None,
):
    """Get JWT validator with fallback logic.

    This extends the standard validator lookup with:
    1. Search by specific name
    2. Fallback to any validator if not found
    3. Log warning and return None if no validator exists or model not available
    """
    try:
        # Check if the model exists
        if 'auth.jwt.validator' not in env:
            _logger.warning(
                f"auth.jwt.validator model not available. This is expected if eb_api_auth "
                f"module is not installed yet."
            )
            return None

        validator = (
            env["auth.jwt.validator"]
            .sudo()
            .search([("name", "=", validator_name)], limit=1)
        )

        if not validator:
            # Fallback to first validator if not found by name
            validator = env["auth.jwt.validator"].sudo().search([], limit=1)

        if not validator:
            # Warning if no validator at all
            _logger.warning(
                f"No JWT validator configuration found. This is expected if eb_api_auth "
                f"module is not installed yet or if no validator has been created."
            )
            return None

        return validator
    except Exception as e:
        _logger.warning(
            f"Error while retrieving JWT validator: {str(e)}. "
            f"This is expected if eb_api_auth module is not installed yet."
        )
        return None


# Get current user from JWT payload
def get_current_user(
    payload: Annotated[Dict[str, Any], Depends(auth_jwt_authenticated_payload)],
    env: Annotated[Environment, Depends(odoo_env)],
) -> Optional[Users]:
    """Get the current user from the JWT payload.

    This uses the standard JWT payload authenticated by fastapi_auth_jwt
    but returns a res.users record instead of partner.
    
    Returns None if authentication is not available or fails.
    """
    if not payload:
        _logger.warning("No JWT payload available. Authentication skipped.")
        return None
        
    _logger.info(f"JWT Payload received: {payload}")

    user_id = payload.get("sub")
    _logger.info(f"Subject (sub) from JWT payload: {user_id}, type: {type(user_id)}")

    if not user_id:
        _logger.error("Missing subject (sub) in JWT token")
        raise api_exception(ErrorCode.UNAUTHORIZED, "Invalid user identity in token")

    try:
        # Convert user_id to int for browsing if it's a string
        if isinstance(user_id, str):
            user_id_int = int(user_id)
            _logger.info(
                f"Converting user_id from string '{user_id}' to int {user_id_int}"
            )
        else:
            user_id_int = int(user_id)
            _logger.info(f"Using user_id as is: {user_id_int}")

        user = env["res.users"].sudo().browse(user_id_int)
        if not user.exists():
            _logger.error(f"User with ID {user_id_int} not found in database")
            raise api_exception(ErrorCode.UNAUTHORIZED, "User not found")

        _logger.info(f"Successfully found user: {user.name} (ID: {user.id})")
        return user
    except Exception as e:
        _logger.error(f"Error getting current user: {str(e)}, payload: {payload}")
        raise api_exception(ErrorCode.UNAUTHORIZED, f"Invalid user identity: {str(e)}")


# Get optional user (may be None)
def get_optional_user(
    payload: Annotated[
        Optional[Dict[str, Any]], Depends(auth_jwt_authenticated_payload)
    ],
    env: Annotated[Environment, Depends(odoo_env)],
) -> Optional[Users]:
    """Get the current user if available, or None if not authenticated."""
    if not payload:
        return None

    user_id = payload.get("sub")
    if not user_id:
        return None

    try:
        user = env["res.users"].sudo().browse(int(user_id))
        if not user.exists():
            return None
        return user
    except Exception as e:
        _logger.error(f"Error getting optional user: {str(e)}")
        return None


# Check if user has required permissions
def check_user_permissions(
    user: Optional[Users],
    permissions: list = None,
) -> bool:
    """Check if the current user has the required permissions.

    Args:
        user: The user record to check (may be None)
        permissions: List of permission strings to check

    Returns:
        True if user has all required permissions, False otherwise
    """
    if user is None:
        return False
        
    if not permissions:
        return True

    # TODO: Implement actual permission checking logic
    # For now, we just check if the user is admin
    is_admin = user.has_group("base.group_system")
    return is_admin


# Require specific permissions
def require_permissions(
    user: Annotated[Optional[Users], Depends(get_current_user)],
    request: Request,
    permissions: list = None,
) -> Optional[Users]:
    """Check if the current user has the required permissions.

    Args:
        user: The user to check (from get_current_user, may be None)
        request: FastAPI request object
        permissions: List of permission strings to check

    Returns:
        User object if authorized

    Raises:
        HTTPException: If user doesn't have required permissions
    """
    if user is None:
        raise api_exception(
            ErrorCode.UNAUTHORIZED,
            "Authentication required to access this resource",
        )
    
    if not permissions:
        return user

    has_permission = check_user_permissions(user, permissions)

    if not has_permission:
        raise api_exception(
            ErrorCode.INSUFFICIENT_PERMISSIONS,
            "You don't have permission to access this resource",
        )

    return user
