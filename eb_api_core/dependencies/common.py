# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging
from typing import Annotated, Optional, Dict, Any

from fastapi import Query, Depends, Request

from odoo.api import Environment
from odoo.addons.fastapi.dependencies import odoo_env
from odoo.addons.eb_api_core.middleware.tracing_middleware import get_request_id as get_id_from_middleware

_logger = logging.getLogger(__name__)


def pagination_params(
    page: int = Query(1, title="Page number", ge=1),
    per_page: int = Query(20, title="Items per page", ge=1, le=100),
) -> Dict[str, int]:
    """Common pagination parameters.

    Args:
        page: Page number (starts from 1)
        per_page: Number of items per page (1-100)

    Returns:
        Dictionary with pagination parameters
    """
    return {"page": page, "per_page": per_page}


def sorting_params(
    sort_by: Optional[str] = Query(
        None, title="Sort field", description="Field to sort by"
    ),
    sort_order: Optional[str] = Query(
        "asc", title="Sort order", description="Ascending (asc) or descending (desc)"
    ),
) -> Dict[str, str]:
    """Common sorting parameters.

    Args:
        sort_by: Field to sort by
        sort_order: Sort order (asc or desc)

    Returns:
        Dictionary with sorting parameters
    """
    # Validate sort_order
    if sort_order and sort_order not in ["asc", "desc"]:
        sort_order = "asc"

    return {"sort_by": sort_by, "sort_order": sort_order}


def filtering_params(
    filters: Optional[str] = Query(
        None, title="Filters", description="JSON encoded filter parameters"
    ),
) -> Dict[str, Any]:
    """Common filtering parameters.

    Args:
        filters: JSON encoded string of filter parameters

    Returns:
        Dictionary with filter parameters
    """
    import json

    if not filters:
        return {}

    try:
        return json.loads(filters)
    except Exception as e:
        _logger.warning(f"Invalid filter JSON: {e}")
        return {}


def api_context(
    request: Request,
    pagination: Dict[str, int] = Depends(pagination_params),
    sorting: Dict[str, str] = Depends(sorting_params),
    filtering: Dict[str, Any] = Depends(filtering_params),
) -> Dict[str, Any]:
    """Combine common API context data.

    Args:
        request: FastAPI request object
        pagination: Pagination parameters
        sorting: Sorting parameters
        filtering: Filter parameters

    Returns:
        Combined API context
    """
    return {
        "pagination": pagination,
        "sorting": sorting,
        "filtering": filtering,
        "client_ip": request.client.host if request.client else None,
        "user_agent": request.headers.get("user-agent", ""),
    }


def get_env_context(env: Annotated[Environment, Depends(odoo_env)]) -> Dict[str, Any]:
    """Get Odoo environment context information.

    Args:
        env: Odoo environment

    Returns:
        Dictionary with environment context
    """
    user = env.user
    company = user.company_id

    return {
        "user_id": user.id,
        "user_login": user.login,
        "user_name": user.name,
        "company_id": company.id,
        "company_name": company.name,
        "lang": env.context.get("lang", "en_US"),
        "tz": user.tz or "UTC",
    }


def get_request_id(request: Request = None) -> str:
    """Trả về request ID hiện tại.

    Dependency này giúp tránh việc phải gọi get_request_id() ở mỗi router.

    Args:
        request: FastAPI Request object (tự động inject bởi FastAPI)

    Returns:
        str: ID của request hiện tại
    """
    # Lấy request ID từ middleware
    request_id = get_id_from_middleware()

    # Nếu không có request ID từ middleware, tạo mới với định dạng giống middleware
    if not request_id:
        import uuid
        request_id = f"req-{uuid.uuid4().hex[:8]}"

    return request_id


# Đổi tên để dùng như một dependency
current_request_id = get_request_id
