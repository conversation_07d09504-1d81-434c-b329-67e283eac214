# Mẫu cấu hình OpenTelemetry cho Odoo
# Copy các dòng dưới đây và thêm vào file odoo.conf

# Bật/tắt OpenTelemetry
# Sử dụng boolean (true/false) hoặc chuỗi ("true"/"false")
# Lưu ý: N<PERSON><PERSON> sử dụng boolean (true/false), không cần dấu ngoặc kép
otel_enabled = true

# Tên của service trong OpenTelemetry
otel_service_name = odoo-api

# Phiên bản của service
otel_service_version = 1.0.0

# Môi trường triển khai (development, staging, production)
otel_environment = development

# Cấu hình exporter
# Loại exporter: otlp hoặc console
otel_exporter_type = otlp

# Đường dẫn tới OpenTelemetry Collector (OTLP exporter)
# Sử dụng gRPC port mặc định là 4317
otel_exporter_otlp_endpoint = http://localhost:4317

# Headers cho OTLP exporter (optional)
# Format: key1=value1,key2=value2
# otel_exporter_otlp_headers = api-key=YOUR_API_KEY

# Resource attributes bổ sung (optional)
# Format: key1=value1,key2=value2
# otel_resource_attributes = deployment.region=asia-southeast1,team=backend

# Đường dẫn URL bị loại trừ khỏi tracing trong FastAPI
# Format: endpoint1,endpoint2,... (hỗ trợ wildcard *)
otel_fastapi_excluded_urls = */favicon.ico,*/docs,*/openapi.json,*/static/*
