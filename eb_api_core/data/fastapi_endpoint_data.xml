<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Endpoint mặc định cho EB API Framework -->
        <record id="eb_api_endpoint" model="fastapi.endpoint">
            <field name="name">EB API Framework</field>
            <field name="app">eb_api</field>
            <field name="root_path">/api/v1</field>
            <field name="enable_rate_limiting" eval="True"/>
            <field name="api_title">EarnBase API</field>
            <field name="api_description">EarnBase API Framework for Odoo</field>
            <field name="api_version">1.0</field>
            <field name="enable_documentation" eval="True"/>
            <field name="active" eval="True"/>
        </record>
    </data>
</odoo> 