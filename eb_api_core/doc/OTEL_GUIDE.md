# Hướng dẫn tích hợp và sử dụng OpenTelemetry trong Odoo API

*Cập nhật lần cuối: 08/04/2025 - EarnBase Technology*

## 1. Giới thiệu

OpenTelemetry là một bộ công cụ mã nguồn mở để thu thập dữ liệu telemetry (logs, metrics, và traces) từ ứng dụng và cơ sở hạ tầng. Tài liệu này mô tả cách tích hợp và sử dụng OpenTelemetry trong Odoo API để theo dõi và gỡ lỗi ứng dụng.

### 1.1. Lợi ích

- **Theo dõi hiệu suất**: Phân tích thời gian thực hiện của từng thao tác, xác định nút thắt cổ chai
- **Gỡ lỗi dễ dàng**: Theo dõi luồng thực thi xuyên suốt các service
- **Phân tích dữ liệu**: <PERSON>hu thập metrics để phân tích và tối ưu hóa ứng dụng
- **Hỗ trợ nhiều exporter**: Tương thích với nhiều hệ thống phân tích như Jaeger, Zipkin, Prometheus, ...

### 1.2. Kiến trúc tổng thể

```
┌────────────────┐     ┌────────────────┐     ┌────────────────┐
│                │     │                │     │                │
│  Odoo API với  │     │  OpenTelemetry │     │  Hệ thống phân │
│  OpenTelemetry ├────>│  Collector     ├────>│  tích (Jaeger, │
│  Instrument    │     │  (tùy chọn)    │     │  Zipkin,...)   │
│                │     │                │     │                │
└────────────────┘     └────────────────┘     └────────────────┘
```

## 2. Cấu hình OpenTelemetry

### 2.1. Cấu hình trong odoo.conf

OpenTelemetry được cấu hình thông qua các biến môi trường hoặc trong file `odoo.conf`. Dưới đây là các cấu hình có sẵn:

```ini
# Kích hoạt OpenTelemetry
OTEL_ENABLED = true

# Tên service (sẽ xuất hiện trong các trace)
OTEL_SERVICE_NAME = odoo-api

# Các thuộc tính resource bổ sung
OTEL_RESOURCE_ATTRIBUTES = deployment.environment=development,service.version=1.0

# Loại exporter (console, otlp, ...)
OTEL_EXPORTER_TYPE = console

# Endpoint cho OTLP exporter (nếu sử dụng)
# OTEL_EXPORTER_OTLP_ENDPOINT = http://localhost:4317

# Headers cho OTLP exporter (nếu cần)
# OTEL_EXPORTER_OTLP_HEADERS = api-key=your-api-key

# Các URL được loại trừ khỏi auto-instrumentation của FastAPI
# OTEL_FASTAPI_EXCLUDED_URLS = */favicon.ico,*/docs,*/openapi.json
```

### 2.2. Các loại exporter

Hệ thống hỗ trợ các loại exporter sau:

1. **Console Exporter**: Ghi traces ra console, hữu ích khi phát triển và debug.
2. **OTLP Exporter**: Gửi traces đến OpenTelemetry Collector hoặc backend hỗ trợ OTLP.

Khi cấu hình OTLP Exporter không thành công, hệ thống sẽ tự động sử dụng Console Exporter như một fallback.

## 3. Sử dụng OpenTelemetry trong code

### 3.1. Tạo span tự động với decorator

```python
from odoo.addons.eb_api_core.utils.trace_utils import trace

# Sử dụng cho hàm thông thường
@trace()
def my_function():
    # code của bạn

# Sử dụng cho coroutine
@trace()
async def my_async_function():
    # code của bạn

# Với tên span tùy chỉnh và các attribute
@trace(name="custom_span_name", custom_attribute="value")
def my_function_with_custom_span():
    # code của bạn
```

### 3.2. Tạo span thủ công

```python
from odoo.addons.eb_api_core.utils.tracing import create_span

# Tạo span với context manager
def my_function():
    with create_span("my_custom_span", attributes={"key": "value"}) as span:
        # code của bạn
        
        # Thêm attributes vào span
        span.set_attribute("another_key", "another_value")
        
        # Ghi lại exception nếu có
        try:
            # code có thể gây lỗi
            pass
        except Exception as e:
            span.record_exception(e)
            span.set_status(opentelemetry.trace.StatusCode.ERROR, str(e))
            raise
```

### 3.3. Thêm attribute vào span hiện tại

```python
from odoo.addons.eb_api_core.utils.trace_utils import add_span_attributes

# Thêm attributes dưới dạng dictionary
add_span_attributes({"key1": "value1", "key2": "value2"})

# Hoặc sử dụng keyword arguments
add_span_attributes(key1="value1", key2="value2")
```

### 3.4. Lấy trace_id hiện tại

```python
from odoo.addons.eb_api_core.utils.tracing import get_current_trace_id

# Lấy trace_id hiện tại
trace_id = get_current_trace_id()
if trace_id:
    # Sử dụng trace_id, ví dụ: thêm vào log
    _logger.info(f"Operation completed with trace_id: {trace_id}")
```

### 3.5. Ghi nhận exception trong trace

```python
from odoo.addons.eb_api_core.utils.trace_utils import record_exception_in_trace

try:
    # code có thể gây lỗi
    pass
except Exception as e:
    # Ghi nhận exception trong trace hiện tại
    record_exception_in_trace(e, error_code="MY_ERROR_CODE")
    raise
```

### 3.6. Truyền trace context qua HTTP requests

```python
from odoo.addons.eb_api_core.utils.trace_utils import get_trace_context
import requests

# Lấy headers chứa trace context
trace_headers = get_trace_context()

# Thêm vào request để duy trì context xuyên suốt
response = requests.get("https://api.example.com", headers=trace_headers)
```

## 4. Instrument FastAPI application

Trong Odoo API, FastAPI applications được tự động instrument khi khởi tạo:

```python
from odoo.addons.eb_api_core.utils.tracing import instrument_fastapi

# Tự động instrument FastAPI app
app = FastAPI()
instrument_fastapi(app)
```

## 5. Xem và phân tích traces

### 5.1. Console Exporter

Khi sử dụng Console Exporter, spans sẽ được ghi ra console trong logs của Odoo. Ví dụ:

```
{
  "name": "my_custom_span",
  "context": {
    "trace_id": "0x1234567890abcdef1234567890abcdef",
    "span_id": "0x1234567890abcdef",
    "trace_state": "[]"
  },
  "kind": "SpanKind.INTERNAL",
  "parent_id": "0xabcdef1234567890",
  "start_time": "2025-04-08T05:14:23.123456Z",
  "end_time": "2025-04-08T05:14:23.234567Z",
  "status": {
    "status_code": "OK"
  },
  "attributes": {
    "key1": "value1",
    "key2": "value2",
    "request_id": "req_123|trace_456"
  },
  "events": [],
  "links": [],
  "resource": {
    "attributes": {
      "service.name": "odoo-api",
      "service.instance.id": "12345",
      "service.version": "1.0.0",
      "deployment.environment": "development"
    }
  }
}
```

### 5.2. OTLP Exporter

Khi sử dụng OTLP Exporter, traces sẽ được gửi đến hệ thống backend (Jaeger, Zipkin,...) và có thể xem thông qua UI của các hệ thống này.

## 6. Giải quyết vấn đề

### 6.1. Trace ID là số 0

Đôi khi span context có thể bị mất, dẫn đến trace_id là số 0. Trong trường hợp này, module sẽ tự động tạo trace_id mới để đảm bảo tính liên tục.

### 6.2. Debug OpenTelemetry

Để debug OpenTelemetry, hãy bật log level debug cho module:

```ini
# Trong odoo.conf
log_level = debug
log_handler = odoo.addons.eb_api_core:DEBUG
```

Tất cả log liên quan đến OpenTelemetry sẽ có prefix `[OTEL_*]`.

### 6.3. Vấn đề về hiệu suất

Nếu bạn gặp vấn đề về hiệu suất khi sử dụng OpenTelemetry:

1. Sử dụng BatchSpanProcessor thay vì SimpleSpanProcessor (đã được cấu hình sẵn cho OTLP Exporter)
2. Giảm số lượng attributes trong span
3. Loại trừ các endpoint không cần thiết khỏi instrumentation

## 7. Phiên bản và tương thích

Hệ thống đã được thử nghiệm với OpenTelemetry phiên bản 1.31.1 và có thể cần điều chỉnh cho các phiên bản khác.

## 8. Kết luận

OpenTelemetry cung cấp khả năng quan sát mạnh mẽ cho Odoo API. Bằng cách tích hợp OpenTelemetry, bạn có thể dễ dàng theo dõi và gỡ lỗi ứng dụng, cải thiện hiệu suất và độ tin cậy.

---

Nếu bạn có câu hỏi hoặc cần hỗ trợ, vui lòng liên hệ:
- **Email**: <EMAIL>
- **Website**: https://earnbase.io 