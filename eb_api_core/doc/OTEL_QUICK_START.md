# Hướng dẫn nhanh OpenTelemetry cho Odoo API

*Cập nhật lần cuối: 08/04/2025 - EarnBase Technology*

## Bật OpenTelemetry trong odoo.conf

```ini
OTEL_ENABLED = true
OTEL_SERVICE_NAME = odoo-api
OTEL_EXPORTER_TYPE = console  # hoặc otlp
```

## Các pattern sử dụng phổ biến

### 1. Sử dụng decorator để tạo span tự động

```python
from odoo.addons.eb_api_core.utils.trace_utils import trace

@trace()
def my_function():
    # code của bạn
```

### 2. Tạo span thủ công

```python
from odoo.addons.eb_api_core.utils.tracing import create_span

with create_span("my_operation") as span:
    # code của bạn
```

### 3. Thê<PERSON> thuộ<PERSON> t<PERSON>h vào span hiện tại

```python
from odoo.addons.eb_api_core.utils.trace_utils import add_span_attributes

# Cách 1: Sử dụng từ khóa arguments
add_span_attributes(user_id=user.id, action="login")

# Cách 2: Sử dụng dictionary
add_span_attributes({"user_id": user.id, "action": "login"})
```

### 4. Lấy trace ID hiện tại

```python
from odoo.addons.eb_api_core.utils.tracing import get_current_trace_id

trace_id = get_current_trace_id()
_logger.info(f"Operation completed [trace_id={trace_id}]")
```

### 5. Ghi nhận lỗi trong span

```python
from odoo.addons.eb_api_core.utils.trace_utils import record_exception_in_trace

try:
    # code có thể gây lỗi
except Exception as e:
    record_exception_in_trace(e, error_code="AUTH_ERROR")
    raise
```

### 6. Truyền trace context qua HTTP requests

```python
from odoo.addons.eb_api_core.utils.trace_utils import get_trace_context
import requests

headers = get_trace_context()
response = requests.get("https://api.example.com", headers=headers)
```

## Xem traces

* Với Console Exporter: Xem trong logs của Odoo
* Với OTLP Exporter: Xem trong UI của backend (Jaeger, Zipkin,...)

---

Để biết thêm chi tiết, xem file [OTEL_GUIDE.md](./OTEL_GUIDE.md). 