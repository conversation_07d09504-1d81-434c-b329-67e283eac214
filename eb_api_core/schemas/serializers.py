# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

from datetime import datetime, date
from typing import Any, Optional

import pytz

from odoo.addons.eb_api_core.utils.datetime_utils import DatetimeConfig


def serialize_datetime(dt_value: Optional[datetime], user_tz: Optional[str] = None) -> Optional[str]:
    """Chuyển đổi datetime sang định dạng chuỗi với timezone của người dùng.

    Args:
        dt_value: Giá trị datetime cần chuyển đổi (UTC)
        user_tz: Timezone của người dùng, nếu None sẽ sử dụng UTC

    Returns:
        Chuỗi datetime đã được định dạng hoặc None nếu dt_value là None
    """
    if dt_value is None:
        return None

    # Chuyển đổi từ UTC sang timezone của người dùng nếu cần
    if DatetimeConfig.USE_USER_TIMEZONE and user_tz:
        # Đảm bảo dt_value có timezone (UTC)
        if not dt_value.tzinfo:
            dt_value = pytz.UTC.localize(dt_value)
        # Chuyển đổi sang timezone của người dùng
        user_timezone = pytz.timezone(user_tz)
        dt_value = dt_value.astimezone(user_timezone)

    # Định dạng datetime
    return dt_value.strftime(DatetimeConfig.DEFAULT_DATETIME_FORMAT)


def serialize_date(date_value: Optional[date]) -> Optional[str]:
    """Chuyển đổi date sang định dạng chuỗi.

    Args:
        date_value: Giá trị date cần chuyển đổi

    Returns:
        Chuỗi date đã được định dạng hoặc None nếu date_value là None
    """
    if date_value is None:
        return None

    # Định dạng date
    return date_value.strftime(DatetimeConfig.DEFAULT_DATE_FORMAT)


def get_user_timezone(env) -> str:
    """Lấy timezone của người dùng hiện tại.

    Args:
        env: Odoo environment

    Returns:
        Timezone của người dùng, "Asia/Ho_Chi_Minh" (UTC+7) nếu không có
    """
    return env.user.tz or "Asia/Ho_Chi_Minh"  # Sử dụng timezone Việt Nam làm mặc định
