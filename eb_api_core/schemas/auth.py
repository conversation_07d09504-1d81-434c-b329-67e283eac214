# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, Field, field_validator

from .base import ResponseBase


# Authentication request models
class LoginRequest(BaseModel):
    """Yêu cầu đăng nhập với username và password"""

    username: str
    password: str


class TokenRequest(BaseModel):
    """Yêu cầu tạo token với nhiều phương thức xác thực"""

    grant_type: str = Field(
        ..., description="Can be 'password', 'refresh_token', or 'client_credentials'"
    )
    username: Optional[str] = Field(
        None, description="Required for password grant type"
    )
    password: Optional[str] = Field(
        None, description="Required for password grant type"
    )
    refresh_token: Optional[str] = Field(
        None, description="Required for refresh_token grant type"
    )
    client_id: Optional[str] = Field(
        None, description="Required for client_credentials grant type"
    )
    client_secret: Optional[str] = Field(
        None, description="Required for client_credentials grant type"
    )

    @field_validator("grant_type")
    @classmethod
    def validate_grant_type(cls, v):
        allowed_types = ["password", "refresh_token", "client_credentials"]
        if v not in allowed_types:
            raise ValueError(f"grant_type must be one of {', '.join(allowed_types)}")
        return v


class TokenRevokeRequest(BaseModel):
    """Yêu cầu thu hồi token"""

    token: str
    token_type_hint: Optional[str] = Field(
        "access_token", description="Type of token to revoke"
    )

    @field_validator("token_type_hint")
    @classmethod
    def validate_token_type_hint(cls, v):
        allowed_types = ["access_token", "refresh_token"]
        if v not in allowed_types:
            raise ValueError(
                f"token_type_hint must be one of {', '.join(allowed_types)}"
            )
        return v


# Authentication response models
class TokenInfo(BaseModel):
    """Thông tin về một token đã cấp"""

    token_id: str
    user_id: int
    token_type: str
    status: str
    expiration_date: datetime
    issued_at: datetime
    last_used: Optional[datetime] = None


class OAuth2Token(BaseModel):
    """Dữ liệu token theo chuẩn OAuth 2.0 (RFC 6749)"""

    access_token: str
    token_type: str = "Bearer"
    expires_in: int
    refresh_token: Optional[str] = None
    scope: str = "api"
    refresh_token_expires_in: Optional[int] = None
    issued_at: int  # Unix timestamp


class TokenResponse(ResponseBase[OAuth2Token]):
    """Phản hồi khi cấp token thành công theo chuẩn API EarnBase và OAuth 2.0 (RFC 6749)"""

    pass


# User information models
class UserInfo(BaseModel):
    """Thông tin người dùng đã xác thực"""

    id: int
    name: str
    email: Optional[str] = None
    login: str
    is_admin: bool = False
    is_system: bool = False
    groups: List[str] = []
    partner_id: int


class UserInfoResponse(ResponseBase[UserInfo]):
    """Phản hồi thông tin người dùng theo chuẩn API EarnBase"""

    pass


class UserTokenInfo(ResponseBase):
    """Thông tin user và tokens liên quan"""

    user: UserInfo
    tokens: List[TokenInfo] = []
