# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field

from odoo.addons.eb_api_core.schemas.base import ResponseBase, PaginatedResponse


class TagInfo(BaseModel):
    """Thông tin về thẻ tài liệu"""
    id: int
    name: str
    color: Optional[str] = None


class DocumentInfo(BaseModel):
    """Thông tin chi tiết về tài liệu"""
    id: int
    name: str
    file_name: Optional[str] = None
    file_type: Optional[str] = None
    file_size: Optional[int] = None
    url: Optional[str] = None
    create_date: Optional[datetime] = None
    write_date: Optional[datetime] = None
    owner_id: Optional[int] = None
    owner_name: Optional[str] = None
    folder_id: Optional[int] = None
    folder_name: Optional[str] = None
    document_type: Optional[str] = None
    description: Optional[str] = None
    tags: List[TagInfo] = []


class DocumentList(BaseModel):
    """Danh sách tài liệu với phân trang"""
    total: int
    page: int
    page_size: int
    items: List[DocumentInfo] = []


class DocumentCreate(BaseModel):
    """Dữ liệu để tạo tài liệu mới"""
    name: str
    description: Optional[str] = None
    document_type: Optional[str] = None
    folder_id: Optional[int] = None
    tag_ids: Optional[List[int]] = None
    file_content: Optional[str] = Field(None, description="Base64 encoded file content")
    file_type: Optional[str] = None
    file_name: Optional[str] = None
    url: Optional[str] = None


class DocumentUpdate(BaseModel):
    """Dữ liệu để cập nhật tài liệu"""
    name: Optional[str] = None
    description: Optional[str] = None
    document_type: Optional[str] = None
    folder_id: Optional[int] = None
    tag_ids: Optional[List[int]] = None
    file_content: Optional[str] = Field(None, description="Base64 encoded file content")
    file_type: Optional[str] = None
    file_name: Optional[str] = None
    url: Optional[str] = None


class DocumentLink(BaseModel):
    """Thông tin liên kết tài liệu với module"""
    document_id: int
    module_type: str
    module_id: int
    link_date: datetime


class DocumentResponse(ResponseBase[DocumentInfo]):
    """Phản hồi cho các thao tác với tài liệu đơn lẻ"""
    pass


class DocumentListResponse(PaginatedResponse):
    """Phản hồi cho các thao tác lấy danh sách tài liệu"""
    data: List[DocumentInfo]


class DocumentLinkResponse(ResponseBase[DocumentLink]):
    """Phản hồi cho các thao tác liên kết tài liệu"""
    pass
