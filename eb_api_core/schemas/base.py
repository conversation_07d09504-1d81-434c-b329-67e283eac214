# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

from typing import Any, Dict, List, Optional, TypeVar, Generic
from datetime import datetime, date
from pydantic import BaseModel, Field, model_validator, ConfigDict

from odoo.addons.eb_api_core.utils.request import get_meta_data
from odoo.addons.eb_api_core.utils.datetime_utils import format_meta_timestamp


T = TypeVar('T')


class ResponseBase(BaseModel, Generic[T]):
    """Base model cho tất cả API response"""

    success: bool = True
    message: str = "Operation completed successfully"
    data: Optional[T] = None
    meta: Dict[str, Any] = Field(default_factory=lambda: {
        "timestamp": format_meta_timestamp(datetime.now())
    })

    # Sử dụng ConfigDict thay vì class Config
    model_config = ConfigDict(
        from_attributes=True,
    )

    def model_dump(self, **kwargs) -> Dict[str, Any]:
        """Serialize model với định dạng datetime tùy chỉnh"""
        result = {
            "success": self.success,
            "message": self.message,
            "meta": self.meta,
        }

        # Xử lý data tùy thuộc vào loại
        if self.data is not None:
            result["data"] = self.data

        return result

    @classmethod
    def success_response(cls, message: str = "Success", data: Optional[T] = None):
        """Tạo response thành công với message và data.

        Args:
            message: Message thành công
            data: Dữ liệu trả về (tùy chọn)

        Returns:
            Đối tượng ResponseBase được cấu hình
        """
        return cls(
            success=True,
            message=message,
            data=data,
            meta=get_meta_data(),
        )


class PaginationMeta(BaseModel):
    """Metadata cho phân trang"""

    page: int
    per_page: int
    total: int
    pages: int


class PaginatedResponse(ResponseBase):
    """Base model cho response có phân trang"""

    data: List[Any]

    # Không sử dụng trường pagination riêng biệt nữa
    # Thay vào đó, sử dụng trường meta của ResponseBase

    @classmethod
    def paginated_response(
        cls,
        data: List[Any],
        page: int,
        per_page: int,
        total: int,
        message: Optional[str] = "Dữ liệu được lấy thành công",
    ):
        """Tạo paginated response"""
        pages = (total + per_page - 1) // per_page if per_page > 0 else 0

        # Tạo meta data với thông tin phân trang
        meta_data = {
            "page": page,
            "per_page": per_page,
            "total": total,
            "pages": pages,
            "timestamp": format_meta_timestamp(datetime.now())
        }

        return cls(
            success=True,
            message=message,
            data=data,
            meta=meta_data,
        )

    def model_dump(self, **kwargs) -> Dict[str, Any]:
        """Serialize model với định dạng datetime tùy chỉnh"""
        result = {
            "success": self.success,
            "message": self.message,
            "data": self.data,
            "meta": self.meta
        }
        return result


class ApiInfo(BaseModel):
    """Thông tin API"""

    version: str
    name: str
    description: str
    documentation_url: Optional[str] = None


class ApiStatus(ResponseBase):
    """API status response"""

    info: ApiInfo
    server_time: datetime
    status: str = "operational"
    modules: Optional[List[str]] = None

    @model_validator(mode='after')
    def format_datetime_fields(self):
        """Format datetime fields according to configuration"""
        if self.server_time:
            # Chuyển đổi sang timezone của người dùng và định dạng lại
            self.server_time = datetime.now()  # Sử dụng thời gian hiện tại
        return self

    def model_dump(self, **kwargs) -> Dict[str, Any]:
        """Serialize model với định dạng datetime tùy chỉnh"""
        result = {
            "success": self.success,
            "message": self.message,
            "meta": self.meta,
            "info": self.info,
            "status": self.status,
            "server_time": format_meta_timestamp(self.server_time),
        }

        if self.modules is not None:
            result["modules"] = self.modules

        return result
