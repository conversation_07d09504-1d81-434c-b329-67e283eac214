# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

from typing import Any, Dict, Type, TypeVar

from pydantic import BaseModel, ConfigDict


T = TypeVar('T', bound=BaseModel)


def create_model_config() -> ConfigDict:
    """Tạo cấu hình cho Pydantic model.

    Returns:
        ConfigDict cho Pydantic model
    """
    # Sử dụng from_attributes thay vì json_encoders
    # Serialization sẽ được xử lý trong model_serializer
    return ConfigDict(
        from_attributes=True,
    )


class ApiModelConfig:
    """Lớp tiện ích để tạo model config cho các schema API."""

    @staticmethod
    def get_config() -> Dict[str, Any]:
        """<PERSON><PERSON><PERSON> c<PERSON>u hình cho model.

        Returns:
            Dict cấu hình cho model_config
        """
        return create_model_config()

    @staticmethod
    def configure_model(model_class: Type[T]) -> Type[T]:
        """Cấu hình model.

        Args:
            model_class: Lớp model cần cấu hình

        Returns:
            Lớp model đã được cấu hình
        """
        model_class.model_config = create_model_config()
        return model_class
