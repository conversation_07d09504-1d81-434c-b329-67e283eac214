# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

from typing import Any, Dict, List, Optional
from enum import Enum
from pydantic import BaseModel


class ErrorCode(str, Enum):
    """Mã lỗi chuẩn cho API"""

    # Lỗi xác thực
    UNAUTHORIZED = "UNAUTHORIZED"
    INVALID_CREDENTIALS = "INVALID_CREDENTIALS"
    TOKEN_EXPIRED = "TOKEN_EXPIRED"
    INVALID_TOKEN = "INVALID_TOKEN"

    # Lỗi dữ liệu
    VALIDATION_ERROR = "VALIDATION_ERROR"
    INVALID_REQUEST = "INVALID_REQUEST"
    NOT_FOUND = "NOT_FOUND"
    ALREADY_EXISTS = "ALREADY_EXISTS"
    CONFLICT = "CONFLICT"

    # Lỗi quyền
    INSUFFICIENT_PERMISSIONS = "INSUFFICIENT_PERMISSIONS"
    ACCESS_DENIED = "ACCESS_DENIED"

    # Lỗi hệ thống
    INTERNAL_SERVER_ERROR = "INTERNAL_SERVER_ERROR"
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE"
    DEPENDENCY_ERROR = "DEPENDENCY_ERROR"
    BUSINESS_LOGIC_ERROR = "BUSINESS_LOGIC_ERROR"

    # Thêm mã lỗi chi tiết cho business logic
    RESOURCE_LIMIT_EXCEEDED = "RESOURCE_LIMIT_EXCEEDED"
    PAYMENT_REQUIRED = "PAYMENT_REQUIRED"
    RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED"
    CONCURRENT_UPDATE = "CONCURRENT_UPDATE"
    IDEMPOTENCY_VIOLATION = "IDEMPOTENCY_VIOLATION"

    # Thêm mã lỗi chi tiết cho dữ liệu
    INVALID_FORMAT = "INVALID_FORMAT"
    MISSING_REQUIRED_FIELD = "MISSING_REQUIRED_FIELD"
    INVALID_REFERENCE = "INVALID_REFERENCE"

    # Thêm mã lỗi cho các tác vụ
    OPERATION_TIMEOUT = "OPERATION_TIMEOUT"
    OPERATION_CANCELED = "OPERATION_CANCELED"
    ASYNC_OPERATION_PENDING = "ASYNC_OPERATION_PENDING"
    DATABASE_ERROR = "DATABASE_ERROR"


# Mapping từ error codes sang HTTP status codes
ERROR_CODE_TO_STATUS = {
    ErrorCode.UNAUTHORIZED: 401,
    ErrorCode.INVALID_CREDENTIALS: 401,
    ErrorCode.TOKEN_EXPIRED: 401,
    ErrorCode.INVALID_TOKEN: 401,
    ErrorCode.VALIDATION_ERROR: 400,
    ErrorCode.INVALID_REQUEST: 400,
    ErrorCode.NOT_FOUND: 404,
    ErrorCode.ALREADY_EXISTS: 409,
    ErrorCode.CONFLICT: 409,
    ErrorCode.INSUFFICIENT_PERMISSIONS: 403,
    ErrorCode.ACCESS_DENIED: 403,
    ErrorCode.INTERNAL_SERVER_ERROR: 500,
    ErrorCode.SERVICE_UNAVAILABLE: 503,
    ErrorCode.DEPENDENCY_ERROR: 502,
    ErrorCode.BUSINESS_LOGIC_ERROR: 422,
    # Mapping cho các mã lỗi mới
    ErrorCode.RESOURCE_LIMIT_EXCEEDED: 429,
    ErrorCode.PAYMENT_REQUIRED: 402,
    ErrorCode.RATE_LIMIT_EXCEEDED: 429,
    ErrorCode.CONCURRENT_UPDATE: 409,
    ErrorCode.IDEMPOTENCY_VIOLATION: 409,
    ErrorCode.INVALID_FORMAT: 400,
    ErrorCode.MISSING_REQUIRED_FIELD: 400,
    ErrorCode.INVALID_REFERENCE: 400,
    ErrorCode.OPERATION_TIMEOUT: 408,
    ErrorCode.OPERATION_CANCELED: 499,
    ErrorCode.ASYNC_OPERATION_PENDING: 202,
    ErrorCode.DATABASE_ERROR: 500,
}


class ErrorDetail(BaseModel):
    """Chi tiết lỗi cho response error"""

    field: Optional[str] = None
    code: Optional[str] = None
    message: str


class ErrorResponse(BaseModel):
    """Base model cho tất cả error response"""

    success: bool = False
    error: str
    error_code: Optional[str] = None
    details: Optional[List[ErrorDetail]] = None
    meta: Optional[Dict[str, Any]] = None

    @classmethod
    def error_response(
        cls,
        error: str,
        error_code: Optional[str] = None,
        details: Optional[List[Dict[str, Any]]] = None,
        meta: Optional[Dict[str, Any]] = None,
    ):
        """Tạo error response với thông tin chi tiết"""
        response_data = {
            "success": False,
            "error": error,
        }

        if error_code:
            response_data["error_code"] = error_code

        if details:
            error_details = [ErrorDetail(**detail) for detail in details]
            response_data["details"] = error_details

        if meta:
            response_data["meta"] = meta

        return cls(**response_data)
