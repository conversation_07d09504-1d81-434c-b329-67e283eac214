# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import uuid
import logging
from contextvars import ContextVar
from typing import Optional, Callable

from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware


class RequestContext:
    """
    Class lưu trữ thông tin request trong context API của opentelemetry
    """

    def __init__(self, request_id: str = None):
        """
        Khởi tạo RequestContext với request_id, nếu không cung cấp sẽ tạo UUID mới

        Args:
            request_id: Request ID, nếu None sẽ tạo UUID mới
        """
        self.request_id = request_id or str(uuid.uuid4())


# Context variable để lưu trữ requestId
request_id_var = ContextVar("request_id", default=RequestContext())


def get_request_id() -> Optional[str]:
    """Lấy request ID từ context hiện tại"""
    request_context = request_id_var.get()
    if request_context and hasattr(request_context, 'request_id'):
        return request_context.request_id
    return None


class RequestIdMiddleware(BaseHTTPMiddleware):
    """Middleware tạo và gắn request ID cho mỗi request"""

    def __init__(self, app, prefix: str = "req-", header_name: str = "x-request-id"):
        super().__init__(app)
        self.prefix = prefix
        self.header_name = header_name

    async def dispatch(self, request: Request, call_next: Callable):
        # Tạo requestId mới hoặc lấy từ header nếu đã có
        request_id = request.headers.get(self.header_name)

        if not request_id:
            request_id = f"{self.prefix}{uuid.uuid4().hex[:8]}"

        # Ghi log chi tiết về request với requestId
        logging.getLogger("odoo.addons.eb_api_core.middleware").info(
            f"Request received: {request.method} {request.url.path} - RequestID: {request_id}"
        )

        # Lưu requestId vào context
        token = request_id_var.set(RequestContext(request_id))

        try:
            # Xử lý request
            response = await call_next(request)

            # Thêm requestId vào header response
            response.headers[self.header_name] = request_id

            # Ghi log khi gửi response
            logging.getLogger("odoo.addons.eb_api_core.middleware").info(
                f"Response sent: {response.status_code} - RequestID: {request_id}"
            )

            return response
        except Exception as e:
            # Ghi log lỗi với requestId
            logging.getLogger("odoo.addons.eb_api_core.middleware").error(
                f"Error processing request - RequestID: {request_id} - Error: {str(e)}"
            )
            raise
        finally:
            # Reset context
            request_id_var.reset(token)


# Custom LoggerAdapter để thêm requestId vào log messages
class RequestIdLoggerAdapter(logging.LoggerAdapter):
    """Logger adapter để gắn request ID vào mỗi log message"""

    def process(self, msg, kwargs):
        request_id = get_request_id()
        if request_id:
            # Format phù hợp với chuỗi log của Odoo
            # Vẫn giữ cấu trúc log gốc nhưng thêm requestId ở đầu message
            return f"{msg} [requestId: {request_id}]", kwargs
        return msg, kwargs


# Hàm để lấy logger với RequestId
def get_logger(name: str) -> logging.LoggerAdapter:
    """Lấy logger với adapter RequestId"""
    logger = logging.getLogger(name)
    return RequestIdLoggerAdapter(logger, {})
