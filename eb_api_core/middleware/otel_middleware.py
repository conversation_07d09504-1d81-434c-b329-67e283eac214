# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import uuid
import logging
from typing import Callable, Dict, Any, Optional
from logging import Logger

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from opentelemetry import trace, context, baggage
from opentelemetry.propagate import extract, inject
from opentelemetry.trace import Span, Status, StatusCode, SpanContext, TraceFlags
from opentelemetry.baggage.propagation import W3CBaggagePropagator
from opentelemetry.trace.propagation.tracecontext import TraceContextTextMapPropagator
from opentelemetry.trace import SpanKind
from opentelemetry.context.context import Context

from odoo.addons.eb_api_core.middleware.request_id import get_request_id

_logger = logging.getLogger(__name__)


class OpenTelemetryMiddleware(BaseHTTPMiddleware):
    """
    Middleware để tích hợp OpenTelemetry vào FastAPI.

    Middleware này sẽ tạo span cho mỗi request và thêm các attribute cần thiết.
    """

    async def dispatch(
        self, request: Request, call_next: Callable
    ) -> Response:
        """
        Xử lý request, tạo span và propagate context.

        Args:
            request: FastAPI request
            call_next: Hàm xử lý request tiếp theo

        Returns:
            FastAPI response
        """
        # Khởi tạo logger
        _logger = logging.getLogger(__name__)

        # Get current tracer
        tracer = trace.get_tracer(__name__)

        # Lấy carrier từ headers của request
        carrier = dict(request.headers)

        # Extract span context từ headers
        extracted_context = extract(carrier)

        # Kiểm tra extracted_context và xử lý trường hợp không tìm thấy
        valid_context = False

        if extracted_context:
            # Kiểm tra xem context có hợp lệ không
            current_span = trace.get_current_span(extracted_context)
            if current_span and hasattr(current_span, "get_span_context"):
                ctx = current_span.get_span_context()
                if ctx.is_valid and ctx.trace_id != 0:
                    _logger.debug("[OTEL_MIDDLEWARE] Tìm thấy span context hợp lệ trong request headers")
                    valid_context = True
                else:
                    _logger.debug("[OTEL_MIDDLEWARE] Span context không hợp lệ hoặc trace_id là 0")
            else:
                _logger.debug("[OTEL_MIDDLEWARE] Không tìm thấy current span hợp lệ")
        else:
            _logger.debug("[OTEL_MIDDLEWARE] Không tìm thấy span context trong request headers")

        # Nếu không có context hợp lệ, sẽ sử dụng context trống
        # OpenTelemetry sẽ tự động tạo trace_id và span_id mới
        if not valid_context:
            # _logger.debug("[OTEL_MIDDLEWARE] Không tìm thấy context hợp lệ, sẽ sử dụng context trống")
            extracted_context = Context()

        # Lấy request_id từ context đã được thiết lập bởi RequestIdMiddleware
        request_id = get_request_id()
        _logger.debug(f"[OTEL_MIDDLEWARE] Request ID: {request_id}")

        # Thêm baggage propagator
        baggage_propagator = W3CBaggagePropagator()
        # Extract baggage (không sử dụng trong middleware này nhưng có thể hữu ích cho các span con)
        baggage_propagator.extract(carrier=carrier)

        # Start a new span with extracted context
        with tracer.start_as_current_span(
            f"{request.method} {request.url.path}",
            context=extracted_context,
            kind=SpanKind.SERVER,
        ) as span:
            span_ctx = span.get_span_context()

            # Đảm bảo trace_id và span_id hợp lệ
            if span_ctx.trace_id == 0 or span_ctx.span_id == 0:
                # Tạo trace_id và span_id mới nếu không hợp lệ
                new_trace_id = int(uuid.uuid4().hex[:16], 16)
                new_span_id = int(uuid.uuid4().hex[:8], 16)

                # Sử dụng trace_id và span_id mới cho request state
                span_id_hex = format(new_span_id, "016x")
                trace_id_hex = format(new_trace_id, "032x")

                _logger.warning(f"[OTEL_MIDDLEWARE] Phát hiện span với ID không hợp lệ, tạo mới: trace_id={trace_id_hex}, span_id={span_id_hex}")
            else:
                # Sử dụng trace_id và span_id từ span context
                span_id_hex = format(span_ctx.span_id, "016x")
                trace_id_hex = format(span_ctx.trace_id, "032x")
                _logger.debug(f"[OTEL_MIDDLEWARE] Tạo span: span_id={span_id_hex}, trace_id={trace_id_hex}")

            # Thêm các attribute cơ bản cho span
            span.set_attribute("http.method", request.method)
            span.set_attribute("http.url", str(request.url))
            span.set_attribute("http.request_id", request_id)
            span.set_attribute("http.host", request.url.hostname)
            span.set_attribute("http.path", request.url.path)
            span.set_attribute("http.scheme", request.url.scheme)

            # Thêm trace ID và span ID vào request state
            # Đảm bảo rằng chúng ta sử dụng các giá trị đã được xử lý ở trên
            request.state.trace_id = trace_id_hex
            request.state.span_id = span_id_hex

            _logger.info(f"[OTEL_MIDDLEWARE] Tracing thông tin: trace_id={request.state.trace_id}, span_id={request.state.span_id}, request_id={request_id}")

            # Gọi middleware tiếp theo
            try:
                response = await call_next(request)

                # Thêm response headers
                response.headers["X-Trace-ID"] = request.state.trace_id
                response.headers["X-Span-ID"] = request.state.span_id
                response.headers["X-Request-ID"] = request_id

                _logger.debug(f"[OTEL_MIDDLEWARE] Gửi response với headers: X-Trace-ID={request.state.trace_id}, X-Span-ID={request.state.span_id}")

                # Cập nhật trạng thái span dựa trên response
                self.set_span_status_from_response(_logger, span, response)

                return response
            except Exception as e:
                _logger.error(f"[OTEL_MIDDLEWARE] Lỗi trong request pipeline: {str(e)}", exc_info=True)
                span.set_status(Status(StatusCode.ERROR))
                span.record_exception(e)
                raise
            finally:
                # Không cần reset context vì RequestIdMiddleware sẽ làm điều đó
                pass

    def set_span_status_from_response(
        self, logger: logging.Logger, span: Span, response: Optional[Response]
    ) -> None:
        """
        Set trạng thái của span dựa trên HTTP status của response.

        Args:
            logger: Logger instance
            span: Current span
            response: FastAPI response object
        """
        if not span:
            logger.warning("[OTEL_MIDDLEWARE] Không thể đặt trạng thái span: không có active span")
            return

        try:
            if response is None:
                logger.warning("[OTEL_MIDDLEWARE] Response là None, đặt trạng thái span thành ERROR")
                span.set_status(Status(StatusCode.ERROR))
                return

            if not hasattr(response, 'status_code'):
                logger.warning("[OTEL_MIDDLEWARE] Response không có thuộc tính status_code, đặt trạng thái span thành ERROR")
                span.set_status(Status(StatusCode.ERROR))
                return

            status_code = response.status_code
            logger.debug(f"[OTEL_MIDDLEWARE] Mã trạng thái response: {status_code}")

            # Thêm status code attribute
            span.set_attribute("http.status_code", status_code)

            if 200 <= status_code < 400:
                span.set_status(Status(StatusCode.OK))
            else:
                span.set_status(Status(StatusCode.ERROR))
                span.add_event(
                    name="http.error",
                    attributes={"http.status_code": status_code},
                )
        except Exception as e:
            logger.error(f"[OTEL_MIDDLEWARE] Lỗi khi đặt trạng thái span: {str(e)}", exc_info=True)
            span.set_status(Status(StatusCode.ERROR))
