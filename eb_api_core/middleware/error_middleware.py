# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and <PERSON>tis <PERSON> <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse
from fastapi import status
from fastapi.exceptions import RequestValidationError, HTTPException

from odoo.addons.eb_api_core.schemas.error import ErrorResponse, ErrorCode
from odoo.addons.eb_api_core.utils.trace_utils import record_exception_in_trace
from odoo.addons.eb_api_core.utils.request import get_meta_data
from odoo.addons.eb_api_core.utils.exceptions import format_validation_errors

_logger = logging.getLogger(__name__)


class GlobalExceptionMiddleware(BaseHTTPMiddleware):
    """Middleware để bắt tất cả exceptions không được xử lý.

    <PERSON><PERSON><PERSON> bảo không có exception nào lọt ra ngoài mà không được chuyển đổi
    thành API response chuẩn và được ghi lại.
    """

    async def dispatch(self, request, call_next):
        """Xử lý request và bắt tất cả exceptions.

        Args:
            request: Starlette Request
            call_next: Hàm next trong middleware chain

        Returns:
            Response: Starlette Response
        """
        try:
            return await call_next(request)
        except HTTPException as exc:
            # Xử lý FastAPI HTTP exceptions (401, 403, 404, etc.)
            _logger.info(f"HTTP exception: {exc.status_code}: {exc.detail}")

            # Ưu tiên error_code từ header X-Error-Code nếu có
            headers = getattr(exc, "headers", None) or {}
            error_code_str = headers.get("X-Error-Code")

            if error_code_str:
                # Sử dụng error_code từ header
                try:
                    error_code = ErrorCode(error_code_str)
                except ValueError:
                    # Fallback nếu error_code không hợp lệ
                    error_code = ErrorCode.INTERNAL_SERVER_ERROR
            else:
                # Fallback mapping dựa trên status code
                if exc.status_code == 401:
                    error_code = ErrorCode.UNAUTHORIZED
                elif exc.status_code == 403:
                    error_code = ErrorCode.INSUFFICIENT_PERMISSIONS
                elif exc.status_code == 404:
                    error_code = ErrorCode.NOT_FOUND
                elif exc.status_code == 409:
                    error_code = ErrorCode.CONFLICT
                else:
                    error_code = ErrorCode.INTERNAL_SERVER_ERROR

            record_exception_in_trace(exc, error_code.value)

            # Tạo response error với status code đúng
            error_resp = ErrorResponse(
                error=exc.detail or "HTTP Exception",
                error_code=error_code,
                meta=get_meta_data(),
            )

            # Sử dụng model_dump thay vì dict
            return JSONResponse(
                status_code=exc.status_code,
                content=error_resp.model_dump(),
                headers=headers,
            )
        except RequestValidationError as e:
            # Xử lý FastAPI validation errors
            _logger.warning(f"Request validation error: {e.errors()}")

            # Ghi nhận vào trace
            record_exception_in_trace(e, ErrorCode.VALIDATION_ERROR.value)

            # Tạo error details
            error_details = format_validation_errors(e.errors())

            # Tạo error response
            error_resp = ErrorResponse(
                error="Request validation error",
                error_code=ErrorCode.VALIDATION_ERROR,
                details=error_details,
                meta=get_meta_data(),
            )

            # Sử dụng model_dump thay vì dict
            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST, content=error_resp.model_dump()
            )
        except Exception as e:
            # Ghi log lỗi
            _logger.exception(f"Unhandled exception in middleware: {str(e)}")

            # Ghi nhận vào trace
            error_code = ErrorCode.INTERNAL_SERVER_ERROR.value
            record_exception_in_trace(e, error_code)

            # Tạo response error
            error_resp = ErrorResponse(
                error="Internal server error",
                error_code=ErrorCode.INTERNAL_SERVER_ERROR,
                meta=get_meta_data(),
            )

            # Sử dụng model_dump thay vì dict
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content=error_resp.model_dump(),
            )
