# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

"""
Middleware kết hợp RequestId và OpenTelemetry.

Middleware này kết hợp chức năng của RequestIdMiddleware và OpenTelemetryMiddleware
để đảm bảo tính nhất quán của request ID và trace ID trong toàn bộ ứng dụng.
"""

import uuid
import logging
import os
from typing import Callable, Dict, Any, Optional
from contextvars import ContextVar
from logging import Logger

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from opentelemetry import trace
from opentelemetry.propagate import extract
from opentelemetry.trace import Span, Status, StatusCode
from opentelemetry.baggage.propagation import W3CBaggagePropagator
from opentelemetry.trace.propagation.tracecontext import TraceContextTextMapPropagator
from opentelemetry.trace import SpanKind
from opentelemetry.context.context import Context

_logger = logging.getLogger(__name__)


class RequestContext:
    """
    Class lưu trữ thông tin request trong context API
    """

    def __init__(self, request_id: str = None):
        """
        Khởi tạo RequestContext với request_id, nếu không cung cấp sẽ tạo UUID mới

        Args:
            request_id: Request ID, nếu None sẽ tạo UUID mới
        """
        self.request_id = request_id or str(uuid.uuid4())


# Context variable để lưu trữ requestId
request_id_var = ContextVar("request_id", default=RequestContext())


def get_request_id() -> Optional[str]:
    """Lấy request ID từ context hiện tại"""
    request_context = request_id_var.get()
    if request_context and hasattr(request_context, 'request_id'):
        return request_context.request_id
    return None


# Không cần ghi đè hàm get_request_id trong module request_id
# Vì chúng ta sẽ sử dụng hàm này trực tiếp


def format_trace_id(trace_id: int) -> str:
    """
    Định dạng trace ID thành chuỗi hex 32 ký tự.

    Args:
        trace_id: Trace ID dạng số nguyên

    Returns:
        Chuỗi hex 32 ký tự biểu diễn trace ID
    """
    return format(trace_id, "032x")


def get_jaeger_trace_id(trace_id_hex: str) -> str:
    """
    Chuyển đổi trace ID từ định dạng OpenTelemetry sang định dạng Jaeger.

    Args:
        trace_id_hex: Trace ID dạng chuỗi hex 32 ký tự

    Returns:
        Trace ID dạng chuỗi hex 32 ký tự theo định dạng Jaeger
    """
    # Bỏ số 0 ở đầu
    clean_id = trace_id_hex.lstrip("0")
    # Nếu trace ID rỗng sau khi bỏ số 0, trả về "0"
    if not clean_id:
        return "0"
    return clean_id


class TracingMiddleware(BaseHTTPMiddleware):
    """
    Middleware kết hợp RequestId và OpenTelemetry.

    Middleware này thực hiện hai chức năng:
    1. Tạo và quản lý request ID
    2. Thiết lập trace context và tạo span cho mỗi request
    """

    def __init__(
        self,
        app,
        prefix: str = "req-",
        header_name: str = "x-request-id"
    ):
        super().__init__(app)
        self.prefix = prefix
        self.header_name = header_name
        # Sử dụng global propagator thay vì tạo mới
        # self.propagator = TraceContextTextMapPropagator()
        # self.baggage_propagator = W3CBaggagePropagator()

    async def dispatch(self, request: Request, call_next: Callable):
        # PHẦN 1: XỬ LÝ REQUEST ID

        # Tạo requestId mới hoặc lấy từ header nếu đã có
        request_id = request.headers.get(self.header_name)

        if not request_id:
            request_id = f"{self.prefix}{uuid.uuid4().hex[:8]}"

        # Ghi log chi tiết về request với requestId
        _logger.info(
            f"Request received: {request.method} {request.url.path} - RequestID: {request_id}"
        )

        # Lưu requestId vào context
        request_context = RequestContext(request_id)
        token = request_id_var.set(request_context)

        # PHẦN 2: XỬ LÝ OPENTELEMETRY TRACING

        # Lấy carrier từ request headers
        carrier = {k.lower(): v for k, v in request.headers.items()}

        # Extract context từ carrier
        # Sử dụng hàm extract mặc định với getter mặc định
        extracted_context = extract(carrier)

        # Kiểm tra extracted_context và xử lý trường hợp không tìm thấy
        valid_context = False

        if extracted_context:
            # Kiểm tra xem context có hợp lệ không
            current_span = trace.get_current_span(extracted_context)
            if current_span and hasattr(current_span, "get_span_context"):
                ctx = current_span.get_span_context()
                if ctx.is_valid and ctx.trace_id != 0:
                    # _logger.debug("[TRACING_MIDDLEWARE] Tìm thấy span context hợp lệ trong request headers")
                    valid_context = True
                else:
                    # _logger.debug("[TRACING_MIDDLEWARE] Span context không hợp lệ hoặc trace_id là 0")
                    pass
            else:
                # _logger.debug("[TRACING_MIDDLEWARE] Không tìm thấy current span hợp lệ")
                pass
        else:
            # _logger.debug("[TRACING_MIDDLEWARE] Không tìm thấy span context trong request headers")
            pass

        # Nếu không có context hợp lệ, sẽ sử dụng context trống
        if not valid_context:
            # _logger.debug("[TRACING_MIDDLEWARE] Không tìm thấy context hợp lệ, sẽ sử dụng context trống")
            extracted_context = Context()

        # Lấy tracer
        tracer = trace.get_tracer("odoo-api")

        # Start a new span with extracted context
        with tracer.start_as_current_span(
            f"{request.method} {request.url.path}",
            context=extracted_context,
            kind=SpanKind.SERVER,
        ) as span:
            span_ctx = span.get_span_context()

            # Đảm bảo trace_id và span_id hợp lệ
            if span_ctx.trace_id == 0 or span_ctx.span_id == 0:
                # Tạo trace_id 128-bit đầy đủ và span_id mới nếu không hợp lệ
                # Tạo UUID mới
                trace_uuid = uuid.uuid4()
                # Chuyển đổi UUID thành bytes và sau đó thành số nguyên
                # Điều này đảm bảo byte order nhất quán
                trace_bytes = trace_uuid.bytes
                # Chuyển đổi 16 bytes thành số nguyên 128-bit
                new_trace_id = int.from_bytes(trace_bytes, byteorder='big')
                # Tạo span_id 64-bit
                span_bytes = os.urandom(8)  # 8 bytes ngẫu nhiên
                new_span_id = int.from_bytes(span_bytes, byteorder='big')

                # Sử dụng trace_id và span_id mới cho request state
                span_id_hex = format(new_span_id, "016x")
                trace_id_hex = format_trace_id(new_trace_id)

                # Tính toán trace ID theo định dạng Jaeger
                jaeger_trace_id = get_jaeger_trace_id(trace_id_hex)

                # _logger.warning(f"[TRACING_MIDDLEWARE] Phát hiện span với ID không hợp lệ, tạo mới: trace_id={trace_id_hex}, jaeger_trace_id={jaeger_trace_id}, span_id={span_id_hex}")
            else:
                # Sử dụng trace_id và span_id từ span context
                span_id_hex = format(span_ctx.span_id, "016x")
                trace_id_hex = format(span_ctx.trace_id, "032x")
                # _logger.debug(f"[TRACING_MIDDLEWARE] Tạo span: span_id={span_id_hex}, trace_id={trace_id_hex}")

            # Thêm trace ID và span ID vào request state
            # Đảm bảo rằng chúng ta sử dụng các giá trị đã được xử lý ở trên
            request.state.trace_id = trace_id_hex
            request.state.span_id = span_id_hex

            # Thêm request_id vào span attributes
            span.set_attribute("request_id", request_id)

            # Log thông tin tracing
            # Tính toán trace ID theo định dạng Jaeger
            jaeger_trace_id = get_jaeger_trace_id(trace_id_hex)
            # _logger.info(f"[TRACING_MIDDLEWARE] Tracing thông tin: trace_id={trace_id_hex}, jaeger_trace_id={jaeger_trace_id}, span_id={span_id_hex}, request_id={request_id}")

            try:
                # Xử lý request
                response = await call_next(request)

                # Thêm requestId và trace ID vào header response
                response.headers[self.header_name] = request_id
                response.headers["x-trace-id"] = trace_id_hex  # Sử dụng chữ thường để thống nhất
                response.headers["x-span-id"] = span_id_hex  # Sử dụng chữ thường để thống nhất

                # Thêm trace ID vào header theo định dạng W3C Trace Context
                # Định dạng: 00-<trace-id>-<span-id>-<trace-flags>
                # Tham khảo: https://www.w3.org/TR/trace-context/
                trace_flags = "01"  # sampled
                trace_parent = f"00-{trace_id_hex}-{span_id_hex}-{trace_flags}"
                response.headers["traceparent"] = trace_parent

                # Thiết lập trạng thái span dựa trên response
                self.set_span_status_from_response(_logger, span, response)

                # Ghi log khi gửi response
                _logger.info(
                    f"Response sent: {response.status_code} - RequestID: {request_id}"
                )
                # Hiển thị cả trace ID đầy đủ và trace ID theo định dạng Jaeger
                jaeger_trace_id = get_jaeger_trace_id(trace_id_hex)
                # _logger.debug(f"[TRACING_MIDDLEWARE] Gửi response với headers: x-trace-id={trace_id_hex}, jaeger_trace_id={jaeger_trace_id}, x-span-id={span_id_hex}")
                # _logger.debug(f"[TRACING_MIDDLEWARE] Mã trạng thái response: {response.status_code}")

                return response
            except Exception as e:
                # Ghi log lỗi với requestId
                _logger.error(
                    f"Error processing request - RequestID: {request_id} - Error: {str(e)}"
                )
                # _logger.error(f"[TRACING_MIDDLEWARE] Lỗi trong request pipeline: {str(e)}", exc_info=True)
                span.set_status(Status(StatusCode.ERROR))
                span.record_exception(e)
                raise
            finally:
                # Reset context
                request_id_var.reset(token)

    def set_span_status_from_response(
        self, logger: logging.Logger, span: Span, response: Optional[Response]
    ) -> None:
        """Thiết lập trạng thái span dựa trên response.

        Args:
            logger: Logger để ghi log
            span: Span hiện tại
            response: Response từ request
        """
        if not response:
            # logger.warning("[TRACING_MIDDLEWARE] Không có response để thiết lập trạng thái span")
            return

        status_code = response.status_code
        if status_code >= 400:
            description = f"HTTP Status Code: {status_code}"
            span.set_status(Status(StatusCode.ERROR, description=description))
            span.set_attribute("http.status_code", status_code)
            # logger.debug(f"[TRACING_MIDDLEWARE] Thiết lập span status ERROR với mã {status_code}")
        else:
            span.set_status(Status(StatusCode.OK))
            span.set_attribute("http.status_code", status_code)
            # logger.debug(f"[TRACING_MIDDLEWARE] Thiết lập span status OK với mã {status_code}")


# Custom LoggerAdapter để thêm requestId vào log messages
class RequestIdLoggerAdapter(logging.LoggerAdapter):
    """Logger adapter để gắn request ID vào mỗi log message"""

    def process(self, msg, kwargs):
        request_id = get_request_id()
        if request_id:
            # Format phù hợp với chuỗi log của Odoo
            # Vẫn giữ cấu trúc log gốc nhưng thêm requestId ở đầu message
            return f"{msg} [requestId: {request_id}]", kwargs
        return msg, kwargs


# Hàm để lấy logger với RequestId
def get_logger(name: str) -> logging.LoggerAdapter:
    """Lấy logger với adapter RequestId"""
    logger = logging.getLogger(name)
    return RequestIdLoggerAdapter(logger, {})
