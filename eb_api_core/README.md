# EB API Core

Module cơ sở cho việc phát triển API thống nhất trong EarnBase Odoo LMS.

## Tính năng

- Cung cấp chuẩn response format chung cho tất cả API
- Hệ thống authentication thống nhất với JWT
- Rate limiting
- Xử lý lỗi và exception handling nhất quán
- Hỗ trợ đăng ký và khám phá API modules
- Tài liệu API tích hợp (Swagger UI & ReDoc)
- Distributed tracing với OpenTelemetry

## Cài đặt

Module này phụ thuộc vào các OCA modules:
- fastapi
- fastapi_auth_jwt
- auth_jwt

```bash
# Cài đặt dependencies
pip install fastapi>=0.110.0 pyjwt[crypto] python-multipart uvicorn redis
pip install opentelemetry-api opentelemetry-sdk opentelemetry-exporter-otlp opentelemetry-instrumentation-fastapi

# Cài đặt module
cd extra-addons
git clone https://github.com/username/eb_api_core.git
```

## Cấu trúc

```
eb_api_core/
├── models/
│   ├── api_module.py     # Model quản lý API modules
│   └── api_endpoint.py   # Mở rộng FastAPI endpoint
├── schemas/
│   ├── base.py           # Response format tiêu chuẩn
│   ├── auth.py           # Schema xác thực
│   └── error.py          # Định nghĩa error codes
├── dependencies/
│   ├── auth.py           # JWT auth dependencies
│   ├── rate_limit.py     # Rate limiting
│   └── common.py         # Common dependencies
├── middleware/
│   ├── request_id.py     # RequestID middleware
│   └── otel_middleware.py # OpenTelemetry middleware
├── routers/
│   ├── base_router.py    # Router core
│   └── registry.py       # Đăng ký module API
├── utils/
│   ├── decorators.py     # Decorators cho API endpoints
│   ├── exceptions.py     # Exception handling
│   ├── tracing.py        # OpenTelemetry utilities
│   ├── trace_utils.py    # Tracing decorators & helpers
│   └── logging_setup.py  # Cấu hình logging với tracing
├── doc/
│   ├── OTEL_GUIDE.md     # Hướng dẫn đầy đủ về OpenTelemetry
│   └── OTEL_QUICK_START.md # Hướng dẫn nhanh OpenTelemetry
└── hooks.py              # Post-install & post-load hooks
```

## Sử dụng

### Tạo module API mới

**Cách 1: Sử dụng file api.py với decorator (Khuyến nghị)**

```python
# my_module/api.py
from fastapi import APIRouter
from odoo.addons.eb_api_core.utils.decorators import register_api_module
from odoo.addons.eb_api_core.schemas.base import ResponseBase

@register_api_module(
    module_name='my_module',
    description='My Module API',
    version='1.0'
)
class MyModuleAPI:
    router = APIRouter(tags=["my_module"])
    
    @router.get("/hello", response_model=ResponseBase)
    async def hello():
        return ResponseBase(
            success=True,
            message="Hello from my module!"
        )
```

**Cách 2: Tạo router trong thư mục routers/**

```python
# my_module/routers/module_router.py
from fastapi import APIRouter
from odoo.addons.eb_api_core.schemas.base import ResponseBase

module_router = APIRouter(tags=["my_module"])

@module_router.get("/hello", response_model=ResponseBase)
async def hello():
    return ResponseBase(
        success=True,
        message="Hello from my module!"
    )
```

**Đăng ký module trong database**

Sau khi tạo code, cần đăng ký module trong database:
1. Đi tới menu API Management > API Modules
2. Tạo module mới với:
   - Tên: My Module API
   - Technical Name: my_module
   - Base Path: /api/v1/my-module
   - Mô tả, version và các tùy chọn khác

Hệ thống sẽ tự động tìm và đăng ký router của module.

### Rate limiting

```python
from odoo.addons.eb_api_core.utils.decorators import rate_limited

@router.get("/limited")
@rate_limited(limit=10, window=60)
async def limited_endpoint():
    # Giới hạn 10 requests mỗi phút
    return {"message": "Rate limited endpoint"}
```

### Authentication

```python
from fastapi import Depends
from odoo.addons.eb_api_core.dependencies.auth import get_current_user

@router.get("/me")
async def get_my_info(
    user = Depends(get_current_user)
):
    return {
        "id": user.id,
        "name": user.name,
        "email": user.email
    }
```

### Chuẩn Response Format

Tất cả các endpoints đều trả về định dạng chuẩn:

```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": { ... }
}
```

Lỗi:

```json
{
  "success": false,
  "error": "Error message",
  "error_code": "ERROR_CODE",
  "details": [
    {
      "field": "field_name",
      "code": "error_code",
      "message": "Field error message"
    }
  ]
}
```

### Automatic Error Handling

Sử dụng decorator để tự động xử lý lỗi:

```python
from odoo.addons.eb_api_core.utils.decorators import auto_error_response

@router.get("/with-errors")
@auto_error_response([400, 401, 404, 500])
async def endpoint_with_error_handling():
    # Các lỗi HTTP 400, 401, 404, 500 sẽ tự động được chuyển đổi sang định dạng chuẩn
    return {"result": "success"}
```

## Distributed Tracing với OpenTelemetry

### Giới thiệu

OpenTelemetry giúp theo dõi và gỡ lỗi các ứng dụng phân tán, cung cấp khả năng quan sát (observability) cho hệ thống. Module này tích hợp OpenTelemetry để:

- Tạo và truyền trace context xuyên suốt các services
- Theo dõi thời gian thực thi của từng hoạt động
- Đánh dấu các sự kiện và lỗi trong quá trình xử lý
- Cung cấp context cho logs để dễ dàng theo dõi

### Bật OpenTelemetry

Để kích hoạt OpenTelemetry, cấu hình các tham số trong odoo.conf:

```ini
# OpenTelemetry Environment Variables
OTEL_ENABLED = true
OTEL_SERVICE_NAME = odoo-api
OTEL_EXPORTER_TYPE = console  # Hoặc "otlp" để gửi tới collector

# Tùy chọn: cấu hình OTLP Exporter
# OTEL_EXPORTER_OTLP_ENDPOINT = http://localhost:4317
# OTEL_EXPORTER_OTLP_HEADERS = api-key=your-api-key

# Tùy chọn: các thuộc tính resource
OTEL_RESOURCE_ATTRIBUTES = deployment.environment=development,service.version=1.0
```

### Cách sử dụng OpenTelemetry

#### 1. Tạo span với decorator

```python
from odoo.addons.eb_api_core.utils.trace_utils import trace

@router.get("/items/{item_id}")
@trace()  # Tự động tạo span cho toàn bộ hàm
async def get_item(item_id: int):
    # Xử lý logic
    return {"item_id": item_id}
```

#### 2. Tạo span thủ công

```python
from odoo.addons.eb_api_core.utils.tracing import create_span

@router.get("/complex-operation")
async def complex_operation():
    # Tạo span với context manager
    with create_span("database_query", attributes={"query_type": "select"}) as span:
        # Thực hiện truy vấn database
        results = db_query()
        # Thêm thông tin vào span
        span.set_attribute("result_count", len(results))
    
    return {"results": results}
```

#### 3. Thêm attributes vào span hiện tại

```python
from odoo.addons.eb_api_core.utils.trace_utils import add_span_attributes

@router.post("/users")
async def create_user(user_data: dict):
    # Thêm thông tin vào current span
    add_span_attributes(
        user_email=user_data.get("email"),
        operation="create_user"
    )
    
    # Hoặc sử dụng dictionary
    add_span_attributes({
        "user_email": user_data.get("email"),
        "operation": "create_user"
    })
    
    # Tiếp tục xử lý
    return {"success": True}
```

#### 4. Lấy trace ID hiện tại

```python
from odoo.addons.eb_api_core.utils.tracing import get_current_trace_id

@router.get("/with-trace-id")
async def operation_with_trace_id():
    # Lấy trace ID để sử dụng (ví dụ: thêm vào response)
    trace_id = get_current_trace_id()
    
    # Xử lý logic
    
    # Trả về trace_id trong response để client có thể tham chiếu
    return {
        "data": {"result": "success"},
        "trace_id": trace_id
    }
```

#### 5. Ghi nhận exception

```python
from odoo.addons.eb_api_core.utils.trace_utils import record_exception_in_trace

@router.get("/risky-operation")
async def risky_operation():
    try:
        # Code có thể gây lỗi
        result = perform_complex_operation()
        return {"result": result}
    except Exception as e:
        # Ghi lại exception vào span hiện tại
        record_exception_in_trace(e, error_code="OPERATION_FAILED")
        # Vẫn raise exception để được xử lý ở exception handler
        raise
```

#### 6. Truyền context qua HTTP requests

```python
from odoo.addons.eb_api_core.utils.trace_utils import get_trace_context
import httpx

@router.get("/call-external")
async def call_external_service():
    # Lấy headers với trace context
    trace_headers = get_trace_context()
    
    # Gọi service khác với việc truyền context
    async with httpx.AsyncClient() as client:
        response = await client.get(
            "https://api.example.com/data", 
            headers=trace_headers
        )
    
    return {"external_data": response.json()}
```

### Tài liệu chi tiết về OpenTelemetry

Để biết thêm chi tiết về cách sử dụng OpenTelemetry trong Odoo API, hãy tham khảo:

- [Hướng dẫn đầy đủ về OpenTelemetry](./doc/OTEL_GUIDE.md)
- [Hướng dẫn nhanh OpenTelemetry](./doc/OTEL_QUICK_START.md)

## API Registry

EB API Core có một hệ thống đăng ký tự động cho các modules, giúp cho việc phát hiện và tải các API endpoints dễ dàng. Quá trình đăng ký diễn ra khi:

1. Module API được cài đặt
2. Một bản ghi module API được tạo/cập nhật
3. Gọi thủ công phương thức `register_all_with_registry()`

### Truy cập vào Registry

```python
from odoo.addons.eb_api_core.routers.registry import get_registry

# Lấy instance registry
registry = get_registry()

# Lấy router từ một module
my_router = registry.get_router('my_module')

# Lấy tất cả routers đã đăng ký
all_routers = registry.get_all_routers()
```

### Buộc tải lại Registry

```python
from odoo.addons.eb_api_core.models.api_module import ApiModule

# Đăng ký lại tất cả modules
env['eb_api.module'].register_all_with_registry()
```

## Xử lý CORS và Security

API tự động cấu hình CORS. Mặc định tất cả origins đều được cho phép. Trong môi trường production, hãy điều chỉnh cài đặt CORS trong `api_endpoint.py` để giới hạn truy cập:

```python
middlewares.append(
    Middleware(
        CORSMiddleware,
        allow_origins=["https://myapp.example.com"],  # Thay đổi ở đây
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
)
```

## License

LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html) 