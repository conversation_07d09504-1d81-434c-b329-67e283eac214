# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging
from typing import Any, Dict, List
from contextlib import asynccontextmanager

from odoo import api, fields, models

from fastapi import APIRouter, FastAPI
from starlette.middleware import Middleware
from starlette.middleware.cors import CORSMiddleware

from odoo.addons.eb_api_core.routers.registry import get_registry
from odoo.addons.eb_api_core.routers.base_router import base_router
from odoo.addons.eb_api_core.utils.exceptions import register_exception_handlers
from odoo.addons.eb_api_core.middleware.tracing_middleware import TracingMiddleware
from odoo.addons.eb_api_core.utils.tracing import (
    init_tracer,
    instrument_fastapi,
)
from odoo.addons.eb_api_core.utils.logging_setup import setup_detailed_logging
from odoo.addons.eb_api_core.utils.config_utils import is_otel_enabled, get_otel_config

_logger = logging.getLogger(__name__)


# Định nghĩa lifespan context manager để thay thế on_startup
@asynccontextmanager
async def api_lifespan(app: FastAPI):
    # Thực hiện các tác vụ khi khởi động (tương tự on_startup)
    _logger.info("[API_LIFESPAN] Bắt đầu khởi tạo API resources...")

    try:
        # Thiết lập exception handlers
        register_exception_handlers(app)
        _logger.info("[API_LIFESPAN] Đã đăng ký exception handlers")

        # Thiết lập logging chi tiết
        setup_detailed_logging()
        _logger.info("[API_LIFESPAN] Đã thiết lập detailed logging")

        # Khởi tạo OpenTelemetry nếu được cấu hình
        if is_otel_enabled():
            _logger.info("[API_LIFESPAN] Khởi tạo OpenTelemetry...")

            # Khởi tạo với init_tracer, không cần truyền tham số vì đã lấy từ odoo.conf
            result = init_tracer()

            if result:
                service_name = get_otel_config("service_name", "odoo-api")
                _logger.info(f"[API_LIFESPAN] OpenTelemetry đã được khởi tạo thành công cho service '{service_name}'")
            else:
                _logger.warning("[API_LIFESPAN] Không thể khởi tạo OpenTelemetry tracer provider")
    except Exception as e:
        _logger.error(f"[API_LIFESPAN] Lỗi khi khởi tạo API resources: {str(e)}")
        # Không raise exception để tránh làm crash ứng dụng

    _logger.info("[API_LIFESPAN] Khởi tạo API resources thành công")
    yield

    # Thực hiện các tác vụ khi tắt (tương tự on_shutdown nếu có)
    _logger.info("[API_LIFESPAN] Đang dọn dẹp API resources...")
    # Hiện không có tác vụ nào khi tắt


class FastapiEndpoint(models.Model):
    """Mở rộng model FastAPI endpoint để hỗ trợ EB API Core."""

    _inherit = "fastapi.endpoint"

    # Thêm loại app mới cho EB API Core
    app = fields.Selection(
        selection_add=[("eb_api", "EB API Framework")],
        ondelete={"eb_api": "cascade"},
    )

    # Cấu hình thêm cho API
    enable_rate_limiting = fields.Boolean(
        string="Rate Limiting", default=True, help="Enable API rate limiting"
    )

    enable_documentation = fields.Boolean(
        string="API Documentation", default=True, help="Enable Swagger UI documentation"
    )

    api_title = fields.Char(
        string="API Title", default="EarnBase API", help="Title for API documentation"
    )

    api_description = fields.Text(
        string="API Description",
        default="API Services for EarnBase platform",
        help="Description for API documentation",
    )

    api_version = fields.Char(
        string="API Version", default="1.0", help="API version number"
    )

    api_module_ids = fields.One2many(
        "eb_api.module",
        "endpoint_id",
        string="API Modules",
        help="API modules registered with this endpoint",
    )

    def _get_fastapi_routers(self) -> List[APIRouter]:
        """Lấy danh sách routers cho FastAPI app dựa trên loại app."""

        # Nếu là EB API Framework
        if self.app == "eb_api":
            # Lấy registry instance
            registry = get_registry()

            # Luôn bao gồm base router
            routers = [base_router]

            # Thêm các routers từ registry
            routers.extend(registry.get_all_routers())

            return routers

        # Gọi super method cho các loại app khác
        return super()._get_fastapi_routers()

    def _prepare_fastapi_app_params(self) -> Dict[str, Any]:
        """Chuẩn bị tham số cho FastAPI app."""
        params = super()._prepare_fastapi_app_params()

        if self.app == "eb_api":
            # Đặt các middleware
            middlewares = params.get("middleware", []) or []

            # Thêm TracingMiddleware (kết hợp RequestId và OpenTelemetry)
            middlewares.append(
                Middleware(
                    TracingMiddleware,
                    prefix="req-",
                    header_name="x-request-id",
                )
            )

            # Thêm CORS middleware
            middlewares.append(
                Middleware(
                    CORSMiddleware,
                    allow_origins=["*"],  # Trong production nên giới hạn domains
                    allow_credentials=True,
                    allow_methods=["*"],
                    allow_headers=["*"],
                )
            )

            # Thêm GlobalExceptionMiddleware để đảm bảo tất cả exceptions đều được bắt
            from odoo.addons.eb_api_core.middleware.error_middleware import (
                GlobalExceptionMiddleware,
            )

            middlewares.append(Middleware(GlobalExceptionMiddleware))

            params["middleware"] = middlewares

            # Cấu hình OpenAPI
            params["title"] = self.api_title or "EarnBase API"
            params["description"] = self.api_description
            params["version"] = self.api_version
            params["docs_url"] = "/docs" if self.enable_documentation else None
            params["redoc_url"] = "/redoc" if self.enable_documentation else None

            # Sử dụng lifespan thay vì on_startup/on_shutdown (đã bị deprecated)
            params["lifespan"] = api_lifespan

            # API Tags metadata
            api_modules = self.env["eb_api.module"].search(
                [
                    ("is_active", "=", True),
                    "|",
                    ("endpoint_id", "=", self.id),
                    ("endpoint_id", "=", False),
                ]
            )

            # Tạo tags cho tất cả module API đã đăng ký
            tags_metadata = params.get("openapi_tags", []) or []
            tags_metadata.append(
                {"name": "core", "description": "Core API functionality"}
            )

            for module in api_modules:
                tags_metadata.append(
                    {"name": module.technical_name, "description": module.description}
                )

            params["openapi_tags"] = tags_metadata

        return params

    @api.model_create_multi
    def create(self, vals_list):
        """Khi tạo endpoint, đăng ký tất cả API modules.

        Hỗ trợ tạo nhiều bản ghi cùng lúc (batch creation).

        Args:
            vals_list: Danh sách các dict chứa giá trị cho các bản ghi mới

        Returns:
            Các bản ghi vừa được tạo
        """
        if not isinstance(vals_list, list):
            vals_list = [vals_list]

        records = super(FastapiEndpoint, self).create(vals_list)

        for record, vals in zip(records, vals_list):
            if vals.get("app") == "eb_api":
                self.env["eb_api.module"].register_all_with_registry()

        return records

    def _get_fastapi_app(self) -> FastAPI:
        """Override để thêm instrumentation từ OpenTelemetry."""
        try:
            app = super()._get_fastapi_app()

            # Đảm bảo lifespan được thiết lập đúng cách
            if self.app == "eb_api" and not hasattr(app, "router") or not getattr(app.router, "lifespan", None):
                _logger.warning(
                    f"[FASTAPI_APP] Lifespan không được thiết lập trong FastAPI app '{app.title}', "
                    f"sẽ không thể khởi tạo OpenTelemetry đúng cách"
                )

            # Thêm instrumentation nếu OpenTelemetry được bật
            if self.app == "eb_api" and is_otel_enabled():
                try:
                    instrument_fastapi(app)
                    _logger.info(f"[FASTAPI_APP] FastAPI app '{app.title}' instrumented with OpenTelemetry")
                except Exception as e:
                    _logger.error(f"[FASTAPI_APP] Lỗi khi instrumentation FastAPI: {str(e)}")

            return app
        except Exception as e:
            _logger.error(f"[FASTAPI_APP] Lỗi khi khởi tạo FastAPI app: {str(e)}")
            raise
