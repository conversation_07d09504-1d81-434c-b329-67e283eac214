# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging
from odoo import api, fields, models
from fastapi import APIRouter

_logger = logging.getLogger(__name__)


class ApiModule(models.Model):
    """Mô hình quản lý các module API trong hệ thống."""

    _name = "eb_api.module"
    _description = "API Module"
    _order = "sequence, name"

    name = fields.Char(string="Module Name", required=True)
    technical_name = fields.Char(string="Technical Name", required=True)
    description = fields.Text(string="Description")
    version = fields.Char(string="Version", default="1.0.0")
    sequence = fields.Integer(string="Sequence", default=10)

    # <PERSON>ên kết với FastAPI endpoint
    endpoint_id = fields.Many2one(
        "fastapi.endpoint",
        string="API Endpoint",
        help="The FastAPI endpoint this module is registered to",
    )

    # Cài đặt module
    is_active = fields.Boolean(string="Active", default=True)
    require_auth = fields.Boolean(string="Require Authentication", default=True)

    # Rate limiting
    enable_rate_limiting = fields.Boolean(
        string="Rate Limiting",
        default=True,
        help="Enable API rate limiting for this module",
    )
    rate_limit = fields.Integer(
        string="Rate Limit", default=100, help="Maximum number of requests per minute"
    )

    # Routing info
    base_path = fields.Char(
        string="Base Path",
        help="Base URL path for this API module, e.g. /api/v1/payment",
        required=True,
    )

    # Metadata
    documentation_url = fields.Char(string="Documentation URL")
    module_dependency_ids = fields.Many2many(
        "ir.module.module",
        string="Module Dependencies",
        help="Odoo modules this API depends on",
    )

    _sql_constraints = [
        (
            "technical_name_uniq",
            "unique(technical_name)",
            "Technical name must be unique",
        )
    ]

    @api.model_create_multi
    def create(self, vals_list):
        """Khi tạo module API, đăng ký vào API registry.

        Hỗ trợ tạo nhiều bản ghi cùng lúc (batch creation).

        Args:
            vals_list: Danh sách các dict chứa giá trị cho các bản ghi mới

        Returns:
            Các bản ghi vừa được tạo
        """
        if not isinstance(vals_list, list):
            vals_list = [vals_list]

        records = super(ApiModule, self).create(vals_list)

        for record in records:
            self._register_with_registry(record)

        return records

    def write(self, vals):
        """Khi cập nhật module API, cập nhật trong registry."""
        res = super(ApiModule, self).write(vals)
        for module in self:
            self._register_with_registry(module)
        return res

    def _register_with_registry(self, module):
        """Đăng ký module với API registry.

        Phương thức này thực hiện các công việc:
        1. Tìm module API được triển khai trong code (nếu có)
        2. Tìm router của module
        3. Đăng ký router vào registry trung tâm

        Args:
            module: Bản ghi module API
        """
        try:
            from odoo.addons.eb_api_core.routers.registry import get_registry
            import importlib

            registry = get_registry()

            # Kiểm tra xem đã đăng ký chưa
            if registry.get_router(module.technical_name):
                _logger.debug(
                    f"Module {module.technical_name} already registered with registry"
                )
                return

            # Các cách để tìm router:
            # 1. Tìm theo file api.py trong module
            router = None
            module_found = False

            try:
                # Tìm api.py trong module
                module_path = f"odoo.addons.{module.technical_name}.api"
                api_module = importlib.import_module(module_path)
                module_found = True

                # Tìm router từ các class sử dụng decorator @register_api_module
                for attr_name in dir(api_module):
                    attr = getattr(api_module, attr_name)
                    if hasattr(attr, "router") and isinstance(attr.router, APIRouter):
                        router = attr.router
                        _logger.info(
                            f"Found router in {module.technical_name}.api.{attr_name}"
                        )
                        break
            except ImportError:
                _logger.debug(f"No api.py found in module {module.technical_name}")

            # 2. Nếu không có api.py, tìm trong routers/{module_name}_router.py
            if not router and not module_found:
                try:
                    router_path = f"odoo.addons.{module.technical_name}.routers.{module.technical_name.split('_')[-1]}_router"
                    router_module = importlib.import_module(router_path)
                    # Tìm biến router
                    for attr_name in dir(router_module):
                        if attr_name.endswith("_router") and isinstance(
                            getattr(router_module, attr_name), APIRouter
                        ):
                            router = getattr(router_module, attr_name)
                            _logger.info(f"Found router in {router_path}.{attr_name}")
                            break
                except ImportError:
                    _logger.debug(f"No router module found in {module.technical_name}")
                except Exception as e:
                    _logger.debug(
                        f"Error looking for router in {module.technical_name}: {str(e)}"
                    )

            # Đăng ký router vào registry nếu tìm thấy
            if router:
                registry.register_module(
                    module.technical_name,
                    {
                        "router": router,
                        "description": module.description or "",
                        "version": module.version or "1.0.0",
                        "base_path": module.base_path,
                        "require_auth": module.require_auth,
                    },
                )
                _logger.info(f"Module {module.technical_name} registered with registry")
            else:
                _logger.warning(f"No router found for module {module.technical_name}")

        except ImportError:
            _logger.error("Could not import API registry")
        except Exception as e:
            _logger.error(f"Error registering module {module.technical_name}: {str(e)}")

    @api.model
    def register_all_with_registry(self):
        """Đăng ký tất cả module API với registry."""
        active_modules = self.search([("is_active", "=", True)])
        for module in active_modules:
            self._register_with_registry(module)
        return True
