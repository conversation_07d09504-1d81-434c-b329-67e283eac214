# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging
from odoo.addons.eb_api_core.utils.tracing import init_tracer
from odoo.addons.eb_api_core.utils.config_utils import is_otel_enabled, get_otel_config
from odoo.addons.eb_api_core.utils.warnings_filter import setup_warnings_filters, apply_odoo_warnings_filters

_logger = logging.getLogger(__name__)


def post_init_hook(env):
    """Khởi tạo dữ liệu sau khi cài đặt module

    Args:
        env: Odoo environment
    """

    # Khởi tạo OpenTelemetry nếu được cấu hình
    if is_otel_enabled():
        try:
            _logger.info("Initializing OpenTelemetry during module installation...")

            # Khởi tạo với init_tracer, không cần truyền tham số vì đã lấy từ odoo.conf
            result = init_tracer()

            if result:
                service_name = get_otel_config("service_name", "odoo-api")
                _logger.info(f"OpenTelemetry initialized successfully with service '{service_name}'")
            else:
                _logger.warning("Could not initialize OpenTelemetry tracer provider")
        except Exception as e:
            _logger.error(f"Error initializing OpenTelemetry: {e}")

    # Đảm bảo cấu hình JWT Validator mặc định đã được tạo
    try:
        jwt_validator = env['auth.jwt.validator'].search([
            ('name', '=', 'eb_api_default')
        ], limit=1)

        if not jwt_validator:
            _logger.info(
                "JWT Validator 'eb_api_default' not found - "
                "this is expected if eb_api_auth module has not been installed yet. "
                "JWT authentication will be available after installing the eb_api_auth module."
            )
        else:
            _logger.info("JWT Validator found: %s", jwt_validator.name)
    except Exception as e:
        _logger.info(
            "Could not check for JWT Validator: %s - "
            "this is expected if auth_jwt module has not been installed yet",
            str(e)
        )

    # Đảm bảo API core endpoint đã được tạo và đồng bộ
    try:
        # Đồng bộ tất cả các FastAPI endpoints
        endpoints = env['fastapi.endpoint'].search([])
        for endpoint in endpoints:
            if not endpoint.registry_sync:
                _logger.info(
                    "Syncing FastAPI endpoint: %s",
                    endpoint.name
                )
                try:
                    endpoint.action_sync_registry()
                except Exception as e:
                    _logger.error(
                        "Error syncing endpoint %s: %s",
                        endpoint.name, e
                    )

        _logger.info("All FastAPI endpoints synced successfully")
    except Exception as e:
        _logger.error("Error initializing API endpoints: %s", e)


def post_load_hook():
    """Được gọi khi module được tải"""
    # Apply warning filters first
    try:
        setup_warnings_filters()
        apply_odoo_warnings_filters()
        _logger.info("Warning filters applied successfully")
    except Exception as e:
        _logger.warning(f"Could not apply warning filters: {e}")

    # Đảm bảo API registry được khởi tạo
    from odoo.addons.eb_api_core.routers import registry
    registry.initialize_registry()
    _logger.info("EB API Core Registry initialized successfully")