# Hướng dẫn sử dụng định dạng datetime mới

Module này cung cấp các tiện ích để định dạng datetime trong API response theo chuẩn mới:
- Định dạng mặc định: `yyyy-mm-dd hh:mm` (ví dụ: `2023-01-01 12:30`)
- Sử dụng timezone của người dùng hiện tại thay vì UTC

## Cấu hình

Cấu hình định dạng datetime được định nghĩa trong class `DatetimeConfig` trong file `datetime_utils.py`:

```python
class DatetimeConfig:
    """Cấu hình định dạng datetime cho API response"""

    # Định dạng mặc định: yyyy-mm-dd hh:mm
    DEFAULT_DATETIME_FORMAT = "%Y-%m-%d %H:%M"
    DEFAULT_DATE_FORMAT = "%Y-%m-%d"

    # Sử dụng timezone của người dùng
    # Nếu người dùng không có timezone, sử dụng "Asia/Ho_Chi_Minh" (UTC+7) làm mặc định
    USE_USER_TIMEZONE = True
```

Bạn có thể thay đổi các giá trị này để điều chỉnh định dạng datetime theo nhu cầu.

## Các hàm tiện ích

### `format_datetime(dt_value, user_tz, dt_format)`

Chuyển đổi datetime sang định dạng chuỗi với timezone của người dùng.

```python
from odoo.addons.eb_api_core.utils.datetime_utils import format_datetime

# Sử dụng timezone của người dùng hiện tại
formatted_datetime = format_datetime(datetime.now(), env.user.tz)

# Sử dụng định dạng tùy chỉnh
formatted_datetime = format_datetime(datetime.now(), env.user.tz, "%Y-%m-%d %H:%M:%S")
```

### `format_date(date_value, date_format)`

Chuyển đổi date sang định dạng chuỗi.

```python
from odoo.addons.eb_api_core.utils.datetime_utils import format_date

# Sử dụng định dạng mặc định
formatted_date = format_date(date.today())

# Sử dụng định dạng tùy chỉnh
formatted_date = format_date(date.today(), "%d/%m/%Y")
```

### `format_meta_timestamp(timestamp)`

Định dạng timestamp cho metadata của API response.

```python
from odoo.addons.eb_api_core.utils.datetime_utils import format_meta_timestamp

# Sử dụng thời gian hiện tại
meta_timestamp = format_meta_timestamp()

# Sử dụng timestamp cụ thể
meta_timestamp = format_meta_timestamp(datetime.now())
```

### `get_user_timezone(env)`

Lấy timezone của người dùng hiện tại. Nếu người dùng không có timezone, sử dụng "Asia/Ho_Chi_Minh" (UTC+7) làm mặc định.

```python
from odoo.addons.eb_api_core.utils.datetime_utils import get_user_timezone

# Lấy timezone của người dùng hiện tại hoặc "Asia/Ho_Chi_Minh" nếu không có
user_tz = get_user_timezone(env)
```

## Sử dụng với Pydantic

### Cấu hình model với `ApiModelConfig`

```python
from odoo.addons.eb_api_core.schemas.config import ApiModelConfig

class MyModel(BaseModel):
    id: int
    name: str
    created_at: datetime

    # Sử dụng cấu hình mới cho định dạng datetime
    model_config = ApiModelConfig.get_config()
```

### Cấu hình model với timezone của người dùng

```python
from odoo.addons.eb_api_core.schemas.config import ApiModelConfig

# Tạo model với cấu hình timezone của người dùng hiện tại
model = ApiModelConfig.configure_model(MyModel, env)(**data)
```

## Ví dụ

Xem thêm các ví dụ trong:
- `extra-addons/eb_lms/schemas/datetime_example.py`
- `extra-addons/eb_lms/examples/datetime_format_example.py`
