# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

"""
Warnings Filter Utility

This module provides utilities to filter and suppress common warnings
that are not actionable or are from third-party libraries.
"""

import warnings
import logging

_logger = logging.getLogger(__name__)


def setup_warnings_filters():
    """Setup warning filters to suppress common non-actionable warnings."""
    
    # Suppress SWIG-related deprecation warnings
    # These come from C/C++ libraries wrapped with SWIG and are not actionable
    warnings.filterwarnings(
        'ignore',
        message='builtin type SwigPyPacked has no __module__ attribute',
        category=DeprecationWarning
    )
    
    warnings.filterwarnings(
        'ignore', 
        message='builtin type SwigPyObject has no __module__ attribute',
        category=DeprecationWarning
    )
    
    warnings.filterwarnings(
        'ignore',
        message='builtin type swigvarlink has no __module__ attribute', 
        category=DeprecationWarning
    )
    
    # Suppress other common third-party warnings
    warnings.filterwarnings(
        'ignore',
        message='.*pkg_resources.*',
        category=DeprecationWarning
    )
    
    # Log that filters have been applied
    _logger.info("Warning filters applied successfully")


def apply_odoo_warnings_filters():
    """Apply Odoo-specific warning filters."""
    
    # Filter out Odoo-specific warnings that are not actionable
    warnings.filterwarnings(
        'ignore',
        message='.*is not overriding the create method in batch.*',
        category=DeprecationWarning
    )
    
    _logger.info("Odoo warning filters applied successfully")


# Auto-apply filters when module is imported
setup_warnings_filters()
