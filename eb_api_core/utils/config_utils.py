# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

"""
Tiện ích giúp đọc cấu hình từ odoo.conf.

Module này cung cấp các hàm để đọc cấu hình từ odoo.conf.
"""

import logging
from typing import Any, Optional
import os

import odoo
from odoo.tools import config as odoo_config

_logger = logging.getLogger(__name__)

def get_otel_config(key: str, default: Any = None) -> Any:
    """Lấy cấu hình OpenTelemetry từ odoo.conf.
    
    Các cấu hình OpenTelemetry trong odoo.conf có thể có tiền tố 'otel_' hoặc 'OTEL_'
    Ví dụ: otel_service_name, OTEL_SERVICE_NAME, otel_exporter_otlp_endpoint, OTEL_EXPORTER_OTLP_ENDPOINT
    
    Args:
        key: <PERSON><PERSON><PERSON> c<PERSON><PERSON> hình, không cần tiền tố 'otel_' hoặc 'OTEL_'
        default: Giá trị mặc định nếu không tìm thấy
        
    Returns:
        Giá trị cấu hình hoặc giá trị mặc định
    """
    # Tạo các khóa cấu hình với cả tiền tố viết hoa và viết thường
    lowercase_key = f"otel_{key.lower()}"
    uppercase_key = f"OTEL_{key.upper()}"
    
    # Thử tìm với tiền tố viết thường trước
    value = odoo_config.get(lowercase_key)
    
    # Nếu không tìm thấy, thử với tiền tố viết hoa
    if value is None:
        value = odoo_config.get(uppercase_key)
        if value is not None:
            _logger.debug(f"[CONFIG] Đọc cấu hình {uppercase_key}={value} từ odoo.conf")
            return value
    else:
        _logger.debug(f"[CONFIG] Đọc cấu hình {lowercase_key}={value} từ odoo.conf")
        return value
    
    # Nếu không tìm thấy cả hai, trả về giá trị mặc định
    _logger.debug(f"[CONFIG] Cấu hình {lowercase_key}/{uppercase_key} không được định nghĩa trong odoo.conf, sử dụng giá trị mặc định: {default}")
    return default


def is_otel_enabled() -> bool:
    """Kiểm tra xem OpenTelemetry có được bật hay không
    
    Returns:
        True nếu OpenTelemetry được bật, ngược lại là False
    """
    value = get_otel_config("enabled", "false")
    
    # Nếu đã là boolean
    if isinstance(value, bool):
        is_enabled = value
    # Nếu là string
    elif isinstance(value, str):
        is_enabled = value.lower() in ("true", "1", "yes")
    else:
        # Trường hợp khác, chuyển đổi thành boolean
        is_enabled = bool(value)
    
    _logger.debug(f"[CONFIG] OpenTelemetry {'được bật' if is_enabled else 'bị tắt'}")
    return is_enabled
