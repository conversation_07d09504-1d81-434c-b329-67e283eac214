# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging
from typing import Optional, Dict, Any
import os
from datetime import datetime

from opentelemetry import trace
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.sampling import ALWAYS_ON
from opentelemetry.sdk.resources import Resource
from opentelemetry.sdk.trace.export import (
    SimpleSpanProcessor,
    BatchSpanProcessor,
    ConsoleSpanExporter
)
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.trace.propagation.tracecontext import TraceContextTextMapPropagator
from opentelemetry.baggage.propagation import W3CBaggagePropagator
from opentelemetry.propagate import set_global_textmap, composite
from opentelemetry import propagate

from odoo.addons.eb_api_core.middleware.tracing_middleware import get_request_id
from odoo.addons.eb_api_core.utils.config_utils import get_otel_config, is_otel_enabled

_logger = logging.getLogger(__name__)

# Global tracer provider
tracer_provider = None

def init_tracer() -> bool:
    """
    Khởi tạo OpenTelemetry TracerProvider dựa trên cấu hình từ odoo.conf.

    Các cấu hình cần thiết trong odoo.conf:
        - otel_enabled: true/false
        - otel_service_name: tên service
        - otel_exporter_otlp_endpoint: endpoint của OpenTelemetry Collector
        - otel_exporter_otlp_headers: headers cho OTLP exporter (optional)
        - otel_resource_attributes: resource attributes (optional)

    Returns:
        bool: True nếu khởi tạo thành công, False nếu không
    """
    global tracer_provider

    # Kiểm tra xem OpenTelemetry có được bật không
    if not is_otel_enabled():
        _logger.warning("[OTEL_INIT] OpenTelemetry không được bật, bỏ qua khởi tạo")
        return False

    # Đã khởi tạo, không cần khởi tạo lại
    if tracer_provider:
        _logger.debug("[OTEL_INIT] TracerProvider đã khởi tạo trước đó")
        return True

    try:
        # Lấy các cấu hình từ odoo.conf
        service_name = get_otel_config("service_name", "odoo")
        exporter_endpoint = get_otel_config("exporter_otlp_endpoint", "")

        # Xử lý headers từ cấu hình
        exporter_headers = {}
        headers_str = get_otel_config("exporter_otlp_headers", "")
        if headers_str:
            try:
                # Format: key1=value1,key2=value2
                header_items = headers_str.split(',')
                for item in header_items:
                    if '=' in item:
                        key, value = item.split('=', 1)
                        exporter_headers[key.strip()] = value.strip()
            except Exception as e:
                _logger.error(f"[OTEL_INIT] Lỗi xử lý otel_exporter_otlp_headers: {e}")

        # Xử lý resource attributes từ cấu hình
        resource_attributes = {}
        attrs_str = get_otel_config("resource_attributes", "")
        if attrs_str:
            try:
                # Format: key1=value1,key2=value2
                attr_items = attrs_str.split(',')
                for item in attr_items:
                    if '=' in item:
                        key, value = item.split('=', 1)
                        resource_attributes[key.strip()] = value.strip()
            except Exception as e:
                _logger.error(f"[OTEL_INIT] Lỗi xử lý otel_resource_attributes: {e}")

        # Luôn thêm service.name và các thông tin bổ sung
        resource_attributes["service.name"] = service_name
        resource_attributes["service.instance.id"] = os.getpid()
        resource_attributes["service.version"] = get_otel_config("service_version", "1.0.0")
        resource_attributes["deployment.environment"] = get_otel_config("environment", "production")
        resource_attributes["host.name"] = os.uname().nodename

        # Thêm thông tin thời gian khởi tạo
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        resource_attributes["service.start.time"] = current_time

        # Tạo resource với các attribute
        resource = Resource.create(resource_attributes)

        # Khởi tạo TracerProvider với AlwaysOn sampler
        tracer_provider = TracerProvider(
            resource=resource,
            sampler=ALWAYS_ON,
        )

        # Thiết lập global TracerProvider
        trace.set_tracer_provider(tracer_provider)
        _logger.debug(f"[OTEL_INIT] Global TracerProvider đã được thiết lập: {trace.get_tracer_provider()}")

        # Thiết lập propagator mặc định
        composite_propagator = composite.CompositePropagator(
            [
                TraceContextTextMapPropagator(),
                W3CBaggagePropagator()
            ]
        )
        propagate.set_global_textmap(composite_propagator)
        _logger.info("[OTEL_INIT] Đã thiết lập CompositePropagator làm global propagator")

        # Khởi tạo Span Processors
        # Xác định loại exporter
        exporter_type = get_otel_config("exporter_type", "otlp").lower()

        if exporter_type == "otlp" and exporter_endpoint:
            # Khởi tạo OTLP Exporter
            try:
                _logger.info(f"[OTEL_INIT] Đang tạo OTLP exporter với endpoint {exporter_endpoint} và headers {exporter_headers}")
                otlp_exporter = OTLPSpanExporter(
                    endpoint=exporter_endpoint,
                    headers=exporter_headers,
                )
                # Thêm BatchSpanProcessor
                tracer_provider.add_span_processor(
                    BatchSpanProcessor(otlp_exporter)
                )
                _logger.info(f"[OTEL_INIT] Đã thiết lập OTLP exporter với endpoint {exporter_endpoint}")
            except Exception as e:
                _logger.error(f"[OTEL_INIT] Lỗi khi tạo OTLP exporter: {e}", exc_info=True)
                # Fallback to console exporter
                _logger.warning("[OTEL_INIT] Fallback sang Console exporter sau khi OTLP exporter lỗi")
                console_exporter = ConsoleSpanExporter()
                tracer_provider.add_span_processor(
                    SimpleSpanProcessor(console_exporter)
                )
        elif exporter_type == "console" or not exporter_endpoint:
            # Thêm ConsoleSpanExporter
            console_exporter = ConsoleSpanExporter()
            tracer_provider.add_span_processor(
                SimpleSpanProcessor(console_exporter)
            )
        else:
            _logger.warning(f"[OTEL_INIT] Không hỗ trợ exporter type: {exporter_type}, sử dụng Console exporter")
            console_exporter = ConsoleSpanExporter()
            tracer_provider.add_span_processor(
                SimpleSpanProcessor(console_exporter)
            )

        # Log kết quả khởi tạo
        _logger.info(
            f"[OTEL_INIT] Khởi tạo OpenTelemetry thành công với service {service_name}"
        )
        return True
    except Exception as e:
        _logger.error(f"[OTEL_INIT] Lỗi khởi tạo OpenTelemetry: {e}", exc_info=True)
        return False


def instrument_fastapi(app, excluded_urls=None):
    """Tự động instrumentation cho FastAPI

    Args:
        app: FastAPI application
        excluded_urls: Danh sách các URL được loại trừ khỏi tracing
    """
    if not is_otel_enabled():
        _logger.warning("[OTEL_INSTRUMENT] OpenTelemetry không được bật, không thể instrument FastAPI")
        return

    if not tracer_provider:
        success = init_tracer()
        if not success:
            _logger.warning("[OTEL_INSTRUMENT] Không thể khởi tạo OpenTelemetry, bỏ qua instrument FastAPI")
            return

    service_name = get_otel_config("service_name", "odoo-api")
    _logger.info(
        f"[OTEL_INSTRUMENT] Đang thiết lập instrumentation cho FastAPI với service name: {service_name}"
    )

    # Lấy danh sách URL loại trừ từ cấu hình nếu không được cung cấp
    if excluded_urls is None:
        excluded_urls = get_otel_config("fastapi_excluded_urls", "*/favicon.ico,*/docs,*/openapi.json")

    try:
        FastAPIInstrumentor.instrument_app(
            app,
            tracer_provider=tracer_provider,
            excluded_urls=excluded_urls,
        )
        _logger.info("[OTEL_INSTRUMENT] FastAPI instrumentation hoàn tất")
    except Exception as e:
        _logger.error(f"[OTEL_INSTRUMENT] Lỗi khi thiết lập FastAPI instrumentation: {str(e)}")


def create_span(name: str, attributes: Optional[Dict[str, Any]] = None):
    """Tạo span cho một thao tác xử lý

    Args:
        name: Tên của span
        attributes: Các thuộc tính bổ sung

    Returns:
        Span object hoặc nullcontext nếu OpenTelemetry không được bật
    """
    _logger = logging.getLogger(__name__)

    if not is_otel_enabled():
        _logger.debug(f"[OTEL_SPAN] OpenTelemetry không được bật, bỏ qua tạo span: {name}")
        # Return dummy context manager
        from contextlib import nullcontext
        return nullcontext()

    # Khởi tạo tracer nếu chưa được khởi tạo
    if not tracer_provider:
        success = init_tracer()
        if not success:
            _logger.warning(f"[OTEL_SPAN] Không thể khởi tạo OpenTelemetry, bỏ qua tạo span: {name}")
            from contextlib import nullcontext
            return nullcontext()

    # Lấy tracer từ service name
    service_name = get_otel_config("service_name", "odoo-api")
    tracer = trace.get_tracer(service_name)

    # Tạo span với các thuộc tính bổ sung
    attributes = attributes or {}

    # Tự động thêm request_id vào attributes nếu có
    request_id = get_request_id()
    if request_id:
        attributes["request_id"] = request_id

    # Kiểm tra span hiện tại
    current_span = trace.get_current_span()
    valid_parent_context = False

    if current_span:
        # Chỉ kiểm tra nếu có get_span_context method
        if hasattr(current_span, "get_span_context"):
            current_ctx = current_span.get_span_context()
            if current_ctx and current_ctx.is_valid:
                if current_ctx.trace_id == 0:
                    _logger.warning(f"[OTEL_SPAN] Phát hiện current span có trace_id = 0 khi tạo span '{name}'")
                    # Span hợp lệ nhưng trace_id là 0, thêm log
                    attributes["detected_invalid_trace_id"] = True
                else:
                    trace_id_hex = format(current_ctx.trace_id, "032x")
                    span_id_hex = format(current_ctx.span_id, "016x")
                    _logger.debug(f"[OTEL_SPAN] Tạo span '{name}' với parent context: trace_id={trace_id_hex}, span_id={span_id_hex}")
                    valid_parent_context = True
        else:
            _logger.debug(f"[OTEL_SPAN] Current span không có method get_span_context, không thể kiểm tra context")

    # Tạo span và đặt làm span hiện tại
    _logger.debug(f"[OTEL_SPAN] Tạo span: {name} với attributes: {attributes}")
    span = tracer.start_as_current_span(name, attributes=attributes)

    # Kiểm tra xem span mới có trace_id và span_id hợp lệ không
    try:
        # Chỉ kiểm tra nếu có get_span_context method
        if hasattr(span, "get_span_context"):
            new_ctx = span.get_span_context()
            if new_ctx and new_ctx.is_valid:
                trace_id_hex = format(new_ctx.trace_id, "032x")
                span_id_hex = format(new_ctx.span_id, "016x")

                if new_ctx.trace_id == 0 or trace_id_hex == "0" * 32:
                    _logger.warning(f"[OTEL_SPAN] Span mới '{name}' có trace_id không hợp lệ: {trace_id_hex}")
                    # Tạo trace_id mới nếu không hợp lệ
                    import uuid
                    new_trace_id = int(uuid.uuid4().hex[:16], 16)
                    trace_id_hex = format(new_trace_id, "032x")
                    _logger.info(f"[OTEL_SPAN] Đã tạo trace_id mới cho span '{name}': {trace_id_hex}")
                else:
                    _logger.debug(f"[OTEL_SPAN] Span mới '{name}' đã được tạo: trace_id={trace_id_hex}, span_id={span_id_hex}")
            else:
                _logger.warning(f"[OTEL_SPAN] Span mới '{name}' không có context hợp lệ")
        else:
            # Nếu là nullcontext hoặc không có method get_span_context
            _logger.debug(f"[OTEL_SPAN] Span đối tượng không có method get_span_context, có thể là nullcontext")
    except Exception as e:
        _logger.error(f"[OTEL_SPAN] Lỗi khi kiểm tra span mới: {str(e)}", exc_info=True)

    return span


def get_current_trace_id() -> Optional[str]:
    """Lấy trace ID của span hiện tại

    Returns:
        Trace ID dạng hex string hoặc None
    """
    if not is_otel_enabled():
        _logger.debug("[OTEL_TRACE] OpenTelemetry không được bật, không thể lấy trace_id")
        return None

    try:
        span = trace.get_current_span()
        _logger.debug(f"[OTEL_TRACE] Current span: {span}, type: {type(span)}")

        if not span:
            _logger.debug("[OTEL_TRACE] Không tìm thấy current span")
            return None

        span_context = span.get_span_context()
        _logger.debug(f"[OTEL_TRACE] Span context: {span_context}, is_valid: {span_context and span_context.is_valid}")

        if not span_context:
            _logger.debug("[OTEL_TRACE] Không thể lấy span context")
            return None

        if not span_context.is_valid:
            _logger.debug("[OTEL_TRACE] Span context không hợp lệ")
            return None

        # Kiểm tra và xử lý trace_id không hợp lệ
        if span_context.trace_id == 0 or format(span_context.trace_id, "032x") == "0" * 32:
            _logger.warning("[OTEL_TRACE] Phát hiện trace_id không hợp lệ, tạo trace_id mới")
            # Tạo trace_id mới
            import uuid
            new_trace_id = int(uuid.uuid4().hex[:16], 16)
            trace_id = format(new_trace_id, "032x")
            _logger.info(f"[OTEL_TRACE] Tạo trace_id mới: {trace_id}")
            return trace_id

        # Trường hợp trace_id hợp lệ
        trace_id = format(span_context.trace_id, "032x")

        _logger.debug(f"[OTEL_TRACE] Lấy current trace_id: {trace_id}")
        return trace_id
    except Exception as e:
        _logger.error(f"[OTEL_TRACE] Lỗi khi lấy trace_id: {str(e)}", exc_info=True)
        return None
