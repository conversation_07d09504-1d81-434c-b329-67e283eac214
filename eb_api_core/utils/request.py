# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import uuid
from datetime import datetime
from typing import Dict, Any

from odoo.addons.eb_api_core.middleware.tracing_middleware import get_request_id


def get_meta_data() -> Dict[str, Any]:
    """
    Tạo metadata chuẩn cho API response

    Returns:
        Dict với timestamp, requestId và traceId (nếu có)
    """
    # Lấy requestId từ context
    request_id = get_request_id()

    # Nếu không có request_id, tạo mới với định dạng giống middleware
    if not request_id:
        request_id = f"req-{uuid.uuid4().hex[:8]}"

    # Đảm bảo request_id có định dạng "req-xxxxxxxx"
    if not request_id.startswith("req-"):
        request_id = f"req-{uuid.uuid4().hex[:8]}"

    # Tạo metadata cơ bản với định dạng timestamp mới
    from odoo.addons.eb_api_core.utils.datetime_utils import format_meta_timestamp
    from odoo.http import request as odoo_request

    # Lấy timezone của người dùng hoặc sử dụng Asia/Ho_Chi_Minh nếu không có
    user_tz = odoo_request.env.user.tz if hasattr(odoo_request, 'env') else "Asia/Ho_Chi_Minh"
    user_tz = user_tz or "Asia/Ho_Chi_Minh"  # Đảm bảo có giá trị mặc định

    meta = {"timestamp": format_meta_timestamp(datetime.now(), user_tz), "requestId": request_id}

    # Thêm trace ID nếu có thể
    try:
        from opentelemetry import trace
        current_span = trace.get_current_span()
        if hasattr(current_span, "get_span_context"):
            span_ctx = current_span.get_span_context()
            if span_ctx and span_ctx.is_valid:
                trace_id_hex = format(span_ctx.trace_id, "032x")
                meta["traceId"] = trace_id_hex

                # Thêm trace ID theo định dạng Jaeger
                try:
                    from odoo.addons.eb_api_core.middleware.tracing_middleware import get_jaeger_trace_id
                    jaeger_trace_id = get_jaeger_trace_id(trace_id_hex)
                    meta["jaegerTraceId"] = jaeger_trace_id
                except ImportError:
                    # Bỏ qua nếu không thể import hàm
                    pass
    except Exception:
        # Bỏ qua nếu không thể lấy trace ID
        pass

    return meta
