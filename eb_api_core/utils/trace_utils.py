# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

"""
Tiện ích hỗ trợ sử dụng OpenTelemetry trong code.

Module này cung cấp các decorator và tiện ích giúp đơn giản hóa việc tạo spans
và quản lý tracing trong ứng dụng.
"""

import functools
import inspect
import logging
from typing import Any, Callable, Dict, Optional, TypeVar, cast

from opentelemetry import trace as otel_trace

from odoo.addons.eb_api_core.utils.tracing import create_span, get_current_trace_id
from odoo.addons.eb_api_core.middleware.tracing_middleware import get_request_id
from odoo.addons.eb_api_core.utils.config_utils import is_otel_enabled

_logger = logging.getLogger(__name__)

F = TypeVar("F", bound=Callable[..., Any])

def trace_method(name: Optional[str] = None, **attributes: Any) -> Callable[[F], F]:
    """
    Decorator để tự động tạo span cho một method.

    Hỗ trợ cả hàm thông thường và coroutine.

    Args:
        name: Tên của span. Nếu None, sẽ sử dụng tên của method.
        **attributes: Các attribute bổ sung cho span.

    Returns:
        Decorator function
    """

    def decorator(func: F) -> F:
        # Xác định tên span
        span_name = name or func.__qualname__

        if inspect.iscoroutinefunction(func):

            @functools.wraps(func)
            async def async_wrapper(*args: Any, **kwargs: Any) -> Any:
                if not is_otel_enabled():
                    return await func(*args, **kwargs)

                # Tạo span với các attribute đã cung cấp
                with create_span(span_name, attributes=attributes) as span:
                    # Nếu là method của một class, thêm class name vào attributes
                    if args and hasattr(args[0], "__class__"):
                        span.set_attribute("class.name", args[0].__class__.__name__)

                    # Thực thi hàm gốc
                    try:
                        result = await func(*args, **kwargs)
                        return result
                    except Exception as e:
                        # Ghi lại thông tin lỗi vào span
                        span.record_exception(e)
                        span.set_status(otel_trace.StatusCode.ERROR, str(e))
                        raise

            return cast(F, async_wrapper)
        else:

            @functools.wraps(func)
            def wrapper(*args: Any, **kwargs: Any) -> Any:
                if not is_otel_enabled():
                    return func(*args, **kwargs)

                # Tạo span với các attribute đã cung cấp
                with create_span(span_name, attributes=attributes) as span:
                    # Nếu là method của một class, thêm class name vào attributes
                    if args and hasattr(args[0], "__class__"):
                        span.set_attribute("class.name", args[0].__class__.__name__)

                    # Thực thi hàm gốc
                    try:
                        result = func(*args, **kwargs)
                        return result
                    except Exception as e:
                        # Ghi lại thông tin lỗi vào span
                        span.record_exception(e)
                        span.set_status(otel_trace.StatusCode.ERROR, str(e))
                        raise

            return cast(F, wrapper)

    return decorator


def trace(**attributes: Any) -> Callable[[F], F]:
    """
    Decorator đơn giản hơn để tự động tạo span cho một method.
    Tự động sử dụng tên function làm tên span.

    Hỗ trợ cả hàm thông thường và coroutine.

    Args:
        **attributes: Các attribute bổ sung cho span.

    Returns:
        Decorated function
    """
    # Gọi trace_method với name=None để tự động sử dụng tên function
    return trace_method(name=None, **attributes)


def add_span_attributes(attributes=None, **kwargs):
    """
    Thêm các attribute vào current span.

    Args:
        attributes: Dictionary các attribute cần thêm
        **kwargs: Các attribute được truyền dưới dạng keyword arguments
    """
    # Khởi tạo logger
    _logger = logging.getLogger(__name__)

    if not is_otel_enabled():
        _logger.debug("[OTEL_ATTRIBUTES] OpenTelemetry không được bật, bỏ qua thêm attributes")
        return

    try:
        # Lấy current span
        current_span = otel_trace.get_current_span()

        # Kiểm tra xem span có tồn tại và có hỗ trợ ghi attributes không
        if not current_span:
            _logger.warning("[OTEL_ATTRIBUTES] Không tìm thấy active span, bỏ qua thêm attributes")
            return

        # Kiểm tra xem span có phải là nullcontext hoặc có is_recording method không
        if not hasattr(current_span, "is_recording"):
            _logger.warning("[OTEL_ATTRIBUTES] Span không hỗ trợ is_recording, có thể là nullcontext, bỏ qua thêm attributes")
            return

        # Kiểm tra xem span có đang ghi không
        if not current_span.is_recording():
            _logger.warning("[OTEL_ATTRIBUTES] Span không trong trạng thái ghi, bỏ qua thêm attributes")
            return

        # Kiểm tra xem span có hỗ trợ set_attribute không
        if not hasattr(current_span, "set_attribute"):
            _logger.warning("[OTEL_ATTRIBUTES] Span không hỗ trợ set_attribute, bỏ qua thêm attributes")
            return

        # Kết hợp attributes từ dictionary và kwargs
        all_attributes = {}

        # Thêm attributes từ dictionary nếu có
        if attributes and isinstance(attributes, dict):
            all_attributes.update({k: v for k, v in attributes.items() if v is not None})

        # Thêm attributes từ kwargs, bỏ qua các giá trị None
        if kwargs:
            all_attributes.update({k: v for k, v in kwargs.items() if v is not None})

        if not all_attributes:
            _logger.debug("[OTEL_ATTRIBUTES] Không có attributes hợp lệ để thêm vào span")
            return

        # Thêm attributes vào span
        for key, value in all_attributes.items():
            current_span.set_attribute(key, value)

        # Log chi tiết các attribute đã thêm
        _logger.debug(f"[OTEL_ATTRIBUTES] Đã thêm attributes vào span: {all_attributes}")
    except Exception as e:
        _logger.error(f"[OTEL_ATTRIBUTES] Lỗi khi thêm attributes vào span: {str(e)}", exc_info=True)


def get_trace_context() -> Dict[str, str]:
    """
    Lấy context để truyền qua HTTP header.

    Returns:
        Dictionary chứa các header liên quan đến trace
    """
    headers = {}
    _logger = logging.getLogger(__name__)

    request_id = get_request_id()
    if request_id:
        headers["X-Request-ID"] = request_id
        _logger.debug(f"[OTEL_TRACE_CTX] Thêm X-Request-ID={request_id} vào headers")

    if is_otel_enabled():
        try:
            # Lấy current span và context
            current_span = otel_trace.get_current_span()
            span_context = current_span.get_span_context() if current_span else None

            if not span_context or not span_context.is_valid:
                _logger.debug("[OTEL_TRACE_CTX] Span context không hợp lệ hoặc không tồn tại")
                return headers

            # Kiểm tra xem trace_id và span_id có hợp lệ không (không phải 0)
            if span_context.trace_id == 0:
                _logger.warning("[OTEL_TRACE_CTX] Phát hiện trace_id là 0, không thêm vào headers")
                return headers

            if span_context.span_id == 0:
                _logger.warning("[OTEL_TRACE_CTX] Phát hiện span_id là 0, không thêm vào headers")
                return headers

            trace_id = format(span_context.trace_id, "032x")
            span_id = format(span_context.span_id, "016x")

            # Kiểm tra xem trace_id có phải chuỗi toàn số 0 không
            if trace_id == "0" * 32:
                _logger.warning("[OTEL_TRACE_CTX] Phát hiện trace_id là chuỗi toàn số 0, không thêm vào headers")
                return headers

            # Kiểm tra xem span_id có phải chuỗi toàn số 0 không
            if span_id == "0" * 16:
                _logger.warning("[OTEL_TRACE_CTX] Phát hiện span_id là chuỗi toàn số 0, không thêm vào headers")
                return headers

            # Tạo traceparent header đúng chuẩn W3C
            headers["traceparent"] = f"00-{trace_id}-{span_id}-01"
            # Thêm trace-id và span-id riêng để dễ truy cập
            headers["trace-id"] = trace_id
            headers["span-id"] = span_id

            # Đồng thời thêm cả headers với tiền tố X-
            headers["X-Trace-ID"] = trace_id
            headers["X-Span-ID"] = span_id

            _logger.debug(f"[OTEL_TRACE_CTX] Tạo trace context headers: trace-id={trace_id}, span-id={span_id}")
        except Exception as e:
            _logger.error(f"[OTEL_TRACE_CTX] Lỗi khi tạo trace context: {str(e)}", exc_info=True)
            # Trả về headers hiện tại mà không thêm trace context

    return headers


def record_exception(exception: Exception) -> None:
    """
    Ghi lại exception vào span hiện tại.

    Args:
        exception: Exception cần ghi lại
    """
    if not is_otel_enabled():
        return

    current_span = otel_trace.get_current_span()
    current_span.record_exception(exception)
    current_span.set_status(otel_trace.StatusCode.ERROR, str(exception))


def record_exception_in_trace(exception: Exception, error_code: str = None):
    """Ghi nhận exception vào trace hiện tại.

    Args:
        exception: Exception object
        error_code: Mã lỗi tùy chọn để gắn vào trace
    """
    _logger = logging.getLogger(__name__)

    if not is_otel_enabled():
        _logger.debug("[OTEL_EXCEPTION] OpenTelemetry không được bật, bỏ qua ghi nhận exception")
        return

    try:
        current_span = otel_trace.get_current_span()

        # Kiểm tra xem span có tồn tại không
        if not current_span:
            _logger.warning("[OTEL_EXCEPTION] Không tìm thấy active span, bỏ qua ghi nhận exception")
            return

        # Kiểm tra xem span có phải là nullcontext không
        if not hasattr(current_span, "is_recording"):
            _logger.warning("[OTEL_EXCEPTION] Span không hỗ trợ is_recording, có thể là nullcontext, bỏ qua ghi nhận exception")
            return

        # Kiểm tra xem span có đang ghi không
        if not current_span.is_recording():
            _logger.warning("[OTEL_EXCEPTION] Span không trong trạng thái ghi, bỏ qua ghi nhận exception")
            return

        _logger.debug(f"[OTEL_EXCEPTION] Ghi nhận exception vào trace: {str(exception)}")

        # Kiểm tra xem span có hỗ trợ record_exception không
        if not hasattr(current_span, "record_exception"):
            _logger.warning("[OTEL_EXCEPTION] Span không hỗ trợ record_exception, bỏ qua ghi nhận exception")
            return

        # Ghi lại exception
        current_span.record_exception(exception)

        # Thêm attribute cho error code nếu có
        if error_code:
            current_span.set_attribute("error.code", error_code)

        # Kiểm tra xem span có hỗ trợ set_status không
        if hasattr(current_span, "set_status"):
            # Đặt trạng thái span là ERROR
            current_span.set_status(otel_trace.StatusCode.ERROR, str(exception))
        else:
            _logger.warning("[OTEL_EXCEPTION] Span không hỗ trợ set_status, không thể đặt trạng thái ERROR")
    except Exception as e:
        _logger.error(f"[OTEL_EXCEPTION] Lỗi khi ghi nhận exception vào trace: {str(e)}", exc_info=True)
