# Hướng dẫn sử dụng OpenTelemetry trong EB API Core

## 1. Tổng quan

OpenTelemetry đã được tích hợp vào EB API Core để cung cấp khả năng theo dõi và tracing phân tán. Điều này cho phép:

- <PERSON> dõ<PERSON> c<PERSON> request từ đầu đến cuối xuyên suốt các microservices
- <PERSON>hi lại thời gian thực thi của các thao tác
- Liên kết logs với traces để dễ dàng debug
- Xác định bottlenecks trong hệ thống

## 2. Cài đặt các phụ thuộc

Đảm bảo đã cài đặt các dependency cần thiết:

```bash
pip install opentelemetry-api
pip install opentelemetry-sdk
pip install opentelemetry-exporter-otlp
pip install opentelemetry-instrumentation-fastapi
```

## 3. Khởi động với OpenTelemetry

### Sử dụng script khởi động có sẵn

Chúng ta đã tạo script `start_with_tracing.sh` để đơn giản hóa việc khởi động Odoo với OpenTelemetry:

```bash
chmod +x /path/to/start_with_tracing.sh
./start_with_tracing.sh
```

### Hoặc cài đặt thủ công

Để bật OpenTelemetry, cần thiết lập các biến môi trường:

```bash
export OTEL_ENABLED=true
export OTEL_SERVICE_NAME="odoo-api"
export OTEL_EXPORTER_OTLP_ENDPOINT="http://localhost:4317"
export OTEL_RESOURCE_ATTRIBUTES="deployment.environment=development,service.version=1.0"
```

Sau đó khởi động Odoo như bình thường.

## 4. Cài đặt Jaeger để trực quan hóa tracing

OpenTelemetry cần một backend để lưu trữ và hiển thị tracing. Jaeger là một lựa chọn phổ biến:

```bash
docker run -d --name jaeger \
  -e COLLECTOR_OTLP_ENABLED=true \
  -p 16686:16686 \
  -p 4317:4317 \
  -p 4318:4318 \
  jaegertracing/all-in-one:latest
```

Sau khi cài đặt, truy cập UI của Jaeger tại: http://localhost:16686

## 5. Sử dụng trong mã nguồn

### 5.1 Sử dụng decorator `trace_method`

```python
from odoo.addons.eb_api_core.utils import trace_method

@trace_method(name="custom_operation_name")
def my_function(self, arg1, arg2):
    # Hàm sẽ được tự động theo dõi với OpenTelemetry
    # Thời gian thực thi và lỗi sẽ được ghi lại
    return result
```

Decorator này cũng hoạt động với async functions:

```python
@trace_method(name="async_operation")
async def my_async_function(self, arg1, arg2):
    # Logic của hàm
    result = await some_async_operation()
    return result
```

### 5.2 Sử dụng decorator `trace`

`trace` là một phiên bản đơn giản hơn của `trace_method` mà tự động sử dụng tên của function làm tên span. Điều này giúp giảm thiểu code cần viết.

```python
from odoo.addons.eb_api_core.utils import trace

@trace()  # Không cần chỉ định name, sẽ tự động dùng tên hàm
def my_function(self, arg1, arg2):
    # Trace sẽ có tên là "my_function"
    return result
```

Bạn vẫn có thể thêm các thuộc tính vào span:

```python
@trace(operation_type="read", module="inventory")
async def get_inventory_items(self, item_id):
    # Logic của hàm
    return await fetch_items(item_id)
```

### 5.3 Tạo span thủ công

```python
from odoo.addons.eb_api_core.utils import create_span

with create_span("operation_name", attributes={"custom_attribute": "value"}) as span:
    # Thực thi logic trong span này
    span.set_attribute("another_attribute", "another_value")
    
    # Thực hiện các thao tác
    result = do_something()
```

### 5.4 Thêm thuộc tính vào span hiện tại

```python
from odoo.addons.eb_api_core.utils import add_span_attributes

# Ở bất kỳ đâu trong mã
add_span_attributes(
    user_id=user.id,
    operation_type="update",
    record_count=len(records)
)
```

### 5.5 Lấy trace ID hiện tại

```python
from odoo.addons.eb_api_core.utils import get_current_trace_id

trace_id = get_current_trace_id()
# Có thể sử dụng trace_id để liên kết với các hệ thống khác
```

### 5.6 Ghi lại ngoại lệ

```python
from odoo.addons.eb_api_core.utils import record_exception

try:
    # Thực hiện thao tác
except Exception as e:
    # Ghi lại exception vào span hiện tại
    record_exception(e)
    raise
```

### 5.7 Truyền context qua HTTP requests

```python
from odoo.addons.eb_api_core.utils import get_trace_context
import requests

# Lấy headers chứa thông tin trace
headers = get_trace_context()

# Thêm headers vào request
response = requests.get("https://another-service/api", headers=headers)
```

## 6. Logging với Tracing

EB API Core đã được cấu hình để tự động kết hợp logging với tracing. Để sử dụng:

```python
from odoo.addons.eb_api_core.utils import get_request_logger

logger = get_request_logger(__name__)

# Log message sẽ tự động bao gồm trace ID và request ID
logger.info("Thực hiện thao tác X")
logger.error("Lỗi khi thực hiện Y")
```

## 7. Cấu hình trong tệp odoo.conf

Cấu hình OpenTelemetry có thể được thiết lập trong tệp odoo.conf:

```
[options]
# OpenTelemetry Environment Variables
OTEL_ENABLED = true
OTEL_SERVICE_NAME = odoo-api
OTEL_EXPORTER_OTLP_ENDPOINT = http://localhost:4317
OTEL_RESOURCE_ATTRIBUTES = deployment.environment=development,service.version=1.0
```

EB API Core sẽ đọc cấu hình theo thứ tự ưu tiên sau:

1. Biến môi trường (nếu được thiết lập)
2. Cấu hình trong odoo.conf
3. Giá trị mặc định

Điều này cho phép bạn ghi đè cấu hình từ odoo.conf bằng biến môi trường khi cần thiết, ví dụ cho các môi trường khác nhau (development, staging, production).

### Sử dụng công cụ đọc cấu hình

Trong code, bạn có thể sử dụng các hàm trợ giúp để đọc cấu hình OpenTelemetry:

```python
from odoo.addons.eb_api_core.utils import get_otel_config, is_otel_enabled

# Kiểm tra xem OpenTelemetry có được bật không
if is_otel_enabled():
    # Làm gì đó với OpenTelemetry

# Đọc các cấu hình khác
service_name = get_otel_config("SERVICE_NAME", "default-service")
endpoint = get_otel_config("EXPORTER_OTLP_ENDPOINT", "http://localhost:4317")
```

## 8. Giải quyết vấn đề

### 8.1 Không thấy traces trong Jaeger

- Kiểm tra OTEL_ENABLED đã được thiết lập thành true
- Đảm bảo OTEL_EXPORTER_OTLP_ENDPOINT trỏ đến đúng địa chỉ Jaeger/collector
- Kiểm tra xem Jaeger có đang chạy và lắng nghe trên port 4317

### 8.2 Hiệu năng bị ảnh hưởng

- Giảm mức độ chi tiết của trace bằng cách chỉ trace các thao tác quan trọng
- Xem xét sử dụng sampling để giảm số lượng spans được gửi

## 9. Mở rộng

OpenTelemetry có thể được mở rộng để:

- Tích hợp với các hệ thống khác như Datadog, Elastic APM, New Relic
- Thêm metrics monitoring 
- Tùy chỉnh propagators để truyền context qua các hệ thống tùy chỉnh

## 10. Tham khảo thêm

- [Tài liệu chính thức của OpenTelemetry](https://opentelemetry.io/docs/)
- [Tài liệu của Jaeger](https://www.jaegertracing.io/docs/)