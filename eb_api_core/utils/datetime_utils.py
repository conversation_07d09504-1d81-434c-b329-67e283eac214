# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

from datetime import datetime
from typing import Optional, Any, Dict
import pytz

from odoo import fields


class DatetimeConfig:
    """Cấu hình định dạng datetime cho API response"""

    # Định dạng mặc định: yyyy-mm-dd hh:mm
    DEFAULT_DATETIME_FORMAT = "%Y-%m-%d %H:%M"
    DEFAULT_DATE_FORMAT = "%Y-%m-%d"

    # Sử dụng timezone của người dùng
    # Nếu người dùng không có timezone, sử dụng "Asia/Ho_Chi_Minh" (UTC+7) làm mặc định
    USE_USER_TIMEZONE = True


def format_datetime(dt_value: datetime, user_tz: Optional[str] = None,
                    dt_format: Optional[str] = None) -> str:
    """Chuyển đổi datetime sang định dạng chuỗi với timezone của người dùng.

    Args:
        dt_value: Giá trị datetime cần chuyển đổi (UTC)
        user_tz: Timezone của người dùng, nếu None sẽ sử dụng UTC
        dt_format: Định dạng datetime, nếu None sẽ sử dụng định dạng mặc định

    Returns:
        Chuỗi datetime đã được định dạng
    """
    if not dt_value:
        return ""

    # Nếu dt_value là chuỗi, chuyển đổi thành datetime
    if isinstance(dt_value, str):
        dt_value = fields.Datetime.from_string(dt_value)

    # Chuyển đổi từ UTC sang timezone của người dùng nếu cần
    if DatetimeConfig.USE_USER_TIMEZONE and user_tz:
        # Đảm bảo dt_value có timezone (UTC)
        if not dt_value.tzinfo:
            dt_value = pytz.UTC.localize(dt_value)
        # Chuyển đổi sang timezone của người dùng
        user_timezone = pytz.timezone(user_tz)
        dt_value = dt_value.astimezone(user_timezone)

    # Sử dụng định dạng mặc định nếu không có định dạng được chỉ định
    dt_format = dt_format or DatetimeConfig.DEFAULT_DATETIME_FORMAT

    # Định dạng datetime
    return dt_value.strftime(dt_format)


def format_date(date_value: Any, date_format: Optional[str] = None) -> str:
    """Chuyển đổi date sang định dạng chuỗi.

    Args:
        date_value: Giá trị date cần chuyển đổi
        date_format: Định dạng date, nếu None sẽ sử dụng định dạng mặc định

    Returns:
        Chuỗi date đã được định dạng
    """
    if not date_value:
        return ""

    # Nếu date_value là chuỗi, chuyển đổi thành date
    if isinstance(date_value, str):
        date_value = fields.Date.from_string(date_value)

    # Sử dụng định dạng mặc định nếu không có định dạng được chỉ định
    date_format = date_format or DatetimeConfig.DEFAULT_DATE_FORMAT

    # Định dạng date
    return date_value.strftime(date_format)


def get_user_timezone(env) -> str:
    """Lấy timezone của người dùng hiện tại.

    Args:
        env: Odoo environment

    Returns:
        Timezone của người dùng, "Asia/Ho_Chi_Minh" (UTC+7) nếu không có
    """
    return env.user.tz or "Asia/Ho_Chi_Minh"  # Sử dụng timezone Việt Nam làm mặc định


def format_meta_timestamp(timestamp: Optional[datetime] = None, user_tz: Optional[str] = None) -> str:
    """Định dạng timestamp cho metadata của API response.

    Args:
        timestamp: Timestamp cần định dạng, nếu None sẽ sử dụng thời gian hiện tại
        user_tz: Timezone của người dùng, nếu None sẽ sử dụng Asia/Ho_Chi_Minh

    Returns:
        Chuỗi timestamp đã được định dạng
    """
    timestamp = timestamp or datetime.now()

    # Chuyển đổi sang timezone của người dùng nếu cần
    if DatetimeConfig.USE_USER_TIMEZONE and user_tz:
        # Đảm bảo timestamp có timezone (UTC)
        if not timestamp.tzinfo:
            timestamp = pytz.UTC.localize(timestamp)
        # Chuyển đổi sang timezone của người dùng
        user_timezone = pytz.timezone(user_tz)
        timestamp = timestamp.astimezone(user_timezone)

    return timestamp.strftime(DatetimeConfig.DEFAULT_DATETIME_FORMAT)


def get_formatted_meta_data(request_id: str, env=None) -> Dict[str, Any]:
    """Tạo metadata chuẩn cho API response với định dạng timestamp mới.

    Args:
        request_id: ID của request
        env: Odoo environment, nếu None sẽ sử dụng Asia/Ho_Chi_Minh làm timezone mặc định

    Returns:
        Dict với timestamp, requestId
    """
    now = datetime.now()

    # Lấy timezone của người dùng hoặc sử dụng Asia/Ho_Chi_Minh nếu không có env
    user_tz = get_user_timezone(env) if env else "Asia/Ho_Chi_Minh"

    return {
        "timestamp": format_meta_timestamp(now, user_tz),
        "requestId": request_id
    }
