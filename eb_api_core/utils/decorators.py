# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and <PERSON>tis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging
import functools
from fastapi import APIRouter
from typing import List, Dict
from fastapi.responses import JSONResponse
from fastapi import status

from odoo.addons.eb_api_core.schemas.error import ErrorCode, ERROR_CODE_TO_STATUS

_logger = logging.getLogger(__name__)

# Lưu trữ thông tin về các API Routers đã đăng ký
registered_routers = {}


def register_api_module(
    module_name: str,
    description: str = None,
    version: str = "1.0",
    app_name: str = None,
):
    """Decorator để đăng ký một module API hoàn chỉnh.

    Args:
        module_name: Tên module API (được sử dụng trong URL)
        description: Mô tả cho module trong tài liệu API
        version: Phiên bản của module API
        app_name: Tên ứng dụng FastAPI (mặc định là module_name)

    Returns:
        Decorator function

    Example:
        @register_api_module('payment', 'Payment API', '1.0')
        class PaymentAPI:
            router = APIRouter()

            @router.get('/methods')
            def get_payment_methods():
                ...
    """

    def decorator(cls):
        # Đảm bảo class có router
        if not hasattr(cls, "router") or not isinstance(cls.router, APIRouter):
            raise ValueError(
                f"Class {cls.__name__} must have a 'router' attribute of type APIRouter"
            )

        # Thiết lập các thuộc tính metadata cho module
        cls.module_name = module_name
        cls.description = description or f"API for {module_name}"
        cls.version = version
        cls.app_name = app_name or module_name

        # Đăng ký router
        registered_routers[module_name] = {
            "router": cls.router,
            "module": cls,
            "description": cls.description,
            "version": cls.version,
            "app_name": cls.app_name,
        }

        _logger.info(f"Registered API module: {module_name}")
        return cls

    return decorator


def require_permissions(*permissions):
    """Decorator để yêu cầu quyền cụ thể cho một endpoint.

    Args:
        *permissions: Danh sách các quyền (permission keys) được yêu cầu

    Example:
        @router.get('/admin/settings')
        @require_permissions('admin.settings.read')
        def get_settings(current_user = Depends(get_current_user)):
            ...
    """

    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Lấy current_user từ kwargs
            current_user = kwargs.get("current_user")

            if not current_user:
                from fastapi import Depends, HTTPException, status
                from odoo.addons.eb_api_core.dependencies.auth import get_current_user

                # Nếu không có current_user, thử lấy từ dependency
                try:
                    current_user = await Depends(get_current_user)
                except Exception as e:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="Authentication required",
                    ) from e

            # Kiểm tra quyền
            if not current_user:
                from fastapi import HTTPException, status

                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required",
                )

            # TODO: Implement permission checking logic
            # has_permission = await check_permissions(current_user, permissions)
            has_permission = True

            if not has_permission:
                from fastapi import HTTPException, status

                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Insufficient permissions",
                )

            return await func(*args, **kwargs)

        # Gắn metadata vào function
        wrapper.__required_permissions__ = permissions
        return wrapper

    return decorator


def auto_error_response(
    include_status_codes: List[int] = None,
    custom_exceptions: Dict[type, tuple] = None,
    log_exceptions: bool = True,
):
    """Decorator để tự động chuyển đổi exceptions thành API errors.

    Args:
        include_status_codes: Danh sách các HTTP status codes muốn bắt và xử lý
        custom_exceptions: Dict mapping exception types to (message, error_code)
        log_exceptions: Có ghi log exceptions hay không

    Example:
        @router.get('/users/{user_id}')
        @auto_error_response(
            include_status_codes=[404, 400],
            custom_exceptions={
                UserNotFoundError: ("User not found", ErrorCode.NOT_FOUND),
                ValidationException: ("Validation error: {exc}", ErrorCode.VALIDATION_ERROR)
            }
        )
        def get_user(user_id: int):
            ...
    """
    if custom_exceptions is None:
        custom_exceptions = {}

    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # import các module cần thiết
            from fastapi import HTTPException
            from odoo.addons.eb_api_core.schemas.error import ErrorResponse
            from odoo.addons.eb_api_core.utils.trace_utils import (
                record_exception_in_trace,
            )
            from odoo.addons.eb_api_core.utils.request import get_meta_data

            try:
                return await func(*args, **kwargs)
            except HTTPException as e:
                # Chỉ xử lý các status codes được chỉ định
                if include_status_codes and e.status_code not in include_status_codes:
                    raise

                # Ghi nhận exception vào trace
                error_code = e.headers.get(
                    "X-Error-Code", ErrorCode.INTERNAL_SERVER_ERROR.value
                )
                record_exception_in_trace(e, error_code)

                # Map status code sang error code
                error_code_enum = None
                for code in ErrorCode:
                    if ERROR_CODE_TO_STATUS.get(code) == e.status_code:
                        error_code_enum = code
                        break

                # Nếu đã có error code trong header thì sử dụng nó
                if e.headers.get("X-Error-Code"):
                    try:
                        error_code_enum = ErrorCode(e.headers.get("X-Error-Code"))
                    except ValueError:
                        pass

                # Tạo error response
                error_resp = ErrorResponse(
                    error=str(e.detail),
                    error_code=error_code_enum,
                    meta=get_meta_data(),
                )

                return JSONResponse(
                    status_code=e.status_code,
                    headers=e.headers,
                    content=error_resp.model_dump(),
                )
            except Exception as e:
                # Kiểm tra xem exception có nằm trong custom_exceptions không
                for exc_type, (message, error_code) in custom_exceptions.items():
                    if isinstance(e, exc_type):
                        if log_exceptions:
                            _logger.warning(
                                f"{exc_type.__name__} in {func.__name__}: {str(e)}"
                            )

                        # Ghi nhận vào trace nếu có
                        record_exception_in_trace(e, error_code.value)

                        # Format message nếu cần (sử dụng {exc} để thay thế)
                        if "{exc}" in message:
                            error_message = message.format(exc=str(e))
                        else:
                            error_message = message or str(e)

                        # Tạo error response tùy chỉnh
                        error_resp = ErrorResponse(
                            error=error_message,
                            error_code=error_code,
                            meta=get_meta_data(),
                        )

                        # Lấy status code từ error code
                        status_code = ERROR_CODE_TO_STATUS.get(
                            error_code, status.HTTP_500_INTERNAL_SERVER_ERROR
                        )

                        return JSONResponse(
                            status_code=status_code, content=error_resp.model_dump()
                        )

                # Ghi log lỗi không xử lý được
                if log_exceptions:
                    _logger.exception(
                        f"Unhandled exception in {func.__name__}: {str(e)}"
                    )

                # Chỉ bắt lỗi nếu 500 nằm trong danh sách status codes cần bắt
                if not include_status_codes or 500 not in include_status_codes:
                    raise

                # Ghi nhận exception vào trace
                record_exception_in_trace(e, ErrorCode.INTERNAL_SERVER_ERROR.value)

                # Trả về lỗi server
                error_resp = ErrorResponse(
                    error="Internal server error",
                    error_code=ErrorCode.INTERNAL_SERVER_ERROR,
                    meta=get_meta_data(),
                )

                return JSONResponse(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    content=error_resp.model_dump(),
                )

        return wrapper

    return decorator
