# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging
import sys
from functools import lru_cache

from opentelemetry import trace

from odoo.addons.eb_api_core.middleware.tracing_middleware import get_request_id


# Các logger nên được tracking chi tiết
TRACKED_LOGGERS = [
    "odoo.addons.eb_api_core",
    "odoo.addons.eb_api_auth",
    "odoo.addons.fastapi_auth_jwt",
    "odoo.addons.fastapi",
]


class TracingLoggerAdapter(logging.LoggerAdapter):
    """Logger adapter với thông tin tracing đầy đủ"""

    def process(self, msg, kwargs):
        request_id = get_request_id()

        if request_id:
            return f"{msg} [requestId: {request_id}]", kwargs

        # Vẫn cố gắng lấy trace từ current span nếu không có requestId
        span = trace.get_current_span()
        span_context = span.get_span_context()
        if span_context.is_valid:
            trace_id = format(span_context.trace_id, "032x")
            return f"{msg} [traceId: {trace_id}]", kwargs

        return msg, kwargs


@lru_cache(maxsize=50)
def get_request_logger(name: str) -> logging.LoggerAdapter:
    """
    Lấy logger đã được cấu hình với Tracing adapter

    Hàm này có cache để tránh tạo lại logger adapter quá nhiều lần.

    Args:
        name: Tên của logger

    Returns:
        Logger được cấu hình với tracing
    """
    logger = logging.getLogger(name)
    return TracingLoggerAdapter(logger, {})


def setup_detailed_logging():
    """Thiết lập logging chi tiết cho API modules"""

    # Đảm bảo root logger được cấu hình
    root_logger = logging.getLogger()

    # Kiểm tra xem đã có handler chưa
    has_handlers = len(root_logger.handlers) > 0

    # Nếu chưa có handlers, thêm một handler mặc định
    if not has_handlers:
        handler = logging.StreamHandler(sys.stdout)
        formatter = logging.Formatter(
            "%(asctime)s %(name)s: %(message)s", "%Y-%m-%d %H:%M:%S"
        )
        handler.setFormatter(formatter)
        root_logger.addHandler(handler)

    # Đặt log level cho các logger cụ thể
    for logger_name in TRACKED_LOGGERS:
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.DEBUG)

    logging.getLogger("odoo.addons.eb_api_core").info(
        "Detailed API logging configured successfully"
    )
