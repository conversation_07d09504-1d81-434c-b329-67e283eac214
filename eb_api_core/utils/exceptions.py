# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging
from typing import List, Optional, Dict, Any, Union

from fastapi import HTTPException, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError

from odoo.exceptions import (
    UserError,
    AccessError,
    ValidationError as OdooValidationError,
    MissingError,
    CacheMiss,
)

from odoo.addons.eb_api_core.schemas.error import ErrorResponse, ErrorDetail, ErrorCode
from odoo.addons.eb_api_core.schemas.error import ERROR_CODE_TO_STATUS
from odoo.addons.eb_api_core.utils.trace_utils import record_exception_in_trace
from odoo.addons.eb_api_core.utils.request import get_meta_data

_logger = logging.getLogger(__name__)


def api_exception(
    error_code: Union[str, ErrorCode], detail: str, status_code: Optional[int] = None
) -> HTTPException:
    """Helper để tạo API exception với error code.

    Args:
        error_code: Mã lỗi từ ErrorCode enum
        detail: Mô tả chi tiết lỗi
        status_code: HTTP status code (tùy chọn, mặc định lấy từ error_code)

    Returns:
        HTTPException đã được cấu hình
    """
    if isinstance(error_code, str):
        # Chuyển đổi string thành ErrorCode enum
        try:
            error_code = ErrorCode(error_code)
        except ValueError:
            # Nếu không tìm thấy, sử dụng INTERNAL_SERVER_ERROR
            error_code = ErrorCode.INTERNAL_SERVER_ERROR

    # Lấy status code từ mapping hoặc sử dụng giá trị mặc định
    if not status_code:
        status_code = ERROR_CODE_TO_STATUS.get(
            error_code, status.HTTP_500_INTERNAL_SERVER_ERROR
        )

    return HTTPException(
        status_code=status_code,
        detail=detail,
        headers={"X-Error-Code": error_code.value},
    )


def format_validation_errors(errors: List[Dict[str, Any]]) -> List[ErrorDetail]:
    """Chuyển đổi pydantic validation errors thành định dạng error details.

    Args:
        errors: Danh sách các lỗi validation từ pydantic

    Returns:
        List các ErrorDetail objects
    """
    details = []
    for error in errors:
        location = error.get("loc", [])
        field = location[-1] if location else None

        # Convert field to string to handle cases where it's an integer (e.g., JSON parsing position)
        field_str = str(field) if field is not None else None

        # Determine error code based on error type
        error_type = error.get("type", "validation_error")
        if error_type == "json_invalid":
            code = "invalid_json"
        elif error_type in ["missing", "missing_required"]:
            code = "required_field"
        elif error_type in ["string_type", "int_parsing", "float_parsing"]:
            code = "invalid_type"
        else:
            code = "invalid_field"

        details.append(
            ErrorDetail(
                field=field_str,
                code=code,
                message=error.get("msg", "Validation error"),
            )
        )
    return details


def odoo_exception_handler(exc: Exception) -> Optional[JSONResponse]:
    """Xử lý Odoo exceptions và chuyển đổi thành API errors.

    Args:
        exc: Exception object

    Returns:
        JSONResponse hoặc None nếu không xử lý được
    """
    # Lưu các ánh xạ từ loại exception sang (error message, error_code, status_code)
    exception_mapping = {
        UserError: (
            "Business logic error",
            ErrorCode.BUSINESS_LOGIC_ERROR,
            status.HTTP_422_UNPROCESSABLE_ENTITY,
        ),
        AccessError: (
            "Insufficient permissions",
            ErrorCode.INSUFFICIENT_PERMISSIONS,
            status.HTTP_403_FORBIDDEN,
        ),
        OdooValidationError: (
            "Validation error",
            ErrorCode.VALIDATION_ERROR,
            status.HTTP_400_BAD_REQUEST,
        ),
        MissingError: (
            "Resource not found",
            ErrorCode.NOT_FOUND,
            status.HTTP_404_NOT_FOUND,
        ),
        CacheMiss: (
            "Resource not available",
            ErrorCode.NOT_FOUND,
            status.HTTP_404_NOT_FOUND,
        ),
    }

    # Kiểm tra exception có thuộc loại nào trong mapping
    for exc_type, (error_message, error_code, status_code) in exception_mapping.items():
        if isinstance(exc, exc_type):
            # Ghi nhận exception vào trace
            record_exception_in_trace(exc, error_code.value)

            # Tạo error response
            error_resp = ErrorResponse(
                error=str(exc) or error_message,
                error_code=error_code,
                meta=get_meta_data(),
            )

            return JSONResponse(status_code=status_code, content=error_resp.model_dump())

    # Thử xử lý PostgreSQL constraint exceptions
    if hasattr(exc, "pgcode"):
        # Lỗi unique constraint
        if getattr(exc, "pgcode") == "23505":
            record_exception_in_trace(exc, ErrorCode.ALREADY_EXISTS.value)
            error_resp = ErrorResponse(
                error="A record with this data already exists",
                error_code=ErrorCode.ALREADY_EXISTS,
                meta=get_meta_data(),
            )
            return JSONResponse(
                status_code=status.HTTP_409_CONFLICT, content=error_resp.model_dump()
            )

        # Lỗi foreign key constraint
        if getattr(exc, "pgcode") == "23503":
            record_exception_in_trace(exc, ErrorCode.INVALID_REFERENCE.value)
            error_resp = ErrorResponse(
                error="Invalid reference to another record",
                error_code=ErrorCode.INVALID_REFERENCE,
                meta=get_meta_data(),
            )
            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST, content=error_resp.model_dump()
            )

        # Các lỗi database khác
        record_exception_in_trace(exc, ErrorCode.DATABASE_ERROR.value)
        error_resp = ErrorResponse(
            error="Database error occurred",
            error_code=ErrorCode.DATABASE_ERROR,
            meta=get_meta_data(),
        )
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content=error_resp.model_dump()
        )

    # Xử lý Redis errors nếu dự án sử dụng Redis
    try:
        import redis

        if isinstance(exc, redis.RedisError):
            record_exception_in_trace(exc, ErrorCode.DEPENDENCY_ERROR.value)
            error_resp = ErrorResponse(
                error="Redis service unavailable",
                error_code=ErrorCode.DEPENDENCY_ERROR,
                meta=get_meta_data(),
            )
            return JSONResponse(
                status_code=status.HTTP_502_BAD_GATEWAY, content=error_resp.model_dump()
            )
    except ImportError:
        # Không có Redis, bỏ qua
        pass

    # Return None if we can't handle this exception
    return None


def request_validation_exception_handler(exc: RequestValidationError) -> JSONResponse:
    """Xử lý FastAPI validation errors.

    Args:
        exc: RequestValidationError object

    Returns:
        JSONResponse với error details
    """
    _logger.warning(f"Request validation error: {exc.errors()}")

    # Ghi nhận exception vào trace
    record_exception_in_trace(exc, ErrorCode.VALIDATION_ERROR.value)

    error_details = format_validation_errors(exc.errors())

    error_resp = ErrorResponse(
        error="Request validation error",
        error_code=ErrorCode.VALIDATION_ERROR,
        details=error_details,
        meta=get_meta_data(),
    )

    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST, content=error_resp.model_dump()
    )


def register_exception_handlers(app):
    """Đăng ký tất cả exception handlers cho FastAPI app.

    Args:
        app: FastAPI application instance
    """
    from fastapi.exceptions import RequestValidationError

    _logger.info("Registering API exception handlers")

    # Đăng ký handler cho FastAPI validation errors
    app.add_exception_handler(
        RequestValidationError, request_validation_exception_handler
    )

    # Đăng ký handler cho HTTPException
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request, exc):
        # Map error code từ header nếu có
        error_code = exc.headers.get("X-Error-Code")

        # Ghi nhận exception vào trace
        record_exception_in_trace(exc, error_code or str(exc.status_code))

        # Tạo error response
        error_resp = ErrorResponse(
            error=str(exc.detail), error_code=error_code, meta=get_meta_data()
        )

        return JSONResponse(
            status_code=exc.status_code, headers=exc.headers, content=error_resp.model_dump()
        )

    # Custom exception handler cho tất cả exceptions
    @app.exception_handler(Exception)
    async def general_exception_handler(request, exc):
        # Thử xử lý Odoo exceptions
        response = odoo_exception_handler(exc)
        if response:
            return response

        # Log lỗi không xử lý được
        _logger.exception(f"Unhandled exception: {str(exc)}")

        # Ghi nhận exception vào trace
        record_exception_in_trace(exc, ErrorCode.INTERNAL_SERVER_ERROR.value)

        # Trả về lỗi server chung
        error_resp = ErrorResponse(
            error="Internal server error",
            error_code=ErrorCode.INTERNAL_SERVER_ERROR,
            meta=get_meta_data(),
        )

        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content=error_resp.model_dump()
        )
