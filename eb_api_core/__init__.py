# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging

# Import tất cả các package con - được sử dụng bởi Odoo để load các modules
from . import models  # noqa: F401
from . import schemas  # noqa: F401
from . import utils  # noqa: F401
from . import dependencies  # noqa: F401
from . import routers  # noqa: F401
from . import hooks  # noqa: F401
from . import middleware  # noqa: F401

# Export hooks để Odoo có thể gọi từ __manifest__.py
from .hooks import post_init_hook  # noqa: F401
from .hooks import post_load_hook  # noqa: F401

_logger = logging.getLogger(__name__)
_logger.info("Loading eBill API Core module")


# Thiết lập logging chi tiết
def setup_logging():
    """Thiết lập logging cho module khi được load"""
    from .utils.logging_setup import setup_detailed_logging

    setup_detailed_logging()
    _logger.info("eBill API Core logging configured")


# Thực hiện cấu hình logging khi module được tải
setup_logging()
