# -*- coding: utf-8 -*-
{
    "name": "EB API Core",
    "summary": "Core API framework for EarnBase modules",
    "description": """
        Provides a standardized API framework with:
        - Common response formats
        - Authentication & authorization
        - API routing and discovery
        - Exception handling
        - Utilities for module API integration
        - Distributed tracing with OpenTelemetry
    """,
    "version": "********.0",
    "category": "Technical/API",
    "author": "eBill and Vantis Vietnam",
    "website": "https://earnbase.io",
    "license": "LGPL-3",
    "depends": [
        "base",
        "web",
        "fastapi",
        "fastapi_auth_jwt",
        "auth_jwt",
    ],
    "data": [
        "security/ir.model.access.csv",
        "data/fastapi_endpoint_data.xml",
    ],
    "demo": [],
    "external_dependencies": {
        "python": [
            "fastapi>=0.104.1",
            "starlette>=0.31.1",
            "opentelemetry-api>=1.21.0",
            "opentelemetry-sdk>=1.21.0",
            "opentelemetry-exporter-otlp>=1.21.0",
            "opentelemetry-instrumentation-fastapi>=0.42b0",
            "opentelemetry-instrumentation>=0.42b0",
            "a2wsgi>=1.0.0",
            "pydantic>=2.3.0",
            # Phiên bản cryptography phải tương thích với Odoo
            # "cryptography>=41.0.0" - phiên bản này không tương thích với Odoo 18
            # Redis là phụ thuộc tùy chọn cho rate limiting
            # "redis>=4.5.1", # Optional - nếu muốn sử dụng Redis cho rate limiting
        ],
    },
    "post_init_hook": "post_init_hook",
    "post_load": "post_load_hook",
    "installable": True,
    "application": False,
    "auto_install": False,
} 