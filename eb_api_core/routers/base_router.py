# -*- coding: utf-8 -*-
# Copyright 2025 Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging
from datetime import datetime
from typing import List

from fastapi import APIRouter, Depends

from odoo.api import Environment
from odoo.addons.fastapi.dependencies import odoo_env

from odoo.addons.eb_api_core.schemas.base import ApiInfo, ApiStatus, ResponseBase
from odoo.addons.eb_api_core.routers.registry import get_registry

_logger = logging.getLogger(__name__)

# Base router cho API core
base_router = APIRouter(tags=["core"])


@base_router.get("/", response_model=ApiStatus)
async def get_api_info(
    env: Environment = Depends(odoo_env),
) -> ApiStatus:
    """Trả về thông tin tổng quan về API."""
    registry = get_registry()

    # Lấy version từ ir.module.module
    version = "1.0.0"
    try:
        module = (
            env["ir.module.module"]
            .sudo()
            .search([("name", "=", "eb_api_core")], limit=1)
        )
        if module:
            version = module.installed_version or "1.0.0"
    except Exception as e:
        _logger.error(f"Error getting module version: {str(e)}")

    return ApiStatus(
        success=True,
        message="API is operational",
        info=ApiInfo(
            version=version,
            name="Vantis LMS API",
            description="Vantis LMS API Framework for Odoo",
            documentation_url="/docs",
        ),
        server_time=datetime.now(),
        modules=registry.get_module_names(),
    )


@base_router.get("/health", response_model=ResponseBase)
async def health_check() -> ResponseBase:
    """Endpoint kiểm tra sức khỏe hệ thống API."""
    return ResponseBase(success=True, message="API is healthy")


@base_router.get("/modules", response_model=List[str])
async def get_api_modules() -> List[str]:
    """Trả về danh sách tất cả API modules đã đăng ký."""
    registry = get_registry()
    return registry.get_module_names()
