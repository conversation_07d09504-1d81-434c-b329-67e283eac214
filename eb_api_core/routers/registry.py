# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging
import importlib
from typing import Dict, List, Optional, Any

from fastapi import APIRouter

from odoo import modules
from odoo.addons.eb_api_core.utils.decorators import registered_routers

_logger = logging.getLogger(__name__)


class ApiRegistry:
    """Đăng ký và quản lý tất cả các API modules."""

    def __init__(self):
        self.modules = {}
        self.routers = {}
        self.loaded = False

    def register_module(self, module_name: str, module_data: Dict[str, Any]):
        """Đăng ký một API module mới.

        Args:
            module_name: Tên module API
            module_data: Dictionary chứa router và metadata
        """
        self.modules[module_name] = module_data
        self.routers[module_name] = module_data.get("router")
        _logger.info(f"API module registered: {module_name}")

    def discover_modules(self):
        """Khám phá tất cả các API modules trong các addon Odoo."""
        for module_name in modules.module.get_modules():
            if module_name.startswith("eb_") and module_name != "eb_api_core":
                try:
                    # Thử import module api nếu có
                    module_path = f"odoo.addons.{module_name}.api"
                    importlib.import_module(module_path)
                    _logger.debug(f"Checked API module: {module_name}")
                except ImportError:
                    # Không có module api, bỏ qua
                    pass

        # Import từ decorated routers
        for name, data in registered_routers.items():
            self.register_module(name, data)

        self.loaded = True
        _logger.info(f"API registry loaded {len(self.modules)} modules")

    def get_router(self, module_name: str) -> Optional[APIRouter]:
        """Lấy router cho một module cụ thể.

        Args:
            module_name: Tên module API

        Returns:
            APIRouter object hoặc None nếu không tìm thấy
        """
        return self.routers.get(module_name)

    def get_all_routers(self) -> List[APIRouter]:
        """Lấy tất cả routers đã đăng ký.

        Returns:
            List các APIRouter objects
        """
        return list(self.routers.values())

    def get_module_names(self) -> List[str]:
        """Lấy danh sách tên tất cả modules đã đăng ký.

        Returns:
            List tên các modules
        """
        return list(self.modules.keys())

    def get_module_info(self, module_name: str) -> Optional[Dict[str, Any]]:
        """Lấy thông tin chi tiết về một module.

        Args:
            module_name: Tên module API

        Returns:
            Dictionary chứa thông tin module hoặc None
        """
        return self.modules.get(module_name)


# Singleton instance
_registry = ApiRegistry()


def get_registry() -> ApiRegistry:
    """Lấy instance của API registry.

    Returns:
        ApiRegistry instance
    """
    return _registry


def initialize_registry():
    """Khởi tạo registry và khám phá các modules."""
    if not _registry.loaded:
        _registry.discover_modules()


def get_module_router(module_name: str) -> Optional[APIRouter]:
    """Helper để lấy router cho một module.

    Args:
        module_name: Tên module API

    Returns:
        APIRouter cho module
    """
    return _registry.get_router(module_name)
