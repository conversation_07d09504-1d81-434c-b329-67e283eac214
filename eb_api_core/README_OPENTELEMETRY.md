# Cấu hình OpenTelemetry trong Odoo

## Giới thiệu

OpenTelemetry là một framework tracing mã nguồn mở, cung cấp khả năng theo dõi và ghi lại thông tin về hoạt động của ứng dụng. Module `eb_api_core` tích hợp OpenTelemetry để giúp theo dõi và ghi lại các hoạt động API và xử lý yêu cầu.

## Cấu hình OpenTelemetry trong odoo.conf

OpenTelemetry được cấu hình thông qua file `odoo.conf` với tiền tố `otel_` cho mỗi tùy chọn. Dưới đây là các cấu hình cơ bản:

### Các cấu hình cơ bản

| Cấu hình | Mô tả | Giá trị mặc định | Ví dụ |
|----------|-------|------------------|-------|
| `otel_enabled` | Bật/tắt OpenTelemetry | `false` | `otel_enabled = true` |
| `otel_service_name` | Tên service trong tracing | `odoo-api` | `otel_service_name = my-odoo-service` |
| `otel_service_version` | Phiên bản service | `1.0.0` | `otel_service_version = 2.1.0` |
| `otel_environment` | Môi trường triển khai | `production` | `otel_environment = development` |

### Cấu hình exporter

| Cấu hình | Mô tả | Giá trị mặc định | Ví dụ |
|----------|-------|------------------|-------|
| `otel_exporter_type` | Loại exporter (otlp/console) | `otlp` | `otel_exporter_type = console` |
| `otel_exporter_otlp_endpoint` | Endpoint của OpenTelemetry Collector | ` ` | `otel_exporter_otlp_endpoint = http://localhost:4317` |
| `otel_exporter_otlp_headers` | Headers cho OTLP exporter | ` ` | `otel_exporter_otlp_headers = api-key=YOUR_API_KEY` |

### Cấu hình bổ sung

| Cấu hình | Mô tả | Giá trị mặc định | Ví dụ |
|----------|-------|------------------|-------|
| `otel_resource_attributes` | Resource attributes bổ sung | ` ` | `otel_resource_attributes = team=backend,region=asia` |
| `otel_fastapi_excluded_urls` | URL bị loại trừ khỏi tracing | `*/favicon.ico,*/docs,*/openapi.json` | `otel_fastapi_excluded_urls = */health,*/static/*` |

## Lưu ý quan trọng

- **Kiểu dữ liệu**: `otel_enabled` có thể sử dụng kiểu boolean (true/false) hoặc chuỗi ("true"/"false", "1"/"0", "yes"/"no").
- **Format chuỗi**: Các cấu hình dạng danh sách như `otel_resource_attributes` và `otel_exporter_otlp_headers` sử dụng format `key1=value1,key2=value2`.
- **Đường dẫn**: `otel_exporter_otlp_endpoint` nên trỏ đến OpenTelemetry Collector với port 4317 (gRPC).

## Ví dụ cấu hình đầy đủ

```ini
# OpenTelemetry Configuration
otel_enabled = true
otel_service_name = odoo-api
otel_service_version = 1.0.0
otel_environment = production

# Exporter configuration
otel_exporter_type = otlp
otel_exporter_otlp_endpoint = http://otel-collector:4317
otel_exporter_otlp_headers = auth=token123,tenant=company1

# Additional configuration
otel_resource_attributes = deployment.region=asia-southeast1,team=backend
otel_fastapi_excluded_urls = */favicon.ico,*/docs,*/openapi.json,*/static/*
```

## Sử dụng OpenTelemetry trong code

### Tạo span mới

```python
from odoo.addons.eb_api_core.utils.tracing import create_span

# Tạo span với tên và attributes
with create_span("operation_name", attributes={"key": "value"}):
    # Code được thực thi trong span này
    result = perform_operation()
```

### Thêm attributes vào span hiện tại

```python
from odoo.addons.eb_api_core.utils.trace_utils import add_span_attributes

# Thêm attributes vào span hiện tại
add_span_attributes(operation_name="login", user_id=user.id)
# Hoặc sử dụng dictionary
add_span_attributes({"operation_name": "login", "user_id": user.id})
```

### Sử dụng decorator để tự động tạo span

```python
from odoo.addons.eb_api_core.utils.trace_utils import trace

# Decorator để tự động tạo span
@trace(operation_type="database")
def perform_database_operation(self, param):
    # Code của function
    return result
```

### Lấy trace context cho HTTP headers

```python
from odoo.addons.eb_api_core.utils.trace_utils import get_trace_context

# Lấy trace context để truyền qua HTTP headers
headers = get_trace_context()
response = requests.get("https://api.example.com", headers=headers)
```

## Xem trace data

Dữ liệu trace được xuất ra theo cấu hình exporter:

1. **Console Exporter**: Dữ liệu sẽ được in ra console (stdout).
2. **OTLP Exporter**: Dữ liệu sẽ được gửi đến OpenTelemetry Collector và có thể xem qua các UI như Jaeger, Zipkin, hay Grafana Tempo.

## Khắc phục sự cố

- **Không thấy trace data**: Kiểm tra `otel_enabled` đã được đặt thành `true` và đường dẫn `otel_exporter_otlp_endpoint` đã chính xác.
- **Lỗi kết nối đến collector**: Kiểm tra firewall và đảm bảo Collector đang chạy và lắng nghe ở port được cấu hình.
- **Thông tin trace không đầy đủ**: Đảm bảo bạn đã tạo span với đủ attributes và có truyền context giữa các dịch vụ. 