# 📋 Full API Request & Response Documentation

## 🎯 Overview
This document contains the complete request and response details for the Instructor Profile API integration.

---

## 🔐 Step 1: Authentication (Login)

### 📤 **LOGIN REQUEST**

**URL:** `https://lms-dev.ebill.vn/api/v1/auth/sign-in`  
**Method:** `POST`  
**Content-Type:** `application/json`  
**Accept:** `application/json`

**Request Body:**
```json
{
  "username": "<EMAIL>",
  "password": "earnbase@X2025",
  "device_info": {
    "device_id": "test-device",
    "device_name": "Test Device",
    "device_type": "mobile",
    "app_version": "1.0.0",
    "os_version": "17.0"
  },
  "remember_me": false
}
```

### 📥 **LOGIN RESPONSE**

**HTTP Status:** `200 OK`  
**Response Time:** `0.647s`  
**Content-Type:** `application/json`  
**Server:** `nginx/1.18.0`

**Response Body:**
```json
{
  "success": true,
  "message": "Authentication successful",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.My8uoH6ys1QiWHdwLH4f_j4EyMlqPfw1v35yJHd187E",
    "token_type": "Bearer",
    "expires_in": 31536000,
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************.tZgZbMEZakpKdE98ZAGA_fUlId5vyGq-vvzJBc80QNQ",
    "refresh_token_expires_in": 31536000,
    "scope": "api",
    "issued_at": 1753633047,
    "user_id": 16,
    "device_registered": true,
    "device_info": {
      "device_id": "test-device",
      "device_name": "Test Device",
      "device_type": "mobile",
      "os_name": null,
      "os_version": "17.0",
      "app_version": "1.0.0",
      "browser_name": null,
      "browser_version": null,
      "user_agent": null,
      "ip_address": null,
      "location": null
    },
    "remember_me": false,
    "login_method": "email"
  },
  "meta": {
    "timestamp": "2025-07-27 23:17",
    "requestId": "req-36564858",
    "traceId": "b9bdf08be2c53858d04abe99b98fd639",
    "jaegerTraceId": "b9bdf08be2c53858d04abe99b98fd639"
  }
}
```

### 🔍 **JWT Token Analysis**
- **Token Length:** 788 characters
- **User ID:** 16
- **Role:** instructor
- **Name:** "Trần Thái Ngân"
- **Login:** "<EMAIL>"
- **Expires:** 31536000 seconds (1 year)
- **Permissions:** Full instructor permissions including profile management

---

## 👤 Step 2: Profile Request

### 📤 **PROFILE REQUEST**

**URL:** `https://lms-dev.ebill.vn/api/v1/instructors/profile`  
**Method:** `GET`  
**Accept:** `application/json`  
**Authorization:** `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6Ik...35yJHd187E`

### 📥 **PROFILE RESPONSE**

**HTTP Status:** `500 Internal Server Error`  
**Response Time:** `0.173s`  
**Content-Type:** `application/json`  
**Server:** `nginx/1.18.0`

**Response Body:**
```json
{
  "success": false,
  "error": "Không thể tải hồ sơ giảng viên: 500: Không thể tải thông tin hồ sơ: 1 validation error for InstructorProfile\nbirth_date\n  Input should be a valid date [type=date_type, input_value=False, input_type=bool]\n    For further information visit https://errors.pydantic.dev/2.11/v/date_type",
  "error_code": "INTERNAL_SERVER_ERROR",
  "details": null,
  "meta": {
    "timestamp": "2025-07-27 23:17",
    "requestId": "req-4ffae0fe",
    "traceId": "f88b7246483bb8f2ab40e7339b123519",
    "jaegerTraceId": "f88b7246483bb8f2ab40e7339b123519"
  }
}
```

---

## 🔍 Error Analysis

### 🎯 **Root Cause**
- **Field:** `birth_date`
- **Issue:** Server returning `False` (boolean) instead of date/null
- **Validation Error:** Pydantic expects date type but receives boolean
- **Impact:** Profile API returns 500 error

### 💡 **Technical Details**
- **Framework:** Pydantic validation (Python backend)
- **Expected Type:** `date`
- **Actual Value:** `False` (boolean)
- **Error Type:** `date_type`
- **Reference:** https://errors.pydantic.dev/2.11/v/date_type

---

## 🛠️ Implementation Solution

### ✅ **Frontend Handling**
1. **Robust Model:** Created `InstructorProfile` with custom decoder
2. **Error Handling:** Graceful handling of server validation errors
3. **Fallback UI:** Show basic profile when full profile fails
4. **User Experience:** No app crashes, friendly error messages

### 🔧 **Backend Fix Needed**
```sql
-- Fix birth_date field in database
UPDATE instructor_profiles 
SET birth_date = NULL 
WHERE birth_date = FALSE;
```

---

## 📊 Performance Metrics

| Metric | Login | Profile |
|--------|-------|---------|
| **Response Time** | 0.647s | 0.173s |
| **HTTP Status** | 200 ✅ | 500 ❌ |
| **Success** | ✅ | ❌ |
| **Token Obtained** | ✅ | N/A |

---

## 🚀 Integration Status

- ✅ **InstructorProfile model** created with robust error handling
- ✅ **InstructorProfileService** implemented with API integration
- ✅ **InstructorProfileViewModel** for UI binding
- ✅ **InstructorProfileView** with fallback UI
- ✅ **Error handling** for birth_date validation issue
- ✅ **Ready for integration** into existing app

---

## 📝 Curl Commands for Testing

### Login:
```bash
curl -X 'POST' \
  'https://lms-dev.ebill.vn/api/v1/auth/sign-in' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
    "username": "<EMAIL>",
    "password": "earnbase@X2025",
    "device_info": {
      "device_id": "test-device",
      "device_name": "Test Device",
      "device_type": "mobile",
      "app_version": "1.0.0",
      "os_version": "17.0"
    },
    "remember_me": false
  }'
```

### Profile:
```bash
curl -X 'GET' \
  'https://lms-dev.ebill.vn/api/v1/instructors/profile' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer YOUR_TOKEN_HERE'
```

---

**Last Updated:** 2025-07-27 23:17  
**Status:** ✅ Implementation Complete, Ready for Integration
