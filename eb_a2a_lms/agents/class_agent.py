# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging

# Import Agent từ eb_a2a hoặc trực tiếp từ crewai
try:
    from crewai import Agent
    CREWAI_AVAILABLE = True
except ImportError:
    from odoo.addons.eb_a2a.ai_agents.agents.base_agents import Agent, CREWAI_AVAILABLE

# Import adapter cho CrewAI
from odoo.addons.eb_a2a.ai_agents.services.crewai_adapter import create_llm_for_crewai

from ..prompts.class_prompts import get_class_agent_prompt
from ..tools.class_tools import ClassScheduleTool, ClassSearchTool

_logger = logging.getLogger(__name__)


def create_class_agent(env, llm=None):
    """Tạo agent chuyên về lớp học và lịch học sử dụng CrewAI.

    Args:
        env: Odoo environment
        llm: Language Model (tùy chọn)

    Returns:
        Agent: CrewAI Agent
    """
    try:
        # Kiểm tra xem CrewAI có khả dụng không
        if not CREWAI_AVAILABLE:
            _logger.error("CrewAI libraries not available")
            return None

        # Lấy system prompt
        system_prompt = get_class_agent_prompt()

        # Tạo các tool
        class_search_tool = ClassSearchTool(env)
        class_schedule_tool = ClassScheduleTool(env)

        # Lấy LLM nếu chưa có
        if not llm:
            # Tìm provider mặc định
            provider = env['eb_a2a.provider'].sudo().search([('is_default', '=', True)], limit=1)
            if not provider:
                provider = env['eb_a2a.provider'].sudo().search([], limit=1)

            if provider:
                # Tạo LLM cho CrewAI
                llm = create_llm_for_crewai(provider)

        # Tạo Agent CrewAI
        agent = Agent(
            role="Class Schedule Expert",
            goal="Tìm kiếm lớp học và cung cấp lịch học",
            backstory="Bạn là chuyên gia về lớp học và lịch học, có kiến thức sâu rộng về tất cả các lớp học và lịch học trong hệ thống.",
            verbose=True,
            llm=llm,
            tools=[class_search_tool.tool, class_schedule_tool.tool]
        )

        return agent
    except Exception as e:
        _logger.exception(f"Lỗi khi tạo Class Agent: {str(e)}")
        return None
