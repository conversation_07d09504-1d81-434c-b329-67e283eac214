# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging
from typing import Dict, List, Any, Optional, Union

try:
    from crewai import Agent
    CREWAI_AVAILABLE = True
except ImportError:
    from odoo.addons.eb_a2a.ai_agents.crews.base_crews import Agent, CREWAI_AVAILABLE

_logger = logging.getLogger(__name__)

def create_manager_agent(env, llm=None):
    """Tạo Manager Agent cho LMS Crew.

    Args:
        env: Odoo environment
        llm: Language Model (tùy chọn)

    Returns:
        Agent: CrewAI Agent
    """
    try:
        if not CREWAI_AVAILABLE:
            _logger.warning("CrewAI không khả dụng. Không thể tạo Manager Agent.")
            return None

        # Tạo Manager Agent
        agent = Agent(
            role="LMS Manager",
            goal="Điều phối các agent để cung cấp thông tin chính xác và đầy đủ về khóa học, gi<PERSON>ng viên và lớp học",
            backstory="""Bạn là người quản lý hệ thống LMS, có kiến thức tổng quan về tất cả các khía cạnh của hệ thống.
            Bạn hiểu rõ về khóa học, giảng viên và lớp học. Nhiệm vụ của bạn là điều phối các chuyên gia để cung cấp
            thông tin chính xác và đầy đủ cho người dùng.
            
            Khi nhận được yêu cầu từ người dùng, bạn sẽ phân tích yêu cầu và quyết định nên hỏi chuyên gia nào.
            Nếu yêu cầu liên quan đến khóa học, bạn sẽ hỏi Course Expert.
            Nếu yêu cầu liên quan đến giảng viên, bạn sẽ hỏi Instructor Expert.
            Nếu yêu cầu liên quan đến lớp học, bạn sẽ hỏi Class Expert.
            
            Sau khi nhận được thông tin từ các chuyên gia, bạn sẽ tổng hợp và cung cấp câu trả lời đầy đủ cho người dùng.
            """,
            verbose=True,
            llm=llm
        )
        
        return agent
    except Exception as e:
        _logger.error(f"Lỗi khi tạo Manager Agent: {str(e)}")
        return None
