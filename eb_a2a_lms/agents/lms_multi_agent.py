# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and <PERSON>tis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging
from ..crews.lms_crew import create_lms_crew

_logger = logging.getLogger(__name__)


# Giữ lại hàm cũ để tương thích ngượ<PERSON>
def create_lms_multi_agent(env, model=None):
    """Hàm wrapper để tương thích ngượ<PERSON>, gọi create_lms_crew.

    Args:
        env: Odoo environment
        model: Model AI (không sử dụng, chỉ để tương thích)

    Returns:
        Crew: CrewAI Crew
    """
    _logger.warning("Hàm create_lms_multi_agent đã lỗi thời. Sử dụng create_lms_crew thay thế.")
    return create_lms_crew(env)
