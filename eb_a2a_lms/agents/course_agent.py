# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging

# Import Agent từ eb_a2a hoặc trực tiếp từ crewai
try:
    from crewai import Agent
    CREWAI_AVAILABLE = True
except ImportError:
    from odoo.addons.eb_a2a.ai_agents.agents.base_agents import Agent, CREWAI_AVAILABLE

# Import adapter cho CrewAI
from odoo.addons.eb_a2a.ai_agents.services.crewai_adapter import create_llm_for_crewai

from ..prompts.course_prompts import get_course_agent_prompt
from ..tools.course_tools import CourseSearchTool, CourseDetailTool

_logger = logging.getLogger(__name__)


def create_course_agent(env, llm=None):
    """Tạo agent chuyên về kh<PERSON>a học sử dụng CrewAI.

    Args:
        env: Odoo environment
        llm: Language Model (tùy chọn)

    Returns:
        Agent: CrewAI Agent
    """
    try:
        # Ki<PERSON>m tra xem CrewAI có khả dụng không
        if not CREWAI_AVAILABLE:
            _logger.error("CrewAI libraries not available")
            return None

        # Lấy system prompt
        system_prompt = get_course_agent_prompt()

        # Tạo các tool
        course_search_tool = CourseSearchTool(env)
        course_detail_tool = CourseDetailTool(env)

        # Lấy LLM nếu chưa có
        if not llm:
            # Tìm provider mặc định
            provider = env['eb_a2a.provider'].sudo().search([('is_default', '=', True)], limit=1)
            if not provider:
                provider = env['eb_a2a.provider'].sudo().search([], limit=1)

            if provider:
                # Tạo LLM cho CrewAI
                llm = create_llm_for_crewai(provider)

        # Tạo Agent CrewAI
        agent = Agent(
            role="Course Search Expert",
            goal="Tìm kiếm và cung cấp thông tin chi tiết về các khóa học",
            backstory="Bạn là chuyên gia về khóa học, có kiến thức sâu rộng về tất cả các khóa học trong hệ thống.",
            verbose=True,
            llm=llm,
            tools=[course_search_tool.tool, course_detail_tool.tool]
        )

        return agent
    except Exception as e:
        _logger.exception(f"Lỗi khi tạo Course Agent: {str(e)}")
        return None
