# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging

# Import Agent từ eb_a2a hoặc trực tiếp từ crewai
try:
    from crewai import Agent
    CREWAI_AVAILABLE = True
except ImportError:
    from odoo.addons.eb_a2a.ai_agents.agents.base_agents import Agent, CREWAI_AVAILABLE

# Import adapter cho CrewAI
from odoo.addons.eb_a2a.ai_agents.services.crewai_adapter import create_llm_for_crewai

from ..prompts.instructor_prompts import get_instructor_agent_prompt
from ..tools.instructor_tools import InstructorSearchTool, InstructorDetailTool

_logger = logging.getLogger(__name__)


def create_instructor_agent(env, llm=None):
    """Tạo agent chuy<PERSON>n về giảng viên sử dụng CrewAI.

    Args:
        env: Odoo environment
        llm: Language Model (tùy chọn)

    Returns:
        Agent: CrewAI Agent
    """
    try:
        # Kiểm tra xem CrewAI có khả dụng không
        if not CREWAI_AVAILABLE:
            _logger.error("CrewAI libraries not available")
            return None

        # Lấy system prompt
        system_prompt = get_instructor_agent_prompt()

        # Tạo các tool
        instructor_search_tool = InstructorSearchTool(env)
        instructor_detail_tool = InstructorDetailTool(env)

        # Lấy LLM nếu chưa có
        if not llm:
            # Tìm provider mặc định
            provider = env['eb_a2a.provider'].sudo().search([('is_default', '=', True)], limit=1)
            if not provider:
                provider = env['eb_a2a.provider'].sudo().search([], limit=1)

            if provider:
                # Tạo LLM cho CrewAI
                llm = create_llm_for_crewai(provider)

        # Tạo Agent CrewAI
        agent = Agent(
            role="Instructor Search Expert",
            goal="Tìm kiếm và cung cấp thông tin chi tiết về các giảng viên",
            backstory="Bạn là chuyên gia về giảng viên, có kiến thức sâu rộng về tất cả các giảng viên trong hệ thống.",
            verbose=True,
            llm=llm,
            tools=[instructor_search_tool.tool, instructor_detail_tool.tool]
        )

        return agent
    except Exception as e:
        _logger.exception(f"Lỗi khi tạo Instructor Agent: {str(e)}")
        return None
