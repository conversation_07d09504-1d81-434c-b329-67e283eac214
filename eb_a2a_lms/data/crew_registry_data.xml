<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- LMS Crew Registry -->
        <record id="crew_registry_lms" model="eb_a2a.crew.registry">
            <field name="name">LMS Crew</field>
            <field name="code">lms</field>
            <field name="module_path">odoo.addons.eb_a2a_lms.crews.lms_crew</field>
            <field name="function_name">create_lms_crew</field>
            <field name="description">Crew cho hệ thống L<PERSON>, hỗ trợ tìm kiếm kh<PERSON>a học, lớ<PERSON> học và thông tin giảng viên</field>
        </record>
        
        <!-- Course Crew Registry -->
        <record id="crew_registry_course" model="eb_a2a.crew.registry">
            <field name="name">Course Crew</field>
            <field name="code">course</field>
            <field name="module_path">odoo.addons.eb_a2a_lms.agents.course_agent</field>
            <field name="function_name">create_course_agent</field>
            <field name="description">Crew chuyên về khóa học</field>
        </record>
        
        <!-- Class Crew Registry -->
        <record id="crew_registry_class" model="eb_a2a.crew.registry">
            <field name="name">Class Crew</field>
            <field name="code">class</field>
            <field name="module_path">odoo.addons.eb_a2a_lms.agents.class_agent</field>
            <field name="function_name">create_class_agent</field>
            <field name="description">Crew chuyên về lớp học</field>
        </record>
        
        <!-- Instructor Crew Registry -->
        <record id="crew_registry_instructor" model="eb_a2a.crew.registry">
            <field name="name">Instructor Crew</field>
            <field name="code">instructor</field>
            <field name="module_path">odoo.addons.eb_a2a_lms.agents.instructor_agent</field>
            <field name="function_name">create_instructor_agent</field>
            <field name="description">Crew chuyên về giảng viên</field>
        </record>
    </data>
</odoo>
