# -*- coding: utf-8 -*-
{
    "name": "EB A2A LMS Integration",
    "summary": "LMS Implementation for Agent-to-Agent Protocol",
    "description": """
        Implements LMS-specific tools and agents for the A2A protocol:
        - Course tools and agents
        - Instructor tools and agents
        - Class tools and agents
        - LMS-specific RAG implementation
    """,
    "author": "eBill and Vantis Vietnam",
    "website": "https://earnbase.io",
    "category": "Technical/AI",
    "version": "********.0",
    "depends": [
        "base",
        "mail",
        "web",
        "eb_a2a",
        "eb_lms"
    ],
    "data": [
        "data/crew_registry_data.xml",
    ],
    "external_dependencies": {
        "python": ["crewai-tools"],
    },
    "license": "LGPL-3",
    "installable": True,
    "application": False,
    "auto_install": False,
}
