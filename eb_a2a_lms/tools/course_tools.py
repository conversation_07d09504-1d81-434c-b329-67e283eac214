# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and <PERSON>tis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import json
import logging
import re
from typing import Dict, List, Any, Optional, Union

# Import Tool từ eb_a2a hoặc trực tiếp từ crewai
try:
    from odoo.addons.eb_a2a.ai_agents.tools.base_tools import Tool
except ImportError:
    from crewai.tools import tool as Tool

_logger = logging.getLogger(__name__)


class CourseSearchTool:
    """Tool CrewAI để tìm kiếm khóa học."""

    def __init__(self, env):
        """Khởi tạo tool với Odoo environment."""
        self.env = env
        self.tool = Tool(
            name="search_courses",
            description="""Tìm kiếm khóa học dựa trên từ khó<PERSON>, da<PERSON> mụ<PERSON>, c<PERSON><PERSON> đ<PERSON>, v.v.

<PERSON>ham số bắt buộc:
- query: Từ khóa tìm kiếm (chuỗi, ví dụ: "python")
- limit: Số lượng kết quả tối đa (số nguyên, mặc định: 10)

Tham số tùy chọn:
- category: Danh mục khóa học (chuỗi, ví dụ: "lập trình")
- level: Cấp độ khóa học (chuỗi, ví dụ: "beginner")

Ví dụ sử dụng: search_courses(query="python", limit=5, category="lập trình")
            """,
            func=self.execute
        )

    def execute(
        self, query: str, limit: int = 10, category: Optional[str] = None, level: Optional[str] = None, **kwargs
    ) -> Dict[str, Any]:
        """
        Tìm kiếm khóa học dựa trên các tiêu chí.

        Hỗ trợ cả truyền tham số trực tiếp và truyền dưới dạng từ điển.
        """
        # Xử lý trường hợp truyền vào dưới dạng từ điển
        if isinstance(query, dict):
            data = query
            query = data.get('query') or data.get('keyword') or ""
            category = data.get('category') or None
            level = data.get('level') or None
            limit = data.get('limit') or 10
        # Ghi log để debug
        _logger.info(f"CourseSearchTool.execute - query={query}, category={category}, limit={limit}")

        # Đảm bảo limit là số nguyên
        try:
            limit = int(limit)
        except (ValueError, TypeError):
            limit = 10
        try:
            # Sử dụng self.env để truy cập model Odoo
            Course = self.env["eb.course.course"]
            domain = []

            if query:
                domain += [
                    "|",
                    "|",
                    "|",
                    ("name", "ilike", query),
                    ("code", "ilike", query),
                    ("short_description", "ilike", query),
                    ("description", "ilike", query),
                ]

            if category:
                domain.append(("category_id.name", "ilike", category))

            # Trường level không tồn tại trong model eb.course.course
            # if level:
            #     domain.append(("level", "=", level))

            courses = Course.search_read(
                domain,
                [
                    "name",
                    "code",
                    "short_description",
                    "description",
                    "category_id",
                    # "level",  # Trường level không tồn tại
                    "product_id",  # Lấy sản phẩm liên kết để có thông tin về giá và tiền tệ
                ],
                limit=limit,
            )

            # Format kết quả
            formatted_courses = []
            for course in courses:
                formatted_courses.append(
                    {
                        "id": course["id"],
                        "name": course["name"],
                        "code": course.get("code", ""),
                        "short_description": course.get("short_description", ""),
                        # "level": course.get("level", ""),  # Trường level không tồn tại
                        "category": course.get("category_id", [0, ""])[1]
                        if course.get("category_id")
                        else "",
                        # Không lấy giá và tiền tệ trực tiếp từ khóa học vì không có trường này
                        # Thông tin về giá và tiền tệ có thể được lấy từ sản phẩm liên kết nếu cần
                        "product_id": course.get("product_id", [0, ""])[0] if course.get("product_id") else 0,
                    }
                )

            return {
                "courses": formatted_courses,
                "total": len(formatted_courses),
                "query": query,
            }
        except Exception as e:
            _logger.exception(f"Lỗi khi tìm kiếm khóa học: {str(e)}")
            return {
                "error": f"Lỗi khi tìm kiếm khóa học: {str(e)}",
                "courses": [],
                "total": 0,
                "query": query,
            }


class CourseDetailTool:
    """Tool CrewAI để xem chi tiết khóa học."""

    def __init__(self, env):
        """Khởi tạo tool với Odoo environment."""
        self.env = env
        self.tool = Tool(
            name="get_course_details",
            description="""Xem thông tin chi tiết về một khóa học cụ thể

Tham số bắt buộc:
- course_id: ID của khóa học (số nguyên, ví dụ: 1)

Ví dụ sử dụng: get_course_details(course_id=1)
            """,
            func=self.execute
        )

    def execute(self, course_id: Union[int, str, dict]) -> Dict[str, Any]:
        """
        Xem thông tin chi tiết về một khóa học.

        Args:
            course_id: ID của khóa học

        Returns:
            Dict chứa thông tin chi tiết về khóa học
        """
        try:
            # Xử lý trường hợp truyền vào dưới dạng từ điển hoặc chuỗi
            if isinstance(course_id, dict):
                course_id = course_id.get('course_id') or 0
            elif isinstance(course_id, str):
                try:
                    course_id = int(course_id)
                except ValueError:
                    return {
                        "error": f"ID khóa học không hợp lệ: {course_id}",
                        "course": None,
                    }

            # Ghi log để debug
            _logger.info(f"CourseDetailTool.execute - course_id={course_id}")

            # Sử dụng self.env để truy cập model Odoo
            Course = self.env["eb.course.course"]
            course = Course.browse(course_id)

            if not course.exists():
                return {
                    "error": f"Không tìm thấy khóa học với ID {course_id}",
                    "course": None,
                }

            # Lấy thông tin chi tiết
            course_data = {
                "id": course.id,
                "name": course.name,
                "code": course.code or "",
                "short_description": course.short_description or "",
                "description": course.description or "",
                "category": course.category_id.name if course.category_id else "",
                # "level": course.level or "",  # Trường level không tồn tại
                # "price": course.list_price,  # Trường list_price không tồn tại
                # Lấy thông tin tiền tệ từ sản phẩm liên kết
                "currency": course.product_id.currency_id.name if course.product_id and course.product_id.currency_id else "",
                # Lấy giá từ sản phẩm liên kết
                "price": course.product_id.list_price if course.product_id else 0.0,
                # Kiểm tra các trường khác có tồn tại không trước khi truy cập
                "discount_percentage": 0.0,  # Trường này có thể không tồn tại trong model hiện tại
                "certification_available": hasattr(course, 'certification_available') and course.certification_available or False,
                "certification_criteria": hasattr(course, 'certification_criteria') and course.certification_criteria or "",
                # Kiểm tra trường instructor_id có tồn tại không
                "instructor": hasattr(course, 'instructor_id') and course.instructor_id and course.instructor_id.name or "",
                # Kiểm tra trường subject_ids có tồn tại và có dữ liệu không
                "subjects": [
                    {"id": subject.id, "name": subject.name}
                    for subject in (hasattr(course, 'subject_ids') and course.subject_ids or [])
                ],
                # Kiểm tra trường lesson_ids có tồn tại và có dữ liệu không
                "lessons": [
                    {
                        "id": lesson.id,
                        "name": lesson.name,
                        "start_time": lesson.start_time if hasattr(lesson, 'start_time') else None,
                        "end_time": lesson.end_time if hasattr(lesson, 'end_time') else None,
                        "location": (
                            lesson.location_id.name if hasattr(lesson, 'location_id') and lesson.location_id else ""
                        ),
                        "room": lesson.room_id.name if hasattr(lesson, 'room_id') and lesson.room_id else "",
                    }
                    for lesson in (hasattr(course, 'lesson_ids') and course.lesson_ids or [])
                ],
            }

            return {"course": course_data}
        except Exception as e:
            _logger.exception(f"Lỗi khi lấy thông tin chi tiết khóa học: {str(e)}")
            return {
                "error": f"Lỗi khi lấy thông tin chi tiết khóa học: {str(e)}",
                "course": None,
            }


