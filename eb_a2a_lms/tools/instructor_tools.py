# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import json
import logging
import re
from typing import Dict, List, Any, Optional, Union

# Import Tool từ eb_a2a hoặc trực tiếp từ crewai
try:
    from odoo.addons.eb_a2a.ai_agents.tools.base_tools import Tool
    CREWAI_AVAILABLE = True
except ImportError:
    from crewai.tools import tool as Tool
    CREWAI_AVAILABLE = True

_logger = logging.getLogger(__name__)


class InstructorSearchTool:
    """Tool CrewAI để tìm kiếm giảng viên."""

    def __init__(self, env):
        """Khởi tạo tool với Odoo environment."""
        self.env = env
        self.tool = Tool(
            name="search_instructors",
            description="""Tìm kiếm giảng viên dựa trên từ khóa, chuy<PERSON><PERSON> môn, v.v.

<PERSON>ham số bắt buộc:
- query: Từ khóa tìm kiếm (chuỗi, ví dụ: "nguyễn")

Tham số tùy chọn:
- specialization: Chuyên môn của giảng viên (chuỗi, ví dụ: "lập trình")
- limit: Số lượng kết quả tối đa (số nguyên, mặc định: 10)

Ví dụ sử dụng: search_instructors(query="nguyễn", specialization="lập trình", limit=5)
            """,
            func=self.execute
        )

    def execute(
        self, query: Union[str, dict], specialization: Optional[str] = None, limit: int = 10, **kwargs
    ) -> Dict[str, Any]:
        """
        Tìm kiếm giảng viên dựa trên các tiêu chí.

        Hỗ trợ cả truyền tham số trực tiếp và truyền dưới dạng từ điển.
        """
        # Xử lý trường hợp truyền vào dưới dạng từ điển
        if isinstance(query, dict):
            data = query
            query = data.get('query') or data.get('keyword') or ""
            specialization = data.get('specialization') or None
            limit = data.get('limit', 10)

        # Ghi log để debug
        _logger.info(f"InstructorSearchTool.execute - query={query}, specialization={specialization}, limit={limit}")

        # Đảm bảo limit là số nguyên
        try:
            limit = int(limit)
        except (ValueError, TypeError):
            limit = 10

        try:
            # Sử dụng self.env để truy cập model Odoo
            domain = []

            if query:
                domain += [
                    "|",
                    "|",
                    ("name", "ilike", query),
                    ("email", "ilike", query),
                    ("partner_id.function", "ilike", query),
                ]

            if specialization:
                domain.append(("partner_id.function", "ilike", specialization))

            # Tìm kiếm trong model eb.instructor.instructor thay vì res.partner
            instructors = self.env["eb.instructor.instructor"].search_read(
                domain,
                ["name", "partner_id", "email", "phone", "mobile", "image_1920"],
                limit=limit,
            )

            # Format kết quả
            formatted_instructors = []
            for instructor in instructors:
                # Lấy thông tin từ partner_id nếu có
                partner_name = ""
                partner_function = ""
                if instructor.get("partner_id"):
                    partner_id = instructor.get("partner_id")
                    partner_name = partner_id[1] if isinstance(partner_id, (list, tuple)) else ""
                    # Lấy thông tin function từ partner
                    if partner_id and isinstance(partner_id, (list, tuple)) and partner_id[0]:
                        partner = self.env["res.partner"].browse(partner_id[0])
                        partner_function = partner.function or ""

                formatted_instructors.append(
                    {
                        "id": instructor["id"],
                        "name": instructor["name"] or partner_name,
                        "function": partner_function,
                        "email": instructor.get("email", ""),
                        "phone": instructor.get("phone", "") or instructor.get("mobile", ""),
                        "has_image": bool(instructor.get("image_1920")),
                    }
                )

            return {
                "instructors": formatted_instructors,
                "total": len(formatted_instructors),
                "query": query,
            }
        except Exception as e:
            _logger.exception(f"Lỗi khi tìm kiếm giảng viên: {str(e)}")
            return {
                "error": f"Lỗi khi tìm kiếm giảng viên: {str(e)}",
                "instructors": [],
                "total": 0,
                "query": query,
            }


class InstructorDetailTool:
    """Tool CrewAI để xem chi tiết giảng viên."""

    def __init__(self, env):
        """Khởi tạo tool với Odoo environment."""
        self.env = env
        self.tool = Tool(
            name="get_instructor_details",
            description="""Xem thông tin chi tiết về một giảng viên cụ thể

Tham số bắt buộc:
- instructor_id: ID của giảng viên (số nguyên, ví dụ: 1)

Ví dụ sử dụng: get_instructor_details(instructor_id=1)
            """,
            func=self.execute
        )

    def execute(self, instructor_id: Union[int, str, dict]) -> Dict[str, Any]:
        """
        Xem thông tin chi tiết về một giảng viên.

        Args:
            instructor_id: ID của giảng viên

        Returns:
            Dict chứa thông tin chi tiết về giảng viên
        """
        try:
            # Xử lý trường hợp truyền vào dưới dạng từ điển hoặc chuỗi
            if isinstance(instructor_id, dict):
                instructor_id = instructor_id.get('instructor_id') or 0
            elif isinstance(instructor_id, str):
                try:
                    instructor_id = int(instructor_id)
                except ValueError:
                    return {
                        "error": f"ID giảng viên không hợp lệ: {instructor_id}",
                        "instructor": None,
                    }

            # Ghi log để debug
            _logger.info(f"InstructorDetailTool.execute - instructor_id={instructor_id}")

            # Tìm kiếm trong model eb.instructor.instructor
            Instructor = self.env["eb.instructor.instructor"]
            instructor = Instructor.browse(instructor_id)

            if not instructor.exists():
                return {
                    "error": f"Không tìm thấy giảng viên với ID {instructor_id}",
                    "instructor": None,
                }

            # Lấy các khóa học của giảng viên
            courses = self.env["eb.course.course"].search_read(
                [("instructor_id", "=", instructor_id)],
                ["name", "code", "short_description", "level"],
            )

            # Lấy thông tin chi tiết
            partner = instructor.partner_id
            instructor_data = {
                "id": instructor.id,
                "name": instructor.name,
                "function": partner.function or "",
                "email": instructor.email or partner.email or "",
                "phone": instructor.phone or partner.phone or "",
                "mobile": instructor.mobile or partner.mobile or "",
                "website": partner.website or "",
                "comment": partner.comment or "",
                "bio": instructor.bio or "",
                "courses": [
                    {
                        "id": course["id"],
                        "name": course["name"],
                        "code": course.get("code", ""),
                        "short_description": course.get("short_description", ""),
                        "level": course.get("level", ""),
                    }
                    for course in courses
                ],
            }

            return {"instructor": instructor_data}
        except Exception as e:
            _logger.exception(f"Lỗi khi lấy thông tin chi tiết giảng viên: {str(e)}")
            return {
                "error": f"Lỗi khi lấy thông tin chi tiết giảng viên: {str(e)}",
                "instructor": None,
            }


