# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta

# Import Tool từ eb_a2a hoặc trực tiếp từ crewai
try:
    from odoo.addons.eb_a2a.ai_agents.tools.base_tools import Tool

    CREWAI_AVAILABLE = True
except ImportError:
    from crewai.tools import tool as Tool

    CREWAI_AVAILABLE = True

_logger = logging.getLogger(__name__)


class ClassScheduleTool:
    """Tool CrewAI để xem lịch học."""

    def __init__(self, env):
        """Khởi tạo tool với Odoo environment."""
        self.env = env
        self.tool = Tool(
            name="get_class_schedule",
            description="<PERSON>em lịch học của một lớp hoặc khóa học cụ thể",
            func=self.execute,
        )

    def execute(
        self,
        class_id: Optional[int] = None,
        course_id: Optional[int] = None,
        date_from: Optional[str] = None,
        date_to: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Xem lịch học của một lớp hoặc khóa học.

        Args:
            class_id: ID của lớp học (tùy chọn)
            course_id: ID của khóa học (tùy chọn)
            date_from: Ngày bắt đầu (tùy chọn, định dạng YYYY-MM-DD)
            date_to: Ngày kết thúc (tùy chọn, định dạng YYYY-MM-DD)

        Returns:
            Dict chứa lịch học
        """
        try:
            # Sử dụng self.env để truy cập model Odoo
            Lesson = self.env["eb.class.lesson"]
            domain = []

            if class_id:
                domain.append(("class_id", "=", class_id))

            if course_id:
                # Tìm các lớp học thuộc khóa học
                classes = self.env["eb.class.class"].search(
                    [("course_id", "=", course_id)]
                )
                if classes:
                    domain.append(("class_id", "in", classes.ids))
                else:
                    return {
                        "schedule": [],
                        "message": "Không tìm thấy lớp học nào thuộc khóa học này",
                    }

            # Xử lý ngày
            if date_from:
                try:
                    date_from_obj = datetime.strptime(date_from, "%Y-%m-%d")
                    domain.append(("start_time", ">=", date_from_obj))
                except ValueError:
                    return {
                        "error": f"Định dạng ngày không hợp lệ: {date_from}. Sử dụng định dạng YYYY-MM-DD"
                    }

            if date_to:
                try:
                    date_to_obj = datetime.strptime(date_to, "%Y-%m-%d")
                    # Thêm 1 ngày để bao gồm cả ngày kết thúc
                    date_to_obj = date_to_obj + timedelta(days=1)
                    domain.append(("start_time", "<", date_to_obj))
                except ValueError:
                    return {
                        "error": f"Định dạng ngày không hợp lệ: {date_to}. Sử dụng định dạng YYYY-MM-DD"
                    }

            # Nếu không có ngày, lấy lịch học trong 30 ngày tới
            if not date_from and not date_to:
                today = datetime.now()
                domain.append(("start_time", ">=", today))
                domain.append(("start_time", "<", today + timedelta(days=30)))

            # Lấy các buổi học
            lessons = Lesson.search_read(
                domain,
                [
                    "name",
                    "class_id",
                    "start_time",
                    "end_time",
                    "location_id",
                    "room_id",
                    "instructor_id",
                    "state",
                ],
                order="start_time asc",
            )

            # Format kết quả
            formatted_lessons = []
            for lesson in lessons:
                class_name = (
                    lesson.get("class_id", [0, ""])[1] if lesson.get("class_id") else ""
                )
                formatted_lessons.append(
                    {
                        "id": lesson["id"],
                        "name": lesson["name"],
                        "class_name": class_name,
                        "start_time": lesson.get("start_time"),
                        "end_time": lesson.get("end_time"),
                        "location": (
                            lesson.get("location_id", [0, ""])[1]
                            if lesson.get("location_id")
                            else ""
                        ),
                        "room": (
                            lesson.get("room_id", [0, ""])[1]
                            if lesson.get("room_id")
                            else ""
                        ),
                        "instructor": (
                            lesson.get("instructor_id", [0, ""])[1]
                            if lesson.get("instructor_id")
                            else ""
                        ),
                        "state": lesson.get("state", ""),
                    }
                )

            return {"schedule": formatted_lessons}
        except Exception as e:
            _logger.exception(f"Lỗi khi lấy lịch học: {str(e)}")
            return {"error": f"Lỗi khi lấy lịch học: {str(e)}", "schedule": []}


class ClassSearchTool:
    """Tool CrewAI để tìm kiếm lớp học."""

    def __init__(self, env):
        """Khởi tạo tool với Odoo environment."""
        self.env = env
        self.tool = Tool(
            name="search_classes",
            description="""Tìm kiếm lớp học dựa trên từ khóa, khóa học, v.v.

Tham số bắt buộc:
- query: Từ khóa tìm kiếm (chuỗi, ví dụ: "python")

Tham số tùy chọn:
- course_id: ID của khóa học (số nguyên, ví dụ: 1)
- active_only: Chỉ tìm các lớp đang hoạt động (boolean, mặc định: True)
- limit: Số lượng kết quả tối đa (số nguyên, mặc định: 10)

Ví dụ sử dụng: search_classes(query="python", course_id=1, limit=5)
            """,
            func=self.execute,
        )

    def execute(
        self,
        query: Union[str, dict],
        course_id: Optional[Union[int, str]] = None,
        active_only: bool = True,
        limit: int = 10,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Tìm kiếm lớp học dựa trên các tiêu chí.

        Hỗ trợ cả truyền tham số trực tiếp và truyền dưới dạng từ điển.
        """
        # Xử lý trường hợp truyền vào dưới dạng từ điển
        if isinstance(query, dict):
            data = query
            query = data.get('query') or data.get('keyword') or ""
            course_id = data.get('course_id')
            active_only = data.get('active_only', True)
            limit = data.get('limit', 10)

        # Ghi log để debug
        _logger.info(f"ClassSearchTool.execute - query={query}, course_id={course_id}, limit={limit}")

        # Đảm bảo limit là số nguyên
        try:
            limit = int(limit)
        except (ValueError, TypeError):
            limit = 10

        try:
            # Sử dụng self.env để truy cập model Odoo
            Class = self.env["eb.class.class"]
            domain = []

            if query:
                domain += [
                    "|",
                    "|",
                    ("name", "ilike", query),
                    ("code", "ilike", query),
                    ("description", "ilike", query),
                ]

            # Xử lý course_id
            if course_id:
                # Nếu course_id là chuỗi, thử chuyển đổi thành số nguyên
                if isinstance(course_id, str) and course_id.strip():
                    try:
                        course_id = int(course_id)
                        domain.append(("course_id", "=", course_id))
                    except ValueError:
                        _logger.warning(f"Không thể chuyển đổi course_id '{course_id}' thành số nguyên")
                # Nếu course_id là số nguyên, thêm vào domain
                elif isinstance(course_id, int) and course_id > 0:
                    domain.append(("course_id", "=", course_id))

            if active_only:
                domain.append(("active", "=", True))

            classes = Class.search_read(
                domain,
                [
                    "name",
                    "code",
                    "course_id",
                    "start_date",
                    "end_date",
                    "stage_id",  # Sử dụng stage_id thay vì state
                    "max_students",  # Sử dụng max_students thay vì max_seats
                    "min_students",  # Sử dụng min_students thay vì min_seats
                    "primary_instructor_id",  # Sử dụng primary_instructor_id thay vì instructor_id
                ],
                limit=limit,
            )

            # Format kết quả
            formatted_classes = []
            for cls in classes:
                formatted_classes.append(
                    {
                        "id": cls["id"],
                        "name": cls["name"],
                        "code": cls.get("code", ""),
                        "course": (
                            cls.get("course_id", [0, ""])[1]
                            if cls.get("course_id")
                            else ""
                        ),
                        "start_date": cls.get("start_date"),
                        "end_date": cls.get("end_date"),
                        "state": cls.get("stage_id", [0, ""])[1] if cls.get("stage_id") else "",
                        "max_seats": cls.get("max_students", 0),
                        "min_seats": cls.get("min_students", 0),
                        "instructor": (
                            cls.get("primary_instructor_id", [0, ""])[1]
                            if cls.get("primary_instructor_id")
                            else ""
                        ),
                    }
                )

            return {
                "classes": formatted_classes,
                "total": len(formatted_classes),
                "query": query,
            }
        except Exception as e:
            _logger.exception(f"Lỗi khi tìm kiếm lớp học: {str(e)}")
            return {
                "error": f"Lỗi khi tìm kiếm lớp học: {str(e)}",
                "classes": [],
                "total": 0,
                "query": query,
            }
