# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging
from typing import Dict, List, Any, Optional, Union

try:
    from crewai import Crew, Process, Task, Agent
    CREWAI_AVAILABLE = True
except ImportError:
    from odoo.addons.eb_a2a.ai_agents.crews.base_crews import Crew, Process, Task, Agent, CREWAI_AVAILABLE
from ..agents.course_agent import create_course_agent
from ..agents.instructor_agent import create_instructor_agent
from ..agents.class_agent import create_class_agent
from ..agents.manager_agent import create_manager_agent

_logger = logging.getLogger(__name__)

def create_lms_crew(env, llm=None, process_type='sequential'):
    """Tạo LMS Crew với các CrewAI Agents.

    Args:
        env: Odoo environment
        llm: Language Model (tùy chọn)
        process_type: <PERSON><PERSON><PERSON> quy trình (sequential hoặc hierarchical, mặc định: sequential)

    Returns:
        Crew: CrewAI Crew
    """
    try:
        if not CREWAI_AVAILABLE:
            _logger.warning("CrewAI không khả dụng. Không thể tạo LMS Crew.")
            return None

        # Tạo các agent
        course_agent = create_course_agent(env, llm=llm)
        instructor_agent = create_instructor_agent(env, llm=llm)
        class_agent = create_class_agent(env, llm=llm)

        # Tạo manager agent nếu sử dụng quy trình phân cấp
        manager_agent = None
        if process_type == 'hierarchical':
            manager_agent = create_manager_agent(env, llm=llm)
            if not manager_agent:
                _logger.warning("Không thể tạo manager agent. Sử dụng quy trình tuần tự thay thế.")
                process_type = 'sequential'

        # Tạo các task
        course_task = Task(
            description="Tìm kiếm và cung cấp thông tin chi tiết về khóa học theo yêu cầu của người dùng",
            expected_output="Danh sách khóa học phù hợp với yêu cầu, bao gồm tên, mô tả, giá, và các thông tin khác",
            agent=course_agent
        )

        instructor_task = Task(
            description="Tìm kiếm và cung cấp thông tin chi tiết về giảng viên theo yêu cầu của người dùng",
            expected_output="Thông tin chi tiết về giảng viên, bao gồm tên, chuyên môn, và các khóa học đang dạy",
            agent=instructor_agent
        )

        class_task = Task(
            description="Tìm kiếm lớp học và cung cấp lịch học theo yêu cầu của người dùng",
            expected_output="Danh sách lớp học và lịch học phù hợp với yêu cầu",
            agent=class_agent
        )

        # Tạo Crew
        crew_args = {
            'agents': [course_agent, instructor_agent, class_agent],
            'tasks': [course_task, instructor_task, class_task],
            'process': Process.hierarchical if process_type == 'hierarchical' else Process.sequential,
            'verbose': True
        }

        # Thêm manager_agent nếu sử dụng quy trình phân cấp
        if process_type == 'hierarchical' and manager_agent:
            crew_args['manager_agent'] = manager_agent
            _logger.info("Sử dụng quy trình phân cấp với manager agent")
        else:
            _logger.info("Sử dụng quy trình tuần tự")

        crew = Crew(**crew_args)

        return crew
    except Exception as e:
        _logger.exception(f"Lỗi khi tạo LMS Crew: {str(e)}")
        return None
