# EB A2A LMS Integration

Module tích hợp LMS cho Agent-to-Agent (A2A) Protocol trong Odoo.

## Giới thiệu

EB A2A LMS Integration là module mở rộng cho module EB A2A Integration, cung cấp các công cụ và agent cụ thể cho hệ thống quản lý học tập (LMS). Module này cho phép bạn sử dụng các agent AI thông minh để tương tác với dữ liệu LMS trong Odoo.

## Tính năng

- **Course Tools**: Công cụ để tìm kiếm và xem thông tin chi tiết về khóa học
- **Instructor Tools**: Công cụ để tìm kiếm và xem thông tin chi tiết về giảng viên
- **Class Tools**: Công cụ để tìm kiếm lớp học và xem lịch học
- **LMS Agents**: Các agent chuy<PERSON><PERSON> bi<PERSON><PERSON> cho kh<PERSON><PERSON> họ<PERSON>, gi<PERSON><PERSON> viên và lớp học
- **Multi-Agent System**: <PERSON><PERSON> thống multi-agent để xử lý các yêu cầu phức tạp

## Cài đặt

1. Cài đặt module eb_a2a
2. Cài đặt module eb_a2a_lms
3. Cài đặt các thư viện Python cần thiết:
   ```
   pip install google-adk openai
   ```

## Sử dụng

### 1. Sử dụng Multi-Agent

```python
from odoo.addons.eb_a2a_lms.agents.lms_multi_agent import create_lms_multi_agent
from google.adk import Message

# Tạo Multi-Agent
multi_agent = create_lms_multi_agent(env)

# Tạo tin nhắn
message = Message(role="user", content="Tôi muốn tìm khóa học về Python")

# Gọi Multi-Agent để xử lý tin nhắn
response = multi_agent.generate_content(message)

# In phản hồi
print(response.content)
```

### 2. Sử dụng các Agent riêng lẻ

```python
from odoo.addons.eb_a2a_lms.agents.course_agent import create_course_agent
from google.adk import Message

# Tạo Course Agent
course_agent = create_course_agent(env)

# Tạo tin nhắn
message = Message(role="user", content="Tôi muốn tìm khóa học về Python")

# Gọi Course Agent để xử lý tin nhắn
response = course_agent.generate_content(message)

# In phản hồi
print(response.content)
```

## Tùy chỉnh

### 1. Thêm Tool mới

Để thêm tool mới, bạn cần:
1. Tạo class tool mới trong thư mục `tools/`
2. Đăng ký tool với agent trong thư mục `agents/`

### 2. Thêm Agent mới

Để thêm agent mới, bạn cần:
1. Tạo file prompt mới trong thư mục `prompts/`
2. Tạo file agent mới trong thư mục `agents/`
3. Cập nhật multi-agent để sử dụng agent mới

## Liên hệ

Nếu bạn có bất kỳ câu hỏi hoặc góp ý nào, vui lòng liên hệ với chúng tôi tại [<EMAIL>](mailto:<EMAIL>).
