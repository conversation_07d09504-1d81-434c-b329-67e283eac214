#!/bin/bash

# Force Clean Build Script
# This script completely cleans the project and rebuilds it

echo "🧹 Starting FORCE CLEAN BUILD process..."

# Navigate to project directory
cd "$(dirname "$0")/.."

echo "📍 Current directory: $(pwd)"

# 1. Clean Xcode build cache
echo "🧹 Cleaning Xcode build cache..."
xcodebuild clean -project VantisInstructor.xcodeproj -scheme VantisInstructor

# 2. Remove derived data
echo "🧹 Removing derived data..."
rm -rf ~/Library/Developer/Xcode/DerivedData/VantisInstructor-*

# 3. Remove build folder
echo "🧹 Removing build folder..."
rm -rf build/

# 4. Clean iOS Simulator
echo "🧹 Cleaning iOS Simulator..."
xcrun simctl shutdown all
xcrun simctl erase all

# 5. Reset iOS Simulator to clean state
echo "🧹 Resetting iOS Simulator..."
xcrun simctl boot "iPhone 16 Pro" || echo "⚠️ Could not boot iPhone 16 Pro simulator"

# 6. Build the project
echo "🔨 Building project..."
xcodebuild build -project VantisInstructor.xcodeproj -scheme VantisInstructor -destination 'platform=iOS Simulator,name=iPhone 16 Pro'

if [ $? -eq 0 ]; then
    echo "✅ FORCE CLEAN BUILD completed successfully!"
    echo ""
    echo "🎯 Next steps:"
    echo "1. Open Xcode and run the app"
    echo "2. Test login/logout functionality"
    echo "3. Verify no auto-signin occurs"
    echo ""
    echo "🧪 To test auto-signin prevention:"
    echo "1. Login with credentials"
    echo "2. Logout completely"
    echo "3. Force close app (swipe up from bottom)"
    echo "4. Reopen app"
    echo "5. Should show empty login screen"
else
    echo "❌ FORCE CLEAN BUILD failed!"
    echo "Check the error messages above"
    exit 1
fi
