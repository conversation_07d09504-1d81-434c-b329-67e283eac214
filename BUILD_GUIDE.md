# Mobile App Template iOS - Build & Run Guide

## 📱 Overview
Mobile App Template iOS is a generic mobile application template built with SwiftUI using Clean Architecture patterns.

## 🛠 System Requirements
- **Xcode**: 15.0 or later
- **iOS**: 17.0 or later
- **macOS**: 13.0 or later (to run Xcode)
- **Swift**: 5.9 or later

## 🚀 Build & Run Guide

### 1. Open project
```bash
cd /path/to/your/VantisInstructor
open VantisInstructor.xcodeproj
```

### 2. Select target and device
- In Xcode, select scheme: `VantisInstructor`
- Choose simulator: `iPhone 16 Pro` or real iOS device

### 3. Build project
**Option A: Sử dụng Xcode**
- Nhấn `Cmd + B` để build
- Hoặc menu `Product > Build`

**Option B: Sử dụng command line**
```bash
# Build cho simulator
xcodebuild -scheme linkx-mobile-ios -destination 'platform=iOS Simulator,name=iPhone 16 Pro' build

# Build cho device (cần provisioning profile)
xcodebuild -scheme linkx-mobile-ios -destination 'platform=iOS,name=Your iPhone' build
```

### 4. Run app
**Option A: Sử dụng Xcode**
- Nhấn `Cmd + R` để run
- Hoặc menu `Product > Run`

**Option B: Sử dụng command line**
```bash
# Run trên simulator
xcodebuild -scheme linkx-mobile-ios -destination 'platform=iOS Simulator,name=iPhone 16 Pro' run
```

## 🔧 Troubleshooting - Các lỗi thường gặp

### 1. Lỗi Keychain Constants
**Lỗi**: `cannot find 'errSecUserCancel' in scope`

**Fix**: Trong file `Core/Storage/KeychainManager.swift`, thay thế:
```swift
// Thay thế dòng này:
case errSecUserCancel:
    continuation.resume(throwing: KeychainError.userCancel)

case errSecBiometryNotAvailable:
    continuation.resume(throwing: KeychainError.authenticationFailed)

// Bằng:
case errSecAuthFailed:
    continuation.resume(throwing: KeychainError.biometricLockout)
```

### 2. Lỗi NetworkError
**Lỗi**: `NetworkError does not conform to 'Error'`

**Fix**: Trong file `Core/Network/NetworkError.swift`, đảm bảo:
```swift
enum NetworkError: Error {
    case invalidURL
    case noData
    case decodingError(Error)
    case serverError(Int, String)
    case unauthorized
    case forbidden
    case notFound
    case timeout
    case noInternetConnection
}
```

### 3. Lỗi TokenManager
**Lỗi**: `getUser()` method issues

**Fix**: Trong file `Core/Storage/TokenManager.swift`, đảm bảo method signature:
```swift
func getUser<T: Codable>(_ type: T.Type) -> T? {
    // implementation
}
```

### 4. Lỗi AppConfiguration
**Lỗi**: `[String: Any]` không Codable

**Fix**: Trong file `Core/Services/ConfigurationService.swift`, remove `features` property:
```swift
struct AppConfiguration: Codable {
    let apiBaseURL: String
    let apiVersion: String
    let environment: String
    let enableLogging: Bool
    let enableAnalytics: Bool
    let enableCrashReporting: Bool
    let maxRetryAttempts: Int
    let requestTimeout: TimeInterval
    // Remove: let features: [String: Any]
}
```

## 📁 Cấu trúc project

```
linkx-mobile-ios/
├── Core/
│   ├── Network/          # API client, endpoints
│   ├── Services/         # Business services
│   ├── Storage/          # Keychain, UserDefaults
│   └── Utils/           # Utilities, constants
├── Domain/
│   ├── Models/          # Data models
│   └── Repositories/    # Repository interfaces
├── Presentation/
│   ├── Authentication/ # Login, register views
│   ├── Common/         # Shared components
│   ├── Home/           # Home screen
│   ├── Profile/        # Profile screens
│   ├── Rewards/        # Rewards screens
│   ├── Transactions/   # Transaction screens
│   └── Wallet/         # Wallet screens
├── Assets.xcassets     # Images, colors
└── Info.plist         # App configuration
```

## 🎯 Các tính năng chính

### Authentication
- Login/Register với email/password
- Biometric authentication (Face ID/Touch ID)
- Forgot password
- Auto-login với stored tokens

### Rewards System
- Browse available rewards
- Redeem rewards với points
- View redemption history
- Search và filter rewards

### Wallet Management
- View token balance
- Send/receive tokens
- Transaction history
- QR code scanning

### Profile Management
- Edit profile information
- Security settings
- Support và help
- App settings

## 🔐 Security Features

### Keychain Integration
- Secure token storage
- Biometric authentication
- Auto-logout on security events

### Network Security
- SSL pinning (nếu cần)
- Request/response encryption
- Token refresh mechanism

## 📱 Supported iOS Versions
- **Minimum**: iOS 17.0
- **Target**: iOS 18.0
- **Tested on**: iPhone 16 Pro, iPhone 15, iPad Pro

## 🚨 Known Issues

1. **Keychain constants**: Một số constants có thể không tồn tại trên iOS versions cũ
2. **Biometric authentication**: Cần test trên device thật
3. **Network timeouts**: Có thể cần adjust timeout values

## 📞 Support

Nếu gặp vấn đề khi build/run:

1. **Clean build folder**: `Cmd + Shift + K`
2. **Reset simulator**: Device > Erase All Content and Settings
3. **Restart Xcode**: Đôi khi Xcode cần restart
4. **Check iOS version**: Đảm bảo target iOS version phù hợp

## 🎉 Success Indicators

Khi build thành công, bạn sẽ thấy:
- ✅ Build Succeeded message
- 📱 App launch trên simulator/device
- 🔐 Login screen hiển thị
- 🎨 UI components render đúng

---

**Happy Coding! 🚀**
