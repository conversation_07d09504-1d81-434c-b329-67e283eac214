---
description: Coding Guideline Odoo 18
globs: 
alwaysApply: true
---
1. <PERSON><PERSON> Override hàm create

hãy dùng @api.model_create_multi

block 

```
<div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers"/>
                    <field name="activity_ids" widget="mail_activity"/>
                    <field name="message_ids" widget="mail_thread"/>
                </div>

``` 

đã không còn hỗ trợ ở Odoo 18, thay vào đó hãy dùng `<chatter />`


2. Đã sử dụng list thay vì tree trong các view
3. Không sử dụng attrs trong views (đã loại bỏ trong Odoo 18)
4. Không sử dụng states trong views (đã loại bỏ trong Odoo 18)