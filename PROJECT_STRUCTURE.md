# Project Structure Documentation

This document provides a comprehensive overview of the EB LMS Vantis Instructor App project structure, architecture, and organization.

## 🏗️ Architecture Overview

The project follows **Clean Architecture** principles with clear separation of concerns across three main layers:

```
┌─────────────────────────────────────────────────────────┐
│                    Presentation Layer                    │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │
│  │    Views    │ │ ViewModels  │ │    UI Components    │ │
│  └─────────────┘ └─────────────┘ └─────────────────────┘ │
└─────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────┐
│                     Domain Layer                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │
│  │   Models    │ │ Repositories│ │   Business Logic    │ │
│  └─────────────┘ └─────────────┘ └─────────────────────┘ │
└─────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────┐
│                      Core Layer                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │
│  │   Network   │ │   Storage   │ │     Services        │ │
│  └─────────────┘ └─────────────┘ └─────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

## 📁 Complete Directory Structure

### Root Level
```
eb-lms-vantis-instructor-app/
├── VantisInstructor/              # Main app source code
├── VantisInstructor.xcodeproj/    # Xcode project file
├── VantisInstructorTests/         # Unit tests
├── VantisInstructorUITests/       # UI tests
├── Documentation/                 # Project documentation
├── Examples/                      # Code examples
├── Features/                      # Feature specifications
├── Scripts/                       # Build and utility scripts
├── quiz-guideline/               # Quiz feature guidelines
├── Core/                         # Shared core components
├── build/                        # Build artifacts
└── Various config files (.md, .swift, .sh)
```

### Core Layer (`VantisInstructor/Core/`)
Infrastructure and framework-specific code.

```
Core/
├── Network/                      # Networking layer
│   ├── APIClient.swift          # HTTP client with retry logic
│   ├── APIConfiguration.swift   # API configuration settings
│   └── APIEndpoints.swift       # API endpoint definitions
├── Services/                     # Core business services
│   ├── AnalyticsService.swift   # Event tracking and analytics
│   ├── AssignmentManager.swift  # Assignment management
│   ├── AttendanceManager.swift  # Attendance tracking
│   ├── ClassManager.swift       # Class management
│   ├── ConfigurationService.swift # Remote configuration
│   ├── LoginService.swift       # Authentication service
│   ├── StudentManager.swift     # Student management
│   └── SyncService.swift        # Background synchronization
├── Storage/                      # Data persistence
│   ├── KeychainManager.swift    # Secure storage (tokens, credentials)
│   └── DataCleanupManager.swift # Data cleanup and management
└── Utils/                        # Utilities and helpers
    ├── AppConstants.swift       # App-wide constants and configuration
    ├── ErrorHandling.swift      # Error management and handling
    ├── Extensions.swift         # Swift extensions
    ├── FontExtensions.swift     # Font-related extensions
    └── Logger.swift             # Logging system
```

### Domain Layer (`VantisInstructor/Domain/`)
Business logic and entities, independent of frameworks.

```
Domain/
├── Models/                       # Data models and entities
│   ├── Assignment.swift         # Assignment entity
│   ├── AssignmentSubmission.swift # Assignment submission model
│   ├── Attendance.swift         # Attendance tracking model
│   ├── Business.swift           # Business/merchant model
│   ├── Class.swift              # Class entity
│   ├── Course.swift             # Course model
│   ├── Lesson.swift             # Lesson entity
│   ├── LoginModels.swift        # Authentication models
│   ├── Mission.swift            # Gamification mission model
│   ├── Notification.swift       # Notification model
│   ├── Quiz.swift               # Quiz entity
│   ├── QuizAttempt.swift        # Quiz attempt model
│   ├── QuizRequests.swift       # Quiz API request models
│   ├── Student.swift            # Student entity
│   ├── Transaction.swift        # Transaction/payment model
│   └── User.swift               # User entity with roles
└── Repositories/                 # Repository interfaces
    ├── AuthRepository.swift     # Authentication contracts
    ├── GradingRepository.swift  # Grading system contracts
    ├── LessonRepository.swift   # Lesson management contracts
    ├── MissionRepository.swift  # Mission system contracts
    ├── QuizRepository.swift     # Quiz management contracts
    └── TransactionRepository.swift # Transaction contracts
```

### Presentation Layer (`VantisInstructor/Presentation/`)
UI components, views, and view models organized by feature.

```
Presentation/
├── Admin/                        # Admin-specific screens
│   ├── Components/
│   │   └── AdminComponents.swift
│   ├── ViewModels/
│   │   └── AdminViewModel.swift
│   └── Views/
│       ├── AdminDashboardView.swift
│       └── AdminPlaceholderViews.swift
├── Assignments/                  # Assignment management
│   ├── Components/
│   │   └── AssignmentListComponents.swift
│   └── Views/
│       ├── AssignmentListView.swift
│       ├── CreateAssignmentView.swift
│       ├── GradingView.swift
│       └── SimpleAssignmentView.swift
├── Authentication/               # Auth flow screens
│   ├── ViewModels/
│   │   └── AuthViewModel.swift
│   └── Views/
│       ├── ForgotPasswordView.swift
│       ├── LoginView.swift
│       └── RegisterView.swift
├── Business/                     # Business-specific screens
│   ├── Components/
│   │   └── BusinessComponents.swift
│   ├── ViewModels/
│   │   └── MerchantViewModel.swift
│   └── Views/
│       ├── MerchantDashboardView.swift
│       └── MerchantPlaceholderViews.swift
├── Businesses/                   # Business management
│   ├── ViewModels/
│   │   └── BusinessesViewModel.swift
│   └── Views/
│       ├── BusinessDetailView.swift
│       ├── BusinessesView.swift
│       ├── MerchantSearchView.swift
│       └── MerchantsView.swift
├── Classes/                      # Class management
│   ├── Components/
│   │   ├── ClassFilterComponents.swift
│   │   └── ClassListComponents.swift
│   └── Views/
│       ├── ClassDetailView.swift
│       └── ClassListView.swift
├── Common/                       # Shared UI components
│   ├── Components/
│   │   ├── BadgeView.swift
│   │   ├── ClassCard.swift
│   │   ├── InstructorQuickActionCard.swift
│   │   ├── ModernTabBar.swift
│   │   ├── QRScannerView.swift
│   │   └── TransactionRowView.swift
│   └── Views/
│       ├── AdminPlaceholderView.swift
│       ├── EmptyStateView.swift
│       ├── FontTestView.swift
│       ├── ModernEmptyStateView.swift
│       ├── RoleBasedTabView.swift
│       └── SkeletonLoadingView.swift
├── Grading/                      # Grading system
│   ├── ViewModels/
│   │   └── GradingViewModel.swift
│   └── Views/
│       ├── AnswerGradingCard.swift
│       ├── GradingDetailView.swift
│       ├── PendingAttemptRowView.swift
│       └── PendingGradingView.swift
├── Home/                         # Home screen
│   ├── ViewModels/
│   │   └── InstructorHomeViewModel.swift
│   └── Views/
│       └── HomeView.swift
├── Missions/                     # Gamification system
│   ├── ViewModels/
│   │   └── MissionViewModel.swift
│   └── Views/
│       ├── BadgeCollectionView.swift
│       ├── MissionCard.swift
│       ├── MissionDetailView.swift
│       ├── MissionFiltersView.swift
│       ├── MissionLeaderboardView.swift
│       ├── MissionsView.swift
│       └── RewardAnimationView.swift
├── Notifications/                # Notification system
│   ├── ViewModels/
│   │   └── NotificationsViewModel.swift
│   └── Views/
│       ├── NotificationRowView.swift
│       ├── NotificationSettingsView.swift
│       ├── NotificationsView.swift
│       └── NotificationsViewPreview.swift
├── Profile/                      # User profile screens
│   └── Views/
│       ├── AboutView.swift
│       ├── EditProfileView.swift
│       ├── ProfileView.swift
│       ├── SecuritySettingsView.swift
│       ├── SettingsView.swift
│       └── SupportView.swift
├── Quiz/                         # Quiz management system
│   ├── ViewModels/
│   │   ├── LessonAssignmentViewModel.swift
│   │   ├── QuestionBuilder.swift
│   │   ├── QuizCreationViewModel.swift
│   │   ├── QuizDetailViewModel.swift
│   │   └── QuizManagementViewModel.swift
│   └── Views/
│       ├── Assignment/           # Quiz assignment to lessons
│       │   ├── Components/
│       │   │   ├── AssignmentConfigurationStepView.swift
│       │   │   ├── AssignmentPreviewStepView.swift
│       │   │   └── LessonSelectionStepView.swift
│       │   └── LessonAssignmentView.swift
│       ├── Creation/             # Quiz creation workflow
│       │   ├── Components/
│       │   │   ├── FormSectionView.swift
│       │   │   └── QuestionBuilderView.swift
│       │   ├── BasicInfoStepView.swift
│       │   ├── CreateQuizView.swift
│       │   ├── PreviewStepView.swift
│       │   ├── QuestionsStepView.swift
│       │   └── SettingsStepView.swift
│       ├── Detail/               # Quiz detail and analytics
│       │   ├── Components/
│       │   │   ├── PublicationStatusView.swift
│       │   │   ├── QuizAnalyticsSection.swift
│       │   │   ├── QuizAttemptsSection.swift
│       │   │   ├── QuizOverviewSection.swift
│       │   │   └── QuizQuestionsSection.swift
│       │   └── QuizDetailView.swift
│       ├── QuizFiltersView.swift
│       ├── QuizManagementView.swift
│       └── QuizRowView.swift
└── Transactions/                 # Transaction history
    └── Views/
        ├── TransactionDetailView.swift
        └── TransactionHistoryView.swift
```

### Additional Resources
```
VantisInstructor/
├── Assets.xcassets/              # App icons and images
│   ├── AccentColor.colorset/
│   ├── AppIcon.appiconset/
│   └── Contents.json
├── Fonts/                        # Custom fonts
│   ├── BeVietnamPro-Bold.ttf
│   ├── BeVietnamPro-Medium.ttf
│   ├── BeVietnamPro-Regular.ttf
│   └── BeVietnamPro-SemiBold.ttf
├── Preview Content/              # SwiftUI preview assets
│   └── Preview Assets.xcassets/
├── Services/                     # Additional services
│   └── NotificationService.swift
├── VantisInstructor.entitlements    # App capabilities
└── MobileApp.swift              # App entry point
```

## 🎯 Key Features by Module

### 🏠 Home Module
- **Instructor Dashboard**: Overview of classes, assignments, and quick actions
- **Quick Stats**: Attendance rates, pending assignments, class counts
- **Today's Classes**: Compact view of daily schedule
- **Quiz Management**: Direct access to quiz creation and management

### 📚 Quiz System
- **Quiz Creation**: Multi-step wizard for creating quizzes
- **Question Builder**: Support for multiple question types
- **Quiz Management**: List, filter, and manage existing quizzes
- **Analytics**: Detailed performance analytics and reporting
- **Lesson Assignment**: Assign quizzes to specific lessons
- **Grading System**: Manual and automatic grading capabilities

### 👥 Class Management
- **Class Scheduling**: View and manage class schedules
- **Attendance Tracking**: Digital attendance management
- **Student Management**: Student roster and information
- **Class Analytics**: Performance and attendance analytics

### 📝 Assignment System
- **Assignment Creation**: Create and manage assignments
- **Submission Tracking**: Monitor student submissions
- **Grading Workflow**: Streamlined grading process
- **Due Date Management**: Track assignment deadlines

### 🎮 Gamification (Missions)
- **Mission System**: Gamified learning objectives
- **Badge Collection**: Achievement system
- **Leaderboards**: Student ranking and competition
- **Reward Animations**: Engaging feedback system

### 🔔 Notifications
- **Push Notifications**: Real-time updates
- **In-App Notifications**: System notifications
- **Notification Settings**: Customizable preferences
- **Badge Management**: Unread count tracking

### 👤 User Management
- **Role-Based Access**: Admin, Business, Instructor roles
- **Profile Management**: User profile editing
- **Security Settings**: Password and security options
- **Authentication**: Login, registration, password recovery

## 🔧 Technical Architecture

### Design Patterns
- **MVVM**: Model-View-ViewModel architecture
- **Repository Pattern**: Data access abstraction
- **Dependency Injection**: Service injection and management
- **Observer Pattern**: Reactive programming with Combine

### Key Technologies
- **SwiftUI**: Modern declarative UI framework
- **Combine**: Reactive programming framework
- **Swift Concurrency**: Async/await for asynchronous operations
- **Core Data**: Local data persistence (if needed)
- **Keychain**: Secure credential storage

### Navigation
- **Role-Based Navigation**: Different tab structures per user role
- **Modern Tab Bar**: Custom animated tab bar
- **Deep Linking**: Support for deep link navigation
- **Sheet Presentations**: Modal presentations for forms

### State Management
- **@StateObject**: ViewModel lifecycle management
- **@Published**: Reactive state updates
- **@EnvironmentObject**: Shared state across views
- **UserDefaults**: Simple preference storage

## 🚀 Development Guidelines

### Code Organization
- **Feature-Based Structure**: Organize by business features
- **Layer Separation**: Clear boundaries between layers
- **Component Reusability**: Shared components in Common/
- **Consistent Naming**: Follow Swift naming conventions

### Best Practices
- **Clean Architecture**: Maintain layer separation
- **SOLID Principles**: Follow object-oriented design principles
- **Protocol-Oriented**: Use protocols for abstraction
- **Error Handling**: Comprehensive error management
- **Testing**: Unit tests for business logic
- **Documentation**: Code comments and README files

### Performance Considerations
- **Lazy Loading**: Load data on demand
- **Memory Management**: Proper cleanup and weak references
- **Network Optimization**: Caching and request deduplication
- **UI Performance**: Efficient SwiftUI view updates

---

*This document is maintained to reflect the current project structure. Last updated: January 2025*