//
//  CheckinTests.swift
//  mobile-app-template
//
//  Created by Instru<PERSON> <PERSON>pp on 29/7/25.
//

import XCTest
import CoreLocation
@testable import mobile_app_template

class CheckinTests: XCTestCase {
    
    var checkinService: InstructorCheckinService!
    var viewModel: LessonCheckinViewModel!
    
    override func setUp() {
        super.setUp()
        checkinService = InstructorCheckinService()
        viewModel = LessonCheckinViewModel(checkinService: checkinService)
    }
    
    override func tearDown() {
        checkinService = nil
        viewModel = nil
        super.tearDown()
    }
    
    // MARK: - Service Tests
    
    func testCheckinRequestCreation() {
        // Given
        let location = CLLocation(latitude: 21.0285, longitude: 105.8542) // Hanoi coordinates
        let notes = "Test checkin"
        
        // When
        let request = InstructorCheckInRequest(
            locationLat: location.coordinate.latitude,
            locationLng: location.coordinate.longitude,
            deviceInfo: "iPhone - iOS 17.0",
            notes: notes
        )
        
        // Then
        XCTAssertEqual(request.locationLat, 21.0285)
        XCTAssertEqual(request.locationLng, 105.8542)
        XCTAssertEqual(request.notes, notes)
        XCTAssertNotNil(request.deviceInfo)
    }
    
    func testCheckinResponseDecoding() throws {
        // Given
        let jsonString = """
        {
            "success": true,
            "message": "Check-in thành công!",
            "data": {},
            "meta": {}
        }
        """
        let jsonData = jsonString.data(using: .utf8)!
        
        // When
        let response = try JSONDecoder().decode(InstructorCheckInResponse.self, from: jsonData)
        
        // Then
        XCTAssertTrue(response.success)
        XCTAssertEqual(response.message, "Check-in thành công!")
        XCTAssertNotNil(response.data)
        XCTAssertNotNil(response.meta)
    }
    
    func testCheckinErrorResponseDecoding() throws {
        // Given
        let jsonString = """
        {
            "success": false,
            "message": "Không thể check-in. Vui lòng thử lại.",
            "data": {},
            "meta": {}
        }
        """
        let jsonData = jsonString.data(using: .utf8)!
        
        // When
        let response = try JSONDecoder().decode(InstructorCheckInResponse.self, from: jsonData)
        
        // Then
        XCTAssertFalse(response.success)
        XCTAssertEqual(response.message, "Không thể check-in. Vui lòng thử lại.")
    }
    
    // MARK: - ViewModel Tests
    
    @MainActor
    func testViewModelInitialState() {
        // Then
        XCTAssertFalse(viewModel.isLoading)
        XCTAssertFalse(viewModel.showingResultBottomSheet)
        XCTAssertEqual(viewModel.resultMessage, "")
        XCTAssertFalse(viewModel.isSuccess)
    }
    
    @MainActor
    func testViewModelDismissResult() {
        // Given
        viewModel.isSuccess = true
        viewModel.resultMessage = "Test message"
        viewModel.showingResultBottomSheet = true
        
        // When
        viewModel.dismissResult()
        
        // Then
        XCTAssertFalse(viewModel.showingResultBottomSheet)
        XCTAssertEqual(viewModel.resultMessage, "")
        XCTAssertFalse(viewModel.isSuccess)
    }
    
    @MainActor
    func testViewModelResetState() {
        // Given
        viewModel.isLoading = true
        viewModel.isSuccess = true
        viewModel.resultMessage = "Test message"
        viewModel.showingResultBottomSheet = true
        
        // When
        viewModel.resetState()
        
        // Then
        XCTAssertFalse(viewModel.isLoading)
        XCTAssertFalse(viewModel.showingResultBottomSheet)
        XCTAssertEqual(viewModel.resultMessage, "")
        XCTAssertFalse(viewModel.isSuccess)
    }
    
    // MARK: - Error Handling Tests
    
    func testCheckinErrorTypes() {
        // Test different error types
        let invalidLessonError = CheckinError.invalidLessonId
        XCTAssertEqual(invalidLessonError.localizedDescription, "ID buổi học không hợp lệ")
        
        let networkError = CheckinError.networkError("Connection failed")
        XCTAssertEqual(networkError.localizedDescription, "Lỗi kết nối: Connection failed")
        
        let unauthorizedError = CheckinError.unauthorized
        XCTAssertEqual(unauthorizedError.localizedDescription, "Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.")
        
        let serverError = CheckinError.serverError("Internal server error")
        XCTAssertEqual(serverError.localizedDescription, "Lỗi server: Internal server error")
    }
    
    // MARK: - Integration Tests
    
    func testDeviceInfoGeneration() {
        // When
        let deviceInfo = UIDevice.current.systemName + " " + UIDevice.current.systemVersion
        
        // Then
        XCTAssertFalse(deviceInfo.isEmpty)
        XCTAssertTrue(deviceInfo.contains("iOS") || deviceInfo.contains("iPhone"))
    }
}

// MARK: - Mock Classes for Testing

class MockInstructorCheckinService: InstructorCheckinService {
    var shouldSucceed = true
    var mockResponse: InstructorCheckInResponse?
    var mockError: Error?
    
    override func checkinToLesson(lessonId: Int, location: CLLocation? = nil, notes: String? = nil) async throws -> InstructorCheckInResponse {
        if let error = mockError {
            throw error
        }
        
        if shouldSucceed {
            return mockResponse ?? InstructorCheckInResponse(
                success: true,
                message: "Check-in thành công!",
                data: CheckinData(),
                meta: CheckinMeta()
            )
        } else {
            return InstructorCheckInResponse(
                success: false,
                message: "Check-in thất bại!",
                data: CheckinData(),
                meta: CheckinMeta()
            )
        }
    }
    
    override func checkoutFromLesson(lessonId: Int, location: CLLocation? = nil, notes: String? = nil, lessonSummary: String? = nil) async throws -> InstructorCheckInResponse {
        if let error = mockError {
            throw error
        }
        
        if shouldSucceed {
            return mockResponse ?? InstructorCheckInResponse(
                success: true,
                message: "Check-out thành công!",
                data: CheckinData(),
                meta: CheckinMeta()
            )
        } else {
            return InstructorCheckInResponse(
                success: false,
                message: "Check-out thất bại!",
                data: CheckinData(),
                meta: CheckinMeta()
            )
        }
    }
}
