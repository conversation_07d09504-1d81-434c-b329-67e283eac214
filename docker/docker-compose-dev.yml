# Docker Compose cho LMS - Cập nhật ng<PERSON><PERSON> Apr 8 08:40:00 +07 2025
# Loại bỏ thuộc tính version lỗi thời và di chuyển env_file vào từng service

# Sử dụng file .env cho biến môi trường
# env_file:
#   - .env

services:
  # website:
  #   image: ghcr.io/earnbaseio/vantis-lms-website:beta
  #   container_name: website
  #   ports:
  #     - "127.0.0.1:38268:80"
  #   restart: unless-stopped
  #   networks:
  #     - odoo_lms_uat
  # odoo:
  #   image: ghcr.io/earnbaseio/odoo:18.0-community
  #   restart: always
  #   tty: true
  #   networks:
  #     - odoo_lms_uat
  #   ports:
  #     - "127.0.0.1:38269:8069"
  #     - "127.0.0.1:38272:8072"
    # entrypoint: ["odoo"]
    # command: ["--dev=reload,xml"]
    # depends_on:
    #   - postgres
    # volumes:
    #   - ./data/odoo/files:/var/lib/odoo
    #   - ./config/odoo/odoo.conf:/etc/odoo/odoo.conf
    #   - ../:/mnt/extra-addons
    # environment:
    #   - OTEL_ENABLED=${OTEL_ENABLED}
    #   - OTEL_SERVICE_NAME=${OTEL_SERVICE_NAME}
    #   - OTEL_EXPORTER_OTLP_ENDPOINT=${OTEL_EXPORTER_OTLP_ENDPOINT}
    #   - OTEL_RESOURCE_ATTRIBUTES=${OTEL_RESOURCE_ATTRIBUTES}
    #   - OTEL_PYTHON_TRACER_PROVIDER=${OTEL_PYTHON_TRACER_PROVIDER}
    # env_file:
    #   - .env
  postgres:
    build: ./postgres-pgvector
    restart: always
    networks:
      - odoo_lms_uat
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    ports:
      - "127.0.0.1:38432:5432"
    environment:
      - POSTGRES_DB=postgres
      - POSTGRES_USER=lms
      - POSTGRES_PASSWORD=lms
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - ./data/postgres:/var/lib/postgresql/data/pgdata
      - ./config/postgres/postgresql.conf:/etc/postgresql/postgresql.conf
      - ./config/postgres/pg_hba.conf:/etc/postgresql/pg_hba.conf
      - ./init-pgvector.sql:/docker-entrypoint-initdb.d/init-pgvector.sql
    env_file:
      - .env

  # Jaeger - hệ thống lưu trữ và hiển thị trace
  jaeger:
    image: jaegertracing/all-in-one:latest
    restart: always
    networks:
      - odoo_lms_uat
    ports:
      - "127.0.0.1:16686:16686"  # UI
      - "127.0.0.1:4317:4317"    # OTLP gRPC
      - "127.0.0.1:4318:4318"    # OTLP HTTP
    environment:
      - COLLECTOR_OTLP_ENABLED=true
      - COLLECTOR_ZIPKIN_HOST_PORT=:9411
      - QUERY_BASE_PATH=/jaeger
    volumes:
      - ./data/jaeger:/badger
    env_file:
      - .env

  # OpenTelemetry Collector - thu thập và xử lý dữ liệu tracing
  otel-collector:
    image: otel/opentelemetry-collector-contrib:latest
    restart: always
    networks:
      - odoo_lms_uat
    command: ["--config=/etc/otel-collector-config.yaml"]
    volumes:
      - ./config/otel/otel-collector-config.yaml:/etc/otel-collector-config.yaml
    environment:
      - SERVICE_ENVIRONMENT=${SERVICE_ENVIRONMENT}
    ports:
      - "127.0.0.1:8888:8888"   # Prometheus metrics
      - "127.0.0.1:8889:8889"   # Prometheus exporter metrics
      - "127.0.0.1:13133:13133" # Health check
    depends_on:
      - jaeger
    env_file:
      - .env

networks:
  odoo_lms_uat:
    name: odoo_lms_uat