# DB Version: 15
# OS Type: linux
# DB Type: web
# Total Memory (RAM): 4 GB
# CPUs num: 2
# Connections num: 65
# Data Storage: ssd
#


wal_level = logical
max_wal_senders = 65

hba_file = '/etc/postgresql/pg_hba.conf'

max_connections = 128
shared_buffers = 6GB
effective_cache_size = 18GB
maintenance_work_mem = 1536MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200
work_mem = 45923kB
huge_pages = off
min_wal_size = 1GB
max_wal_size = 4GB
max_worker_processes = 9
max_parallel_workers_per_gather = 4
max_parallel_workers = 9
max_parallel_maintenance_workers = 4

listen_addresses = '*'
