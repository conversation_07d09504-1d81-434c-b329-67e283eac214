# TYPE  DATABASE        USER            ADDRESS                 METHOD
# "local" is for Unix domain socket connections only
local   all             all                                     trust
# to enable local docker connections:
host    all             all             samenet         md5
# IPv4 local connections:
host    all             all             127.0.0.1/32            trust
# IPv6 local connections:
host    all             all             ::1/128                 trust
# Allow replication connections from localhost, by a user with the
# replication privilege.
local   all     all                                     trust
host    replication     all             127.0.0.1/32            trust
host    replication     all             ::1/128                 trust
host    replication     repmgr          0.0.0.0/0               md5
host    replication     repmgr          ::/0                    md5

# Allow remote access from any IP address
host    all             all             0.0.0.0/0               md5
host    all             all             ::/0                    md5