receivers:
  otlp:
    protocols:
      grpc:
        endpoint: 0.0.0.0:4317
      http:
        endpoint: 0.0.0.0:4318

processors:
  batch:
    # Gửi dữ liệu theo lô mỗi 10 giây hoặc khi đạt tối đa 512 spans
    timeout: 10s
    send_batch_size: 512
  
  # Thê<PERSON> các thông tin tài nguyên vào dữ liệu
  resource:
    attributes:
      - key: service.environment
        value: ${SERVICE_ENVIRONMENT}
        action: upsert

  # Lọc các dữ liệu không cần thiết
  filter:
    metrics:
      include:
        match_type: regexp
        metric_names:
          - .* # Bao gồm tất cả metrics

exporters:
  # <PERSON><PERSON><PERSON> traces tới Jaeger
  otlp:
    endpoint: jaeger:4317
    tls:
      insecure: true
  
  # Exporter để gửi metrics ra Prometheus
  prometheus:
    endpoint: 0.0.0.0:8889

  # Ghi log (đã thay thế logging thành debug)
  debug:
    verbosity: detailed

extensions:
  health_check:
    endpoint: 0.0.0.0:13133
  zpages:
    endpoint: 0.0.0.0:55679

service:
  extensions: [health_check, zpages]
  pipelines:
    traces:
      receivers: [otlp]
      processors: [batch, resource]
      exporters: [otlp, debug]
    metrics:
      receivers: [otlp]
      processors: [batch, resource]
      exporters: [prometheus, debug] 