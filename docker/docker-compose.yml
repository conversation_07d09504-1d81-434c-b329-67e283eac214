# Docker Compose cho LMS - Cập nhật ng<PERSON><PERSON> Apr 8 08:40:00 +07 2025
# Loại bỏ thuộc tính version lỗi thời và di chuyển env_file vào từng service

# Sử dụng file .env cho biến môi trường
# env_file:
#   - .env

services:
  lms_website:
    image: ghcr.io/earnbaseio/vantis-lms-website:latest
    ports:
      - "127.0.0.1:40268:80"
    restart: unless-stopped
    networks:
      - odoo_lms_dev
  odoo:
    image: ghcr.io/earnbaseio/odoo:18.0-community
    restart: always
    tty: true
    networks:
      - odoo_lms_dev
    entrypoint: ["odoo"]
    command: ["--dev=reload,xml"]
    ports:
      - "127.0.0.1:40269:8069"
      - "127.0.0.1:40272:8072"
    volumes:
      - ./data/odoo/files:/var/lib/odoo
      - ./config/odoo/odoo.conf:/etc/odoo/odoo.conf
      - ../:/mnt/extra-addons
    environment:
      - OTEL_ENABLED=${OTEL_ENABLED}
      - OTEL_SERVICE_NAME=${OTEL_SERVICE_NAME}
      - OTEL_EXPORTER_OTLP_ENDPOINT=${OTEL_EXPORTER_OTLP_ENDPOINT}
      - OTEL_RESOURCE_ATTRIBUTES=${OTEL_RESOURCE_ATTRIBUTES}
      - OTEL_PYTHON_TRACER_PROVIDER=${OTEL_PYTHON_TRACER_PROVIDER}
    env_file:
      - .env
  postgres:
    build: ./postgres-pgvector
    restart: always
    networks:
      - odoo_lms_dev
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    ports:
      - "127.0.0.1:40432:5432"
    environment:
      - POSTGRES_DB=postgres
      - POSTGRES_USER=lms
      - POSTGRES_PASSWORD=lms
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - ./data/postgres:/var/lib/postgresql/data/pgdata
      - ./config/postgres/postgresql.conf:/etc/postgresql/postgresql.conf
      - ./config/postgres/pg_hba.conf:/etc/postgresql/pg_hba.conf
      - ./postgres-pgvector/init-pgvector.sql:/docker-entrypoint-initdb.d/init-pgvector.sql
    env_file:
      - .env

networks:
  odoo_lms_dev:
    name: odoo_lms_dev