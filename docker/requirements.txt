PyJWT==2.8.0
pydantic
pydantic-core
python-multipart==0.0.6
ujson==5.8.0
parse-accept-language==0.1.2
mccabe==0.7.0
pycodestyle==2.11.0
contextvars==2.4
requests


google-auth==2.23.4
google-auth-oauthlib==1.1.0
google-auth-httplib2==0.1.1
google-api-python-client==2.106.0
oauth2client==4.1.3
pyzk==0.9
boto3==1.33.2
pyotp==2.9.0
fastapi==0.115.12

# FastAPI và các phụ thuộc
uvicorn>=0.23.2
a2wsgi>=1.0.0
pydantic>=2.3.0
starlette>=0.31.1
python-multipart>=0.0.6
python-jose>=3.3.0
passlib>=1.7.4

# OpenTelemetry và các phụ thuộc
opentelemetry-api>=1.21.0
opentelemetry-sdk>=1.21.0
opentelemetry-exporter-otlp>=1.21.0
opentelemetry-instrumentation-fastapi>=0.42b0
opentelemetry-instrumentation>=0.42b0

# Các phụ thuộc bổ sung
pyjwt>=2.8.0
python-dateutil>=2.8.2
pytz
requests>=2.31.0
# Redis (optional) - dùng cho rate limiting
redis>=4.5.1

# for push notifications
firebase_admin 
hyper

# crewai
crewai==0.114.0 
crewai-tools==0.40.1

# for RAG
pgvector==0.4.0
sentence-transformers==4.1.0
tokenizers==0.21.1
numpy==2.2.5