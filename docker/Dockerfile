FROM python:3.12-slim-bookworm

SHELL ["/bin/bash", "-xo", "pipefail", "-c"]

ARG GIT_APIKEY=
ARG GIT_ORG=earnbaseio
ARG GIT_REPO=odoo
ARG GIT_REGISTRY=github.com
ARG TARGETARCH=

# Generate locale C.UTF-8 for postgres and general locale data
ENV LANG C.UTF-8

# Install system dependencies, lessc, Less plugins, wkhtmltopdf, and build tools
# Removed python3-* packages as they are implicitly handled or installed via pip
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    ca-certificates \
    curl \
    dirmngr \
    fonts-noto-cjk \
    gnupg \
    libssl-dev \
    node-less \
    npm \
    xz-utils \
    libsasl2-dev \
    libldap2-dev \
    libffi-dev \
    build-essential \
    wget \
    git screen vim \
    libmagic1 \
    libcairo2-dev libglib2.0-dev \
    libpq-dev \
    zlib1g-dev libncurses5-dev libgdbm-dev libnss3-dev libreadline-dev libsqlite3-dev libbz2-dev \
    postgresql-client postgresql-client-common \
    # Add python dev headers for the specific version, gcc/g++ and libzbar-dev
    gcc g++ libzbar-dev \
    && rm -rf /var/lib/apt/lists/*

# Install wkhtmltopdf
RUN curl -o wkhtmltox.deb -sSL https://github.com/wkhtmltopdf/packaging/releases/download/********-3/wkhtmltox_********-3.bookworm_amd64.deb \
    && apt-get update && apt-get install -y --no-install-recommends ./wkhtmltox.deb \
    && rm -rf /var/lib/apt/lists/* wkhtmltox.deb

# Install rtlcss
RUN npm install -g rtlcss

ENV ODOO_VERSION=18.0 \
    ODOO_USER=odoo \
    ODOO_HOME=/opt/odoo

# Create the Odoo user and directory
RUN adduser --system --group --home $ODOO_HOME --uid 1000 $ODOO_USER && \
    mkdir -p $ODOO_HOME/odoo-addons && \
    mkdir $ODOO_HOME/odoo-data && \
    mkdir /mnt/extra-addons && \
    chown -R $ODOO_USER:$ODOO_USER $ODOO_HOME

ENV PATH="/opt/odoo/.local/bin:$PATH"

# Install wheel and an older cryptography version if needed (check if still required for Odoo 18/Python 3.12)
# Use pip (linked to python3.12)
RUN pip install --no-cache-dir wheel

# Install Odoo from source
RUN echo "Cloning Odoo version $ODOO_VERSION"
# Consider using a shallow clone with --depth 1 if full history isn't needed
RUN git clone --depth 1 --branch $ODOO_VERSION --recursive https://github.com/odoo/odoo $ODOO_HOME/odoo-src

# Install Odoo python dependencies
# Use pip instead of pip3
RUN cd $ODOO_HOME/odoo-src && \
    pip install --no-cache-dir -e . && \
    pip install --no-cache-dir -r requirements.txt

# Install custom 3rd party python libraries
COPY ./requirements.txt /
RUN pip install --no-cache-dir -r /requirements.txt

# Expose Odoo ports
EXPOSE 8069 8072

# Set the entrypoint and supporting files
COPY ./entrypoint.sh /
COPY ./odoo.conf /etc/odoo/
COPY wait-for-psql.py /usr/local/bin/wait-for-psql.py
RUN chmod +x /entrypoint.sh /usr/local/bin/wait-for-psql.py

# using for wsgi application
COPY ./odoo-bin $ODOO_HOME/odoo-src
RUN chmod +x $ODOO_HOME/odoo-src/odoo-bin

# Set the default config file
ENV ODOO_RC /etc/odoo/odoo.conf

# Set the working directory
WORKDIR $ODOO_HOME/odoo-src

# Run as root initially for setup, entrypoint script might switch user
USER root

ENTRYPOINT ["/entrypoint.sh"]
CMD ["odoo"]
