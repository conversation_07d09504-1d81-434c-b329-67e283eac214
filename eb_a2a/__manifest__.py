# -*- coding: utf-8 -*-
{
    "name": "EB A2A Integration",
    "summary": "Simple AI Integration for Odoo",
    "description": """
        Simple AI integration for Odoo:
        - Agent management
        - Task management
        - Message handling
        - OpenAI integration
    """,
    "author": "eBill and Vantis Vietnam",
    "website": "https://earnbase.io",
    "category": "Technical/AI",
    "version": "********.0",
    "depends": [
        "base",
        "mail",
        "web",
        "website",
        "eb_api_core"
    ],
    "data": [
        "security/security_rules.xml",
        "security/ir.model.access.csv",
        "views/a2a_menu_views.xml",
        "views/a2a_provider_views.xml",
        "views/a2a_agent_endpoint_views.xml",
        "views/a2a_task_views.xml",
        "views/a2a_task_card_views.xml",
        "views/a2a_crew_registry_views.xml",
        "views/a2a_website_config_views.xml",
        "data/agent_card_data.xml",
        "data/provider_data.xml",
        "data/crew_registry_data.xml",
        "data/a2a_crew_registry_data.xml",
        "data/agent_endpoint_data.xml",
        "views/a2a_chat_views.xml",
    ],
    "assets": {
        "web.assets_backend": [
            "eb_a2a/static/lib/marked/marked.min.js",
            "eb_a2a/static/src/js/marked_setup.js",
            "eb_a2a/static/src/js/a2a_markdown.js",
            "eb_a2a/static/src/js/**/*",
            "eb_a2a/static/src/xml/**/*",
            "eb_a2a/static/src/scss/**/*",
            "eb_a2a/static/src/css/**/*",
        ],
    },
    "external_dependencies": {
        "python": ["openai", "requests", "crewai"],
    },
    "license": "LGPL-3",
    "installable": True,
    "application": False,
    "auto_install": False,

}
