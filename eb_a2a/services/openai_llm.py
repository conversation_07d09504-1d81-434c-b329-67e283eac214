# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging
import os
import sys
import warnings
import collections
import collections.abc

# Monkey patch cho Python 3.10+ để hỗ trợ các thư viện cũ sử dụng collections.MutableSet
# Trong Python 3.10+, collections.MutableSet đã được chuyển sang collections.abc.MutableSet
if sys.version_info >= (3, 10):
    for name in ['MutableSet', 'MutableMapping']:
        if not hasattr(collections, name) and hasattr(collections.abc, name):
            setattr(collections, name, getattr(collections.abc, name))

# Monkey patch để tắt cảnh báo về class-based config trong Pydantic
# Cảnh báo này xuất hiện khi sử dụng thư viện LiteLLM với Pydantic v2
warnings.filterwarnings("ignore", message="Support for class-based `config` is deprecated")

_logger = logging.getLogger(__name__)

# Kiểm tra xem LiteLLM có khả dụng không
try:
    import litellm
    LITELLM_AVAILABLE = True
except ImportError:
    _logger.warning("Thư viện LiteLLM không khả dụng. Cài đặt bằng: pip install litellm")
    LITELLM_AVAILABLE = False
    litellm = None  # Đặt litellm là None để tránh lỗi "unbound"


def create_litellm(env, model=None):
    """Tạo LiteLLM model sử dụng OpenAI Compatible API.

    Args:
        env: Odoo environment
        model: Model AI (mặc định: gpt-4o)

    Returns:
        LiteLLM model hoặc None nếu không thể tạo
    """
    try:
        # Kiểm tra xem LiteLLM có khả dụng không
        if not LITELLM_AVAILABLE:
            _logger.error("LiteLLM libraries not available")
            return None

        # Lấy provider từ database
        Provider = env['eb_a2a.provider']
        provider = Provider.search([('is_default', '=', True)], limit=1)

        if not provider:
            _logger.error("No default provider found")
            return None

        # Lấy model từ parameter hoặc sử dụng model mặc định của provider
        if not model:
            provider_model = provider.get_default_model()
            if provider_model:
                model = provider_model.name
            else:
                model = "gpt-4o"  # Fallback model

        # Thiết lập API key và base URL
        api_key = provider.api_key
        api_base = provider.endpoint_url

        if not api_key:
            _logger.error("No API key found for provider")
            return None

        # Thiết lập LiteLLM
        if LITELLM_AVAILABLE and litellm is not None:
            litellm.api_key = api_key
            if api_base:
                os.environ["OPENAI_API_BASE"] = api_base

        # Tạo model
        _logger.info(f"Creating LiteLLM model: {model}")
        return model  # LiteLLM sẽ sử dụng model name này với API key và base URL đã thiết lập

    except Exception as e:
        _logger.exception(f"Error creating LiteLLM model: {str(e)}")
        return None
