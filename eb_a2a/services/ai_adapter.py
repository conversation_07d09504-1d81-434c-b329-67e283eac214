# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging
import requests
import json
import asyncio
from typing import Dict, Any, Optional, AsyncGenerator, List

_logger = logging.getLogger(__name__)


class AIAdapter:
    """Base class for AI adapters."""

    def generate_response(self, prompt: str, context: Optional[Dict[str, Any]] = None) -> str:
        """Generate a response to a prompt.

        Args:
            prompt: The prompt to generate a response for
            context: Additional context for the prompt

        Returns:
            The generated response
        """
        raise NotImplementedError("Subclasses must implement generate_response")

    async def generate_streaming_response(self, prompt: str, context: Optional[Dict[str, Any]] = None) -> AsyncGenerator[str, None]:
        """Generate a streaming response to a prompt.

        Args:
            prompt: The prompt to generate a response for
            context: Additional context for the prompt

        Yields:
            Chunks of the generated response
        """
        raise NotImplementedError("Subclasses must implement generate_streaming_response")


class OpenAIAdapter(AIAdapter):
    """Adapter for OpenAI API."""

    def __init__(self, api_key: str, model: str = "gpt-4o", api_base: Optional[str] = None):
        """Initialize the OpenAI adapter.

        Args:
            api_key: OpenAI API key
            model: Model to use (default: gpt-4o)
            api_base: Base URL for API (default: None, uses OpenAI's default)
        """
        self.api_key = api_key
        self.model = model
        self.api_base = api_base or "https://api.openai.com/v1"
        self.api_url = f"{self.api_base}/chat/completions"

        # Kiểm tra xem OpenAI có khả dụng không
        try:
            import openai
            self.openai_available = True
            self.openai = openai
            self.openai_client = openai.AsyncOpenAI(api_key=self.api_key, base_url=self.api_base)
        except ImportError:
            _logger.warning("Thư viện OpenAI không khả dụng. Cài đặt bằng: pip install openai>=1.0.0")
            self.openai_available = False
            self.openai_client = None

    def _prepare_messages(self, prompt: str, context: Optional[Dict[str, Any]] = None) -> List[Dict[str, str]]:
        """Prepare messages for OpenAI API."""
        messages = []

        # Add system message if provided
        if context and "system_message" in context:
            system_message = context["system_message"]
            _logger.info(f"OpenAI API request - Using provided system message: {system_message[:50]}...")
            messages.append({
                "role": "system",
                "content": system_message
            })
        else:
            default_system_message = "Bạn là trợ lý AI của hệ thống quản lý học tập. Hãy trả lời các câu hỏi về khóa học, lịch học, và các thông tin liên quan đến việc học tập."
            _logger.info("OpenAI API request - Using default system message")
            messages.append({
                "role": "system",
                "content": default_system_message
            })

        # Add conversation history if provided
        if context and "history" in context:
            history = context["history"]
            _logger.info(f"OpenAI API request - Adding conversation history, {len(history)} messages")
            messages.extend(history)

        # Add user prompt
        _logger.info(f"OpenAI API request - User prompt: {prompt[:50]}...")
        messages.append({
            "role": "user",
            "content": prompt
        })

        return messages

    def generate_response(self, prompt: str, context: Optional[Dict[str, Any]] = None) -> str:
        """Generate a response using OpenAI API."""
        try:
            _logger.info(f"OpenAI API request - API Base: {self.api_base}, URL: {self.api_url}")
            _logger.info(f"OpenAI API request - Model: {self.model}")

            # Mask API key for security
            masked_api_key = self.api_key[:4] + "*" * (len(self.api_key) - 8) + self.api_key[-4:] if self.api_key and len(self.api_key) > 8 else "****"
            _logger.info(f"OpenAI API request - API Key (masked): {masked_api_key}")

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            _logger.info(f"OpenAI API request - Headers: {headers}")

            messages = self._prepare_messages(prompt, context)

            data = {
                "model": self.model,
                "messages": messages,
                "max_tokens": 1000
            }

            _logger.info(f"OpenAI API request - Request data: model={self.model}, messages_count={len(messages)}, max_tokens=1000")

            _logger.info(f"OpenAI API request - Sending POST request to {self.api_url}")
            response = requests.post(self.api_url, headers=headers, json=data)

            _logger.info(f"OpenAI API response - Status code: {response.status_code}")
            if response.status_code != 200:
                _logger.error(f"OpenAI API response - Error response: {response.text}")

            response.raise_for_status()

            result = response.json()
            _logger.info("OpenAI API response - Successfully parsed JSON response")

            content = result["choices"][0]["message"]["content"]
            _logger.info(f"OpenAI API response - Response content: {content[:50]}...")

            return content
        except Exception as e:
            _logger.exception(f"Error calling OpenAI API: {str(e)}")
            return f"Xin lỗi, tôi không thể xử lý yêu cầu của bạn lúc này. Lỗi: {str(e)}"

    async def generate_streaming_response(self, prompt: str, context: Optional[Dict[str, Any]] = None) -> AsyncGenerator[str, None]:
        """Generate a streaming response using OpenAI API."""
        try:
            if not self.openai_available or not self.openai_client:
                _logger.error("OpenAI library not available for streaming")
                yield "Xin lỗi, thư viện OpenAI không khả dụng cho streaming. Vui lòng cài đặt bằng: pip install openai>=1.0.0"
                return

            _logger.info(f"OpenAI API streaming request - API Base: {self.api_base}")
            _logger.info(f"OpenAI API streaming request - Model: {self.model}")

            messages = self._prepare_messages(prompt, context)

            _logger.info(f"OpenAI API streaming request - Starting stream with {len(messages)} messages")

            # Gọi API với stream=True
            stream = await self.openai_client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=1000,
                stream=True
            )

            _logger.info("OpenAI API streaming request - Stream created successfully")

            # Xử lý từng chunk
            async for chunk in stream:
                if chunk.choices and chunk.choices[0].delta and chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    _logger.debug(f"OpenAI API streaming response - Chunk: {content}")
                    yield content

            _logger.info("OpenAI API streaming request - Stream completed successfully")

        except Exception as e:
            _logger.exception(f"Error in streaming response from OpenAI API: {str(e)}")
            yield f"\n\nXin lỗi, đã xảy ra lỗi trong quá trình streaming: {str(e)}"
            return


class AzureOpenAIAdapter(OpenAIAdapter):
    """Adapter for Azure OpenAI API."""

    def __init__(self, api_key: str, model: str = "gpt-4", api_base: Optional[str] = None, api_version: str = "2023-05-15"):
        """Initialize the Azure OpenAI adapter.

        Args:
            api_key: Azure OpenAI API key
            model: Model to use (default: gpt-4)
            api_base: Base URL for API (required)
            api_version: API version (default: 2023-05-15)
        """
        if not api_base:
            raise ValueError("api_base is required for Azure OpenAI")

        # Không gọi super().__init__() để tránh gọi OpenAIAdapter.__init__()
        # vì chúng ta cần cấu hình khác cho Azure
        self.api_key = api_key
        self.model = model
        self.api_base = api_base
        self.api_version = api_version
        self.api_url = f"{self.api_base}/openai/deployments/{self.model}/chat/completions?api-version={self.api_version}"

        # Kiểm tra xem OpenAI có khả dụng không
        try:
            import openai
            self.openai_available = True
            self.openai = openai
            self.openai_client = openai.AsyncAzureOpenAI(
                api_key=self.api_key,
                api_version=self.api_version,
                azure_endpoint=self.api_base
            )
        except ImportError:
            _logger.warning("Thư viện OpenAI không khả dụng. Cài đặt bằng: pip install openai>=1.0.0")
            self.openai_available = False
            self.openai_client = None

    def generate_response(self, prompt: str, context: Optional[Dict[str, Any]] = None) -> str:
        """Generate a response using Azure OpenAI API."""
        try:
            _logger.info(f"Azure OpenAI API request - API Base: {self.api_base}, URL: {self.api_url}")
            _logger.info(f"Azure OpenAI API request - Model: {self.model}, API Version: {self.api_version}")

            # Mask API key for security
            masked_api_key = self.api_key[:4] + "*" * (len(self.api_key) - 8) + self.api_key[-4:] if self.api_key and len(self.api_key) > 8 else "****"
            _logger.info(f"Azure OpenAI API request - API Key (masked): {masked_api_key}")

            headers = {
                "Content-Type": "application/json",
                "api-key": self.api_key
            }

            _logger.info(f"Azure OpenAI API request - Headers: {headers}")

            messages = self._prepare_messages(prompt, context)

            data = {
                "messages": messages,
                "max_tokens": 1000
            }

            _logger.info(f"Azure OpenAI API request - Request data: messages_count={len(messages)}, max_tokens=1000")

            _logger.info(f"Azure OpenAI API request - Sending POST request to {self.api_url}")
            response = requests.post(self.api_url, headers=headers, json=data)

            _logger.info(f"Azure OpenAI API response - Status code: {response.status_code}")
            if response.status_code != 200:
                _logger.error(f"Azure OpenAI API response - Error response: {response.text}")

            response.raise_for_status()

            result = response.json()
            _logger.info("Azure OpenAI API response - Successfully parsed JSON response")

            content = result["choices"][0]["message"]["content"]
            _logger.info(f"Azure OpenAI API response - Response content: {content[:50]}...")

            return content
        except Exception as e:
            _logger.exception(f"Error calling Azure OpenAI API: {str(e)}")
            return f"Xin lỗi, tôi không thể xử lý yêu cầu của bạn lúc này. Lỗi: {str(e)}"

    async def generate_streaming_response(self, prompt: str, context: Optional[Dict[str, Any]] = None) -> AsyncGenerator[str, None]:
        """Generate a streaming response using Azure OpenAI API."""
        try:
            if not self.openai_available or not self.openai_client:
                _logger.error("OpenAI library not available for streaming with Azure")
                yield "Xin lỗi, thư viện OpenAI không khả dụng cho streaming với Azure. Vui lòng cài đặt bằng: pip install openai>=1.0.0"
                return

            _logger.info(f"Azure OpenAI API streaming request - API Base: {self.api_base}")
            _logger.info(f"Azure OpenAI API streaming request - Model: {self.model}, API Version: {self.api_version}")

            messages = self._prepare_messages(prompt, context)

            _logger.info(f"Azure OpenAI API streaming request - Starting stream with {len(messages)} messages")

            # Gọi API với stream=True
            stream = await self.openai_client.chat.completions.create(
                model=self.model,  # Azure sử dụng deployment name thay vì model name
                messages=messages,
                max_tokens=1000,
                stream=True
            )

            _logger.info("Azure OpenAI API streaming request - Stream created successfully")

            # Xử lý từng chunk
            async for chunk in stream:
                if chunk.choices and chunk.choices[0].delta and chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    _logger.debug(f"Azure OpenAI API streaming response - Chunk: {content}")
                    yield content

            _logger.info("Azure OpenAI API streaming request - Stream completed successfully")

        except Exception as e:
            _logger.exception(f"Error in streaming response from Azure OpenAI API: {str(e)}")
            yield f"\n\nXin lỗi, đã xảy ra lỗi trong quá trình streaming với Azure: {str(e)}"
            return


# Factory function to create AI adapters
def create_ai_adapter(api_type: str, api_key: Optional[str] = None, model: Optional[str] = None, api_base: Optional[str] = None, api_version: Optional[str] = None):
    """Create an AI adapter based on the API type.

    Args:
        api_type: Type of API to use (openai, azure)
        api_key: API key
        model: Model to use
        api_base: Base URL for API
        api_version: API version (for Azure)
        organization_id: Organization ID (for OpenAI)

    Returns:
        An instance of AIAdapter
    """
    _logger.info(f"Creating AI adapter - Type: {api_type}, Model: {model or 'default'}, API Base: {api_base or 'default'}")

    # Kiểm tra api_key
    if not api_key:
        _logger.error("API key is required")
        raise ValueError("API key is required")

    # Mask API key for security
    masked_api_key = api_key[:4] + "*" * (len(api_key) - 8) + api_key[-4:] if len(api_key) > 8 else "****"
    _logger.info(f"Creating AI adapter - API Key (masked): {masked_api_key}")

    if api_type == "openai":
        _logger.info("Creating OpenAI adapter")
        adapter = OpenAIAdapter(api_key=api_key, model=model or "gpt-4o", api_base=api_base)
        _logger.info(f"OpenAI adapter created - URL: {adapter.api_url}")
        return adapter
    elif api_type == "azure":
        _logger.info(f"Creating Azure OpenAI adapter - API Version: {api_version or 'default'}")
        adapter = AzureOpenAIAdapter(
            api_key=api_key,
            model=model or "gpt-4",
            api_base=api_base,
            api_version=api_version or "2023-05-15"
        )
        _logger.info(f"Azure OpenAI adapter created - URL: {adapter.api_url}")
        return adapter
    else:
        _logger.error(f"Unsupported API type: {api_type}")
        raise ValueError(f"Unsupported API type: {api_type}")
