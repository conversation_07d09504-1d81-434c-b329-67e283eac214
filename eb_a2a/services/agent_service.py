# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import json
import logging
import asyncio
from typing import AsyncGenerator, Optional, Dict, Any, List
from .ai_adapter import create_ai_adapter

_logger = logging.getLogger(__name__)


class AgentService:
    """Service class để xử lý các tác vụ của agent.

    Phiên bản đơn giản hóa, chỉ giữ lại các chức năng cơ bản.
    """

    def __init__(self, env):
        """Khởi tạo service với Odoo environment."""
        self.env = env
        self.ai_adapter = None  # Adapter cache
        self.streaming_tasks = {}  # Cache cho các streaming task

    def get_agent(self, agent_code):
        """Lấy agent theo mã."""
        return self.env['eb_a2a.agent'].search([('code', '=', agent_code), ('active', '=', True)], limit=1)

    def get_task(self, task_id, agent_id=None):
        """Lấy task theo ID."""
        domain = [('task_id', '=', task_id)]
        if agent_id:
            domain.append(('agent_id', '=', agent_id))
        return self.env['eb_a2a.task'].search(domain, limit=1)

    def create_task(self, task_id, agent_id, user_id=None):
        """Tạo task mới."""
        if not user_id:
            user_id = self.env.user.id

        return self.env['eb_a2a.task'].create({
            'task_id': task_id,
            'agent_id': agent_id,
            'user_id': user_id,
            'state': 'submitted',
        })

    def update_task_state(self, task, state, error_message=None):
        """Cập nhật trạng thái của task."""
        task.write({
            'state': state,
            'error_message': error_message,
        })
        return task

    def add_artifact(self, task, name, artifact_type, content=None, file_data=None, file_name=None, mime_type=None):
        """Thêm artifact vào task."""
        try:
            artifact = self.env['eb_a2a.task.artifact'].create({
                'task_id': task.id,
                'name': name,
                'artifact_type': artifact_type,
                'content': content,
                'file_data': file_data,
                'file_name': file_name,
                'mime_type': mime_type,
            })
            return artifact
        except Exception as e:
            _logger.exception(f"Lỗi khi thêm artifact: {str(e)}")
            return None

    def process_task(self, agent_code, task_id, message_content, message_role='user'):
        """Xử lý task.

        Args:
            agent_code: Mã của agent
            task_id: ID của task
            message_content: Nội dung tin nhắn
            message_role: Vai trò của tin nhắn (mặc định: 'user')

        Returns:
            dict: Kết quả xử lý task
        """
        try:
            # Tìm agent
            agent = self.env['eb_a2a.agent'].search([('code', '=', agent_code)], limit=1)
            if not agent:
                return {
                    'error': f"Không tìm thấy agent với mã {agent_code}"
                }

            # Tìm hoặc tạo task
            task = self.env['eb_a2a.task'].search([('task_id', '=', task_id)], limit=1)
            if not task:
                # Tạo task mới
                task = self.env['eb_a2a.task'].create({
                    'task_id': task_id,
                    'name': f"Task {task_id}",
                    'agent_id': agent.id,
                    'user_id': self.env.user.id,
                    'state': 'submitted',
                })

            # Kiểm tra trạng thái task
            if task.state in ['completed', 'failed', 'canceled']:
                # Đặt lại trạng thái
                task.state = 'submitted'

            # Thêm tin nhắn vào task
            self.add_message(task, message_role, message_content)

            # Cập nhật trạng thái task
            task.state = 'working'

            # Xử lý task dựa trên loại agent
            if agent.agent_type == 'external':
                # Xử lý agent bên ngoài
                result = self._process_external_agent(agent, message_content)
            else:
                # Xử lý agent nội bộ
                try:
                    result = self._handle_task(task, message_content)
                except Exception as e:
                    _logger.exception(f"Lỗi khi xử lý task: {str(e)}")
                    # Trả về phản hồi mặc định nếu có lỗi
                    result = {
                        'response': "Xin chào! Tôi là trợ lý AI của hệ thống Odoo. Tôi có thể giúp bạn trả lời các câu hỏi và hỗ trợ bạn sử dụng hệ thống. Bạn cần hỗ trợ gì?",
                        'state': 'completed'
                    }

            # Cập nhật trạng thái task
            if result.get('state'):
                task.state = result['state']

            # Thêm message phản hồi từ agent
            if result.get('response'):
                self.add_message(task, 'agent', result['response'])

            # Format kết quả trả về
            return {
                'task_id': task.task_id,
                'state': task.state,
                'response': result.get('response', ''),
                'error': result.get('error', None)
            }
        except Exception as e:
            _logger.exception(f"Lỗi khi xử lý task: {str(e)}")
            return {
                'error': f"Lỗi khi xử lý task: {str(e)}"
            }

    def add_message(self, task, role, content):
        """Thêm message vào task."""
        try:
            # Xác định author_id dựa trên role
            author_id = False
            if role == 'user':
                author_id = task.user_id.partner_id.id
            elif role == 'agent':
                author_id = task.agent_id.partner_id.id if task.agent_id.partner_id else False

            # Gửi tin nhắn vào task
            task_message = task.with_context(
                mail_create_nosubscribe=True,
                mail_auto_delete=False,
                mail_notify_author=False,
                mail_create_nolog=True,
                tracking_disable=True,
                mail_notrack=True,
                mail_create_force_send=False,
            ).sudo().message_post(
                body=content,
                message_type='comment',
                subtype_xmlid='mail.mt_comment',
                author_id=author_id,
                email_from='<EMAIL>',
            )

            # Tạo TextPart mặc định
            default_parts = [{
                'type': 'text',
                'text': content
            }]

            # Lưu trữ thông tin parts và role
            self.env.cr.execute(
                "UPDATE mail_message SET a2a_parts = %s, a2a_role = %s WHERE id = %s",
                (json.dumps(default_parts), role, task_message.id)
            )

            # Cập nhật trạng thái task nếu cần
            if task.state == 'input_required' and role == 'user':
                task.state = 'working'

            return task_message
        except Exception as e:
            _logger.exception(f"Lỗi khi thêm message: {str(e)}")
            return None

    def _process_external_agent(self, agent, message_content):
        """Xử lý task cho agent bên ngoài bằng cách gọi API.

        Args:
            agent: Bản ghi agent
            message_content: Nội dung message

        Returns:
            Dict chứa kết quả xử lý
        """
        # Kiểm tra endpoint_url
        if not agent.endpoint_url:
            raise ValueError("Agent bên ngoài phải có endpoint_url")

        # Hiện tại chỉ trả về kết quả giả lập
        return {
            'response': f"[External Agent] Đã nhận yêu cầu: {message_content}",
            'state': 'completed'
        }

    def _prepare_context(self, task, agent):
        """Chuẩn bị context cho AI."""
        # Tạo context cho AI
        system_message = agent.system_message or f"Bạn là trợ lý AI {agent.name} của hệ thống Odoo."

        # Lấy lịch sử hội thoại
        history = []
        for message in task.message_ids.sorted('id'):
            if message.a2a_role in ['user', 'agent']:
                history.append({
                    'role': 'user' if message.a2a_role == 'user' else 'assistant',
                    'content': message.body
                })

        return {
            'system_message': system_message,
            'history': history
        }

    def _handle_task(self, task, message_content):
        """Xử lý task dựa trên nội dung message."""
        # Lấy agent
        agent = task.agent_id

        # Sử dụng AI adapter để trả lời
        try:
            ai_adapter = self._get_ai_adapter(agent)
            if ai_adapter:
                # Chuẩn bị context
                context = self._prepare_context(task, agent)

                # Sử dụng AI để trả lời
                ai_response = ai_adapter.generate_response(message_content, context)
                if ai_response:
                    return {
                        'response': ai_response,
                        'state': 'completed'
                    }
        except Exception as e:
            _logger.warning(f"Lỗi khi sử dụng AI adapter: {str(e)}")

        # Nếu không sử dụng được AI, trả về phản hồi mặc định
        return {
            'response': "Xin chào! Tôi là trợ lý AI của hệ thống Odoo. Tôi có thể giúp bạn trả lời các câu hỏi và hỗ trợ bạn sử dụng hệ thống. Bạn cần hỗ trợ gì?",
            'state': 'completed'
        }

    async def process_streaming_task(self, agent_code, task_id, message_content, message_role='user'):
        """Xử lý task với streaming response.

        Args:
            agent_code: Mã của agent
            task_id: ID của task
            message_content: Nội dung tin nhắn
            message_role: Vai trò của tin nhắn (mặc định: 'user')

        Yields:
            Các phần của phản hồi streaming
        """
        try:
            # Tìm agent
            agent = self.get_agent(agent_code)
            if not agent:
                yield json.dumps({
                    'type': 'error',
                    'error': f"Không tìm thấy agent với mã {agent_code}"
                })
                return

            # Kiểm tra xem agent có hỗ trợ streaming không
            if not agent.supports_streaming:
                yield json.dumps({
                    'type': 'error',
                    'error': f"Agent {agent_code} không hỗ trợ streaming"
                })
                return

            # Tìm hoặc tạo task
            task = self.env['eb_a2a.task'].search([('task_id', '=', task_id)], limit=1)
            if not task:
                # Tạo task mới
                task = self.env['eb_a2a.task'].create({
                    'task_id': task_id,
                    'name': f"Task {task_id}",
                    'agent_id': agent.id,
                    'user_id': self.env.user.id,
                    'state': 'submitted',
                    'is_streaming': True,
                })

            # Kiểm tra trạng thái task
            if task.state in ['completed', 'failed', 'canceled']:
                # Đặt lại trạng thái
                task.state = 'submitted'

            # Thêm tin nhắn vào task
            self.add_message(task, message_role, message_content)

            # Cập nhật trạng thái task
            task.state = 'working'

            # Thông báo bắt đầu streaming
            yield json.dumps({
                'type': 'task_status_update',
                'task': {
                    'id': task_id,
                    'status': 'working',
                    'messages': []
                }
            })

            # Xử lý task dựa trên loại agent
            if agent.agent_type == 'external':
                # Xử lý agent bên ngoài (không hỗ trợ streaming)
                yield json.dumps({
                    'type': 'error',
                    'error': "Agent bên ngoài không hỗ trợ streaming"
                })
                return
            else:
                # Xử lý agent nội bộ với streaming
                try:
                    # Lấy AI adapter
                    ai_adapter = self._get_ai_adapter(agent)
                    if not ai_adapter:
                        yield json.dumps({
                            'type': 'error',
                            'error': "Không thể tạo AI adapter"
                        })
                        return

                    # Chuẩn bị context
                    context = self._prepare_context(task, agent)

                    # Tạo biến để lưu trữ nội dung phản hồi
                    full_response = ""

                    # Sử dụng AI để trả lời với streaming
                    async for chunk in ai_adapter.generate_streaming_response(message_content, context):
                        # Cập nhật nội dung phản hồi
                        full_response += chunk

                        # Trả về chunk
                        yield json.dumps({
                            'type': 'chunk',
                            'content': chunk
                        })

                    # Cập nhật trạng thái task
                    task.state = 'completed'

                    # Thêm message phản hồi từ agent
                    self.add_message(task, 'agent', full_response)

                    # Thông báo hoàn thành
                    yield json.dumps({
                        'type': 'task_status_update',
                        'task': {
                            'id': task_id,
                            'status': 'completed',
                            'messages': [{
                                'role': 'agent',
                                'parts': [{'type': 'text', 'text': full_response}]
                            }]
                        }
                    })

                except Exception as e:
                    _logger.exception(f"Lỗi khi xử lý streaming task: {str(e)}")
                    # Trả về lỗi
                    yield json.dumps({
                        'type': 'error',
                        'error': f"Lỗi khi xử lý streaming task: {str(e)}"
                    })
                    # Cập nhật trạng thái task
                    task.state = 'failed'
                    task.error_message = str(e)

        except Exception as e:
            _logger.exception(f"Lỗi khi xử lý streaming task: {str(e)}")
            yield json.dumps({
                'type': 'error',
                'error': f"Lỗi khi xử lý streaming task: {str(e)}"
            })

    def _get_ai_adapter(self, agent):
        """Lấy AI adapter cho agent.

        Args:
            agent: Bản ghi agent

        Returns:
            AIAdapter: Instance của AIAdapter hoặc None nếu có lỗi
        """
        # Nếu đã có adapter trong cache, trả về
        if self.ai_adapter:
            return self.ai_adapter

        # Lấy provider
        provider = agent.provider_id
        if not provider:
            # Tìm provider mặc định
            provider = self.env['eb_a2a.provider'].search([('is_default', '=', True)], limit=1)
            if not provider:
                # Tìm provider đầu tiên
                provider = self.env['eb_a2a.provider'].search([], limit=1)
                if not provider:
                    return None

        # Lấy model mặc định
        default_model = self.env['eb_a2a.provider.model'].search([
            ('provider_id', '=', provider.id),
            ('is_default', '=', True),
            ('model_type', 'in', ['chat', 'multimodal'])
        ], limit=1)

        model = default_model.model_id if default_model else "gpt-4o"

        # Tạo AI adapter
        try:
            self.ai_adapter = create_ai_adapter(
                provider.api_type,
                api_key=provider.api_key,
                model=model,
                api_base=provider.api_base,
                api_version=provider.api_version
            )
            return self.ai_adapter
        except Exception as e:
            _logger.exception(f"Lỗi khi tạo AI adapter: {str(e)}")
            return None
