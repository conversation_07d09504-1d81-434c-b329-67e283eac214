# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging
from typing import Dict, List, Any, Optional, Union

try:
    from crewai import Crew, Process, Task
    CREWAI_AVAILABLE = True
except ImportError:
    logging.getLogger(__name__).warning("CrewAI không khả dụng. Cài đặt bằng: pip install crewai")
    CREWAI_AVAILABLE = False
    # Tạo class giả để tránh lỗi khi import
    class Crew:
        def __init__(self, agents=None, tasks=None, process=None, verbose=None):
            self.agents = agents
            self.tasks = tasks
            self.process = process
            self.verbose = verbose

        def kickoff(self, inputs=None):
            return "CrewAI không khả dụng. Vui lòng cài đặt bằng: pip install crewai"

    class Process:
        sequential = "sequential"
        hierarchical = "hierarchical"

    class Task:
        def __init__(self, description=None, expected_output=None, agent=None):
            self.description = description
            self.expected_output = expected_output
            self.agent = agent

_logger = logging.getLogger(__name__)
