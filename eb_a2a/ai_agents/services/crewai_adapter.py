# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging
from typing import Optional, Dict, Any, List

try:
    from crewai import LLM
    CREWAI_AVAILABLE = True
except ImportError:
    logging.getLogger(__name__).warning("Thư viện CrewAI không khả dụng. Cài đặt bằng: pip install crewai")
    CREWAI_AVAILABLE = False

_logger = logging.getLogger(__name__)

def create_llm_for_crewai(provider):
    """Tạo LLM cho CrewAI dựa trên provider.

    Args:
        provider: Bản ghi eb_a2a.provider

    Returns:
        LLM: CrewAI LLM
    """
    _logger.info(f"create_llm_for_crewai - Starting with provider={provider.name}, api_type={provider.api_type}")

    try:
        if not CREWAI_AVAILABLE:
            _logger.warning("CrewAI không khả dụng. Không thể tạo LLM.")
            return None

        if provider.api_type == 'openai':
            # Tìm model mặc định
            default_model = provider.env['eb_a2a.provider.model'].search([
                ('provider_id', '=', provider.id),
                ('is_default', '=', True),
                ('model_type', '=', 'chat')
            ], limit=1)

            model = default_model.model_id if default_model else "gpt-4o"
            model_path = f"openai/{model}"

            _logger.info(f"create_llm_for_crewai - Creating OpenAI LLM with model={model_path}, api_base={provider.api_base}")

            llm = LLM(
                model=model_path,
                api_key=provider.api_key,
                base_url=provider.api_base,
                organization=provider.organization_id,
                temperature=0.7
            )

            _logger.info(f"create_llm_for_crewai - OpenAI LLM created successfully")
            return llm
        elif provider.api_type == 'azure':
            # Tìm model mặc định
            default_model = provider.env['eb_a2a.provider.model'].search([
                ('provider_id', '=', provider.id),
                ('is_default', '=', True),
            ], limit=1)

            model = default_model.model_id if default_model else "gpt-4"
            model_path = f"azure/{model}"

            return LLM(
                model=model_path,
                api_key=provider.api_key,
                azure_endpoint=provider.api_base,
                api_version=provider.api_version,
                temperature=0.7
            )
        else:
            # Fallback to OpenAI
            _logger.warning(f"Loại API không được hỗ trợ: {provider.api_type}. Sử dụng OpenAI mặc định.")
            return LLM(model="openai/gpt-4o", temperature=0.7)
    except Exception as e:
        _logger.exception(f"Lỗi khi tạo LLM cho CrewAI: {str(e)}")
        return LLM(model="openai/gpt-4o", temperature=0.7)  # Fallback
