# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import json
import logging
import importlib
from .crewai_adapter import create_llm_for_crewai
from ..crews.base_crews import CREWAI_AVAILABLE, Task, Crew, Process

_logger = logging.getLogger(__name__)


class AgentServiceCrewAI:
    """Service class để xử lý các tác vụ của agent sử dụng CrewAI.
    """

    def __init__(self, env):
        """Khởi tạo service với Odoo environment."""
        self.env = env
        self.llm = None  # LLM cache

    def get_agent(self, agent_code):
        """Lấy agent endpoint theo mã."""
        return self.env['eb_a2a.agent_endpoint'].search([('code', '=', agent_code), ('active', '=', True)], limit=1)

    def get_task(self, task_id, agent_endpoint_id=None):
        """Lấy task theo ID."""
        domain = [('task_id', '=', task_id)]
        if agent_endpoint_id:
            domain.append(('agent_endpoint_id', '=', agent_endpoint_id))
        return self.env['eb_a2a.task'].search(domain, limit=1)

    def create_task(self, task_id, agent_endpoint_id, user_id=None):
        """Tạo task mới."""
        if not user_id:
            user_id = self.env.user.id

        return self.env['eb_a2a.task'].create({
            'task_id': task_id,
            'agent_endpoint_id': agent_endpoint_id,
            'user_id': user_id,
            'state': 'submitted',
        })

    def update_task_state(self, task, state, error_message=None):
        """Cập nhật trạng thái của task."""
        task.write({
            'state': state,
            'error_message': error_message,
        })
        return task

    def add_artifact(self, task, name, artifact_type, content=None, file_data=None, file_name=None, mime_type=None):
        """Thêm artifact vào task."""
        try:
            artifact = self.env['eb_a2a.task.artifact'].create({
                'task_id': task.id,
                'name': name,
                'artifact_type': artifact_type,
                'content': content,
                'file_data': file_data,
                'file_name': file_name,
                'mime_type': mime_type,
            })
            return artifact
        except Exception as e:
            _logger.exception(f"Lỗi khi thêm artifact: {str(e)}")
            return None

    def process_task(self, agent_code, task_id, message_content, message_role='user'):
        """Xử lý task.

        Args:
            agent_code: Mã của agent
            task_id: ID của task
            message_content: Nội dung tin nhắn
            message_role: Vai trò của tin nhắn (mặc định: 'user')

        Returns:
            dict: Kết quả xử lý task
        """
        try:
            _logger.info(f"AgentServiceCrewAI.process_task - Starting with agent_code={agent_code}, task_id={task_id}")

            # Kiểm tra CrewAI có khả dụng không
            if not CREWAI_AVAILABLE:
                _logger.warning("CrewAI không khả dụng. Không thể xử lý task.")
                return {
                    'error': "CrewAI không khả dụng. Vui lòng cài đặt bằng: pip install crewai"
                }

            # Tìm agent endpoint
            agent_endpoint = self.env['eb_a2a.agent_endpoint'].search([('code', '=', agent_code)], limit=1)
            if not agent_endpoint:
                return {
                    'error': f"Không tìm thấy agent endpoint với mã {agent_code}"
                }

            # Tìm hoặc tạo task
            task = self.env['eb_a2a.task'].search([('task_id', '=', task_id)], limit=1)
            if not task:
                # Tạo task mới
                task = self.env['eb_a2a.task'].create({
                    'task_id': task_id,
                    'name': f"Task {task_id}",
                    'agent_endpoint_id': agent_endpoint.id,
                    'user_id': self.env.user.id,
                    'state': 'submitted',
                })

            # Kiểm tra trạng thái task
            if task.state in ['completed', 'failed', 'canceled']:
                # Đặt lại trạng thái
                task.state = 'submitted'

            # Thêm tin nhắn vào task
            self.add_message(task, message_role, message_content)

            # Cập nhật trạng thái task
            task.state = 'working'

            # Xử lý task dựa trên loại agent endpoint
            if agent_endpoint.deployment_type == 'external':
                # Xử lý agent bên ngoài
                result = self._process_external_agent(agent_endpoint, message_content)
            else:
                # Xử lý agent nội bộ
                try:
                    result = self._handle_task(task, message_content)
                except Exception as e:
                    _logger.exception(f"Lỗi khi xử lý task: {str(e)}")
                    # Trả về phản hồi mặc định nếu có lỗi
                    result = {
                        'response': "Xin chào! Tôi là trợ lý AI của hệ thống Odoo. Tôi có thể giúp bạn trả lời các câu hỏi và hỗ trợ bạn sử dụng hệ thống. Bạn cần hỗ trợ gì?",
                        'state': 'completed'
                    }

            # Cập nhật trạng thái task
            if result.get('state'):
                task.state = result['state']

            # Thêm message phản hồi từ agent
            if result.get('response'):
                self.add_message(task, 'agent', result['response'])

            # Format kết quả trả về
            return {
                'task_id': task.task_id,
                'state': task.state,
                'response': result.get('response', ''),
                'error': result.get('error', None)
            }
        except Exception as e:
            _logger.exception(f"Lỗi khi xử lý task: {str(e)}")
            return {
                'error': f"Lỗi khi xử lý task: {str(e)}"
            }

    def add_message(self, task, role, content):
        """Thêm message vào task."""
        try:
            # Đảm bảo content là chuỗi
            if not isinstance(content, str):
                content = str(content)

            # Xác định author_id dựa trên role
            author_id = False
            if role == 'user':
                author_id = task.user_id.partner_id.id
            elif role == 'agent':
                author_id = task.agent_endpoint_id.partner_id.id if task.agent_endpoint_id.partner_id else False

            # Gửi tin nhắn vào task
            task_message = task.with_context(
                mail_create_nosubscribe=True,
                mail_auto_delete=False,
                mail_notify_author=False,
                mail_create_nolog=True,
                tracking_disable=True,
                mail_notrack=True,
                mail_create_force_send=False,
            ).sudo().message_post(
                body=content,
                message_type='comment',
                subtype_xmlid='mail.mt_comment',
                author_id=author_id,
                email_from='<EMAIL>',
            )

            # Tạo TextPart mặc định
            default_parts = [{
                'type': 'text',
                'text': content
            }]

            # Lưu trữ thông tin parts và role
            self.env.cr.execute(
                "UPDATE mail_message SET a2a_parts = %s, a2a_role = %s WHERE id = %s",
                (json.dumps(default_parts), role, task_message.id)
            )

            # Cập nhật trạng thái task nếu cần
            if task.state == 'input_required' and role == 'user':
                task.state = 'working'

            return task_message
        except Exception as e:
            _logger.exception(f"Lỗi khi thêm message: {str(e)}")
            return None

    def _process_external_agent(self, agent_endpoint, message_content):
        """Xử lý task cho agent bên ngoài bằng cách gọi API.

        Args:
            agent_endpoint: Bản ghi agent endpoint
            message_content: Nội dung message

        Returns:
            Dict chứa kết quả xử lý
        """
        # Kiểm tra endpoint_url
        if not agent_endpoint.endpoint_url:
            raise ValueError("Agent endpoint bên ngoài phải có endpoint_url")

        # Hiện tại chỉ trả về kết quả giả lập
        return {
            'response': f"[External Agent] Đã nhận yêu cầu: {message_content}",
            'state': 'completed'
        }

    def _handle_task(self, task, message_content):
        """Xử lý task dựa trên nội dung message sử dụng CrewAI."""
        # Lấy agent endpoint
        agent_endpoint = task.agent_endpoint_id
        _logger.info(f"AgentServiceCrewAI._handle_task - Processing task for agent_endpoint_id={agent_endpoint.id}, deployment_type={agent_endpoint.deployment_type}, framework={agent_endpoint.framework}")

        if agent_endpoint.deployment_type == 'multi':
            _logger.info(f"AgentServiceCrewAI._handle_task - Multi-agent with crew_registry_id={agent_endpoint.crew_registry_id and agent_endpoint.crew_registry_id.id}")
            if agent_endpoint.crew_registry_id:
                _logger.info(f"AgentServiceCrewAI._handle_task - Using crew_registry: name={agent_endpoint.crew_registry_id.name}, module_path={agent_endpoint.crew_registry_id.module_path}, function_name={agent_endpoint.crew_registry_id.function_name}")

        # Sử dụng CrewAI để trả lời
        try:
            # Lấy LLM
            _logger.info(f"AgentServiceCrewAI._handle_task - Getting LLM for agent endpoint")
            llm = self._get_llm(agent_endpoint)
            _logger.info(f"AgentServiceCrewAI._handle_task - LLM created: {llm is not None}")

            # Lấy lịch sử hội thoại
            history = []
            for message in task.message_ids.sorted('id'):
                if message.a2a_role in ['user', 'agent']:
                    history.append({
                        'role': 'user' if message.a2a_role == 'user' else 'assistant',
                        'content': message.body
                    })

            # Tạo context
            context = {
                'history': history
            }

            # Xử lý dựa trên loại agent endpoint
            if agent_endpoint.deployment_type == 'multi':
                # Kiểm tra xem có crew_registry_id không
                if agent_endpoint.crew_registry_id:
                    # Sử dụng crew_registry_id
                    crew_registry = agent_endpoint.crew_registry_id
                    try:
                        # Import module và hàm tạo crew
                        _logger.info(f"AgentServiceCrewAI._handle_task - Importing module {crew_registry.module_path}")
                        module = importlib.import_module(crew_registry.module_path)
                        _logger.info(f"AgentServiceCrewAI._handle_task - Getting function {crew_registry.function_name}")
                        create_crew_func = getattr(module, crew_registry.function_name)
                        _logger.info(f"AgentServiceCrewAI._handle_task - Function found: {create_crew_func}")

                        # Tạo Crew
                        _logger.info(f"AgentServiceCrewAI._handle_task - Creating crew with function {crew_registry.function_name}, process_type={crew_registry.process_type}")
                        crew = create_crew_func(self.env, llm=llm, process_type=crew_registry.process_type)
                        _logger.info(f"AgentServiceCrewAI._handle_task - Crew created: {crew}")

                        # Kiểm tra xem có cần tạo manager agent không (cho Process.hierarchical)
                        if crew_registry.process_type == 'hierarchical' and crew_registry.manager_agent_module_path and crew_registry.manager_agent_function_name:
                            try:
                                # Import module và hàm tạo manager agent
                                _logger.info(f"AgentServiceCrewAI._handle_task - Importing manager agent module {crew_registry.manager_agent_module_path}")
                                manager_module = importlib.import_module(crew_registry.manager_agent_module_path)
                                _logger.info(f"AgentServiceCrewAI._handle_task - Getting manager agent function {crew_registry.manager_agent_function_name}")
                                create_manager_func = getattr(manager_module, crew_registry.manager_agent_function_name)

                                # Tạo manager agent
                                _logger.info(f"AgentServiceCrewAI._handle_task - Creating manager agent")
                                manager_agent = create_manager_func(self.env, llm=llm)
                                _logger.info(f"AgentServiceCrewAI._handle_task - Manager agent created: {manager_agent}")

                                # Cập nhật manager_agent cho crew
                                crew.manager_agent = manager_agent
                                _logger.info(f"AgentServiceCrewAI._handle_task - Manager agent set for crew")
                            except Exception as e:
                                _logger.warning(f"Không thể tạo manager agent: {str(e)}")
                                # Tiếp tục mà không có manager agent, sẽ sử dụng Process.sequential

                        # Kickoff crew với message_content
                        _logger.info(f"AgentServiceCrewAI._handle_task - Starting crew kickoff with message: {message_content[:50]}...")
                        result = crew.kickoff(inputs={'query': message_content, 'context': context})
                        _logger.info(f"AgentServiceCrewAI._handle_task - Crew kickoff completed with result: {result}")

                        # Chuyển đổi CrewOutput thành chuỗi
                        result_str = str(result) if result is not None else ""

                        return {
                            'response': result_str,
                            'state': 'completed'
                        }
                    except (ImportError, AttributeError) as e:
                        _logger.warning(f"Không thể import hàm tạo crew: {str(e)}")
                        return {
                            'response': f"Xin lỗi, tôi không thể xử lý yêu cầu của bạn vì không thể tạo crew. Lỗi: {str(e)}",
                            'state': 'completed'
                        }
                else:
                    return {
                        'response': "Xin lỗi, tôi không thể xử lý yêu cầu của bạn vì agent không có crew_registry_id.",
                        'state': 'completed'
                    }
            else:
                # Kiểm tra xem module eb_a2a_lms có được cài đặt không
                try:
                    # Thử import create_agent từ eb_a2a_lms
                    try:
                        module_name = f"odoo.addons.eb_a2a_lms.agents.{agent_endpoint.agent_implementation}_agent"
                        module = importlib.import_module(module_name)
                        create_agent_func = getattr(module, f"create_{agent_endpoint.agent_implementation}_agent")
                    except (ImportError, AttributeError):
                        # Fallback to default agent
                        from ..agents.base_agents import create_default_agent
                        create_agent_func = create_default_agent

                    # Tạo agent
                    agent_obj = create_agent_func(self.env, llm=llm)

                    # Tạo Task
                    agent_task = Task(
                        description=f"Trả lời yêu cầu: {message_content}",
                        expected_output="Phản hồi chi tiết và hữu ích cho yêu cầu của người dùng",
                        agent=agent_obj
                    )

                    # Tạo Crew đơn giản
                    crew = Crew(
                        agents=[agent_obj],
                        tasks=[agent_task],
                        process=Process.sequential,
                        verbose=True
                    )

                    # Kickoff crew
                    result = crew.kickoff(inputs={'query': message_content, 'context': context})

                    # Chuyển đổi CrewOutput thành chuỗi
                    result_str = str(result) if result is not None else ""

                    return {
                        'response': result_str,
                        'state': 'completed'
                    }
                except (ImportError, AttributeError) as e:
                    _logger.warning(f"Không thể import create_course_agent từ eb_a2a_lms: {str(e)}")
                    return {
                        'response': "Xin lỗi, tôi không thể xử lý yêu cầu của bạn vì module LMS chưa được cài đặt.",
                        'state': 'completed'
                    }
        except Exception as e:
            _logger.warning(f"Lỗi khi sử dụng CrewAI: {str(e)}")

            # Trả về phản hồi mặc định nếu có lỗi
            return {
                'response': "Xin chào! Tôi là trợ lý AI của hệ thống Odoo. Tôi có thể giúp bạn trả lời các câu hỏi và hỗ trợ bạn sử dụng hệ thống. Bạn cần hỗ trợ gì?",
                'state': 'completed'
            }

    def _prepare_context(self, task, agent_endpoint):
        """Chuẩn bị context cho AI.

        Phương thức này được thêm vào để tương thích với AgentService.

        Args:
            task: Bản ghi task
            agent_endpoint: Bản ghi agent endpoint

        Returns:
            dict: Context cho AI
        """
        _logger.info(f"AgentServiceCrewAI._prepare_context - Preparing context for task_id={task.task_id}")

        # Lấy lịch sử hội thoại
        history = []
        for message in task.message_ids.sorted('id'):
            if message.a2a_role in ['user', 'agent']:
                history.append({
                    'role': 'user' if message.a2a_role == 'user' else 'assistant',
                    'content': message.body
                })

        # Tạo context
        context = {
            'history': history,
            'system_message': agent_endpoint.system_message or "Bạn là trợ lý AI của hệ thống Odoo. Hãy trả lời các câu hỏi của người dùng một cách chính xác, hữu ích và thân thiện."
        }

        _logger.info(f"AgentServiceCrewAI._prepare_context - Context prepared with {len(history)} messages")
        return context

    def _get_ai_adapter(self, agent_endpoint):
        """Lấy AI adapter cho agent endpoint.

        Phương thức này được thêm vào để tương thích với AgentService.
        Trong thực tế, AgentServiceCrewAI không sử dụng AI adapter mà sử dụng LLM.

        Args:
            agent_endpoint: Bản ghi agent endpoint

        Returns:
            AIAdapter: Instance của AIAdapter hoặc None nếu có lỗi
        """
        _logger.info(f"AgentServiceCrewAI._get_ai_adapter - Called for agent_endpoint_id={agent_endpoint.id}")

        # Lấy provider
        provider = agent_endpoint.provider_id
        if not provider:
            # Tìm provider mặc định
            provider = self.env['eb_a2a.provider'].search([('is_default', '=', True)], limit=1)
            if not provider:
                # Tìm provider đầu tiên
                provider = self.env['eb_a2a.provider'].search([], limit=1)
                if not provider:
                    return None

        # Import create_ai_adapter từ module ai_adapter
        try:
            from odoo.addons.eb_a2a.services.ai_adapter import create_ai_adapter

            # Lấy model mặc định
            default_model = self.env['eb_a2a.provider.model'].search([
                ('provider_id', '=', provider.id),
                ('is_default', '=', True),
                ('model_type', 'in', ['chat', 'multimodal'])
            ], limit=1)

            model = default_model.model_id if default_model else "gpt-4o"

            # Tạo AI adapter
            ai_adapter = create_ai_adapter(
                provider.api_type,
                api_key=provider.api_key,
                model=model,
                api_base=provider.api_base,
                api_version=provider.api_version
            )

            return ai_adapter
        except Exception as e:
            _logger.exception(f"Lỗi khi tạo AI adapter: {str(e)}")
            return None

    def _get_llm(self, agent_endpoint):
        """Lấy LLM cho agent endpoint.

        Args:
            agent_endpoint: Bản ghi agent endpoint

        Returns:
            LLM: Instance của LLM hoặc None nếu có lỗi
        """
        # Nếu đã có LLM trong cache, trả về
        if self.llm:
            return self.llm

        # Lấy provider
        provider = agent_endpoint.provider_id
        if not provider:
            # Tìm provider mặc định
            provider = self.env['eb_a2a.provider'].search([('is_default', '=', True)], limit=1)
            if not provider:
                # Tìm provider đầu tiên
                provider = self.env['eb_a2a.provider'].search([], limit=1)
                if not provider:
                    return None

        # Tạo LLM
        try:
            self.llm = create_llm_for_crewai(provider)
            return self.llm
        except Exception as e:
            _logger.exception(f"Lỗi khi tạo LLM: {str(e)}")
            return None
