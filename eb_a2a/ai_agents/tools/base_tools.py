# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging
import functools
from typing import Dict, List, Any, Optional, Union, Callable

try:
    from crewai.tools import BaseTool, tool as crewai_tool
    CREWAI_AVAILABLE = True
except ImportError:
    logging.getLogger(__name__).warning("CrewAI không khả dụng. Cài đặt bằng: pip install crewai")
    CREWAI_AVAILABLE = False
    # Tạo class giả để tránh lỗi khi import
    class BaseTool:
        def __init__(self, name=None, description=None):
            self.name = name
            self.description = description

        def _run(self, *args, **kwargs):
            pass

    # Tạo hàm giả cho crewai_tool
    def crewai_tool(*args, **kwargs):
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                return func(*args, **kwargs)
            return wrapper
        return decorator

_logger = logging.getLogger(__name__)


def Tool(name=None, description=None, func=None):
    """Tạo tool cho CrewAI.

    Args:
        name: Tên của tool
        description: Mô tả về tool
        func: Hàm thực thi của tool

    Returns:
        BaseTool: Tool cho CrewAI
    """
    # Tạo decorator cho tool
    def decorator(f):
        # Sử dụng crewai_tool để tạo tool
        @crewai_tool
        @functools.wraps(f)
        def wrapper(*args, **kwargs):
            return f(*args, **kwargs)

        # Cập nhật tên và mô tả nếu có
        if name:
            wrapper.name = name
        if description:
            wrapper.description = description

        return wrapper

    if func is None:
        # Nếu không có func, trả về decorator
        return decorator

    # Nếu có func, trả về tool đã được tạo
    return decorator(func)


def create_tools(env, agent_type):
    """Tạo danh sách các tool cho agent.

    Args:
        env: Odoo environment
        agent_type: Loại agent

    Returns:
        list: Danh sách các tool
    """
    # Mặc định không có tool nào
    return []
