# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging
from typing import Dict, List, Any, Optional, Union

try:
    from crewai import Agent
    CREWAI_AVAILABLE = True
except ImportError:
    logging.getLogger(__name__).warning("CrewAI không khả dụng. Cài đặt bằng: pip install crewai")
    CREWAI_AVAILABLE = False
    # Tạo class giả để tránh lỗi khi import
    class Agent:
        def __init__(self, role=None, goal=None, backstory=None, verbose=None, llm=None, tools=None):
            self.role = role
            self.goal = goal
            self.backstory = backstory
            self.verbose = verbose
            self.llm = llm
            self.tools = tools

_logger = logging.getLogger(__name__)


def create_default_agent(env, llm=None):
    """Tạo agent mặc định.

    Args:
        env: Odoo environment
        llm: Language Model (tù<PERSON> chọn)

    Returns:
        Agent: CrewAI Agent
    """
    if not CREWAI_AVAILABLE:
        _logger.warning("CrewAI không khả dụng. Không thể tạo agent.")
        return None

    return Agent(
        role="General Assistant",
        goal="Hỗ trợ người dùng với các yêu cầu chung",
        backstory="Bạn là trợ lý AI, luôn sẵn sàng giúp đỡ người dùng với các yêu cầu khác nhau.",
        verbose=True,
        llm=llm
    )
