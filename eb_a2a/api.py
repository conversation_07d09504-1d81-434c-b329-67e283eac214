# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging
from fastapi import APIRouter
from odoo.addons.eb_api_core.utils.decorators import register_api_module

_logger = logging.getLogger(__name__)


@register_api_module(
    module_name="a2a",
    description="AI Agent API for chatbot and AI assistants",
    version="1.0",
    app_name="a2a_api",
)
class A2AAPI:
    """API module for A2A endpoints"""

    # Router instance
    router = APIRouter()

    # Import routers để đăng ký endpoints
    # Router cho API public (không cần xác thực)
    from odoo.addons.eb_a2a.public_api.routers.public_router import public_router

    # Đăng ký sub-routers với các tag khác nhau
    # API public
    router.include_router(public_router, tags=["public"])
