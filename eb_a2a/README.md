# EB A2A - <PERSON><PERSON><PERSON> hợp AI Agent cho <PERSON><PERSON><PERSON> tích hợp AI Agent đơn gi<PERSON>n cho <PERSON>, sử dụng CrewAI framework và hỗ trợ OpenAI Compatible API.

## Giới thiệu

EB A2A (Agent-to-Agent) là module tích hợp AI và<PERSON>, cho phép tạo và quản lý các agent AI thông minh có khả năng tương tác với người dùng thông qua giao diện chat hoặc API. Module này được thiết kế để dễ dàng mở rộng và tích hợp với các framework AI khác nhau.

Hiện tại, module hỗ trợ CrewAI framework và có thể được mở rộng để hỗ trợ các framework khác như OpenManus trong tương lai. Module cũng hỗ trợ các API tương thích với OpenAI, bao gồm OpenAI ch<PERSON>h thức, <PERSON>zure OpenAI, hoặc các mô hình mã nguồn mở tự host.

## T<PERSON>h năng chính

- **Quản lý Agent AI**: Tạo và quản lý các agent AI với các vai trò và khả năng khác nhau
- **Quản lý Task**: Theo dõi và quản lý các task được gửi đến agent
- **Xử lý Message**: Lưu trữ và xử lý các message giữa người dùng và agent
- **Tích hợp CrewAI**: Sử dụng CrewAI framework để tạo các agent thông minh
- **Hỗ trợ Multi-Agent**: Tạo và quản lý hệ thống multi-agent với CrewAI
- **Tích hợp OpenAI**: Hỗ trợ OpenAI và Azure OpenAI
- **Giao diện Chat**: Tương tác với agent thông qua giao diện chat
- **API đầy đủ**: Cung cấp API để tích hợp với các ứng dụng khác

## Cài đặt

### Yêu cầu

- Odoo 18.0
- Python 3.10+
- Module `eb_api_core` (nếu sử dụng API)
- Thư viện Python: `openai>=1.0.0`, `requests>=2.0.0`, `crewai>=0.114.0`

### Các bước cài đặt

1. Đặt thư mục `eb_a2a` vào thư mục `extra-addons` của Odoo
2. Cài đặt các dependency:
   ```bash
   pip install openai requests crewai
   ```
3. Cập nhật danh sách module: Đi đến **Apps** > Nhấn nút **Update Apps List**
4. Tìm kiếm và cài đặt module "EB A2A Integration"

## Cấu hình

### Cấu hình Provider AI

1. Truy cập **A2A > Providers**
2. Tạo provider mới với thông tin API key và cấu hình:
   - **Tên**: Tên của provider (ví dụ: OpenAI, Azure OpenAI)
   - **Mã**: Mã định danh của provider (ví dụ: openai, azure)
   - **Loại API**: Loại API (openai hoặc azure)
   - **API Key**: Nhập API key
   - **API Base**: URL của API endpoint (tùy chọn, để trống nếu sử dụng OpenAI chính thức)
   - **API Version**: Phiên bản API (chỉ cần thiết cho Azure OpenAI)
   - **Organization ID**: ID tổ chức (tùy chọn, chỉ cho OpenAI)

### Cấu hình Agent

1. Truy cập **A2A > Agents**
2. Tạo agent mới:
   - **Tên Agent**: Tên hiển thị của agent
   - **Mã Agent**: Mã định danh duy nhất của agent
   - **Mô tả**: Mô tả về agent và khả năng của nó
   - **Loại Agent**: Nội bộ (xử lý trong Odoo) hoặc Bên ngoài (gọi API bên ngoài)
   - **Provider AI**: Chọn provider AI đã tạo
   - **Framework**: Chọn framework AI (CrewAI)
   - **System Message**: Thiết lập thông điệp hệ thống cho AI

## Sử dụng

### Sử dụng AI Assistant

1. Truy cập **A2A > AI Assistant**
2. Sử dụng giao diện chat để tương tác với agent:
   - Nhập câu hỏi hoặc yêu cầu vào ô nhập liệu
   - Nhấn Enter hoặc nút gửi để gửi message
   - Xem phản hồi từ agent trong cửa sổ chat

### Quản lý Task

1. Truy cập **A2A > Tasks**
2. Xem danh sách các task đã gửi đến agent
3. Nhấp vào task để xem chi tiết:
   - Xem lịch sử message
   - Kiểm tra trạng thái task
   - Xem các artifact được tạo ra (nếu có)

### Sử dụng API

Module cung cấp các API sau:

- **Agent Card Discovery**:
  ```
  GET /.well-known/agent.json?agent_code=<agent_code>
  ```

- **Gửi Task**:
  ```
  POST /api/a2a/tasks/send

  {
    "agent_code": "<agent_code>",
    "task_id": "<task_id>",
    "message": "<message_content>"
  }
  ```

- **Lấy Thông Tin Task**:
  ```
  GET /api/a2a/tasks/<task_id>?agent_code=<agent_code>
  ```

- **Hủy Task**:
  ```
  POST /api/a2a/tasks/<task_id>/cancel?agent_code=<agent_code>
  ```

## Kiến trúc Module

### Cấu trúc thư mục

```
eb_a2a/
├── models/                  # Các model Odoo
│   ├── a2a_provider.py      # Quản lý các nhà cung cấp AI
│   ├── a2a_agent.py         # Quản lý các agent AI
│   ├── a2a_task.py          # Quản lý các task
│   └── mail_message.py      # Mở rộng model mail.message
├── controllers/             # Các controller Odoo
│   └── a2a_controller.py    # Xử lý các endpoint API
├── services/                # Các service
│   ├── agent_service.py     # Service xử lý agent (phiên bản cũ)
│   ├── ai_adapter.py        # Adapter cho các dịch vụ AI
│   └── openai_llm.py        # Tích hợp với OpenAI
├── ai_agents/               # Các agent AI
│   ├── services/            # Các service cho AI agents
│   │   ├── agent_service.py # Service xử lý agent với CrewAI
│   │   └── crewai_adapter.py # Adapter cho CrewAI
│   ├── agents/              # Các agent
│   │   └── base_agents.py   # Định nghĩa cơ bản cho agents
│   ├── crews/               # Các crew
│   │   └── base_crews.py    # Định nghĩa cơ bản cho crews
│   └── tools/               # Các tool
│       └── base_tools.py    # Định nghĩa cơ bản cho tools
└── data/                    # Dữ liệu
    ├── update_agents.py     # Script cập nhật agent
    └── update_enhanced_agents.py # Script cập nhật agent nâng cao
```

### Luồng xử lý

1. **Người dùng gửi yêu cầu** thông qua giao diện chat hoặc API
2. **Controller** nhận yêu cầu và chuyển đến service phù hợp
3. **Service** xử lý yêu cầu:
   - Nếu agent sử dụng CrewAI, `AgentServiceCrewAI` được sử dụng
   - Nếu không, `AgentService` được sử dụng
4. **Service** tạo hoặc cập nhật task và gửi yêu cầu đến agent
5. **Agent** xử lý yêu cầu và trả về kết quả
6. **Service** lưu kết quả và trả về cho người dùng

## Mở rộng Module

### Thêm Framework AI mới

Module được thiết kế để dễ dàng mở rộng với các framework AI mới. Dưới đây là các bước để thêm framework mới:

1. **Cập nhật model Agent**:
   ```python
   # Trong file a2a_agent.py
   framework = fields.Selection([
       ('crewai', 'CrewAI'),
       ('openmanus', 'OpenManus'),  # Thêm framework mới
   ], string="Framework", default='crewai', required=True)
   ```

2. **Tạo adapter cho framework mới**:
   ```python
   # Tạo file ai_agents/services/openmanus_adapter.py
   def create_llm_for_openmanus(provider):
       """Tạo LLM cho OpenManus dựa trên provider."""
       # Implement adapter logic
       return llm
   ```

3. **Tạo service cho framework mới**:
   ```python
   # Tạo file ai_agents/services/openmanus_service.py
   class OpenManusService:
       """Service class để xử lý các tác vụ của agent sử dụng OpenManus."""
       
       def __init__(self, env):
           self.env = env
           
       def process_task(self, agent_code, task_id, message_content):
           # Implement task processing logic
           return result
   ```

4. **Cập nhật controller để sử dụng service mới**:
   ```python
   # Trong file controllers/a2a_controller.py
   if agent.framework == 'openmanus':
       from odoo.addons.eb_a2a.ai_agents.services.openmanus_service import OpenManusService
       service = OpenManusService(request.env)
   ```

### Tạo Agent và Tool mới

Bạn có thể tạo các agent và tool mới để mở rộng khả năng của hệ thống:

1. **Tạo Agent mới**:
   ```python
   # Tạo file ai_agents/agents/custom_agent.py
   from odoo.addons.eb_a2a.ai_agents.agents.base_agents import create_default_agent
   
   def create_custom_agent(env, llm=None):
       """Tạo agent tùy chỉnh."""
       agent = create_default_agent(env, llm)
       # Tùy chỉnh agent
       agent.role = "Custom Agent"
       agent.goal = "Mục tiêu tùy chỉnh"
       return agent
   ```

2. **Tạo Tool mới**:
   ```python
   # Tạo file ai_agents/tools/custom_tools.py
   from odoo.addons.eb_a2a.ai_agents.tools.base_tools import Tool
   
   @Tool(
       name="custom_tool",
       description="Công cụ tùy chỉnh"
   )
   def custom_tool(query):
       """Thực hiện tác vụ tùy chỉnh."""
       # Implement tool logic
       return "Kết quả từ công cụ tùy chỉnh"
   ```

## Ví dụ Sử Dụng

### Tìm kiếm thông tin

```
User: Tôi muốn biết thêm về khóa học Python
Agent: Tôi có thể giúp bạn tìm hiểu về các khóa học Python. Chúng tôi có các khóa học sau:

1. Python cơ bản:
   - Dành cho người mới bắt đầu
   - Thời lượng: 30 giờ
   - Học phí: 2,500,000 VND

2. Python nâng cao:
   - Yêu cầu kiến thức Python cơ bản
   - Thời lượng: 40 giờ
   - Học phí: 3,500,000 VND

3. Data Science với Python:
   - Dành cho người đã có kiến thức Python
   - Thời lượng: 50 giờ
   - Học phí: 4,500,000 VND

Bạn quan tâm đến khóa học nào? Tôi có thể cung cấp thêm thông tin chi tiết.
```

### Hỏi đáp phức tạp

```
User: Tôi muốn đăng ký khóa học Python nhưng không biết nên chọn khóa nào phù hợp với trình độ của tôi. Tôi đã có kinh nghiệm lập trình với Java.

Agent: Với kinh nghiệm lập trình Java, bạn đã có nền tảng về lập trình hướng đối tượng và các khái niệm cơ bản về lập trình. Dựa vào đó, tôi có một số gợi ý:

1. Bạn có thể bỏ qua khóa "Python cơ bản" vì nó chủ yếu dành cho người mới bắt đầu lập trình.

2. "Python nâng cao" sẽ phù hợp với bạn. Khóa học này sẽ giúp bạn:
   - Nhanh chóng làm quen với cú pháp Python
   - Học các tính năng đặc biệt của Python mà Java không có
   - Hiểu sâu về các thư viện và framework phổ biến

3. Nếu bạn quan tâm đến phân tích dữ liệu, "Data Science với Python" cũng là lựa chọn tốt, nhưng bạn nên dành thời gian tự học Python cơ bản trước.

Tôi khuyên bạn nên đăng ký khóa "Python nâng cao" và dành 1-2 tuần tự học Python cơ bản trước khi khóa học bắt đầu. Chúng tôi có thể cung cấp tài liệu học trước cho bạn.

Bạn có muốn biết thêm thông tin về lịch học và giảng viên của khóa "Python nâng cao" không?
```

## Giấy phép

LGPL-3
