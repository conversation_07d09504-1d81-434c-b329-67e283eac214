<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <!-- <PERSON><PERSON><PERSON><PERSON> bảo mật -->
        <record id="group_a2a_user" model="res.groups">
            <field name="name">A2A User</field>
            <field name="category_id" ref="base.module_category_hidden"/>
        </record>

        <record id="group_a2a_manager" model="res.groups">
            <field name="name">A2A Manager</field>
            <field name="category_id" ref="base.module_category_hidden"/>
            <field name="implied_ids" eval="[(4, ref('group_a2a_user'))]"/>
            <field name="users" eval="[(4, ref('base.user_admin'))]"/>
        </record>

        <!-- Quy tắc bảo mật -->
        <record id="rule_a2a_agent_endpoint_user" model="ir.rule">
            <field name="name">A2A Agent Endpoint: User can see all agent endpoints</field>
            <field name="model_id" ref="model_eb_a2a_agent_endpoint"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_a2a_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <record id="rule_a2a_agent_endpoint_manager" model="ir.rule">
            <field name="name">A2A Agent Endpoint: Manager can manage all agent endpoints</field>
            <field name="model_id" ref="model_eb_a2a_agent_endpoint"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_a2a_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>

        <record id="rule_a2a_task_user" model="ir.rule">
            <field name="name">A2A Task: User can see own tasks</field>
            <field name="model_id" ref="model_eb_a2a_task"/>
            <field name="domain_force">[('user_id', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('group_a2a_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <record id="rule_a2a_task_manager" model="ir.rule">
            <field name="name">A2A Task: Manager can manage all tasks</field>
            <field name="model_id" ref="model_eb_a2a_task"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_a2a_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
    </data>
</odoo>
