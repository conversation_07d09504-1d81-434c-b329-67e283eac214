# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from pydantic import BaseModel, Field
from odoo.addons.eb_api_core.schemas.base import ResponseBase, PaginatedResponse

# Chat Session Schemas
class ChatSessionCreate(BaseModel):
    """Schema cho việc tạo phiên chat mới"""
    agent_code: Optional[str] = Field(None, description="Mã của agent (tùy chọn, sẽ lấy từ cấu hình website nếu không có)")
    website_code: Optional[str] = Field(None, description="Mã của website (dùng để lấy cấu hình)")
    session_id: Optional[str] = Field(None, description="ID phiên chat (t<PERSON><PERSON> chọn, sẽ tự động tạo nếu không có)")
    name: Optional[str] = Field(None, description="Tên phiên chat")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Metadata cho phiên chat")

class ChatSessionData(BaseModel):
    """Schema cho dữ liệu phiên chat"""
    session_id: str
    created_at: datetime
    status: str
    metadata: Optional[Dict[str, Any]] = None

class ChatSessionResponse(ResponseBase):
    """Schema cho response khi tạo phiên chat"""
    data: Optional[ChatSessionData] = None

class ChatSessionListResponse(PaginatedResponse):
    """Schema cho response khi lấy danh sách phiên chat"""
    data: List[ChatSessionData] = []

# Chat Message Schemas
class ChatMessageCreate(BaseModel):
    """Schema cho việc tạo tin nhắn mới"""
    content: str = Field(..., description="Nội dung tin nhắn")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Metadata cho tin nhắn")

class ChatMessageData(BaseModel):
    """Schema cho dữ liệu tin nhắn"""
    message_id: str
    session_id: str
    content: str
    role: str
    created_at: datetime
    metadata: Optional[Dict[str, Any]] = None

class ChatMessageResponse(ResponseBase):
    """Schema cho response khi gửi tin nhắn"""
    data: Optional[ChatMessageData] = None

class ChatMessageListResponse(ResponseBase):
    """Schema cho response khi lấy danh sách tin nhắn"""
    data: List[ChatMessageData] = []

# Chat Feedback Schemas
class ChatFeedbackCreate(BaseModel):
    """Schema cho việc gửi feedback"""
    message_id: str = Field(..., description="ID tin nhắn cần feedback")
    rating: int = Field(..., ge=1, le=5, description="Đánh giá (1-5)")
    comment: Optional[str] = Field(None, description="Bình luận")

class ChatFeedbackData(BaseModel):
    """Schema cho dữ liệu feedback"""
    feedback_id: str
    message_id: str
    rating: int
    comment: Optional[str] = None
    created_at: datetime

class ChatFeedbackResponse(ResponseBase):
    """Schema cho response khi gửi feedback"""
    data: Optional[ChatFeedbackData] = None
