# Chatbot Public API

API công khai cho chatbot chăm sóc khách hàng.

## Endpoints

### Chat Sessions

- `POST /api/v1/a2a/public/chatbot/sessions`: Tạo phiên chat mới
- `GET /api/v1/a2a/public/chatbot/sessions/{session_id}`: L<PERSON><PERSON> thông tin phiên chat
- `DELETE /api/v1/a2a/public/chatbot/sessions/{session_id}`: K<PERSON>t thúc phiên chat

### Chat Messages

- `POST /api/v1/a2a/public/chatbot/sessions/{session_id}/messages`: Gửi tin nhắn mới
- `GET /api/v1/a2a/public/chatbot/sessions/{session_id}/messages`: L<PERSON><PERSON> lịch sử tin nhắn

### Chat Feedback

- `POST /api/v1/a2a/public/chatbot/messages/{message_id}/feedback`: Gửi feedback cho tin nhắn

### Chat Config

- `GET /api/v1/a2a/public/chatbot/config`: L<PERSON><PERSON> c<PERSON><PERSON> hình chatbot cho website

## Authentication

API này là public, không yêu cầu authentication. <PERSON><PERSON>hi<PERSON>, có thể sử dụng API key để bảo vệ API khỏi spam.

## Rate Limiting

API có rate limiting để tránh lạm dụng:
- 60 requests/phút cho mỗi IP
- 10 requests/phút cho mỗi session_id

## Ví dụ sử dụng

### Lấy cấu hình chatbot

```javascript
// Sử dụng fetch API
fetch('/api/v1/a2a/public/chatbot/config?website_code=my_website', {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json',
  }
})
.then(response => response.json())
.then(data => {
  console.log('Success:', data);
  // Lưu cấu hình để sử dụng sau này
  const config = data.data;
})
.catch((error) => {
  console.error('Error:', error);
});
```

### Tạo phiên chat mới

```javascript
// Sử dụng fetch API
fetch('/api/v1/a2a/public/chatbot/sessions', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    website_code: 'my_website', // Lấy agent_code từ cấu hình website
    name: 'Website Chat Session',
    metadata: {
      source: 'website',
      page: '/products'
    }
  }),
})
.then(response => response.json())
.then(data => {
  console.log('Success:', data);
  // Lưu session_id để sử dụng sau này
  const sessionId = data.data.session_id;
})
.catch((error) => {
  console.error('Error:', error);
});
```

### Gửi tin nhắn

```javascript
// Sử dụng fetch API
fetch(`/api/v1/a2a/public/chatbot/sessions/${sessionId}/messages`, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    content: 'Xin chào, tôi cần hỗ trợ về sản phẩm của bạn',
    metadata: {
      source: 'website',
      page: '/products'
    }
  }),
})
.then(response => response.json())
.then(data => {
  console.log('Success:', data);
  // Hiển thị phản hồi từ agent
  const agentResponse = data.data.content;
})
.catch((error) => {
  console.error('Error:', error);
});
```

### Lấy lịch sử tin nhắn

```javascript
// Sử dụng fetch API
fetch(`/api/v1/a2a/public/chatbot/sessions/${sessionId}/messages`, {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json',
  }
})
.then(response => response.json())
.then(data => {
  console.log('Success:', data);
  // Hiển thị lịch sử tin nhắn
  const messages = data.data;
})
.catch((error) => {
  console.error('Error:', error);
});
```

### Gửi feedback

```javascript
// Sử dụng fetch API
fetch(`/api/v1/a2a/public/chatbot/messages/${messageId}/feedback`, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    rating: 5,
    comment: 'Phản hồi rất hữu ích!'
  }),
})
.then(response => response.json())
.then(data => {
  console.log('Success:', data);
})
.catch((error) => {
  console.error('Error:', error);
});
```

## Tích hợp với website

### Thêm widget vào website

```html
<div id="chatbot-widget">
  <div id="chatbot-header">
    <h3>Hỗ trợ trực tuyến</h3>
    <button id="chatbot-toggle">X</button>
  </div>
  <div id="chatbot-messages"></div>
  <div id="chatbot-input">
    <input type="text" id="chatbot-message" placeholder="Nhập tin nhắn...">
    <button id="chatbot-send">Gửi</button>
  </div>
</div>

<script>
  // Khởi tạo chatbot
  const chatbot = new Chatbot({
    apiUrl: '/api/v1/a2a/public',
    websiteCode: 'my_website' // Lấy cấu hình từ website
  });

  // Xử lý sự kiện gửi tin nhắn
  document.getElementById('chatbot-send').addEventListener('click', () => {
    const message = document.getElementById('chatbot-message').value;
    chatbot.sendMessage(message);
  });
</script>
```
