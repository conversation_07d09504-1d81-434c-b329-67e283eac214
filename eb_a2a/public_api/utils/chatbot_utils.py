# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import json
import uuid
import logging
from typing import Dict, Any, Optional

_logger = logging.getLogger(__name__)

def generate_session_id() -> str:
    """Tạo ID phiên chat mới."""
    return str(uuid.uuid4())

def get_agent_endpoint(env, agent_code: str):
    """Lấy agent endpoint từ agent_code."""
    return env["eb_a2a.agent_endpoint"].sudo().search([("code", "=", agent_code)], limit=1)

def create_chat_task(env, agent_endpoint_id: int, session_id: str, name: Optional[str] = None, metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """Tạo task mới cho phiên chat."""
    try:
        # Tạo task mới
        task = env["eb_a2a.task"].sudo().create({
            "task_id": session_id,
            "agent_endpoint_id": agent_endpoint_id,
            "state": "submitted",
            "name": name or "Website Chat Session",
            "is_chatbot_session": True,
            "chatbot_metadata": json.dumps(metadata or {})
        })
        
        return {
            "success": True,
            "task": task,
            "session_id": task.task_id,
            "created_at": task.create_date,
            "status": task.state
        }
    except Exception as e:
        _logger.exception(f"Error creating chat task: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

def process_chat_message(env, task, message_content: str) -> Dict[str, Any]:
    """Xử lý tin nhắn chat và lấy phản hồi từ agent."""
    try:
        # Thử import và sử dụng AgentServiceCrewAI
        try:
            import importlib
            module = importlib.import_module('odoo.addons.eb_a2a.ai_agents.services.agent_service')
            AgentServiceCrewAI = getattr(module, 'AgentServiceCrewAI')
            
            # Sử dụng AgentServiceCrewAI
            service = AgentServiceCrewAI(env)
            result = service.process_task(
                agent_code=task.agent_endpoint_id.code,
                task_id=task.task_id,
                message_content=message_content,
                message_role='user'
            )
            
            return {
                "success": True,
                "response": result.get("response", ""),
                "state": result.get("state", "completed"),
                "error": result.get("error")
            }
        except Exception as e:
            _logger.warning(f"Error using AgentServiceCrewAI: {str(e)}. Falling back to AgentService.")
            
            # Fallback to AgentService
            from odoo.addons.eb_a2a.services.agent_service import AgentService
            service = AgentService(env)
            result = service.process_task(
                agent_code=task.agent_endpoint_id.code,
                task_id=task.task_id,
                message_content=message_content,
                message_role='user'
            )
            
            return {
                "success": True,
                "response": result.get("response", ""),
                "state": result.get("state", "completed"),
                "error": result.get("error")
            }
    except Exception as e:
        _logger.exception(f"Error processing chat message: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }
