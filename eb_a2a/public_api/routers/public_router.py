# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and <PERSON>tis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

from fastapi import APIRouter
from odoo.addons.eb_api_core.utils import get_request_logger

# Import các router con
from odoo.addons.eb_a2a.public_api.routers.chatbot.chat_session import chat_session_router
from odoo.addons.eb_a2a.public_api.routers.chatbot.chat_message import chat_message_router
from odoo.addons.eb_a2a.public_api.routers.chatbot.chat_feedback import chat_feedback_router
from odoo.addons.eb_a2a.public_api.routers.chatbot.chat_config import chat_config_router

_logger = get_request_logger(__name__)

# Router chính cho API public
public_router = APIRouter(prefix="/public")

# <PERSON><PERSON>ng ký các router con
public_router.include_router(chat_session_router)
public_router.include_router(chat_message_router)
public_router.include_router(chat_feedback_router)
public_router.include_router(chat_config_router)
