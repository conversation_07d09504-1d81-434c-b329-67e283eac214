# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import uuid
from typing import Annotated, Optional
from fastapi import APIRouter, Depends, Body, Path, HTTPException
from odoo import fields
from odoo.api import Environment
from odoo.addons.fastapi.dependencies import odoo_env
from odoo.addons.eb_api_core.dependencies import current_request_id
from odoo.addons.eb_api_core.utils.decorators import auto_error_response
from odoo.addons.eb_api_core.utils import trace, get_request_logger, add_span_attributes
from odoo.addons.eb_api_core.schemas.error import ErrorCode
from odoo.addons.eb_api_core.utils.exceptions import api_exception

# Import schemas
from odoo.addons.eb_a2a.public_api.schemas.chatbot_schema import (
    ChatFeedbackCreate,
    ChatFeedbackResponse,
)

_logger = get_request_logger(__name__)

# Router cho Chat Feedback API
chat_feedback_router = APIRouter(prefix="/chatbot/messages/{message_id}/feedback")

@chat_feedback_router.post("", response_model=ChatFeedbackResponse)
@auto_error_response([400, 404, 500])
@trace()
async def create_feedback(
    message_id: str = Path(...),
    feedback_data: ChatFeedbackCreate = Body(...),
    env: Annotated[Environment, Depends(odoo_env)] = None,
    request_id: Annotated[str, Depends(current_request_id)] = None,
):
    """Gửi feedback cho tin nhắn"""
    _logger.info(f"Creating feedback for message {message_id}, requestId={request_id}")

    try:
        # Thêm attribute vào span
        add_span_attributes(message_id=message_id, rating=feedback_data.rating)

        # Tìm tin nhắn
        try:
            message_id_int = int(message_id)
        except ValueError:
            raise api_exception(
                ErrorCode.BAD_REQUEST,
                f"Invalid message ID: {message_id}"
            )

        message = env["mail.message"].sudo().browse(message_id_int)
        if not message.exists() or message.a2a_role != 'agent':
            raise api_exception(
                ErrorCode.NOT_FOUND,
                f"Agent message with ID {message_id} not found"
            )

        # Tìm task liên quan
        task = env["eb_a2a.task"].sudo().browse(message.res_id)
        if not task.exists():
            raise api_exception(
                ErrorCode.NOT_FOUND,
                f"Task for message {message_id} not found"
            )

        # Tạo feedback
        # Lưu ý: Cần tạo model eb_a2a.feedback nếu chưa có
        # Trong ví dụ này, chúng ta sẽ lưu feedback vào note của tin nhắn
        feedback_id = str(uuid.uuid4())

        # Cập nhật note của tin nhắn
        note = message.note or ""
        feedback_note = f"""
        --- FEEDBACK ---
        ID: {feedback_id}
        Rating: {feedback_data.rating}/5
        Comment: {feedback_data.comment or 'N/A'}
        Created: {fields.Datetime.now()}
        """
        message.sudo().write({"note": note + feedback_note})

        # Trả về kết quả
        return ChatFeedbackResponse(
            success=True,
            message="Feedback submitted successfully",
            data={
                "feedback_id": feedback_id,
                "message_id": message_id,
                "rating": feedback_data.rating,
                "comment": feedback_data.comment,
                "created_at": fields.Datetime.now()
            }
        )
    except Exception as e:
        _logger.exception(f"Error creating feedback: {str(e)}")
        raise
