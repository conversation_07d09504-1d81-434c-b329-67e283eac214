# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

from typing import Annotated, Optional
from fastapi import APIRouter, Depends, Query
from odoo.api import Environment
from odoo.addons.fastapi.dependencies import odoo_env
from odoo.addons.eb_api_core.dependencies import current_request_id
from odoo.addons.eb_api_core.utils.decorators import auto_error_response
from odoo.addons.eb_api_core.utils import trace, get_request_logger, add_span_attributes
from odoo.addons.eb_api_core.schemas.error import ErrorCode
from odoo.addons.eb_api_core.utils.exceptions import api_exception
from pydantic import BaseModel

from odoo.addons.eb_api_core.schemas.base import ResponseBase

_logger = get_request_logger(__name__)

# Schema cho response
class ChatbotConfigData(BaseModel):
    """Schema cho dữ liệu cấu hình chatbot"""
    is_enabled: bool
    agent_code: str
    auto_popup: bool
    popup_delay: int
    welcome_message: str
    placeholder_text: str
    button_text: str
    header_text: str
    theme_color: str

class ChatbotConfigResponse(ResponseBase):
    """Schema cho response khi lấy cấu hình chatbot"""
    data: Optional[ChatbotConfigData] = None

# Router cho Chat Config API
chat_config_router = APIRouter(prefix="/chatbot/config")

@chat_config_router.get("", response_model=ChatbotConfigResponse)
@auto_error_response([404, 500])
@trace()
async def get_chatbot_config(
    website_code: Optional[str] = Query(None, description="Mã của website"),
    env: Annotated[Environment, Depends(odoo_env)] = None,
    request_id: Annotated[str, Depends(current_request_id)] = None,
):
    """Lấy cấu hình chatbot cho website"""
    _logger.info(f"Getting chatbot config for website code {website_code}, requestId={request_id}")

    try:
        # Thêm attribute vào span
        add_span_attributes(website_code=website_code)

        # Lấy cấu hình cho website
        WebsiteConfig = env["eb_a2a.website.config"]
        config = WebsiteConfig.get_config_for_website(website_code)

        if not config:
            raise api_exception(
                ErrorCode.NOT_FOUND,
                f"Chatbot configuration for website code {website_code} not found"
            )

        if not config.get('is_enabled', False):
            return ChatbotConfigResponse(
                success=True,
                message="Chatbot is disabled for this website",
                data={
                    "is_enabled": False,
                    "agent_code": config.get('agent_code', ''),
                    "auto_popup": False,
                    "popup_delay": 0,
                    "welcome_message": "",
                    "placeholder_text": "",
                    "button_text": "",
                    "header_text": "",
                    "theme_color": ""
                }
            )

        # Trả về cấu hình
        return ChatbotConfigResponse(
            success=True,
            message="Chatbot configuration retrieved successfully",
            data=config
        )
    except Exception as e:
        _logger.exception(f"Error getting chatbot config: {str(e)}")
        raise
