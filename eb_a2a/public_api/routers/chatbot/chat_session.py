# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import uuid
from typing import Annotated, Optional
from fastapi import APIRouter, Depends, Body, Path, HTTPException
from odoo.api import Environment
from odoo.addons.fastapi.dependencies import odoo_env
from odoo.addons.eb_api_core.dependencies import current_request_id
from odoo.addons.eb_api_core.utils.decorators import auto_error_response
from odoo.addons.eb_api_core.utils import trace, get_request_logger, add_span_attributes
from odoo.addons.eb_api_core.schemas.error import ErrorCode
from odoo.addons.eb_api_core.utils.exceptions import api_exception

# Import schemas
from odoo.addons.eb_a2a.public_api.schemas.chatbot_schema import (
    ChatSessionCreate,
    ChatSessionResponse,
    ChatSessionListResponse,
)

# Import utils
from odoo.addons.eb_a2a.public_api.utils.chatbot_utils import (
    generate_session_id,
    get_agent_endpoint,
    create_chat_task,
)

_logger = get_request_logger(__name__)

# Router cho Chat Session API
chat_session_router = APIRouter(prefix="/chatbot/sessions")

@chat_session_router.post("", response_model=ChatSessionResponse)
@auto_error_response([400, 500])
@trace()
async def create_chat_session(
    session_data: ChatSessionCreate = Body(...),
    env: Annotated[Environment, Depends(odoo_env)] = None,
    request_id: Annotated[str, Depends(current_request_id)] = None,
):
    """Tạo phiên chat mới"""
    _logger.info(f"Creating new chat session, requestId={request_id}")

    try:
        # Lấy agent_code từ cấu hình website nếu không có
        agent_code = session_data.agent_code
        if not agent_code and session_data.website_code:
            WebsiteConfig = env["eb_a2a.website.config"]
            config = WebsiteConfig.get_config_for_website(session_data.website_code)
            if config and config.get('is_enabled', False):
                agent_code = config.get('agent_code')
                _logger.info(f"Using agent_code {agent_code} from website configuration for code {session_data.website_code}")

        if not agent_code:
            raise api_exception(
                ErrorCode.BAD_REQUEST,
                "Agent code is required. Either provide it directly or specify a website_code with a valid configuration."
            )

        # Thêm attribute vào span
        add_span_attributes(agent_code=agent_code)

        # Kiểm tra agent_code
        agent_endpoint = get_agent_endpoint(env, agent_code)
        if not agent_endpoint:
            raise api_exception(
                ErrorCode.NOT_FOUND,
                f"Agent endpoint with code {agent_code} not found"
            )

        # Tạo session_id nếu chưa có
        session_id = session_data.session_id or generate_session_id()

        # Tạo task mới
        result = create_chat_task(
            env,
            agent_endpoint.id,
            session_id,
            session_data.name,
            session_data.metadata
        )

        # Lưu website_code vào metadata nếu có
        if session_data.website_code and result["success"]:
            task = result["task"]
            metadata = task.get_chatbot_metadata() if hasattr(task, 'get_chatbot_metadata') else {}
            metadata['website_code'] = session_data.website_code
            if hasattr(task, 'update_chatbot_metadata'):
                task.update_chatbot_metadata(metadata)

        if not result["success"]:
            raise api_exception(
                ErrorCode.INTERNAL_ERROR,
                f"Error creating chat session: {result.get('error', 'Unknown error')}"
            )

        # Trả về thông tin phiên chat
        return ChatSessionResponse(
            success=True,
            message="Chat session created successfully",
            data={
                "session_id": result["session_id"],
                "created_at": result["created_at"],
                "status": result["status"],
                "metadata": session_data.metadata
            }
        )
    except Exception as e:
        _logger.exception(f"Error creating chat session: {str(e)}")
        raise

@chat_session_router.get("/{session_id}", response_model=ChatSessionResponse)
@auto_error_response([404, 500])
@trace()
async def get_chat_session(
    session_id: str = Path(...),
    env: Annotated[Environment, Depends(odoo_env)] = None,
    request_id: Annotated[str, Depends(current_request_id)] = None,
):
    """Lấy thông tin phiên chat"""
    _logger.info(f"Getting chat session {session_id}, requestId={request_id}")

    try:
        # Thêm attribute vào span
        add_span_attributes(session_id=session_id)

        # Tìm task
        task = env["eb_a2a.task"].sudo().search([("task_id", "=", session_id)], limit=1)
        if not task:
            raise api_exception(
                ErrorCode.NOT_FOUND,
                f"Chat session {session_id} not found"
            )

        # Lấy metadata
        metadata = {}
        if hasattr(task, 'get_chatbot_metadata'):
            metadata = task.get_chatbot_metadata()

        # Trả về thông tin phiên chat
        return ChatSessionResponse(
            success=True,
            message="Chat session retrieved successfully",
            data={
                "session_id": task.task_id,
                "created_at": task.create_date,
                "status": task.state,
                "metadata": metadata
            }
        )
    except Exception as e:
        _logger.exception(f"Error getting chat session: {str(e)}")
        raise

@chat_session_router.delete("/{session_id}", response_model=ChatSessionResponse)
@auto_error_response([404, 500])
@trace()
async def end_chat_session(
    session_id: str = Path(...),
    env: Annotated[Environment, Depends(odoo_env)] = None,
    request_id: Annotated[str, Depends(current_request_id)] = None,
):
    """Kết thúc phiên chat"""
    _logger.info(f"Ending chat session {session_id}, requestId={request_id}")

    try:
        # Thêm attribute vào span
        add_span_attributes(session_id=session_id)

        # Tìm task
        task = env["eb_a2a.task"].sudo().search([("task_id", "=", session_id)], limit=1)
        if not task:
            raise api_exception(
                ErrorCode.NOT_FOUND,
                f"Chat session {session_id} not found"
            )

        # Cập nhật trạng thái task
        task.sudo().write({"state": "completed"})

        # Trả về thông tin phiên chat
        return ChatSessionResponse(
            success=True,
            message="Chat session ended successfully",
            data={
                "session_id": task.task_id,
                "created_at": task.create_date,
                "status": "completed",
                "metadata": task.get_chatbot_metadata() if hasattr(task, 'get_chatbot_metadata') else {}
            }
        )
    except Exception as e:
        _logger.exception(f"Error ending chat session: {str(e)}")
        raise
