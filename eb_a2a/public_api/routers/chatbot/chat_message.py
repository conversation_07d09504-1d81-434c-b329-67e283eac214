# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import json
from typing import Annotated, Optional, List
from fastapi import APIRouter, Depends, Body, Path, HTTPException, Query
from odoo import fields
from odoo.api import Environment
from odoo.addons.fastapi.dependencies import odoo_env
from odoo.addons.eb_api_core.dependencies import current_request_id
from odoo.addons.eb_api_core.utils.decorators import auto_error_response
from odoo.addons.eb_api_core.utils import trace, get_request_logger, add_span_attributes
from odoo.addons.eb_api_core.schemas.error import ErrorCode
from odoo.addons.eb_api_core.utils.exceptions import api_exception

# Import schemas
from odoo.addons.eb_a2a.public_api.schemas.chatbot_schema import (
    ChatMessageCreate,
    ChatMessageResponse,
    ChatMessageListResponse,
)

# Import utils
from odoo.addons.eb_a2a.public_api.utils.chatbot_utils import process_chat_message

_logger = get_request_logger(__name__)

# Router cho Chat Message API
chat_message_router = APIRouter(prefix="/chatbot/sessions/{session_id}/messages")

@chat_message_router.post("", response_model=ChatMessageResponse)
@auto_error_response([400, 404, 500])
@trace()
async def send_message(
    session_id: str = Path(...),
    message_data: ChatMessageCreate = Body(...),
    env: Annotated[Environment, Depends(odoo_env)] = None,
    request_id: Annotated[str, Depends(current_request_id)] = None,
):
    """Gửi tin nhắn mới trong phiên chat"""
    _logger.info(f"Sending message in session {session_id}, requestId={request_id}")

    try:
        # Thêm attribute vào span
        add_span_attributes(session_id=session_id)

        # Tìm task
        task = env["eb_a2a.task"].sudo().search([("task_id", "=", session_id)], limit=1)
        if not task:
            raise api_exception(
                ErrorCode.NOT_FOUND,
                f"Chat session {session_id} not found"
            )

        # Kiểm tra trạng thái task
        if task.state in ['completed', 'failed', 'canceled']:
            raise api_exception(
                ErrorCode.BAD_REQUEST,
                f"Chat session {session_id} is already {task.state}"
            )

        # Xử lý tin nhắn
        result = process_chat_message(env, task, message_data.content)

        if not result["success"]:
            raise api_exception(
                ErrorCode.INTERNAL_ERROR,
                f"Error processing message: {result.get('error', 'Unknown error')}"
            )

        # Lấy ID tin nhắn mới nhất
        latest_message = env["mail.message"].sudo().search([
            ("model", "=", "eb_a2a.task"),
            ("res_id", "=", task.id),
            ("a2a_role", "=", "agent")
        ], order="id desc", limit=1)

        message_id = latest_message.id if latest_message else "unknown"

        # Trả về kết quả
        return ChatMessageResponse(
            success=True,
            message="Message sent successfully",
            data={
                "message_id": str(message_id),
                "session_id": session_id,
                "content": result.get("response", ""),
                "role": "agent",
                "created_at": fields.Datetime.now(),
                "metadata": message_data.metadata
            }
        )
    except Exception as e:
        _logger.exception(f"Error sending message: {str(e)}")
        raise

@chat_message_router.get("", response_model=ChatMessageListResponse)
@auto_error_response([404, 500])
@trace()
async def get_messages(
    session_id: str = Path(...),
    env: Annotated[Environment, Depends(odoo_env)] = None,
    request_id: Annotated[str, Depends(current_request_id)] = None,
    limit: int = Query(50, description="Số lượng tin nhắn tối đa", ge=1, le=100),
):
    """Lấy lịch sử tin nhắn trong phiên chat"""
    _logger.info(f"Getting messages for session {session_id}, requestId={request_id}")

    try:
        # Thêm attribute vào span
        add_span_attributes(session_id=session_id, limit=limit)

        # Tìm task
        task = env["eb_a2a.task"].sudo().search([("task_id", "=", session_id)], limit=1)
        if not task:
            raise api_exception(
                ErrorCode.NOT_FOUND,
                f"Chat session {session_id} not found"
            )

        # Lấy tin nhắn
        messages = []
        for message in env["mail.message"].sudo().search([
            ("model", "=", "eb_a2a.task"),
            ("res_id", "=", task.id),
            ("a2a_role", "in", ['user', 'agent'])
        ], order="id asc", limit=limit):
            messages.append({
                "message_id": str(message.id),
                "session_id": session_id,
                "content": message.body,
                "role": message.a2a_role,
                "created_at": message.create_date,
                "metadata": {}
            })

        # Trả về kết quả
        return ChatMessageListResponse(
            success=True,
            message=f"Found {len(messages)} messages",
            data=messages
        )
    except Exception as e:
        _logger.exception(f"Error getting messages: {str(e)}")
        raise
