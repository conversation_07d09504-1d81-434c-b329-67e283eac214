# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import json
import logging
import werkzeug.exceptions
import importlib
import time
from odoo import http
from odoo.http import request
from odoo.addons.eb_a2a.services.agent_service import AgentService

_logger = logging.getLogger(__name__)


class A2AController(http.Controller):
    """Controller cho các endpoint A2A."""

    @http.route('/.well-known/agent.json', type='http', auth='public', methods=['GET'], website=True)
    def get_agent_card(self, **kw):
        """Endpoint để lấy Agent Card."""
        try:
            # Lấy agent endpoint mặc định
            agent_endpoint = request.env['eb_a2a.agent_endpoint'].sudo().search([('active', '=', True)], limit=1)
            if not agent_endpoint:
                return request.not_found()

            # Tạo Agent Card nếu chưa có
            if not agent_endpoint.agent_card:
                agent_endpoint.generate_agent_card()

            # Trả về Agent Card
            return request.make_response(
                agent_endpoint.agent_card,
                headers=[('Content-Type', 'application/json')]
            )
        except Exception as e:
            _logger.exception(f"Lỗi khi lấy Agent Card: {str(e)}")
            return request.make_response(
                json.dumps({'error': str(e)}),
                headers=[('Content-Type', 'application/json')],
                status=500
            )

    @http.route('/api/a2a/tasks/send', type='json', auth='public', methods=['POST'], csrf=False)
    def send_task(self, **kw):
        """Endpoint để gửi task đến agent."""
        try:
            # Lấy dữ liệu từ request
            data = request.params
            agent_code = data.get('agent_code')
            task_id = data.get('task_id')
            message = data.get('message')

            if not agent_code or not task_id or not message:
                return {'error': 'Missing required parameters'}

            # Xử lý message
            message_content = ""
            if isinstance(message, dict) and message.get('parts'):
                for part in message.get('parts', []):
                    if part.get('type') == 'text':
                        message_content += part.get('text', '')
            elif isinstance(message, dict) and message.get('content'):
                message_content = message.get('content')
            elif isinstance(message, str):
                message_content = message
            else:
                message_content = str(message)

            # Sử dụng AgentService
            try:
                # Thử import và sử dụng AgentServiceCrewAI trước
                try:
                    # Thử import AgentServiceCrewAI
                    _logger.info("Trying to import AgentServiceCrewAI")
                    module = importlib.import_module('odoo.addons.eb_a2a.ai_agents.services.agent_service')
                    AgentServiceCrewAI = getattr(module, 'AgentServiceCrewAI')
                    _logger.info("Successfully imported AgentServiceCrewAI")

                    # Sử dụng AgentServiceCrewAI
                    service = AgentServiceCrewAI(request.env)
                    result = service.process_task(
                        agent_code=agent_code,
                        task_id=task_id,
                        message_content=message_content,
                        message_role='user'
                    )
                except (ImportError, AttributeError) as e:
                    _logger.warning(f"Lỗi khi import AgentServiceCrewAI: {str(e)}. Chuyển sang AgentService.")
                    raise
            except Exception as e:
                _logger.warning(f"Lỗi khi sử dụng AgentServiceCrewAI: {str(e)}. Chuyển sang AgentService.")
                # Nếu có lỗi, sử dụng AgentService
                service = AgentService(request.env)
                result = service.process_task(
                    agent_code=agent_code,
                    task_id=task_id,
                    message_content=message_content,
                    message_role='user'
                )

            # Trả về kết quả
            return {
                'id': task_id,
                'status': result.get('state', 'completed'),
                'messages': [
                    {
                        'role': 'user',
                        'parts': [{'type': 'text', 'text': message_content}]
                    },
                    {
                        'role': 'agent',
                        'parts': [{'type': 'text', 'text': result.get('response', '')}]
                    }
                ],
                'error': result.get('error')
            }
        except Exception as e:
            _logger.exception(f"Lỗi khi xử lý task: {str(e)}")
            return {'error': str(e)}

    @http.route('/api/a2a/tasks/<string:task_id>', type='json', auth='public', methods=['GET'], csrf=False)
    def get_task(self, task_id, **kw):
        """Endpoint để lấy thông tin task."""
        try:
            # Tìm task
            task = request.env['eb_a2a.task'].sudo().search([('task_id', '=', task_id)], limit=1)
            if not task:
                raise werkzeug.exceptions.NotFound(f'Task with ID {task_id} not found')

            # Lấy thông tin task
            messages = []
            for message in task.message_ids.sorted('id'):
                if message.a2a_role in ['user', 'agent']:
                    messages.append({
                        'role': message.a2a_role,
                        'parts': [{'type': 'text', 'text': message.body}]
                    })

            # Trả về kết quả
            return {
                'id': task.task_id,
                'status': task.state,
                'messages': messages,
                'error': task.error_message
            }
        except Exception as e:
            _logger.exception(f"Lỗi khi lấy thông tin task: {str(e)}")
            return {'error': str(e)}

    @http.route('/api/a2a/tasks/<string:task_id>/cancel', type='json', auth='public', methods=['POST'], csrf=False)
    def cancel_task(self, task_id, **kw):
        """Endpoint để hủy task."""
        try:
            # Tìm task
            task = request.env['eb_a2a.task'].sudo().search([('task_id', '=', task_id)], limit=1)
            if not task:
                raise werkzeug.exceptions.NotFound(f'Task with ID {task_id} not found')

            # Hủy task
            task.sudo().write({'state': 'canceled'})

            # Trả về kết quả
            return {'status': 'canceled'}
        except Exception as e:
            _logger.exception(f"Lỗi khi hủy task: {str(e)}")
            return {'error': str(e)}

    @http.route('/api/a2a/tasks/sendSubscribe', type='http', auth='public', methods=['POST'], csrf=False)
    def send_subscribe_task(self, **kw):
        """Endpoint để gửi task và nhận streaming updates."""
        try:
            # Lấy dữ liệu từ request
            data = json.loads(request.httprequest.data.decode('utf-8'))
            agent_code = request.params.get('agent_code') or data.get('agent_code')
            task_id = data.get('task_id')
            message = data.get('message')

            if not agent_code or not task_id or not message:
                return werkzeug.wrappers.Response(
                    json.dumps({'error': 'Missing required parameters'}),
                    status=400,
                    content_type='application/json'
                )

            # Kiểm tra agent endpoint
            agent_endpoint = request.env['eb_a2a.agent_endpoint'].sudo().search([('code', '=', agent_code)], limit=1)
            if not agent_endpoint:
                return werkzeug.wrappers.Response(
                    json.dumps({'error': f"Agent endpoint with code {agent_code} not found"}),
                    status=404,
                    content_type='application/json'
                )

            # Kiểm tra xem agent endpoint có hỗ trợ streaming không
            if not agent_endpoint.supports_streaming:
                return werkzeug.wrappers.Response(
                    json.dumps({'error': f"Agent endpoint {agent_code} does not support streaming"}),
                    status=400,
                    content_type='application/json'
                )

            # Xử lý message
            message_content = ""
            if isinstance(message, dict) and message.get('parts'):
                for part in message.get('parts', []):
                    if part.get('type') == 'text':
                        message_content += part.get('text', '')
            elif isinstance(message, dict) and message.get('content'):
                message_content = message.get('content')
            elif isinstance(message, str):
                message_content = message
            else:
                message_content = str(message)

            # Lưu trữ các giá trị cần thiết từ request
            env = request.env
            current_user_id = request.env.user.id

            # Tạo service
            try:
                # Thử import và sử dụng AgentServiceCrewAI trước
                try:
                    # Thử import AgentServiceCrewAI
                    _logger.info("Trying to import AgentServiceCrewAI for streaming")
                    module = importlib.import_module('odoo.addons.eb_a2a.ai_agents.services.agent_service')
                    AgentServiceCrewAI = getattr(module, 'AgentServiceCrewAI')
                    _logger.info("Successfully imported AgentServiceCrewAI for streaming")

                    # Sử dụng AgentServiceCrewAI
                    service = AgentServiceCrewAI(env)
                except (ImportError, AttributeError) as e:
                    _logger.warning(f"Lỗi khi import AgentServiceCrewAI cho streaming: {str(e)}. Chuyển sang AgentService.")
                    raise
            except Exception as e:
                _logger.warning(f"Lỗi khi sử dụng AgentServiceCrewAI cho streaming: {str(e)}. Chuyển sang AgentService.")
                # Nếu có lỗi, sử dụng AgentService
                service = AgentService(env)

            # Tìm hoặc tạo task trước khi tạo generator
            task = env['eb_a2a.task'].sudo().search([('task_id', '=', task_id)], limit=1)
            if not task:
                # Tạo task mới
                task = env['eb_a2a.task'].sudo().create({
                    'task_id': task_id,
                    'name': f"Task {task_id}",
                    'agent_endpoint_id': agent_endpoint.id,
                    'user_id': current_user_id,
                    'state': 'submitted',
                    'is_streaming': True,
                })

            # Kiểm tra trạng thái task
            if task.state in ['completed', 'failed', 'canceled']:
                # Đặt lại trạng thái
                task.sudo().write({'state': 'submitted'})

            # Thêm tin nhắn vào task
            service.add_message(task, 'user', message_content)

            # Cập nhật trạng thái task
            task.sudo().write({'state': 'working'})

            # Lưu trữ các giá trị cần thiết từ agent endpoint
            deployment_type = agent_endpoint.deployment_type
            is_external_agent = deployment_type == 'external'

            # Lưu trữ task_id để sử dụng trong generator
            task_id_str = task_id

            # Xử lý task dựa trên loại service
            ai_response = None
            error_message = None

            if is_external_agent:
                error_message = "External agents do not support streaming"
            else:
                try:
                    # Kiểm tra xem service có phải là AgentServiceCrewAI không
                    if hasattr(service, '__class__') and service.__class__.__name__ == 'AgentServiceCrewAI' and agent_endpoint.framework == 'crewai':
                        _logger.info(f"Using AgentServiceCrewAI.process_task for agent_endpoint_id={agent_endpoint.id}")
                        # Sử dụng process_task của AgentServiceCrewAI
                        result = service.process_task(agent_endpoint.code, task_id, message_content)
                        if result.get('error'):
                            error_message = result.get('error')
                        else:
                            ai_response = result.get('response')
                    else:
                        # Sử dụng AI adapter trực tiếp
                        _logger.info(f"Using AI adapter directly for agent_endpoint_id={agent_endpoint.id}")
                        ai_adapter = service._get_ai_adapter(agent_endpoint)
                        if ai_adapter:
                            # Chuẩn bị context
                            context = service._prepare_context(task, agent_endpoint)
                            # Lấy phản hồi từ AI
                            if message_content and context:
                                ai_response = ai_adapter.generate_response(message_content, context)
                            else:
                                error_message = "Could not prepare context or message is empty"
                        else:
                            error_message = "Could not create AI adapter"
                except Exception as e:
                    _logger.exception(f"Error processing task: {str(e)}")
                    error_message = f"Error processing task: {str(e)}"

            # Kiểm tra kết quả
            if not ai_response and not error_message:
                error_message = "AI did not generate any response"

            # Tạo generator để xử lý streaming
            def generate_streaming_response():
                # Thông báo bắt đầu streaming
                yield "data: " + json.dumps({'type': 'task_status_update', 'task': {'id': task_id_str, 'status': 'working', 'messages': []}}) + "\n\n"

                # Xử lý các trường hợp lỗi
                if error_message:
                    yield "data: " + json.dumps({'type': 'error', 'error': error_message}) + "\n\n"
                    return

                try:
                    # Tạo biến để lưu trữ nội dung phản hồi
                    full_response = ""

                    # Mô phỏng streaming bằng cách chia nhỏ phản hồi
                    if ai_response:
                        # Chia nhỏ phản hồi thành các chunk nhỏ hơn để giả lập streaming tốt hơn
                        # Sử dụng kích thước chunk nhỏ hơn để tạo hiệu ứng mượt mà
                        # Chia nhỏ hơn nữa, mỗi chunk chỉ 1-2 ký tự
                        chunks = [ai_response[i:i + 1] for i in range(0, len(ai_response), 1)]
                        for chunk in chunks:
                            full_response += chunk
                            yield "data: " + json.dumps({'type': 'chunk', 'content': chunk}) + "\n\n"
                            # Giả lập độ trễ rất nhỏ để tăng tốc độ hiển thị
                            time.sleep(0.01)

                    # Thông báo hoàn thành
                    yield "data: " + json.dumps({'type': 'task_status_update', 'task': {'id': task_id_str, 'status': 'completed', 'messages': [{'role': 'agent', 'parts': [{'type': 'text', 'text': full_response}]}]}}) + "\n\n"

                    # Cập nhật task sau khi hoàn thành streaming
                    # Lưu ý: Đoạn code này sẽ không được thực thi trong generator
                    # Nó được đặt ở đây chỉ để tham khảo
                    # task.sudo().write({'state': 'completed'})
                    # service.add_message(task, 'agent', full_response)

                except Exception as e:
                    _logger.exception(f"Lỗi khi xử lý streaming task: {str(e)}")
                    # Trả về lỗi
                    yield "data: " + json.dumps({'type': 'error', 'error': f'Error processing streaming task: {str(e)}'}) + "\n\n"

            # Cập nhật trạng thái task trước khi trả về response
            if ai_response:
                # Cập nhật trạng thái task
                task.sudo().write({'state': 'completed'})
                # Thêm message phản hồi từ agent
                service.add_message(task, 'agent', ai_response)
            elif error_message:
                # Cập nhật trạng thái task
                task.sudo().write({'state': 'failed', 'error_message': error_message})

            # Trả về streaming response
            return werkzeug.wrappers.Response(
                generate_streaming_response(),
                content_type='text/event-stream',
                headers=[
                    ('Cache-Control', 'no-cache'),
                    ('X-Accel-Buffering', 'no'),
                    ('Connection', 'keep-alive')
                ]
            )
        except Exception as e:
            _logger.exception(f"Lỗi khi xử lý streaming task: {str(e)}")
            return werkzeug.wrappers.Response(
                json.dumps({'error': str(e)}),
                status=500,
                content_type='application/json'
            )
