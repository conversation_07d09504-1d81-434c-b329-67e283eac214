<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- LMS Crew Registry - Sequential -->
        <record id="crew_registry_lms_sequential" model="eb_a2a.crew.registry">
            <field name="name">LMS Crew (Sequential)</field>
            <field name="code">lms_crew_sequential</field>
            <field name="module_path">odoo.addons.eb_a2a_lms.crews.lms_crew</field>
            <field name="function_name">create_lms_crew</field>
            <field name="process_type">sequential</field>
            <field name="description">LMS Crew sử dụng quy trình tuần tự (sequential). Các agent thự<PERSON> hiện các task theo thứ tự tuần tự, với kết quả của agent tr<PERSON><PERSON><PERSON> đ<PERSON><PERSON><PERSON> chuyển cho agent sau.</field>
        </record>

        <!-- LMS Crew Registry - Hierarchical -->
        <record id="crew_registry_lms_hierarchical" model="eb_a2a.crew.registry">
            <field name="name">LMS Crew (Hierarchical)</field>
            <field name="code">lms_crew_hierarchical</field>
            <field name="module_path">odoo.addons.eb_a2a_lms.crews.lms_crew</field>
            <field name="function_name">create_lms_crew</field>
            <field name="process_type">hierarchical</field>
            <field name="manager_agent_module_path">odoo.addons.eb_a2a_lms.agents.manager_agent</field>
            <field name="manager_agent_function_name">create_manager_agent</field>
            <field name="description">LMS Crew sử dụng quy trình phân cấp (hierarchical). Một agent đóng vai trò quản lý, phân phối công việc cho các agent khác và tổng hợp kết quả.</field>
        </record>
    </data>
</odoo>
