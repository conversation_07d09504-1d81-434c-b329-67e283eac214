# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

def migrate(cr, version):
    """Cập nhật các agent hiện có để tạo partner cho chúng."""
    if version:
        # Lấy tất cả các agent chưa có partner
        cr.execute("""
            SELECT id, name
            FROM eb_a2a_agent
            WHERE partner_id IS NULL
        """)
        agents = cr.fetchall()
        
        # Tạo partner cho từng agent
        for agent_id, agent_name in agents:
            # Tạo partner mới
            cr.execute("""
                INSERT INTO res_partner (name, is_company, type, create_date, write_date)
                VALUES (%s, false, 'other', NOW(), NOW())
                RETURNING id
            """, (agent_name,))
            partner_id = cr.fetchone()[0]
            
            # Cậ<PERSON> nhật agent với partner mới
            cr.execute("""
                UPDATE eb_a2a_agent
                SET partner_id = %s
                WHERE id = %s
            """, (partner_id, agent_id))
