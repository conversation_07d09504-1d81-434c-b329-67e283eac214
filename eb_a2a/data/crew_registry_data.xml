<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Default Crew Registry -->
        <record id="crew_registry_default" model="eb_a2a.crew.registry">
            <field name="name">Default Crew</field>
            <field name="code">default</field>
            <field name="module_path">odoo.addons.eb_a2a.ai_agents.agents.base_agents</field>
            <field name="function_name">create_default_agent</field>
            <field name="description">Crew mặc định sử dụng agent đ<PERSON><PERSON> lẻ</field>
        </record>
    </data>
</odoo>
