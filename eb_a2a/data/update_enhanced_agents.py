# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

from odoo import api, SUPERUSER_ID

def update_agents(cr, registry):
    """Cập nhật hoặc tạo mới các agent với tính năng nâng cao."""
    with api.Environment.manage():
        env = api.Environment(cr, SUPERUSER_ID, {})



        # Tạo provider cho OpenAI
        openai_provider = env['eb_a2a.provider'].search([('code', '=', 'openai')], limit=1)
        if not openai_provider:
            openai_provider = env['eb_a2a.provider'].create({
                'name': 'OpenAI',
                'code': 'openai',
                'api_type': 'openai',
                'api_base': 'https://api.openai.com',
                'auth_type': 'api_key',
                'is_default': False,
                'system_message': """<PERSON>ạn là trợ lý AI của hệ thống quản lý học tập {agent_name}.
Bạn có thể giúp người dùng tìm kiếm khóa học, xem thông tin chi tiết về khóa học, lịch học, và thông tin về giảng viên.
Hãy trả lời một cách lịch sự, chuyên nghiệp và hữu ích.
Nếu bạn không biết câu trả lời, hãy thành thật và đề xuất cách người dùng có thể tìm thông tin."""
            })

            # Thêm các model cho provider
            env['eb_a2a.provider.model'].create([
                {
                    'name': 'GPT-4o',
                    'model_id': 'gpt-4o',
                    'provider_id': openai_provider.id,
                    'model_type': 'chat',
                    'supports_functions': True,
                    'supports_vision': True,
                    'supports_streaming': True,
                    'is_default': True,
                    'context_window': 128000,
                },
                {
                    'name': 'GPT-3.5 Turbo',
                    'model_id': 'gpt-3.5-turbo',
                    'provider_id': openai_provider.id,
                    'model_type': 'chat',
                    'supports_functions': True,
                    'supports_vision': False,
                    'supports_streaming': True,
                    'is_default': False,
                    'context_window': 16000,
                }
            ])

        # Tạo Enhanced LMS Agent
        lms_agent = env['eb_a2a.agent'].search([('code', '=', 'enhanced_lms_agent')], limit=1)
        if not lms_agent:
            # Tạo partner cho agent
            partner = env['res.partner'].create({
                'name': 'Enhanced LMS Agent',
                'is_company': False,
                'type': 'other',
            })

            # Sử dụng OpenAI provider
            provider_id = openai_provider.id

            lms_agent = env['eb_a2a.agent'].create({
                'name': 'Enhanced LMS Agent',
                'code': 'enhanced_lms_agent',
                'description': 'Trợ lý AI nâng cao cho hệ thống quản lý học tập',
                'agent_type': 'internal',
                'provider_id': provider_id,
                'partner_id': partner.id,
                'framework': 'crewai',  # Sử dụng CrewAI framework
                'system_message': """Bạn là trợ lý AI nâng cao của hệ thống quản lý học tập.
Bạn có thể giúp người dùng tìm kiếm khóa học, xem thông tin chi tiết về khóa học, lịch học, và thông tin về giảng viên.
Bạn có khả năng phân tích yêu cầu của người dùng và sử dụng các công cụ để trả lời chính xác.
Hãy trả lời một cách lịch sự, chuyên nghiệp và hữu ích.
Nếu bạn không biết câu trả lời, hãy thành thật và đề xuất cách người dùng có thể tìm thông tin."""
            })

            # Tạo Agent Card
            lms_agent.generate_agent_card()

        # Cập nhật Agent Card cho tất cả các agent
        agents = env['eb_a2a.agent'].search([])
        for agent in agents:
            agent.generate_agent_card()
