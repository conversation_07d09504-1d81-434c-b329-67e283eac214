# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import warnings
import logging

_logger = logging.getLogger(__name__)

def suppress_deprecation_warnings():
    """
    Bỏ qua các cảnh báo DeprecationWarning không cần thiết.
    
    Bao gồm:
    - Cảnh báo về 'open_text' bị loại bỏ trong thư viện litellm
    """
    # Bỏ qua cảnh báo DeprecationWarning từ litellm về open_text
    warnings.filterwarnings("ignore", category=DeprecationWarning, module="litellm.utils")
    warnings.filterwarnings("ignore", category=DeprecationWarning, message=".*open_text is deprecated.*")
    
    _logger.info("<PERSON><PERSON> cấu hình bỏ qua cảnh báo về 'open_text' trong litellm")
