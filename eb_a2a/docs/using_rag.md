# Hướng dẫn sử dụng RAG trong eb_a2a

## Giới thiệu

RAG (Retrieval-Augmented Generation) là một kỹ thuật kết hợp giữa truy xuất thông tin và sinh văn bản, cho phép mô hình ngôn ngữ lớn (LLM) truy cập vào dữ liệu bên ngoài để tạo ra câu trả lời chính xác và cập nhật hơn. Trong module eb_a2a, chúng ta đã triển khai hệ thống RAG để cho phép agent truy xuất thông tin từ cơ sở dữ liệu.

Tài liệu này hướng dẫn cách sử dụng và tùy chỉnh hệ thống RAG trong module eb_a2a.

## Kiến trúc RAG

Hệ thống RAG trong eb_a2a bao gồm các thành phần sau:

1. **RAG Service**: <PERSON>uản lý việc tạo và truy vấn RAG corpus, import tài liệu từ cơ sở dữ liệu vào corpus.

2. **RAG Adapter**: Kết nối với các RAG Engine khác nhau (Vertex AI RAG Engine, LlamaIndex).

3. **RAG Tools**: Các công cụ ADK cho phép agent truy vấn thông tin từ RAG corpus.

4. **Multi-Agent Integration**: Tích hợp RAG với hệ thống Multi-Agent.

## Luồng xử lý

Khi người dùng gửi một yêu cầu, luồng xử lý RAG sẽ diễn ra như sau:

1. Agent nhận yêu cầu từ người dùng.
2. Agent xác định xem có cần truy xuất thông tin từ RAG corpus không.
3. Nếu cần, agent sử dụng RAG Tool để truy vấn corpus.
4. RAG Tool gửi truy vấn đến RAG Service.
5. RAG Service sử dụng RAG Adapter để truy vấn RAG Engine.
6. RAG Engine trả về các kết quả liên quan.
7. RAG Service định dạng kết quả và trả về cho agent.
8. Agent kết hợp kết quả truy vấn với kiến thức của mình để tạo ra câu trả lời.

## Cách sử dụng RAG

### 1. Tạo và quản lý RAG corpus

RAG corpus là nơi lưu trữ các tài liệu đã được vector hóa để phục vụ cho việc truy vấn. Để tạo và quản lý RAG corpus, bạn có thể sử dụng RagService:

```python
from odoo.addons.eb_a2a.rag.rag_service import RagService

# Tạo RagService
rag_service = RagService(env)

# Tạo corpus mới
corpus_id = rag_service.create_corpus("Knowledge Base", "Corpus chứa thông tin về hệ thống")

# Import tài liệu vào corpus
rag_service.import_all_documents(corpus_id)

# Hoặc import từng loại tài liệu
rag_service.import_documents(corpus_id, "product")
rag_service.import_documents(corpus_id, "partner")
rag_service.import_documents(corpus_id, "sale")

# Truy vấn corpus
results = rag_service.query(corpus_id, "Sản phẩm XYZ", top_k=5)

# Định dạng kết quả
formatted_results = rag_service.get_formatted_results(results)
print(formatted_results)
```

### 2. Sử dụng RAG Tools

RAG Tools là các công cụ ADK cho phép agent truy vấn thông tin từ RAG corpus. Để sử dụng RAG Tools, bạn cần tạo các tool và thêm chúng vào agent:

```python
from odoo.addons.eb_a2a.rag.rag_tools import ADKRagQueryTool
from google.adk import Agent

# Tạo RAG Tool
rag_query_tool = ADKRagQueryTool(env, corpus_id)

# Tạo Agent với RAG Tool
agent = Agent(
    model="gemini-1.5-pro",
    global_instruction="Bạn là trợ lý AI của hệ thống Odoo.",
    name="rag_agent",
    tools=[rag_query_tool.get_adk_tool()]
)

# Sử dụng Agent
from google.adk import Message
response = agent.generate_content(Message(role="user", content="Cho tôi thông tin về sản phẩm XYZ"))
print(response.content)
```

### 3. Sử dụng RAG với Multi-Agent

Hệ thống RAG đã được tích hợp với Multi-Agent trong module eb_a2a. Để sử dụng RAG với Multi-Agent, bạn chỉ cần truyền corpus_id khi tạo Multi-Agent:

```python
from odoo.addons.eb_a2a.services.multi_agents import create_lms_multi_agent

# Tạo Multi-Agent với RAG
multi_agent = create_lms_multi_agent(env, corpus_id=corpus_id)

# Sử dụng Multi-Agent
from google.adk import Message
response = multi_agent.generate_content(Message(role="user", content="Cho tôi thông tin về sản phẩm XYZ"))
print(response.content)
```

### 4. Sử dụng RAG thông qua AgentService

AgentService đã được cập nhật để tự động sử dụng RAG khi xử lý các yêu cầu từ người dùng. Khi người dùng gửi một yêu cầu, AgentService sẽ tự động tạo corpus nếu chưa có và sử dụng RAG để truy vấn thông tin:

```python
from odoo.addons.eb_a2a.services.agent_service import AgentService

# Tạo AgentService
agent_service = AgentService(env)

# Xử lý yêu cầu
result = agent_service.process_message(agent, task, "Cho tôi thông tin về sản phẩm XYZ")

# Kết quả
print(result['response'])
```

## Tùy chỉnh RAG

### 1. Thay đổi RAG Engine

Mặc định, module eb_a2a sử dụng Vertex AI RAG Engine. Tuy nhiên, bạn có thể thay đổi RAG Engine bằng cách chỉ định adapter_type khi tạo RagService:

```python
# Sử dụng Vertex AI RAG Engine (mặc định)
rag_service = RagService(env, adapter_type='vertex')

# Sử dụng LlamaIndex
rag_service = RagService(env, adapter_type='llama_index')
```

### 2. Tùy chỉnh cách import tài liệu

Bạn có thể tùy chỉnh cách import tài liệu vào corpus bằng cách chỉnh sửa các phương thức import trong RagService:

- `import_documents`: Import tài liệu từ một model cụ thể
- `import_product_documents`: Import tài liệu từ sản phẩm
- `import_partner_documents`: Import tài liệu từ đối tác
- `import_all_documents`: Import tất cả tài liệu

### 3. Tùy chỉnh RAG Tools

Bạn có thể tùy chỉnh RAG Tools bằng cách tạo các tool mới kế thừa từ các tool hiện có hoặc tạo tool mới từ đầu:

```python
from odoo.addons.eb_a2a.rag.rag_tools import ADKRagQueryTool

class CustomRagQueryTool(ADKRagQueryTool):
    """Tool tùy chỉnh để truy vấn RAG corpus."""

    def __init__(self, env, corpus_id):
        super().__init__(env, corpus_id)
        self.name = "custom_query_rag"
        self.description = "Truy vấn tùy chỉnh RAG corpus"

    def execute(self, query, top_k=5):
        # Tùy chỉnh cách truy vấn
        results = self.rag_service.query(self.corpus_id, query, top_k)

        # Tùy chỉnh cách định dạng kết quả
        formatted_results = "Kết quả tùy chỉnh:\n\n"
        for result in results:
            formatted_results += f"- {result['metadata']['name']}: {result['content']}\n"

        return {
            "query": query,
            "results": results,
            "formatted_results": formatted_results
        }
```

## Ví dụ sử dụng

### Ví dụ 1: Tìm kiếm thông tin sản phẩm

```
User: Cho tôi thông tin về các sản phẩm XYZ
Agent: Dựa trên thông tin từ hệ thống, tôi tìm thấy các sản phẩm XYZ sau:

1. XYZ Basic
   - Mã: XYZ001
   - Mô tả: Sản phẩm cơ bản cho người mới bắt đầu
   - Giá: 299.000 VND
   - Loại: Cơ bản

2. XYZ Pro
   - Mã: XYZ002
   - Mô tả: Sản phẩm nâng cao với nhiều tính năng hiện đại
   - Giá: 499.000 VND
   - Loại: Chuyên nghiệp

3. XYZ Enterprise
   - Mã: XYZ003
   - Mô tả: Giải pháp toàn diện cho doanh nghiệp
   - Giá: 799.000 VND
   - Loại: Doanh nghiệp

Bạn muốn biết thêm thông tin chi tiết về sản phẩm nào?
```

### Ví dụ 2: Tìm kiếm thông tin đối tác

```
User: Ai là nhà cung cấp sản phẩm XYZ?
Agent: Theo thông tin từ hệ thống, nhà cung cấp sản phẩm XYZ là:

Công ty ABC
- Loại: Nhà cung cấp chính thức
- Email: <EMAIL>
- Điện thoại: 0123456789

Công ty ABC là đối tác chiến lược của chúng tôi từ năm 2020 và cung cấp hơn 50 sản phẩm khác nhau. Họ cũng là nhà phân phối độc quyền của dòng sản phẩm XYZ tại Việt Nam.
```

### Ví dụ 3: Tìm kiếm thông tin đơn hàng

```
User: Trạng thái đơn hàng SO001 là gì?
Agent: Dựa trên thông tin từ hệ thống, đơn hàng SO001 có thông tin như sau:

Đơn hàng: SO001
Khách hàng: Công ty XYZ
Trạng thái: Đã giao hàng

Chi tiết đơn hàng:
1. Sản phẩm: XYZ Basic
   - Số lượng: 2
   - Đơn giá: 299.000 VND
   - Thành tiền: 598.000 VND

2. Sản phẩm: XYZ Pro
   - Số lượng: 1
   - Đơn giá: 499.000 VND
   - Thành tiền: 499.000 VND

Tổng cộng: 1.097.000 VND
Ngày giao hàng: 05/07/2024

Đơn hàng đã được giao thành công và khách hàng đã thanh toán đầy đủ.
```

## Kết luận

RAG trong eb_a2a cung cấp một cách hiệu quả để agent truy xuất thông tin từ cơ sở dữ liệu, giúp tạo ra các câu trả lời chính xác và cập nhật hơn. Bằng cách kết hợp RAG với Multi-Agent, hệ thống có thể cung cấp trải nghiệm tương tác tự nhiên và thông minh cho người dùng.

Bạn có thể tùy chỉnh và mở rộng hệ thống RAG để đáp ứng nhu cầu cụ thể của mình, từ việc thay đổi RAG Engine đến tùy chỉnh cách import tài liệu và định dạng kết quả.
