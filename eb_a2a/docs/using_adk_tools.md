# Hướng dẫn sử dụng ADK Tools trong eb_a2a

## <PERSON><PERSON><PERSON><PERSON> thiệu

ADK (Agent Development Kit) Tools là các công cụ được tạo ra để cho phép agent tương tác với dữ liệu trong Odoo. Tài liệu này hướng dẫn cách sử dụng các ADK Tools đã được tích hợp trong module eb_a2a.

## Các ADK Tools hiện có

### 1. Course Tools

#### 1.1. ADKCourseSearchTool

Tool này cho phép tìm kiếm khóa học dựa trên các tiêu chí.

**Tham số:**
- `query` (str): Từ khóa tìm kiếm
- `category` (str, optional): Danh mục khóa học
- `level` (str, optional): Cấp độ khóa học
- `limit` (int, optional): <PERSON><PERSON> lượng kết quả tối đa (mặc định: 10)

**Kết quả:**
```json
{
  "courses": [
    {
      "id": 1,
      "name": "Python cơ bản",
      "code": "PY001",
      "short_description": "Khóa học nhập môn Python",
      "price": 299000,
      "currency": "VND",
      "category": "Lập trình",
      "level": "Cơ bản"
    }
  ],
  "total": 1,
  "query": "Python"
}
```

#### 1.2. ADKCourseDetailTool

Tool này cho phép xem thông tin chi tiết về một khóa học cụ thể.

**Tham số:**
- `course_id` (int/str): ID của khóa học

**Kết quả:**
```json
{
  "course": {
    "id": 1,
    "name": "Python cơ bản",
    "code": "PY001",
    "short_description": "Khóa học nhập môn Python",
    "description": "Khóa học Python cơ bản dành cho người mới bắt đầu...",
    "category": "Lập trình",
    "level": "Cơ bản",
    "price": 299000,
    "currency": "VND",
    "discount_percentage": 10,
    "certification_available": true,
    "certification_criteria": "Hoàn thành 80% bài tập và đạt 70% điểm thi cuối khóa",
    "instructor": "Nguyễn Văn A",
    "subjects": [
      {"id": 1, "name": "Giới thiệu Python"},
      {"id": 2, "name": "Biến và kiểu dữ liệu"}
    ],
    "lessons": [
      {
        "id": 1,
        "name": "Buổi 1: Giới thiệu Python",
        "start_time": "2024-07-01 09:00:00",
        "end_time": "2024-07-01 12:00:00",
        "location": "Tòa nhà A",
        "room": "Phòng 101"
      }
    ]
  }
}
```

### 2. Instructor Tools

#### 2.1. ADKInstructorSearchTool

Tool này cho phép tìm kiếm giảng viên dựa trên các tiêu chí.

**Tham số:**
- `query` (str, optional): Từ khóa tìm kiếm
- `specialization` (str, optional): Chuyên môn của giảng viên
- `limit` (int, optional): Số lượng kết quả tối đa (mặc định: 10)

**Kết quả:**
```json
{
  "instructors": [
    {
      "id": 1,
      "name": "Nguyễn Văn A",
      "function": "Giảng viên Python",
      "email": "<EMAIL>",
      "phone": "0123456789",
      "has_image": true
    }
  ],
  "total": 1,
  "query": "Python"
}
```

#### 2.2. ADKInstructorDetailTool

Tool này cho phép xem thông tin chi tiết về một giảng viên cụ thể.

**Tham số:**
- `instructor_id` (int/str): ID của giảng viên

**Kết quả:**
```json
{
  "instructor": {
    "id": 1,
    "name": "Nguyễn Văn A",
    "function": "Giảng viên Python",
    "email": "<EMAIL>",
    "phone": "0123456789",
    "mobile": "0987654321",
    "website": "https://example.com",
    "comment": "Giảng viên có 5 năm kinh nghiệm giảng dạy Python",
    "courses": [
      {
        "id": 1,
        "name": "Python cơ bản",
        "code": "PY001",
        "short_description": "Khóa học nhập môn Python",
        "level": "Cơ bản"
      }
    ]
  }
}
```

### 3. Class Tools

#### 3.1. ADKClassScheduleTool

Tool này cho phép xem lịch học của một lớp hoặc khóa học cụ thể.

**Tham số:**
- `class_id` (int/str, optional): ID của lớp học
- `course_id` (int/str, optional): ID của khóa học
- `start_date` (str, optional): Ngày bắt đầu (định dạng YYYY-MM-DD)
- `end_date` (str, optional): Ngày kết thúc (định dạng YYYY-MM-DD)

**Kết quả:**
```json
{
  "class_info": {
    "id": 1,
    "name": "Lớp Python cơ bản - K1",
    "course": "Python cơ bản",
    "instructor": "Nguyễn Văn A"
  },
  "schedule": [
    {
      "id": 1,
      "name": "Buổi 1: Giới thiệu Python",
      "start_time": "2024-07-01 09:00:00",
      "end_time": "2024-07-01 12:00:00",
      "location": "Tòa nhà A",
      "room": "Phòng 101",
      "instructor": "Nguyễn Văn A",
      "is_online": false,
      "meeting_url": ""
    }
  ],
  "total": 1,
  "start_date": "2024-07-01",
  "end_date": "2024-07-31"
}
```

#### 3.2. ADKClassSearchTool

Tool này cho phép tìm kiếm lớp học dựa trên các tiêu chí.

**Tham số:**
- `query` (str, optional): Từ khóa tìm kiếm
- `course_id` (int/str, optional): ID của khóa học
- `instructor_id` (int/str, optional): ID của giảng viên
- `limit` (int, optional): Số lượng kết quả tối đa (mặc định: 10)

**Kết quả:**
```json
{
  "classes": [
    {
      "id": 1,
      "name": "Lớp Python cơ bản - K1",
      "course": "Python cơ bản",
      "instructor": "Nguyễn Văn A",
      "start_date": "2024-07-01",
      "end_date": "2024-07-31",
      "state": "draft"
    }
  ],
  "total": 1,
  "query": "Python"
}
```

## Cách sử dụng ADK Tools trong Agent

### 1. Tạo Agent với ADK Tools

```python
from google.adk import Agent
from odoo.addons.eb_a2a.tools.adk_course_tools import ADKCourseSearchTool, ADKCourseDetailTool
from odoo.addons.eb_a2a.tools.adk_instructor_tools import ADKInstructorSearchTool, ADKInstructorDetailTool
from odoo.addons.eb_a2a.tools.adk_class_tools import ADKClassScheduleTool, ADKClassSearchTool

def create_lms_agent(env, model="gemini-1.5-pro"):
    """Tạo agent LMS với các ADK Tools.
    
    Args:
        env: Odoo environment
        model: Model AI (mặc định: gemini-1.5-pro)
        
    Returns:
        Agent: ADK Agent
    """
    # Tạo các tool
    course_search_tool = ADKCourseSearchTool(env)
    course_detail_tool = ADKCourseDetailTool(env)
    instructor_search_tool = ADKInstructorSearchTool(env)
    instructor_detail_tool = ADKInstructorDetailTool(env)
    class_schedule_tool = ADKClassScheduleTool(env)
    class_search_tool = ADKClassSearchTool(env)
    
    # Lấy các ADK Tool
    tools = [
        course_search_tool.get_adk_tool(),
        course_detail_tool.get_adk_tool(),
        instructor_search_tool.get_adk_tool(),
        instructor_detail_tool.get_adk_tool(),
        class_schedule_tool.get_adk_tool(),
        class_search_tool.get_adk_tool()
    ]
    
    # Tạo Agent ADK
    adk_agent = Agent(
        model=model,
        global_instruction="Bạn là trợ lý AI của hệ thống quản lý học tập. Hãy giúp người dùng tìm kiếm thông tin về khóa học, giảng viên và lớp học.",
        name="lms_agent",
        tools=tools
    )
    
    return adk_agent
```

### 2. Sử dụng Agent với ADK Tools

```python
# Tạo agent
agent = create_lms_agent(env)

# Tạo message
message = Message(role="user", content="Tìm kiếm khóa học về Python")

# Gọi agent để xử lý message
response = agent.generate_content(message)

# In phản hồi
print(response.content)
```

## Mở rộng ADK Tools

Để tạo thêm ADK Tools mới, bạn có thể tham khảo tài liệu [Hướng dẫn tạo Tool cho ADK trong eb_a2a](creating_adk_tools.md).

## Kết luận

ADK Tools trong eb_a2a cung cấp một cách hiệu quả để tương tác với dữ liệu trong Odoo thông qua các agent AI. Bằng cách sử dụng các tools này, bạn có thể tạo các agent thông minh có khả năng tìm kiếm và hiển thị thông tin về khóa học, giảng viên và lớp học trong hệ thống LMS.
