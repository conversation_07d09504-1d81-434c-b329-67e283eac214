# Hướng dẫn sử dụng CrewAI trong eb_a2a

## Giới thiệu

CrewAI là một framework mạnh mẽ để tạo và quản lý các agent AI. Module eb_a2a đã tích hợp CrewAI để cung cấp khả năng tạo các agent thông minh và linh hoạt. Tài liệu này hướng dẫn cách sử dụng CrewAI trong module eb_a2a.

## Cài đặt

Để sử dụng CrewAI, bạn cần cài đặt thư viện:

```bash
pip install crewai
```

## C<PERSON><PERSON> thành phần chính

### 1. Agent

Agent là thành phần cơ bản của CrewAI, đại diện cho một trợ lý AI có vai trò, mục tiêu và kỹ năng cụ thể.

```python
from odoo.addons.eb_a2a.agents.course_agent import create_course_agent

# Tạo Course Agent
course_agent = create_course_agent(env)
```

### 2. Tool

Tool là các công cụ mà agent có thể sử dụng để thực hiện các tác vụ cụ thể.

```python
from odoo.addons.eb_a2a.tools.course_tools import CourseSearchTool, CourseDetailTool

# Tạo các tool
course_search_tool = CourseSearchTool(env)
course_detail_tool = CourseDetailTool(env)
```

### 3. Crew

Crew là một nhóm các agent làm việc cùng nhau để hoàn thành một nhiệm vụ phức tạp.

```python
from odoo.addons.eb_a2a.crews.lms_crew import create_lms_crew

# Tạo LMS Crew
crew = create_lms_crew(env)

# Kickoff crew
result = crew.kickoff(inputs={'query': 'Tìm khóa học về Python'})
```

### 4. Task

Task là một nhiệm vụ cụ thể được giao cho agent.

```python
from crewai import Task

# Tạo task
task = Task(
    description="Tìm kiếm khóa học về Python",
    expected_output="Danh sách các khóa học về Python",
    agent=course_agent
)
```

## Sử dụng AgentServiceCrewAI

Module eb_a2a cung cấp AgentServiceCrewAI để xử lý các tác vụ của agent sử dụng CrewAI.

```python
from odoo.addons.eb_a2a.services.agent_service_crewai import AgentServiceCrewAI

# Tạo AgentServiceCrewAI
agent_service = AgentServiceCrewAI(env)

# Xử lý task
result = agent_service.process_task('course_agent', 'task_123', 'Tìm khóa học về Python')

# Kết quả
print(result['response'])
```

## Tùy chỉnh

### 1. Thêm Tool mới

Để thêm tool mới, bạn cần:

1. Tạo class tool mới kế thừa từ `crewai.Tool`:

```python
from crewai import Tool

class MyNewTool(Tool):
    def __init__(self, env):
        self.env = env
        super().__init__(
            name="my_new_tool",
            description="Mô tả về tool mới",
            func=self.execute
        )
    
    def execute(self, param1, param2=None):
        # Triển khai logic của tool
        return {"result": "Kết quả"}
```

2. Đăng ký tool với agent:

```python
# Tạo tool
my_new_tool = MyNewTool(env)

# Tạo agent với tool mới
agent = Agent(
    role="Expert",
    goal="Mục tiêu",
    backstory="Giới thiệu",
    verbose=True,
    tools=[my_new_tool]
)
```

### 2. Thêm Agent mới

Để thêm agent mới, bạn cần:

1. Tạo hàm để tạo agent mới:

```python
def create_my_new_agent(env, llm=None):
    # Tạo các tool
    tool1 = Tool1(env)
    tool2 = Tool2(env)
    
    # Tạo agent
    agent = Agent(
        role="My New Agent",
        goal="Mục tiêu của agent",
        backstory="Giới thiệu về agent",
        verbose=True,
        llm=llm,
        tools=[tool1, tool2]
    )
    
    return agent
```

2. Sử dụng agent mới:

```python
# Tạo agent mới
my_new_agent = create_my_new_agent(env)

# Tạo task
task = Task(
    description="Mô tả nhiệm vụ",
    expected_output="Kết quả mong đợi",
    agent=my_new_agent
)

# Tạo crew
crew = Crew(
    agents=[my_new_agent],
    tasks=[task],
    process=Process.sequential,
    verbose=True
)

# Kickoff crew
result = crew.kickoff()
```

### 3. Thêm Crew mới

Để thêm crew mới, bạn cần:

1. Tạo hàm để tạo crew mới:

```python
def create_my_new_crew(env, llm=None):
    # Tạo các agent
    agent1 = create_agent1(env, llm=llm)
    agent2 = create_agent2(env, llm=llm)
    
    # Tạo các task
    task1 = Task(
        description="Mô tả nhiệm vụ 1",
        expected_output="Kết quả mong đợi 1",
        agent=agent1
    )
    
    task2 = Task(
        description="Mô tả nhiệm vụ 2",
        expected_output="Kết quả mong đợi 2",
        agent=agent2
    )
    
    # Tạo crew
    crew = Crew(
        agents=[agent1, agent2],
        tasks=[task1, task2],
        process=Process.sequential,  # hoặc Process.hierarchical
        verbose=True
    )
    
    return crew
```

2. Sử dụng crew mới:

```python
# Tạo crew mới
my_new_crew = create_my_new_crew(env)

# Kickoff crew
result = my_new_crew.kickoff()
```

## Kết luận

CrewAI trong eb_a2a cung cấp một cách hiệu quả để tạo và quản lý các agent AI. Bằng cách sử dụng các agent, tool và crew, bạn có thể tạo các hệ thống AI phức tạp và linh hoạt để giải quyết nhiều loại vấn đề khác nhau.
