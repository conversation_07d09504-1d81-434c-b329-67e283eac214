# Hướng dẫn tạo Tool cho ADK trong eb_a2a

## Giới thiệu

Tools (công cụ) trong ADK là các hàm Python được định nghĩa để cho phép agent thực hiện các hành động cụ thể. Mỗi tool có một tên, mô tả, và một hàm thực thi. Khi agent nhận được yêu cầu từ người dùng, nó sẽ quyết định sử dụng tool nào để thực hiện yêu cầu đó.

Tài liệu này hướng dẫn cách tạo và sử dụng các tool ADK trong module eb_a2a.

## Cấu trúc Tool ADK

Một tool ADK bao gồm các thành phần sau:

1. **Tên (name)**: Tên của tool, được sử dụng để tham chiếu đến tool trong code.
2. **<PERSON><PERSON> tả (description)**: <PERSON><PERSON> tả ngắn gọn về chức năng của tool.
3. **<PERSON>àm thực thi (function)**: <PERSON><PERSON><PERSON> Python thực hiện chức năng của tool.
4. **Tham số (parameters)**: Các tham số mà tool cần để thực hiện chức năng.

## Cách tạo Tool ADK

### Bước 1: Import các module cần thiết

```python
from google.adk import Tool
from typing import Dict, List, Any, Optional
```

### Bước 2: Định nghĩa hàm thực thi

```python
def search_courses(query: str, category: Optional[str] = None, level: Optional[str] = None, limit: int = 10) -> Dict[str, Any]:
    """
    Tìm kiếm khóa học dựa trên các tiêu chí.
    
    Args:
        query: Từ khóa tìm kiếm
        category: Danh mục khóa học (tùy chọn)
        level: Cấp độ khóa học (tùy chọn)
        limit: Số lượng kết quả tối đa (mặc định: 10)
        
    Returns:
        Dict chứa kết quả tìm kiếm
    """
    # Triển khai logic tìm kiếm khóa học
    # Ví dụ:
    courses = []
    # ... (code tìm kiếm khóa học trong Odoo)
    
    return {
        "courses": courses,
        "total": len(courses),
        "query": query
    }
```

### Bước 3: Tạo Tool ADK

```python
search_courses_tool = Tool(
    name="search_courses",
    description="Tìm kiếm khóa học dựa trên từ khóa, danh mục, cấp độ, v.v.",
    function=search_courses
)
```

## Tích hợp Tool với Odoo

Để tích hợp Tool ADK với Odoo, chúng ta cần truy cập Odoo environment trong hàm thực thi. Có hai cách để làm điều này:

### Cách 1: Truyền env vào hàm thực thi

```python
def search_courses(env, query: str, category: Optional[str] = None, level: Optional[str] = None, limit: int = 10) -> Dict[str, Any]:
    """
    Tìm kiếm khóa học dựa trên các tiêu chí.
    
    Args:
        env: Odoo environment
        query: Từ khóa tìm kiếm
        category: Danh mục khóa học (tùy chọn)
        level: Cấp độ khóa học (tùy chọn)
        limit: Số lượng kết quả tối đa (mặc định: 10)
        
    Returns:
        Dict chứa kết quả tìm kiếm
    """
    # Sử dụng env để truy cập model Odoo
    Course = env['slide.channel']
    domain = [('name', 'ilike', query)]
    
    if category:
        domain.append(('category_id.name', 'ilike', category))
    
    if level:
        domain.append(('level', '=', level))
    
    courses = Course.search_read(domain, ['name', 'description', 'category_id', 'level'], limit=limit)
    
    return {
        "courses": courses,
        "total": len(courses),
        "query": query
    }
```

### Cách 2: Sử dụng class Tool

```python
class CourseSearchTool:
    """Tool để tìm kiếm khóa học."""
    
    def __init__(self, env):
        """Khởi tạo tool với Odoo environment."""
        self.env = env
        self.name = "search_courses"
        self.description = "Tìm kiếm khóa học dựa trên từ khóa, danh mục, cấp độ, v.v."
        
    def execute(self, query: str, category: Optional[str] = None, level: Optional[str] = None, limit: int = 10) -> Dict[str, Any]:
        """
        Tìm kiếm khóa học dựa trên các tiêu chí.
        
        Args:
            query: Từ khóa tìm kiếm
            category: Danh mục khóa học (tùy chọn)
            level: Cấp độ khóa học (tùy chọn)
            limit: Số lượng kết quả tối đa (mặc định: 10)
            
        Returns:
            Dict chứa kết quả tìm kiếm
        """
        # Sử dụng self.env để truy cập model Odoo
        Course = self.env['slide.channel']
        domain = [('name', 'ilike', query)]
        
        if category:
            domain.append(('category_id.name', 'ilike', category))
        
        if level:
            domain.append(('level', '=', level))
        
        courses = Course.search_read(domain, ['name', 'description', 'category_id', 'level'], limit=limit)
        
        return {
            "courses": courses,
            "total": len(courses),
            "query": query
        }
    
    @property
    def function_schema(self):
        """Trả về schema của tool để sử dụng với ADK."""
        return {
            "name": self.name,
            "description": self.description,
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Từ khóa tìm kiếm"
                    },
                    "category": {
                        "type": "string",
                        "description": "Danh mục khóa học (tùy chọn)"
                    },
                    "level": {
                        "type": "string",
                        "description": "Cấp độ khóa học (tùy chọn)"
                    },
                    "limit": {
                        "type": "integer",
                        "description": "Số lượng kết quả tối đa (mặc định: 10)"
                    }
                },
                "required": ["query"]
            }
        }
```

## Chuyển đổi Tool hiện tại sang Tool ADK

Để chuyển đổi các tool hiện tại trong eb_a2a sang Tool ADK, chúng ta cần thực hiện các bước sau:

### Bước 1: Tạo file tool mới

Tạo file mới trong thư mục `eb_a2a/tools/` với tên tương ứng với loại tool, ví dụ: `adk_course_tools.py`.

### Bước 2: Định nghĩa class Tool

```python
# -*- coding: utf-8 -*-
# Copyright 2025 EarnBase Technology <https://earnbase.io>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging
from typing import Dict, List, Any, Optional
from google.adk import Tool

_logger = logging.getLogger(__name__)

class ADKCourseSearchTool:
    """Tool ADK để tìm kiếm khóa học."""
    
    def __init__(self, env):
        """Khởi tạo tool với Odoo environment."""
        self.env = env
        self.name = "search_courses"
        self.description = "Tìm kiếm khóa học dựa trên từ khóa, danh mục, cấp độ, v.v."
        
    def execute(self, query: str, category: Optional[str] = None, level: Optional[str] = None, limit: int = 10) -> Dict[str, Any]:
        """
        Tìm kiếm khóa học dựa trên các tiêu chí.
        
        Args:
            query: Từ khóa tìm kiếm
            category: Danh mục khóa học (tùy chọn)
            level: Cấp độ khóa học (tùy chọn)
            limit: Số lượng kết quả tối đa (mặc định: 10)
            
        Returns:
            Dict chứa kết quả tìm kiếm
        """
        try:
            # Sử dụng self.env để truy cập model Odoo
            Course = self.env['slide.channel']
            domain = [('name', 'ilike', query)]
            
            if category:
                domain.append(('category_id.name', 'ilike', category))
            
            if level:
                domain.append(('level', '=', level))
            
            courses = Course.search_read(domain, ['name', 'description', 'category_id', 'level'], limit=limit)
            
            # Format kết quả
            formatted_courses = []
            for course in courses:
                formatted_courses.append({
                    "id": course['id'],
                    "name": course['name'],
                    "description": course['description'] or "",
                    "category": course['category_id'][1] if course['category_id'] else "",
                    "level": course['level'] or ""
                })
            
            return {
                "courses": formatted_courses,
                "total": len(formatted_courses),
                "query": query
            }
        except Exception as e:
            _logger.exception(f"Lỗi khi tìm kiếm khóa học: {str(e)}")
            return {
                "error": f"Lỗi khi tìm kiếm khóa học: {str(e)}",
                "courses": [],
                "total": 0,
                "query": query
            }
    
    def get_adk_tool(self):
        """Trả về Tool ADK."""
        return Tool(
            name=self.name,
            description=self.description,
            function=self.execute
        )
```

### Bước 3: Sử dụng Tool ADK trong AgentService

Trong file `eb_a2a/services/adk_adapter.py`, cập nhật phương thức `_get_tools_for_agent` để sử dụng các Tool ADK:

```python
def _get_tools_for_agent(self, agent_record):
    """Lấy danh sách tools cho agent.
    
    Args:
        agent_record: Bản ghi eb_a2a.agent
        
    Returns:
        List[Tool]: Danh sách ADK Tool
    """
    from ..tools.adk_course_tools import ADKCourseSearchTool
    from ..tools.adk_instructor_tools import ADKInstructorSearchTool
    from ..tools.adk_class_tools import ADKClassScheduleTool
    
    # Tạo các tool
    course_search_tool = ADKCourseSearchTool(self.env)
    instructor_search_tool = ADKInstructorSearchTool(self.env)
    class_schedule_tool = ADKClassScheduleTool(self.env)
    
    # Trả về danh sách các ADK Tool
    return [
        course_search_tool.get_adk_tool(),
        instructor_search_tool.get_adk_tool(),
        class_schedule_tool.get_adk_tool()
    ]
```

## Ví dụ Tool ADK

### Tool tìm kiếm khóa học

```python
# -*- coding: utf-8 -*-
# Copyright 2025 EarnBase Technology <https://earnbase.io>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging
from typing import Dict, List, Any, Optional
from google.adk import Tool

_logger = logging.getLogger(__name__)

class ADKCourseSearchTool:
    """Tool ADK để tìm kiếm khóa học."""
    
    def __init__(self, env):
        """Khởi tạo tool với Odoo environment."""
        self.env = env
        self.name = "search_courses"
        self.description = "Tìm kiếm khóa học dựa trên từ khóa, danh mục, cấp độ, v.v."
        
    def execute(self, query: str, category: Optional[str] = None, level: Optional[str] = None, limit: int = 10) -> Dict[str, Any]:
        """
        Tìm kiếm khóa học dựa trên các tiêu chí.
        
        Args:
            query: Từ khóa tìm kiếm
            category: Danh mục khóa học (tùy chọn)
            level: Cấp độ khóa học (tùy chọn)
            limit: Số lượng kết quả tối đa (mặc định: 10)
            
        Returns:
            Dict chứa kết quả tìm kiếm
        """
        try:
            # Sử dụng self.env để truy cập model Odoo
            Course = self.env['slide.channel']
            domain = [('name', 'ilike', query)]
            
            if category:
                domain.append(('category_id.name', 'ilike', category))
            
            if level:
                domain.append(('level', '=', level))
            
            courses = Course.search_read(domain, ['name', 'description', 'category_id', 'level'], limit=limit)
            
            # Format kết quả
            formatted_courses = []
            for course in courses:
                formatted_courses.append({
                    "id": course['id'],
                    "name": course['name'],
                    "description": course['description'] or "",
                    "category": course['category_id'][1] if course['category_id'] else "",
                    "level": course['level'] or ""
                })
            
            return {
                "courses": formatted_courses,
                "total": len(formatted_courses),
                "query": query
            }
        except Exception as e:
            _logger.exception(f"Lỗi khi tìm kiếm khóa học: {str(e)}")
            return {
                "error": f"Lỗi khi tìm kiếm khóa học: {str(e)}",
                "courses": [],
                "total": 0,
                "query": query
            }
    
    def get_adk_tool(self):
        """Trả về Tool ADK."""
        return Tool(
            name=self.name,
            description=self.description,
            function=self.execute
        )
```

### Tool xem chi tiết khóa học

```python
# -*- coding: utf-8 -*-
# Copyright 2025 EarnBase Technology <https://earnbase.io>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging
from typing import Dict, List, Any, Optional, Union
from google.adk import Tool

_logger = logging.getLogger(__name__)

class ADKCourseDetailTool:
    """Tool ADK để xem chi tiết khóa học."""
    
    def __init__(self, env):
        """Khởi tạo tool với Odoo environment."""
        self.env = env
        self.name = "get_course_details"
        self.description = "Xem thông tin chi tiết về một khóa học cụ thể"
        
    def execute(self, course_id: Union[int, str]) -> Dict[str, Any]:
        """
        Xem thông tin chi tiết về một khóa học.
        
        Args:
            course_id: ID của khóa học
            
        Returns:
            Dict chứa thông tin chi tiết về khóa học
        """
        try:
            # Chuyển đổi course_id sang int nếu cần
            if isinstance(course_id, str) and course_id.isdigit():
                course_id = int(course_id)
            
            # Sử dụng self.env để truy cập model Odoo
            Course = self.env['slide.channel']
            course = Course.browse(course_id)
            
            if not course.exists():
                return {
                    "error": f"Không tìm thấy khóa học với ID {course_id}",
                    "course": None
                }
            
            # Lấy thông tin chi tiết
            course_data = {
                "id": course.id,
                "name": course.name,
                "description": course.description or "",
                "category": course.category_id.name if course.category_id else "",
                "level": course.level or "",
                "total_slides": len(course.slide_ids),
                "total_time": course.total_time,
                "rating": course.rating or 0.0,
                "members_count": course.members_count,
                "instructor": course.user_id.name if course.user_id else "",
                "tags": [tag.name for tag in course.tag_ids],
                "slides": [{
                    "id": slide.id,
                    "name": slide.name,
                    "type": slide.slide_type,
                    "duration": slide.completion_time
                } for slide in course.slide_ids]
            }
            
            return {
                "course": course_data
            }
        except Exception as e:
            _logger.exception(f"Lỗi khi lấy thông tin chi tiết khóa học: {str(e)}")
            return {
                "error": f"Lỗi khi lấy thông tin chi tiết khóa học: {str(e)}",
                "course": None
            }
    
    def get_adk_tool(self):
        """Trả về Tool ADK."""
        return Tool(
            name=self.name,
            description=self.description,
            function=self.execute
        )
```

## Kết luận

Tạo Tool ADK trong eb_a2a là một cách hiệu quả để mở rộng khả năng của agent. Bằng cách tuân theo hướng dẫn này, bạn có thể tạo các tool mới hoặc chuyển đổi các tool hiện tại sang định dạng ADK.

Lưu ý rằng các tool ADK cần được đăng ký với agent thông qua OdooADKAdapter để có thể sử dụng. Đảm bảo rằng bạn đã cập nhật phương thức `_get_tools_for_agent` trong `adk_adapter.py` để bao gồm các tool mới của bạn.
