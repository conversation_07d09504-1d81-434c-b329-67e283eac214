# Hướng dẫn sử dụng Multi-Agent trong eb_a2a

## G<PERSON><PERSON><PERSON> thiệu

Multi-Agent là một kiến trúc agent trong đó nhiều agent chuyên biệt làm việc cùng nhau để giải quyết các vấn đề phức tạp. Trong module eb_a2a, chúng ta đã triển khai một hệ thống Multi-Agent bao gồm một router agent và các sub-agent chuyê<PERSON> bi<PERSON>t.

Tài liệu này hướng dẫn cách sử dụng và tùy chỉnh hệ thống Multi-Agent trong module eb_a2a.

## Kiến trúc Multi-Agent

Hệ thống Multi-Agent trong eb_a2a bao gồm các thành phần sau:

1. **Router Agent**: Agent ch<PERSON>h, có nhiệm vụ phân tích yêu cầu của người dùng và chuyển yêu cầu đến sub-agent ph<PERSON> hợ<PERSON>.

2. **Product Agent**: Sub-agent chuy<PERSON><PERSON> về sản phẩm, c<PERSON> khả năng tìm kiếm và cung cấp thông tin chi tiết về sản phẩm.

3. **Partner Agent**: Sub-agent chuyên về đối tác, có khả năng tìm kiếm và cung cấp thông tin chi tiết về đối tác.

4. **Sales Agent**: Sub-agent chuyên về bán hàng, có khả năng tìm kiếm đơn hàng và xem thông tin bán hàng.

Mỗi sub-agent được trang bị các ADK Tool phù hợp với lĩnh vực chuyên môn của nó, cho phép nó thực hiện các nhiệm vụ cụ thể một cách hiệu quả.

## Luồng xử lý

Khi người dùng gửi một yêu cầu, luồng xử lý sẽ diễn ra như sau:

1. Yêu cầu được gửi đến Router Agent.
2. Router Agent phân tích yêu cầu và xác định sub-agent phù hợp.
3. Router Agent chuyển yêu cầu đến sub-agent đã chọn.
4. Sub-agent xử lý yêu cầu và trả về kết quả cho Router Agent.
5. Router Agent tổng hợp kết quả và trả lời người dùng.

## Cách sử dụng Multi-Agent

### 1. Sử dụng Multi-Agent thông qua AgentService

AgentService đã được cập nhật để sử dụng Multi-Agent khi xử lý các yêu cầu từ người dùng. Khi người dùng gửi một yêu cầu, AgentService sẽ tự động tạo và sử dụng Multi-Agent để xử lý yêu cầu.

```python
# Tạo AgentService
agent_service = AgentService(env)

# Xử lý yêu cầu
result = agent_service.process_message(agent, task, "Tôi muốn tìm sản phẩm XYZ")

# Kết quả
print(result['response'])
```

### 2. Sử dụng Multi-Agent trực tiếp

Bạn cũng có thể tạo và sử dụng Multi-Agent trực tiếp:

```python
from odoo.addons.eb_a2a.services.multi_agents import create_multi_agent
from google.adk import Message

# Tạo Multi-Agent
multi_agent = create_multi_agent(env)

# Tạo tin nhắn
message = Message(role="user", content="Tôi muốn tìm sản phẩm XYZ")

# Gọi Multi-Agent để xử lý tin nhắn
response = multi_agent.generate_content(message)

# In phản hồi
print(response.content)
```

### 3. Sử dụng các sub-agent riêng lẻ

Nếu bạn chỉ cần sử dụng một sub-agent cụ thể, bạn có thể tạo và sử dụng nó trực tiếp:

```python
from odoo.addons.eb_a2a.services.multi_agents import create_product_agent
from google.adk import Message

# Tạo Product Agent
product_agent = create_product_agent(env)

# Tạo tin nhắn
message = Message(role="user", content="Tôi muốn tìm sản phẩm XYZ")

# Gọi Product Agent để xử lý tin nhắn
response = product_agent.generate_content(message)

# In phản hồi
print(response.content)
```

## Tùy chỉnh Multi-Agent

### 1. Tùy chỉnh prompt

Bạn có thể tùy chỉnh prompt cho các agent bằng cách chỉnh sửa các hàm trong file `multi_agent_prompts.py`:

- `get_router_agent_prompt()`: Prompt cho Router Agent
- `get_product_agent_prompt()`: Prompt cho Product Agent
- `get_partner_agent_prompt()`: Prompt cho Partner Agent
- `get_sales_agent_prompt()`: Prompt cho Sales Agent

### 2. Thêm sub-agent mới

Để thêm một sub-agent mới, bạn cần:

1. Tạo prompt cho sub-agent mới trong file `multi_agent_prompts.py`.
2. Tạo các ADK Tool cần thiết cho sub-agent mới.
3. Tạo hàm để tạo sub-agent mới trong file `multi_agents.py`.
4. Cập nhật hàm `create_router_agent()` để thêm sub-agent mới vào danh sách sub-agent.

Ví dụ, để thêm một sub-agent chuyên về đánh giá khóa học:

```python
# Trong file multi_agent_prompts.py
def get_inventory_agent_prompt():
    """Trả về prompt cho inventory agent."""
    return """
    Bạn là Inventory Agent, một trợ lý AI thông minh chuyên về quản lý kho hàng trong hệ thống Odoo.

    Nhiệm vụ của bạn là:
    1. Giúp người dùng kiểm tra tồn kho của sản phẩm
    2. Giúp người dùng tìm kiếm sản phẩm trong kho
    3. Trả lời các câu hỏi liên quan đến quản lý kho hàng

    ...
    """

# Trong file multi_agents.py
def create_inventory_agent(env, model="gemini-1.5-pro"):
    """Tạo agent chuyên về quản lý kho hàng."""
    try:
        # Lấy system prompt
        system_prompt = get_inventory_agent_prompt()

        # Tạo các tool
        inventory_check_tool = ADKInventoryCheckTool(env)
        inventory_search_tool = ADKInventorySearchTool(env)

        # Lấy các ADK Tool
        tools = [
            inventory_check_tool.get_adk_tool(),
            inventory_search_tool.get_adk_tool()
        ]

        # Tạo Agent ADK
        adk_agent = Agent(
            model=model,
            global_instruction=system_prompt,
            name="review_agent",
            tools=tools
        )

        return adk_agent
    except Exception as e:
        _logger.exception(f"Lỗi khi tạo Review Agent: {str(e)}")
        return None

# Cập nhật hàm create_router_agent()
def create_router_agent(env, model="gemini-1.5-pro"):
    """Tạo router agent để điều phối các sub-agent."""
    try:
        # ...

        # Tạo các sub-agent
        product_agent = create_product_agent(env, model)
        partner_agent = create_partner_agent(env, model)
        sales_agent = create_sales_agent(env, model)
        inventory_agent = create_inventory_agent(env, model)  # Thêm sub-agent mới

        # ...

        # Tạo các AgentTool
        # ...

        inventory_agent_tool = AgentTool(  # Thêm AgentTool mới
            name="inventory_agent",
            description="Sử dụng agent này để kiểm tra và tìm kiếm sản phẩm trong kho",
            agent=inventory_agent
        )

        # Tạo Agent ADK
        adk_agent = Agent(
            model=model,
            global_instruction=system_prompt,
            name="router_agent",
            tools=[product_agent_tool, partner_agent_tool, sales_agent_tool, inventory_agent_tool]  # Thêm tool mới
        )

        return adk_agent
    except Exception as e:
        _logger.exception(f"Lỗi khi tạo Router Agent: {str(e)}")
        return None
```

### 3. Tùy chỉnh model AI

Bạn có thể tùy chỉnh model AI được sử dụng cho các agent bằng cách truyền tham số `model` khi gọi các hàm tạo agent:

```python
# Sử dụng model khác
multi_agent = create_multi_agent(env, model="gemini-1.5-flash")
```

## Ví dụ sử dụng

### Ví dụ 1: Tìm kiếm sản phẩm

```
User: Tôi muốn tìm sản phẩm XYZ
Agent: Tôi đã tìm thấy 3 sản phẩm XYZ:

1. XYZ Basic
   - Mã: XYZ001
   - Mô tả: Sản phẩm cơ bản cho người mới bắt đầu
   - Giá: 299.000 VND

2. XYZ Pro
   - Mã: XYZ002
   - Mô tả: Sản phẩm nâng cao với nhiều tính năng hiện đại
   - Giá: 499.000 VND

3. XYZ Enterprise
   - Mã: XYZ003
   - Mô tả: Giải pháp toàn diện cho doanh nghiệp
   - Giá: 799.000 VND

Bạn muốn biết thêm thông tin chi tiết về sản phẩm nào?
```

### Ví dụ 2: Xem thông tin đối tác

```
User: Cho tôi thông tin về đối tác ABC
Agent: Đây là thông tin về đối tác ABC:

- Tên công ty: Công ty ABC
- Loại: Nhà cung cấp
- Email: <EMAIL>
- Điện thoại: 0123456789

Các sản phẩm cung cấp:
1. XYZ Basic (XYZ001)
2. XYZ Pro (XYZ002)
3. XYZ Enterprise (XYZ003)

Công ty ABC là đối tác chiến lược của chúng tôi từ năm 2020 và cung cấp hơn 50 sản phẩm khác nhau.
```

### Ví dụ 3: Xem thông tin đơn hàng

```
User: Cho tôi xem thông tin đơn hàng SO001
Agent: Đây là thông tin đơn hàng SO001:

Đơn hàng: SO001
Khách hàng: Công ty XYZ
Trạng thái: Đã giao hàng

Chi tiết đơn hàng:
1. Sản phẩm: XYZ Basic
   - Số lượng: 2
   - Đơn giá: 299.000 VND
   - Thành tiền: 598.000 VND

2. Sản phẩm: XYZ Pro
   - Số lượng: 1
   - Đơn giá: 499.000 VND
   - Thành tiền: 499.000 VND

Tổng cộng: 1.097.000 VND
Ngày giao hàng: 05/07/2024

Đơn hàng đã được giao thành công và khách hàng đã thanh toán đầy đủ.
```

## Kết luận

Multi-Agent trong eb_a2a cung cấp một cách hiệu quả để xử lý các yêu cầu phức tạp từ người dùng. Bằng cách sử dụng các sub-agent chuyên biệt, hệ thống có thể cung cấp phản hồi chính xác và đầy đủ cho nhiều loại yêu cầu khác nhau trong hệ thống Odoo.

Bạn có thể tùy chỉnh và mở rộng hệ thống Multi-Agent để đáp ứng nhu cầu cụ thể của mình, từ việc thay đổi prompt đến thêm các sub-agent mới với các khả năng khác nhau.
