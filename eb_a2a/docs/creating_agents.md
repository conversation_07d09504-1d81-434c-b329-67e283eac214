# Hướng dẫn tạo Agent trong eb_a2a

## G<PERSON><PERSON><PERSON> thiệu

Agent trong eb_a2a là các trợ lý AI được thiết kế để thực hiện các nhiệm vụ cụ thể. Mỗi agent có thể được cấu hình với các công cụ (tools), prompt, và model khác nhau để phục vụ các mục đích khác nhau.

Tài liệu này hướng dẫn cách tạo và cấu hình agent trong module eb_a2a.

## Cấu trúc Agent

Một agent trong eb_a2a bao gồm các thành phần sau:

1. **Bản ghi agent**: Bản ghi trong model `eb_a2a.agent` l<PERSON>u trữ thông tin cơ bản của agent.
2. **Provider**: Nhà cung cấp AI (OpenAI, Anthropic, Vertex AI, v.v.) đ<PERSON><PERSON><PERSON> sử dụng bởi agent.
3. **Tools**: <PERSON><PERSON><PERSON> cô<PERSON> cụ mà agent có thể sử dụng để thực hiện các nhiệ<PERSON> vụ.
4. **System message**: Hướng dẫn cho agent về cách thức hoạt động.

## Cách tạo Agent

### Bước 1: Tạo bản ghi agent trong Odoo

1. Đăng nhập vào Odoo với quyền quản trị.
2. Điều hướng đến **A2A > Cấu hình > Agents**.
3. Nhấp vào **Tạo mới**.
4. Điền thông tin cơ bản:
   - **Tên**: Tên của agent (ví dụ: "Course Assistant").
   - **Mã**: Mã duy nhất của agent (ví dụ: "course_assistant").
   - **Loại**: Chọn loại agent (internal hoặc external).
   - **Provider**: Chọn nhà cung cấp AI.
   - **System message**: Nhập hướng dẫn cho agent.
5. Nhấp vào **Lưu**.

### Bước 2: Cấu hình Provider

1. Điều hướng đến **A2A > Cấu hình > Providers**.
2. Nhấp vào **Tạo mới**.
3. Điền thông tin cơ bản:
   - **Tên**: Tên của provider (ví dụ: "OpenAI").
   - **Loại API**: Chọn loại API (openai, anthropic, vertex, v.v.).
   - **API Key**: Nhập API key của provider.
   - **Endpoint URL**: (Tùy chọn) Nhập endpoint URL nếu sử dụng API tùy chỉnh.
4. Nhấp vào **Lưu**.

### Bước 3: Cấu hình Model

1. Điều hướng đến **A2A > Cấu hình > Provider Models**.
2. Nhấp vào **Tạo mới**.
3. Điền thông tin cơ bản:
   - **Tên**: Tên của model (ví dụ: "GPT-4").
   - **Provider**: Chọn provider đã tạo ở bước 2.
   - **Model ID**: Nhập ID của model (ví dụ: "gpt-4").
   - **Loại model**: Chọn loại model (chat, completion, multimodal, v.v.).
   - **Mặc định**: Đánh dấu nếu đây là model mặc định cho provider.
4. Nhấp vào **Lưu**.

### Bước 4: Tạo Agent bằng code

Nếu bạn muốn tạo agent bằng code thay vì giao diện người dùng, bạn có thể sử dụng đoạn code sau:

```python
def create_course_assistant(self):
    """Tạo agent Course Assistant."""
    # Tạo provider
    provider = self.env['eb_a2a.provider'].create({
        'name': 'OpenAI',
        'api_type': 'openai',
        'api_key': 'your_api_key_here'
    })
    
    # Tạo model
    model = self.env['eb_a2a.provider.model'].create({
        'name': 'GPT-4',
        'provider_id': provider.id,
        'model_id': 'gpt-4',
        'model_type': 'chat',
        'is_default': True
    })
    
    # Tạo agent
    agent = self.env['eb_a2a.agent'].create({
        'name': 'Course Assistant',
        'code': 'course_assistant',
        'agent_type': 'internal',
        'provider_id': provider.id,
        'system_message': """
        Bạn là Course Assistant, một trợ lý AI thông minh của hệ thống quản lý học tập (LMS).
        
        Nhiệm vụ của bạn là:
        1. Giúp người dùng tìm kiếm khóa học phù hợp với nhu cầu của họ
        2. Cung cấp thông tin chi tiết về các khóa học
        3. Hỗ trợ người dùng xem lịch học và thời khóa biểu
        4. Cung cấp thông tin về giảng viên
        5. Trả lời các câu hỏi liên quan đến hệ thống LMS
        """
    })
    
    return agent
```

## Tạo Agent với ADK

Với việc tích hợp ADK vào eb_a2a, chúng ta có thể tạo agent sử dụng ADK. Dưới đây là cách tạo agent với ADK:

### Bước 1: Import các module cần thiết

```python
from google.adk import Agent, Tool
from ..prompts.system_prompts import get_default_system_prompt
```

### Bước 2: Tạo các tool cho agent

```python
from ..tools.adk_course_tools import ADKCourseSearchTool, ADKCourseDetailTool
from ..tools.adk_instructor_tools import ADKInstructorSearchTool, ADKInstructorDetailTool
from ..tools.adk_class_tools import ADKClassScheduleTool

# Tạo các tool
course_search_tool = ADKCourseSearchTool(env)
course_detail_tool = ADKCourseDetailTool(env)
instructor_search_tool = ADKInstructorSearchTool(env)
instructor_detail_tool = ADKInstructorDetailTool(env)
class_schedule_tool = ADKClassScheduleTool(env)

# Lấy các ADK Tool
tools = [
    course_search_tool.get_adk_tool(),
    course_detail_tool.get_adk_tool(),
    instructor_search_tool.get_adk_tool(),
    instructor_detail_tool.get_adk_tool(),
    class_schedule_tool.get_adk_tool()
]
```

### Bước 3: Tạo Agent ADK

```python
# Lấy system prompt
system_prompt = get_default_system_prompt(agent_name="Course Assistant")

# Tạo Agent ADK
adk_agent = Agent(
    model="gemini-1.5-pro",  # Hoặc model khác tùy thuộc vào provider
    global_instruction=system_prompt,
    name="course_assistant",
    tools=tools
)
```

### Bước 4: Sử dụng Agent ADK

```python
# Tạo message
message = {
    'role': 'user',
    'content': "Tôi muốn tìm khóa học về Python"
}

# Chuyển đổi message sang định dạng ADK
adk_message = adk_adapter.convert_odoo_message_to_adk(message)

# Gọi Agent ADK
response = adk_agent.generate_content(adk_message)

# Chuyển đổi phản hồi sang định dạng Odoo
odoo_response = adk_adapter.convert_adk_message_to_odoo(response)

# Trả về kết quả
return {
    'response': odoo_response.get('content', ''),
    'state': 'completed'
}
```

## Tích hợp Agent với OdooADKAdapter

Để tích hợp agent với OdooADKAdapter, chúng ta cần cập nhật phương thức `create_agent_from_record` trong `adk_adapter.py`:

```python
def create_agent_from_record(self, agent_record):
    """Tạo ADK Agent từ bản ghi eb_a2a.agent.
    
    Args:
        agent_record: Bản ghi eb_a2a.agent
        
    Returns:
        Agent: ADK Agent
    """
    try:
        # Lấy system message
        from ..prompts.system_prompts import get_default_system_prompt
        
        system_message = agent_record.system_message
        if not system_message:
            system_message = get_default_system_prompt(agent_name=agent_record.name)
        
        # Lấy model
        model = self._get_model_from_provider(agent_record.provider_id)
        
        # Lấy tools
        tools = self._get_tools_for_agent(agent_record)
        
        # Tạo ADK Agent
        adk_agent = Agent(
            model=model,
            global_instruction=system_message,
            name=agent_record.code,
            tools=tools
        )
        
        return adk_agent
    except Exception as e:
        _logger.exception(f"Lỗi khi tạo ADK Agent: {str(e)}")
        return None
```

## Ví dụ Agent

### Agent tìm kiếm khóa học

```python
# -*- coding: utf-8 -*-
# Copyright 2025 EarnBase Technology <https://earnbase.io>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

from google.adk import Agent
from ..prompts.system_prompts import get_course_search_prompt
from ..tools.adk_course_tools import ADKCourseSearchTool, ADKCourseDetailTool

def create_course_search_agent(env, model="gemini-1.5-pro"):
    """Tạo agent tìm kiếm khóa học.
    
    Args:
        env: Odoo environment
        model: Model AI (mặc định: gemini-1.5-pro)
        
    Returns:
        Agent: ADK Agent
    """
    # Lấy system prompt
    system_prompt = get_course_search_prompt()
    
    # Tạo các tool
    course_search_tool = ADKCourseSearchTool(env)
    course_detail_tool = ADKCourseDetailTool(env)
    
    # Lấy các ADK Tool
    tools = [
        course_search_tool.get_adk_tool(),
        course_detail_tool.get_adk_tool()
    ]
    
    # Tạo Agent ADK
    adk_agent = Agent(
        model=model,
        global_instruction=system_prompt,
        name="course_search_agent",
        tools=tools
    )
    
    return adk_agent
```

### Agent hỗ trợ giảng viên

```python
# -*- coding: utf-8 -*-
# Copyright 2025 EarnBase Technology <https://earnbase.io>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

from google.adk import Agent
from ..prompts.system_prompts import get_instructor_search_prompt
from ..tools.adk_instructor_tools import ADKInstructorSearchTool, ADKInstructorDetailTool

def create_instructor_agent(env, model="gemini-1.5-pro"):
    """Tạo agent hỗ trợ giảng viên.
    
    Args:
        env: Odoo environment
        model: Model AI (mặc định: gemini-1.5-pro)
        
    Returns:
        Agent: ADK Agent
    """
    # Lấy system prompt
    system_prompt = get_instructor_search_prompt()
    
    # Tạo các tool
    instructor_search_tool = ADKInstructorSearchTool(env)
    instructor_detail_tool = ADKInstructorDetailTool(env)
    
    # Lấy các ADK Tool
    tools = [
        instructor_search_tool.get_adk_tool(),
        instructor_detail_tool.get_adk_tool()
    ]
    
    # Tạo Agent ADK
    adk_agent = Agent(
        model=model,
        global_instruction=system_prompt,
        name="instructor_agent",
        tools=tools
    )
    
    return adk_agent
```

## Tạo Multi-Agent

ADK hỗ trợ tạo multi-agent, cho phép nhiều agent làm việc cùng nhau để giải quyết các vấn đề phức tạp. Dưới đây là cách tạo multi-agent:

```python
# -*- coding: utf-8 -*-
# Copyright 2025 EarnBase Technology <https://earnbase.io>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

from google.adk import Agent, AgentTool
from ..prompts.system_prompts import get_default_system_prompt

def create_lms_multi_agent(env, model="gemini-1.5-pro"):
    """Tạo multi-agent cho hệ thống LMS.
    
    Args:
        env: Odoo environment
        model: Model AI (mặc định: gemini-1.5-pro)
        
    Returns:
        Agent: ADK Agent
    """
    # Tạo các sub-agent
    course_agent = create_course_search_agent(env, model)
    instructor_agent = create_instructor_agent(env, model)
    
    # Tạo các AgentTool
    course_agent_tool = AgentTool(
        name="course_agent",
        description="Sử dụng agent này để tìm kiếm và xem thông tin chi tiết về khóa học",
        agent=course_agent
    )
    
    instructor_agent_tool = AgentTool(
        name="instructor_agent",
        description="Sử dụng agent này để tìm kiếm và xem thông tin chi tiết về giảng viên",
        agent=instructor_agent
    )
    
    # Lấy system prompt
    system_prompt = get_default_system_prompt(agent_name="LMS Assistant")
    
    # Tạo Agent ADK
    adk_agent = Agent(
        model=model,
        global_instruction=system_prompt,
        name="lms_multi_agent",
        tools=[course_agent_tool, instructor_agent_tool]
    )
    
    return adk_agent
```

## Kết luận

Tạo Agent trong eb_a2a là một cách hiệu quả để xây dựng các trợ lý AI thông minh cho hệ thống LMS. Bằng cách tuân theo hướng dẫn này, bạn có thể tạo các agent mới hoặc cấu hình lại các agent hiện tại để phục vụ các mục đích khác nhau.

Lưu ý rằng các agent cần được đăng ký với hệ thống thông qua model `eb_a2a.agent` để có thể sử dụng. Đảm bảo rằng bạn đã cập nhật các thông tin cần thiết như provider, model, và system message.
