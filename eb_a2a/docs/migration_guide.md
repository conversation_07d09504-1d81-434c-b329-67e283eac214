# Hướng dẫn chuyển đổi từ Google ADK sang CrewAI

## Giớ<PERSON> thiệu

Tài liệu này hướng dẫn cách chuyển đổi các module mở rộng từ Google ADK sang CrewAI. Module cơ sở eb_a2a đã được chuyển đổi sang CrewAI, và các module mở rộng như eb_a2a_lms cần được cập nhật để tương thích với phiên bản mới.

## Các thay đổi chính

### 1. Thay đổi trong Tools

#### Google ADK:

```python
class CourseSearchTool:
    def __init__(self, env):
        self.env = env
        self.name = "search_courses"
        self.description = "Tìm kiếm khóa học dựa trên từ khóa, danh mục, cấp độ, v.v."

    def execute(self, query: str, category: Optional[str], level: Optional[str], limit: int = 10) -> Dict[str, Any]:
        # Logic tìm kiếm khóa học
        return {"courses": courses, "total": len(courses), "query": query}

    def get_adk_tool(self):
        return Tool(func=self.execute)
```

#### CrewAI:

```python
from crewai import Tool

class CourseSearchTool(Tool):
    def __init__(self, env):
        self.env = env
        super().__init__(
            name="search_courses",
            description="Tìm kiếm khóa học dựa trên từ khóa, danh mục, cấp độ, v.v.",
            func=self.execute
        )

    def execute(self, query: str, category: Optional[str] = None, level: Optional[str] = None, limit: int = 10) -> Dict[str, Any]:
        # Logic tìm kiếm khóa học (giữ nguyên)
        return {"courses": courses, "total": len(courses), "query": query}
```

### 2. Thay đổi trong Agents

#### Google ADK:

```python
def create_course_agent(env, model=None):
    # Lấy system prompt
    system_prompt = get_course_agent_prompt()
    
    # Tạo các tool
    course_search_tool = CourseSearchTool(env)
    course_detail_tool = CourseDetailTool(env)
    
    # Lấy các ADK Tool
    tools = [
        course_search_tool.get_adk_tool(),
        course_detail_tool.get_adk_tool()
    ]
    
    # Tạo Agent ADK
    adk_agent = Agent(
        model=model,
        global_instruction=system_prompt,
        name="course_agent",
        tools=tools
    )
    
    return adk_agent
```

#### CrewAI:

```python
from crewai import Agent

def create_course_agent(env, llm=None):
    # Tạo các tool
    course_search_tool = CourseSearchTool(env)
    course_detail_tool = CourseDetailTool(env)
    
    # Tạo Agent CrewAI
    agent = Agent(
        role="Course Search Expert",
        goal="Tìm kiếm và cung cấp thông tin chi tiết về các khóa học",
        backstory="Bạn là chuyên gia về khóa học, có kiến thức sâu rộng về tất cả các khóa học trong hệ thống.",
        verbose=True,
        llm=llm,
        tools=[course_search_tool, course_detail_tool]
    )
    
    return agent
```

### 3. Thay đổi trong Multi-Agent

#### Google ADK:

```python
def create_lms_multi_agent(env, model=None):
    # Tạo các sub-agent
    course_agent = create_course_agent(env, model)
    instructor_agent = create_instructor_agent(env, model)
    class_agent = create_class_agent(env, model)
    
    # Tạo router agent
    router_agent = create_router_agent(env, model, course_agent, instructor_agent, class_agent)
    
    return router_agent
```

#### CrewAI:

```python
from crewai import Crew, Process, Task

def create_lms_crew(env, llm=None):
    # Tạo các agent
    course_agent = create_course_agent(env, llm=llm)
    instructor_agent = create_instructor_agent(env, llm=llm)
    class_agent = create_class_agent(env, llm=llm)
    
    # Tạo các task
    course_task = Task(
        description="Tìm kiếm và cung cấp thông tin chi tiết về khóa học theo yêu cầu của người dùng",
        expected_output="Danh sách khóa học phù hợp với yêu cầu, bao gồm tên, mô tả, giá, và các thông tin khác",
        agent=course_agent
    )
    
    instructor_task = Task(
        description="Tìm kiếm và cung cấp thông tin chi tiết về giảng viên theo yêu cầu của người dùng",
        expected_output="Thông tin chi tiết về giảng viên, bao gồm tên, chuyên môn, và các khóa học đang dạy",
        agent=instructor_agent
    )
    
    class_task = Task(
        description="Tìm kiếm lớp học và cung cấp lịch học theo yêu cầu của người dùng",
        expected_output="Danh sách lớp học và lịch học phù hợp với yêu cầu",
        agent=class_agent
    )
    
    # Tạo Crew
    crew = Crew(
        agents=[course_agent, instructor_agent, class_agent],
        tasks=[course_task, instructor_task, class_task],
        process=Process.hierarchical,
        verbose=True
    )
    
    return crew
```

## Các bước chuyển đổi

### 1. Cập nhật dependencies

Thêm CrewAI vào dependencies của module:

```python
"external_dependencies": {
    "python": ["openai", "requests", "crewai"],
},
```

### 2. Import các class cơ bản từ eb_a2a

```python
from odoo.addons.eb_a2a.tools.base_tools import Tool
from odoo.addons.eb_a2a.agents.base_agents import Agent
from odoo.addons.eb_a2a.crews.base_crews import Crew, Process, Task
```

### 3. Chuyển đổi Tools

1. Kế thừa từ `Tool` thay vì tạo class riêng
2. Sử dụng `super().__init__()` để khởi tạo
3. Loại bỏ phương thức `get_adk_tool()`

### 4. Chuyển đổi Agents

1. Sử dụng `Agent` từ CrewAI
2. Thêm các tham số mới: `role`, `goal`, `backstory`
3. Sử dụng `llm` thay vì `model`

### 5. Chuyển đổi Multi-Agent sang Crew

1. Tạo các `Task` cho từng agent
2. Tạo `Crew` với danh sách agents và tasks
3. Sử dụng `Process.sequential` hoặc `Process.hierarchical`

### 6. Sử dụng AgentServiceCrewAI

```python
from odoo.addons.eb_a2a.services.agent_service_crewai import AgentServiceCrewAI

# Tạo AgentServiceCrewAI
agent_service = AgentServiceCrewAI(env)

# Xử lý task
result = agent_service.process_task('course_agent', 'task_123', 'Tìm khóa học về Python')
```

## Ví dụ chuyển đổi module eb_a2a_lms

### 1. Chuyển đổi CourseSearchTool

#### Trước:

```python
class CourseSearchTool:
    def __init__(self, env):
        self.env = env
        self.name = "search_courses"
        self.description = "Tìm kiếm khóa học dựa trên từ khóa, danh mục, cấp độ, v.v."

    def execute(self, query: str, category: Optional[str], level: Optional[str], limit: int = 10) -> Dict[str, Any]:
        # Logic tìm kiếm khóa học
        return {"courses": courses, "total": len(courses), "query": query}

    def get_adk_tool(self):
        return Tool(func=self.execute)
```

#### Sau:

```python
from odoo.addons.eb_a2a.tools.base_tools import Tool

class CourseSearchTool(Tool):
    def __init__(self, env):
        self.env = env
        super().__init__(
            name="search_courses",
            description="Tìm kiếm khóa học dựa trên từ khóa, danh mục, cấp độ, v.v.",
            func=self.execute
        )

    def execute(self, query: str, category: Optional[str] = None, level: Optional[str] = None, limit: int = 10) -> Dict[str, Any]:
        # Logic tìm kiếm khóa học (giữ nguyên)
        return {"courses": courses, "total": len(courses), "query": query}
```

### 2. Chuyển đổi CourseAgent

#### Trước:

```python
def create_course_agent(env, model=None):
    # Lấy system prompt
    system_prompt = get_course_agent_prompt()
    
    # Tạo các tool
    course_search_tool = CourseSearchTool(env)
    course_detail_tool = CourseDetailTool(env)
    
    # Lấy các ADK Tool
    tools = [
        course_search_tool.get_adk_tool(),
        course_detail_tool.get_adk_tool()
    ]
    
    # Tạo Agent ADK
    adk_agent = Agent(
        model=model,
        global_instruction=system_prompt,
        name="course_agent",
        tools=tools
    )
    
    return adk_agent
```

#### Sau:

```python
from odoo.addons.eb_a2a.agents.base_agents import Agent

def create_course_agent(env, llm=None):
    # Tạo các tool
    course_search_tool = CourseSearchTool(env)
    course_detail_tool = CourseDetailTool(env)
    
    # Tạo Agent CrewAI
    agent = Agent(
        role="Course Search Expert",
        goal="Tìm kiếm và cung cấp thông tin chi tiết về các khóa học",
        backstory="Bạn là chuyên gia về khóa học, có kiến thức sâu rộng về tất cả các khóa học trong hệ thống.",
        verbose=True,
        llm=llm,
        tools=[course_search_tool, course_detail_tool]
    )
    
    return agent
```

### 3. Chuyển đổi LMS Multi-Agent

#### Trước:

```python
def create_lms_multi_agent(env, model=None):
    # Tạo các sub-agent
    course_agent = create_course_agent(env, model)
    instructor_agent = create_instructor_agent(env, model)
    class_agent = create_class_agent(env, model)
    
    # Tạo router agent
    router_agent = create_router_agent(env, model, course_agent, instructor_agent, class_agent)
    
    return router_agent
```

#### Sau:

```python
from odoo.addons.eb_a2a.crews.base_crews import Crew, Process, Task

def create_lms_crew(env, llm=None):
    # Tạo các agent
    course_agent = create_course_agent(env, llm=llm)
    instructor_agent = create_instructor_agent(env, llm=llm)
    class_agent = create_class_agent(env, llm=llm)
    
    # Tạo các task
    course_task = Task(
        description="Tìm kiếm và cung cấp thông tin chi tiết về khóa học theo yêu cầu của người dùng",
        expected_output="Danh sách khóa học phù hợp với yêu cầu, bao gồm tên, mô tả, giá, và các thông tin khác",
        agent=course_agent
    )
    
    instructor_task = Task(
        description="Tìm kiếm và cung cấp thông tin chi tiết về giảng viên theo yêu cầu của người dùng",
        expected_output="Thông tin chi tiết về giảng viên, bao gồm tên, chuyên môn, và các khóa học đang dạy",
        agent=instructor_agent
    )
    
    class_task = Task(
        description="Tìm kiếm lớp học và cung cấp lịch học theo yêu cầu của người dùng",
        expected_output="Danh sách lớp học và lịch học phù hợp với yêu cầu",
        agent=class_agent
    )
    
    # Tạo Crew
    crew = Crew(
        agents=[course_agent, instructor_agent, class_agent],
        tasks=[course_task, instructor_task, class_task],
        process=Process.hierarchical,
        verbose=True
    )
    
    return crew
```

## Kết luận

Việc chuyển đổi từ Google ADK sang CrewAI đòi hỏi một số thay đổi trong cách triển khai tools, agents và multi-agent. Tuy nhiên, logic xử lý bên trong các tool và agent có thể được giữ nguyên, chỉ cần thay đổi cách khai báo và khởi tạo.

Bằng cách tuân theo hướng dẫn này, bạn có thể dễ dàng chuyển đổi các module mở rộng từ Google ADK sang CrewAI, tận dụng các tính năng mạnh mẽ của CrewAI như hiệu suất cao hơn, tính linh hoạt và khả năng tùy chỉnh sâu hơn.
