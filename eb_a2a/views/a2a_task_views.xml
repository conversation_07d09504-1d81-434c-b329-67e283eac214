<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Form View -->
    <record id="view_a2a_task_form" model="ir.ui.view">
        <field name="name">eb_a2a.task.form</field>
        <field name="model">eb_a2a.task</field>
        <field name="arch" type="xml">
            <form string="A2A Task">
                <header>
                    <button
                        name="action_cancel"
                        string="Hủy"
                        type="object"
                        invisible="state not in ('submitted', 'working', 'input_required')"
                    />
                    <button
                        name="action_retry"
                        string="Thử lại"
                        type="object"
                        invisible="state not in ('failed', 'canceled')"
                    />
                    <field name="state" widget="statusbar" statusbar_visible="submitted,working,input_required,completed,failed,canceled"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="task_id"/>
                            <field name="agent_endpoint_id"/>
                            <field name="user_id"/>
                        </group>
                        <group>
                            <field name="is_streaming"/>
                            <field
                                name="webhook_url"
                                invisible="not is_streaming"
                            />
                            <field
                                name="error_message"
                                invisible="state != 'failed'"
                            />
                        </group>
                    </group>
                    <notebook>
                        <page string="Messages" name="messages">
                            <widget name="a2a_messages" />
                        </page>
                        <page string="Artifacts" name="artifacts">
                            <field name="artifact_ids">
                                <list>
                                    <field name="name"/>
                                    <field name="artifact_type"/>
                                    <field name="create_date"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <chatter />
            </form>
        </field>
    </record>

    <!-- List View -->
    <record id="view_a2a_task_list" model="ir.ui.view">
        <field name="name">eb_a2a.task.list</field>
        <field name="model">eb_a2a.task</field>
        <field name="arch" type="xml">
            <list
                string="A2A Tasks"
                decoration-info="state in ('submitted', 'working')"
                decoration-danger="state == 'failed'"
                decoration-success="state == 'completed'"
                decoration-warning="state == 'input_required'"
            >
                <field name="name"/>
                <field name="task_id"/>
                <field name="agent_endpoint_id"/>
                <field name="user_id"/>
                <field name="state"/>
                <field name="create_date"/>
            </list>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_a2a_task_search" model="ir.ui.view">
        <field name="name">eb_a2a.task.search</field>
        <field name="model">eb_a2a.task</field>
        <field name="arch" type="xml">
            <search string="Search A2A Tasks">
                <field name="name"/>
                <field name="task_id"/>
                <field name="agent_endpoint_id"/>
                <field name="user_id"/>
                <filter string="Của tôi" name="my_tasks" domain="[('user_id', '=', uid)]"/>
                <filter string="Đang xử lý" name="in_progress" domain="[('state', 'in', ['submitted', 'working', 'input_required'])]"/>
                <filter string="Hoàn thành" name="completed" domain="[('state', '=', 'completed')]"/>
                <filter string="Thất bại" name="failed" domain="[('state', '=', 'failed')]"/>
                <group expand="0" string="Group By">
                    <filter string="Agent Endpoint" name="group_by_agent_endpoint" domain="[]" context="{'group_by': 'agent_endpoint_id'}"/>
                    <filter string="Người dùng" name="group_by_user" domain="[]" context="{'group_by': 'user_id'}"/>
                    <filter string="Trạng thái" name="group_by_state" domain="[]" context="{'group_by': 'state'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="action_a2a_task" model="ir.actions.act_window">
        <field name="name">Tasks</field>
        <field name="res_model">eb_a2a.task</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_a2a_task_search"/>
        <field name="context">{'search_default_my_tasks': 1, 'search_default_in_progress': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Chưa có task nào
            </p>
            <p>
                Tasks là các yêu cầu được gửi đến agents thông qua A2A Protocol.
            </p>
        </field>
    </record>

    <!-- Menu Item -->
    <menuitem id="menu_eb_a2a_task"
              name="Tasks"
              parent="menu_eb_a2a_assistant"
              action="action_a2a_task"
              sequence="20"/>
</odoo>
