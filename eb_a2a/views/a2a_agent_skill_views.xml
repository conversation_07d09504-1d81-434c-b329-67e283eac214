<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Form View -->
    <record id="view_a2a_agent_skill_form" model="ir.ui.view">
        <field name="name">eb_a2a.agent.skill.form</field>
        <field name="model">eb_a2a.agent.skill</field>
        <field name="arch" type="xml">
            <form string="Agent Skill">
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="description"/>
                        <field name="agent_id"/>
                    </group>
                    <group string="Parameters">
                        <field name="parameters" widget="ace" options="{'mode': 'json'}"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- List View -->
    <record id="view_a2a_agent_skill_list" model="ir.ui.view">
        <field name="name">eb_a2a.agent.skill.list</field>
        <field name="model">eb_a2a.agent.skill</field>
        <field name="arch" type="xml">
            <list string="Agent Skills">
                <field name="name"/>
                <field name="description"/>
                <field name="agent_id"/>
            </list>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_a2a_agent_skill_search" model="ir.ui.view">
        <field name="name">eb_a2a.agent.skill.search</field>
        <field name="model">eb_a2a.agent.skill</field>
        <field name="arch" type="xml">
            <search string="Search Agent Skills">
                <field name="name"/>
                <field name="description"/>
                <field name="agent_id"/>
                <group expand="0" string="Group By">
                    <filter string="Agent" name="group_by_agent" domain="[]" context="{'group_by': 'agent_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="action_a2a_agent_skill" model="ir.actions.act_window">
        <field name="name">Agent Skills</field>
        <field name="res_model">eb_a2a.agent.skill</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_a2a_agent_skill_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Tạo kỹ năng đầu tiên cho agent
            </p>
            <p>
                Kỹ năng cho phép agent thực hiện các tác vụ cụ thể.
            </p>
        </field>
    </record>

    <!-- Menu Items -->
    <menuitem id="menu_a2a_agent_skill"
              name="Agent Skills"
              parent="menu_a2a_assistant"
              action="action_a2a_agent_skill"
              sequence="15"/>
</odoo>
