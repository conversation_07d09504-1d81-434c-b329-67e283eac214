<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Kanban View -->
    <record id="view_a2a_task_kanban" model="ir.ui.view">
        <field name="name">eb_a2a.task.kanban</field>
        <field name="model">eb_a2a.task</field>
        <field name="arch" type="xml">
            <kanban class="o_a2a_task_kanban" default_group_by="state" records_draggable="0">
                <field name="id"/>
                <field name="name"/>
                <field name="task_id"/>
                <field name="agent_endpoint_id"/>
                <field name="user_id"/>
                <field name="state"/>
                <field name="create_date"/>
                <field name="error_message"/>

                <templates>
                    <t t-name="card">
                        <div class="oe_kanban_global_click o_a2a_task_card" t-att-data-task-id="record.id.raw_value">
                            <div class="o_a2a_task_card_header">
                                <div class="o_a2a_task_card_status">
                                    <span t-attf-class="o_a2a_task_card_status_badge badge {{record.state.raw_value == 'submitted' ? 'badge-submitted' :
                                        record.state.raw_value == 'working' ? 'badge-working' :
                                        record.state.raw_value == 'input_required' ? 'badge-input_required' :
                                        record.state.raw_value == 'completed' ? 'badge-completed' :
                                        record.state.raw_value == 'failed' ? 'badge-failed' : 'badge-canceled'}}">
                                        <t t-if="record.state.raw_value == 'submitted'">Đã gửi</t>
                                        <t t-elif="record.state.raw_value == 'working'">Đang xử lý</t>
                                        <t t-elif="record.state.raw_value == 'input_required'">Cần thêm thông tin</t>
                                        <t t-elif="record.state.raw_value == 'completed'">Hoàn thành</t>
                                        <t t-elif="record.state.raw_value == 'failed'">Thất bại</t>
                                        <t t-else="">Đã hủy</t>
                                    </span>
                                </div>
                                <div class="o_a2a_task_card_date">
                                    <small><t t-esc="record.create_date.value"/></small>
                                </div>
                            </div>
                            <div class="o_a2a_task_card_body">
                                <div class="o_a2a_task_card_title">
                                    <h5><t t-esc="record.name.value"/></h5>
                                </div>
                                <div class="o_a2a_task_card_info">
                                    <div class="o_a2a_task_card_info_item">
                                        <i class="fa fa-robot me-1" title="Agent"></i>
                                        <span><t t-esc="record.agent_endpoint_id.value"/></span>
                                    </div>
                                    <div class="o_a2a_task_card_info_item">
                                        <i class="fa fa-user me-1" title="User"></i>
                                        <span><t t-esc="record.user_id.value"/></span>
                                    </div>
                                </div>
                                <div t-if="record.error_message.raw_value" class="o_a2a_task_card_error">
                                    <i class="fa fa-exclamation-triangle me-1" title="Error"></i>
                                    <span><t t-esc="record.error_message.raw_value.substring(0, 100) + (record.error_message.raw_value.length > 100 ? '...' : '')"/></span>
                                </div>
                            </div>
                            <div class="o_a2a_task_card_footer">
                                <button class="btn btn-sm btn-primary" name="action_view_task" type="object">
                                    <i class="fa fa-eye me-1" title="View"></i> Xem
                                </button>
                                <button t-if="record.state.raw_value == 'failed' || record.state.raw_value == 'canceled'" class="btn btn-sm btn-secondary" name="action_retry" type="object">
                                    <i class="fa fa-redo me-1" title="Retry"></i> Thử lại
                                </button>
                                <button t-if="record.state.raw_value == 'submitted' || record.state.raw_value == 'working' || record.state.raw_value == 'input_required'" class="btn btn-sm btn-danger" name="action_cancel" type="object">
                                    <i class="fa fa-times me-1" title="Cancel"></i> Hủy
                                </button>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Update Action to include Kanban view -->
    <record id="action_a2a_task" model="ir.actions.act_window">
        <field name="view_mode">kanban,list,form</field>
    </record>
</odoo>
