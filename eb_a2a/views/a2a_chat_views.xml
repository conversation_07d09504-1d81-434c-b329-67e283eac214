<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Chat Dashboard View -->
    <record id="view_a2a_chat_dashboard" model="ir.ui.view">
        <field name="name">eb_a2a.chat.dashboard</field>
        <field name="model">eb_a2a.agent_endpoint</field>
        <field name="arch" type="xml">
            <form string="AI Assistant Test">
                <header>
                    <button name="generate_agent_card" type="object" string="Generate Agent Card" class="btn-primary"/>
                    <button name="action_test_connection" type="object" string="Test Connection" class="btn-secondary"/>
                </header>
                <sheet>
                    <div class="row">
                        <div class="col-md-4">
                            <group>
                                <field name="name"/>
                                <field name="code"/>
                                <field name="description"/>
                                <field name="provider_id"/>
                                <field name="deployment_type"/>
                                <field name="framework"/>
                                <field name="supports_streaming"/>
                            </group>
                            <div class="alert alert-info" role="alert">
                                <strong><PERSON><PERSON><PERSON> ý:</strong> <PERSON><PERSON> khi thay đổi thông tin, h<PERSON><PERSON> l<PERSON>u lại tr<PERSON>ớ<PERSON> khi test chat.
                            </div>
                        </div>
                        <div class="col-md-8">
                            <widget
                                name="a2a_chat"
                                agentCode="code"
                                supportsStreaming="true"
                            />
                        </div>
                    </div>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Action -->
    <record id="action_a2a_chat" model="ir.actions.act_window">
        <field name="name">AI Assistant Test</field>
        <field name="res_model">eb_a2a.agent_endpoint</field>
        <field name="view_mode">list,form</field>
        <field name="view_id" eval="False"/>
        <field name="target">current</field>
        <field name="domain">[('active', '=', True)]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Không tìm thấy agent endpoint nào
            </p>
            <p>
                Vui lòng tạo một agent endpoint trước khi sử dụng AI Assistant Test.
            </p>
        </field>
    </record>

    <!-- Form Action -->
    <record id="action_a2a_chat_form" model="ir.actions.act_window.view">
        <field name="sequence">20</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="view_a2a_chat_dashboard"/>
        <field name="act_window_id" ref="action_a2a_chat"/>
    </record>

    <!-- List Action -->
    <record id="action_a2a_chat_list" model="ir.actions.act_window.view">
        <field name="sequence">10</field>
        <field name="view_mode">list</field>
        <field name="view_id" ref="view_eb_a2a_agent_endpoint_list"/>
        <field name="act_window_id" ref="action_a2a_chat"/>
    </record>

    <!-- Menu Item -->
    <menuitem id="menu_eb_a2a_chat"
              name="Test Agents"
              parent="menu_eb_a2a_assistant"
              action="action_a2a_chat"
              sequence="5"/>
</odoo>
