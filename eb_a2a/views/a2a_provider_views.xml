<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Provider Form View -->
    <record id="view_a2a_provider_form" model="ir.ui.view">
        <field name="name">eb_a2a.provider.form</field>
        <field name="model">eb_a2a.provider</field>
        <field name="arch" type="xml">
            <form string="AI Provider">
                <header>
                    <button name="action_test_connection" string="Test Connection" type="object" class="oe_highlight"/>
                    <button name="action_fetch_models" string="Fetch Models" type="object" class="btn-primary"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Provider Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="code"/>
                            <field name="api_type"/>
                            <field name="api_base"/>
                            <field name="api_version" invisible="api_type != 'azure'" required="api_type == 'azure'"/>
                        </group>
                        <group>
                            <field name="auth_type"/>
                            <field name="api_key" password="True" invisible="auth_type == 'none'" required="auth_type != 'none'"/>
                            <field name="organization_id" invisible="api_type != 'openai'"/>
                            <field name="active"/>
                            <field name="is_default"/>
                            <field name="max_history_length"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="System Message" name="system_message">
                            <field name="system_message" placeholder="Thông điệp hệ thống cho AI"/>
                        </page>
                        <page string="Models" name="models">
                            <field name="model_ids" context="{'default_provider_id': id}" mode="list,form" views_switcher="bottom">
                                <list>
                                    <field name="name"/>
                                    <field name="model_id"/>
                                    <field name="model_type"/>
                                    <field name="is_default"/>
                                </list>
                                <form>
                                    <group>
                                        <group>
                                            <field name="name"/>
                                            <field name="model_id"/>
                                            <field name="provider_id" invisible="1"/>
                                            <field name="model_type"/>
                                        </group>
                                        <group>
                                            <field name="max_tokens"/>
                                            <field name="context_window"/>
                                            <field name="supports_functions"/>
                                            <field name="supports_vision"/>
                                            <field name="supports_streaming"/>
                                            <field name="is_default"/>
                                            <field name="active"/>
                                        </group>
                                    </group>
                                    <field name="description" placeholder="Describe this model..."/>
                                </form>
                            </field>
                        </page>
                        <page string="Description" name="description">
                            <field name="description" placeholder="Describe this provider..."/>
                        </page>
                        <page string="Agent Endpoints" name="agent_endpoints">
                            <field name="agent_endpoint_ids" readonly="1">
                                <list>
                                    <field name="name"/>
                                    <field name="code"/>
                                    <field name="deployment_type"/>
                                    <field name="framework"/>
                                    <field name="active"/>
                                </list>
                            </field>
                        </page>

                    </notebook>
                </sheet>
                <chatter />
            </form>
        </field>
    </record>

    <!-- Provider Tree View -->
    <record id="view_a2a_provider_list" model="ir.ui.view">
        <field name="name">eb_a2a.provider.list</field>
        <field name="model">eb_a2a.provider</field>
        <field name="arch" type="xml">
            <list string="AI Providers">
                <field name="name"/>
                <field name="code"/>
                <field name="api_type"/>
                <field name="api_base"/>
                <field name="auth_type"/>
                <field name="is_default"/>
                <field name="active"/>
            </list>
        </field>
    </record>

    <!-- Provider Search View -->
    <record id="view_a2a_provider_search" model="ir.ui.view">
        <field name="name">eb_a2a.provider.search</field>
        <field name="model">eb_a2a.provider</field>
        <field name="arch" type="xml">
            <search string="Search AI Providers">
                <field name="name"/>
                <field name="code"/>
                <field name="api_type"/>
                <field name="api_base"/>
                <filter string="Mặc định" name="default" domain="[('is_default', '=', True)]"/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="API Type" name="group_by_api_type" context="{'group_by': 'api_type'}"/>
                    <filter string="Auth Type" name="group_by_auth_type" context="{'group_by': 'auth_type'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Provider Model Form View -->
    <record id="view_a2a_provider_model_form" model="ir.ui.view">
        <field name="name">eb_a2a.provider.model.form</field>
        <field name="model">eb_a2a.provider.model</field>
        <field name="arch" type="xml">
            <form string="AI Model">
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="model_id"/>
                            <field name="provider_id"/>
                            <field name="model_type"/>
                        </group>
                        <group>
                            <field name="max_tokens"/>
                            <field name="context_window"/>
                            <field name="supports_functions"/>
                            <field name="supports_vision"/>
                            <field name="supports_streaming"/>
                            <field name="is_default"/>
                            <field name="active"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Description" name="description">
                            <field name="description" placeholder="Describe this model..."/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Provider Model Tree View -->
    <record id="view_a2a_provider_model_list" model="ir.ui.view">
        <field name="name">eb_a2a.provider.model.list</field>
        <field name="model">eb_a2a.provider.model</field>
        <field name="arch" type="xml">
            <list string="AI Models">
                <field name="sequence"/>
                <field name="name"/>
                <field name="model_id"/>
                <field name="provider_id"/>
                <field name="model_type"/>
                <field name="max_tokens"/>
                <field name="is_default"/>
                <field name="active"/>
            </list>
        </field>
    </record>

    <!-- Provider Model List View (Embedded) -->
    <record id="view_a2a_provider_model_embedded_list" model="ir.ui.view">
        <field name="name">eb_a2a.provider.model.embedded.list</field>
        <field name="model">eb_a2a.provider.model</field>
        <field name="arch" type="xml">
            <list string="AI Models" editable="bottom">
                <field name="sequence"/>
                <field name="name"/>
                <field name="model_id"/>
                <field name="model_type"/>
                <field name="max_tokens"/>
                <field name="context_window"/>
                <field name="supports_functions"/>
                <field name="supports_vision"/>
                <field name="supports_streaming"/>
                <field name="is_default"/>
                <field name="active"/>
            </list>
        </field>
    </record>

    <!-- Provider Model Search View -->
    <record id="view_a2a_provider_model_search" model="ir.ui.view">
        <field name="name">eb_a2a.provider.model.search</field>
        <field name="model">eb_a2a.provider.model</field>
        <field name="arch" type="xml">
            <search string="Search AI Models">
                <field name="name"/>
                <field name="model_id"/>
                <field name="provider_id"/>
                <field name="model_type"/>
                <filter string="Default Models" name="default" domain="[('is_default', '=', True)]"/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="Provider" name="group_by_provider" context="{'group_by': 'provider_id'}"/>
                    <filter string="Model Type" name="group_by_model_type" context="{'group_by': 'model_type'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Provider Action -->
    <record id="action_a2a_provider" model="ir.actions.act_window">
        <field name="name">AI Providers</field>
        <field name="res_model">eb_a2a.provider</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_a2a_provider_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first AI Provider
            </p>
            <p>
                AI Providers allow you to connect to various AI services like OpenAI, Azure OpenAI, Anthropic, etc.
            </p>
        </field>
    </record>

    <!-- Provider Model Action -->
    <record id="action_a2a_provider_model" model="ir.actions.act_window">
        <field name="name">AI Models</field>
        <field name="res_model">eb_a2a.provider.model</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_a2a_provider_model_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first AI Model
            </p>
            <p>
                AI Models are specific models provided by AI Providers that can be used for different tasks.
            </p>
        </field>
    </record>

    <!-- Menu Items -->
    <menuitem id="menu_eb_a2a_provider"
              name="AI Providers"
              parent="menu_eb_a2a_configuration"
              action="action_a2a_provider"
              sequence="20"/>

    <menuitem id="menu_eb_a2a_provider_model"
              name="AI Models"
              parent="menu_eb_a2a_configuration"
              action="action_a2a_provider_model"
              sequence="30"/>
</odoo>
