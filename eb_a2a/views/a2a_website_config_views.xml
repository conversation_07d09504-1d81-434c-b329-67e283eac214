<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Form View -->
    <record id="view_a2a_website_config_form" model="ir.ui.view">
        <field name="name">eb_a2a.website.config.form</field>
        <field name="model">eb_a2a.website.config</field>
        <field name="arch" type="xml">
            <form string="Cấu hình Chatbot Website">
                <sheet>
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only"/>
                        <h1><field name="name"/></h1>
                    </div>
                    <group>
                        <group string="Thông tin Website">
                            <field name="website_code"/>
                            <field name="website_url"/>
                            <field name="is_enabled"/>
                        </group>
                        <group string="Cấu hình Agent">
                            <field name="agent_endpoint_id" options="{'no_create': True}"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="<PERSON><PERSON><PERSON> h<PERSON>nh hiển thị">
                            <group>
                                <group string="Hiển thị">
                                    <field name="auto_popup"/>
                                    <field name="popup_delay" invisible="auto_popup == False"/>
                                </group>
                                <group string="Giao diện">
                                    <field name="header_text"/>
                                    <field name="welcome_message"/>
                                    <field name="placeholder_text"/>
                                    <field name="button_text"/>
                                    <field name="theme_color" widget="color"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <chatter />
            </form>
        </field>
    </record>

    <!-- Tree View -->
    <record id="view_a2a_website_config_list" model="ir.ui.view">
        <field name="name">eb_a2a.website.config.list</field>
        <field name="model">eb_a2a.website.config</field>
        <field name="arch" type="xml">
            <list string="Cấu hình Chatbot Website">
                <field name="name"/>
                <field name="website_code"/>
                <field name="website_url"/>
                <field name="agent_endpoint_id"/>
                <field name="is_enabled"/>
            </list>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_a2a_website_config_search" model="ir.ui.view">
        <field name="name">eb_a2a.website.config.search</field>
        <field name="model">eb_a2a.website.config</field>
        <field name="arch" type="xml">
            <search string="Tìm kiếm cấu hình">
                <field name="name"/>
                <field name="website_code"/>
                <field name="website_url"/>
                <field name="agent_endpoint_id"/>
                <filter string="Đã kích hoạt" name="active" domain="[('is_enabled', '=', True)]"/>
                <filter string="Tự động hiển thị" name="auto_popup" domain="[('auto_popup', '=', True)]"/>
                <group expand="0" string="Group By">
                    <filter string="Agent" name="group_by_agent" context="{'group_by': 'agent_endpoint_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="action_a2a_website_config" model="ir.actions.act_window">
        <field name="name">Cấu hình Chatbot Website</field>
        <field name="res_model">eb_a2a.website.config</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Tạo cấu hình chatbot cho website
            </p>
            <p>
                Cấu hình chatbot cho từng website để cung cấp trải nghiệm hỗ trợ khách hàng tốt nhất.
            </p>
        </field>
    </record>

    <!-- Menu -->
    <menuitem id="menu_a2a_website_config"
              name="Cấu hình Website"
              parent="menu_eb_a2a_configuration"
              action="action_a2a_website_config"
              sequence="30"/>
</odoo>
