<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- List View -->
    <record id="view_eb_a2a_agent_endpoint_list" model="ir.ui.view">
        <field name="name">eb_a2a.agent_endpoint.list</field>
        <field name="model">eb_a2a.agent_endpoint</field>
        <field name="arch" type="xml">
            <list string="Agent Endpoints">
                <field name="name"/>
                <field name="code"/>
                <field name="deployment_type"/>
                <field name="agent_implementation" invisible="deployment_type != 'internal'"/>
                <field name="crew_registry_id" invisible="deployment_type != 'multi'"/>
                <field name="endpoint_url" invisible="deployment_type != 'external'"/>
                <field name="framework"/>
                <field name="active" widget="boolean_toggle"/>
            </list>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_eb_a2a_agent_endpoint_form" model="ir.ui.view">
        <field name="name">eb_a2a.agent_endpoint.form</field>
        <field name="model">eb_a2a.agent_endpoint</field>
        <field name="arch" type="xml">
            <form string="Agent Endpoint">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_test_connection" type="object" class="oe_stat_button" icon="fa-check">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Test Connection</span>
                            </div>
                        </button>
                        <button name="generate_agent_card" type="object" class="oe_stat_button" icon="fa-id-card">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Generate Agent Card</span>
                            </div>
                        </button>
                    </div>
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only"/>
                        <h1><field name="name" placeholder="Agent Endpoint Name"/></h1>
                        <label for="code" class="oe_edit_only"/>
                        <h2><field name="code" placeholder="agent_code"/></h2>
                    </div>
                    <group>
                        <group>
                            <field name="deployment_type" widget="radio"/>
                            <field name="agent_implementation" invisible="deployment_type != 'internal'" required="deployment_type == 'internal'"/>
                            <field name="crew_registry_id" invisible="deployment_type != 'multi'" required="deployment_type == 'multi'"/>
                            <field name="endpoint_url" invisible="deployment_type != 'external'" required="deployment_type == 'external'"/>
                            <field name="active"/>
                        </group>
                        <group>
                            <field name="framework" widget="radio"/>
                            <field name="provider_id" options="{'no_create': True, 'no_open': True}"/>
                            <field name="ai_adapter_type" invisible="framework != 'openmanus'"/>
                            <field name="supports_streaming"/>
                            <field name="supports_push_notifications"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Description">
                            <field name="description" placeholder="Describe the agent endpoint and its capabilities..."/>
                        </page>
                        <page string="System Message">
                            <field name="system_message" placeholder="System message for the AI..."/>
                        </page>
                        <page string="Authentication" invisible="deployment_type != 'external'">
                            <group>
                                <field name="auth_required"/>
                                <field name="auth_type" invisible="not auth_required"/>
                                <field name="auth_config" invisible="not auth_required" widget="ace" options="{'mode': 'json'}" placeholder="{&#10;  // Authentication configuration in JSON format&#10;  // Example for API Key:&#10;  // &quot;api_key&quot;: &quot;your-api-key&quot;&#10;}"/>
                            </group>
                        </page>
                        <page string="Agent Card">
                            <field name="agent_card" widget="ace" options="{'mode': 'json'}" readonly="1"/>
                        </page>
                        <page string="Skills">
                            <field name="skill_ids">
                                <list editable="bottom">
                                    <field name="name"/>
                                    <field name="description"/>
                                </list>
                                <form>
                                    <group>
                                        <field name="name"/>
                                        <field name="description"/>
                                        <field name="parameters" widget="ace" options="{'mode': 'json'}"/>
                                    </group>
                                </form>
                            </field>
                        </page>
                        <page string="Tasks">
                            <field name="task_ids" readonly="1"/>
                        </page>
                    </notebook>
                </sheet>
                <chatter />
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_eb_a2a_agent_endpoint_search" model="ir.ui.view">
        <field name="name">eb_a2a.agent_endpoint.search</field>
        <field name="model">eb_a2a.agent_endpoint</field>
        <field name="arch" type="xml">
            <search string="Search Agent Endpoints">
                <field name="name"/>
                <field name="code"/>
                <field name="description"/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                <separator/>
                <filter string="Internal" name="internal" domain="[('deployment_type', '=', 'internal')]"/>
                <filter string="External" name="external" domain="[('deployment_type', '=', 'external')]"/>
                <filter string="Multi-Agent" name="multi" domain="[('deployment_type', '=', 'multi')]"/>
                <group expand="0" string="Group By">
                    <filter string="Deployment Type" name="group_by_deployment_type" context="{'group_by': 'deployment_type'}"/>
                    <filter string="Framework" name="group_by_framework" context="{'group_by': 'framework'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="action_eb_a2a_agent_endpoint" model="ir.actions.act_window">
        <field name="name">Agent Endpoints</field>
        <field name="res_model">eb_a2a.agent_endpoint</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first agent endpoint!
            </p>
            <p>
                Agent endpoints are used to connect to AI agents, either internal or external.
            </p>
        </field>
    </record>

    <!-- Menu Item -->
    <menuitem id="menu_eb_a2a_agent_endpoint"
              name="Agent Endpoints"
              parent="menu_eb_a2a_root"
              action="action_eb_a2a_agent_endpoint"
              sequence="10"/>
</odoo>
