<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- List View -->
    <record id="view_eb_a2a_crew_registry_list" model="ir.ui.view">
        <field name="name">eb_a2a.crew.registry.list</field>
        <field name="model">eb_a2a.crew.registry</field>
        <field name="arch" type="xml">
            <list string="Crew Registries">
                <field name="name"/>
                <field name="code"/>
                <field name="module_path"/>
                <field name="function_name"/>
                <field name="process_type"/>
                <field name="active" widget="boolean_toggle"/>
            </list>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_eb_a2a_crew_registry_form" model="ir.ui.view">
        <field name="name">eb_a2a.crew.registry.form</field>
        <field name="model">eb_a2a.crew.registry</field>
        <field name="arch" type="xml">
            <form string="Crew Registry">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Tên Crew Registry"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="code"/>
                            <field name="active"/>
                            <field name="process_type" widget="radio"/>
                            <field name="requires_manager" readonly="1"/>
                        </group>
                        <group>
                            <field name="module_path"/>
                            <field name="function_name"/>
                            <field name="manager_agent_module_path"
                                   invisible="process_type != 'hierarchical'"
                                   required="process_type == 'hierarchical'"/>
                            <field name="manager_agent_function_name"
                                   invisible="process_type != 'hierarchical'"
                                   required="process_type == 'hierarchical'"/>
                        </group>
                    </group>
                    <group string="Mô tả">
                        <field name="description" nolabel="1"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_eb_a2a_crew_registry_search" model="ir.ui.view">
        <field name="name">eb_a2a.crew.registry.search</field>
        <field name="model">eb_a2a.crew.registry</field>
        <field name="arch" type="xml">
            <search string="Tìm kiếm Crew Registry">
                <field name="name"/>
                <field name="code"/>
                <field name="module_path"/>
                <field name="function_name"/>
                <separator/>
                <filter string="Đang hoạt động" name="active" domain="[('active', '=', True)]"/>
                <filter string="Không hoạt động" name="inactive" domain="[('active', '=', False)]"/>
                <separator/>
                <filter string="Sequential" name="sequential" domain="[('process_type', '=', 'sequential')]"/>
                <filter string="Hierarchical" name="hierarchical" domain="[('process_type', '=', 'hierarchical')]"/>
                <group expand="0" string="Group By">
                    <filter string="Module Path" name="group_by_module_path" context="{'group_by': 'module_path'}"/>
                    <filter string="Process Type" name="group_by_process_type" context="{'group_by': 'process_type'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="action_eb_a2a_crew_registry" model="ir.actions.act_window">
        <field name="name">Crew Registries</field>
        <field name="res_model">eb_a2a.crew.registry</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_eb_a2a_crew_registry_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Tạo Crew Registry đầu tiên
            </p>
            <p>
                Crew Registry cho phép đăng ký các crew từ các module khác nhau.
            </p>
        </field>
    </record>

    <!-- Menu Item -->
    <menuitem id="menu_eb_a2a_crew_registry"
              name="Crew Registries"
              parent="menu_eb_a2a_configuration"
              action="action_eb_a2a_crew_registry"
              sequence="40"/>
</odoo>
