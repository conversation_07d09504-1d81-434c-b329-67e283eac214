/* A2A Chat Widget CSS */

/* Thinking animation */
.o_a2a_chat_thinking {
    display: flex;
    align-items: center;
    padding: 10px;
    margin-bottom: 10px;
    background-color: #f8f9fa;
    border-radius: 10px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    animation: pulse 2s infinite;
}

.o_a2a_chat_thinking_content {
    display: flex;
    align-items: center;
}

.o_a2a_chat_thinking_text {
    margin-left: 10px;
    font-style: italic;
    color: #6c757d;
}

.o_a2a_chat_thinking_dots {
    display: flex;
    align-items: center;
    margin-left: 5px;
}

.o_a2a_chat_thinking_dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #6c757d;
    margin: 0 2px;
    opacity: 0.6;
}

.o_a2a_chat_thinking_dot:nth-child(1) {
    animation: dot-pulse 1.5s infinite 0s;
}

.o_a2a_chat_thinking_dot:nth-child(2) {
    animation: dot-pulse 1.5s infinite 0.3s;
}

.o_a2a_chat_thinking_dot:nth-child(3) {
    animation: dot-pulse 1.5s infinite 0.6s;
}

@keyframes dot-pulse {
    0% {
        opacity: 0.6;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.2);
    }
    100% {
        opacity: 0.6;
        transform: scale(1);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }
    50% {
        box-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
    }
    100% {
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }
}
