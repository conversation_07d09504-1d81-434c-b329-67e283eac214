.o_a2a_messages {
    max-height: 600px;
    overflow-y: auto;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 8px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);

    // Loading state
    .o_a2a_messages_loading {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px 0;
        color: #6c757d;

        .o_a2a_messages_loading_spinner {
            margin-bottom: 16px;
        }

        p {
            margin: 0;
            font-size: 1rem;
        }
    }

    // Empty state
    .o_a2a_message_empty {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px 0;
        color: #6c757d;

        .o_a2a_message_empty_icon {
            margin-bottom: 16px;
            opacity: 0.5;
        }

        p {
            margin: 0;
            font-size: 1rem;
        }
    }

    // Message list
    .o_a2a_message_list {
        .o_a2a_message_items {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        // Individual message
        .o_a2a_message_item {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;

            // Message header
            .o_a2a_message_item_header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 12px 16px;
                background-color: #f8f9fa;
                border-bottom: 1px solid #eee;

                .o_a2a_message_item_author {
                    display: flex;
                    align-items: center;

                    .o_a2a_message_item_avatar {
                        width: 32px;
                        height: 32px;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: white;
                        margin-right: 12px;
                    }

                    .o_a2a_message_item_info {
                        .o_a2a_message_item_name {
                            font-weight: 600;
                            color: #212529;
                        }

                        .o_a2a_message_item_role {
                            margin-top: 2px;

                            .badge {
                                font-size: 0.7rem;
                                padding: 2px 6px;
                                border-radius: 10px;
                            }
                        }
                    }
                }

                .o_a2a_message_item_time {
                    color: #6c757d;
                    font-size: 0.8rem;
                }
            }

            // Message body
            .o_a2a_message_item_body {
                padding: 16px;

                .o_a2a_message_content {
                    word-break: break-word;

                    // Images
                    img {
                        max-width: 100%;
                        height: auto;
                        margin: 8px 0;
                        border-radius: 8px;
                        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                    }

                    // Code blocks
                    pre {
                        background-color: #f5f5f5;
                        padding: 12px 16px;
                        border-radius: 8px;
                        overflow-x: auto;
                        margin: 12px 0;
                        border: 1px solid #e0e0e0;
                        font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
                        font-size: 0.9rem;
                    }

                    // Inline code
                    code {
                        background-color: #f5f5f5;
                        padding: 2px 4px;
                        border-radius: 4px;
                        font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
                        font-size: 0.9em;
                        color: #e83e8c;
                    }

                    // Headings
                    h1, h2, h3, h4, h5, h6 {
                        margin-top: 16px;
                        margin-bottom: 8px;
                        font-weight: 600;
                        line-height: 1.25;
                    }

                    // Links
                    a {
                        color: $o-brand-primary;
                        text-decoration: none;

                        &:hover {
                            text-decoration: underline;
                        }
                    }

                    // Lists
                    ul, ol {
                        padding-left: 20px;
                        margin: 8px 0;
                    }

                    // Blockquotes
                    blockquote {
                        border-left: 4px solid #ddd;
                        padding: 8px 16px;
                        margin: 16px 0;
                        color: #6c757d;
                        font-style: italic;
                    }

                    // Tables
                    table {
                        border-collapse: collapse;
                        width: 100%;
                        margin: 16px 0;

                        th, td {
                            border: 1px solid #ddd;
                            padding: 8px 12px;
                        }

                        th {
                            background-color: #f5f5f5;
                            font-weight: 600;
                        }

                        tr:nth-child(even) {
                            background-color: #f9f9f9;
                        }
                    }

                    // Horizontal rules
                    hr {
                        height: 0.25em;
                        padding: 0;
                        margin: 24px 0;
                        background-color: #e1e4e8;
                        border: 0;
                    }

                    // Paragraphs
                    p {
                        margin-top: 0;
                        margin-bottom: 10px;
                    }
                }
            }

            // Message footer
            .o_a2a_message_item_footer {
                padding: 8px 16px;
                border-top: 1px solid #eee;
                display: flex;
                justify-content: flex-end;

                .o_a2a_message_item_actions {
                    .o_a2a_message_action {
                        color: #6c757d;
                        padding: 4px 8px;
                        border-radius: 4px;

                        &:hover {
                            background-color: #f0f0f0;
                            color: $o-brand-primary;
                        }
                    }
                }
            }
        }
    }
}
