.o_a2a_chat_widget {
    display: flex;
    flex-direction: column;
    height: 600px;
    border: 1px solid #ddd;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    background-color: #f8f9fa;

    // Header
    .o_a2a_chat_header {
        background-color: $o-brand-primary;
        color: white;
        padding: 12px 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        .o_a2a_chat_header_left {
            display: flex;
            align-items: center;

            h4 {
                margin: 0;
                font-weight: 600;
            }
        }

        .o_a2a_chat_header_right {
            .o_a2a_chat_header_button {
                color: white;
                padding: 4px 8px;
                border-radius: 4px;

                &:hover {
                    background-color: rgba(255, 255, 255, 0.2);
                }
            }
        }
    }

    // Messages container
    .o_a2a_chat_messages {
        flex: 1;
        overflow-y: auto;
        padding: 16px;
        background-color: #f8f9fa;
        display: flex;
        flex-direction: column;
        gap: 16px;

        // Message bubbles
        .o_a2a_chat_message {
            display: flex;
            margin-bottom: 8px;

            // Avatar
            .o_a2a_chat_message_avatar {
                margin-right: 12px;
                align-self: flex-start;

                .o_a2a_chat_avatar {
                    width: 36px;
                    height: 36px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;

                    &.o_a2a_chat_avatar_user {
                        background-color: $o-brand-primary;
                    }

                    &.o_a2a_chat_avatar_agent {
                        background-color: $o-brand-odoo;
                    }

                    &.o_a2a_chat_avatar_system {
                        background-color: #6c757d;
                    }
                }
            }

            // Message bubble
            .o_a2a_chat_message_bubble {
                max-width: 80%;
                padding: 12px 16px;
                border-radius: 16px;
                position: relative;
                box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

                .o_a2a_chat_message_content, .o_a2a_html_content, .o_a2a_html_renderer {
                    word-break: break-word;

                    // Markdown styling
                    p {
                        margin-bottom: 8px;

                        &:last-child {
                            margin-bottom: 0;
                        }
                    }

                    pre {
                        background-color: #f0f0f0;
                        padding: 8px 12px;
                        border-radius: 4px;
                        overflow-x: auto;
                        margin: 8px 0;
                    }

                    code {
                        background-color: #f0f0f0;
                        padding: 2px 4px;
                        border-radius: 4px;
                        font-family: monospace;
                    }

                    ul, ol {
                        padding-left: 20px;
                        margin: 8px 0;
                    }

                    img {
                        max-width: 100%;
                        border-radius: 8px;
                        margin: 8px 0;
                    }

                    a {
                        color: $o-brand-primary;
                        text-decoration: none;

                        &:hover {
                            text-decoration: underline;
                        }
                    }

                    // Fix for HTML tags
                    * {
                        max-width: 100%;
                    }

                    // Fix for HTML content
                    .o_a2a_html_content, .o_a2a_html_renderer {
                        width: 100%;
                        padding: 0;
                        margin: 0;
                    }

                    // Fix for HTML tags that might be rendered incorrectly
                    p, div, h1, h2, h3, h4, h5, h6, blockquote, pre {
                        display: block;
                    }

                    // Fix for list items
                    ul, ol {
                        display: block;
                        padding-left: 20px;
                        margin: 8px 0;
                    }

                    li {
                        display: list-item;
                    }

                    // Inline elements
                    span, a, strong, em {
                        display: inline;
                    }

                    // Fix for code tag
                    :not(pre) > code {
                        display: inline;
                        background-color: #f0f0f0;
                        padding: 2px 4px;
                        border-radius: 4px;
                        font-family: monospace;
                    }

                    pre > code {
                        display: block;
                        background-color: transparent;
                        padding: 0;
                        border-radius: 0;
                        font-family: monospace;
                    }

                    // Fix for specific HTML tags
                    p, div, h1, h2, h3, h4, h5, h6 {
                        margin-bottom: 8px;
                    }

                    // Fix for HTML tags that might be used in Markdown
                    blockquote {
                        border-left: 3px solid #ddd;
                        padding-left: 10px;
                        margin-left: 0;
                        color: #666;
                    }

                    table {
                        border-collapse: collapse;
                        width: 100%;
                        margin-bottom: 10px;

                        th, td {
                            border: 1px solid #ddd;
                            padding: 8px;
                            text-align: left;
                        }

                        th {
                            background-color: #f2f2f2;
                        }

                        tr:nth-child(even) {
                            background-color: #f9f9f9;
                        }
                    }
                }

                // Message actions
                .o_a2a_chat_message_actions {
                    position: absolute;
                    top: 4px;
                    right: 4px;
                    display: none;

                    .o_a2a_chat_message_action {
                        padding: 2px 4px;
                        color: #6c757d;

                        &:hover {
                            color: $o-brand-primary;
                        }
                    }
                }

                &:hover .o_a2a_chat_message_actions {
                    display: block;
                }

                // Message timestamp
                .o_a2a_chat_message_time {
                    font-size: 0.75rem;
                    opacity: 0.7;
                    margin-top: 4px;
                    text-align: right;
                }
            }

            // User message styling
            &.o_a2a_chat_message_user {
                flex-direction: row-reverse;

                .o_a2a_chat_message_avatar {
                    margin-right: 0;
                    margin-left: 12px;
                }

                .o_a2a_chat_message_bubble {
                    background-color: $o-brand-primary;
                    color: white;
                    border-bottom-right-radius: 4px;

                    .o_a2a_chat_message_action {
                        color: rgba(255, 255, 255, 0.8);

                        &:hover {
                            color: white;
                        }
                    }

                    a {
                        color: white;
                        text-decoration: underline;
                    }

                    code {
                        background-color: rgba(255, 255, 255, 0.2);
                        color: white;
                    }
                }
            }

            // Agent message styling
            &.o_a2a_chat_message_agent {
                .o_a2a_chat_message_bubble {
                    background-color: white;
                    border-bottom-left-radius: 4px;

                    // Specific styling for agent messages
                    .o_a2a_chat_message_content {
                        // Fix for HTML tags in agent messages
                        p {
                            margin-bottom: 8px;

                            &:last-child {
                                margin-bottom: 0;
                            }
                        }

                        // Fix for HTML entities
                        p:first-child {
                            margin-top: 0;
                        }
                    }
                }
            }

            // System message styling
            &.o_a2a_chat_message_system {
                justify-content: center;

                .o_a2a_chat_message_bubble {
                    background-color: #f1f1f1;
                    border: 1px solid #ddd;
                    font-style: italic;
                    max-width: 90%;
                    text-align: center;
                }
            }
        }

        // Loading indicator
        .o_a2a_chat_loading {
            align-self: flex-start;
            margin: 0 0 0 48px;

            .o_a2a_chat_typing {
                display: flex;
                align-items: center;
                background-color: white;
                padding: 12px 16px;
                border-radius: 16px;
                box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

                .o_a2a_chat_typing_dot {
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    background-color: #aaa;
                    margin: 0 3px;
                    animation: typing-dot 1.4s infinite ease-in-out;

                    &:nth-child(1) {
                        animation-delay: 0s;
                    }

                    &:nth-child(2) {
                        animation-delay: 0.2s;
                    }

                    &:nth-child(3) {
                        animation-delay: 0.4s;
                    }
                }
            }
        }

        // Error message
        .o_a2a_chat_error {
            margin: 0 48px;
            padding: 8px 12px;
            font-size: 0.9rem;
        }
    }

    // Input area
    .o_a2a_chat_input {
        padding: 12px 16px;
        background-color: white;
        border-top: 1px solid #ddd;
        position: relative;

        .o_a2a_chat_input_container {
            display: flex;
            align-items: flex-end;

            textarea {
                resize: none;
                min-height: 40px;
                max-height: 120px;
                overflow-y: auto;
                padding: 10px 12px;
                border-radius: 20px;
                border-color: #ddd;
                box-shadow: none;

                &:focus {
                    box-shadow: 0 0 0 1px rgba($o-brand-primary, 0.3);
                    border-color: $o-brand-primary;
                }
            }

            .o_a2a_chat_input_actions {
                display: flex;
                align-items: center;
                margin-left: 8px;

                .o_a2a_chat_input_action {
                    color: #6c757d;
                    padding: 6px;
                    border-radius: 50%;

                    &:hover {
                        background-color: #f0f0f0;
                        color: $o-brand-primary;
                    }
                }

                .o_a2a_chat_input_send {
                    height: 40px;
                    width: 40px;
                    padding: 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 50%;
                    margin-left: 8px;
                }
            }
        }

        // Attachment menu
        .o_a2a_chat_attachment_menu {
            position: absolute;
            bottom: 100%;
            right: 16px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 8px 0;
            z-index: 100;

            .o_a2a_chat_attachment_menu_item {
                padding: 8px 16px;
                cursor: pointer;
                white-space: nowrap;

                &:hover {
                    background-color: #f0f0f0;
                }
            }
        }
    }
}

// Animation for typing dots
@keyframes typing-dot {
    0%, 60%, 100% {
        transform: translateY(0);
    }
    30% {
        transform: translateY(-4px);
    }
}
