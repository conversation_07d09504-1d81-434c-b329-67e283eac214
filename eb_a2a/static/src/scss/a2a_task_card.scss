.o_a2a_task_card {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 16px;
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s;
    
    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    
    .o_a2a_task_card_header {
        padding: 12px 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #f0f0f0;
        
        .o_a2a_task_card_status_badge {
            font-size: 0.8rem;
            padding: 4px 8px;
            border-radius: 12px;
            
            &.badge-submitted {
                background-color: #6c757d;
            }
            
            &.badge-working {
                background-color: #17a2b8;
            }
            
            &.badge-input_required {
                background-color: #ffc107;
                color: #212529;
            }
            
            &.badge-completed {
                background-color: #28a745;
            }
            
            &.badge-failed {
                background-color: #dc3545;
            }
            
            &.badge-canceled {
                background-color: #6c757d;
            }
        }
        
        .o_a2a_task_card_date {
            color: #6c757d;
            font-size: 0.8rem;
        }
    }
    
    .o_a2a_task_card_body {
        padding: 16px;
        
        .o_a2a_task_card_title {
            margin-bottom: 12px;
            
            h5 {
                margin: 0;
                font-weight: 600;
                color: #212529;
            }
        }
        
        .o_a2a_task_card_info {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin-bottom: 12px;
            
            .o_a2a_task_card_info_item {
                display: flex;
                align-items: center;
                color: #495057;
                font-size: 0.9rem;
                
                i {
                    color: #6c757d;
                }
            }
        }
        
        .o_a2a_task_card_error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 0.9rem;
            margin-top: 12px;
            
            i {
                color: #dc3545;
            }
        }
    }
    
    .o_a2a_task_card_footer {
        padding: 12px 16px;
        border-top: 1px solid #f0f0f0;
        display: flex;
        gap: 8px;
        justify-content: flex-end;
    }
}

// Task list view
.o_a2a_task_list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
    padding: 16px;
    
    @media (max-width: 767px) {
        grid-template-columns: 1fr;
    }
}

// Task filters
.o_a2a_task_filters {
    background-color: white;
    padding: 16px;
    margin-bottom: 16px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    
    .o_a2a_task_filters_header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        
        h5 {
            margin: 0;
            font-weight: 600;
        }
    }
    
    .o_a2a_task_filters_body {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        
        .o_a2a_task_filter {
            padding: 6px 12px;
            border-radius: 16px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            cursor: pointer;
            transition: all 0.2s;
            
            &:hover {
                background-color: #e9ecef;
            }
            
            &.active {
                background-color: $o-brand-primary;
                color: white;
                border-color: $o-brand-primary;
            }
        }
    }
}
