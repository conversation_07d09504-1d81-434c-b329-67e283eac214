/** @odoo-module **/

import { registry } from "@web/core/registry";
import { useService } from "@web/core/utils/hooks";
import { Component, useState, onWillStart, onWillUpdateProps } from "@odoo/owl";
import { renderMarkdown, formatTime, copyToClipboard } from "./a2a_markdown";

class A2AMessages extends Component {
    static template = "eb_a2a.A2AMessages";
    static props = {
        record: Object,
        readonly: { type: Boolean, optional: true },
    };

    setup() {
        this.orm = useService("orm");
        this.notification = useService("notification");

        this.state = useState({
            messages: [],
            loading: true,
        });

        onWillStart(async () => {
            await this.loadMessages();
        });

        onWillUpdateProps(async (nextProps) => {
            if (nextProps.record.resId !== this.props.record.resId) {
                await this.loadMessages();
            }
        });
    }

    /**
     * Load messages for the current task
     */
    async loadMessages() {
        if (!this.props.record || !this.props.record.resId) {
            this.state.messages = [];
            this.state.loading = false;
            return;
        }

        this.state.loading = true;

        try {
            // Get messages for the current task
            const messages = await this.orm.call(
                "mail.message",
                "search_read",
                [
                    [
                        ["model", "=", "eb_a2a.task"],
                        ["res_id", "=", this.props.record.resId],
                        ["message_type", "!=", "user_notification"],
                    ],
                ],
                {
                    fields: [
                        "id",
                        "body",
                        "author_id",
                        "create_date",
                        "a2a_role",
                        "a2a_parts",
                    ],
                    order: "create_date asc",
                }
            );

            // Process messages
            this.state.messages = messages.map((msg) => {
                // Convert body to HTML content
                let htmlContent = msg.body || "";

                // Check if message has a2a_parts
                if (msg.a2a_parts) {
                    try {
                        const parts = JSON.parse(msg.a2a_parts);
                        // If there are text parts, use them instead of body
                        const textParts = parts.filter(part => part.type === 'text');
                        if (textParts.length > 0) {
                            // Use the first text part
                            const textContent = textParts[0].text;
                            // Check if content is likely markdown
                            if (this.isLikelyMarkdown(textContent)) {
                                htmlContent = renderMarkdown(textContent);
                            } else {
                                // Wrap in paragraph tags if not already HTML
                                if (!textContent.includes('<')) {
                                    htmlContent = `<p>${textContent}</p>`;
                                } else {
                                    htmlContent = textContent;
                                }
                            }
                        }
                    } catch (e) {
                        console.error("Error parsing a2a_parts:", e);
                    }
                } else if (msg.body) {
                    // If no a2a_parts, try to render body as markdown if it looks like markdown
                    if (this.isLikelyMarkdown(msg.body)) {
                        htmlContent = renderMarkdown(msg.body);
                    }
                }

                return {
                    ...msg,
                    author_name: msg.author_id ? msg.author_id[1] : "System",
                    role_label: this.getRoleLabel(msg.a2a_role),
                    role_class: this.getRoleClass(msg.a2a_role),
                    html_content: htmlContent,
                };
            });
        } catch (error) {
            console.error("Error loading A2A messages:", error);
            this.state.messages = [];
        } finally {
            this.state.loading = false;
        }
    }

    /**
     * Get role label based on role
     *
     * @param {string} role - Message role
     * @returns {string} - Role label
     */
    getRoleLabel(role) {
        const roles = {
            "user": "Người dùng",
            "agent": "Agent",
            "system": "Hệ thống",
        };
        return roles[role] || role || "Unknown";
    }

    /**
     * Get role class based on role
     *
     * @param {string} role - Message role
     * @returns {string} - CSS class
     */
    getRoleClass(role) {
        const classes = {
            "user": "bg-primary",
            "agent": "bg-success",
            "system": "bg-info",
        };
        return classes[role] || "bg-secondary";
    }

    /**
     * Get avatar color based on role
     *
     * @param {string} role - Message role
     * @returns {string} - CSS color
     */
    getAvatarColor(role) {
        const colors = {
            "user": "#007bff",
            "agent": "#28a745",
            "system": "#6c757d",
        };
        return colors[role] || "#6c757d";
    }

    /**
     * Format message time
     *
     * @param {string} dateStr - Date string
     * @returns {string} - Formatted time
     */
    formatMessageTime(dateStr) {
        return formatTime(new Date(dateStr));
    }

    /**
     * Check if text is likely markdown
     *
     * @param {string} text - Text to check
     * @returns {boolean} - True if likely markdown
     */
    isLikelyMarkdown(text) {
        if (!text) return false;

        // Check for common markdown patterns
        const markdownPatterns = [
            /^#+\s+.+$/m,                // Headers
            /\*\*.+\*\*/,                // Bold
            /\*.+\*/,                    // Italic
            /\[.+\]\(.+\)/,              // Links
            /```[\s\S]+```/,             // Code blocks
            /`[^`]+`/,                   // Inline code
            /^\s*[\*\-\+]\s+.+$/m,       // Unordered lists
            /^\s*\d+\.\s+.+$/m,          // Ordered lists
            /!\[.+\]\(.+\)/,             // Images
            /^\s*>\s+.+$/m,              // Blockquotes
            /^\s*---\s*$/m,              // Horizontal rules
            /\|\s*:?-+:?\s*\|/           // Tables
        ];

        return markdownPatterns.some(pattern => pattern.test(text));
    }

    /**
     * Copy message content to clipboard
     *
     * @param {Object} message - Message object
     */
    async copyMessageContent(message) {
        try {
            // Extract text content from HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = message.html_content;
            const textContent = tempDiv.textContent || tempDiv.innerText || '';

            // Copy to clipboard
            const success = await copyToClipboard(textContent);

            if (success) {
                this.notification.add('Đã sao chép vào clipboard', {
                    type: 'success',
                    title: 'Thành công',
                });
            } else {
                this.notification.add('Không thể sao chép nội dung', {
                    type: 'warning',
                    title: 'Lỗi',
                });
            }
        } catch (error) {
            console.error('Error copying message content:', error);
            this.notification.add('Không thể sao chép nội dung', {
                type: 'danger',
                title: 'Lỗi',
            });
        }
    }
}

registry.category("view_widgets").add("a2a_messages", {
    component: A2AMessages,
});
