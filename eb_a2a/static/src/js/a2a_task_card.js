/** @odoo-module **/

import { registry } from "@web/core/registry";
import { useService } from "@web/core/utils/hooks";
import { Component, useState } from "@odoo/owl";

/**
 * Task Card Component
 * 
 * Displays a single task in a card format
 */
export class TaskCardComponent extends Component {
    static template = "eb_a2a.TaskCard";
    static props = {
        task: Object,
        onAction: { type: Function, optional: true },
    };

    setup() {
        this.orm = useService("orm");
        this.actionService = useService("action");
        this.notification = useService("notification");
    }

    /**
     * Get status label based on state
     * 
     * @param {string} state - Task state
     * @returns {string} - Status label
     */
    getStatusLabel(state) {
        const labels = {
            'submitted': 'Đã gửi',
            'working': 'Đang xử lý',
            'input_required': 'Cần thêm thông tin',
            'completed': 'Hoàn thành',
            'failed': 'Th<PERSON>t bại',
            'canceled': 'Đã hủy',
        };
        return labels[state] || state;
    }

    /**
     * Get status class based on state
     * 
     * @param {string} state - Task state
     * @returns {string} - CSS class
     */
    getStatusClass(state) {
        return `badge-${state}`;
    }

    /**
     * Format date for display
     * 
     * @param {string} dateStr - Date string
     * @returns {string} - Formatted date
     */
    formatDate(dateStr) {
        if (!dateStr) return '';
        
        const date = new Date(dateStr);
        const now = new Date();
        const diffMs = now - date;
        const diffSec = Math.floor(diffMs / 1000);
        const diffMin = Math.floor(diffSec / 60);
        const diffHour = Math.floor(diffMin / 60);
        const diffDay = Math.floor(diffHour / 24);

        // Format based on how long ago
        if (diffSec < 60) {
            return 'Vừa xong';
        } else if (diffMin < 60) {
            return `${diffMin} phút trước`;
        } else if (diffHour < 24) {
            return `${diffHour} giờ trước`;
        } else if (diffDay < 7) {
            return `${diffDay} ngày trước`;
        } else {
            return date.toLocaleDateString('vi-VN');
        }
    }

    /**
     * Truncate text to specified length
     * 
     * @param {string} text - Text to truncate
     * @param {number} maxLength - Maximum length
     * @returns {string} - Truncated text
     */
    truncateText(text, maxLength = 100) {
        if (!text) return '';
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }

    /**
     * Open task detail view
     * 
     * @param {number} taskId - Task ID
     */
    openTask(taskId) {
        this.actionService.doAction({
            type: 'ir.actions.act_window',
            res_model: 'eb_a2a.task',
            res_id: taskId,
            views: [[false, 'form']],
            target: 'current',
        });
    }

    /**
     * Retry failed task
     * 
     * @param {number} taskId - Task ID
     */
    async retryTask(taskId) {
        try {
            await this.orm.call('eb_a2a.task', 'action_retry', [taskId]);
            this.notification.add('Task đã được khởi động lại', {
                type: 'success',
                title: 'Thành công',
            });
            if (this.props.onAction) {
                this.props.onAction('retry', taskId);
            }
        } catch (error) {
            this.notification.add('Không thể khởi động lại task', {
                type: 'danger',
                title: 'Lỗi',
            });
            console.error('Error retrying task:', error);
        }
    }

    /**
     * Cancel task
     * 
     * @param {number} taskId - Task ID
     */
    async cancelTask(taskId) {
        if (!confirm('Bạn có chắc chắn muốn hủy task này?')) {
            return;
        }
        
        try {
            await this.orm.call('eb_a2a.task', 'action_cancel', [taskId]);
            this.notification.add('Task đã bị hủy', {
                type: 'success',
                title: 'Thành công',
            });
            if (this.props.onAction) {
                this.props.onAction('cancel', taskId);
            }
        } catch (error) {
            this.notification.add('Không thể hủy task', {
                type: 'danger',
                title: 'Lỗi',
            });
            console.error('Error canceling task:', error);
        }
    }
}

registry.category("components").add("a2a_task_card", TaskCardComponent);
