/** @odoo-module **/

/**
 * Markdown utilities for A2A module
 */

// Import marked và hàm loadMarkedLibrary từ thư viện đã được include trong module
// Thư viện marked đã được include trong assets_backend trong __manifest__.py
import { markedLib, loadMarkedLibrary } from "./marked_setup";
import { escape } from "@web/core/utils/strings";

// Biến để lưu trữ instance của marked
let markedInstance = null;

// Hàm để lấy instance của marked
async function getMarked() {
    if (markedInstance) {
        return markedInstance;
    }

    try {
        // T<PERSON>i thư viện marked nếu chưa được tải
        await loadMarkedLibrary();

        // Kiểm tra xem marked đã được tải thành công chưa
        if (typeof window.marked === 'function') {
            markedInstance = window.marked;
            console.log("Marked library loaded successfully");
            return markedInstance;
        } else {
            console.warn("Marked library not available after loading attempt");
            return null;
        }
    } catch (error) {
        console.error("Error loading marked library:", error);
        return null;
    }
}

/**
 * Renders markdown content to HTML with additional security and formatting
 *
 * @param {string} content - Markdown content to render
 * @returns {string} - HTML content
 */
/**
 * Render Markdown thành HTML
 * Hàm này sẽ cố gắng sử dụng thư viện marked.js, nếu không có sẽ sử dụng phương pháp fallback
 */
export async function renderMarkdownAsync(content) {
    console.log("renderMarkdownAsync called with:", content);
    if (!content) {
        console.log("Empty content, returning empty string");
        return "";
    }

    try {
        // Lấy instance của marked
        const marked = await getMarked();

        // Sử dụng thư viện marked để chuyển đổi Markdown thành HTML
        if (marked) {
            // Cấu hình marked để đảm bảo an toàn
            const options = {
                breaks: true,        // Chuyển đổi line breaks thành <br>
                gfm: true,           // Sử dụng GitHub Flavored Markdown
                headerIds: false,    // Không tạo ID cho headers
            };

            // Chuyển đổi Markdown thành HTML
            let html = marked(content, options);

            console.log("Rendered HTML using marked:", html);
            return html;
        } else {
            // Fallback nếu marked không khả dụng
            return renderMarkdownFallback(content);
        }
    } catch (error) {
        console.error("Error rendering markdown:", error);
        // Fallback to simple paragraph
        return renderMarkdownFallback(content);
    }
}

/**
 * Phương pháp fallback để render Markdown khi không có thư viện marked
 */
function renderMarkdownFallback(content) {
    console.warn("Using fallback markdown renderer");
    try {
        // Xử lý các định dạng Markdown cơ bản
        let html = content;

        // Xử lý headers
        html = html.replace(/^# (.+)$/gm, '<h1>$1</h1>');
        html = html.replace(/^## (.+)$/gm, '<h2>$1</h2>');
        html = html.replace(/^### (.+)$/gm, '<h3>$1</h3>');

        // Xử lý bold và italic
        html = html.replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>');
        html = html.replace(/\*(.+?)\*/g, '<em>$1</em>');

        // Xử lý links
        html = html.replace(/\[(.+?)\]\((.+?)\)/g, '<a href="$2">$1</a>');

        // Xử lý code blocks
        html = html.replace(/```([\s\S]+?)```/g, '<pre><code>$1</code></pre>');

        // Xử lý inline code
        html = html.replace(/`([^`]+)`/g, '<code>$1</code>');

        // Xử lý danh sách không thứ tự
        html = html.replace(/^\s*[\*\-\+]\s+(.+)$/gm, '<li>$1</li>');
        html = html.replace(/(<li>.+<\/li>\n)+/g, '<ul>$&</ul>');

        // Xử lý danh sách có thứ tự
        html = html.replace(/^\s*\d+\.\s+(.+)$/gm, '<li>$1</li>');
        html = html.replace(/(<li>.+<\/li>\n)+/g, '<ol>$&</ol>');

        // Xử lý các đoạn văn bản
        const paragraphs = html.split('\n\n');
        html = '';
        for (const paragraph of paragraphs) {
            if (paragraph.trim() &&
                !paragraph.includes('<h') &&
                !paragraph.includes('<ul') &&
                !paragraph.includes('<ol') &&
                !paragraph.includes('<pre')) {
                html += `<p>${paragraph.trim()}</p>`;
            } else {
                html += paragraph;
            }
        }

        console.log("Rendered HTML with fallback:", html);
        return html;
    } catch (error) {
        console.error("Error in fallback renderer:", error);
        // Fallback to simple paragraph with escaped content
        return `<p>${escape(content)}</p>`;
    }
}

/**
 * Hàm render Markdown đồng bộ (cho tương thích ngược)
 */
export function renderMarkdown(content) {
    console.log("renderMarkdown called with:", content);
    if (!content) {
        return "";
    }

    // Gọi hàm async và xử lý kết quả sau
    renderMarkdownAsync(content).then(html => {
        // Tìm tất cả các phần tử có nội dung Markdown này
        const elements = document.querySelectorAll(`[data-markdown-content="${content.substring(0, 50).replace(/"/g, '&quot;')}"]`);
        for (const element of elements) {
            element.innerHTML = html;
        }
    }).catch(error => {
        console.error("Error rendering markdown asynchronously:", error);
    });

    // Trả về placeholder để sau đó cập nhật
    return `<div data-markdown-content="${escape(content.substring(0, 50))}">${escape(content)}</div>`;
}

/**
 * Detects if content is likely markdown
 *
 * @param {string} content - Content to check
 * @returns {boolean} - True if content is likely markdown
 */
export function isMarkdown(content) {
    // Luôn trả về true cho tin nhắn từ agent
    return true;
}

/**
 * Formats a timestamp for display
 *
 * @param {Date} timestamp - Timestamp to format
 * @returns {string} - Formatted timestamp
 */
export function formatTime(timestamp) {
    if (!timestamp) {
        return "";
    }

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const date = new Date(timestamp);
    const dateDay = new Date(date.getFullYear(), date.getMonth(), date.getDate());

    // Format options
    const timeOptions = { hour: '2-digit', minute: '2-digit' };
    const dateOptions = { day: '2-digit', month: '2-digit', year: 'numeric' };

    // Check if date is today, yesterday, or earlier
    if (dateDay.getTime() === today.getTime()) {
        return `Hôm nay, ${date.toLocaleTimeString(undefined, timeOptions)}`;
    } else if (dateDay.getTime() === yesterday.getTime()) {
        return `Hôm qua, ${date.toLocaleTimeString(undefined, timeOptions)}`;
    } else {
        return `${date.toLocaleDateString(undefined, dateOptions)}, ${date.toLocaleTimeString(undefined, timeOptions)}`;
    }
}

/**
 * Copies text to clipboard
 *
 * @param {string} text - Text to copy
 * @returns {Promise<boolean>} - True if copy was successful
 */
export async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        return true;
    } catch (err) {
        console.error("Failed to copy text: ", err);

        // Fallback method
        try {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.position = "fixed";
            textArea.style.left = "-999999px";
            textArea.style.top = "-999999px";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            const success = document.execCommand('copy');
            document.body.removeChild(textArea);
            return success;
        } catch (err) {
            console.error("Fallback copy failed: ", err);
            return false;
        }
    }
}

export default {
    renderMarkdown,
    isMarkdown,
    formatTime,
    copyToClipboard
};
