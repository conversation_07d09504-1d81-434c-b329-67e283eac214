/** @odoo-module **/

import { registry } from "@web/core/registry";
import { Component, useState, onMounted, onWillUnmount, useRef, useEffect } from "@odoo/owl";
import { standardFieldProps } from "@web/views/fields/standard_field_props";
import { renderMarkdown, renderMarkdownAsync, isMarkdown, formatTime, copyToClipboard } from "./a2a_markdown";
import { useService } from "@web/core/utils/hooks";

class A2AChatWidgetComponent extends Component {
    setup() {
        console.log("A2AChatWidgetComponent setup called");
        this.state = useState({
            messages: [],
            inputMessage: "",
            isLoading: false,
            error: null,
            taskId: null,
            showAttachmentMenu: false,
            isStreaming: false,
            currentStreamingMessage: null,
        });

        // References
        this.messagesContainer = useRef("messagesContainer");
        this.inputTextarea = useRef("inputTextarea");
        this.fileInput = useRef("fileInput");

        // Services
        this.notification = useService("notification");

        onMounted(() => {
            console.log("A2AChatWidgetComponent mounted", this);
            this.initChat();
            this.setupAutoResize();
            // Add click event listener to close attachment menu when clicking outside
            document.addEventListener("click", this.handleDocumentClick.bind(this));

            // Debug: Check if textarea has keydown event
            if (this.inputTextarea.el) {
                console.log("Textarea element found:", this.inputTextarea.el);
                console.log("Adding test keydown event listener");
                this.inputTextarea.el.addEventListener('keydown', (e) => {
                    console.log("Direct keydown event on textarea:", e.key);
                });
            } else {
                console.error("Textarea element not found!");
            }
        });

        // Sử dụng useEffect để render lại nội dung khi messages thay đổi
        useEffect(
            () => {
                console.log("Messages changed, count:", this.state.messages.length);
                // Đảm bảo messagesContainer đã được khởi tạo
                if (this.messagesContainer.el) {
                    // Render HTML content for all markdown messages
                    setTimeout(async () => {
                        // Lọc ra các tin nhắn Markdown cần render
                        const markdownMessages = this.state.messages.filter(
                            msg => msg.isMarkdown && msg.content && msg.content.trim() !== ""
                        );

                        console.log(`Found ${markdownMessages.length} markdown messages to render`);

                        // Render từng tin nhắn
                        for (const message of markdownMessages) {
                            console.log(`Rendering message ${message.id} from useEffect`);
                            try {
                                // Sử dụng await để đảm bảo các tin nhắn được render tuần tự
                                await this.renderMessageContent(message);
                            } catch (error) {
                                console.error(`Error rendering message ${message.id} from useEffect:`, error);
                            }
                        }

                        // Buộc cập nhật DOM sau khi render tất cả tin nhắn
                        this.forceUpdate();
                    }, 100);
                }
            },
            () => [this.state.messages.length]
        );

        onWillUnmount(() => {
            console.log("A2AChatWidgetComponent will unmount");
            // Remove event listeners
            document.removeEventListener("click", this.handleDocumentClick.bind(this));
        });
    }

    // Markdown rendering
    renderMarkdown(content) {
        return renderMarkdown(content);
    }

    // Format timestamp
    formatTime(timestamp) {
        return formatTime(timestamp);
    }

    async initChat() {
        try {
            console.log("Initializing chat");

            // Kiểm tra xem đã có taskId chưa
            if (!this.state.taskId) {
                console.log("Generating new task ID");
                this.state.taskId = this.generateTaskId();
            } else {
                console.log("Using existing task ID:", this.state.taskId);
            }

            // Kiểm tra xem đã có tin nhắn chào mừng chưa
            const hasWelcomeMessage = this.state.messages.some(msg =>
                msg.role === "agent" &&
                msg.content.includes("Xin chào! Tôi là trợ lý AI")
            );

            // Chỉ thêm tin nhắn chào mừng nếu chưa có
            if (!hasWelcomeMessage) {
                console.log("Adding welcome message");
                this.addMessage({
                    role: "agent",
                    content: "Xin chào! Tôi là trợ lý AI của hệ thống quản lý học tập. Tôi có thể giúp gì cho bạn?",
                });
            } else {
                console.log("Welcome message already exists, skipping");
            }

            // Log agent code for debugging
            console.log("Agent code:", this.props.agentCode);
        } catch (error) {
            this.handleError(error);
        }
    }

    generateTaskId() {
        return 'task-' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    }

    addMessage(message) {
        console.log("addMessage called with:", message);

        // Kiểm tra xem tin nhắn có nội dung không (trừ khi là tin nhắn thinking)
        if (!message.isThinking && (!message.content || message.content.trim() === "")) {
            console.log("Skipping empty message");
            return null; // Trả về null để biết tin nhắn không được thêm
        }

        // Check if this is a duplicate message - kiểm tra trong toàn bộ lịch sử tin nhắn
        const isDuplicate = this.state.messages.some(msg => {
            const isDuplicateContent = msg.role === message.role && msg.content === message.content;
            if (isDuplicateContent) {
                const timeDiff = new Date() - msg.timestamp;
                console.log(`Found duplicate message, time difference: ${timeDiff}ms`);
                // Chỉ coi là trùng lặp nếu thời gian chênh lệch < 30 giây
                return timeDiff < 30000;
            }
            return false;
        });

        if (!isDuplicate) {
            console.log("Message is not a duplicate, adding to chat");

            // Luôn xử lý tin nhắn từ agent như Markdown
            const isMarkdownContent = message.role === 'agent';
            console.log("Message is Markdown:", isMarkdownContent);

            // Log message content
            console.log("Message content:", message.content);
            console.log("Message content type:", typeof message.content);
            console.log("Message content length:", message.content ? message.content.length : 0);

            // Ensure content is a string
            if (message.content && typeof message.content !== 'string') {
                message.content = String(message.content);
                console.log("Converted content to string:", message.content);
            }

            const newMessage = {
                ...message,
                id: this.state.messages.length + 1,
                timestamp: new Date(),
                isMarkdown: isMarkdownContent,
            };

            console.log("Adding new message to state:", newMessage);
            this.state.messages.push(newMessage);

            // Scroll to bottom after message is added
            this.scrollToBottom();

            // Buộc cập nhật DOM để đảm bảo tin nhắn được hiển thị
            this.forceUpdate();

            // Trả về tin nhắn đã thêm
            return newMessage;
        } else {
            console.log("Duplicate message detected, not adding");
            return null; // Trả về null để biết tin nhắn không được thêm
        }
    }

    async sendMessage() {
        if (!this.state.inputMessage.trim()) return;

        // Lưu tin nhắn người dùng
        const userMessage = this.state.inputMessage;

        // Xóa nội dung input
        this.state.inputMessage = "";

        // Gọi phương thức gửi tin nhắn với nội dung đã lưu
        this.sendMessageWithText(userMessage);
        return;

        console.log("Sending message:", userMessage, "Task ID:", this.state.taskId);

        // Add user message to chat
        this.addMessage({
            role: "user",
            content: userMessage,
        });

        this.state.isLoading = true;

        try {
            // Send message to agent using Odoo's built-in JSON-RPC
            let data = {};
            try {
                const response = await fetch("/api/a2a/tasks/send", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify({
                        jsonrpc: "2.0",
                        id: Math.floor(Math.random() * 1000000000),
                        params: {
                            agent_code: this.props.agentCode || "odoo_lms_agent",
                            task_id: this.state.taskId,
                            message: {
                                role: "user",
                                parts: [
                                    {
                                        type: "text",
                                        text: userMessage,
                                    },
                                ],
                            },
                        },
                    }),
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                data = result.result || {};
            } catch (error) {
                console.error("Error calling API:", error);
                throw error;
            }

            // Process response
            if (data && data.messages) {
                // Find agent messages in the response
                const agentMessages = data.messages.filter(msg => msg.role === "agent");

                if (agentMessages.length > 0) {
                    // Add the latest agent message to chat
                    const latestMessage = agentMessages[agentMessages.length - 1];
                    let content = "";

                    // Extract text from parts
                    if (latestMessage.parts && latestMessage.parts.length > 0) {
                        for (const part of latestMessage.parts) {
                            if (part.type === "text" && part.text) {
                                content += part.text;
                            }
                        }
                    }

                    if (content) {
                        this.addMessage({
                            role: "agent",
                            content: content,
                        });
                    }
                }

                // Process artifacts if any
                if (data.artifacts && data.artifacts.length > 0) {
                    for (const artifact of data.artifacts) {
                        this.processArtifact(artifact);
                    }
                }
            }
        } catch (error) {
            this.handleError(error);

            // Add error message to chat
            this.addMessage({
                role: "system",
                content: "Xin lỗi, đã xảy ra lỗi khi xử lý yêu cầu của bạn.",
            });
        } finally {
            this.state.isLoading = false;
        }
    }

    processArtifact(artifact) {
        // Process different types of artifacts
        if (artifact.parts && artifact.parts.length > 0) {
            for (const part of artifact.parts) {
                if (part.type === "text" && part.text) {
                    // Add text artifact as system message
                    this.addMessage({
                        role: "system",
                        content: `Artifact: ${artifact.name}\n${part.text}`,
                    });
                } else if (part.type === "data" && part.data) {
                    // Process data artifact
                    console.log("Data artifact:", part.data);
                    // You can display this data in a structured way if needed
                }
            }
        }
    }

    handleError(error) {
        console.error("A2A Chat Error:", error);
        this.state.error = error.message || "Unknown error";
        // Use notification service
        this.notification.add(this.state.error, {
            type: 'danger',
            title: 'Lỗi',
            sticky: false,
            className: 'o_a2a_chat_notification',
        });
    }

    onInputKeydown(ev) {
        console.log("Key pressed:", ev.key);
        if (ev.key === "Enter" && !ev.shiftKey) {
            ev.preventDefault();
            console.log("Enter key pressed, calling sendMessageWithText()");

            // Lưu tin nhắn trước khi gọi sendMessage
            const message = this.state.inputMessage;
            console.log("Message from Enter key:", message);

            // Xóa input trước khi gọi sendMessage
            this.state.inputMessage = "";

            // Gọi sendMessage với tin nhắn đã lưu
            console.log("Calling sendMessageWithText from Enter key handler");
            this.sendMessageWithText(message);
        }
    }

    // Phương thức mới để gửi tin nhắn với nội dung đã cho
    async sendMessageWithText(text) {
        if (!text || !text.trim()) return;

        // Prevent double submission
        if (this.state.isLoading) {
            console.log("Preventing double submission - already loading");
            return;
        }

        console.log("Sending message with text:", text, "Task ID:", this.state.taskId);
        console.log("Props:", this.props);

        // Kiểm tra và log giá trị agentCode
        const agentCode = this.props.agentCode;
        console.log("Agent code from props:", agentCode);

        // Kiểm tra xem agentCode có phải là giá trị hợp lệ không
        if (!agentCode) {
            console.error("Missing agent code");
            this.handleError(new Error("Thiếu mã agent. Vui lòng lưu lại trước khi test chat."));
            this.state.isLoading = false;
            return;
        }

        // Kiểm tra xem agentCode có phải là chuỗi "code" không (chưa được thay thế đúng)
        if (agentCode === "code") {
            console.error("Invalid agent code: still using field name 'code' instead of actual value");
            this.handleError(new Error("Mã agent không hợp lệ. Vui lòng lưu lại trước khi test chat."));
            this.state.isLoading = false;
            return;
        }

        // Add user message to chat
        this.addMessage({
            role: "user",
            content: text,
        });

        this.state.isLoading = true;

        // Kiểm tra xem agent có hỗ trợ streaming không
        // Đảm bảo this.props.supportsStreaming là boolean
        // Sử dụng cách kiểm tra chặt chẽ hơn
        const trueValues = [true, "true", 1, "1", "True", "TRUE"];
        const supportsStreaming = trueValues.includes(this.props.supportsStreaming);
        console.log("Agent supports streaming:", supportsStreaming, "Type:", typeof this.props.supportsStreaming, "Value:", this.props.supportsStreaming);

        if (supportsStreaming) {
            console.log("Using streaming API");
            // Sử dụng streaming API
            await this.sendMessageWithStreaming(text);
        } else {
            console.log("Using regular API");
            // Sử dụng API thông thường
            await this.sendMessageWithoutStreaming(text);
        }
    }

    // Phương thức gửi tin nhắn với streaming
    async sendMessageWithStreaming(text) {
        // Tạo biến để theo dõi ID của tin nhắn thinking
        let thinkingMessageId = null;

        try {
            console.log("Using streaming API");

            // Đảm bảo trạng thái loading được đặt đúng
            this.state.isLoading = true;

            // Tạo message thinking để hiển thị trong lúc chờ
            const thinkingMessage = {
                role: "thinking",
                content: "AI đang suy nghĩ...",
                isThinking: true,
            };

            // Thêm tin nhắn thinking và lưu ID
            this.addMessage(thinkingMessage);

            // Tìm ID của tin nhắn thinking vừa thêm
            const thinkingMsg = this.state.messages.find(msg =>
                msg.role === "thinking" && msg.isThinking === true
            );

            if (thinkingMsg) {
                thinkingMessageId = thinkingMsg.id;
                console.log("Added thinking message with ID:", thinkingMessageId);
            }

            // Scroll to bottom
            this.scrollToBottom();

            // Tạo message trống để hiển thị streaming
            this.state.isStreaming = true;
            this.state.currentStreamingMessage = {
                role: "agent",
                content: "",
                id: this.state.messages.length + 1,
                timestamp: new Date(),
                isMarkdown: true,
            };

            // Tạo URL cho streaming API
            const url = `/api/a2a/tasks/sendSubscribe?agent_code=${encodeURIComponent(this.props.agentCode || "odoo_lms_agent")}`;
            console.log("Streaming URL with agent code:", url);

            // Tạo request body
            const body = JSON.stringify({
                task_id: this.state.taskId,
                message: {
                    role: "user",
                    parts: [
                        {
                            type: "text",
                            text: text,
                        },
                    ],
                },
            });

            console.log("Sending streaming request to:", url);

            // Gửi request
            const response = await fetch(url, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: body,
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // Xử lý streaming response
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let receivedFirstChunk = false;

            while (true) {
                const { done, value } = await reader.read();
                if (done) {
                    console.log("Streaming response completed");
                    break;
                }

                // Decode chunk
                const chunk = decoder.decode(value);
                console.log("Received chunk of size:", chunk.length);

                // Xử lý từng dòng trong chunk
                const lines = chunk.split("\n");
                for (const line of lines) {
                    if (line.startsWith("data: ")) {
                        const data = line.substring(6);
                        try {
                            const event = JSON.parse(data);
                            console.log("Parsed event type:", event.type);

                            // Xử lý các loại event
                            if (event.type === "chunk") {
                                // Nếu đây là chunk đầu tiên, xóa message thinking và thêm message streaming
                                if (!receivedFirstChunk) {
                                    receivedFirstChunk = true;
                                    console.log("Received first chunk, removing thinking message");

                                    // Xóa message thinking sử dụng phương thức mới
                                    console.log("Removing thinking messages using removeThinkingMessages()");
                                    this.removeThinkingMessages();

                                    // Thêm message streaming vào danh sách
                                    this.state.messages.push(this.state.currentStreamingMessage);
                                    console.log("Added streaming message to chat");
                                }

                                // Cập nhật nội dung message
                                if (event.content) {
                                    this.state.currentStreamingMessage.content += event.content;
                                    console.log("Updated streaming message content, new length:",
                                        this.state.currentStreamingMessage.content.length);

                                    // Buộc cập nhật DOM
                                    this.forceUpdate();

                                    // Render lại nội dung
                                    try {
                                        await this.renderMessageContent(this.state.currentStreamingMessage);
                                        console.log("Rendered streaming message content");
                                    } catch (error) {
                                        console.error("Error rendering streaming message content:", error);
                                    }

                                    // Scroll to bottom
                                    this.scrollToBottom();
                                }
                            } else if (event.type === "task_status_update") {
                                console.log("Task status update:", event.task);

                                // Kiểm tra xem có tin nhắn mới không
                                if (event.task && event.task.messages && event.task.messages.length > 0) {
                                    const agentMessages = event.task.messages.filter(msg => msg.role === "agent");
                                    if (agentMessages.length > 0) {
                                        const latestMessage = agentMessages[agentMessages.length - 1];
                                        if (latestMessage.parts && latestMessage.parts.length > 0) {
                                            const textPart = latestMessage.parts.find(part => part.type === "text");
                                            if (textPart && textPart.text) {
                                                // Nếu đây là tin nhắn hoàn chỉnh, cập nhật nội dung
                                                if (!receivedFirstChunk) {
                                                    // Xóa tin nhắn thinking
                                                    this.removeThinkingMessages();

                                                    // Thêm tin nhắn mới
                                                    this.addMessage({
                                                        role: "agent",
                                                        content: textPart.text,
                                                    });
                                                } else if (this.state.currentStreamingMessage) {
                                                    // Cập nhật nội dung tin nhắn streaming hiện tại
                                                    this.state.currentStreamingMessage.content = textPart.text;
                                                    this.forceUpdate();
                                                    await this.renderMessageContent(this.state.currentStreamingMessage);
                                                }
                                            }
                                        }
                                    }
                                }
                            } else if (event.type === "error") {
                                // Xóa message thinking sử dụng phương thức mới
                                console.log("Received error event, removing thinking messages");
                                this.removeThinkingMessages();

                                console.error("Streaming error:", event.error);
                                this.handleError(new Error(event.error));
                            }
                        } catch (e) {
                            console.error("Error parsing event data:", e);
                        }
                    }
                }
            }

            // Kết thúc streaming
            console.log("Ending streaming session");

            // Đảm bảo tin nhắn streaming được render lần cuối
            if (this.state.currentStreamingMessage) {
                try {
                    // Render lại nội dung lần cuối
                    await this.renderMessageContent(this.state.currentStreamingMessage);
                    console.log("Final render of streaming message content");
                } catch (error) {
                    console.error("Error rendering final streaming message content:", error);
                }

                // Buộc cập nhật DOM
                this.forceUpdate();
            }

            // Đặt lại trạng thái streaming
            this.state.isStreaming = false;
            this.state.currentStreamingMessage = null;

        } catch (error) {
            console.error("Error in streaming:", error);
            this.handleError(error);

            // Xóa message thinking sử dụng phương thức mới
            console.log("Removing thinking messages after error");
            this.removeThinkingMessages();

            // Add error message to chat
            this.addMessage({
                role: "system",
                content: "Xin lỗi, đã xảy ra lỗi khi xử lý yêu cầu của bạn.",
            });

            // Reset streaming state
            this.state.isStreaming = false;
            this.state.currentStreamingMessage = null;
        } finally {
            // Đảm bảo trạng thái loading được đặt lại
            console.log("Setting isLoading to false");
            this.state.isLoading = false;

            // Đảm bảo một lần nữa rằng tin nhắn thinking đã được xóa
            if (this.state.messages.some(msg => msg.isThinking)) {
                console.log("Thinking message still exists, removing again");
                this.removeThinkingMessages();
            }
        }
    }

    // Phương thức gửi tin nhắn không sử dụng streaming
    async sendMessageWithoutStreaming(text) {
        // Tạo biến để theo dõi ID của tin nhắn thinking
        let thinkingMessageId = null;

        try {
            console.log("Sending message without streaming");

            // Đảm bảo trạng thái loading được đặt đúng
            this.state.isLoading = true;

            // Tạo message thinking để hiển thị trong lúc chờ
            const thinkingMessage = {
                role: "thinking",
                content: "AI đang suy nghĩ...",
                isThinking: true,
            };

            // Thêm tin nhắn thinking và lưu ID
            this.addMessage(thinkingMessage);

            // Tìm ID của tin nhắn thinking vừa thêm
            const thinkingMsg = this.state.messages.find(msg =>
                msg.role === "thinking" && msg.isThinking === true
            );

            if (thinkingMsg) {
                thinkingMessageId = thinkingMsg.id;
                console.log("Added thinking message with ID:", thinkingMessageId);
            }

            // Scroll to bottom
            this.scrollToBottom();

            // Send message to agent using Odoo's built-in JSON-RPC
            let data = {};
            try {
                console.log("Sending API request to /api/a2a/tasks/send");
                const response = await fetch("/api/a2a/tasks/send", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify({
                        jsonrpc: "2.0",
                        id: Math.floor(Math.random() * 1000000000),
                        params: {
                            agent_code: (this.props.agentCode || "odoo_lms_agent").replace(/['"\`]/g, ""),
                            task_id: this.state.taskId,
                            message: {
                                role: "user",
                                parts: [
                                    {
                                        type: "text",
                                        text: text,
                                    },
                                ],
                            },
                        },
                    }),
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                data = result.result || {};
                console.log("Received API response:", data);
            } catch (error) {
                console.error("Error calling API:", error);
                throw error;
            }

            // Xóa message thinking sử dụng phương thức mới
            console.log("Removing thinking messages using removeThinkingMessages()");
            this.removeThinkingMessages();

            // Process response
            if (data && data.messages) {
                // Find agent messages in the response
                const agentMessages = data.messages.filter(msg => msg.role === "agent");
                console.log(`Found ${agentMessages.length} agent messages in response`);

                if (agentMessages.length > 0) {
                    // Add the latest agent message to chat
                    const latestMessage = agentMessages[agentMessages.length - 1];
                    let content = "";

                    // Extract text from parts
                    if (latestMessage.parts && latestMessage.parts.length > 0) {
                        for (const part of latestMessage.parts) {
                            if (part.type === "text" && part.text) {
                                // Thêm nội dung văn bản
                                const text = part.text;
                                content += text;
                            }
                        }
                    }

                    // Kiểm tra nội dung tin nhắn
                    if (content && content.trim()) {
                        console.log("Adding agent message with content:", content.substring(0, 50) + "...");
                        console.log("Content type:", typeof content);
                        console.log("Content length:", content.length);

                        // Ensure content is a string
                        if (typeof content !== 'string') {
                            content = String(content);
                            console.log("Converted content to string");
                        }

                        // Thêm tin nhắn agent và đảm bảo nó được hiển thị
                        const agentMessage = {
                            role: "agent",
                            content: content,
                        };

                        // Thêm tin nhắn vào state
                        this.addMessage(agentMessage);

                        // Đảm bảo tin nhắn được render ngay lập tức
                        setTimeout(async () => {
                            // Tìm tin nhắn vừa thêm
                            const addedMessage = this.state.messages.find(msg =>
                                msg.role === "agent" && msg.content === content
                            );

                            if (addedMessage && addedMessage.isMarkdown) {
                                // Render nội dung tin nhắn
                                await this.renderMessageContent(addedMessage);
                                // Buộc cập nhật DOM
                                this.forceUpdate();
                            }
                        }, 100);
                    } else {
                        console.warn("Agent message has no content or empty content");

                        // Thêm tin nhắn mặc định nếu nội dung trống
                        this.addMessage({
                            role: "agent",
                            content: "Xin lỗi, tôi không thể xử lý yêu cầu của bạn lúc này. Vui lòng thử lại sau.",
                        });
                    }
                } else {
                    console.warn("No agent messages found in response");

                    // Thêm tin nhắn mặc định nếu không tìm thấy tin nhắn agent
                    this.addMessage({
                        role: "agent",
                        content: "Xin lỗi, tôi không nhận được phản hồi từ hệ thống. Vui lòng thử lại sau.",
                    });
                }

                // Process artifacts if any
                if (data.artifacts && data.artifacts.length > 0) {
                    console.log(`Processing ${data.artifacts.length} artifacts`);
                    for (const artifact of data.artifacts) {
                        this.processArtifact(artifact);
                    }
                }
            } else {
                console.warn("No messages in response data");

                // Thêm tin nhắn mặc định nếu không có dữ liệu phản hồi
                this.addMessage({
                    role: "agent",
                    content: "Xin lỗi, tôi không nhận được phản hồi từ hệ thống. Vui lòng thử lại sau.",
                });
            }
        } catch (error) {
            console.error("Error in sendMessageWithoutStreaming:", error);
            this.handleError(error);

            // Xóa message thinking sử dụng phương thức mới
            console.log("Removing thinking messages after error");
            this.removeThinkingMessages();

            // Add error message to chat
            this.addMessage({
                role: "system",
                content: "Xin lỗi, đã xảy ra lỗi khi xử lý yêu cầu của bạn.",
            });
        } finally {
            // Đảm bảo trạng thái loading được đặt lại
            console.log("Setting isLoading to false");
            this.state.isLoading = false;

            // Đảm bảo một lần nữa rằng tin nhắn thinking đã được xóa
            if (this.state.messages.some(msg => msg.isThinking)) {
                console.log("Thinking message still exists, removing again");
                this.removeThinkingMessages();
            }

            // Buộc cập nhật DOM một lần nữa để đảm bảo tin nhắn được hiển thị
            setTimeout(() => {
                this.forceUpdate();
            }, 200);
        }
    }
}

A2AChatWidgetComponent.template = "eb_a2a.ChatWidget";
A2AChatWidgetComponent.props = {
    ...standardFieldProps,
    name: { type: String },
    agentCode: { type: String, optional: true },
    supportsStreaming: { type: Boolean, optional: true },
};

// Add new methods to the component prototype
Object.assign(A2AChatWidgetComponent.prototype, {
    /**
     * Buộc OWL cập nhật DOM bằng cách tạo bản sao mới của state
     */
    forceUpdate() {
        console.log("Forcing OWL component update");
        // Tạo bản sao mới của state.messages để đảm bảo OWL nhận biết sự thay đổi
        this.state.messages = [...this.state.messages];

        // Đảm bảo các trạng thái khác cũng được cập nhật
        this.state.isLoading = !!this.state.isLoading;
        this.state.isStreaming = !!this.state.isStreaming;

        // Scroll to bottom sau khi cập nhật
        this.scrollToBottom();
    },

    /**
     * Xóa tất cả tin nhắn thinking khỏi danh sách tin nhắn
     * Sử dụng phương pháp tạo mảng mới để đảm bảo OWL nhận biết sự thay đổi
     */
    removeThinkingMessages() {
        console.log("Removing all thinking messages");
        const messagesBeforeFilter = this.state.messages.length;

        // Lưu lại các ID của tin nhắn thinking để debug
        const thinkingMessageIds = this.state.messages
            .filter(msg => msg.isThinking)
            .map(msg => msg.id);

        console.log("Thinking message IDs to remove:", thinkingMessageIds);

        // Tạo mảng mới không chứa tin nhắn thinking
        const filteredMessages = this.state.messages.filter(msg => !msg.isThinking);

        // Log để debug
        console.log("Messages before filter:", this.state.messages.map(m => ({id: m.id, role: m.role, isThinking: m.isThinking})));
        console.log("Messages after filter:", filteredMessages.map(m => ({id: m.id, role: m.role, isThinking: m.isThinking})));

        // Gán mảng mới cho state.messages
        this.state.messages = filteredMessages;

        const messagesAfterFilter = this.state.messages.length;
        console.log(`Removed ${messagesBeforeFilter - messagesAfterFilter} thinking messages`);

        // Đảm bảo DOM được cập nhật
        if (messagesBeforeFilter > messagesAfterFilter) {
            // Nếu đã xóa tin nhắn, buộc cập nhật ngay lập tức
            this.forceUpdate();
        }

        // Kiểm tra lại sau một khoảng thời gian
        setTimeout(() => {
            // Kiểm tra lại xem còn tin nhắn thinking không
            const remainingThinking = this.state.messages.some(msg => msg.isThinking);
            if (remainingThinking) {
                console.warn("Thinking messages still exist after removal, forcing update again");

                // Log để debug
                const remainingThinkingIds = this.state.messages
                    .filter(msg => msg.isThinking)
                    .map(msg => msg.id);
                console.log("Remaining thinking message IDs:", remainingThinkingIds);

                // Tạo bản sao mới của mảng messages để đảm bảo OWL nhận biết sự thay đổi
                this.state.messages = [...this.state.messages.filter(msg => !msg.isThinking)];

                // Buộc cập nhật lại
                this.forceUpdate();
            }
        }, 100);
    },

    /**
     * Scroll the messages container to the bottom
     */
    scrollToBottom() {
        setTimeout(() => {
            const container = this.messagesContainer.el;
            if (container) {
                container.scrollTop = container.scrollHeight;
            }
        }, 50);
    },

    /**
     * Setup auto-resize for the textarea
     */
    setupAutoResize() {
        if (this.inputTextarea.el) {
            this.inputTextarea.el.addEventListener('input', () => {
                const textarea = this.inputTextarea.el;
                textarea.style.height = 'auto';
                textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
            });
        }
    },

    /**
     * Handle document click to close attachment menu
     */
    handleDocumentClick(event) {
        if (this.state.showAttachmentMenu && !event.target.closest('.o_a2a_chat_attachment_menu') &&
            !event.target.closest('.o_a2a_chat_input_action')) {
            this.state.showAttachmentMenu = false;
        }
    },

    /**
     * Toggle attachment menu
     */
    toggleAttachmentMenu() {
        this.state.showAttachmentMenu = !this.state.showAttachmentMenu;
    },

    /**
     * Upload file
     */
    uploadFile(type) {
        if (this.fileInput.el) {
            // Set accept attribute based on type
            if (type === 'image') {
                this.fileInput.el.setAttribute('accept', 'image/*');
            } else {
                this.fileInput.el.removeAttribute('accept');
            }
            this.fileInput.el.click();
        }
        this.state.showAttachmentMenu = false;
    },

    /**
     * Handle file upload
     */
    handleFileUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        // For now, just show a message that file upload is not implemented
        this.notification.add('Tính năng tải lên file đang được phát triển.', {
            type: 'info',
            title: 'Thông báo',
        });

        // Reset file input
        event.target.value = '';
    },

    /**
     * Clear chat history
     */
    clearChat() {
        if (confirm('Bạn có chắc chắn muốn xóa tất cả tin nhắn?')) {
            console.log("Clearing chat history");
            // Xóa tất cả tin nhắn
            this.state.messages = [];
            // Tạo task ID mới
            this.state.taskId = this.generateTaskId();
            console.log("New task ID after clear:", this.state.taskId);
            // Thêm tin nhắn chào mừng mới
            this.addMessage({
                role: "agent",
                content: "Xin chào! Tôi là trợ lý AI của hệ thống quản lý học tập. Tôi có thể giúp gì cho bạn?",
            });
            // Reset các trạng thái khác
            this.state.error = null;
            this.state.isStreaming = false;
            this.state.currentStreamingMessage = null;
            console.log("Chat cleared successfully");
        }
    },

    /**
     * Copy message content to clipboard
     */
    async copyMessageContent(content) {
        const success = await copyToClipboard(content);
        if (success) {
            this.notification.add('Đã sao chép vào clipboard', {
                type: 'success',
                title: 'Thành công',
            });
        } else {
            this.notification.add('Không thể sao chép nội dung', {
                type: 'warning',
                title: 'Lỗi',
            });
        }
    },

    /**
     * Render message content as HTML
     */
    async renderMessageContent(message) {
        if (!message || !message.isMarkdown) return;

        // Tìm phần tử bằng data-message-id
        const messageEl = this.messagesContainer.el?.querySelector(`[data-message-id="${message.id}"]`);

        if (messageEl) {
            console.log(`Rendering HTML content for message ${message.id}`);
            try {
                // Kiểm tra nội dung tin nhắn
                if (!message.content || message.content.trim() === "") {
                    console.warn(`Message ${message.id} has empty content, skipping rendering`);
                    return;
                }

                // Sử dụng hàm renderMarkdownAsync để render Markdown thành HTML
                const htmlContent = await renderMarkdownAsync(message.content);
                console.log(`HTML content: ${htmlContent.substring(0, 100)}...`);

                // Kiểm tra xem messageEl có còn tồn tại trong DOM không
                if (this.messagesContainer.el?.contains(messageEl)) {
                    messageEl.innerHTML = htmlContent;
                } else {
                    console.warn(`Element with data-message-id=${message.id} no longer in DOM`);

                    // Tìm lại phần tử
                    const newMessageEl = this.messagesContainer.el?.querySelector(`[data-message-id="${message.id}"]`);
                    if (newMessageEl) {
                        console.log(`Found new element for message ${message.id}`);
                        newMessageEl.innerHTML = htmlContent;
                    } else {
                        console.error(`Cannot find element for message ${message.id} after DOM update`);
                        // Buộc cập nhật DOM và thử lại sau một khoảng thời gian
                        this.forceUpdate();
                        setTimeout(() => {
                            const retryMessageEl = this.messagesContainer.el?.querySelector(`[data-message-id="${message.id}"]`);
                            if (retryMessageEl) {
                                console.log(`Found element for message ${message.id} after retry`);
                                retryMessageEl.innerHTML = htmlContent;
                            }
                        }, 50);
                    }
                }
            } catch (error) {
                console.error(`Error rendering markdown for message ${message.id}:`, error);
                // Fallback to simple rendering
                messageEl.innerHTML = `<p>${message.content || ""}</p>`;
            }
        } else {
            console.warn(`Element with data-message-id=${message.id} not found`);

            // Buộc cập nhật DOM và thử lại sau một khoảng thời gian
            this.forceUpdate();
            setTimeout(() => {
                const retryMessageEl = this.messagesContainer.el?.querySelector(`[data-message-id="${message.id}"]`);
                if (retryMessageEl) {
                    console.log(`Found element for message ${message.id} after DOM update`);
                    if (message.content && message.content.trim() !== "") {
                        renderMarkdownAsync(message.content)
                            .then(htmlContent => {
                                retryMessageEl.innerHTML = htmlContent;
                            })
                            .catch(error => {
                                console.error(`Error rendering markdown after retry:`, error);
                                retryMessageEl.innerHTML = `<p>${message.content || ""}</p>`;
                            });
                    } else {
                        console.warn(`Message ${message.id} has empty content after retry, using fallback`);
                        retryMessageEl.innerHTML = `<p>Không thể hiển thị nội dung tin nhắn.</p>`;
                    }
                }
            }, 50);
        }
    }
});

export const a2aChatWidget = {
    component: A2AChatWidgetComponent,
    supportedOptions: [],
    extractProps: ({ attrs, record }) => {
        // Xử lý giá trị agentCode
        let agentCode = attrs.agentCode;

        console.log("Initial agentCode from attrs:", agentCode);
        console.log("Record data available:", record?.data ? Object.keys(record.data) : "No record data");

        // Nếu agentCode là tên trường, lấy giá trị từ record
        if (record && record.data) {
            // Kiểm tra xem agentCode có phải là tên trường trong record.data không
            if (agentCode in record.data) {
                // Lấy giá trị từ trường tương ứng
                agentCode = record.data[agentCode];
                console.log(`Using agent code from record field ${attrs.agentCode}:`, agentCode);
            } else {
                // Trường hợp đặc biệt cho "code"
                if (agentCode === "code" && "code" in record.data) {
                    agentCode = record.data.code;
                    console.log(`Using agent code from record.data.code:`, agentCode);
                }
            }
        }

        // Kiểm tra nếu agentCode vẫn là "code" hoặc không hợp lệ
        if (agentCode === "code" || !agentCode) {
            // Thử lấy từ record.data.code nếu có
            if (record && record.data && record.data.code) {
                agentCode = record.data.code;
                console.log(`Fallback: Using agent code from record.data.code:`, agentCode);
            } else {
                // Mặc định sử dụng odoo_lms_agent nếu không lấy được
                agentCode = 'odoo_lms_agent';
                console.log("Using default agent code:", agentCode);
            }
        } else if (agentCode && agentCode.startsWith('context_get(')) {
            // Mặc định sử dụng odoo_lms_agent nếu không lấy được từ context
            agentCode = 'odoo_lms_agent';
            console.log("Using default agent code from context:", agentCode);
        }

        // Xử lý giá trị supportsStreaming
        let supportsStreaming = attrs.supportsStreaming;
        console.log("Initial supportsStreaming from attrs:", supportsStreaming);

        // Nếu supportsStreaming là tên trường, lấy giá trị từ record
        if (record && record.data) {
            // Kiểm tra trực tiếp trường supports_streaming trong record.data
            if ("supports_streaming" in record.data) {
                // Lấy giá trị trực tiếp từ record.data và in ra để debug
                const rawValue = record.data.supports_streaming;
                console.log("Raw value from record.data.supports_streaming:", rawValue, "Type:", typeof rawValue);
                supportsStreaming = rawValue;
            }
            // Nếu supportsStreaming là tên trường trong record.data
            else if (supportsStreaming in record.data) {
                const rawValue = record.data[supportsStreaming];
                console.log(`Raw value from record.data[${supportsStreaming}]:`, rawValue, "Type:", typeof rawValue);
                supportsStreaming = rawValue;
            }
        }

        // Chuyển đổi giá trị supportsStreaming thành boolean - cách mới
        // Trong Odoo, trường Boolean có thể trả về nhiều dạng khác nhau
        // Sử dụng cách kiểm tra chặt chẽ hơn
        const trueValues = [true, "true", 1, "1", "True", "TRUE"];

        // Kiểm tra xem giá trị có nằm trong danh sách các giá trị true không
        if (trueValues.includes(supportsStreaming)) {
            supportsStreaming = true;
            console.log("Converted supportsStreaming to true");
        } else {
            // Kiểm tra trường hợp đặc biệt: nếu là chuỗi "supports_streaming" và record.data.supports_streaming là true
            if (supportsStreaming === "supports_streaming" && record && record.data && record.data.supports_streaming === true) {
                supportsStreaming = true;
                console.log("Special case: Using true from record.data.supports_streaming");
            } else {
                supportsStreaming = false;
                console.log("Converted supportsStreaming to false");
            }
        }

        console.log("Final props:", {
            name: "a2a_chat",
            agentCode: agentCode,
            supportsStreaming: supportsStreaming,
        });

        return {
            name: "a2a_chat",
            agentCode: agentCode,
            supportsStreaming: supportsStreaming,
        };
    },
};

registry.category("view_widgets").add("a2a_chat", a2aChatWidget);
