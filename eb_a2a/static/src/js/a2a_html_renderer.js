/** @odoo-module **/

import { Component, xml } from "@odoo/owl";

/**
 * Component để render HTML an toàn
 * Sử dụng dangerouslySetInnerHTML để render HTML
 */
export class HtmlRenderer extends Component {
    static template = xml`
        <div t-ref="container" class="o_a2a_html_renderer"/>
    `;
    
    static props = {
        content: { type: String },
    };

    setup() {
        this.content = this.props.content || "";
    }

    mounted() {
        this._renderContent();
    }

    patched() {
        this._renderContent();
    }

    _renderContent() {
        if (this.content && this.refs.container) {
            this.refs.container.innerHTML = this.content;
        }
    }

    willUpdateProps(nextProps) {
        this.content = nextProps.content || "";
    }
}

export default HtmlRenderer;
