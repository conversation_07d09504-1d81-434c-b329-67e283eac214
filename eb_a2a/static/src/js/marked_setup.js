/** @odoo-module **/

/**
 * This file ensures that the marked library is properly loaded and available
 * It should be loaded before any other JS file that uses marked
 */

// Biến để theo dõi trạng thái tải thư viện
let markedLoaded = false;
let markedLoadPromise = null;

/**
 * Hàm tải thư viện marked.js
 * @returns {Promise} Promise sẽ resolve khi thư viện được tải xong
 */
function loadMarkedLibrary() {
    if (markedLoadPromise) {
        return markedLoadPromise;
    }

    markedLoadPromise = new Promise((resolve, reject) => {
        // Kiểm tra xem marked đã được định nghĩa chưa
        if (typeof window.marked === 'function') {
            console.log("marked library already loaded");
            markedLoaded = true;
            resolve(window.marked);
            return;
        }

        console.warn("marked library not found in window, trying to load it");

        // Tạo script element để tải thư viện
        const script = document.createElement('script');
        script.src = '/eb_a2a/static/lib/marked/marked.min.js';
        script.async = false; // Tải đồng bộ

        // <PERSON>ử lý khi tải thành công
        script.onload = function() {
            console.log("marked library loaded successfully");
            markedLoaded = true;
            resolve(window.marked);
        };

        // Xử lý khi tải thất bại
        script.onerror = function(e) {
            console.error("Failed to load marked library", e);
            reject(new Error("Failed to load marked library"));
        };

        // Thêm script vào document
        document.head.appendChild(script);
    });

    return markedLoadPromise;
}

// Tự động tải thư viện khi module được import
loadMarkedLibrary().catch(error => {
    console.error("Error initializing marked library:", error);
});

// Export markedLib và hàm loadMarkedLibrary
export const markedLib = window.marked;
export { loadMarkedLibrary };
