/** @odoo-module **/

import { Component } from "@odoo/owl";

/**
 * Widget để hiển thị nội dung HTML an toàn
 * Sử dụng DOM API để render HTML thay vì sử dụng t-raw hoặc t-out
 */
export class HtmlContent extends Component {
    static template = "eb_a2a.HtmlContent";
    static props = {
        content: { type: String },
    };

    setup() {
        this.htmlContent = this.props.content || "";
    }

    mounted() {
        if (this.htmlContent && this.el) {
            // Sử dụng innerHTML để render HTML
            this.el.innerHTML = this.htmlContent;
        }
    }

    willUpdateProps(nextProps) {
        this.htmlContent = nextProps.content || "";
        if (this.htmlContent && this.el) {
            // Cập nhật nội dung khi props thay đổi
            this.el.innerHTML = this.htmlContent;
        }
    }
}
