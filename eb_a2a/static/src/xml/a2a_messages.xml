<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="eb_a2a.A2AMessages" owl="1">
        <div class="o_a2a_messages">
            <div t-if="state.loading" class="o_a2a_messages_loading">
                <div class="o_a2a_messages_loading_spinner">
                    <i class="fa fa-spinner fa-spin fa-2x" title="Loading"></i>
                </div>
                <p>Đang tải tin nhắn...</p>
            </div>
            <div t-else="" class="o_a2a_message_list">
                <div t-if="state.messages.length === 0" class="o_a2a_message_empty">
                    <div class="o_a2a_message_empty_icon">
                        <i class="fa fa-comments-o fa-3x" title="No Messages"></i>
                    </div>
                    <p>Chưa c<PERSON> tin nhắn nào cho task này.</p>
                </div>
                <div t-else="" class="o_a2a_message_items">
                    <div t-foreach="state.messages" t-as="message" t-key="message.id" class="o_a2a_message_item">
                        <div class="o_a2a_message_item_header">
                            <div class="o_a2a_message_item_author">
                                <div class="o_a2a_message_item_avatar" t-attf-style="background-color: {{getAvatarColor(message.a2a_role || 'system')}};">
                                    <t t-if="message.a2a_role === 'user'">
                                        <i class="fa fa-user" title="User"></i>
                                    </t>
                                    <t t-elif="message.a2a_role === 'agent'">
                                        <i class="fa fa-robot" title="Agent"></i>
                                    </t>
                                    <t t-else="">
                                        <i class="fa fa-info-circle" title="System"></i>
                                    </t>
                                </div>
                                <div class="o_a2a_message_item_info">
                                    <div class="o_a2a_message_item_name">
                                        <span t-esc="message.author_name"/>
                                    </div>
                                    <div class="o_a2a_message_item_role">
                                        <span class="badge" t-attf-class="{{message.role_class}}" t-esc="message.role_label"/>
                                    </div>
                                </div>
                            </div>
                            <div class="o_a2a_message_item_time">
                                <span t-esc="formatMessageTime(message.create_date)"/>
                            </div>
                        </div>
                        <div class="o_a2a_message_item_body">
                            <div class="o_a2a_message_content" t-raw="message.html_content"/>
                        </div>
                        <div class="o_a2a_message_item_footer">
                            <div class="o_a2a_message_item_actions">
                                <button class="btn btn-link btn-sm o_a2a_message_action" t-on-click="() => copyMessageContent(message)" title="Sao chép">
                                    <i class="fa fa-copy" title="Copy"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </t>
</templates>
