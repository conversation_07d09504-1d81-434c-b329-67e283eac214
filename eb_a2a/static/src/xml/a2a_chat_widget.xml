<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="eb_a2a.ChatWidget" owl="1">
        <div class="o_a2a_chat_widget">
            <div class="o_a2a_chat_header">
                <div class="o_a2a_chat_header_left">
                    <i class="fa fa-robot me-2" title="AI Assistant"></i>
                    <h4>AI Assistant</h4>
                </div>
                <div class="o_a2a_chat_header_right">
                    <button class="btn btn-link o_a2a_chat_header_button" t-on-click="clearChat" title="Xóa cuộc trò chuyện">
                        <i class="fa fa-trash" title="Clear Chat"></i>
                    </button>
                </div>
            </div>
            <div class="o_a2a_chat_messages" t-ref="messagesContainer">
                <t t-foreach="state.messages" t-as="message" t-key="message.id">
                    <div t-if="message.isThinking" class="o_a2a_chat_thinking">
                        <div class="o_a2a_chat_thinking_content">
                            <i class="fa fa-robot" title="AI Thinking"></i>
                            <span class="o_a2a_chat_thinking_text" t-esc="message.content"></span>
                            <div class="o_a2a_chat_thinking_dots">
                                <div class="o_a2a_chat_thinking_dot"></div>
                                <div class="o_a2a_chat_thinking_dot"></div>
                                <div class="o_a2a_chat_thinking_dot"></div>
                            </div>
                        </div>
                    </div>
                    <div t-else="" t-attf-class="o_a2a_chat_message o_a2a_chat_message_{{message.role}}">
                        <div class="o_a2a_chat_message_avatar">
                            <t t-if="message.role === 'user'">
                                <div class="o_a2a_chat_avatar o_a2a_chat_avatar_user">
                                    <i class="fa fa-user" title="User"></i>
                                </div>
                            </t>
                            <t t-elif="message.role === 'agent'">
                                <div class="o_a2a_chat_avatar o_a2a_chat_avatar_agent">
                                    <i class="fa fa-robot" title="AI Assistant"></i>
                                </div>
                            </t>
                            <t t-else="">
                                <div class="o_a2a_chat_avatar o_a2a_chat_avatar_system">
                                    <i class="fa fa-info-circle" title="System"></i>
                                </div>
                            </t>
                        </div>
                        <div class="o_a2a_chat_message_bubble">
                            <!-- Sử dụng div với class để render HTML -->
                            <div t-if="message.isMarkdown" class="o_a2a_chat_message_content" t-att-data-message-id="message.id" />
                            <div class="o_a2a_chat_message_content" t-else="" t-esc="message.content" />
                            <div class="o_a2a_chat_message_actions">
                                <button class="btn btn-link btn-sm o_a2a_chat_message_action" t-on-click="() => copyMessageContent(message.content)" title="Sao chép">
                                    <i class="fa fa-copy" title="Copy"></i>
                                </button>
                            </div>
                            <div class="o_a2a_chat_message_time">
                                <small t-esc="formatTime(message.timestamp)" />
                            </div>
                        </div>
                    </div>
                </t>
                <!-- Hiển thị biểu tượng loading khi đang tải và không có tin nhắn thinking -->
                <div t-if="state.isLoading and !state.isStreaming and !state.messages.some(msg => msg.isThinking)" class="o_a2a_chat_loading">
                    <div class="o_a2a_chat_typing">
                        <div class="o_a2a_chat_typing_dot"></div>
                        <div class="o_a2a_chat_typing_dot"></div>
                        <div class="o_a2a_chat_typing_dot"></div>
                    </div>
                </div>
                <div t-if="state.error" class="o_a2a_chat_error alert alert-danger">
                    <i class="fa fa-exclamation-triangle me-2" title="Error"></i>
                    <span t-esc="state.error" />
                </div>
            </div>
            <div class="o_a2a_chat_input">
                <div class="o_a2a_chat_input_container">
                    <textarea
                        class="form-control"
                        t-model="state.inputMessage"
                        placeholder="Nhập tin nhắn..."
                        t-on-keydown="onInputKeydown"
                        t-att-disabled="state.isLoading"
                        rows="1"
                        t-ref="inputTextarea"
                    ></textarea>
                    <div class="o_a2a_chat_input_actions">
                        <button
                            class="btn btn-link o_a2a_chat_input_action"
                            t-on-click="toggleAttachmentMenu"
                            t-att-disabled="state.isLoading"
                            title="Đính kèm file"
                        >
                            <i class="fa fa-paperclip" title="Attach"></i>
                        </button>
                        <button
                            class="btn btn-primary o_a2a_chat_input_send"
                            t-on-click="() => {
                                console.log('Send button clicked');
                                const msg = state.inputMessage;
                                console.log('Message to send:', msg);
                                state.inputMessage = '';
                                sendMessageWithText(msg);
                            }"
                            t-att-disabled="!state.inputMessage.trim() || state.isLoading"
                            t-att-data-sending="state.isLoading"
                        >
                            <i class="fa fa-paper-plane" title="Send"></i>
                        </button>
                    </div>
                </div>
                <div t-if="state.showAttachmentMenu" class="o_a2a_chat_attachment_menu">
                    <div class="o_a2a_chat_attachment_menu_item" t-on-click="() => uploadFile('image')">
                        <i class="fa fa-image me-2" title="Image"></i> Hình ảnh
                    </div>
                    <div class="o_a2a_chat_attachment_menu_item" t-on-click="() => uploadFile('file')">
                        <i class="fa fa-file me-2" title="File"></i> Tệp tin
                    </div>
                    <input type="file" t-ref="fileInput" class="d-none" t-on-change="handleFileUpload" />
                </div>
            </div>
        </div>
    </t>
</templates>
