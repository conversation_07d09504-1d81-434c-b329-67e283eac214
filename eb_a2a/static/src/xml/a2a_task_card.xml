<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="eb_a2a.TaskCard" owl="1">
        <div class="o_a2a_task_card" t-att-data-task-id="task.id">
            <div class="o_a2a_task_card_header">
                <div class="o_a2a_task_card_status">
                    <span t-attf-class="o_a2a_task_card_status_badge badge {{getStatusClass(task.state)}}">
                        <t t-esc="getStatusLabel(task.state)" />
                    </span>
                </div>
                <div class="o_a2a_task_card_date">
                    <small t-esc="formatDate(task.create_date)" />
                </div>
            </div>
            <div class="o_a2a_task_card_body">
                <div class="o_a2a_task_card_title">
                    <h5 t-esc="task.name" />
                </div>
                <div class="o_a2a_task_card_info">
                    <div class="o_a2a_task_card_info_item">
                        <i class="fa fa-robot me-1" title="Agent"></i>
                        <span t-esc="task.agent_id[1]" />
                    </div>
                    <div class="o_a2a_task_card_info_item">
                        <i class="fa fa-user me-1" title="User"></i>
                        <span t-esc="task.user_id[1]" />
                    </div>
                </div>
                <div t-if="task.error_message" class="o_a2a_task_card_error">
                    <i class="fa fa-exclamation-triangle me-1" title="Error"></i>
                    <span t-esc="truncateText(task.error_message, 100)" />
                </div>
            </div>
            <div class="o_a2a_task_card_footer">
                <button class="btn btn-sm btn-primary" t-on-click="() => openTask(task.id)">
                    <i class="fa fa-eye me-1" title="View"></i> Xem
                </button>
                <button t-if="task.state === 'failed' || task.state === 'canceled'" class="btn btn-sm btn-secondary" t-on-click="() => retryTask(task.id)">
                    <i class="fa fa-redo me-1" title="Retry"></i> Thử lại
                </button>
                <button t-if="task.state === 'submitted' || task.state === 'working' || task.state === 'input_required'" class="btn btn-sm btn-danger" t-on-click="() => cancelTask(task.id)">
                    <i class="fa fa-times me-1" title="Cancel"></i> Hủy
                </button>
            </div>
        </div>
    </t>
</templates>
