# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging
from odoo import api, fields, models, _

_logger = logging.getLogger(__name__)


class A2ACrewRegistry(models.Model):
    """Model đăng ký các crew từ các module kh<PERSON>c nhau."""

    _name = "eb_a2a.crew.registry"
    _description = "A2A Crew Registry"
    _order = "name"

    name = fields.Char(
        string="Tên",
        required=True,
        help="Tên hiển thị của crew"
    )

    code = fields.Char(
        string="Mã",
        required=True,
        help="Mã định danh duy nhất của crew"
    )

    module_path = fields.Char(
        string="Đường dẫn Module",
        required=True,
        help="Đường dẫn đến module chứa hàm tạo crew (ví dụ: odoo.addons.eb_a2a_lms.crews.lms_crew)"
    )

    function_name = fields.Char(
        string="Tên <PERSON>à<PERSON>",
        required=True,
        help="Tên hàm tạo crew (ví dụ: create_lms_crew)"
    )

    description = fields.Text(
        string="Mô tả",
        help="Mô tả chi tiết về crew và khả năng của nó"
    )

    active = fields.Boolean(
        string="Đang hoạt động",
        default=True
    )

    process_type = fields.Selection([
        ('sequential', 'Sequential'),
        ('hierarchical', 'Hierarchical')
    ], string="Loại quy trình",
       default='sequential',
       required=True,
       help="Loại quy trình mà crew sử dụng: Sequential (tuần tự) hoặc Hierarchical (phân cấp)")

    requires_manager = fields.Boolean(
        string="Yêu cầu Manager",
        compute='_compute_requires_manager',
        store=True,
        help="Đánh dấu nếu crew yêu cầu manager (khi sử dụng quy trình phân cấp)"
    )

    manager_agent_module_path = fields.Char(
        string="Đường dẫn Module Manager Agent",
        help="Đường dẫn đến module chứa hàm tạo manager agent (ví dụ: odoo.addons.eb_a2a_lms.agents.manager_agent)"
    )

    manager_agent_function_name = fields.Char(
        string="Tên Hàm Manager Agent",
        help="Tên hàm tạo manager agent (ví dụ: create_manager_agent)"
    )

    _sql_constraints = [
        ('code_uniq', 'unique(code)', 'Mã Crew phải là duy nhất!')
    ]

    @api.depends('process_type')
    def _compute_requires_manager(self):
        """Tính toán xem crew có yêu cầu manager hay không dựa trên loại quy trình."""
        for record in self:
            record.requires_manager = record.process_type == 'hierarchical'

    @api.onchange('process_type')
    def _onchange_process_type(self):
        """Xử lý khi thay đổi loại quy trình."""
        if self.process_type != 'hierarchical':
            self.manager_agent_module_path = False
            self.manager_agent_function_name = False
