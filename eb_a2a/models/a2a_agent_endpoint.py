# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and <PERSON>tis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import json
import logging
from odoo import api, fields, models, _
from odoo.exceptions import ValidationError

_logger = logging.getLogger(__name__)


class A2AAgentEndpoint(models.Model):
    """Model đại diện cho một A2A Agent Endpoint."""

    _name = "eb_a2a.agent_endpoint"
    _description = "A2A Agent Endpoint"
    _inherit = ["mail.thread", "mail.activity.mixin"]
    _order = "name"

    name = fields.Char(
        string="Tên Endpoint",
        required=True,
        tracking=True,
        help="Tên hiển thị của agent endpoint"
    )

    code = fields.Char(
        string="Mã Endpoint",
        required=True,
        tracking=True,
        help="Mã định danh duy nhất của agent endpoint"
    )

    description = fields.Text(
        string="<PERSON>ô tả",
        help="<PERSON>ô tả chi tiết về agent endpoint và khả năng của nó"
    )

    # Phân loại triển khai
    deployment_type = fields.Selection([
        ('internal', 'Nội bộ'),
        ('external', 'Bên ngoài'),
        ('multi', 'Multi-Agent'),
    ], string="Loại Triển khai", default='internal', required=True,
        help="Loại triển khai: Nội bộ (xử lý trong Odoo), Bên ngoài (gọi API bên ngoài), hoặc Multi-Agent (sử dụng nhiều agent)")

    # Thông tin cho internal agent
    agent_implementation = fields.Selection([
        ('default', 'Default Agent'),
        ('course', 'Course Agent'),
        ('instructor', 'Instructor Agent'),
        ('class', 'Class Agent'),
        ('lms', 'LMS Agent'),
    ], string="Agent Implementation", default='default',
        help="Chọn agent cụ thể cần thực thi đối với internal agent")

    # Thông tin cho external agent
    endpoint_url = fields.Char(
        string="Endpoint URL",
        required=False,
        help="URL endpoint của agent bên ngoài để nhận các yêu cầu A2A. Chỉ cần thiết cho agent bên ngoài."
    )

    # Thông tin cho multi-agent
    crew_registry_id = fields.Many2one(
        'eb_a2a.crew.registry',
        string="Crew Registry",
        help="Crew registry được sử dụng cho agent này (chỉ cần thiết cho agent loại Multi-Agent)"
    )

    active = fields.Boolean(default=True, string="Đang hoạt động")

    # Khả năng
    supports_streaming = fields.Boolean(
        string="Hỗ trợ streaming",
        default=False,
        help="Agent có hỗ trợ streaming hay không"
    )

    supports_push_notifications = fields.Boolean(
        string="Hỗ trợ thông báo đẩy",
        default=False,
        help="Agent có hỗ trợ thông báo đẩy hay không"
    )

    # Xác thực
    auth_required = fields.Boolean(
        string="Yêu cầu xác thực",
        default=False,
        help="Agent có yêu cầu xác thực hay không"
    )

    auth_type = fields.Selection([
        ('none', 'Không'),
        ('basic', 'Basic'),
        ('bearer', 'Bearer'),
        ('api_key', 'API Key'),
        ('oauth2', 'OAuth2'),
    ], string="Loại xác thực", default='none', help="Loại xác thực sử dụng cho agent")

    auth_config = fields.Text(
        string="Cấu hình xác thực",
        help="Cấu hình xác thực dạng JSON",
        default="{}"
    )

    # Thông tin Agent Card
    agent_card = fields.Text(
        string="Agent Card",
        help="Thông tin Agent Card dạng JSON"
    )

    # Các task liên quan
    task_ids = fields.One2many(
        'eb_a2a.task',
        'agent_endpoint_id',
        string="Tasks"
    )

    # Cấu hình AI
    provider_id = fields.Many2one(
        'eb_a2a.provider',
        string="Provider AI",
        help="Provider AI cho agent"
    )

    ai_adapter_type = fields.Selection([
        ('openai', 'OpenAI'),
        ('azure', 'Azure OpenAI'),
    ], string="Loại Adapter AI", default='openai', help="Loại adapter AI sử dụng cho agent")

    framework = fields.Selection([
        ('crewai', 'CrewAI'),
        ('openmanus', 'OpenManus'),
    ], string="Framework", default='crewai', required=True, tracking=True,
        help="Framework AI được sử dụng cho agent này")

    system_message = fields.Text(
        string="System Message",
        help="Thông điệp hệ thống cho AI",
        default="Bạn là trợ lý AI của hệ thống Odoo. Hãy trả lời các câu hỏi của người dùng một cách chính xác, hữu ích và thân thiện."
    )

    partner_id = fields.Many2one(
        'res.partner',
        string="Partner",
        required=False,
        help="Partner liên kết với agent này (tùy chọn)"
    )

    # Các kỹ năng của agent
    skill_ids = fields.One2many(
        'eb_a2a.agent_endpoint.skill',
        'agent_endpoint_id',
        string="Kỹ năng",
        help="Các kỹ năng của agent"
    )

    _sql_constraints = [
        ('code_uniq', 'unique(code)', 'Mã Agent Endpoint phải là duy nhất!')
    ]

    @api.constrains('endpoint_url', 'deployment_type')
    def _check_endpoint_url(self):
        for record in self:
            # Kiểm tra endpoint_url bắt buộc cho agent bên ngoài
            if record.deployment_type == 'external' and not record.endpoint_url:
                raise ValidationError(_("Endpoint URL là bắt buộc đối với agent bên ngoài"))

            # Kiểm tra định dạng URL
            if record.endpoint_url and not (
                record.endpoint_url.startswith('http://') or
                record.endpoint_url.startswith('https://')
            ):
                raise ValidationError(_("Endpoint URL phải bắt đầu bằng http:// hoặc https://"))

    @api.onchange('deployment_type')
    def _onchange_deployment_type(self):
        """Xử lý khi thay đổi loại triển khai."""
        if self.deployment_type == 'external':
            self.agent_implementation = False
            self.crew_registry_id = False
        elif self.deployment_type == 'multi':
            self.agent_implementation = False
            self.endpoint_url = False
        else:  # internal
            self.crew_registry_id = False
            self.endpoint_url = False

    # Không tự động tạo partner khi tạo agent endpoint
    # Người dùng sẽ tự tạo partner khi cần thiết

    def generate_agent_card(self):
        """Tạo Agent Card theo định dạng A2A."""
        self.ensure_one()

        # Xác định endpoint URL
        endpoint = self.endpoint_url
        if not endpoint and self.deployment_type == 'internal':
            # Sử dụng URL mặc định cho agent nội bộ
            base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
            endpoint = f"{base_url}/api/a2a/tasks/send"

        # Tạo Agent Card
        agent_card = {
            "name": self.name,
            "description": self.description,
            "version": "1.0",
            "endpoint": endpoint,
            "capabilities": {
                "streaming": self.supports_streaming,
                "pushNotifications": self.supports_push_notifications
            },
            "authentication": {
                "required": self.auth_required,
                "type": self.auth_type,
                "config": json.loads(self.auth_config or "{}")
            }
        }

        self.agent_card = json.dumps(agent_card, indent=2)
        return self.agent_card

    def action_test_connection(self):
        """Kiểm tra kết nối với agent."""
        self.ensure_one()

        try:
            if self.deployment_type == 'external':
                if not self.endpoint_url:
                    raise ValidationError(_("Endpoint URL là bắt buộc đối với agent bên ngoài"))

                # Thực hiện kiểm tra kết nối với endpoint bên ngoài
                # TODO: Triển khai logic kiểm tra kết nối thực tế

                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Thành công'),
                        'message': _('Kết nối thành công với %s') % self.endpoint_url,
                        'sticky': False,
                        'type': 'success',
                    }
                }
            else:
                # Đối với agent nội bộ, kiểm tra provider
                if not self.provider_id:
                    raise ValidationError(_("Provider AI là bắt buộc đối với agent nội bộ"))

                # TODO: Triển khai logic kiểm tra kết nối với provider

                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Thành công'),
                        'message': _('Kết nối thành công với provider %s') % self.provider_id.name,
                        'sticky': False,
                        'type': 'success',
                    }
                }
        except Exception as e:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Lỗi'),
                    'message': str(e),
                    'sticky': False,
                    'type': 'danger',
                }
            }
