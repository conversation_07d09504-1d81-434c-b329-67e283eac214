# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and <PERSON>tis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

from odoo import api, fields, models
import json


class MailMessage(models.Model):
    """Mở rộng mail.message để lưu trữ thông tin A2A."""

    _inherit = "mail.message"

    a2a_role = fields.Selection([
        ('user', 'Người dùng'),
        ('agent', 'Agent'),
        ('system', 'Hệ thống'),
    ], string="A2A Role", help="Vai trò trong hệ thống A2A")

    a2a_parts = fields.Text(
        string="A2A Parts",
        help="Các phần của message theo định dạng A2A"
    )

    plain_content = fields.Text(
        string="Plain Content",
        compute="_compute_plain_content",
        store=False,
        help="Nội dung tin nhắn dưới dạng văn bản thuần túy"
    )

    @api.depends('body')
    def _compute_plain_content(self):
        """Tính toán nội dung văn bản thuần túy từ HTML."""
        for record in self:
            # Loại bỏ các thẻ HTML
            content = record.body or ''
            # Loại bỏ các thẻ HTML cơ bản
            content = content.replace('<p>', '').replace('</p>', '\n')
            content = content.replace('<br>', '\n').replace('<br/>', '\n').replace('<br />', '\n')
            content = content.replace('&nbsp;', ' ')
            # Giới hạn độ dài
            if len(content) > 200:
                content = content[:197] + '...'
            record.plain_content = content



    def get_a2a_parts(self):
        """Lấy các phần của message theo định dạng A2A."""
        self.ensure_one()
        if self.a2a_parts:
            return json.loads(self.a2a_parts)

        # Nếu không có a2a_parts, tạo TextPart mặc định
        return [{
            "type": "text",
            "text": self.body
        }]
