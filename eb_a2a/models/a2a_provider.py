# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import logging
from odoo import api, fields, models, _
from odoo.exceptions import ValidationError

_logger = logging.getLogger(__name__)


class A2AProvider(models.Model):
    """Model đại diện cho một nhà cung cấp AI và cấu hình A2A."""

    _name = "eb_a2a.provider"
    _description = "A2A Provider & Configuration"
    _inherit = ["mail.thread", "mail.activity.mixin"]
    _order = "name"

    name = fields.Char(
        string="Tên Provider",
        required=True,
        tracking=True,
        help="Tên nhà cung cấp AI"
    )

    code = fields.Char(
        string="Mã Provider",
        required=True,
        tracking=True,
        help="Mã định danh duy nhất của nhà cung cấp"
    )

    description = fields.Text(
        string="Mô tả",
        help="Mô tả chi tiết về nhà cung cấp"
    )

    api_base = fields.Char(
        string="API Base URL",
        required=True,
        help="URL cơ sở của API (ví dụ: https://api.openai.com/v1)"
    )

    api_type = fields.Selection([
        ('openai', 'OpenAI Compatible'),
        ('azure', 'Azure OpenAI'),
        ('google', 'Google Gemini'),
    ], string="Loại API", default='openai', required=True)

    auth_type = fields.Selection([
        ('api_key', 'API Key'),
        ('none', 'No Authentication'),
    ], string="Loại xác thực", default='api_key', required=True)

    api_key = fields.Char(
        string="API Key",
        help="API Key để xác thực với nhà cung cấp"
    )

    api_version = fields.Char(
        string="API Version",
        help="Phiên bản API (nếu cần)"
    )

    organization_id = fields.Char(
        string="Organization ID",
        help="ID tổ chức (nếu cần)"
    )

    # Cấu hình bổ sung cho Azure OpenAI
    api_version = fields.Char(
        string="API Version",
        default="2023-05-15",
        help="Phiên bản API của Azure OpenAI"
    )

    active = fields.Boolean(default=True, string="Đang hoạt động")

    sequence = fields.Integer(
        string="Thứ tự",
        default=10,
        help="Thứ tự hiển thị"
    )

    model_ids = fields.One2many(
        'eb_a2a.provider.model',
        'provider_id',
        string="Mô hình"
    )

    # Các trường từ eb_a2a.config
    is_default = fields.Boolean(
        string="Mặc định",
        default=False,
        help="Cấu hình mặc định sẽ được sử dụng khi không có cấu hình cụ thể cho agent"
    )

    # Cấu hình chung
    system_message = fields.Text(
        string="System Message",
        help="Thông điệp hệ thống mặc định cho AI",
        default="Bạn là trợ lý AI của hệ thống quản lý học tập. Hãy trả lời các câu hỏi về khóa học, lịch học, và các thông tin liên quan đến việc học tập."
    )

    max_history_length = fields.Integer(
        string="Độ dài lịch sử tối đa",
        default=10,
        help="Số lượng message tối đa trong lịch sử hội thoại"
    )

    # Các agent endpoint sử dụng provider này
    agent_endpoint_ids = fields.One2many(
        'eb_a2a.agent_endpoint',
        'provider_id',
        string="Agent Endpoints"
    )

    _sql_constraints = [
        ('code_uniq', 'unique(code)', 'Mã Provider phải là duy nhất!')
    ]

    @api.constrains('is_default')
    def _check_default(self):
        """Đảm bảo chỉ có một cấu hình mặc định."""
        for record in self:
            if record.is_default:
                default_configs = self.search([
                    ('is_default', '=', True),
                    ('id', '!=', record.id)
                ])
                if default_configs:
                    default_configs.write({'is_default': False})

    @api.constrains('api_base')
    def _check_api_base(self):
        for record in self:
            if record.api_base and not (
                record.api_base.startswith('http://') or
                record.api_base.startswith('https://')
            ):
                raise ValidationError(_("API Base URL phải bắt đầu bằng http:// hoặc https://"))

    def action_test_connection(self):
        """Kiểm tra kết nối đến nhà cung cấp."""
        self.ensure_one()

        try:
            # Kiểm tra các trường bắt buộc
            if not self.api_base:
                raise ValidationError(_('API Base URL không được để trống'))

            if self.auth_type != 'none' and not self.api_key:
                raise ValidationError(_('API Key không được để trống với loại xác thực này'))

            if self.api_type == 'azure' and not self.api_version:
                raise ValidationError(_('API Version không được để trống với Azure OpenAI'))



            # Kiểm tra kết nối dựa trên loại API
            if self.api_type == 'openai':
                self._test_openai_connection()
            elif self.api_type == 'azure':
                self._test_azure_connection()
            elif self.api_type == 'google':
                self._test_google_connection()
            else:
                # Loại API không được hỗ trợ
                raise ValidationError(_('Loại API %s chưa được hỗ trợ kiểm tra kết nối') % self.api_type)

            # Nếu không có lỗi, hiển thị thông báo thành công
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Kết nối thành công'),
                    'message': _('Kết nối đến %s thành công') % self.name,
                    'sticky': False,
                    'type': 'success',
                }
            }
        except Exception as e:
            # Hiển thị thông báo lỗi
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Lỗi kết nối'),
                    'message': str(e),
                    'sticky': True,
                    'type': 'danger',
                }
            }

    def action_fetch_models(self):
        """Lấy danh sách models từ nhà cung cấp."""
        self.ensure_one()

        try:
            # Kiểm tra các trường bắt buộc (tương tự như action_test_connection)
            if not self.api_base:
                raise ValidationError(_('API Base URL không được để trống'))

            if self.auth_type != 'none' and not self.api_key:
                raise ValidationError(_('API Key không được để trống với loại xác thực này'))

            if self.api_type == 'azure' and not self.api_version:
                raise ValidationError(_('API Version không được để trống với Azure OpenAI'))



            # Lấy danh sách models dựa trên loại API
            models = []
            if self.api_type == 'openai':
                models = self._fetch_openai_models()
            elif self.api_type == 'azure':
                models = self._fetch_azure_models()
            elif self.api_type == 'google':
                models = self._fetch_google_models()
            else:
                # Loại API không được hỗ trợ
                raise ValidationError(_('Loại API %s chưa được hỗ trợ lấy danh sách models') % self.api_type)

            # Tạo các bản ghi model
            if not models:
                raise ValidationError(_('Không tìm thấy models nào'))

            # Đếm số models đã tạo
            created_count = 0
            updated_count = 0
            skipped_count = 0

            for model_data in models:
                # Kiểm tra xem model đã tồn tại chưa
                existing_model = self.env['eb_a2a.provider.model'].search([
                    ('provider_id', '=', self.id),
                    ('model_id', '=', model_data['model_id']),
                ], limit=1)

                if existing_model:
                    # Cập nhật model hiện có
                    existing_model.write(model_data)
                    updated_count += 1
                else:
                    # Tạo model mới
                    model_data['provider_id'] = self.id
                    self.env['eb_a2a.provider.model'].create(model_data)
                    created_count += 1

            # Hiển thị thông báo thành công
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Lấy danh sách models thành công'),
                    'message': _('Đã tạo %s models mới, cập nhật %s models, bỏ qua %s models') % (created_count, updated_count, skipped_count),
                    'sticky': False,
                    'type': 'success',
                }
            }
        except Exception as e:
            # Hiển thị thông báo lỗi
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Lỗi khi lấy danh sách models'),
                    'message': str(e),
                    'sticky': True,
                    'type': 'danger',
                }
            }


    def _test_openai_connection(self):
        """Kiểm tra kết nối đến OpenAI hoặc API tương thích."""
        try:
            import requests
            import json

            # Tạo headers
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            # Thêm Organization ID nếu có và là chuỗi
            if self.organization_id and isinstance(self.organization_id, str) and self.organization_id.strip():
                headers["OpenAI-Organization"] = self.organization_id

            # Gọi API models để kiểm tra kết nối
            url = f"{self.api_base}/models"
            response = requests.get(url, headers=headers, timeout=10)

            # Kiểm tra response
            if response.status_code != 200:
                error_msg = response.text
                try:
                    error_data = json.loads(response.text)
                    if 'error' in error_data and 'message' in error_data['error']:
                        error_msg = error_data['error']['message']
                except:
                    pass
                raise ValidationError(_("Lỗi kết nối đến OpenAI: %s") % error_msg)
        except ImportError:
            raise ValidationError(_("Thư viện 'requests' không được cài đặt. Hãy cài đặt bằng lệnh: pip install requests"))
        except Exception as e:
            raise ValidationError(_("Lỗi kết nối đến OpenAI: %s") % str(e))

    def _test_azure_connection(self):
        """Kiểm tra kết nối đến Azure OpenAI."""
        try:
            import requests
            import json

            # Tạo headers
            headers = {
                "Content-Type": "application/json",
                "api-key": self.api_key
            }

            # Gọi API deployments để kiểm tra kết nối
            url = f"{self.api_base}/openai/deployments?api-version={self.api_version}"
            response = requests.get(url, headers=headers, timeout=10)

            # Kiểm tra response
            if response.status_code != 200:
                error_msg = response.text
                try:
                    error_data = json.loads(response.text)
                    if 'error' in error_data and 'message' in error_data['error']:
                        error_msg = error_data['error']['message']
                except:
                    pass
                raise ValidationError(_("Lỗi kết nối đến Azure OpenAI: %s") % error_msg)
        except ImportError:
            raise ValidationError(_("Thư viện 'requests' không được cài đặt. Hãy cài đặt bằng lệnh: pip install requests"))
        except Exception as e:
            raise ValidationError(_("Lỗi kết nối đến Azure OpenAI: %s") % str(e))

    def _test_google_connection(self):
        """Kiểm tra kết nối đến Google Gemini."""
        try:
            # Thử import thư viện
            try:
                from google import genai
            except ImportError:
                raise ValidationError(_("Thư viện 'google-generativeai' không được cài đặt. Hãy cài đặt bằng lệnh: pip install google-generativeai"))

            # Cấu hình API key
            genai.configure(api_key=self.api_key)

            # Lấy danh sách models để kiểm tra kết nối
            models = genai.list_models()

            # Nếu không có lỗi, kết nối thành công
            if not models:
                raise ValidationError(_("Không tìm thấy models nào từ Google Gemini"))

        except Exception as e:
            raise ValidationError(_("Lỗi kết nối đến Google Gemini: %s") % str(e))






    def _fetch_openai_models(self):
        """Lấy danh sách models từ OpenAI hoặc API tương thích."""
        try:
            import requests
            import json

            # Tạo headers
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            # Thêm Organization ID nếu có và là chuỗi
            if self.organization_id and isinstance(self.organization_id, str) and self.organization_id.strip():
                headers["OpenAI-Organization"] = self.organization_id

            # Gọi API models để lấy danh sách
            url = f"{self.api_base}/models"
            response = requests.get(url, headers=headers, timeout=10)

            # Kiểm tra response
            if response.status_code != 200:
                error_msg = response.text
                try:
                    error_data = json.loads(response.text)
                    if 'error' in error_data and 'message' in error_data['error']:
                        error_msg = error_data['error']['message']
                except:
                    pass
                raise ValidationError(_("Lỗi khi lấy danh sách models từ OpenAI: %s") % error_msg)

            # Xử lý kết quả
            data = response.json()
            models = []

            for model_info in data.get('data', []):
                model_id = model_info.get('id')
                if not model_id:
                    continue

                # Phân loại model
                model_type = 'chat'
                if 'gpt' in model_id.lower():
                    model_type = 'chat'
                elif 'embedding' in model_id.lower() or 'text-embedding' in model_id.lower():
                    model_type = 'embedding'
                elif 'dall-e' in model_id.lower() or 'image' in model_id.lower():
                    model_type = 'image'
                elif 'whisper' in model_id.lower() or 'audio' in model_id.lower():
                    model_type = 'audio'
                elif 'text-davinci' in model_id.lower() or 'davinci' in model_id.lower():
                    model_type = 'completion'

                # Xác định các khả năng
                supports_functions = 'gpt-4' in model_id.lower() or 'gpt-3.5-turbo' in model_id.lower()
                supports_vision = 'vision' in model_id.lower() or 'gpt-4-vision' in model_id.lower()
                supports_streaming = 'gpt' in model_id.lower() or 'davinci' in model_id.lower()

                # Tạo dữ liệu model
                model_data = {
                    'name': model_id,
                    'model_id': model_id,
                    'model_type': model_type,
                    'supports_functions': supports_functions,
                    'supports_vision': supports_vision,
                    'supports_streaming': supports_streaming,
                }

                models.append(model_data)

            return models
        except ImportError:
            raise ValidationError(_("Thư viện 'requests' không được cài đặt. Hãy cài đặt bằng lệnh: pip install requests"))
        except Exception as e:
            raise ValidationError(_("Lỗi khi lấy danh sách models từ OpenAI: %s") % str(e))

    def _fetch_azure_models(self):
        """Lấy danh sách models từ Azure OpenAI."""
        try:
            import requests
            import json

            # Tạo headers
            headers = {
                "Content-Type": "application/json",
                "api-key": self.api_key
            }

            # Gọi API deployments để lấy danh sách
            url = f"{self.api_base}/openai/deployments?api-version={self.api_version}"
            response = requests.get(url, headers=headers, timeout=10)

            # Kiểm tra response
            if response.status_code != 200:
                error_msg = response.text
                try:
                    error_data = json.loads(response.text)
                    if 'error' in error_data and 'message' in error_data['error']:
                        error_msg = error_data['error']['message']
                except:
                    pass
                raise ValidationError(_("Lỗi khi lấy danh sách models từ Azure OpenAI: %s") % error_msg)

            # Xử lý kết quả
            data = response.json()
            models = []

            for model_info in data.get('value', []):
                model_id = model_info.get('id')
                model_name = model_info.get('name', model_id)
                if not model_id:
                    continue

                # Lấy thông tin về model
                model_type = 'chat'
                base_model = model_info.get('model', {}).get('name', '').lower()

                # Phân loại model
                if 'gpt' in base_model:
                    model_type = 'chat'
                elif 'embedding' in base_model or 'text-embedding' in base_model:
                    model_type = 'embedding'
                elif 'dall-e' in base_model or 'image' in base_model:
                    model_type = 'image'
                elif 'whisper' in base_model or 'audio' in base_model:
                    model_type = 'audio'
                elif 'text-davinci' in base_model or 'davinci' in base_model:
                    model_type = 'completion'

                # Xác định các khả năng
                supports_functions = 'gpt-4' in base_model or 'gpt-3.5-turbo' in base_model
                supports_vision = 'vision' in base_model or 'gpt-4-vision' in base_model
                supports_streaming = 'gpt' in base_model or 'davinci' in base_model

                # Tạo dữ liệu model
                model_data = {
                    'name': model_name,
                    'model_id': model_id,
                    'model_type': model_type,
                    'supports_functions': supports_functions,
                    'supports_vision': supports_vision,
                    'supports_streaming': supports_streaming,
                }

                models.append(model_data)

            return models
        except ImportError:
            raise ValidationError(_("Thư viện 'requests' không được cài đặt. Hãy cài đặt bằng lệnh: pip install requests"))
        except Exception as e:
            raise ValidationError(_("Lỗi khi lấy danh sách models từ Azure OpenAI: %s") % str(e))

    def _fetch_google_models(self):
        """Lấy danh sách models từ Google Gemini."""
        try:
            # Thử import thư viện
            try:
                from google import genai
            except ImportError:
                raise ValidationError(_("Thư viện 'google-generativeai' không được cài đặt. Hãy cài đặt bằng lệnh: pip install google-generativeai"))

            # Cấu hình API key
            genai.configure(api_key=self.api_key)

            # Lấy danh sách models
            model_list = genai.list_models()
            models = []

            # Danh sách các model embedding đã biết
            known_embedding_models = [
                'gemini-embedding-exp-03-07',
                'text-embedding-004',
                'embedding-001',
            ]

            # Xử lý kết quả
            for model_info in model_list:
                model_id = model_info.name.split('/')[-1]
                if not model_id:
                    continue

                # Phân loại model
                model_type = 'chat'

                # Xác định loại model dựa trên tên
                if any(embed_model in model_id for embed_model in known_embedding_models) or 'embedding' in model_id.lower():
                    model_type = 'embedding'
                elif 'image' in model_id.lower() or 'imagen' in model_id.lower():
                    model_type = 'image'
                elif 'audio' in model_id.lower():
                    model_type = 'audio'
                elif 'gemini' in model_id.lower():
                    model_type = 'chat'

                # Xác định các khả năng
                supports_functions = 'gemini' in model_id.lower()
                supports_vision = 'vision' in model_id.lower() or 'gemini-pro-vision' in model_id.lower()
                supports_streaming = 'gemini' in model_id.lower()

                # Tạo dữ liệu model
                model_data = {
                    'name': model_id,
                    'model_id': model_id,
                    'model_type': model_type,
                    'supports_functions': supports_functions,
                    'supports_vision': supports_vision,
                    'supports_streaming': supports_streaming,
                }

                models.append(model_data)

            # Nếu không tìm thấy models từ API, thêm các model embedding đã biết
            if not any(model['model_type'] == 'embedding' for model in models):
                for embed_model in known_embedding_models:
                    model_data = {
                        'name': embed_model,
                        'model_id': embed_model,
                        'model_type': 'embedding',
                        'supports_functions': False,
                        'supports_vision': False,
                        'supports_streaming': False,
                    }
                    models.append(model_data)

            return models
        except Exception as e:
            raise ValidationError(_("Lỗi khi lấy danh sách models từ Google Gemini: %s") % str(e))






    def get_ai_adapter_config(self):
        """Lấy cấu hình AI adapter dưới dạng dict."""
        self.ensure_one()

        config = {}

        # Cấu hình chung
        config['system_message'] = self.system_message
        config['max_history_length'] = self.max_history_length

        # Cấu hình theo loại API
        if self.api_type == 'openai':
            config.update({
                'api_key': self.api_key,
                'api_base': self.api_base,
                'organization_id': self.organization_id,
            })

            # Thêm model mặc định nếu có
            default_model = self.env['eb_a2a.provider.model'].search([
                ('provider_id', '=', self.id),
                ('is_default', '=', True),
                ('model_type', '=', 'chat')
            ], limit=1)

            if default_model:
                config['model'] = default_model.model_id

        elif self.api_type == 'azure':
            config.update({
                'api_key': self.api_key,
                'api_base': self.api_base,
                'api_type': self.api_type,
                'api_version': self.api_version,
                'organization_id': self.organization_id,
            })

            # Thêm model mặc định nếu có
            default_model = self.env['eb_a2a.provider.model'].search([
                ('provider_id', '=', self.id),
                ('is_default', '=', True),
            ], limit=1)

            if default_model:
                config['model'] = default_model.model_id

        return config

    @api.model
    def get_default_provider(self):
        """Lấy provider mặc định."""
        provider = self.search([('is_default', '=', True)], limit=1)
        if not provider:
            provider = self.search([], limit=1)
        return provider


class A2AProviderModel(models.Model):
    """Model đại diện cho một mô hình AI của nhà cung cấp."""

    _name = "eb_a2a.provider.model"
    _description = "A2A Provider Model"
    _order = "provider_id, sequence, name"

    name = fields.Char(
        string="Tên mô hình",
        required=True,
        help="Tên mô hình AI"
    )

    model_id = fields.Char(
        string="Model ID",
        required=True,
        help="ID của mô hình trong API của nhà cung cấp"
    )

    provider_id = fields.Many2one(
        'eb_a2a.provider',
        string="Nhà cung cấp",
        required=True,
        ondelete='cascade'
    )

    description = fields.Text(
        string="Mô tả",
        help="Mô tả chi tiết về mô hình"
    )

    sequence = fields.Integer(
        string="Thứ tự",
        default=10,
        help="Thứ tự hiển thị"
    )

    is_default = fields.Boolean(
        string="Mặc định",
        default=False,
        help="Đây là mô hình mặc định của nhà cung cấp"
    )

    model_type = fields.Selection([
        ('chat', 'Chat'),
        ('completion', 'Completion'),
        ('embedding', 'Embedding'),
        ('image', 'Image'),
        ('audio', 'Audio'),
        ('multimodal', 'Multimodal'),
    ], string="Loại mô hình", default='chat', required=True)

    max_tokens = fields.Integer(
        string="Max Tokens",
        help="Số lượng token tối đa mô hình có thể xử lý"
    )

    context_window = fields.Integer(
        string="Context Window",
        help="Kích thước cửa sổ ngữ cảnh (số token)"
    )

    supports_functions = fields.Boolean(
        string="Hỗ trợ Functions",
        default=False,
        help="Mô hình có hỗ trợ function calling không"
    )

    supports_vision = fields.Boolean(
        string="Hỗ trợ Vision",
        default=False,
        help="Mô hình có hỗ trợ xử lý hình ảnh không"
    )

    supports_streaming = fields.Boolean(
        string="Hỗ trợ Streaming",
        default=False,
        help="Mô hình có hỗ trợ streaming không"
    )

    active = fields.Boolean(default=True, string="Đang hoạt động")

    @api.constrains('is_default')
    def _check_default_model(self):
        for record in self:
            if record.is_default:
                # Kiểm tra xem có mô hình mặc định nào khác của cùng nhà cung cấp không
                other_defaults = self.search([
                    ('provider_id', '=', record.provider_id.id),
                    ('is_default', '=', True),
                    ('id', '!=', record.id),
                    ('model_type', '=', record.model_type),
                ])
                if other_defaults:
                    # Nếu có, đặt chúng thành không mặc định
                    other_defaults.write({'is_default': False})
