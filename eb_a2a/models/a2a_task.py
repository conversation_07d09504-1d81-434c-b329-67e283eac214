# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

import json
import logging
import uuid
from datetime import datetime
from odoo import api, fields, models, _

_logger = logging.getLogger(__name__)


class A2ATask(models.Model):
    """Model đại diện cho một A2A Task."""

    _name = "eb_a2a.task"
    _description = "A2A Task"
    _inherit = ["mail.thread", "mail.activity.mixin"]
    _order = "create_date desc"

    name = fields.Char(
        string="Tên Task",
        compute="_compute_name",
        store=True
    )

    task_id = fields.Char(
        string="Task ID",
        required=True,
        readonly=True,
        default=lambda self: str(uuid.uuid4()),
        help="ID duy nhất của task theo định dạng A2A"
    )

    agent_endpoint_id = fields.Many2one(
        'eb_a2a.agent_endpoint',
        string="Agent Endpoint",
        required=True,
        ondelete='restrict',
        tracking=True
    )

    user_id = fields.Many2one(
        'res.users',
        string="Người dùng",
        default=lambda self: self.env.user,
        help="Người dùng khởi tạo task"
    )

    state = fields.Selection([
        ('submitted', 'Đã gửi'),
        ('working', 'Đang xử lý'),
        ('input_required', 'Cần thêm thông tin'),
        ('completed', 'Hoàn thành'),
        ('failed', 'Thất bại'),
        ('canceled', 'Đã hủy'),
    ], string="Trạng thái", default='submitted', tracking=True)

    # Đã loại bỏ a2a_message_ids và chuyển sang sử dụng message_ids của mail.thread

    artifact_ids = fields.One2many(
        'eb_a2a.task.artifact',
        'task_id',
        string="Artifacts"
    )

    error_message = fields.Text(
        string="Thông báo lỗi",
        readonly=True,
        help="Thông báo lỗi nếu task thất bại"
    )

    is_streaming = fields.Boolean(
        string="Đang streaming",
        default=False,
        help="Task có đang trong chế độ streaming không"
    )

    webhook_url = fields.Char(
        string="Webhook URL",
        help="URL để nhận push notifications"
    )

    channel_id = fields.Many2one(
        'discuss.channel',
        string="Kênh thảo luận",
        help="Kênh thảo luận Odoo được sử dụng cho task này"
    )

    # Trường cho chatbot
    is_chatbot_session = fields.Boolean(
        string="Phiên Chatbot",
        default=False,
        help="Đánh dấu task này là phiên chatbot"
    )

    chatbot_metadata = fields.Text(
        string="Metadata Chatbot",
        help="Metadata cho phiên chatbot dạng JSON"
    )

    website_visitor_id = fields.Many2one(
        'website.visitor',
        string="Website Visitor",
        help="Visitor từ website"
    )

    @api.depends('task_id', 'agent_endpoint_id', 'create_date')
    def _compute_name(self):
        for task in self:
            if task.agent_endpoint_id and task.create_date:
                create_date = task.create_date.strftime('%Y-%m-%d %H:%M:%S')
                task.name = f"{task.agent_endpoint_id.name} - {create_date}"
            else:
                task.name = task.task_id

    @api.model_create_multi
    def create(self, vals_list):
        """Tạo task và kênh thảo luận tương ứng."""
        tasks = super().create(vals_list)

        for task in tasks:
            # Tạo kênh thảo luận cho task
            channel = self.env['discuss.channel'].create({
                'name': task.name,
                'channel_type': 'group',
                'description': f"Kênh thảo luận cho task {task.task_id}",
            })

            # Kiểm tra xem người dùng đã là thành viên của kênh chưa
            partner_id = task.user_id.partner_id.id
            existing_member = self.env['discuss.channel.member'].sudo().search([
                ('channel_id', '=', channel.id),
                ('partner_id', '=', partner_id)
            ], limit=1)

            # Chỉ thêm người dùng vào kênh nếu họ chưa là thành viên
            if not existing_member:
                channel.channel_member_ids = [(0, 0, {
                    'partner_id': partner_id,
                })]

            # Liên kết kênh với task
            task.channel_id = channel.id

            # Gửi tin nhắn chào mừng vào cả task và kênh
            welcome_message = f"Chào mừng đến với {task.agent_endpoint_id.name}! Tôi có thể giúp gì cho bạn?"
            author_id = task.agent_endpoint_id.partner_id.id if task.agent_endpoint_id.partner_id else None

            # Vô hiệu hóa chức năng gửi email
            context = {
                'mail_create_nosubscribe': True,
                'mail_auto_delete': False,
                'mail_notify_author': False,
                'mail_notify_user_signature': False,
                'mail_create_nolog': True,
                'tracking_disable': True,
                'mail_notrack': True,
            }

            # Gửi vào kênh
            channel_msg = channel.with_context(**context).message_post(
                body=welcome_message,
                message_type='comment',
                subtype_xmlid='mail.mt_comment',
                author_id=author_id,
                email_from='<EMAIL>',
            )

            # Lưu trữ thông tin role cho tin nhắn
            self.env.cr.execute(
                "UPDATE mail_message SET a2a_role = %s WHERE id = %s",
                ('agent', channel_msg.id)
            )

            # Gửi vào task
            task_msg = task.with_context(**context).message_post(
                body=welcome_message,
                message_type='comment',
                subtype_xmlid='mail.mt_comment',
                author_id=author_id,
                email_from='<EMAIL>',
            )

            # Lưu trữ thông tin role cho tin nhắn
            self.env.cr.execute(
                "UPDATE mail_message SET a2a_role = %s WHERE id = %s",
                ('agent', task_msg.id)
            )

        return tasks

    def action_cancel(self):
        """Hủy task."""
        self.ensure_one()
        if self.state in ['submitted', 'working', 'input_required']:
            self.state = 'canceled'
            # TODO: Gửi yêu cầu hủy đến agent
        return True

    def action_retry(self):
        """Thử lại task."""
        self.ensure_one()
        if self.state in ['failed', 'canceled']:
            self.state = 'submitted'
            # TODO: Gửi lại yêu cầu đến agent
        return True

    def action_view_task(self):
        """Mở form view của task."""
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'eb_a2a.task',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'current',
        }

    def get_chatbot_metadata(self):
        """Lấy metadata của phiên chatbot."""
        self.ensure_one()
        if self.chatbot_metadata:
            try:
                return json.loads(self.chatbot_metadata)
            except Exception:
                return {}
        return {}

    def update_chatbot_metadata(self, metadata):
        """Cập nhật metadata của phiên chatbot."""
        self.ensure_one()
        current_metadata = self.get_chatbot_metadata()
        current_metadata.update(metadata)
        self.chatbot_metadata = json.dumps(current_metadata)

    def send_message(self, content, role='user', parts=None):
        """Gửi message mới đến task."""
        self.ensure_one()

        # Xác định author_id dựa trên role
        author_id = False
        if role == 'user':
            author_id = self.user_id.partner_id.id
        elif role == 'agent':
            author_id = self.agent_endpoint_id.partner_id.id if self.agent_endpoint_id.partner_id else False

        # Xác định subtype dựa trên role
        subtype_id = self.env.ref('mail.mt_comment').id

        # Xử lý nội dung Markdown
        # Giữ nguyên nội dung Markdown, không cần chuyển đổi vì sẽ được xử lý bởi compute field html_content

        # Gửi tin nhắn trực tiếp vào task hoặc kênh
        # Vô hiệu hóa chức năng gửi email
        context = {
            'mail_create_nosubscribe': True,
            'mail_auto_delete': False,
            'mail_notify_author': False,
            'mail_notify_user_signature': False,
            'mail_create_nolog': True,
            'tracking_disable': True,
            'mail_notrack': True,
        }

        if self.channel_id:
            # Gửi tin nhắn vào kênh
            message = self.channel_id.with_context(**context).message_post(
                body=content,
                message_type='comment',
                subtype_id=subtype_id,
                author_id=author_id,
                email_from='<EMAIL>',
            )
        else:
            # Gửi tin nhắn vào task
            message = self.with_context(**context).message_post(
                body=content,
                message_type='comment',
                subtype_id=subtype_id,
                author_id=author_id,
                email_from='<EMAIL>',
            )

        # Lưu trữ thông tin parts và role cho tất cả tin nhắn
        if parts:
            self.env.cr.execute(
                "UPDATE mail_message SET a2a_parts = %s, a2a_role = %s WHERE id = %s",
                (json.dumps(parts), role, message.id)
            )
        else:
            # Tạo TextPart mặc định
            default_parts = [{
                'type': 'text',
                'text': content
            }]
            self.env.cr.execute(
                "UPDATE mail_message SET a2a_parts = %s, a2a_role = %s WHERE id = %s",
                (json.dumps(default_parts), role, message.id)
            )

        # Cập nhật trạng thái task
        if self.state == 'input_required' and role == 'user':
            self.state = 'working'

        return message

# Model A2ATaskMessage đã được loại bỏ và thay thế bằng mail.message


class A2ATaskArtifact(models.Model):
    """Model đại diện cho một artifact trong A2A Task."""

    _name = "eb_a2a.task.artifact"
    _description = "A2A Task Artifact"

    task_id = fields.Many2one(
        'eb_a2a.task',
        string="Task",
        required=True,
        ondelete='cascade'
    )

    name = fields.Char(
        string="Tên",
        required=True
    )

    artifact_type = fields.Selection([
        ('file', 'File'),
        ('data', 'Data'),
        ('text', 'Text'),
    ], string="Loại", default='text', required=True)

    content = fields.Text(
        string="Nội dung",
        help="Nội dung của artifact (cho text hoặc data)"
    )

    file_data = fields.Binary(
        string="File",
        attachment=True,
        help="Dữ liệu file (cho artifact loại file)"
    )

    file_name = fields.Char(
        string="Tên file"
    )

    mime_type = fields.Char(
        string="MIME Type"
    )

    # Lưu trữ các phần của artifact theo định dạng A2A
    parts_json = fields.Text(
        string="Parts",
        help="Các phần của artifact dạng JSON"
    )

    def set_parts(self, parts):
        """Thiết lập các phần của artifact."""
        self.ensure_one()
        self.parts_json = json.dumps(parts)

    def get_parts(self):
        """Lấy các phần của artifact."""
        self.ensure_one()
        if self.parts_json:
            return json.loads(self.parts_json)

        # Nếu không có parts_json, tạo part mặc định dựa trên loại
        if self.artifact_type == 'file':
            return [{
                "type": "file",
                "name": self.file_name,
                "mimeType": self.mime_type or "application/octet-stream"
            }]
        elif self.artifact_type == 'data':
            return [{
                "type": "data",
                "data": json.loads(self.content or '{}')
            }]
        else:  # text
            return [{
                "type": "text",
                "text": self.content or ""
            }]
