# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

from odoo import api, fields, models


class A2AWebsiteConfig(models.Model):
    _name = 'eb_a2a.website.config'
    _description = 'Cấu hình Chatbot cho Website Ngoài'
    _rec_name = 'name'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(
        string='Tên cấu hình',
        required=True,
        tracking=True,
        help='Tên của cấu hình chatbot'
    )

    website_code = fields.Char(
        string='Mã website',
        required=True,
        tracking=True,
        help='Mã định danh của website ngoài'
    )

    website_url = fields.Char(
        string='URL Website',
        tracking=True,
        help='URL của website ngoài'
    )

    agent_endpoint_id = fields.Many2one(
        'eb_a2a.agent_endpoint',
        string='Agent Endpoint',
        required=True,
        tracking=True,
        domain=[('active', '=', True)],
        help='Agent sẽ được sử dụng cho chatbot trên website này'
    )

    is_enabled = fields.Boolean(
        string='Kích hoạt',
        default=True,
        tracking=True,
        help='Kích hoạt chatbot trên website này'
    )

    auto_popup = fields.Boolean(
        string='Tự động hiển thị',
        default=False,
        help='Tự động hiển thị chatbot sau một khoảng thời gian'
    )

    popup_delay = fields.Integer(
        string='Thời gian chờ (giây)',
        default=30,
        help='Thời gian chờ trước khi hiển thị chatbot (giây)'
    )

    welcome_message = fields.Text(
        string='Tin nhắn chào mừng',
        default='Xin chào! Tôi có thể giúp gì cho bạn?',
        help='Tin nhắn chào mừng khi người dùng mở chatbot'
    )

    placeholder_text = fields.Char(
        string='Placeholder',
        default='Nhập tin nhắn...',
        help='Placeholder cho ô nhập tin nhắn'
    )

    button_text = fields.Char(
        string='Nút gửi',
        default='Gửi',
        help='Văn bản cho nút gửi tin nhắn'
    )

    header_text = fields.Char(
        string='Tiêu đề',
        default='Hỗ trợ trực tuyến',
        help='Tiêu đề của chatbot'
    )

    theme_color = fields.Char(
        string='Màu chủ đạo',
        default='#7C7BAD',
        help='Màu chủ đạo của chatbot (mã hex)'
    )

    _sql_constraints = [
        ('website_code_uniq', 'unique(website_code)', 'Mỗi mã website chỉ có thể có một cấu hình chatbot!')
    ]

    @api.model
    def get_config_for_website(self, website_code=None):
        """Lấy cấu hình cho website được chỉ định bằng mã."""
        if not website_code:
            # Lấy cấu hình mặc định (cấu hình đầu tiên)
            config = self.search([], limit=1)
        else:
            config = self.search([('website_code', '=', website_code)], limit=1)

        if not config:
            return False

        return {
            'is_enabled': config.is_enabled,
            'agent_code': config.agent_endpoint_id.code,
            'auto_popup': config.auto_popup,
            'popup_delay': config.popup_delay,
            'welcome_message': config.welcome_message,
            'placeholder_text': config.placeholder_text,
            'button_text': config.button_text,
            'header_text': config.header_text,
            'theme_color': config.theme_color,
        }

    @api.model
    def _get_thread_with_access(self, thread_id, **kwargs):
        """Triển khai phương thức _get_thread_with_access cho mail.thread.

        Phương thức này được yêu cầu bởi controller mail_thread_data trong module mail.
        """
        return self.browse(int(thread_id)).sudo()
