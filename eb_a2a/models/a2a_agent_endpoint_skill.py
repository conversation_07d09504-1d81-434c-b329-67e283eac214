# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

from odoo import api, fields, models, _
import json
import logging

_logger = logging.getLogger(__name__)


class A2AAgentEndpointSkill(models.Model):
    """Model đại diện cho một kỹ năng của A2A Agent Endpoint."""

    _name = "eb_a2a.agent_endpoint.skill"
    _description = "A2A Agent Endpoint Skill"
    _order = "name"

    name = fields.Char(
        string="Tên kỹ năng",
        required=True,
        help="Tên của kỹ năng"
    )

    description = fields.Text(
        string="Mô tả",
        help="Mô tả chi tiết về kỹ năng và cách sử dụng"
    )

    agent_endpoint_id = fields.Many2one(
        'eb_a2a.agent_endpoint',
        string="Agent Endpoint",
        required=True,
        ondelete='cascade',
        help="Agent endpoint sở hữu kỹ năng này"
    )

    parameters = fields.Text(
        string="Tham số",
        help="Tham số của kỹ năng dưới dạng JSON",
        default="{}"
    )

    @api.model_create_multi
    def create(self, vals_list):
        """Tạo kỹ năng và kiểm tra tham số."""
        for vals in vals_list:
            # Kiểm tra tham số JSON
            if vals.get('parameters'):
                try:
                    json.loads(vals['parameters'])
                except json.JSONDecodeError:
                    vals['parameters'] = "{}"
                    _logger.warning("Invalid JSON parameters for skill, resetting to empty object")

        return super().create(vals_list)

    def write(self, vals):
        """Cập nhật kỹ năng và kiểm tra tham số."""
        if vals.get('parameters'):
            try:
                json.loads(vals['parameters'])
            except json.JSONDecodeError:
                vals['parameters'] = "{}"
                _logger.warning("Invalid JSON parameters for skill, resetting to empty object")

        return super().write(vals)
