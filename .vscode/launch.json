{"version": "0.2.0", "configurations": [{"name": "O<PERSON>o Debug", "type": "python", "request": "launch", "python": "/Users/<USER>/miniconda3/envs/odoo18/bin/python3", "module": "debugpy", "stopOnEntry": false, "pythonArgs": ["-Xfrozen_modules=off"], "args": ["/Users/<USER>/miniconda3/envs/odoo18/bin/odoo", "-c", "/Users/<USER>/Develop/lms/odoo-lms/odoo.conf", "--dev", "xml"], "cwd": "${workspaceRoot}", "envFile": "${workspaceFolder}/.env", "console": "integratedTerminal", "env": {"GEVENT_SUPPORT": "false", "PYTEST_ADDOPTS": "--no-cov", "-Xfrozen_modules": "off", "Xfrozen_modules": "off", "PYDEVD_DISABLE_FILE_VALIDATION": "1"}, "justMyCode": false}]}