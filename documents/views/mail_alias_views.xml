<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <record id="mail_alias_view_form" model="ir.ui.view">
        <field name="name">document.mail.alias.view.form</field>
        <field name="model">mail.alias</field>
        <field name="inherit_id" ref="mail.mail_alias_view_form"/>
        <field name="mode">primary</field>
        <field name="priority">24</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='alias_model_id']" position="attributes">
                <attribute name="readonly">1</attribute>
            </xpath>
        </field>
    </record>

    <!-- todo - remove in master (with view(s)) -->
    <record id="mail_alias_action" model="ir.actions.act_window">
        <field name="name">Email links</field>
        <field name="res_model">mail.alias</field>
        <field name="view_mode">list,form</field>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'list', 'view_id': False}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('documents.mail_alias_view_form')})]"/>
        <field name="domain">[('alias_model_id', '=', 'documents.document')]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No shared links
            </p>
        </field>
    </record>

</odoo>
