<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="document_portal_view" name="Document Portal View">
        <t t-call="web.layout">
            <t t-set="head_document_portal_view">
                <script type="text/javascript">
                    odoo.__session_info__ = <t t-out="json.dumps(session_info)"/>;
                    odoo.loadMenusPromise = Promise.resolve();
                </script>
                <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
                <base target="_parent"/>
                <t t-call-assets="documents.webclient"/>
                <t t-call="web.conditional_assets_tests"/>
            </t>
            <t t-set="head" t-value="head_document_portal_view + (head or '')"/>
            <t t-set="body_classname" t-value="'o_web_client o_documents_portal_view'"/>
        </t>
    </template>

    <template id="portal_my_home_documents" name="documents.portal_my_home" inherit_id="portal.portal_my_home" priority="200">
        <xpath expr="//div[hasclass('o_portal_docs')]" position="inside">
            <t t-call="portal.portal_docs_entry">
                <t t-set="icon" t-value="'/documents/static/description/icon.svg'"/>
                <t t-set="title">Documents</t>
                <t t-set="url" t-value="'/odoo/documents_portal'"/>
                <t t-set="text">Access all your documents</t>
                <t t-set="placeholder_count" t-value="'document_count'"/>
            </t>
        </xpath>
    </template>
</odoo>
