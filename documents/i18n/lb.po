# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents
#
# Translators:
# <AUTHOR> <EMAIL>, 2019
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 16:01+0000\n"
"PO-Revision-Date: 2019-08-26 09:35+0000\n"
"Last-Translator: <PERSON> ALT <<EMAIL>>, 2019\n"
"Language-Team: Luxembourgish (https://www.transifex.com/odoo/teams/41243/lb/)\n"
"Language: lb\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.js:0
msgid " + Add a tag"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "#{document_is_url and 'Link to ' or 'Download '}#{document.name}"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "%s Documents"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "%s Documents (%s locked)"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "%s file(s) not moved because they are locked by another user"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid ", expired on"
msgstr ""

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid ". Scanned files will appear automatically in your workspace. Then, process your documents in bulk with the split tool: launch user defined actions, request a signature, convert to vendor bills with AI, etc."
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_finance_fiscal_year_2017
msgid "2022"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_finance_fiscal_year_2018
msgid "2023"
msgstr ""

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid "<b class=\"tip_title\">Tip: Become a paperless company</b>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "<b>Bytes</b>"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "<b>Deselect this page</b> as we plan to process all bills first."
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "<b>Gb</b>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "<b>Kb</b>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "<b>Mb</b>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "<br/>Powered by"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "<i class=\"fa fa fa-folder text-odoo me-2\" title=\"Workspace\"/>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "<i class=\"fa fa-download fa-fw\"/>  Download All"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid "<i class=\"fa fa-download fa-fw\"/> Download"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<i class=\"fa fa-globe\" title=\"Document url\"/>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid "<i class=\"fa fa-link\"/> Go to URL"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "<i class=\"fa fa-lock oe_inline\" title=\"Locked\" attrs=\"{'invisible': [('lock_uid', '=', False)]}\"/>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "<i class=\"fa fa-tag o_documents_tag_color ms-2\" attrs=\"{'invisible': [('tag_ids', '=', [])]}\" title=\"Tags\"/>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "<i class=\"fa fa-upload\"/>  Upload"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "<span class=\"o_stat_text\">Actions</span>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_document_res_partner_view
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "<span class=\"o_stat_text\">Documents</span>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "<span class=\"o_stat_text\">Related <br/> Record</span>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "<span title=\"Requested Document\">Requested Document</span>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "<span>&amp;nbsp;Documents.</span>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<span><b> Request</b></span>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<span><b>Requested Document</b></span>"
msgstr ""

#. module: documents
#: model:mail.template,body_html:documents.mail_template_document_request_reminder
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"                    <tbody>\n"
"                        <!-- HEADER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\">\n"
"                                        <span style=\"font-size: 10px;\">\n"
"                                            Document Request: <br>\n"
"                                            <t t-if=\"object.create_share_id.name\">\n"
"                                                <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.create_share_id.name or ''\">Inbox Financial</span>\n"
"                                            </t>\n"
"                                        </span><br>\n"
"                                    </td><td valign=\"middle\" align=\"right\">\n"
"                                        <img t-attf-src=\"/logo.png?company={{ object.create_uid.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.create_uid.company_id.name\">\n"
"                                    </td></tr>\n"
"                                    <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- CONTENT -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                        <div>\n"
"                                            Hello <t t-out=\"object.create_share_id.owner_id.name or ''\">OdooBot</t>,\n"
"                                            <br><br>\n"
"                                            This is a friendly reminder to upload your requested document:\n"
"                                            <br><br>\n"
"                                            <center>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.create_share_id.name\">\n"
"                                                        <b t-out=\"object.create_share_id.name or ''\">Inbox Financial</b>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.create_share_id.activity_note\">\n"
"                                                        <i t-out=\"object.create_share_id.activity_note or ''\">Example of a note.</i>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <br>\n"
"                                                <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                    <a t-att-href=\"object.create_share_id.full_url\" style=\"background-color: #875A7B; padding: 20px 30px 20px 30px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                        Upload the requested document\n"
"                                                    </a>\n"
"                                                </div>\n"
"                                            </center><br>\n"
"                                            Please provide us with the missing document before the link expires (planned on <t t-out=\"object.create_share_id.date_deadline or ''\">2021-05-17</t>).\n"
"                                            <br><br>\n"
"                                            Thank you,\n"
"                                            <t t-if=\"user and user.signature\">\n"
"                                                <br>\n"
"                                                <t t-out=\"user.signature\">--<br>Mitchell Admin</t>\n"
"                                                <br>\n"
"                                            </t>\n"
"                                        </div>\n"
"                                    </td></tr>\n"
"                                    <tr><td style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- FOOTER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\" align=\"left\">\n"
"                                        <t t-out=\"object.create_uid.company_id.name or ''\">YourCompany</t>\n"
"                                    </td></tr>\n"
"                                    <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                                        <t t-out=\"object.create_uid.company_id.phone or ''\">******-123-4567</t>\n"
"                                        <t t-if=\"object.create_uid.company_id.email\">\n"
"                                            | <a t-attf-href=\"'mailto:%s' % {{ object.create_uid.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.email\"><EMAIL></a>\n"
"                                        </t>\n"
"                                        <t t-if=\"object.create_uid.company_id.website\">\n"
"                                            | <a t-att-href=\"object.create_uid.company_id.website\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.website\">http://www.example.com</a>\n"
"                                        </t>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                    </table>\n"
"                    </td></tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                          <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"                            <t t-if=\"object.create_share_id.date_deadline\">\n"
"                                This link expires on <b t-out=\"object.create_share_id.date_deadline\">2021-05-17</b>.<br>\n"
"                            </t>\n"
"                            Powered by <a target=\"_blank\" href=\"https://www.odoo.com/app/documents\" style=\"color: #875A7B;\">Odoo Documents</a>\n"
"                          </td></tr>\n"
"                        </table>\n"
"                    </td></tr>\n"
"                </table>\n"
"            "
msgstr ""

#. module: documents
#: model:mail.template,body_html:documents.mail_template_document_request
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"                    <tbody>\n"
"                        <!-- HEADER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\">\n"
"                                        <span style=\"font-size: 10px;\">\n"
"                                            Document Request: <br>\n"
"                                            <t t-if=\"object.name\">\n"
"                                                <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Inbox Financial</span>\n"
"                                            </t>\n"
"                                        </span><br>\n"
"                                    </td><td valign=\"middle\" align=\"right\" t-if=\"not object.create_uid.company_id.uses_default_logo\">\n"
"                                        <img t-attf-src=\"/logo.png?company={{ object.create_uid.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.create_uid.company_id.name\">\n"
"                                    </td></tr>\n"
"                                    <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- CONTENT -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                        <div>\n"
"                                            Hello <t t-out=\"object.owner_id.name or ''\">OdooBot</t>,\n"
"                                            <br><br>\n"
"                                            <t t-out=\"object.create_uid.name or ''\">OdooBot</t> (<t t-out=\"object.create_uid.email or ''\"><EMAIL></t>) asks you to provide the following document:\n"
"                                            <br><br>\n"
"                                            <center>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.name\">\n"
"                                                        <b t-out=\"object.name or ''\">Inbox Financial</b>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.activity_note\">\n"
"                                                        <i t-out=\"object.activity_note or ''\">Example of a note.</i>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <br>\n"
"                                                <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                    <a t-att-href=\"object.full_url\" style=\"background-color: #875A7B; padding: 20px 30px 20px 30px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                        Upload the requested document\n"
"                                                    </a>\n"
"                                                </div>\n"
"                                            </center><br>\n"
"                                            Please provide us with the missing document<t t-if=\"object.date_deadline\"> before the link expires (planned on <t t-out=\"object.date_deadline\">2021-05-17</t>)</t>.\n"
"                                            <t t-if=\"user and user.signature\">\n"
"                                                <br>\n"
"                                                <t t-out=\"user.signature or ''\">--<br>Mitchell Admin</t>\n"
"                                                <br>\n"
"                                            </t>\n"
"                                        </div>\n"
"                                    </td></tr>\n"
"                                    <tr><td style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- FOOTER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\" align=\"left\">\n"
"                                        <t t-out=\"object.create_uid.company_id.name or ''\">YourCompany</t>\n"
"                                    </td></tr>\n"
"                                    <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                                        <t t-out=\"object.create_uid.company_id.phone or ''\">******-123-4567</t>\n"
"                                        <t t-if=\"object.create_uid.company_id.email\">\n"
"                                            | <a t-attf-href=\"'mailto:%s' % {{ object.create_uid.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.email or ''\"><EMAIL></a>\n"
"                                        </t>\n"
"                                        <t t-if=\"object.create_uid.company_id.website\">\n"
"                                            | <a t-attf-href=\"'%s' % {{ object.create_uid.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.website or ''\">http://www.example.com</a>\n"
"                                        </t>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                    </table>\n"
"                    </td></tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                          <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"                            <t t-if=\"object.date_deadline\">\n"
"                                This link expires on <b t-out=\"object.date_deadline or ''\">2021-05-17</b>.<br>\n"
"                            </t>\n"
"                            Powered by <a target=\"_blank\" href=\"https://www.odoo.com/app/documents\" style=\"color: #875A7B;\">Odoo Documents</a>\n"
"                          </td></tr>\n"
"                        </table>\n"
"                    </td></tr>\n"
"                </table>\n"
"            "
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_defaults
msgid "A Python dictionary that will be evaluated to provide default values when creating new records for this alias."
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_workflow_rule
msgid "A set of condition and actions which will be available to all attachments matching the conditions"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_folder.py:0
msgid "A workspace cannot have one of his child defined as Parent Workspace in order to avoid a recursion issue."
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__parent_folder_id
#: model:ir.model.fields,help:documents.field_documents_folder_deletion_wizard__parent_folder_id
msgid "A workspace will inherit the tags of its parent workspace"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Access"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "Access Error"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__group_ids
msgid "Access Groups"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Access Rights"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__access_token
msgid "Access Token"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__action
msgid "Action"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__name
msgid "Action Button Name"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__action_count
msgid "Action Count"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Action Name"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_needaction
#: model:ir.model.fields,field_description:documents.field_documents_share__message_needaction
msgid "Action Needed"
msgstr ""

#. module: documents
#. odoo-python
#. odoo-javascript
#: code:addons/documents/models/documents_folder.py:0
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
#: model:ir.ui.menu,name:documents.workflow_rules_menu
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Actions"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__active
msgid "Active"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_ids
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Activities"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_mail_activity
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Activity"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_note
msgid "Activity Note"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_state
msgid "Activity State"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_mail_activity_type
msgid "Activity Type"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_type_id
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_type_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_type_id
msgid "Activity type"
msgstr ""

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_action__action__add
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "Add"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Add File"
msgstr ""

#. module: documents
#: model:ir.actions.act_window,name:documents.action_url_form
msgid "Add Url"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "Add a Link"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Add new file"
msgstr ""

#. module: documents
#: model:res.groups,name:documents.group_documents_manager
msgid "Administrator"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_marketing_assets_ads
msgid "Ads"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_id
msgid "Alias"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_contact
msgid "Alias Contact Security"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_name
msgid "Alias Name"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_status
msgid "Alias Status"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_domain
msgid "Alias domain"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_status
msgid "Alias status assessed on the last message received."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_model_id
msgid "Aliased Model"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/controllers/documents.py:0
msgid "All files uploaded"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__action
msgid "Allows to"
msgstr ""

#. module: documents
#: model:ir.module.category,description:documents.module_category_documents_management
msgid "Allows you to manage your documents."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_workflow_rule.py:0
msgid "Already linked Documents"
msgstr ""

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid "An easy way to process incoming mails is to configure your scanner to send PDFs to"
msgstr ""

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid "An easy way to process incoming mails is to configure your scanner to send PDFs to your workspace email. Scanned files will appear automatically in your workspace. Then, process your documents in bulk with the split tool: launch user defined actions, request a signature, convert to vendor bills with AI, etc."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "An error occured while uploading."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Archive"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Archive original file(s)"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Archived"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "As this PDF contains multiple documents, let's split and process in bulk."
msgstr ""

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_finance_afv
msgid "Ask for Validation"
msgstr ""

#. module: documents
#: model:documents.facet,name:documents.documents_marketing_assets
msgid "Assets"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Attached To"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_ir_attachment
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_id
msgid "Attachment"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_attachment_count
#: model:ir.model.fields,field_description:documents.field_documents_share__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__description
msgid "Attachment Description"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_name
msgid "Attachment Name"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_type
msgid "Attachment Type"
msgstr ""

#. module: documents
#: model_terms:documents.folder,description:documents.documents_finance_folder
msgid "Automate your inbox using scanned documents or emails sent to <span class=\"o_folder_description_alias\"><strong>inbox-financial</strong> email alias</span>."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__available_rule_ids
msgid "Available Rules"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_bill
msgid "Bill"
msgstr ""

#. module: documents
#: model:documents.folder,name:documents.documents_marketing_brand1_folder
msgid "Brand 1"
msgstr ""

#. module: documents
#: model:documents.folder,name:documents.documents_marketing_brand2_folder
msgid "Brand 2"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_marketing_assets_brochures
msgid "Brochures"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_mail_activity_type__folder_id
msgid "By defining a folder, the upload activities will generate a document"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__can_upload
msgid "Can Upload"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_folder_deletion_wizard_view_form
#: model_terms:ir.ui.view,arch_db:documents.documents_link_to_record_form_view
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Cancel"
msgstr ""

#. module: documents
#: model_terms:documents.folder,description:documents.documents_internal_folder
msgid "Categorize, share and keep track of all your internal documents."
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_facet
#: model:ir.model.fields,field_description:documents.field_documents_tag__facet_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__facet_id
#: model_terms:ir.ui.view,arch_db:documents.facet_view_tree
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Category"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__checksum
msgid "Checksum/SHA1"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_workflow_rule.py:0
msgid "Choose a record to link"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Click on a card to <b>select the document</b>."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Click on a thumbnail to <b>preview the document</b>."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Click on the <b>page separator</b>: we don't want to split these two pages as they belong to the same document."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Close"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__company_id
#: model:ir.model.fields,field_description:documents.field_documents_folder__company_id
msgid "Company"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__condition_type
msgid "Condition type"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Conditions"
msgstr ""

#. module: documents
#: model:ir.ui.menu,name:documents.Config
msgid "Configuration"
msgstr "Konfiguratioun"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Configure Email Servers"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_res_partner
#: model:ir.model.fields,field_description:documents.field_documents_document__partner_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__partner_id
#: model:ir.model.fields,field_description:documents.field_documents_share__partner_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__criteria_partner_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Contact"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Contains"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_Contracts
#: model:documents.tag,name:documents.documents_internal_template_contracts
msgid "Contracts"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "Control panel buttons"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__create_model
msgid "Create"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_share_id
msgid "Create Share"
msgstr ""

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_internal_mark
msgid "Create Vendor Bill"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_option
msgid "Create a new activity"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_facet__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_folder__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_folder_deletion_wizard__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_share__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_tag__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__create_uid
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Created by"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_date
#: model:ir.model.fields,field_description:documents.field_documents_facet__create_date
#: model:ir.model.fields,field_description:documents.field_documents_folder__create_date
#: model:ir.model.fields,field_description:documents.field_documents_folder_deletion_wizard__create_date
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__create_date
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__create_date
#: model:ir.model.fields,field_description:documents.field_documents_share__create_date
#: model:ir.model.fields,field_description:documents.field_documents_tag__create_date
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__create_date
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__create_date
msgid "Created on"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Creation Date"
msgstr ""

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__condition_type__criteria
msgid "Criteria"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_bounced_content
msgid "Custom Bounced Message"
msgstr ""

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__days
#: model:ir.model.fields.selection,name:documents.selection__documents_share__activity_date_deadline_range_type__days
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__activity_date_deadline_range_type__days
msgid "Days"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_defaults
msgid "Default Values"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Default values for uploaded documents"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
#: model_terms:ir.ui.view,arch_db:documents.documents_folder_deletion_wizard_view_form
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_tree
msgid "Delete"
msgstr ""

#. module: documents
#: model:ir.actions.act_window,name:documents.documents_folder_deletion_wizard_action
msgid "Delete Confirmation"
msgstr ""

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_internal_deprecate
msgid "Deprecate"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_internal_status_deprecated
msgid "Deprecated"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__description
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Description"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Discard"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__display_name
#: model:ir.model.fields,field_description:documents.field_documents_facet__display_name
#: model:ir.model.fields,field_description:documents.field_documents_folder__display_name
#: model:ir.model.fields,field_description:documents.field_documents_folder_deletion_wizard__display_name
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__display_name
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__display_name
#: model:ir.model.fields,field_description:documents.field_documents_share__display_name
#: model:ir.model.fields,field_description:documents.field_documents_tag__display_name
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__display_name
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__display_name
msgid "Display Name"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.js:0
msgid "Do you really want to unlink this record?"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_document
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.documents_view_list
msgid "Document"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__document_count
#: model:ir.model.fields,field_description:documents.field_res_partner__document_count
msgid "Document Count"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Document Name"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__owner_id
msgid "Document Owner"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_request_wizard
msgid "Document Request"
msgstr ""

#. module: documents
#: model:mail.template,subject:documents.mail_template_document_request
msgid "Document Request {{ object.name != False and ': '+ object.name or '' }}"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: code:addons/documents/models/mail_activity.py:0
msgid "Document Request: %s Uploaded by: %s"
msgstr ""

#. module: documents
#: model:mail.template,name:documents.mail_template_document_request_reminder
msgid "Document Request: Reminder"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__has_write_access
msgid "Document User Upload Rights"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_workflow_action
msgid "Document Workflow Tag Action"
msgstr ""

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_share__type__ids
msgid "Document list"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "Document preview"
msgstr ""

#. module: documents
#: model:mail.template,name:documents.mail_template_document_request
msgid "Document: Document Request"
msgstr ""

#. module: documents
#. odoo-python
#. odoo-javascript
#: code:addons/documents/models/documents_folder.py:0
#: code:addons/documents/models/res_partner.py:0
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
#: model:documents.facet,name:documents.documents_finance_documents
#: model:ir.actions.act_window,name:documents.document_action
#: model:ir.model.fields,field_description:documents.field_documents_folder__document_ids
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__document_ids
#: model:ir.model.fields,field_description:documents.field_res_users__document_count
#: model:ir.module.category,name:documents.module_category_documents_management
#: model:ir.ui.menu,name:documents.dashboard
#: model:ir.ui.menu,name:documents.menu_root
#: model_terms:ir.ui.view,arch_db:documents.action_view_search
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.documents_view_activity
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Documents"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_folder_deletion_wizard
msgid "Documents Folder Deletion Wizard"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_link_to_record_wizard
msgid "Documents Link to Record"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_share
msgid "Documents Share"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_folder
msgid "Documents Workspace"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_mixin
msgid "Documents creation mixin"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Does not contain"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__domain
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__domain
#: model:ir.model.fields.selection,name:documents.selection__documents_share__type__domain
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__condition_type__domain
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Domain"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
#: model:ir.model.fields.selection,name:documents.selection__documents_share__action__download
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Download"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "Download all files"
msgstr ""

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_share__action__downloadupload
msgid "Download and Upload"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_drop_zone.xml:0
msgid "Drop files here to upload"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_date_deadline_range
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_date_deadline_range
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_date_deadline_range
msgid "Due Date In"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_date_deadline_range_type
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_date_deadline_range_type
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_date_deadline_range_type
msgid "Due type"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
#: code:addons/documents/static/src/views/search/documents_search_panel.xml:0
msgid "Edit"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.js:0
msgid "Edit the linked record"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Email Alias"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__email_cc
msgid "Email cc"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#: code:addons/documents/static/src/views/hooks.js:0
#: model:ir.model.fields.selection,name:documents.selection__documents_document__thumbnail_status__error
msgid "Error"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__excluded_tag_ids
msgid "Excluded Tags"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_expense
msgid "Expense"
msgstr ""

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_share__state__expired
msgid "Expired"
msgstr ""

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_facet_name_unique
msgid "Facet already exists in this folder"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__favorited_ids
msgid "Favorite of"
msgstr ""

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__binary
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "File"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__datas
msgid "File Content (base64)"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__raw
msgid "File Content (raw)"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__file_extension
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "File Extension"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__file_size
msgid "File Size"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_ir_binary
msgid "File streaming helper model for controllers"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/controllers/documents.py:0
msgid "File uploaded by:"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Files Centralization"
msgstr ""

#. module: documents
#: model:documents.folder,name:documents.documents_finance_folder
msgid "Finance"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_financial
msgid "Financial"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_fiscal
msgid "Fiscal"
msgstr ""

#. module: documents
#: model:documents.facet,name:documents.documents_finance_fiscal_year
msgid "Fiscal years"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder_deletion_wizard__folder_id
#: model:ir.model.fields,field_description:documents.field_mail_activity_type__folder_id
msgid "Folder"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_follower_ids
#: model:ir.model.fields,field_description:documents.field_documents_share__message_follower_ids
msgid "Followers"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_partner_ids
#: model:ir.model.fields,field_description:documents.field_documents_share__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Future Activities"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.facet_view_search
msgid "Group By"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__read_group_ids
msgid "Groups able to see the workspace and read its documents without create/edit rights."
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__group_ids
msgid "Groups able to see the workspace and read/create/edit its documents."
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_internal_knowledge_hr
msgid "HR"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__has_message
#: model:ir.model.fields,field_description:documents.field_documents_share__has_message
msgid "Has Message"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_document__previous_attachment_ids
msgid "History"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__id
#: model:ir.model.fields,field_description:documents.field_documents_facet__id
#: model:ir.model.fields,field_description:documents.field_documents_folder__id
#: model:ir.model.fields,field_description:documents.field_documents_folder_deletion_wizard__id
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__id
#: model:ir.model.fields,field_description:documents.field_documents_share__id
#: model:ir.model.fields,field_description:documents.field_documents_tag__id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__id
msgid "ID"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_parent_thread_id
msgid "ID of the parent record holding the alias (example: project holding the task creation alias)"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_needaction
#: model:ir.model.fields,help:documents.field_documents_share__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_has_error
#: model:ir.model.fields,help:documents.field_documents_document__message_has_sms_error
#: model:ir.model.fields,help:documents.field_documents_share__message_has_error
#: model:ir.model.fields,help:documents.field_documents_share__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_bounced_content
msgid "If set, this content will automatically be sent out to unauthorized users instead of the default message."
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Image/Video"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_marketing_assets_images
msgid "Images"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_finance_status_inbox
#: model:documents.tag,name:documents.documents_internal_status_inbox
#: model:mail.activity.type,name:documents.mail_documents_activity_data_Inbox
msgid "Inbox"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__include_sub_folders
msgid "Include Sub Folders"
msgstr ""

#. module: documents
#: model_terms:documents.folder,description:documents.documents_internal_folder
msgid "Incoming letters sent to <span class=\"o_folder_description_alias\">inbox email alias</span> will be added to your inbox automatically."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__index_content
msgid "Indexed Content"
msgstr ""

#. module: documents
#: model:documents.folder,name:documents.documents_internal_folder
msgid "Internal"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_editable_attachment
msgid "Is Editable Attachment"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_favorited
msgid "Is Favorited"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_is_follower
#: model:ir.model.fields,field_description:documents.field_documents_share__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__is_shared
msgid "Is Shared"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_multipage
msgid "Is considered multipage"
msgstr ""

#. module: documents
#: model:documents.facet,name:documents.documents_internal_knowledge
msgid "Knowledge"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_facet__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_folder__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_folder_deletion_wizard__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_share__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_tag__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__write_uid
msgid "Last Updated by"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__write_date
#: model:ir.model.fields,field_description:documents.field_documents_facet__write_date
#: model:ir.model.fields,field_description:documents.field_documents_folder__write_date
#: model:ir.model.fields,field_description:documents.field_documents_folder_deletion_wizard__write_date
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__write_date
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__write_date
#: model:ir.model.fields,field_description:documents.field_documents_share__write_date
#: model:ir.model.fields,field_description:documents.field_documents_tag__write_date
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__write_date
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__write_date
msgid "Last Updated on"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Late Activities"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_internal_knowledge_legal
msgid "Legal"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Let's process documents in your Inbox.<br/><i>Tip: Use Tags to filter documents and structure your process.</i>"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Let's process these bills: send to Finance workspace."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Let's process this document, coming from our scanner."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Let's tag this mail as legal<br/> <i>Tips: actions can be tailored to your process, according to the workspace.</i>"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__user_specific
msgid "Limit Read Groups to the documents of which they are owner."
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__user_specific_write
msgid "Limit Write Groups to the documents of which they are owner."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
#: model_terms:ir.ui.view,arch_db:documents.documents_link_to_record_form_view
msgid "Link"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/controllers/documents.py:0
msgid "Link created by:"
msgstr ""

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__create_model__link_to_record
msgid "Link to record"
msgstr ""

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_share__state__live
msgid "Live"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Lock"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_locked
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Locked"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__lock_uid
msgid "Locked by"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Log a note..."
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Login"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Logout"
msgstr ""

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_internal_mad
msgid "Mark As Draft"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__remove_activities
msgid "Mark all as Done"
msgstr ""

#. module: documents
#: model:documents.folder,name:documents.documents_marketing_folder
msgid "Marketing"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_error
#: model:ir.model.fields,field_description:documents.field_documents_share__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_ids
#: model:ir.model.fields,field_description:documents.field_documents_share__message_ids
msgid "Messages"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__mimetype
msgid "Mime Type"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Miscellaneous"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__model_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Model"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__accessible_model_ids
msgid "Models"
msgstr ""

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__months
#: model:ir.model.fields.selection,name:documents.selection__documents_share__activity_date_deadline_range_type__months
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__activity_date_deadline_range_type__months
msgid "Months"
msgstr ""

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_finance_mti
msgid "Move To Inbox"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__folder_id
msgid "Move to Workspace"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_folder_deletion_wizard_view_form
msgid "Move to parent workspace"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector_field.js:0
msgid "Multiple values"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "My Activities"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "My Documents"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "My Favorites"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__name
#: model:ir.model.fields,field_description:documents.field_documents_facet__name
#: model:ir.model.fields,field_description:documents.field_documents_folder__name
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__name
#: model:ir.model.fields,field_description:documents.field_documents_share__name
#: model:ir.model.fields,field_description:documents.field_documents_tag__name
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Name"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Name of the share link"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.xml:0
msgid "New"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "New File"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "New Group"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "New Tag"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "New Workspace"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "No document has been selected"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "No limit"
msgstr ""

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.share_action
msgid "No shared links"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Not a file"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Not attached"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_note
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_note
msgid "Note"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_needaction_counter
#: model:ir.model.fields,field_description:documents.field_documents_share__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_error_counter
#: model:ir.model.fields,field_description:documents.field_documents_share__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_needaction_counter
#: model:ir.model.fields,help:documents.field_documents_share__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_has_error_counter
#: model:ir.model.fields,help:documents.field_documents_share__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Odoo Logo"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Odoo Website"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__limited_to_single_record
msgid "One record limit"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "Open chatter"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_force_thread_id
msgid "Optional ID of a thread (record) to which all incoming messages will be attached, even if they did not reply to it. If set, this will disable the creation of new records completely."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.xml:0
msgid "Or send emails to"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_internal_knowledge_other
msgid "Other"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__user_specific
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Own Documents Only"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__user_specific_write
msgid "Own Documents Only (Write)"
msgstr ""

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_folder_check_user_specific
msgid "Own Documents Only may not be enabled for write groups if it is not enabled for read groups."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__owner_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__owner_id
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_user_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__criteria_owner_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Owner"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "Owner: #{document.owner_id.name}"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "PDF/Document"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder_deletion_wizard__parent_folder_id
msgid "Parent Folder"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_parent_model_id
msgid "Parent Model"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__parent_path
msgid "Parent Path"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__parent_folder_id
#: model_terms:ir.ui.view,arch_db:documents.folder_view_search
msgid "Parent Workspace"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_parent_model_id
msgid "Parent model holding the alias. The model holding the alias reference is not necessarily the model given by alias_model_id (example: project (parent_model) and task (model))"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "Partial transfer"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.not_available
msgid "Please contact the person that shared this link for more information."
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__thumbnail_status__present
msgid "Present"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_internal_template_presentations
msgid "Presentations"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_internal_template_project
msgid "Project"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__rating_ids
#: model:ir.model.fields,field_description:documents.field_documents_share__rating_ids
msgid "Ratings"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Read Access"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__read_group_ids
msgid "Read Groups"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__resource_ref
msgid "Record"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_force_thread_id
msgid "Record Thread ID"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__domain_folder_id
msgid "Related Workspace"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Remaining Pages"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/activity/documents_activity_controller.js:0
msgid "Reminder emails have been sent."
msgstr ""

#. module: documents
#: model:mail.template,subject:documents.mail_template_document_request_reminder
msgid "Reminder to upload your document{{ object.name and ' : ' + object.name or '' }}"
msgstr ""

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_action__action__remove
msgid "Remove"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "Replace"
msgstr ""

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_action__action__replace
msgid "Replace by"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__empty
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Request"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__request_activity_id
msgid "Request Activity"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Request To"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/web/activity/activity_menu_patch.xml:0
msgid "Request a Document"
msgstr ""

#. module: documents
#: model:ir.actions.act_window,name:documents.action_request_form
msgid "Request a file"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid "Requested"
msgstr ""

#. module: documents
#: model:mail.activity.type,name:documents.mail_documents_activity_data_md
msgid "Requested Document"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__required_tag_ids
msgid "Required Tags"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_model_name
msgid "Res Model Name"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__res_id
msgid "Resource ID"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_model
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__res_model
msgid "Resource Model"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_name
msgid "Resource Name"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_user_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_user_id
msgid "Responsible"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Restore"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_sms_error
#: model:ir.model.fields,field_description:documents.field_documents_share__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_internal_knowledge_sales
msgid "Sales"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Save"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_option
msgid "Schedule Activity"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Select/Unselect All: Shift + A"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Send this letter to the legal department, by assigning the right tags."
msgstr ""

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_internal_legal
msgid "Send to Legal"
msgstr ""

#. module: documents
#: model:mail.template,description:documents.mail_template_document_request
msgid "Sent to partner when requesting a document from them"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_facet__sequence
#: model:ir.model.fields,field_description:documents.field_documents_folder__sequence
#: model:ir.model.fields,field_description:documents.field_documents_tag__sequence
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__sequence
msgid "Sequence"
msgstr ""

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_finance_2018contracts
msgid "Set As 2023 Contracts"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__partner_id
msgid "Set Contact"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__user_id
msgid "Set Owner"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__tag_action_ids
msgid "Set Tags"
msgstr ""

#. module: documents
#: model:mail.template,description:documents.mail_template_document_request_reminder
msgid "Set reminders in activities to notify users who didn't upload their requested document"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__has_owner_activity
msgid "Set the activity on the document owner"
msgstr ""

#. module: documents
#: model:ir.actions.act_window,name:documents.configuration_action
#: model:ir.actions.act_window,name:documents.settings_action
#: model:ir.ui.menu,name:documents.settings_menu
msgid "Settings"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Share"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__share_link_ids
msgid "Share Links"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/controllers/documents.py:0
msgid "Share link"
msgstr ""

#. module: documents
#: model:ir.actions.act_window,name:documents.share_action
msgid "Share links"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_share.py:0
msgid "Share selected files"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_share.py:0
msgid "Share selected workspace"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "Share this domain"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "Share this selection"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__type
msgid "Share type"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__create_share_id
msgid "Share used to create this document"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Shared"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__document_ids
msgid "Shared Documents"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__tag_ids
msgid "Shared Tags"
msgstr ""

#. module: documents
#: model:ir.ui.menu,name:documents.share_menu
msgid "Shares & Emails"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Show all records which has next action date is before today"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "Size"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Some files could not be uploaded (max size: %s)."
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.not_available
msgid "Sorry, this link is no longer valid."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__link_model
msgid "Specific Model Linked"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "Split"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/attachments/attachment_viewer_patch.xml:0
msgid "Split PDF"
msgstr ""

#. module: documents
#: model:documents.facet,name:documents.documents_finance_status
#: model:documents.facet,name:documents.documents_internal_status
#: model:ir.model.fields,field_description:documents.field_documents_share__state
msgid "Status"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__children_folder_ids
msgid "Sub workspaces"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_summary
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_summary
msgid "Summary"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_tag
#: model:ir.model.fields,field_description:documents.field_documents_facet__tag_ids
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__tag_id
#: model:ir.model.fields,field_description:documents.field_mail_activity_type__tag_ids
#: model_terms:ir.ui.view,arch_db:documents.tag_view_search
msgid "Tag"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__facet_ids
#: model_terms:ir.ui.view,arch_db:documents.folder_view_tree
msgid "Tag Categories"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.facet_view_form
msgid "Tag Category"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Tag Name"
msgstr ""

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_tag_facet_name_unique
msgid "Tag already exists for this facet"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__facet_ids
msgid "Tag categories defined for this workspace"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
#: model:ir.actions.act_window,name:documents.facet_action
#: model:ir.model.fields,field_description:documents.field_documents_document__tag_ids
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__tag_ids
#: model:ir.ui.menu,name:documents.category_menu
#: model_terms:ir.ui.view,arch_db:documents.facet_view_form
#: model_terms:ir.ui.view,arch_db:documents.facet_view_tree
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Tags"
msgstr ""

#. module: documents
#: model:documents.facet,name:documents.documents_internal_template
msgid "Templates"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_internal_template_text
msgid "Text"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_facet__tooltip
msgid "Text shown when hovering on this tag category or its tags"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_model_id
msgid "The model (Odoo Document Kind) to which this alias corresponds. Any incoming email that does not reply to an existing record will cause the creation of a new record of this model (e.g. a Project Task)"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_name
msgid "The name of the email alias, e.g. 'jobs' if you want to catch emails for <<EMAIL>>"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_user_id
msgid "The owner of records created upon receiving emails on this alias. If this field is not set the system will attempt to find the right owner based on the sender (From) address, or will use the Administrator account if no system user is found for that address."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
#: code:addons/documents/static/src/views/inspector/documents_inspector.js:0
msgid "The share url has been copied to your clipboard."
msgstr ""

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_share_share_unique
msgid "This access token already exists"
msgstr ""

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_document_attachment_unique
msgid "This attachment is already a document"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__group_ids
msgid "This attachment will only be available for the selected user groups"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid ""
"This document has been requested.\n"
"                <b onclick=\"document.querySelector('.o_request_upload').click()\" style=\"cursor:pointer;\">Upload it</b>."
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__company_id
#: model:ir.model.fields,help:documents.field_documents_folder__company_id
msgid "This workspace will only be available to the selected company"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__thumbnail
msgid "Thumbnail"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__thumbnail_status
msgid "Thumbnail Status"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.xml:0
msgid "Tip: configure your scanner to send all documents to this address."
msgstr ""

#. module: documents
#: model:digest.tip,name:documents.digest_tip_documents_0
msgid "Tip: Become a paperless company"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_finance_status_tc
#: model:documents.tag,name:documents.documents_internal_status_tc
msgid "To Validate"
msgstr ""

#. module: documents
#: model:mail.activity.type,name:documents.mail_documents_activity_data_tv
msgid "To validate"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Today Activities"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "Toggle Dropdown"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_facet__tooltip
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__note
#: model_terms:ir.ui.view,arch_db:documents.facet_view_form
msgid "Tooltip"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__is_editable_attachment
msgid "True if we can edit the link attachment."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__type
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Type"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__url
#: model:ir.model.fields,field_description:documents.field_documents_share__full_url
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__url
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
#: model_terms:ir.ui.view,arch_db:documents.share_page
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid "URL"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "Un-archive"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Unlock"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "Unnamed"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "Upload"
msgstr ""

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.document_action
msgid "Upload <span class=\"fw-normal\">a file or</span> drag <span class=\"fw-normal\">it here.</span>"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "Upload Document"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__email_drop
msgid "Upload by Email"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Upload by email"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/mail_activity.py:0
msgid "Upload file request"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_res_users
#: model:res.groups,name:documents.group_documents_user
msgid "User"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_vat
msgid "VAT"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__date_deadline
msgid "Valid Until"
msgstr ""

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_finance_validate
msgid "Validate"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_finance_status_validated
#: model:documents.tag,name:documents.documents_internal_status_validated
msgid "Validated"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_marketing_assets_Videos
msgid "Videos"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Want to become a <b>paperless company</b>? Let's discover Odoo Documents."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__website_message_ids
#: model:ir.model.fields,field_description:documents.field_documents_share__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__website_message_ids
#: model:ir.model.fields,help:documents.field_documents_share__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__weeks
#: model:ir.model.fields.selection,name:documents.selection__documents_share__activity_date_deadline_range_type__weeks
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__activity_date_deadline_range_type__weeks
msgid "Weeks"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_folder_deletion_wizard_view_form
msgid "What do you want to do with the files in the workspace?"
msgstr ""

#. module: documents
#: model:ir.actions.act_window,name:documents.workflow_rule_action
msgid "Workflow Actions"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__workflow_rule_id
msgid "Workflow Rule"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_facet__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_share__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_tag__folder_id
#: model_terms:ir.ui.view,arch_db:documents.facet_view_search
#: model_terms:ir.ui.view,arch_db:documents.folder_view_tree
msgid "Workspace"
msgstr ""

#. module: documents
#: model:ir.actions.act_window,name:documents.folder_action
#: model:ir.ui.menu,name:documents.folder_menu
#: model_terms:ir.ui.view,arch_db:documents.folder_view_tree
msgid "Workspaces"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Wow... 6 documents processed in a few seconds, You're good.<br/>The tour is complete. Try uploading your own documents now."
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Write Access"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__group_ids
msgid "Write Groups"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Write a tooltip for the action here"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__attachment_type
msgid "You can either upload a file from your computer or copy/paste an internet link to your file."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You don't have the right to move documents to that workspace."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "You don't have the rights to move documents to that workspace"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_drop_zone.xml:0
msgid "You must be in a specific workspace to upload files"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "Youtube Video"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "delete"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "document"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "documents"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "documents selected"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "download"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "e.g. Discuss proposal"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "e.g. Finance"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "e.g. Missing Expense"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.facet_view_form
msgid "e.g. Status"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "e.g. To Validate"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "e.g. Validate document"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "e.g. https://www.youtube.com/watch?v=CP96yVnXNrY"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "fa fa-folder text-odoo"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "file_extension"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "folder_id"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__is_readonly_model
msgid "is_readonly_model"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "name"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "owner_id"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "partner_id"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "restore"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_page/pdf_page.xml:0
msgid "select"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "selected"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "shared by"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "status code: %s, message: %s"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "unnamed"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "url"
msgstr ""
