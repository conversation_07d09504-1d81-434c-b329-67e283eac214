# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# eriiikgt, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# jabiri7, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# Guspy12, 2024
# Jonatan Gk, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2025
# <PERSON>, 2025
# <PERSON><PERSON><PERSON>, 2025
# Wil <PERSON>, 2025
# <PERSON><PERSON><PERSON>, 2025
# <PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-26 20:44+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Santiago Payà <<EMAIL>>, 2025\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid " (%s locked)"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "%s (copy)"
msgstr "%s (còpia)"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "%s (remaining pages)"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "%s Files"
msgstr "%s Arxius"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "%s Folders"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/list/document_count_field.js:0
msgid "%s documents"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid "%s documents have been moved."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "%s file(s) not moved because they are locked by another user"
msgstr "%s fitxer(s) no moguts perquè estan bloquejats per un altre usuari"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.js:0
msgid "%s has been copied in My Drive."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.js:0
msgid "%s have been copied in My Drive."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "%s new document(s) created"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "%s page(s) deleted"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "%s shared with you"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid "%s shortcuts have been created."
msgstr ""

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid ""
". Scanned files will appear automatically in your workspace. Then, process "
"your documents in bulk with the split tool: launch user defined actions, "
"request a signature, convert to vendor bills with AI, etc."
msgstr ""
". Els fitxers explorats apareixeran automàticament a l'espai de treball. "
"Després, processeu els vostres documents en gran manera amb l'eina de "
"divisió: llançar accions definides per l'usuari, sol·licitar una signatura, "
"convertir a factures de venedor amb IA, etc."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.mail_template_document_share
msgid ".<br/>"
msgstr ".<br/>"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "1 File"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "1 Folder"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/list/document_count_field.js:0
msgid "1 document"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_tag_year_previous
msgid "2024"
msgstr "2024"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_year_current
msgid "2025"
msgstr "2025"

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid "<b class=\"tip_title\">Tip: Become a paperless company</b>"
msgstr ""
"<b class=\"tip_title\">Consell: Convertir-se en una empresa sense paper</b>"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "<b>Deselect this page</b> as we plan to process all bills first."
msgstr ""
"<b>Desselecciona aquesta pàgina</b>mentre pensem processar primer totes les "
"factures."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "<b>Select</b> this page to continue."
msgstr "<b>Seleccioneu</b> aquesta pàgina per a continuar."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "<br/>Powered by"
msgstr "<br/>Impulsat per"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "<i class=\"fa fa-download fa-fw\"/> Download All"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_file
msgid "<i class=\"fa fa-download fa-fw\"/> Download file"
msgstr "<i class=\"fa fa-download fa-fw\"/> Descarregar arxiu"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<i class=\"fa fa-external-link me-2\" title=\"This is a shortcut\"/>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<i class=\"fa fa-external-link text-gray-500\" title=\"This is a shortcut\"/>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_file
msgid "<i class=\"fa fa-eye\"/> Preview file"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "<i class=\"fa fa-lock oe_inline\" title=\"Locked\" invisible=\"not lock_uid\"/>"
msgstr "<i class=\"fa fa-lock oe_inline\" title=\"Locked\" invisible=\"not lock_uid\"/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "<i class=\"fa fa-upload\"/>  Upload"
msgstr "<i class=\"fa fa-upload\"/>  Carregar"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_file
msgid "<i class=\"fa fa-upload\"/> Replace file"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_document_res_partner_view
msgid "<span class=\"o_stat_text\">Documents</span>"
msgstr "<span class=\"o_stat_text\">Documents</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "<span class=\"o_stat_text\">Related <br/> Record</span>"
msgstr "<span class=\"o_stat_text\">Registre <br/> Relacionat</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "<span title=\"Requested Document\">Requested Document</span>"
msgstr "<span title=\"Requested Document\">Document sol·licitat</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "<span>&amp;nbsp;Documents.</span>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<span><b> Request</b></span>"
msgstr "<span><b> Petició</b></span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<span><b>Requested Document</b></span>"
msgstr "<span><b>Document sol·licitat</b></span>"

#. module: documents
#: model:mail.template,body_html:documents.mail_template_document_request
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"                    <tbody>\n"
"                        <!-- HEADER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\">\n"
"                                        <span style=\"font-size: 10px;\">\n"
"                                            Document Request: <br/>\n"
"                                            <t t-if=\"object.name\">\n"
"                                                <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Inbox Financial</span>\n"
"                                            </t>\n"
"                                        </span><br/>\n"
"                                    </td><td valign=\"middle\" align=\"right\" t-if=\"not object.create_uid.company_id.uses_default_logo\">\n"
"                                        <img t-attf-src=\"/logo.png?company={{ object.create_uid.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.create_uid.company_id.name\"/>\n"
"                                    </td></tr>\n"
"                                    <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- CONTENT -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                        <div>\n"
"                                            Hello <t t-out=\"object.owner_id.name or ''\">OdooBot</t>,\n"
"                                            <br/><br/>\n"
"                                            <t t-out=\"object.create_uid.name or ''\">OdooBot</t> (<t t-out=\"object.create_uid.email or ''\"><EMAIL></t>) asks you to provide the following document:\n"
"                                            <br/><br/>\n"
"                                            <center>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.name\">\n"
"                                                        <b t-out=\"object.name or ''\">Inbox Financial</b>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.request_activity_id.note\">\n"
"                                                        <i t-out=\"object.request_activity_id.note or ''\">Example of a note.</i>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <br/>\n"
"                                                <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                    <a t-att-href=\"object.access_url\" style=\"background-color: #875A7B; padding: 20px 30px 20px 30px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                        Upload the requested document\n"
"                                                    </a>\n"
"                                                </div>\n"
"                                            </center><br/>\n"
"                                            Please provide us with the missing document before <t t-out=\"object.request_activity_id.date_deadline\">2021-05-17</t>.\n"
"                                            <t t-if=\"user and user.signature\" data-o-mail-quote-container=\"1\">\n"
"                                                <br/>\n"
"                                                <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                                                <br data-o-mail-quote=\"1\"/>\n"
"                                            </t>\n"
"                                        </div>\n"
"                                    </td></tr>\n"
"                                    <tr><td style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- FOOTER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\" align=\"left\">\n"
"                                        <t t-out=\"object.create_uid.company_id.name or ''\">YourCompany</t>\n"
"                                    </td></tr>\n"
"                                    <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                                        <t t-out=\"object.create_uid.company_id.phone or ''\">******-123-4567</t>\n"
"                                        <t t-if=\"object.create_uid.company_id.email\">\n"
"                                            | <a t-attf-href=\"'mailto:%s' % {{ object.create_uid.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.email or ''\"><EMAIL></a>\n"
"                                        </t>\n"
"                                        <t t-if=\"object.create_uid.company_id.website\">\n"
"                                            | <a t-attf-href=\"'%s' % {{ object.create_uid.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.website or ''\">http://www.example.com</a>\n"
"                                        </t>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                    </table>\n"
"                    </td></tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                          <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"                            Powered by <a target=\"_blank\" href=\"https://www.odoo.com/app/documents\" style=\"color: #875A7B;\">Odoo Documents</a>\n"
"                          </td></tr>\n"
"                        </table>\n"
"                    </td></tr>\n"
"                </table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"                    <tbody>\n"
"                        <!-- HEADER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\">\n"
"                                        <span style=\"font-size: 10px;\">\n"
"                                            Sol·licitud de document: <br/>\n"
"                                            <t t-if=\"object.name\">\n"
"                                                <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Bústia d'entrada financera</span>\n"
"                                            </t>\n"
"                                        </span><br/>\n"
"                                    </td><td valign=\"middle\" align=\"right\" t-if=\"not object.create_uid.company_id.uses_default_logo\">\n"
"                                        <img t-attf-src=\"/logo.png?company={{ object.create_uid.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.create_uid.company_id.name\"/>\n"
"                                    </td></tr>\n"
"                                    <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- CONTENT -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                        <div>\n"
"                                            Hola, <t t-out=\"object.owner_id.name or ''\">OdooBot</t>:\n"
"                                            <br/><br/>\n"
"                                            <t t-out=\"object.create_uid.name or ''\">OdooBot</t> (<t t-out=\"object.create_uid.email or ''\"><EMAIL></t>) sol·licita que proporcioni el següent document:\n"
"                                            <br/><br/>\n"
"                                            <center>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.name\">\n"
"                                                        <b t-out=\"object.name or ''\">Bústia d'entada financera</b>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.request_activity_id.note\">\n"
"                                                        <i t-out=\"object.request_activity_id.note or ''\">Exemple d'una nota.</i>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <br/>\n"
"                                                <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                    <a t-att-href=\"object.access_url\" style=\"background-color: #875A7B; padding: 20px 30px 20px 30px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                        Carregui el document sol·licitat\n"
"                                                    </a>\n"
"                                                </div>\n"
"                                            </center><br/>\n"
"                                            Si us plau, proporcioni el document que falta abans del <t t-out=\"object.request_activity_id.date_deadline\">17-05-2021</t>.\n"
"                                            <t t-if=\"user and user.signature\" data-o-mail-quote-container=\"1\">\n"
"                                                <br/>\n"
"                                                <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                                                <br data-o-mail-quote=\"1\"/>\n"
"                                            </t>\n"
"                                        </div>\n"
"                                    </td></tr>\n"
"                                    <tr><td style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- FOOTER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\" align=\"left\">\n"
"                                        <t t-out=\"object.create_uid.company_id.name or ''\">LaSevaEmpresa</t>\n"
"                                    </td></tr>\n"
"                                    <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                                        <t t-out=\"object.create_uid.company_id.phone or ''\">******-123-4567</t>\n"
"                                        <t t-if=\"object.create_uid.company_id.email\">\n"
"                                            | <a t-attf-href=\"'mailto:%s' % {{ object.create_uid.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.email or ''\"><EMAIL></a>\n"
"                                        </t>\n"
"                                        <t t-if=\"object.create_uid.company_id.website\">\n"
"                                            | <a t-attf-href=\"'%s' % {{ object.create_uid.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.website or ''\">http://www.example.com</a>\n"
"                                        </t>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                    </table>\n"
"                    </td></tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                          <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"                            Desenvolupat per <a target=\"_blank\" href=\"https://www.odoo.com/app/documents\" style=\"color: #875A7B;\">Documents d'Odoo</a>\n"
"                          </td></tr>\n"
"                        </table>\n"
"                    </td></tr>\n"
"                </table>\n"
"            "

#. module: documents
#: model:mail.template,body_html:documents.mail_template_document_request_reminder
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"                    <tbody>\n"
"                        <!-- HEADER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\">\n"
"                                        <span style=\"font-size: 10px;\">\n"
"                                            Document Request: <br/>\n"
"                                            <t t-if=\"object.name\">\n"
"                                                <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Inbox Financial</span>\n"
"                                            </t>\n"
"                                        </span><br/>\n"
"                                    </td><td valign=\"middle\" align=\"right\">\n"
"                                        <img t-attf-src=\"/logo.png?company={{ object.create_uid.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.create_uid.company_id.name\"/>\n"
"                                    </td></tr>\n"
"                                    <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- CONTENT -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                        <div>\n"
"                                            Hello <t t-out=\"object.owner_id.name or ''\">OdooBot</t>,\n"
"                                            <br/><br/>\n"
"                                            This is a friendly reminder to upload your requested document:\n"
"                                            <br/><br/>\n"
"                                            <center>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.name\">\n"
"                                                        <b t-out=\"object.name or ''\">Inbox Financial</b>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.request_activity_id.note\">\n"
"                                                        <i t-out=\"object.request_activity_id.note or ''\">Example of a note.</i>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <br/>\n"
"                                                <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                    <a t-att-href=\"object.access_url\" style=\"background-color: #875A7B; padding: 20px 30px 20px 30px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                        Upload the requested document\n"
"                                                    </a>\n"
"                                                </div>\n"
"                                            </center><br/>\n"
"                                            Please provide us with the missing document before <t t-out=\"object.request_activity_id.date_deadline or ''\">2021-05-17</t>.\n"
"                                            <br/><br/>\n"
"                                            Thank you,\n"
"                                            <t t-if=\"user and user.signature\" data-o-mail-quote-container=\"1\">\n"
"                                                <br/>\n"
"                                                <t t-out=\"user.signature\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                                                <br data-o-mail-quote=\"1\"/>\n"
"                                            </t>\n"
"                                        </div>\n"
"                                    </td></tr>\n"
"                                    <tr><td style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- FOOTER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\" align=\"left\">\n"
"                                        <t t-out=\"object.create_uid.company_id.name or ''\">YourCompany</t>\n"
"                                    </td></tr>\n"
"                                    <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                                        <t t-out=\"object.create_uid.company_id.phone or ''\">******-123-4567</t>\n"
"                                        <t t-if=\"object.create_uid.company_id.email\">\n"
"                                            | <a t-attf-href=\"'mailto:%s' % {{ object.create_uid.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.email\"><EMAIL></a>\n"
"                                        </t>\n"
"                                        <t t-if=\"object.create_uid.company_id.website\">\n"
"                                            | <a t-att-href=\"object.create_uid.company_id.website\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.website\">http://www.example.com</a>\n"
"                                        </t>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                    </table>\n"
"                    </td></tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                          <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"                            Powered by <a target=\"_blank\" href=\"https://www.odoo.com/app/documents\" style=\"color: #875A7B;\">Odoo Documents</a>\n"
"                          </td></tr>\n"
"                        </table>\n"
"                    </td></tr>\n"
"                </table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"                    <tbody>\n"
"                        <!-- HEADER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\">\n"
"                                        <span style=\"font-size: 10px;\">\n"
"                                            Sol·licitud de document: <br/>\n"
"                                            <t t-if=\"object.name\">\n"
"                                                <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Bústia d'entrada financera</span>\n"
"                                            </t>\n"
"                                        </span><br/>\n"
"                                    </td><td valign=\"middle\" align=\"right\">\n"
"                                        <img t-attf-src=\"/logo.png?company={{ object.create_uid.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.create_uid.company_id.name\"/>\n"
"                                    </td></tr>\n"
"                                    <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- CONTENT -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                        <div>\n"
"                                            Hola, <t t-out=\"object.owner_id.name or ''\">OdooBot</t>:\n"
"                                            <br/><br/>\n"
"                                            Aquest és un recordatori perquè carregui el document sol·licitat:\n"
"                                            <br/><br/>\n"
"                                            <center>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.name\">\n"
"                                                        <b t-out=\"object.name or ''\">Bústia d'entada financera</b>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.request_activity_id.note\">\n"
"                                                        <i t-out=\"object.request_activity_id.note or ''\">Exemple d'una nota.</i>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <br/>\n"
"                                                <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                    <a t-att-href=\"object.access_url\" style=\"background-color: #875A7B; padding: 20px 30px 20px 30px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                        Carregui el document sol·licitat\n"
"                                                    </a>\n"
"                                                </div>\n"
"                                            </center><br/>\n"
"                                            Si us plau, proporcioni el document que falta abans del <t t-out=\"object.request_activity_id.date_deadline or ''\">17-05-2021</t>.\n"
"                                            <br/><br/>\n"
"                                            Gràcies,\n"
"                                            <t t-if=\"user and user.signature\" data-o-mail-quote-container=\"1\">\n"
"                                                <br/>\n"
"                                                <t t-out=\"user.signature\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                                                <br data-o-mail-quote=\"1\"/>\n"
"                                            </t>\n"
"                                        </div>\n"
"                                    </td></tr>\n"
"                                    <tr><td style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- FOOTER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\" align=\"left\">\n"
"                                        <t t-out=\"object.create_uid.company_id.name or ''\">LaSevaEmpresa</t>\n"
"                                    </td></tr>\n"
"                                    <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                                        <t t-out=\"object.create_uid.company_id.phone or ''\">******-123-4567</t>\n"
"                                        <t t-if=\"object.create_uid.company_id.email\">\n"
"                                            | <a t-attf-href=\"'mailto:%s' % {{ object.create_uid.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.email\"><EMAIL></a>\n"
"                                        </t>\n"
"                                        <t t-if=\"object.create_uid.company_id.website\">\n"
"                                            | <a t-att-href=\"object.create_uid.company_id.website\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.website\">http://www.example.com</a>\n"
"                                        </t>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                    </table>\n"
"                    </td></tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                          <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"                            Desenvolupat per <a target=\"_blank\" href=\"https://www.odoo.com/app/documents\" style=\"color: #875A7B;\">Documents d'Odoo</a>\n"
"                          </td></tr>\n"
"                        </table>\n"
"                    </td></tr>\n"
"                </table>\n"
"            "

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Diccionari Python que s'avaluarà per a proporcionar valors per defecte quan "
"es creïn nous registres per aquest pseudònim."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "A destination is required when creating multiple shortcuts at once."
msgstr ""

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_document_folder_id_not_id
msgid "A folder cannot be included in itself"
msgstr ""

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_document_shortcut_document_id_not_id
msgid "A shortcut cannot point to itself"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid "A shortcut has been created."
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Access"
msgstr "Accés"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "Access Error"
msgstr "Error d'accés"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__access_token
#: model:ir.model.fields,field_description:documents.field_documents_redirect__access_token
msgid "Access Token"
msgstr "Token d'accés"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.portal_my_home_documents
msgid "Access all your documents"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_access.py:0
msgid "Access documents and partners cannot be changed."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Access to Automations"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid "Access to Server Actions"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Access to a folder or a document"
msgstr ""

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_document_document_token_unique
msgid "Access tokens already used."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__access_url
msgid "Access url"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Action"
msgstr "Acció"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_needaction
msgid "Action Needed"
msgstr "Acció necessària"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_pin_actions.xml:0
msgid "Actions on Select"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__active
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_new_folder
msgid "Active"
msgstr "Actiu"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_ids
#: model:ir.ui.menu,name:documents.mail_activities
msgid "Activities"
msgstr "Activitats"

#. module: documents
#: model:ir.model,name:documents.model_mail_activity
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Activity"
msgstr "Activitat"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Activitat d'excepció de decoració"

#. module: documents
#: model:ir.ui.menu,name:documents.mail_activity_plan_menu
msgid "Activity Plans"
msgstr "Plans d'activitat"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_state
msgid "Activity State"
msgstr "Estat de l'activitat"

#. module: documents
#: model:ir.model,name:documents.model_mail_activity_type
msgid "Activity Type"
msgstr "Tipus d'activitat"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icona de tipus d'activitat"

#. module: documents
#: model:ir.actions.act_window,name:documents.mail_activity_type_action_document
#: model:ir.ui.menu,name:documents.mail_activity_type_menu
msgid "Activity Types"
msgstr "Tipus d'activitats"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "Activity assigned to"
msgstr ""

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.mail_activity_plan_action_document
msgid ""
"Activity plans are used to assign a list of activities in just a few clicks\n"
"                    (e.g. \"NDA signature process\", \"Workspace workflow\", ...)"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_type_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_type_id
msgid "Activity type"
msgstr "Tipus d'activitat"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "Add"
msgstr "Afegir"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_pin_actions.xml:0
msgid "Add Custom Action"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Add File"
msgstr "Afegir fitxer"

#. module: documents
#: model:ir.actions.act_window,name:documents.action_folder_form
msgid "Add Folder"
msgstr ""

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_tag_add_bill
msgid "Add Tag Bill"
msgstr ""

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_tag_add_validated
msgid "Add Tag Validated"
msgstr ""

#. module: documents
#: model:ir.actions.act_window,name:documents.action_url_form
msgid "Add Url"
msgstr "Afegir l'URL"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "Add a tag..."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "Add an alias tag..."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Add new file"
msgstr "Afegir un fitxer"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Add people or email addresses..."
msgstr "Afegeix persones o adreces de correu electrònic..."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Add people or email addresses:"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_shortcut.js:0
msgid "Add shortcut"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_star.js:0
msgid "Add star"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Additional documents you have access to."
msgstr ""

#. module: documents
#: model:res.groups,name:documents.group_documents_manager
msgid "Administrator"
msgstr "Administrador"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_ads
msgid "Ads"
msgstr "Anuncis"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_id
msgid "Alias"
msgstr "Pseudònim"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_contact
msgid "Alias Contact Security"
msgstr "Alias del contacte de seguretat"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_domain_id
msgid "Alias Domain"
msgstr "Àlies de domini"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_domain
msgid "Alias Domain Name"
msgstr "Nom del domini d'àlies"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_full_name
msgid "Alias Email"
msgstr "Pseudònim de correu"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_name
msgid "Alias Name"
msgstr "Pseudònim"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_status
msgid "Alias Status"
msgstr "Estat de l'àlies"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_tag_ids
msgid "Alias Tags"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_status
msgid "Alias status assessed on the last message received."
msgstr "Estat de l'àlies avaluat a l'últim missatge rebut."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_model_id
msgid "Aliased Model"
msgstr "Model amb pseudònim"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.js:0
msgid ""
"All users with access to this %(documentType)s or its parent will have edit "
"permissions."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__access_ids
msgid "Allowed Access"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Already linked Documents: %s"
msgstr ""

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid ""
"An easy way to process incoming mails is to configure your scanner to send "
"PDFs to"
msgstr ""
"Una manera fàcil de processar els correus entrants és configurar l'escàner "
"per enviar els PDF"

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid ""
"An easy way to process incoming mails is to configure your scanner to send "
"PDFs to your workspace email. Scanned files will appear automatically in "
"your workspace. Then, process your documents in bulk with the split tool: "
"launch user defined actions, request a signature, convert to vendor bills "
"with AI, etc."
msgstr ""
"Una manera fàcil de processar els correus entrants és configurar l'escàner "
"per enviar PDF al vostre correu electrònic de l'espai de treball. Els "
"fitxers explorats apareixeran automàticament a l'espai de treball. Després, "
"processeu els vostres documents en gran manera amb l'eina de divisió: "
"llançar accions definides per l'usuari, sol·licitar una signatura, convertir"
" a factures de venedor amb IA, etc."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "An error occured while uploading."
msgstr "S'ha produït un error en carregar."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.js:0
msgid "Anyone on the internet with the link can edit"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.js:0
msgid "Anyone on the internet with the link can view"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.xml:0
msgid "Anyone with the link"
msgstr "Qualsevol persona amb l'enllaç"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Archive original file(s)"
msgstr "Arxivar fitxer(s) original(s)"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Are you sure that you want to delete the focused page ?"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Are you sure that you want to delete the selected page(s)"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Are you sure that you want to delete this page ?"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.js:0
msgid "Are you sure you want to delete this attachment?"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Are you sure you want to permanently erase the document?"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Are you sure you want to permanently erase the documents?"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid ""
"As this PDF contains multiple documents, let's split and process in bulk."
msgstr ""
"Com aquest PDF conté diversos documents, ho dividirem i processar-ho en "
"bloc."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid ""
"At least one document couldn't be moved due to access rights. Shortcuts have"
" been created."
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_ir_attachment
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_id
msgid "Attachment"
msgstr "Adjunt"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_attachment_count
msgid "Attachment Count"
msgstr "Nombre d'adjunts"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__description
msgid "Attachment Description"
msgstr "Descripció de l'annex"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_name
msgid "Attachment Name"
msgstr "Nom del document adjunt"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_type
msgid "Attachment Type"
msgstr "Tipus d'adjunt"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_automations.js:0
msgid "Automations"
msgstr "Automatitzacions"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__available_embedded_actions_ids
msgid "Available Embedded Actions"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_tag_bill
msgid "Bill"
msgstr "Factura "

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Blank Page"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_tag_brochures
msgid "Brochures"
msgstr "Fulletons"

#. module: documents
#: model:ir.model.fields,help:documents.field_mail_activity_type__folder_id
msgid "By defining a folder, the upload activities will generate a document"
msgstr ""
"En definir una carpeta, les activitats de pujada generaran un document"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_link_to_record_form_view
msgid "Cancel"
msgstr "Cancel·la"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__checksum
msgid "Checksum/SHA1"
msgstr "Suma verificació/SHA1"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__children_ids
msgid "Children"
msgstr "Fills"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Choose a record to link"
msgstr "Trieu un registre per enllaçar"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_details
msgid "Choose or Configure Email Servers"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Click on a card to <b>select the document</b>."
msgstr "Fes clic en una targeta per <b>seleccionar el document</b>."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Click on a thumbnail to <b>preview the document</b>."
msgstr "Feu clic en una miniatura a<b>previsualizar el document</b>."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid ""
"Click on the <b>page separator</b>: we don't want to split these two pages "
"as they belong to the same document."
msgstr ""
"Feu clic a <b>separador de pàgina</b>: No volem dividir aquestes dues "
"pàgines perquè pertanyen al mateix document."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Click the cross to <b>exit preview</b>."
msgstr "Cliqueu a la creu per a <b>sortir de la previsualització</b>."

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__thumbnail_status__client_generated
msgid "Client Generated"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.xml:0
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Close"
msgstr "Tancar"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Close split tools"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_tag__color
msgid "Color"
msgstr "Color"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Common roots for all company users."
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_res_company
msgid "Companies"
msgstr "Empreses"

#. module: documents
#. odoo-javascript
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.js:0
#: code:addons/documents/static/src/views/kanban/documents_kanban_record.js:0
#: model:ir.model.fields,field_description:documents.field_documents_document__company_id
msgid "Company"
msgstr "Empresa"

#. module: documents
#: model:ir.model,name:documents.model_res_config_settings
msgid "Config Settings"
msgstr "Paràmetres de configuració"

#. module: documents
#: model:ir.ui.menu,name:documents.Config
msgid "Configuration"
msgstr "Configuració"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model:ir.model,name:documents.model_res_partner
#: model:ir.model.fields,field_description:documents.field_documents_document__partner_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__partner_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Contact"
msgstr "Contacte"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Contact your Administrator to get access if needed."
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_tag_contracts
msgid "Contracts"
msgstr "Contractes"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "Control panel buttons"
msgstr "Botons del panel de control"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.js:0
msgid "Copy Link"
msgstr "Copiar enllaç"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Copy Links"
msgstr ""

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_create_activity
msgid "Create Activity"
msgstr "Crear activitat"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Create Shortcut"
msgstr ""

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.mail_activity_plan_action_document
msgid "Create a Document Activity Plan"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_option
msgid "Create a new activity"
msgstr "Crea una activitat nova"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Create and edit new contact \""
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Create new contact \""
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_tag__create_uid
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Created by"
msgstr "Creat per"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_date
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__create_date
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__create_date
#: model:ir.model.fields,field_description:documents.field_documents_tag__create_date
msgid "Created on"
msgstr "Creat el"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Creation Date"
msgstr "Data de creació"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.xml:0
msgid "Current Version"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Missatge personalitzat de rebot"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__create_activity_date_deadline_range_type__days
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__days
msgid "Days"
msgstr "Dies"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_defaults
msgid "Default Values"
msgstr "Valors per defecte"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__deletion_delay
#: model:ir.model.fields,help:documents.field_res_config_settings__deletion_delay
msgid "Delay after permanent deletion of the document in the trash (days)"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.xml:0
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
#: code:addons/documents/static/src/views/list/documents_list_controller.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Delete"
msgstr "Eliminar"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Delete focused or selected pages"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Delete permanently"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_res_config_settings__deletion_delay
msgid "Deletion Delay"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__deletion_delay
msgid "Deletion delay"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Deletion delay (days)"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_tag_deprecated
msgid "Deprecated"
msgstr "Obsolet"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
#: code:addons/documents/static/src/views/hooks.js:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_rename
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
#: model_terms:ir.ui.view,arch_db:documents.folder_deletion_form
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Discard"
msgstr "Descartar"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Discoverable"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_access__display_name
#: model:ir.model.fields,field_description:documents.field_documents_document__display_name
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__display_name
#: model:ir.model.fields,field_description:documents.field_documents_redirect__display_name
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__display_name
#: model:ir.model.fields,field_description:documents.field_documents_tag__display_name
msgid "Display Name"
msgstr "Nom mostrat"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "Do you want to exit without saving or gather pages into one document ?"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_document
#: model:ir.model.fields,field_description:documents.field_documents_access__document_id
#: model:ir.model.fields,field_description:documents.field_documents_redirect__document_id
#: model:ir.model.fields,field_description:documents.field_documents_tag__document_ids
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "Document"
msgstr "Document"

#. module: documents
#: model:ir.model,name:documents.model_documents_access
msgid "Document / Partner"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.xml:0
msgid "Document Access Internal"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.xml:0
msgid "Document Access Link"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__document_count
#: model:ir.model.fields,field_description:documents.field_res_partner__document_count
msgid "Document Count"
msgstr "Recompte de documents"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Document Name"
msgstr "Nom del document"

#. module: documents
#: model:ir.actions.act_window,name:documents.mail_activity_plan_action_document
msgid "Document Plans"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_redirect
msgid "Document Redirect"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_request_wizard
msgid "Document Request"
msgstr "Sol·licitud de document"

#. module: documents
#: model:mail.template,subject:documents.mail_template_document_request
msgid ""
"Document Request {{ object.name != False and ': '+ object.name or '' }}"
msgstr ""
"Sol·licitud de document {{ object.name != False and ': '+ object.name or '' "
"}}"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: code:addons/documents/models/mail_activity.py:0
msgid "Document Request: %(name)s Uploaded by: %(user)s"
msgstr "Sol·licitud de document: %(name)sPujat per: %(user)s"

#. module: documents
#: model:mail.template,name:documents.mail_template_document_request_reminder
msgid "Document Request: Reminder"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__document_token
msgid "Document Token"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "Document preview"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/controllers/documents.py:0
msgid "Document uploaded by %(user)s"
msgstr ""

#. module: documents
#: model:mail.template,name:documents.mail_template_document_request
msgid "Document: Document Request"
msgstr "Document: Sol·licitud de document"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/list/document_count_field.js:0
msgid "DocumentCountIntegerField"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: code:addons/documents/models/res_partner.py:0
#: model:ir.actions.act_window,name:documents.document_action
#: model:ir.actions.act_window,name:documents.document_action_portal
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__document_ids
#: model:ir.model.fields,field_description:documents.field_res_users__document_count
#: model:ir.ui.menu,name:documents.dashboard
#: model:ir.ui.menu,name:documents.menu_root
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.documents_view_activity
#: model_terms:ir.ui.view,arch_db:documents.portal_my_home_documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Documents"
msgstr "Documents"

#. module: documents
#: model:ir.model,name:documents.model_documents_link_to_record_wizard
msgid "Documents Link to Record"
msgstr "Documents enllaç a registre"

#. module: documents
#: model:ir.model,name:documents.model_documents_mixin
msgid "Documents creation mixin"
msgstr "Mescla de creació de documents"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "Documents in the Trash cannot be shared"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.js:0
msgid "Documents moved to trash will show up here"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.js:0
msgid "Documents shared with you will appear here"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_unlink_mixin
msgid "Documents unlink mixin"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.xml:0
msgid "Done"
msgstr "Fet"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "Done!"
msgstr "Fet"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.xml:0
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_download.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Download"
msgstr "Descarregar"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Download #{document.name}"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Download all files"
msgstr "Descarregar tots els arxius"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_file
msgid "Download file"
msgstr "Descarregar arxiu"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Download folder as zip"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Download zip #{subfolder.name}"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_tag_draft
msgid "Draft"
msgstr "Esborrany"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_drop_zone.xml:0
msgid "Drop files here to upload"
msgstr "Deixi anar els arxius aquí per a carregar-los"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_date_deadline_range
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_date_deadline_range
msgid "Due Date In"
msgstr "Data de venciment en"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_date_deadline_range_type
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_date_deadline_range_type
msgid "Due type"
msgstr "Tipus de venciment"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Duplicate"
msgstr "Duplicar"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
#: code:addons/documents/static/src/views/search/documents_search_panel.xml:0
msgid "Edit"
msgstr "Modificar"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: model:ir.model.fields.selection,name:documents.selection__documents_access__role__edit
#: model:ir.model.fields.selection,name:documents.selection__documents_document__access_internal__edit
#: model:ir.model.fields.selection,name:documents.selection__documents_document__access_via_link__edit
#: model:ir.model.fields.selection,name:documents.selection__documents_document__user_permission__edit
msgid "Editor"
msgstr "Editor "

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "Email"
msgstr "Correu electrònic"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_email
msgid "Email Alias"
msgstr "Àlies de correu electrònic"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_details
msgid "Email Upload"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__email_cc
msgid "Email cc"
msgstr "cc de correu electrònic"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "Domini de correu electrònic p.ex. 'example.com' a '<EMAIL>'"

#. module: documents
#: model:ir.actions.act_window,name:documents.mail_alias_action
msgid "Email links"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#: code:addons/documents/static/src/views/hooks.js:0
#: model:ir.model.fields.selection,name:documents.selection__documents_document__thumbnail_status__error
msgid "Error"
msgstr "Error"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Escape Preview/Deselect/Exit"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "Exit Split Tools"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "Exit without saving"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_partner_access.xml:0
msgid "Exp:"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_tag_expense
msgid "Expense"
msgstr "Despesa"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_access__expiration_date
msgid "Expiration"
msgstr "Caducitat"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/list/documents_list_controller.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Export"
msgstr "Exportar"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Extra comments..."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__favorited_ids
msgid "Favorite of"
msgstr "Preferit de"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_type_icon/documents_type_icon.xml:0
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__binary
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "File"
msgstr "Fitxer"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__datas
msgid "File Content (base64)"
msgstr "Contingut del fitxer (base64)"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__raw
msgid "File Content (raw)"
msgstr "Contingut del fitxer (raw)"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__file_extension
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "File Extension"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__file_size
msgid "File Size"
msgstr "Mida del fitxer"

#. module: documents
#: model:ir.model,name:documents.model_ir_binary
msgid "File streaming helper model for controllers"
msgstr "Model d'ajuda de transmissió de fitxers per a controladors"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/kanban/documents_kanban_renderer.xml:0
msgid "Files"
msgstr "Fitxers"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Files Centralization"
msgstr "Centralització dels fitxers"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_deletion_form
msgid "Files will be sent to trash and deleted forever after"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
msgid "Files will be sent to trash and deleted forever after %s days."
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_tag_financial
msgid "Financial"
msgstr "Financer"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_fiscal
msgid "Fiscal"
msgstr "Fiscal"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Focus first page of next group"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Focus first page of previous group"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Focus next page"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Focus previous page"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
#: code:addons/documents/static/src/views/documents_type_icon/documents_type_icon.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_document__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__folder_id
#: model:ir.model.fields,field_description:documents.field_mail_activity_type__folder_id
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__folder
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "Folder"
msgstr "Carpeta"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/kanban/documents_kanban_renderer.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Folders"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_follower_ids
msgid "Followers"
msgstr "Seguidors"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidors (Partners)"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icona Font Awesome p.e. fa-tasks"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "Gather in one document"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_details
msgid "General"
msgstr "General"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.xml:0
msgid "General access"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "GiB"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Group By"
msgstr "Agrupar per"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_hr
msgid "HR"
msgstr "HR"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__has_message
msgid "Has Message"
msgstr "Té un missatge"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Hide shortcuts"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__previous_attachment_ids
msgid "History"
msgstr "Historial"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_access__id
#: model:ir.model.fields,field_description:documents.field_documents_document__id
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__id
#: model:ir.model.fields,field_description:documents.field_documents_redirect__id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__id
#: model:ir.model.fields,field_description:documents.field_documents_tag__id
msgid "ID"
msgstr "ID"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"Id del registre pare que te el pseudònim.(exemple: el projecte que conté el "
"pseudònim per la creació de tasques)"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_exception_icon
msgid "Icon"
msgstr "Icona"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icona que indica una activitat d'excepció."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__is_access_via_link_hidden
msgid ""
"If \"True\", only people given direct access to this document will be able "
"to view it. If \"False\", access with the link also given to all who can "
"access the parent folder."
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""
"Si està marcat, hi ha nous missatges que requereixen la vostra atenció."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_has_error
#: model:ir.model.fields,help:documents.field_documents_document__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si està marcat, alguns missatges tenen un error d'entrega."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Si està seleccionat, aquest contingut s' enviarà automàticament als usuaris "
"no autoritzats enlloc del missatge per omissió."

#. module: documents
#: model:documents.tag,name:documents.documents_tag_images
msgid "Images"
msgstr "Imatges"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Impossible to delete folders used by other applications."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Impossible to invite partners on multiple documents at once."
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "In"
msgstr "Dins"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__thumbnail_status__restricted
msgid "Inaccessible"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_tag_inbox
#: model:mail.activity.type,name:documents.mail_documents_activity_data_Inbox
msgid "Inbox"
msgstr "Bústia d'entrada"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"Incorrect values. Use one of the following for the following fields: "
"%(hints)s.)"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__index_content
msgid "Indexed Content"
msgstr "Contingut indexat"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_details.js:0
msgid "Info & Tags"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Info & tags"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.xml:0
msgid "Internal Users"
msgstr "Usuaris interns "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__access_internal
msgid "Internal Users Rights"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.js:0
msgid "Internal users can edit"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.js:0
msgid "Internal users can view"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Invalid folder id"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "Invalid operation"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Invalid role."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_editable_attachment
msgid "Is Editable Attachment"
msgstr "És un annex editable"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_favorited
msgid "Is Favorited"
msgstr "Es Favorit"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_is_follower
msgid "Is Follower"
msgstr "És un seguidor"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_multipage
msgid "Is considered multipage"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"It is not possible to move archived documents or documents to archived "
"folders."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"Item(s) you wish to restore are included in archived folders. To restore these items, you must restore the following including folders instead:\n"
"- %(folders_list)s"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Items in trash will be deleted forever after %s days."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Items moved to the trash will be deleted forever after %s days."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_access__last_access_date
#: model:ir.model.fields,field_description:documents.field_documents_document__last_access_date_group
msgid "Last Accessed On"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_tag__write_uid
msgid "Last Updated by"
msgstr "Última actualització per"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__write_date
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__write_date
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__write_date
#: model:ir.model.fields,field_description:documents.field_documents_tag__write_date
msgid "Last Updated on"
msgstr "Última actualització el"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_legal
msgid "Legal"
msgstr "Legal"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid ""
"Let's process documents in your Inbox.<br/><i>Tip: Use Tags to filter "
"documents and structure your process.</i>"
msgstr ""
"Processem els documents a la vostra bústia d'entrada.<br/><i>Consell: "
"Utilitza les etiquetes per filtrar documents i estructurar el vostre "
"procés.</i>"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Let's process these bills: send to Finance workspace."
msgstr ""
"Procedim a aquestes factures: enviar a l'espai de treball de finances."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Let's process this document, coming from our scanner."
msgstr "Processem aquest document, que ve del nostre escàner."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid ""
"Let's tag this mail as legal<br/> <i>Tips: actions can be tailored to your "
"process, according to the workspace.</i>"
msgstr ""
"Etiquetarem aquest correu com a legal<br/> <i>Consells: les accions es poden"
" adaptar al vostre procés, d'acord amb l'espai de treball.</i>"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
#: code:addons/documents/static/src/views/documents_type_icon/documents_type_icon.xml:0
#: model_terms:ir.ui.view,arch_db:documents.documents_link_to_record_form_view
msgid "Link"
msgstr "Enllaç"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_access_via_link_hidden
msgid "Link Access Hidden"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__access_via_link
msgid "Link Access Rights"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__url
msgid "Link URL"
msgstr "URL de l'enllaç"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.js:0
msgid "Link copied to clipboard!"
msgstr "Enllaç copiat al porta-retalls!"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.xml:0
msgid "Link copied to the clipboard."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "Linked To"
msgstr "Enllaçat a"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.js:0
msgid "Links copied to clipboard!"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "Detecció d'entrada basada en la part local"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Lock"
msgstr "Bloqueja"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_document__is_locked
msgid "Locked"
msgstr "Bloquejat"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__lock_uid
msgid "Locked by"
msgstr "Bloquejat per"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Log a note..."
msgstr "Registrar una nota"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Login"
msgstr "Iniciar sessió"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Logout"
msgstr "Tancar sessió"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Mail: %s"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Manage Versions"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.js:0
msgid "Manage Versions of \"%s\""
msgstr ""

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_remove_activities
msgid "Mark activities as completed"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Merge PDFs"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_note
msgid "Message"
msgstr "Missatge"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_error
msgid "Message Delivery error"
msgstr "Error d'entrega del missatge"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_ids
msgid "Messages"
msgstr "Missatges"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "MiB"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__mimetype
msgid "Mime Type"
msgstr "Tipus MIME"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Missing documents reference."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__model_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Model"
msgstr "Model"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__accessible_model_ids
msgid "Models"
msgstr "Models"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__create_activity_date_deadline_range_type__months
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__months
msgid "Months"
msgstr "Mesos"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "More"
msgstr "Més"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Move this page to trash"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Move to Trash"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_archive.js:0
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
#: code:addons/documents/static/src/views/hooks.js:0
#: model_terms:ir.ui.view,arch_db:documents.folder_deletion_form
msgid "Move to trash"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
msgid "Move to trash?"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Moved to trash"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "Must have the link to access"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "My Activities"
msgstr "Les meves activitats"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Venciment de l'activitat"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "My Documents"
msgstr "Els meus documents"

#. module: documents
#. odoo-javascript
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.js:0
#: code:addons/documents/static/src/views/kanban/documents_kanban_record.js:0
msgid "My Drive"
msgstr ""

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_access_role_or_last_access_date
msgid "NULL roles must have a set last_access_date"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__name
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__name
#: model:ir.model.fields,field_description:documents.field_documents_tag__name
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Name"
msgstr "Nom"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Name / Extension"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "New"
msgstr "Nou"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "New File"
msgstr "Nou Arxiu"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.xml:0
msgid "New Folder"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "New Group"
msgstr "Grup Nou"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Proper esdeveniment del calendari d'activitats"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Data límit de la següent activitat"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_summary
msgid "Next Activity Summary"
msgstr "Resum de la següent activitat"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_type_id
msgid "Next Activity Type"
msgstr "Tipus de la següent activitat"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "No activity"
msgstr "No activitat"

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.mail_activity_type_action_document
msgid "No activity types found. Let's create one!"
msgstr "No s'ha trobat cap tipus d'activitat. Creem-ne un!"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.xml:0
msgid "No alias configured"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "No document has been selected"
msgstr "No s'ha seleccionat cap document"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/chatter/documents_chatter_overlay.xml:0
msgid "No item selected"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.js:0
msgid "No one on the internet can access"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/ir_actions_server.py:0
msgid "No server actions found for Documents!"
msgstr ""

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.mail_alias_action
msgid "No shared links"
msgstr "No hi ha enllaços compartits"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__access_internal__none
#: model:ir.model.fields.selection,name:documents.selection__documents_document__access_via_link__none
#: model:ir.model.fields.selection,name:documents.selection__documents_document__user_permission__none
msgid "None"
msgstr "Cap"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Not a file"
msgstr "No és un Arxiu"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Not a folder."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Not attached"
msgstr "No adjuntat"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "Not set"
msgstr "No establert"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_note
msgid "Note"
msgstr "Nota"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Notify"
msgstr "Notifica"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'accions"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'errors"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Nombre de missatges que requereixen una acció"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de missatges amb error d'entrega"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Odoo Logo"
msgstr "Logo Odoo"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Odoo Studio - Customize workflows in minutes"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Odoo Website"
msgstr "Lloc web d'Odoo"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__last_access_date_group__0_older
msgid "Older"
msgstr "Més antiga"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Only Documents Managers can create in company folder."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Only Documents Managers can set aliases."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Only Documents managers can set an alias."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.js:0
msgid "Only people with access can open with the link"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Open #{document_url.name}"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Open folder #{subfolder.name}"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "Open the permissions panel"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"Operation not supported. Please use \"Move to Trash\" / `action_archive` "
"instead."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"Operation not supported. Please use \"Restore\" / `action_unarchive` "
"instead."
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"Id opcional d'un fil (registre) al que s'adjuntaran tots els missatges "
"entrants, inclús si no en son respostes. Si s'estableix, es desactivarà "
"completament la creació de nous registres."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.js:0
msgid "Optional message..."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.xml:0
msgid "Or send emails to"
msgstr "O enviar correus electrònics a"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_other
msgid "Other"
msgstr "Altres"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: code:addons/documents/static/src/components/documents_permission_panel/documents_partner_access.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_document__owner_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__requestee_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Owner"
msgstr "Propietari"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Owner: #{document.owner_id.name}"
msgstr "Propietari: #{document.owner_id.name}"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Owner: #{document_url.owner_id.name}"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Owner: #{subfolder.owner_id.name}"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_parent_model_id
msgid "Parent Model"
msgstr "Model pare"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__parent_path
msgid "Parent Path"
msgstr "Ruta principal"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "Id del registre pare"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"El model pare conte el pseudònim. El model que conte la referència del "
"pseudònim no és necessàriament el model que es dóna a través del camp "
"alias_model_id (exemple: projecto (parent_model) i tasca (model))"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "Partial transfer"
msgstr "Transferència parcial"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_access__partner_id
msgid "Partner"
msgstr "Empresa"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_partner_access.xml:0
msgid "People with access"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_pinned_folder
msgid "Pinned to Company roots"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.not_available
msgid ""
"Please login or contact the person that shared this link for more "
"information."
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Política per enviar un missatge en el document utilitzant la passarel·la del correu.\n"
"-Tots: Tothom pot publicar\n"
"-Empreses: Només les empreses autenticades\n"
"-Seguidors: Només els seguidors del document relacionat o els membres dels canals següents\n"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__thumbnail_status__present
msgid "Present"
msgstr "Present"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_presentations
msgid "Presentations"
msgstr "Presentacions"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_file
msgid "Preview files"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_tag_project
msgid "Project"
msgstr "Projecte"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Put in %s"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__rating_ids
msgid "Ratings"
msgstr "Valoracions"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Recent"
msgstr "Recent"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.js:0
msgid "Recently accessed Documents will show up here"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Recently accessed documents."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__resource_ref
msgid "Record"
msgstr "Registre "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_force_thread_id
msgid "Record Thread ID"
msgstr "Id del registre del fil"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Remaining Pages"
msgstr "Pàgines restants"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/activity/documents_activity_controller.js:0
msgid "Reminder emails have been sent."
msgstr ""

#. module: documents
#: model:mail.template,subject:documents.mail_template_document_request_reminder
msgid ""
"Reminder to upload your document{{ object.name and ' : ' + object.name or ''"
" }}"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_remove_partner_button.xml:0
msgid "Remove Access"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_remove_partner_button.xml:0
msgid "Remove Member"
msgstr "Elimina el membre"

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_tag_remove_inbox
msgid "Remove Tag Inbox"
msgstr ""

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_tag_remove_to_validate
msgid "Remove Tag To Validate"
msgstr ""

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_remove_tags
msgid "Remove all tags"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_star.js:0
msgid "Remove star"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_rename.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_rename
msgid "Rename"
msgstr "Canvia el nom"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Request"
msgstr "Sol·licitud"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__request_activity_id
msgid "Request Activity"
msgstr "Sol·licitud d'activitat"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Request To"
msgstr "Sol·licitud a"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/web/activity/activity_menu_patch.xml:0
msgid "Request a Document"
msgstr "Sol·licitar un document"

#. module: documents
#: model:ir.actions.act_window,name:documents.action_request_form
msgid "Request a file"
msgstr "Solicitar un fitxer"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_request_page
msgid ""
"Requested\n"
"                                ∙"
msgstr ""

#. module: documents
#: model:mail.activity.type,name:documents.mail_documents_activity_data_md
msgid "Requested Document"
msgstr "Document sol·licitat"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Requested Documents"
msgstr "Documents sol·licitats"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__requestee_partner_id
msgid "Requestee Partner"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_model_name
msgid "Res Model Name"
msgstr "Nom del model Res"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__res_id
msgid "Resource ID"
msgstr "ID del recurs "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_model
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__res_model
msgid "Resource Model"
msgstr "Model del Recurs"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_name
msgid "Resource Name"
msgstr "Nom del Recurs"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_user_id
msgid "Responsible"
msgstr "Responsable"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_user_id
msgid "Responsible User"
msgstr "Usuari responsable"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Restore"
msgstr "Restaurar"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Restricted"
msgstr "Restringit"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Restricted Folder"
msgstr "Carpeta amb accés restringit"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.xml:0
msgid "Revert changes"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_access__role
msgid "Role"
msgstr "Rol"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error de lliurament SMS"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_sales
msgid "Sales"
msgstr "Vendes"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Save"
msgstr "Desar"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.js:0
msgid "Save or discard changes first"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.xml:0
msgid "Saving..."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Search people"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.js:0
msgid "Select a folder to upload a document"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/kanban/documents_kanban_renderer.js:0
#: code:addons/documents/static/src/views/list/documents_list_renderer.js:0
msgid "Select all"
msgstr "Selecciona tot"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select focused page"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select next page"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select next pages of the group"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select previous page"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select previous pages of the group"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select/Deselect all pages"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Send"
msgstr "Enviar"

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_send_to_finance
msgid "Send To Finance"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Send this letter to the legal department, by assigning the right tags."
msgstr ""
"Envia aquesta carta al departament legal, assignant les etiquetes correctes."

#. module: documents
#: model:mail.template,description:documents.mail_template_document_request
msgid "Sent to partner when requesting a document from them"
msgstr "Enviat a un company quan se li demana un document"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_tag__sequence
msgid "Sequence"
msgstr "Seqüència"

#. module: documents
#: model:ir.model,name:documents.model_ir_actions_server
msgid "Server Action"
msgstr "Acció del Servidor "

#. module: documents
#. odoo-python
#: code:addons/documents/models/ir_actions_server.py:0
msgid "Server Actions"
msgstr "Accions del servidor"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Set deletion delay for documents in the Trash"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_expiration_date_btn.xml:0
msgid "Set expiration date"
msgstr ""

#. module: documents
#: model:mail.template,description:documents.mail_template_document_request_reminder
msgid ""
"Set reminders in activities to notify users who didn't upload their "
"requested document"
msgstr ""

#. module: documents
#: model:ir.actions.act_window,name:documents.configuration_action
#: model:ir.actions.act_window,name:documents.settings_action
#: model:ir.ui.menu,name:documents.settings_menu
msgid "Settings"
msgstr "Configuració"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_share.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
#: code:addons/documents/static/src/views/search/documents_search_panel.xml:0
#: code:addons/documents/static/src/web/error_dialog/error_dialog_patch.xml:0
msgid "Share"
msgstr "Compartir"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.js:0
msgid "Share: %(documentName)s"
msgstr ""

#. module: documents
#. odoo-javascript
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.js:0
#: code:addons/documents/static/src/views/kanban/documents_kanban_record.js:0
msgid "Shared with me"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Sharing..."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__shortcut_ids
msgid "Shortcut"
msgstr "Abreviació"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "Shortcut created"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.js:0
msgid "Shortcuts can only be created one at a time."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Shortcuts cannot change target document."
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_file
msgid "Size:"
msgstr "Mida:"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid "Some files could not be uploaded (max size: %s)."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__shortcut_document_id
msgid "Source Document"
msgstr "Document d'origen"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "Special folders cannot be shared"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Split"
msgstr "Divideix"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/attachments/document_file_viewer.xml:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Split PDF"
msgstr "Divideix el PDF"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Split all white pages"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Split selected pages"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Starred"
msgstr "Destacat"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estat basat en activitats\n"
"Vençuda: La data límit ja ha passat\n"
"Avui: La data de l'activitat és avui\n"
"Planificat: Activitats futures."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "Stay here"
msgstr "Queda't aquí"

#. module: documents
#: model:ir.ui.menu,name:documents.structure
msgid "Structure"
msgstr "Estructura"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_summary
msgid "Summary"
msgstr "Resum"

#. module: documents
#: model:res.groups,name:documents.group_documents_system
msgid "System Administrator"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_tag
#: model:ir.model.fields,field_description:documents.field_mail_activity_type__tag_ids
#: model_terms:ir.ui.view,arch_db:documents.tag_view_search
msgid "Tag"
msgstr "Etiqueta"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Tag Name"
msgstr "Nom de l'etiqueta"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_tag_tag_name_unique
msgid "Tag name already used"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model:ir.actions.act_window,name:documents.tag_action
#: model:ir.model.fields,field_description:documents.field_documents_document__tag_ids
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__tag_ids
#: model:ir.ui.menu,name:documents.category_menu
msgid "Tags"
msgstr "Etiquetes"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_text
msgid "Text"
msgstr "Text"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_tag__tooltip
msgid "Text shown when hovering on this tag"
msgstr ""

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_res_config_settings_check_deletion_delay
msgid "The deletion delay should be positive."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/web/error_dialog/error_dialog_patch.js:0
#: code:addons/documents/static/src/web/error_dialog/error_dialog_patch.xml:0
msgid "The document URL has been copied to your clipboard."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid "The document has been moved."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"The following documents can't have alias: \n"
"- %(records)s"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"The following documents/folders can't be owned by a Portal User: \n"
"- %(partners)s"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "The following documents/shortcuts have a company mismatch: \n"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "The following documents/shortcuts have a type mismatch: \n"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "The following shortcuts cannot be set as documents parents: \n"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"El model (Tipus de document d'Odoo) al que correspon aquest pseudònim. "
"Qualsevol correu electrònic entrant que no sigui resposta a un registre "
"existent, causarà la creació d'un nou registre d'aquest model (per exemple, "
"una tasca de projecte)"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"El nom del pseudònim de correu electrònic, per exemple, \"jobs\", si voleu "
"obtenir els correus mitjançant <<EMAIL>>"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__last_access_date_group__1_month
msgid "This Month"
msgstr "Aquest mes"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__last_access_date_group__2_week
msgid "This Week"
msgstr "Aquesta setmana"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "This action does not exist."
msgstr ""

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_document_attachment_unique
msgid "This attachment is already a document"
msgstr "Aquest annex ja és un document"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.not_available
msgid "This document does not exist or is not publicly available."
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_request_page
msgid ""
"This document has been requested.\n"
"                <b onclick=\"document.querySelector('.o_request_upload').click()\" style=\"cursor:pointer;\">Upload it</b>."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "This document has been restored."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"This file has been sent to the trash and will be deleted forever on the %s"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "This folder does not exist or is not accessible."
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "This is a folder"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.xml:0
msgid "This is a shortcut"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "This is a shortcut - Click to access source document"
msgstr ""

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_access_unique_document_access_partner
msgid "This partner is already set on this document."
msgstr ""

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.mail_activity_type_action_document
msgid ""
"Those represent the different categories of things you have to do (e.g. "
"\"Call\" or \"Send email\")."
msgstr ""
"Aquestes representen les diferents categories de coses que heu de fer (p. "
"ex. «Call» o «Envia un correu electrònic»)."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__thumbnail
msgid "Thumbnail"
msgstr "Miniatura"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__thumbnail_status
msgid "Thumbnail Status"
msgstr "Estat de les miniatures"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.xml:0
msgid "Tip: configure your scanner to send all documents to this address."
msgstr ""
"Consell: configurar l'escàner per enviar tots els documents a aquesta "
"adreça."

#. module: documents
#: model:digest.tip,name:documents.digest_tip_documents_0
msgid "Tip: Become a paperless company"
msgstr "Consell: Convertir-se en una empresa sense paper"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_to_validate
msgid "To Validate"
msgstr "Per a validar"

#. module: documents
#: model:mail.activity.type,name:documents.mail_documents_activity_data_tv
msgid "To validate"
msgstr "Per validar"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__last_access_date_group__3_day
msgid "Today"
msgstr "Avui"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/kanban/documents_kanban_renderer.js:0
msgid "Toggle favorite"
msgstr "Commuta com a preferit"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_tag__tooltip
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Tooltip"
msgstr "Informació sobre eines"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Trash"
msgstr "Paperera"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Trash Management"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__is_editable_attachment
msgid "True if we can edit the link attachment."
msgstr "Veritat si podem modificar l'adjunt de l'enllaç."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__type
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Type"
msgstr "Tipus"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipus d'activitat d'excepció registrada."

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__url
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "URL"
msgstr "L'URL"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"URL %s does not seem complete, as it does not begin with http(s):// or "
"ftp://"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__url_preview_image
msgid "URL Preview Image"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Unavailable action."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Unlock"
msgstr "Desbloquejar"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "Unnamed"
msgstr "Sense nom"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Upload"
msgstr "Pujar"

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.document_action
#: model_terms:ir.actions.act_window,help:documents.document_action_portal
msgid ""
"Upload <span class=\"fw-normal\">a file or</span> drag <span class=\"fw-"
"normal\">it here.</span>"
msgstr ""
"Puja <span class=\"fw-normal\">un fitxer o</span> arrossega <span "
"class=\"fw-normal\">aquí.</span>"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.xml:0
msgid "Upload New Version"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/mail_activity.py:0
msgid "Upload file request"
msgstr "Sol·licitud de càrrega d'arxius"

#. module: documents
#: model:ir.model,name:documents.model_res_users
#: model:res.groups,name:documents.group_documents_user
msgid "User"
msgstr "Usuari"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
#: code:addons/documents/static/src/components/documents_permission_panel/documents_partner_access.xml:0
msgid "User Access Role"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__user_permission
msgid "User permission"
msgstr "Permís d'usuari"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_vat
msgid "VAT"
msgstr "CIF/NIF"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_validated
msgid "Validated"
msgstr "Validat"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.xml:0
msgid ""
"Versions are displayed in the order they were uploaded to Documents.\n"
"                    If you delete the most recent version, the previous one is automatically restored as the current one."
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_tag_videos
msgid "Videos"
msgstr "Vídeos"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: model:ir.model.fields.selection,name:documents.selection__documents_access__role__view
#: model:ir.model.fields.selection,name:documents.selection__documents_document__access_internal__view
#: model:ir.model.fields.selection,name:documents.selection__documents_document__access_via_link__view
#: model:ir.model.fields.selection,name:documents.selection__documents_document__user_permission__view
msgid "Viewer"
msgstr "Visor"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid ""
"Want to become a <b>paperless company</b>? Let's discover Odoo Documents."
msgstr ""
"Vols convertir-te en <b>empresa sense paper</b>? Descobrim documents d'Odoo."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__website_message_ids
msgid "Website Messages"
msgstr "Missatges del lloc web"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicacions del lloc web"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__create_activity_date_deadline_range_type__weeks
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__weeks
msgid "Weeks"
msgstr "Setmanes"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "What do you want to do with the remaining pages ?"
msgstr ""

#. module: documents
#: model_terms:web_tour.tour,rainbow_man_message:documents.documents_tour
msgid ""
"Wow... 6 documents processed in a few seconds, You're good.<br>The tour is "
"complete. Try uploading your own documents now."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_partner_access.xml:0
msgid "You"
msgstr "Tu"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to (un)archive documents."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to change ownerships of documents you do not own."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to delete all these items."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to execute embedded actions."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to move (some of) these documents."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to pin/unpin embedded Actions."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to read the permission panel data."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to write in this folder."
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__attachment_type
msgid ""
"You can either upload a file from your computer or copy/paste an internet "
"link to your file."
msgstr ""
"Pots pujar tant un fitxer del teu ordinador com copiar/enganxar un enllaç "
"d'internet al teu fitxer."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_pin_actions.js:0
msgid "You can not pin actions for that folder."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"You can not update the access of a shortcut, update its target instead."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You can't access that folder_id."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid ""
"You can't create shortcuts in or move documents to this special folder."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You can't move documents out of folders you cannot edit."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You cannot change the owner of documents you do not own."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_tag.py:0
msgid "You cannot delete tags used in server actions."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You cannot delete this attachment."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You cannot duplicate document(s) in the Trash."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "You cannot move folders or files when in the trash."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "You cannot move request in the company folder"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You cannot pin an action on that document."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You cannot pin that type of action."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_remove_partner_button.xml:0
msgid "You cannot remove this partner"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "You cannot share multiple documents at the same time"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You do not have sufficient access rights to delete these documents."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid ""
"You don't have the rights to move documents nor create shortcut to that "
"folder."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_drop_zone.xml:0
msgid "You must be in a specific writable workspace to upload files"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Your Role: %s"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Your individual space."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.js:0
msgid "Your personal space"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_details
msgid "alias"
msgstr "àlies"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "bytes"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_deletion_form
msgid "days."
msgstr "dies."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.js:0
msgid "document"
msgstr "document"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "documents"
msgstr "documents"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "e.g. Discuss proposal"
msgstr "p. ex. Discussió d'una proposta"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_new_folder
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_rename
msgid "e.g. Finance"
msgstr "p. ex. Finances"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "e.g. Missing Expense"
msgstr "p. ex. Falta la despesa"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "e.g. To Validate"
msgstr "p. ex. Per validar"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "e.g. https://www.youtube.com/watch?v=CP96yVnXNrY"
msgstr "p. ex. https://www.youtube.com/watch?v=CP96yVnXNrY"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_details
msgid "e.g. mycompany.com"
msgstr "e.g. lamevaempresa.com"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "files"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.js:0
msgid "folder"
msgstr "carpeta"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "folders,"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__is_readonly_model
msgid "is_readonly_model"
msgstr "is_readonly_model"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/chatter/documents_chatter_overlay.xml:0
msgid "items selected"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "items,"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "kiB"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_page/pdf_page.xml:0
msgid "select"
msgstr "selecciona"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.mail_template_document_share
msgid "shared a document with you.<br/>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.mail_template_document_share
msgid "shared a folder with you.<br/>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "shared by"
msgstr "compartit per"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.mail_template_document_share
msgid "shared this document with you:"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.mail_template_document_share
msgid "shared this folder with you:"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "status code: %(status)s, message: %(message)s"
msgstr "codi d'estat: %(status)s, missatge: %(message)s"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "sub-doc-%s"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "unnamed"
msgstr "sense nom"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid ""
"•\n"
"                                        <b>URL</b>"
msgstr ""
