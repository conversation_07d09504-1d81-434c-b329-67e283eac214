# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents
# 
# Translators:
# <PERSON><PERSON><PERSON> <pernillek<PERSON><EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# <PERSON><PERSON>, 2025
# <PERSON><PERSON> <<EMAIL>>, 2025
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-26 20:44+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid " (%s locked)"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "%s (copy)"
msgstr "%s (kopi)"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "%s (remaining pages)"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "%s Files"
msgstr "%s Filer"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "%s Folders"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/list/document_count_field.js:0
msgid "%s documents"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid "%s documents have been moved."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "%s file(s) not moved because they are locked by another user"
msgstr "%s fil(er) ikke flyttet fordi de var låst fra en anden bruger"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.js:0
msgid "%s has been copied in My Drive."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.js:0
msgid "%s have been copied in My Drive."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "%s new document(s) created"
msgstr "%s nye dokument(er) oprettet"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "%s page(s) deleted"
msgstr "%s side(r) slettet"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "%s shared with you"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid "%s shortcuts have been created."
msgstr ""

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid ""
". Scanned files will appear automatically in your workspace. Then, process "
"your documents in bulk with the split tool: launch user defined actions, "
"request a signature, convert to vendor bills with AI, etc."
msgstr ""
". Scanned files vil automatisk vises i din arbejdsplads. Derefter kan du "
"behandle dine dokumenter i bulk med split-værktøjet: start brugerdefinerede "
"handlinger, anmod om en underskrift, konverter til leverandørregninger med "
"AI, osv."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.mail_template_document_share
msgid ".<br/>"
msgstr ".<br/>"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "1 File"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "1 Folder"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/list/document_count_field.js:0
msgid "1 document"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_tag_year_previous
msgid "2024"
msgstr "2024"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_year_current
msgid "2025"
msgstr ""

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid "<b class=\"tip_title\">Tip: Become a paperless company</b>"
msgstr "<b class=\"tip_title\">Tip: Bliv en papirløs virksomhed</b>"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "<b>Deselect this page</b> as we plan to process all bills first."
msgstr ""
"<b>Fravælg denne side</b> eftersom vi planlægger at behandle alle regninger "
"først."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "<b>Select</b> this page to continue."
msgstr "<b>Vælg</b> denne side for at fortsætte."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "<br/>Powered by"
msgstr "<br/>Powered by"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "<i class=\"fa fa-download fa-fw\"/> Download All"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_file
msgid "<i class=\"fa fa-download fa-fw\"/> Download file"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<i class=\"fa fa-external-link me-2\" title=\"This is a shortcut\"/>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<i class=\"fa fa-external-link text-gray-500\" title=\"This is a shortcut\"/>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_file
msgid "<i class=\"fa fa-eye\"/> Preview file"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "<i class=\"fa fa-lock oe_inline\" title=\"Locked\" invisible=\"not lock_uid\"/>"
msgstr "<i class=\"fa fa-lock oe_inline\" title=\"Locked\" invisible=\"not lock_uid\"/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "<i class=\"fa fa-upload\"/>  Upload"
msgstr "<i class=\"fa fa-upload\"/>  Upload"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_file
msgid "<i class=\"fa fa-upload\"/> Replace file"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_document_res_partner_view
msgid "<span class=\"o_stat_text\">Documents</span>"
msgstr "<span class=\"o_stat_text\">Dokumenter</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "<span class=\"o_stat_text\">Related <br/> Record</span>"
msgstr "<span class=\"o_stat_text\">Relateret <br/> Datasæt</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "<span title=\"Requested Document\">Requested Document</span>"
msgstr "<span title=\"Requested Document\">Anmodet Dokument</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "<span>&amp;nbsp;Documents.</span>"
msgstr "<span>&amp;nbsp;Dokumenter.</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<span><b> Request</b></span>"
msgstr "<span><b> Anmodning</b></span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<span><b>Requested Document</b></span>"
msgstr "<span><b>Anmodet dokument</b></span>"

#. module: documents
#: model:mail.template,body_html:documents.mail_template_document_request
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"                    <tbody>\n"
"                        <!-- HEADER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\">\n"
"                                        <span style=\"font-size: 10px;\">\n"
"                                            Document Request: <br/>\n"
"                                            <t t-if=\"object.name\">\n"
"                                                <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Inbox Financial</span>\n"
"                                            </t>\n"
"                                        </span><br/>\n"
"                                    </td><td valign=\"middle\" align=\"right\" t-if=\"not object.create_uid.company_id.uses_default_logo\">\n"
"                                        <img t-attf-src=\"/logo.png?company={{ object.create_uid.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.create_uid.company_id.name\"/>\n"
"                                    </td></tr>\n"
"                                    <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- CONTENT -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                        <div>\n"
"                                            Hello <t t-out=\"object.owner_id.name or ''\">OdooBot</t>,\n"
"                                            <br/><br/>\n"
"                                            <t t-out=\"object.create_uid.name or ''\">OdooBot</t> (<t t-out=\"object.create_uid.email or ''\"><EMAIL></t>) asks you to provide the following document:\n"
"                                            <br/><br/>\n"
"                                            <center>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.name\">\n"
"                                                        <b t-out=\"object.name or ''\">Inbox Financial</b>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.request_activity_id.note\">\n"
"                                                        <i t-out=\"object.request_activity_id.note or ''\">Example of a note.</i>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <br/>\n"
"                                                <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                    <a t-att-href=\"object.access_url\" style=\"background-color: #875A7B; padding: 20px 30px 20px 30px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                        Upload the requested document\n"
"                                                    </a>\n"
"                                                </div>\n"
"                                            </center><br/>\n"
"                                            Please provide us with the missing document before <t t-out=\"object.request_activity_id.date_deadline\">2021-05-17</t>.\n"
"                                            <t t-if=\"user and user.signature\" data-o-mail-quote-container=\"1\">\n"
"                                                <br/>\n"
"                                                <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                                                <br data-o-mail-quote=\"1\"/>\n"
"                                            </t>\n"
"                                        </div>\n"
"                                    </td></tr>\n"
"                                    <tr><td style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- FOOTER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\" align=\"left\">\n"
"                                        <t t-out=\"object.create_uid.company_id.name or ''\">YourCompany</t>\n"
"                                    </td></tr>\n"
"                                    <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                                        <t t-out=\"object.create_uid.company_id.phone or ''\">******-123-4567</t>\n"
"                                        <t t-if=\"object.create_uid.company_id.email\">\n"
"                                            | <a t-attf-href=\"'mailto:%s' % {{ object.create_uid.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.email or ''\"><EMAIL></a>\n"
"                                        </t>\n"
"                                        <t t-if=\"object.create_uid.company_id.website\">\n"
"                                            | <a t-attf-href=\"'%s' % {{ object.create_uid.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.website or ''\">http://www.example.com</a>\n"
"                                        </t>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                    </table>\n"
"                    </td></tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                          <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"                            Powered by <a target=\"_blank\" href=\"https://www.odoo.com/app/documents\" style=\"color: #875A7B;\">Odoo Documents</a>\n"
"                          </td></tr>\n"
"                        </table>\n"
"                    </td></tr>\n"
"                </table>\n"
"            "
msgstr ""

#. module: documents
#: model:mail.template,body_html:documents.mail_template_document_request_reminder
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"                    <tbody>\n"
"                        <!-- HEADER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\">\n"
"                                        <span style=\"font-size: 10px;\">\n"
"                                            Document Request: <br/>\n"
"                                            <t t-if=\"object.name\">\n"
"                                                <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Inbox Financial</span>\n"
"                                            </t>\n"
"                                        </span><br/>\n"
"                                    </td><td valign=\"middle\" align=\"right\">\n"
"                                        <img t-attf-src=\"/logo.png?company={{ object.create_uid.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.create_uid.company_id.name\"/>\n"
"                                    </td></tr>\n"
"                                    <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- CONTENT -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                        <div>\n"
"                                            Hello <t t-out=\"object.owner_id.name or ''\">OdooBot</t>,\n"
"                                            <br/><br/>\n"
"                                            This is a friendly reminder to upload your requested document:\n"
"                                            <br/><br/>\n"
"                                            <center>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.name\">\n"
"                                                        <b t-out=\"object.name or ''\">Inbox Financial</b>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.request_activity_id.note\">\n"
"                                                        <i t-out=\"object.request_activity_id.note or ''\">Example of a note.</i>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <br/>\n"
"                                                <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                    <a t-att-href=\"object.access_url\" style=\"background-color: #875A7B; padding: 20px 30px 20px 30px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                        Upload the requested document\n"
"                                                    </a>\n"
"                                                </div>\n"
"                                            </center><br/>\n"
"                                            Please provide us with the missing document before <t t-out=\"object.request_activity_id.date_deadline or ''\">2021-05-17</t>.\n"
"                                            <br/><br/>\n"
"                                            Thank you,\n"
"                                            <t t-if=\"user and user.signature\" data-o-mail-quote-container=\"1\">\n"
"                                                <br/>\n"
"                                                <t t-out=\"user.signature\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                                                <br data-o-mail-quote=\"1\"/>\n"
"                                            </t>\n"
"                                        </div>\n"
"                                    </td></tr>\n"
"                                    <tr><td style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- FOOTER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\" align=\"left\">\n"
"                                        <t t-out=\"object.create_uid.company_id.name or ''\">YourCompany</t>\n"
"                                    </td></tr>\n"
"                                    <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                                        <t t-out=\"object.create_uid.company_id.phone or ''\">******-123-4567</t>\n"
"                                        <t t-if=\"object.create_uid.company_id.email\">\n"
"                                            | <a t-attf-href=\"'mailto:%s' % {{ object.create_uid.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.email\"><EMAIL></a>\n"
"                                        </t>\n"
"                                        <t t-if=\"object.create_uid.company_id.website\">\n"
"                                            | <a t-att-href=\"object.create_uid.company_id.website\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.website\">http://www.example.com</a>\n"
"                                        </t>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                    </table>\n"
"                    </td></tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                          <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"                            Powered by <a target=\"_blank\" href=\"https://www.odoo.com/app/documents\" style=\"color: #875A7B;\">Odoo Documents</a>\n"
"                          </td></tr>\n"
"                        </table>\n"
"                    </td></tr>\n"
"                </table>\n"
"            "
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"En Python-ordbog, der vil blive evalueret for at angive standardværdier, når"
" du opretter nye poster til dette alias."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "A destination is required when creating multiple shortcuts at once."
msgstr ""

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_document_folder_id_not_id
msgid "A folder cannot be included in itself"
msgstr ""

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_document_shortcut_document_id_not_id
msgid "A shortcut cannot point to itself"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid "A shortcut has been created."
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Access"
msgstr "Adgang"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "Access Error"
msgstr "Adgangsfejl"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__access_token
#: model:ir.model.fields,field_description:documents.field_documents_redirect__access_token
msgid "Access Token"
msgstr "Adgangstoken"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.portal_my_home_documents
msgid "Access all your documents"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_access.py:0
msgid "Access documents and partners cannot be changed."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Access to Automations"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid "Access to Server Actions"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Access to a folder or a document"
msgstr ""

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_document_document_token_unique
msgid "Access tokens already used."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__access_url
msgid "Access url"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Action"
msgstr "Handling"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_needaction
msgid "Action Needed"
msgstr "Handling påkrævet"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_pin_actions.xml:0
msgid "Actions on Select"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__active
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_new_folder
msgid "Active"
msgstr "Aktiv"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_ids
#: model:ir.ui.menu,name:documents.mail_activities
msgid "Activities"
msgstr "Aktiviteter"

#. module: documents
#: model:ir.model,name:documents.model_mail_activity
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Activity"
msgstr "Aktivitet"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Aktivitet undtagelse markering"

#. module: documents
#: model:ir.ui.menu,name:documents.mail_activity_plan_menu
msgid "Activity Plans"
msgstr "Aktivitetsplaner"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_state
msgid "Activity State"
msgstr "Aktivitetstilstand"

#. module: documents
#: model:ir.model,name:documents.model_mail_activity_type
msgid "Activity Type"
msgstr "Aktivitetstype"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_type_icon
msgid "Activity Type Icon"
msgstr "Aktivitetstype ikon"

#. module: documents
#: model:ir.actions.act_window,name:documents.mail_activity_type_action_document
#: model:ir.ui.menu,name:documents.mail_activity_type_menu
msgid "Activity Types"
msgstr "Aktivitetstyper"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "Activity assigned to"
msgstr ""

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.mail_activity_plan_action_document
msgid ""
"Activity plans are used to assign a list of activities in just a few clicks\n"
"                    (e.g. \"NDA signature process\", \"Workspace workflow\", ...)"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_type_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_type_id
msgid "Activity type"
msgstr "Aktivitetstype"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "Add"
msgstr "Tilføj"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_pin_actions.xml:0
msgid "Add Custom Action"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Add File"
msgstr "Tilføj Fil"

#. module: documents
#: model:ir.actions.act_window,name:documents.action_folder_form
msgid "Add Folder"
msgstr ""

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_tag_add_bill
msgid "Add Tag Bill"
msgstr ""

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_tag_add_validated
msgid "Add Tag Validated"
msgstr ""

#. module: documents
#: model:ir.actions.act_window,name:documents.action_url_form
msgid "Add Url"
msgstr "Tilføj url"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "Add a tag..."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "Add an alias tag..."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Add new file"
msgstr "Tilføj ny fil"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Add people or email addresses..."
msgstr "Tilføj personer eller emailadresser..."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Add people or email addresses:"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_shortcut.js:0
msgid "Add shortcut"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_star.js:0
msgid "Add star"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Additional documents you have access to."
msgstr ""

#. module: documents
#: model:res.groups,name:documents.group_documents_manager
msgid "Administrator"
msgstr "Administrator"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_ads
msgid "Ads"
msgstr "Reklamer"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_id
msgid "Alias"
msgstr "Alias"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_contact
msgid "Alias Contact Security"
msgstr "Alias kontakt sikkerhed"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_domain_id
msgid "Alias Domain"
msgstr "Alias domæne"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_domain
msgid "Alias Domain Name"
msgstr "Alias domænenavn"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_full_name
msgid "Alias Email"
msgstr "Alias e-mail"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_name
msgid "Alias Name"
msgstr "Alias navn"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_status
msgid "Alias Status"
msgstr "Alias status"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_tag_ids
msgid "Alias Tags"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_status
msgid "Alias status assessed on the last message received."
msgstr "Alias status vurderet ud fra den seneste modtagne besked."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_model_id
msgid "Aliased Model"
msgstr "Aliased model"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.js:0
msgid ""
"All users with access to this %(documentType)s or its parent will have edit "
"permissions."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__access_ids
msgid "Allowed Access"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Already linked Documents: %s"
msgstr ""

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid ""
"An easy way to process incoming mails is to configure your scanner to send "
"PDFs to"
msgstr ""
"En nem måde at behandle indgående mails på er at konfigurere din scanner til"
" at sende PDF-filer til"

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid ""
"An easy way to process incoming mails is to configure your scanner to send "
"PDFs to your workspace email. Scanned files will appear automatically in "
"your workspace. Then, process your documents in bulk with the split tool: "
"launch user defined actions, request a signature, convert to vendor bills "
"with AI, etc."
msgstr ""
"En nem måde at behandle indgående mails på, er at konfigurere din scanner "
"til at sende PDF'er til dit arbejdsområdes email. Scannede filer vil vises "
"automatisk i dit arbejdsområde. Behandl derefter dine dokumenter i partier "
"med delings værktøjet: kør brugerdefinerede handlinger, anmod om en "
"underskrift, konverter til leverandørfaktura med kunstig intelligens, osv."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "An error occured while uploading."
msgstr "Der opstod en fejl under upload"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.js:0
msgid "Anyone on the internet with the link can edit"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.js:0
msgid "Anyone on the internet with the link can view"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.xml:0
msgid "Anyone with the link"
msgstr "Enhver med linket"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Archive original file(s)"
msgstr "Arkivér originale fil(er)"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Are you sure that you want to delete the focused page ?"
msgstr "Er du sikker på, at du vil slette den fokuserede side?"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Are you sure that you want to delete the selected page(s)"
msgstr "Er du sikker på, at du vil slette den valgte side(r)"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Are you sure that you want to delete this page ?"
msgstr "Er du sikker på at du vil slette denne side ?"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.js:0
msgid "Are you sure you want to delete this attachment?"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Are you sure you want to permanently erase the document?"
msgstr "Er du sikker på, at du vil slette dokumentet permanent?"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Are you sure you want to permanently erase the documents?"
msgstr "Er du sikker på, at du vil slette dokumenterne permanent?"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid ""
"As this PDF contains multiple documents, let's split and process in bulk."
msgstr ""
"Lad os dele denne PDF og behandle den i partier, eftersom den indeholder "
"flere dokumenter."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid ""
"At least one document couldn't be moved due to access rights. Shortcuts have"
" been created."
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_ir_attachment
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_id
msgid "Attachment"
msgstr "Vedhæftning"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_attachment_count
msgid "Attachment Count"
msgstr "Antal vedhæftninger"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__description
msgid "Attachment Description"
msgstr "Vedhæftning beskrivelse"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_name
msgid "Attachment Name"
msgstr "Navn på vedhæftede fil "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_type
msgid "Attachment Type"
msgstr "Vedhæftning type"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_automations.js:0
msgid "Automations"
msgstr "Automatiseringer"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__available_embedded_actions_ids
msgid "Available Embedded Actions"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_tag_bill
msgid "Bill"
msgstr "Faktura"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Blank Page"
msgstr "Blank side"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_brochures
msgid "Brochures"
msgstr "Brochurer"

#. module: documents
#: model:ir.model.fields,help:documents.field_mail_activity_type__folder_id
msgid "By defining a folder, the upload activities will generate a document"
msgstr "Ved at definere en mappe, vil upload aktiviteter generere et dokument"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_link_to_record_form_view
msgid "Cancel"
msgstr "Annullér"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__checksum
msgid "Checksum/SHA1"
msgstr "Checksum/SHA1"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__children_ids
msgid "Children"
msgstr "Underkonti"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Choose a record to link"
msgstr "Vælg en post, der skal linkes"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_details
msgid "Choose or Configure Email Servers"
msgstr "Vælg eller konfigurer e-mail servere"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Click on a card to <b>select the document</b>."
msgstr "Klik på et kort for at <b>vælge dokumentet</b>."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Click on a thumbnail to <b>preview the document</b>."
msgstr "Klik på miniaturebilledet for at <b>forhåndsvise dokumentet</b>."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid ""
"Click on the <b>page separator</b>: we don't want to split these two pages "
"as they belong to the same document."
msgstr ""
"Klik på <b>side deleren</b>: Vi vil ikke dele disse to sider, eftersom de "
"tilhører samme dokument."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Click the cross to <b>exit preview</b>."
msgstr "Klik på krydset for at <b> afslutte forhåndsvisning</b>."

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__thumbnail_status__client_generated
msgid "Client Generated"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.xml:0
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Close"
msgstr "Luk"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Close split tools"
msgstr "Luk opdelte værktøjer"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_tag__color
msgid "Color"
msgstr "Farve"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Common roots for all company users."
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_res_company
msgid "Companies"
msgstr "Virksomheder"

#. module: documents
#. odoo-javascript
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.js:0
#: code:addons/documents/static/src/views/kanban/documents_kanban_record.js:0
#: model:ir.model.fields,field_description:documents.field_documents_document__company_id
msgid "Company"
msgstr "Virksomhed"

#. module: documents
#: model:ir.model,name:documents.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurer opsætning"

#. module: documents
#: model:ir.ui.menu,name:documents.Config
msgid "Configuration"
msgstr "Konfiguration"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model:ir.model,name:documents.model_res_partner
#: model:ir.model.fields,field_description:documents.field_documents_document__partner_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__partner_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Contact"
msgstr "Kontakt"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Contact your Administrator to get access if needed."
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_tag_contracts
msgid "Contracts"
msgstr "Kontrakter"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "Control panel buttons"
msgstr "Kontrolpanel knapper"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.js:0
msgid "Copy Link"
msgstr "Kopiér link"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Copy Links"
msgstr ""

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_create_activity
msgid "Create Activity"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Create Shortcut"
msgstr ""

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.mail_activity_plan_action_document
msgid "Create a Document Activity Plan"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_option
msgid "Create a new activity"
msgstr "Opret en ny aktivitet"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Create and edit new contact \""
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Create new contact \""
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_tag__create_uid
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Created by"
msgstr "Oprettet af"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_date
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__create_date
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__create_date
#: model:ir.model.fields,field_description:documents.field_documents_tag__create_date
msgid "Created on"
msgstr "Oprettet den"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Creation Date"
msgstr "Oprettelsesdato"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.xml:0
msgid "Current Version"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Tilpasset Ikke modtaget Besked"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__create_activity_date_deadline_range_type__days
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__days
msgid "Days"
msgstr "Dage"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_defaults
msgid "Default Values"
msgstr "Standardværdier"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__deletion_delay
#: model:ir.model.fields,help:documents.field_res_config_settings__deletion_delay
msgid "Delay after permanent deletion of the document in the trash (days)"
msgstr ""
"Forsinkelse efter permanent sletning af dokumentet i papirkurven (dage)"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.xml:0
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
#: code:addons/documents/static/src/views/list/documents_list_controller.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Delete"
msgstr "Slet"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Delete focused or selected pages"
msgstr "Slet fokuserede eller valgte sider"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Delete permanently"
msgstr "Slet permanent"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_res_config_settings__deletion_delay
msgid "Deletion Delay"
msgstr "Sletningsforsinkelse"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__deletion_delay
msgid "Deletion delay"
msgstr "Sletningsforsinkelse"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Deletion delay (days)"
msgstr "Sletningsforsinkelse (dage)"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_deprecated
msgid "Deprecated"
msgstr "Forældet"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
#: code:addons/documents/static/src/views/hooks.js:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_rename
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
#: model_terms:ir.ui.view,arch_db:documents.folder_deletion_form
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Discard"
msgstr "Kassér"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Discoverable"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_access__display_name
#: model:ir.model.fields,field_description:documents.field_documents_document__display_name
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__display_name
#: model:ir.model.fields,field_description:documents.field_documents_redirect__display_name
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__display_name
#: model:ir.model.fields,field_description:documents.field_documents_tag__display_name
msgid "Display Name"
msgstr "Vis navn"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "Do you want to exit without saving or gather pages into one document ?"
msgstr "Vil du afslutte uden at gemme eller samle sider i ét dokument?"

#. module: documents
#: model:ir.model,name:documents.model_documents_document
#: model:ir.model.fields,field_description:documents.field_documents_access__document_id
#: model:ir.model.fields,field_description:documents.field_documents_redirect__document_id
#: model:ir.model.fields,field_description:documents.field_documents_tag__document_ids
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "Document"
msgstr "Dokument"

#. module: documents
#: model:ir.model,name:documents.model_documents_access
msgid "Document / Partner"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.xml:0
msgid "Document Access Internal"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.xml:0
msgid "Document Access Link"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__document_count
#: model:ir.model.fields,field_description:documents.field_res_partner__document_count
msgid "Document Count"
msgstr "Dokument antal"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Document Name"
msgstr "Navn på Dokument"

#. module: documents
#: model:ir.actions.act_window,name:documents.mail_activity_plan_action_document
msgid "Document Plans"
msgstr "Dokument planer"

#. module: documents
#: model:ir.model,name:documents.model_documents_redirect
msgid "Document Redirect"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_request_wizard
msgid "Document Request"
msgstr "Dokumentanmodning"

#. module: documents
#: model:mail.template,subject:documents.mail_template_document_request
msgid ""
"Document Request {{ object.name != False and ': '+ object.name or '' }}"
msgstr ""
"Dokument Anmodning {{ object.name != False and ': '+ object.name or '' }}"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: code:addons/documents/models/mail_activity.py:0
msgid "Document Request: %(name)s Uploaded by: %(user)s"
msgstr "Dokument Anmodning: %(name)s Uploaded af: %(user)s"

#. module: documents
#: model:mail.template,name:documents.mail_template_document_request_reminder
msgid "Document Request: Reminder"
msgstr "Dokument anmodning: Påmindelse"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__document_token
msgid "Document Token"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "Document preview"
msgstr "Forhåndsvisning af dokument"

#. module: documents
#. odoo-python
#: code:addons/documents/controllers/documents.py:0
msgid "Document uploaded by %(user)s"
msgstr ""

#. module: documents
#: model:mail.template,name:documents.mail_template_document_request
msgid "Document: Document Request"
msgstr "Dokument: Dokument anmodning"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/list/document_count_field.js:0
msgid "DocumentCountIntegerField"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: code:addons/documents/models/res_partner.py:0
#: model:ir.actions.act_window,name:documents.document_action
#: model:ir.actions.act_window,name:documents.document_action_portal
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__document_ids
#: model:ir.model.fields,field_description:documents.field_res_users__document_count
#: model:ir.ui.menu,name:documents.dashboard
#: model:ir.ui.menu,name:documents.menu_root
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.documents_view_activity
#: model_terms:ir.ui.view,arch_db:documents.portal_my_home_documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Documents"
msgstr "Dokumenter"

#. module: documents
#: model:ir.model,name:documents.model_documents_link_to_record_wizard
msgid "Documents Link to Record"
msgstr "Dokumenter Link til Post"

#. module: documents
#: model:ir.model,name:documents.model_documents_mixin
msgid "Documents creation mixin"
msgstr "Dokument oprettelse mixin"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "Documents in the Trash cannot be shared"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.js:0
msgid "Documents moved to trash will show up here"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.js:0
msgid "Documents shared with you will appear here"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_unlink_mixin
msgid "Documents unlink mixin"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.xml:0
msgid "Done"
msgstr "Udført"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "Done!"
msgstr "Færdig!"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.xml:0
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_download.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Download"
msgstr "Download"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Download #{document.name}"
msgstr "Download #{document.name}"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Download all files"
msgstr "Hent alle filer"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_file
msgid "Download file"
msgstr "Download fil"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Download folder as zip"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Download zip #{subfolder.name}"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_tag_draft
msgid "Draft"
msgstr "Udkast"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_drop_zone.xml:0
msgid "Drop files here to upload"
msgstr "Træk filer hertil for at uploade"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_date_deadline_range
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_date_deadline_range
msgid "Due Date In"
msgstr "Forfaldsdato om"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_date_deadline_range_type
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_date_deadline_range_type
msgid "Due type"
msgstr "Forfaldstype"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Duplicate"
msgstr "Duplikér"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
#: code:addons/documents/static/src/views/search/documents_search_panel.xml:0
msgid "Edit"
msgstr "Rediger"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: model:ir.model.fields.selection,name:documents.selection__documents_access__role__edit
#: model:ir.model.fields.selection,name:documents.selection__documents_document__access_internal__edit
#: model:ir.model.fields.selection,name:documents.selection__documents_document__access_via_link__edit
#: model:ir.model.fields.selection,name:documents.selection__documents_document__user_permission__edit
msgid "Editor"
msgstr "Redaktør"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "Email"
msgstr "E-mail"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_email
msgid "Email Alias"
msgstr "E-mail alias"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_details
msgid "Email Upload"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__email_cc
msgid "Email cc"
msgstr "Email cc"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "E-mail domæne f.eks. 'example.com' i '<EMAIL>'"

#. module: documents
#: model:ir.actions.act_window,name:documents.mail_alias_action
msgid "Email links"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#: code:addons/documents/static/src/views/hooks.js:0
#: model:ir.model.fields.selection,name:documents.selection__documents_document__thumbnail_status__error
msgid "Error"
msgstr "Fejl"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Escape Preview/Deselect/Exit"
msgstr "Undslip Forhåndsvisning/Fravælg/Forlad"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "Exit Split Tools"
msgstr "Afslut opdelte værktøjer"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "Exit without saving"
msgstr "Afslut uden at gemme"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_partner_access.xml:0
msgid "Exp:"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_tag_expense
msgid "Expense"
msgstr "Omkostning"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_access__expiration_date
msgid "Expiration"
msgstr "Udløbsdato"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/list/documents_list_controller.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Export"
msgstr "Eksporter"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Extra comments..."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__favorited_ids
msgid "Favorite of"
msgstr "Favorit hos"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_type_icon/documents_type_icon.xml:0
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__binary
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "File"
msgstr "Fil"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__datas
msgid "File Content (base64)"
msgstr "Fil Indhold (base64)"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__raw
msgid "File Content (raw)"
msgstr "Fil Indhold (rå)"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__file_extension
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "File Extension"
msgstr "Filudvidelse"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__file_size
msgid "File Size"
msgstr "Filstørrelse"

#. module: documents
#: model:ir.model,name:documents.model_ir_binary
msgid "File streaming helper model for controllers"
msgstr "Filstreaming-hjælpermodel til controllere"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/kanban/documents_kanban_renderer.xml:0
msgid "Files"
msgstr "Filer"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Files Centralization"
msgstr "Filer Centralisering"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_deletion_form
msgid "Files will be sent to trash and deleted forever after"
msgstr "Filer vil blive sendt til papirkurven og slettet for evigt efter"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
msgid "Files will be sent to trash and deleted forever after %s days."
msgstr ""
"Filer vil blive sendt til papirkurven og slettet for evigt efter %s dage."

#. module: documents
#: model:documents.tag,name:documents.documents_tag_financial
msgid "Financial"
msgstr "Financiel"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_fiscal
msgid "Fiscal"
msgstr "Finans"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Focus first page of next group"
msgstr "Fokus på første side i næste gruppe"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Focus first page of previous group"
msgstr "Fokus på første side i forrige gruppe"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Focus next page"
msgstr "Fokus på næste side"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Focus previous page"
msgstr "Fokus på forrige side"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
#: code:addons/documents/static/src/views/documents_type_icon/documents_type_icon.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_document__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__folder_id
#: model:ir.model.fields,field_description:documents.field_mail_activity_type__folder_id
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__folder
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "Folder"
msgstr "Mappe"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/kanban/documents_kanban_renderer.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Folders"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_follower_ids
msgid "Followers"
msgstr "Følgere"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_partner_ids
msgid "Followers (Partners)"
msgstr "Følgere (partnere)"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Skrifttype awesome icon f.eks. fa-opgaver"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "Gather in one document"
msgstr "Saml i ét dokument"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_details
msgid "General"
msgstr "Generel"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.xml:0
msgid "General access"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "GiB"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Group By"
msgstr "Sortér efter"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_hr
msgid "HR"
msgstr "HR"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__has_message
msgid "Has Message"
msgstr "Har besked"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Hide shortcuts"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__previous_attachment_ids
msgid "History"
msgstr "Historik"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_access__id
#: model:ir.model.fields,field_description:documents.field_documents_document__id
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__id
#: model:ir.model.fields,field_description:documents.field_documents_redirect__id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__id
#: model:ir.model.fields,field_description:documents.field_documents_tag__id
msgid "ID"
msgstr "ID"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"ID for overordnet record indeholdende aliaset (eksempel: projekt, der "
"indeholder opgaveoprettelses aliaset)"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_exception_icon
msgid "Icon"
msgstr "Ikon"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikon for uventet aktivitet."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__is_access_via_link_hidden
msgid ""
"If \"True\", only people given direct access to this document will be able "
"to view it. If \"False\", access with the link also given to all who can "
"access the parent folder."
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Hvis afkrydset, kræver nye beskeder din opmærksomhed "

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_has_error
#: model:ir.model.fields,help:documents.field_documents_document__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Hvis afkrydset har nogle beskeder en leveringsfejl"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Hvis angivet vil dette indhold automatisk blive sendt ud til uautoriserede "
"brugere, i stedet for standard beskeden."

#. module: documents
#: model:documents.tag,name:documents.documents_tag_images
msgid "Images"
msgstr "Billeder"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Impossible to delete folders used by other applications."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Impossible to invite partners on multiple documents at once."
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "In"
msgstr "I"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__thumbnail_status__restricted
msgid "Inaccessible"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_tag_inbox
#: model:mail.activity.type,name:documents.mail_documents_activity_data_Inbox
msgid "Inbox"
msgstr "Indbakke"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"Incorrect values. Use one of the following for the following fields: "
"%(hints)s.)"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__index_content
msgid "Indexed Content"
msgstr "Indekseret indhold"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_details.js:0
msgid "Info & Tags"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Info & tags"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.xml:0
msgid "Internal Users"
msgstr "Interne brugere"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__access_internal
msgid "Internal Users Rights"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.js:0
msgid "Internal users can edit"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.js:0
msgid "Internal users can view"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Invalid folder id"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "Invalid operation"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Invalid role."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_editable_attachment
msgid "Is Editable Attachment"
msgstr "Er redigerbar vedhæftning"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_favorited
msgid "Is Favorited"
msgstr "Er Favorit"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_is_follower
msgid "Is Follower"
msgstr "Er følger"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_multipage
msgid "Is considered multipage"
msgstr "Betragtes som flersidet"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"It is not possible to move archived documents or documents to archived "
"folders."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"Item(s) you wish to restore are included in archived folders. To restore these items, you must restore the following including folders instead:\n"
"- %(folders_list)s"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Items in trash will be deleted forever after %s days."
msgstr "Elementer i papirkurven vil blive slettet for evigt efter %s dage."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Items moved to the trash will be deleted forever after %s days."
msgstr ""
"Elementer, der flyttes til papirkurven, slettes for altid efter %s dage."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_access__last_access_date
#: model:ir.model.fields,field_description:documents.field_documents_document__last_access_date_group
msgid "Last Accessed On"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_tag__write_uid
msgid "Last Updated by"
msgstr "Sidst opdateret af"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__write_date
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__write_date
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__write_date
#: model:ir.model.fields,field_description:documents.field_documents_tag__write_date
msgid "Last Updated on"
msgstr "Sidst opdateret den"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_legal
msgid "Legal"
msgstr "Juridisk"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid ""
"Let's process documents in your Inbox.<br/><i>Tip: Use Tags to filter "
"documents and structure your process.</i>"
msgstr ""
"Lad os behandle dokumenter i din indbakke.<br/><i>Råd: Brug tags til at "
"filtrere dokumenter samt strukturere din behandling.</i>"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Let's process these bills: send to Finance workspace."
msgstr "Lad os behandle disse regninger: Send til Finans arbejdsområde."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Let's process this document, coming from our scanner."
msgstr "Lad os behandle dette dokument, der kommer fra vores scanner."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid ""
"Let's tag this mail as legal<br/> <i>Tips: actions can be tailored to your "
"process, according to the workspace.</i>"
msgstr ""
"Lad os mærke denne mail som lovlig<br/> <i>Tips: handlinger kan skræddersyes"
" til din proces i henhold til arbejdsområdet.</i>"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
#: code:addons/documents/static/src/views/documents_type_icon/documents_type_icon.xml:0
#: model_terms:ir.ui.view,arch_db:documents.documents_link_to_record_form_view
msgid "Link"
msgstr "Link"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_access_via_link_hidden
msgid "Link Access Hidden"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__access_via_link
msgid "Link Access Rights"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__url
msgid "Link URL"
msgstr "Link URL"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.js:0
msgid "Link copied to clipboard!"
msgstr "Link kopieret til udklipsholder."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.xml:0
msgid "Link copied to the clipboard."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "Linked To"
msgstr "Forbundet til"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.js:0
msgid "Links copied to clipboard!"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "Lokal-del baseret indgående detektion"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Lock"
msgstr "Lås"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_document__is_locked
msgid "Locked"
msgstr "Låst"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__lock_uid
msgid "Locked by"
msgstr "Låst af"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Log a note..."
msgstr "Log et notat..."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Login"
msgstr "Log ind"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Logout"
msgstr "Log ud"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Mail: %s"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Manage Versions"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.js:0
msgid "Manage Versions of \"%s\""
msgstr ""

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_remove_activities
msgid "Mark activities as completed"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Merge PDFs"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_note
msgid "Message"
msgstr "Besked"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_error
msgid "Message Delivery error"
msgstr "Besked ved leveringsfejl"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_ids
msgid "Messages"
msgstr "Beskeder"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "MiB"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__mimetype
msgid "Mime Type"
msgstr "Mime Type"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Missing documents reference."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__model_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Model"
msgstr "Model"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__accessible_model_ids
msgid "Models"
msgstr "Modeller"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__create_activity_date_deadline_range_type__months
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__months
msgid "Months"
msgstr "Måneder"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "More"
msgstr "Flere"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Move this page to trash"
msgstr "Flyt denne side til papirkurven"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Move to Trash"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_archive.js:0
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
#: code:addons/documents/static/src/views/hooks.js:0
#: model_terms:ir.ui.view,arch_db:documents.folder_deletion_form
msgid "Move to trash"
msgstr "Flyt til papirkurven"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
msgid "Move to trash?"
msgstr "Flyt til papirkurven?"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Moved to trash"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "Must have the link to access"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "My Activities"
msgstr "Mine aktiviteter"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Deadline på mine aktiviteter "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "My Documents"
msgstr "Mine dokumenter"

#. module: documents
#. odoo-javascript
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.js:0
#: code:addons/documents/static/src/views/kanban/documents_kanban_record.js:0
msgid "My Drive"
msgstr ""

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_access_role_or_last_access_date
msgid "NULL roles must have a set last_access_date"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__name
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__name
#: model:ir.model.fields,field_description:documents.field_documents_tag__name
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Name"
msgstr "Navn"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Name / Extension"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "New"
msgstr "Ny"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "New File"
msgstr "Ny fil"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.xml:0
msgid "New Folder"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "New Group"
msgstr "Ny Gruppe"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Næste aktivitet for kalenderarrangement"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Deadline for næste aktivitet"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_summary
msgid "Next Activity Summary"
msgstr "Oversigt over næste aktivitet"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_type_id
msgid "Next Activity Type"
msgstr "Næste aktivitetstype"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "No activity"
msgstr "Ingen aktivitet"

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.mail_activity_type_action_document
msgid "No activity types found. Let's create one!"
msgstr "Ingen aktivitetstyper fundet. Lad os oprette en!"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.xml:0
msgid "No alias configured"
msgstr "Intet alias er konfigureret"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "No document has been selected"
msgstr "Intet dokument er valgt"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/chatter/documents_chatter_overlay.xml:0
msgid "No item selected"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.js:0
msgid "No one on the internet can access"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/ir_actions_server.py:0
msgid "No server actions found for Documents!"
msgstr ""

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.mail_alias_action
msgid "No shared links"
msgstr "Ingen delte links"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__access_internal__none
#: model:ir.model.fields.selection,name:documents.selection__documents_document__access_via_link__none
#: model:ir.model.fields.selection,name:documents.selection__documents_document__user_permission__none
msgid "None"
msgstr "Ingen"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Not a file"
msgstr "Ikke en fil"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Not a folder."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Not attached"
msgstr "Ikke vedhæftet"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "Not set"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_note
msgid "Note"
msgstr "Notat"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Notify"
msgstr "Underret"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_needaction_counter
msgid "Number of Actions"
msgstr "Antal handlinger"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_error_counter
msgid "Number of errors"
msgstr "Antal fejl"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Antal meddelelser der kræver handling"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Antal beskeder med leveringsfejl"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Odoo Logo"
msgstr "Odoo logo"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Odoo Studio - Customize workflows in minutes"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Odoo Website"
msgstr "Odoo website"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__last_access_date_group__0_older
msgid "Older"
msgstr "Ældre"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Only Documents Managers can create in company folder."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Only Documents Managers can set aliases."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Only Documents managers can set an alias."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.js:0
msgid "Only people with access can open with the link"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Open #{document_url.name}"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Open folder #{subfolder.name}"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "Open the permissions panel"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"Operation not supported. Please use \"Move to Trash\" / `action_archive` "
"instead."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"Operation not supported. Please use \"Restore\" / `action_unarchive` "
"instead."
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"Valgfri ID for en tråd (record), som alle indgående meddelelser vil blive "
"vedhæftet, selvom de ikke svarede på det. Hvis opsat, vil dette fuldstændigt"
" deaktivere oprettelsen af nye poster."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.js:0
msgid "Optional message..."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.xml:0
msgid "Or send emails to"
msgstr "Eller send e-mails til"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_other
msgid "Other"
msgstr "Andet"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: code:addons/documents/static/src/components/documents_permission_panel/documents_partner_access.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_document__owner_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__requestee_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Owner"
msgstr "Ejer"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Owner: #{document.owner_id.name}"
msgstr "Ejer: #{document.owner_id.name}"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Owner: #{document_url.owner_id.name}"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Owner: #{subfolder.owner_id.name}"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_parent_model_id
msgid "Parent Model"
msgstr "Overordnet model"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__parent_path
msgid "Parent Path"
msgstr "Overordnet sti"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "Overordnet tråd (record) ID"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Overordnet model der holder aliaset. Modellen med aliasreferencen er ikke "
"nødvendigvis den model, der er givet af alias_model_id (eksempel: projekt "
"(parent_model) og opgave (model))."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "Partial transfer"
msgstr "Delvis overførsel"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_access__partner_id
msgid "Partner"
msgstr "Kontakt"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_partner_access.xml:0
msgid "People with access"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_pinned_folder
msgid "Pinned to Company roots"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.not_available
msgid ""
"Please login or contact the person that shared this link for more "
"information."
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Politik til at oprette en besked på dokumentet ved hjælp af mailgatewayen.\n"
"- alle: alle kan skrive\n"
"- partnere: kun godkendte partnere\n"
"- følgere: kun følgere af det relaterede dokument eller medlemmer af følgende kanaler\n"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__thumbnail_status__present
msgid "Present"
msgstr "Til stede"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_presentations
msgid "Presentations"
msgstr "Præsentationer"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_file
msgid "Preview files"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_tag_project
msgid "Project"
msgstr "Projekt"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Put in %s"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__rating_ids
msgid "Ratings"
msgstr "Bedømmelser"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Recent"
msgstr "Nye"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.js:0
msgid "Recently accessed Documents will show up here"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Recently accessed documents."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__resource_ref
msgid "Record"
msgstr "Datasæt"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_force_thread_id
msgid "Record Thread ID"
msgstr "Optag tråd ID"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Remaining Pages"
msgstr "Tilbageværende Sider"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/activity/documents_activity_controller.js:0
msgid "Reminder emails have been sent."
msgstr "Påmindelses e-mails er blevet sendt."

#. module: documents
#: model:mail.template,subject:documents.mail_template_document_request_reminder
msgid ""
"Reminder to upload your document{{ object.name and ' : ' + object.name or ''"
" }}"
msgstr ""
"Påmindelse om at uploade dit dokument{{ object.name and ' : ' + object.name "
"or '' }}"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_remove_partner_button.xml:0
msgid "Remove Access"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_remove_partner_button.xml:0
msgid "Remove Member"
msgstr ""

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_tag_remove_inbox
msgid "Remove Tag Inbox"
msgstr ""

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_tag_remove_to_validate
msgid "Remove Tag To Validate"
msgstr ""

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_remove_tags
msgid "Remove all tags"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_star.js:0
msgid "Remove star"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_rename.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_rename
msgid "Rename"
msgstr "Omdøb"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Request"
msgstr "Anmodning"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__request_activity_id
msgid "Request Activity"
msgstr "Anmod aktivitet"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Request To"
msgstr "Anmod "

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/web/activity/activity_menu_patch.xml:0
msgid "Request a Document"
msgstr "Anmod om et dokument"

#. module: documents
#: model:ir.actions.act_window,name:documents.action_request_form
msgid "Request a file"
msgstr "Anmod om en fil"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_request_page
msgid ""
"Requested\n"
"                                ∙"
msgstr ""
"Anmodet\n"
"                                ∙"

#. module: documents
#: model:mail.activity.type,name:documents.mail_documents_activity_data_md
msgid "Requested Document"
msgstr "Anmodet dokument"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Requested Documents"
msgstr "Anmodede Dokumenter"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__requestee_partner_id
msgid "Requestee Partner"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_model_name
msgid "Res Model Name"
msgstr "Res model navn"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__res_id
msgid "Resource ID"
msgstr "Ressource ID"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_model
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__res_model
msgid "Resource Model"
msgstr "Ressource model"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_name
msgid "Resource Name"
msgstr "Navn på ressourse"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_user_id
msgid "Responsible"
msgstr "Ansvarlig"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_user_id
msgid "Responsible User"
msgstr "Ansvarlig bruger"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Restore"
msgstr "Gendan"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Restricted"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Restricted Folder"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.xml:0
msgid "Revert changes"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_access__role
msgid "Role"
msgstr "Rolle"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS leveringsfejl"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_sales
msgid "Sales"
msgstr "Salg"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Save"
msgstr "Gem"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.js:0
msgid "Save or discard changes first"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.xml:0
msgid "Saving..."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Search people"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.js:0
msgid "Select a folder to upload a document"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/kanban/documents_kanban_renderer.js:0
#: code:addons/documents/static/src/views/list/documents_list_renderer.js:0
msgid "Select all"
msgstr "Vælg alle"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select focused page"
msgstr "Vælg fokuseret side"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select next page"
msgstr "Vælg næste side"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select next pages of the group"
msgstr "Vælg næste sider i gruppen"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select previous page"
msgstr "Vælg forrige side"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select previous pages of the group"
msgstr "Vælg tidligere sider i gruppen"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select/Deselect all pages"
msgstr "Vælg/fravælg alle sider"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Send"
msgstr "Send"

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_send_to_finance
msgid "Send To Finance"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Send this letter to the legal department, by assigning the right tags."
msgstr ""
"Send dette brev til en juridisk afdeling, ved at tildele de korrekte tags."

#. module: documents
#: model:mail.template,description:documents.mail_template_document_request
msgid "Sent to partner when requesting a document from them"
msgstr "Sendt til partner, når de anmoder om et dokument fra dem"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_tag__sequence
msgid "Sequence"
msgstr "Sekvens"

#. module: documents
#: model:ir.model,name:documents.model_ir_actions_server
msgid "Server Action"
msgstr "Server handling"

#. module: documents
#. odoo-python
#: code:addons/documents/models/ir_actions_server.py:0
msgid "Server Actions"
msgstr "Server handling"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Set deletion delay for documents in the Trash"
msgstr "Indstil sletningsforsinkelse for dokumenter i papirkurven"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_expiration_date_btn.xml:0
msgid "Set expiration date"
msgstr ""

#. module: documents
#: model:mail.template,description:documents.mail_template_document_request_reminder
msgid ""
"Set reminders in activities to notify users who didn't upload their "
"requested document"
msgstr ""
"Indstil påmindelser i aktiviteter for at underrette brugere, der ikke har "
"uploadet deres anmodede dokument"

#. module: documents
#: model:ir.actions.act_window,name:documents.configuration_action
#: model:ir.actions.act_window,name:documents.settings_action
#: model:ir.ui.menu,name:documents.settings_menu
msgid "Settings"
msgstr "Opsætning"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_share.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
#: code:addons/documents/static/src/views/search/documents_search_panel.xml:0
#: code:addons/documents/static/src/web/error_dialog/error_dialog_patch.xml:0
msgid "Share"
msgstr "Del"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.js:0
msgid "Share: %(documentName)s"
msgstr ""

#. module: documents
#. odoo-javascript
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.js:0
#: code:addons/documents/static/src/views/kanban/documents_kanban_record.js:0
msgid "Shared with me"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Sharing..."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__shortcut_ids
msgid "Shortcut"
msgstr "Genvej"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "Shortcut created"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.js:0
msgid "Shortcuts can only be created one at a time."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Shortcuts cannot change target document."
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_file
msgid "Size:"
msgstr "Størrelse:"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid "Some files could not be uploaded (max size: %s)."
msgstr "Nogle filer kunne ikke uploades (maks. størrelse: %s)."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__shortcut_document_id
msgid "Source Document"
msgstr "Kildedokument"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "Special folders cannot be shared"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Split"
msgstr "Del"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/attachments/document_file_viewer.xml:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Split PDF"
msgstr "Del PDF"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Split all white pages"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Split selected pages"
msgstr "Opdel valgte sider"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Starred"
msgstr "Stjernemarkeret"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status baseret på aktiviteter\n"
"Forfaldne: Forfaldsdato er allerede overskredet\n"
"I dag: Aktivitetsdato er i dag\n"
"Planlagt: Fremtidige aktiviteter."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "Stay here"
msgstr "Bliv her"

#. module: documents
#: model:ir.ui.menu,name:documents.structure
msgid "Structure"
msgstr "Struktur"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_summary
msgid "Summary"
msgstr "Opsummering"

#. module: documents
#: model:res.groups,name:documents.group_documents_system
msgid "System Administrator"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_tag
#: model:ir.model.fields,field_description:documents.field_mail_activity_type__tag_ids
#: model_terms:ir.ui.view,arch_db:documents.tag_view_search
msgid "Tag"
msgstr "Tag"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Tag Name"
msgstr "Tag-navn"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_tag_tag_name_unique
msgid "Tag name already used"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model:ir.actions.act_window,name:documents.tag_action
#: model:ir.model.fields,field_description:documents.field_documents_document__tag_ids
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__tag_ids
#: model:ir.ui.menu,name:documents.category_menu
msgid "Tags"
msgstr "Tags"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_text
msgid "Text"
msgstr "Tekst"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_tag__tooltip
msgid "Text shown when hovering on this tag"
msgstr ""

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_res_config_settings_check_deletion_delay
msgid "The deletion delay should be positive."
msgstr "Sletningsforsinkelsen skal være positiv."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/web/error_dialog/error_dialog_patch.js:0
#: code:addons/documents/static/src/web/error_dialog/error_dialog_patch.xml:0
msgid "The document URL has been copied to your clipboard."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid "The document has been moved."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"The following documents can't have alias: \n"
"- %(records)s"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"The following documents/folders can't be owned by a Portal User: \n"
"- %(partners)s"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "The following documents/shortcuts have a company mismatch: \n"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "The following documents/shortcuts have a type mismatch: \n"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "The following shortcuts cannot be set as documents parents: \n"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"Modellen (Odoo Document Kind), som dette alias svarer til. Enhver indgående "
"e-mail, der ikke svarer til en eksisterende post, vil medføre oprettelse af "
"en ny rekord af denne model (fx en projektopgave)"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"Navnet på e-mail aliaset, f.eks. 'jobs', hvis du vil fange e-mails til "
"<<EMAIL>>"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__last_access_date_group__1_month
msgid "This Month"
msgstr "Denne måned"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__last_access_date_group__2_week
msgid "This Week"
msgstr "Denne uge"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "This action does not exist."
msgstr ""

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_document_attachment_unique
msgid "This attachment is already a document"
msgstr "Denne vedhæftning er allerede et dokument"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.not_available
msgid "This document does not exist or is not publicly available."
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_request_page
msgid ""
"This document has been requested.\n"
"                <b onclick=\"document.querySelector('.o_request_upload').click()\" style=\"cursor:pointer;\">Upload it</b>."
msgstr ""
"Dette dokument er blevet anmodet om.\n"
"                <b onclick=\"document.querySelector('.o_request_upload').click()\" style=\"cursor:pointer;\">Upload det</b>."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "This document has been restored."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"This file has been sent to the trash and will be deleted forever on the %s"
msgstr ""
"Denne fil er blevet sendt til papirkurven og vil blive slettet for altid på "
"den %s"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "This folder does not exist or is not accessible."
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "This is a folder"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.xml:0
msgid "This is a shortcut"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "This is a shortcut - Click to access source document"
msgstr ""

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_access_unique_document_access_partner
msgid "This partner is already set on this document."
msgstr ""

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.mail_activity_type_action_document
msgid ""
"Those represent the different categories of things you have to do (e.g. "
"\"Call\" or \"Send email\")."
msgstr ""
"Disse repræsenterer de forskellige kategorier af ting, du skal gøre (f.eks. "
"\"Ring\" eller \"Send e-mail\")."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__thumbnail
msgid "Thumbnail"
msgstr "Thumbnail"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__thumbnail_status
msgid "Thumbnail Status"
msgstr "Status for miniaturebilleder"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.xml:0
msgid "Tip: configure your scanner to send all documents to this address."
msgstr ""
"Tip: Konfigurer din scanner til at sende alle dokumenter til denne adresse."

#. module: documents
#: model:digest.tip,name:documents.digest_tip_documents_0
msgid "Tip: Become a paperless company"
msgstr "Råd: Bliv en papirløs virksomhed"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_to_validate
msgid "To Validate"
msgstr "Til Bekræftelse"

#. module: documents
#: model:mail.activity.type,name:documents.mail_documents_activity_data_tv
msgid "To validate"
msgstr "Til validering"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__last_access_date_group__3_day
msgid "Today"
msgstr "I dag"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/kanban/documents_kanban_renderer.js:0
msgid "Toggle favorite"
msgstr "Aktiver favorit"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_tag__tooltip
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Tooltip"
msgstr "Værktøjstip"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Trash"
msgstr "Skrald"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Trash Management"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__is_editable_attachment
msgid "True if we can edit the link attachment."
msgstr "True hvis vi kan redigere linkvedhæftningen."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__type
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Type"
msgstr "Type"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type af undtagelsesaktivitet registreret "

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__url
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "URL"
msgstr "URL"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"URL %s does not seem complete, as it does not begin with http(s):// or "
"ftp://"
msgstr ""
"URL %s virker ikke komplet, da den ikke begynder med http(s):// eller ftp://"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__url_preview_image
msgid "URL Preview Image"
msgstr "URL forhåndsvisningsbillede"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Unavailable action."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Unlock"
msgstr "Lås op"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "Unnamed"
msgstr "Ikke navngivet"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Upload"
msgstr "Upload"

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.document_action
#: model_terms:ir.actions.act_window,help:documents.document_action_portal
msgid ""
"Upload <span class=\"fw-normal\">a file or</span> drag <span class=\"fw-"
"normal\">it here.</span>"
msgstr ""
"Upload <span class=\"fw-normal\">en fil eller</span> træk <span class=\"fw-"
"normal\">den her.</span>"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.xml:0
msgid "Upload New Version"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/mail_activity.py:0
msgid "Upload file request"
msgstr "Upload filanmodning"

#. module: documents
#: model:ir.model,name:documents.model_res_users
#: model:res.groups,name:documents.group_documents_user
msgid "User"
msgstr "Bruger"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
#: code:addons/documents/static/src/components/documents_permission_panel/documents_partner_access.xml:0
msgid "User Access Role"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__user_permission
msgid "User permission"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_tag_vat
msgid "VAT"
msgstr "Moms"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_validated
msgid "Validated"
msgstr "Valideret"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.xml:0
msgid ""
"Versions are displayed in the order they were uploaded to Documents.\n"
"                    If you delete the most recent version, the previous one is automatically restored as the current one."
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_tag_videos
msgid "Videos"
msgstr "Videoer"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: model:ir.model.fields.selection,name:documents.selection__documents_access__role__view
#: model:ir.model.fields.selection,name:documents.selection__documents_document__access_internal__view
#: model:ir.model.fields.selection,name:documents.selection__documents_document__access_via_link__view
#: model:ir.model.fields.selection,name:documents.selection__documents_document__user_permission__view
msgid "Viewer"
msgstr "Viser"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid ""
"Want to become a <b>paperless company</b>? Let's discover Odoo Documents."
msgstr ""
"Vil du blive en <b>papirløs virksomhed</b>? Lad os udforske Odoo Dokumenter."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__website_message_ids
msgid "Website Messages"
msgstr "Beskeder fra hjemmesiden"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__website_message_ids
msgid "Website communication history"
msgstr "Hjemmesidens kommunikations historik"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__create_activity_date_deadline_range_type__weeks
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__weeks
msgid "Weeks"
msgstr "Uger"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "What do you want to do with the remaining pages ?"
msgstr "Hvad vil du med de resterende sider?"

#. module: documents
#: model_terms:web_tour.tour,rainbow_man_message:documents.documents_tour
msgid ""
"Wow... 6 documents processed in a few seconds, You're good.<br>The tour is "
"complete. Try uploading your own documents now."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_partner_access.xml:0
msgid "You"
msgstr "Dig"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to (un)archive documents."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to change ownerships of documents you do not own."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to delete all these items."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to execute embedded actions."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to move (some of) these documents."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to pin/unpin embedded Actions."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to read the permission panel data."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to write in this folder."
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__attachment_type
msgid ""
"You can either upload a file from your computer or copy/paste an internet "
"link to your file."
msgstr ""
"Du kan enten overføre en fil fra din computer eller kopiere/indsætte en "
"internethenvisning i din fil."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_pin_actions.js:0
msgid "You can not pin actions for that folder."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"You can not update the access of a shortcut, update its target instead."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You can't access that folder_id."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid ""
"You can't create shortcuts in or move documents to this special folder."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You can't move documents out of folders you cannot edit."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You cannot change the owner of documents you do not own."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_tag.py:0
msgid "You cannot delete tags used in server actions."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You cannot delete this attachment."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You cannot duplicate document(s) in the Trash."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "You cannot move folders or files when in the trash."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "You cannot move request in the company folder"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You cannot pin an action on that document."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You cannot pin that type of action."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_remove_partner_button.xml:0
msgid "You cannot remove this partner"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "You cannot share multiple documents at the same time"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You do not have sufficient access rights to delete these documents."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid ""
"You don't have the rights to move documents nor create shortcut to that "
"folder."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_drop_zone.xml:0
msgid "You must be in a specific writable workspace to upload files"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Your Role: %s"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Your individual space."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.js:0
msgid "Your personal space"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_details
msgid "alias"
msgstr "alias"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "bytes"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_deletion_form
msgid "days."
msgstr "dage."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.js:0
msgid "document"
msgstr "dokument"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "documents"
msgstr "dokumenter"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "e.g. Discuss proposal"
msgstr "f.eks. drøft forslag"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_new_folder
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_rename
msgid "e.g. Finance"
msgstr "f.eks. Finans"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "e.g. Missing Expense"
msgstr "f.eks. Manglende udgift"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "e.g. To Validate"
msgstr "f.eks. At validere"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "e.g. https://www.youtube.com/watch?v=CP96yVnXNrY"
msgstr "f.eks. https://www.youtube.com/watch?v=CP96yVnXNrY"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_details
msgid "e.g. mycompany.com"
msgstr "f.eks. mitfirma.com"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "files"
msgstr "filer"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.js:0
msgid "folder"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "folders,"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__is_readonly_model
msgid "is_readonly_model"
msgstr "is_readonly_model"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/chatter/documents_chatter_overlay.xml:0
msgid "items selected"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "items,"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "kiB"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_page/pdf_page.xml:0
msgid "select"
msgstr "vælg"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.mail_template_document_share
msgid "shared a document with you.<br/>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.mail_template_document_share
msgid "shared a folder with you.<br/>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "shared by"
msgstr "delt af"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.mail_template_document_share
msgid "shared this document with you:"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.mail_template_document_share
msgid "shared this folder with you:"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "status code: %(status)s, message: %(message)s"
msgstr "status kode: %(status)s, besked: %(message)s"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "sub-doc-%s"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "unnamed"
msgstr "unavngivet"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid ""
"•\n"
"                                        <b>URL</b>"
msgstr ""
