# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents
# 
# Translators:
# <PERSON>, 2024
# <PERSON>, 2025
# <PERSON><PERSON>, 2025
# Wil <PERSON>, 2025
# <PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-26 20:44+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid " (%s locked)"
msgstr " (%s vergrendeld)"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "%s (copy)"
msgstr "%s (kopie)"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "%s (remaining pages)"
msgstr "%s (Resterende pagina's"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "%s Files"
msgstr "%s Bestanden"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "%s Folders"
msgstr "%s Mappen"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/list/document_count_field.js:0
msgid "%s documents"
msgstr "%s documenten"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid "%s documents have been moved."
msgstr "%s documenten zijn verplaatst."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "%s file(s) not moved because they are locked by another user"
msgstr ""
"%s bestand(en) niet verplaatst omdat ze zijn vergrendeld door een andere "
"gebruiker"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.js:0
msgid "%s has been copied in My Drive."
msgstr "%s is gekopieerd naar Mijn Drive."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.js:0
msgid "%s have been copied in My Drive."
msgstr "%s zijn gekopieerd naar Mijn Drive."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "%s new document(s) created"
msgstr "%s nieuwe documenten aangemaakt"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "%s page(s) deleted"
msgstr "%s pagina's verwijderd"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "%s shared with you"
msgstr "%s met jou gedeeld"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid "%s shortcuts have been created."
msgstr "%s snelkoppelingen zijn gemaakt."

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid ""
". Scanned files will appear automatically in your workspace. Then, process "
"your documents in bulk with the split tool: launch user defined actions, "
"request a signature, convert to vendor bills with AI, etc."
msgstr ""
". Gescande bestanden verschijnen automatisch in je werkruimte. Verwerk "
"vervolgens je documenten in bulk met de splitstool: start door de gebruiker "
"gedefinieerde acties, vraag een handtekening, zet om naar "
"leveranciersfacturen met AI, enz."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.mail_template_document_share
msgid ".<br/>"
msgstr ".<br/>"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "1 File"
msgstr "1 Bestand"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "1 Folder"
msgstr "1 map"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/list/document_count_field.js:0
msgid "1 document"
msgstr "1 document"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_year_previous
msgid "2024"
msgstr "2024"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_year_current
msgid "2025"
msgstr "2025"

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid "<b class=\"tip_title\">Tip: Become a paperless company</b>"
msgstr "<b class=\"tip_title\">Tip: Word een papierloos bedrijf</b>"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "<b>Deselect this page</b> as we plan to process all bills first."
msgstr ""
"<b>Schakel deze pagina uit</b>, aangezien we van plan zijn om eerst alle "
"facturen te verwerken."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "<b>Select</b> this page to continue."
msgstr "<b>Selecteer</b> deze pagina om verder te gaan."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "<br/>Powered by"
msgstr "<br/>Aangeboden door"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "<i class=\"fa fa-download fa-fw\"/> Download All"
msgstr "<i class=\"fa fa-download fa-fw\"/> Alles downloaden"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_file
msgid "<i class=\"fa fa-download fa-fw\"/> Download file"
msgstr "<i class=\"fa fa-download fa-fw\"/> Bestand downloaden"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<i class=\"fa fa-external-link me-2\" title=\"This is a shortcut\"/>"
msgstr "<i class=\"fa fa-external-link me-2\" title=\"This is a shortcut\"/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<i class=\"fa fa-external-link text-gray-500\" title=\"This is a shortcut\"/>"
msgstr ""
"<i class=\"fa fa-external-link text-gray-500\" title=\"Dit is een "
"snelkoppeling\"/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_file
msgid "<i class=\"fa fa-eye\"/> Preview file"
msgstr "<i class=\"fa fa-eye\"/> Bestand bekijken"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "<i class=\"fa fa-lock oe_inline\" title=\"Locked\" invisible=\"not lock_uid\"/>"
msgstr ""
"<i class=\"fa fa-lock oe_inline\" title=\"Vergrendeld\" invisible=\"not "
"lock_uid\"/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "<i class=\"fa fa-upload\"/>  Upload"
msgstr "<i class=\"fa fa-upload\"/>  Uploaden"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_file
msgid "<i class=\"fa fa-upload\"/> Replace file"
msgstr "<i class=\"fa fa-upload\"/> Bestand vervangen"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_document_res_partner_view
msgid "<span class=\"o_stat_text\">Documents</span>"
msgstr "<span class=\"o_stat_text\">Documenten</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "<span class=\"o_stat_text\">Related <br/> Record</span>"
msgstr "<span class=\"o_stat_text\">Gerelateerd <br/> Record</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "<span title=\"Requested Document\">Requested Document</span>"
msgstr "<span title=\"Gevraagde documente\">Gevraagde documenten</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "<span>&amp;nbsp;Documents.</span>"
msgstr "<span>&amp;nbsp;Documenten.</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<span><b> Request</b></span>"
msgstr "<span><b> Aanvraag</b></span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<span><b>Requested Document</b></span>"
msgstr "<span><b>Gevraagde Documenten</b></span>"

#. module: documents
#: model:mail.template,body_html:documents.mail_template_document_request
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"                    <tbody>\n"
"                        <!-- HEADER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\">\n"
"                                        <span style=\"font-size: 10px;\">\n"
"                                            Document Request: <br/>\n"
"                                            <t t-if=\"object.name\">\n"
"                                                <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Inbox Financial</span>\n"
"                                            </t>\n"
"                                        </span><br/>\n"
"                                    </td><td valign=\"middle\" align=\"right\" t-if=\"not object.create_uid.company_id.uses_default_logo\">\n"
"                                        <img t-attf-src=\"/logo.png?company={{ object.create_uid.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.create_uid.company_id.name\"/>\n"
"                                    </td></tr>\n"
"                                    <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- CONTENT -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                        <div>\n"
"                                            Hello <t t-out=\"object.owner_id.name or ''\">OdooBot</t>,\n"
"                                            <br/><br/>\n"
"                                            <t t-out=\"object.create_uid.name or ''\">OdooBot</t> (<t t-out=\"object.create_uid.email or ''\"><EMAIL></t>) asks you to provide the following document:\n"
"                                            <br/><br/>\n"
"                                            <center>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.name\">\n"
"                                                        <b t-out=\"object.name or ''\">Inbox Financial</b>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.request_activity_id.note\">\n"
"                                                        <i t-out=\"object.request_activity_id.note or ''\">Example of a note.</i>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <br/>\n"
"                                                <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                    <a t-att-href=\"object.access_url\" style=\"background-color: #875A7B; padding: 20px 30px 20px 30px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                        Upload the requested document\n"
"                                                    </a>\n"
"                                                </div>\n"
"                                            </center><br/>\n"
"                                            Please provide us with the missing document before <t t-out=\"object.request_activity_id.date_deadline\">2021-05-17</t>.\n"
"                                            <t t-if=\"user and user.signature\" data-o-mail-quote-container=\"1\">\n"
"                                                <br/>\n"
"                                                <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                                                <br data-o-mail-quote=\"1\"/>\n"
"                                            </t>\n"
"                                        </div>\n"
"                                    </td></tr>\n"
"                                    <tr><td style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- FOOTER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\" align=\"left\">\n"
"                                        <t t-out=\"object.create_uid.company_id.name or ''\">YourCompany</t>\n"
"                                    </td></tr>\n"
"                                    <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                                        <t t-out=\"object.create_uid.company_id.phone or ''\">******-123-4567</t>\n"
"                                        <t t-if=\"object.create_uid.company_id.email\">\n"
"                                            | <a t-attf-href=\"'mailto:%s' % {{ object.create_uid.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.email or ''\"><EMAIL></a>\n"
"                                        </t>\n"
"                                        <t t-if=\"object.create_uid.company_id.website\">\n"
"                                            | <a t-attf-href=\"'%s' % {{ object.create_uid.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.website or ''\">http://www.example.com</a>\n"
"                                        </t>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                    </table>\n"
"                    </td></tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                          <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"                            Powered by <a target=\"_blank\" href=\"https://www.odoo.com/app/documents\" style=\"color: #875A7B;\">Odoo Documents</a>\n"
"                          </td></tr>\n"
"                        </table>\n"
"                    </td></tr>\n"
"                </table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"                    <tbody>\n"
"                        <!-- HEADER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\">\n"
"                                        <span style=\"font-size: 10px;\">\n"
"                                            Verzoek om documenten: <br/>\n"
"                                            <t t-if=\"object.name\">\n"
"                                                <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Inbox Financieel</span>\n"
"                                            </t>\n"
"                                        </span><br/>\n"
"                                    </td><td valign=\"middle\" align=\"right\" t-if=\"not object.create_uid.company_id.uses_default_logo\">\n"
"                                        <img t-attf-src=\"/logo.png?company={{ object.create_uid.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.create_uid.company_id.name\"/>\n"
"                                    </td></tr>\n"
"                                    <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- CONTENT -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                        <div>\n"
"                                            Hallo <t t-out=\"object.owner_id.name or ''\">OdooBot</t>,\n"
"                                           <br/><br/>\n"
"                                           <t t-out=\"object.create_uid.name or ''\">OdooBot</t><t t-out=\"object.create_uid.email or ''\">(<EMAIL>)</t> vraagt je om het volgende document:\n"
"                                           <br/><br/>\n"
"                                            <center>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.name\">\n"
"                                                        <b t-out=\"object.name or ''\">Inbox Financieel</b>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.request_activity_id.note\">\n"
"                                                        <i t-out=\"object.request_activity_id.note or ''\">Voorbeeld van een notitie.</i>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <br/>\n"
"                                                <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                    <a t-att-href=\"object.access_url\" style=\"background-color: #875A7B; padding: 20px 30px 20px 30px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                        Upload het gevraagde document\n"
"                                                    </a>\n"
"                                                </div>\n"
"                                            </center><br/>\n"
"                                            Geef ons het ontbrekende document vóór <t t-out=\"object.request_activity_id.date_deadline\">2021-05-17</t>.\n"
"                                            <t t-if=\"user and user.signature\" data-o-mail-quote-container=\"1\">\n"
"                                                <br/>\n"
"                                                <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                                                <br data-o-mail-quote=\"1\"/>\n"
"                                            </t>\n"
"                                        </div>\n"
"                                    </td></tr>\n"
"                                    <tr><td style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- FOOTER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\" align=\"left\">\n"
"                                        <t t-out=\"object.create_uid.company_id.name or ''\">UwBedrijf</t>\n"
"                                    </td></tr>\n"
"                                    <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                                        <t t-out=\"object.create_uid.company_id.phone or ''\">******-123-4567</t>\n"
"                                        <t t-if=\"object.create_uid.company_id.email\">\n"
"                                            | <a t-attf-href=\"'mailto:%s' % {{ object.create_uid.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.email or ''\">info@y</a>ourcompany.com\n"
"                                        </t>\n"
"                                        <t t-if=\"object.create_uid.company_id.website\">\n"
"                                            | <a t-attf-href=\"'%s' % {{ object.create_uid.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.website or ''\">http:/</a>/www.example.com\n"
"                                        </t>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                    </table>\n"
"                    </td></tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                          <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"                            Aangedreven door <a target=\"_blank\" href=\"https://www.odoo.com/app/documents\" style=\"color: #875A7B;\">Odoo Documenten</a>\n"
"                          </td></tr>\n"
"                        </table>\n"
"                    </td></tr>\n"
"                </table>\n"
"            "

#. module: documents
#: model:mail.template,body_html:documents.mail_template_document_request_reminder
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"                    <tbody>\n"
"                        <!-- HEADER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\">\n"
"                                        <span style=\"font-size: 10px;\">\n"
"                                            Document Request: <br/>\n"
"                                            <t t-if=\"object.name\">\n"
"                                                <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Inbox Financial</span>\n"
"                                            </t>\n"
"                                        </span><br/>\n"
"                                    </td><td valign=\"middle\" align=\"right\">\n"
"                                        <img t-attf-src=\"/logo.png?company={{ object.create_uid.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.create_uid.company_id.name\"/>\n"
"                                    </td></tr>\n"
"                                    <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- CONTENT -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                        <div>\n"
"                                            Hello <t t-out=\"object.owner_id.name or ''\">OdooBot</t>,\n"
"                                            <br/><br/>\n"
"                                            This is a friendly reminder to upload your requested document:\n"
"                                            <br/><br/>\n"
"                                            <center>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.name\">\n"
"                                                        <b t-out=\"object.name or ''\">Inbox Financial</b>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.request_activity_id.note\">\n"
"                                                        <i t-out=\"object.request_activity_id.note or ''\">Example of a note.</i>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <br/>\n"
"                                                <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                    <a t-att-href=\"object.access_url\" style=\"background-color: #875A7B; padding: 20px 30px 20px 30px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                        Upload the requested document\n"
"                                                    </a>\n"
"                                                </div>\n"
"                                            </center><br/>\n"
"                                            Please provide us with the missing document before <t t-out=\"object.request_activity_id.date_deadline or ''\">2021-05-17</t>.\n"
"                                            <br/><br/>\n"
"                                            Thank you,\n"
"                                            <t t-if=\"user and user.signature\" data-o-mail-quote-container=\"1\">\n"
"                                                <br/>\n"
"                                                <t t-out=\"user.signature\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                                                <br data-o-mail-quote=\"1\"/>\n"
"                                            </t>\n"
"                                        </div>\n"
"                                    </td></tr>\n"
"                                    <tr><td style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- FOOTER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\" align=\"left\">\n"
"                                        <t t-out=\"object.create_uid.company_id.name or ''\">YourCompany</t>\n"
"                                    </td></tr>\n"
"                                    <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                                        <t t-out=\"object.create_uid.company_id.phone or ''\">******-123-4567</t>\n"
"                                        <t t-if=\"object.create_uid.company_id.email\">\n"
"                                            | <a t-attf-href=\"'mailto:%s' % {{ object.create_uid.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.email\"><EMAIL></a>\n"
"                                        </t>\n"
"                                        <t t-if=\"object.create_uid.company_id.website\">\n"
"                                            | <a t-att-href=\"object.create_uid.company_id.website\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.website\">http://www.example.com</a>\n"
"                                        </t>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                    </table>\n"
"                    </td></tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                          <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"                            Powered by <a target=\"_blank\" href=\"https://www.odoo.com/app/documents\" style=\"color: #875A7B;\">Odoo Documents</a>\n"
"                          </td></tr>\n"
"                        </table>\n"
"                    </td></tr>\n"
"                </table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"                    <tbody>\n"
"                        <!-- HEADER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\">\n"
"                                        <span style=\"font-size: 10px;\">\n"
"                                            Verzoek om documenten: <br/>\n"
"                                            <t t-if=\"object.name\">\n"
"                                                <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Inbox Financieel</span>\n"
"                                            </t>\n"
"                                        </span><br/>\n"
"                                    </td><td valign=\"middle\" align=\"right\">\n"
"                                        <img t-attf-src=\"/logo.png?company={{ object.create_uid.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.create_uid.company_id.name\"/>\n"
"                                    </td></tr>\n"
"                                    <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- CONTENT -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                        <div>\n"
"                                            Hallo <t t-out=\"object.owner_id.name or ''\">OdooBot</t>,\n"
"                                           <br/><br/>\n"
"                                            Dit is een vriendelijke herinnering om je gevraagde document te uploaden:\n"
"                                           <br/><br/>\n"
"                                            <center>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.name\">\n"
"                                                        <b t-out=\"object.name or ''\">Inbox Financieel</b>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.request_activity_id.note\">\n"
"                                                        <i t-out=\"object.request_activity_id.note or ''\">Voorbeeld van een notitie.</i>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <br/>\n"
"                                                <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                    <a t-att-href=\"object.access_url\" style=\"background-color: #875A7B; padding: 20px 30px 20px 30px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                        Upload het gevraagde document\n"
"                                                    </a>\n"
"                                                </div>\n"
"                                            </center><br/>\n"
"                                            Geef ons het ontbrekende document vóór <t t-out=\"object.request_activity_id.date_deadline or ''\">2021-05-17</t>.\n"
"                                           <br/><br/>\n"
"                                            Hartelijk dank,\n"
"                                            <t t-if=\"user and user.signature\" data-o-mail-quote-container=\"1\">\n"
"                                                <br/>\n"
"                                                <t t-out=\"user.signature\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                                                <br data-o-mail-quote=\"1\"/>\n"
"                                            </t>\n"
"                                        </div>\n"
"                                    </td></tr>\n"
"                                    <tr><td style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- FOOTER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\" align=\"left\">\n"
"                                        <t t-out=\"object.create_uid.company_id.name or ''\">UwBedrijf</t>\n"
"                                    </td></tr>\n"
"                                    <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                                        <t t-out=\"object.create_uid.company_id.phone or ''\">******-123-4567</t>\n"
"                                        <t t-if=\"object.create_uid.company_id.email\">\n"
"                                            | <a t-attf-href=\"'mailto:%s' % {{ object.create_uid.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.email\">info@y</a>ourcompany.com\n"
"                                        </t>\n"
"                                        <t t-if=\"object.create_uid.company_id.website\">\n"
"                                            | <a t-att-href=\"object.create_uid.company_id.website\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.website\">http:/</a>/www.example.com\n"
"                                        </t>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                    </table>\n"
"                    </td></tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                          <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"                            Aangedreven door <a target=\"_blank\" href=\"https://www.odoo.com/app/documents\" style=\"color: #875A7B;\">Odoo Documenten</a>\n"
"                          </td></tr>\n"
"                        </table>\n"
"                    </td></tr>\n"
"                </table>\n"
"            "

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Een Python bibliotheek dat zal worden gebruikt om standaardwaarden te bieden"
" bij het maken van nieuwe records voor deze alias."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "A destination is required when creating multiple shortcuts at once."
msgstr ""
"Een bestemming is nodig als je meerdere snelkoppelingen tegelijk maakt."

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_document_folder_id_not_id
msgid "A folder cannot be included in itself"
msgstr "Een map kan niet in zichzelf worden opgenomen"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_document_shortcut_document_id_not_id
msgid "A shortcut cannot point to itself"
msgstr "Een snelkoppeling kan niet naar zichzelf wijzen"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid "A shortcut has been created."
msgstr "Er is een snelkoppeling gemaakt."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Access"
msgstr "Toegang"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "Access Error"
msgstr "Toegangsfout"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__access_token
#: model:ir.model.fields,field_description:documents.field_documents_redirect__access_token
msgid "Access Token"
msgstr "Toegangstoken"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.portal_my_home_documents
msgid "Access all your documents"
msgstr "Toegang tot al je documenten"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_access.py:0
msgid "Access documents and partners cannot be changed."
msgstr "Toegang tot documenten en partners kan niet worden gewijzigd."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Access to Automations"
msgstr "Toegang automatiseringen"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid "Access to Server Actions"
msgstr "Toegang serveracties"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Access to a folder or a document"
msgstr "Toegang tot een map of een document"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_document_document_token_unique
msgid "Access tokens already used."
msgstr "Toegangstokens die al gebruikt zijn."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__access_url
msgid "Access url"
msgstr "Toegang url"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Action"
msgstr "Actie"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_needaction
msgid "Action Needed"
msgstr "Actie gevraagd"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_pin_actions.xml:0
msgid "Actions on Select"
msgstr "Acties bij selecteren"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__active
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_new_folder
msgid "Active"
msgstr "Actief"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_ids
#: model:ir.ui.menu,name:documents.mail_activities
msgid "Activities"
msgstr "Activiteiten"

#. module: documents
#: model:ir.model,name:documents.model_mail_activity
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Activity"
msgstr "Activiteit"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Activiteit uitzondering decoratie"

#. module: documents
#: model:ir.ui.menu,name:documents.mail_activity_plan_menu
msgid "Activity Plans"
msgstr "Activiteitenplan"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_state
msgid "Activity State"
msgstr "Activiteitsfase"

#. module: documents
#: model:ir.model,name:documents.model_mail_activity_type
msgid "Activity Type"
msgstr "Soort activiteit"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_type_icon
msgid "Activity Type Icon"
msgstr "Activiteitensoort icoon"

#. module: documents
#: model:ir.actions.act_window,name:documents.mail_activity_type_action_document
#: model:ir.ui.menu,name:documents.mail_activity_type_menu
msgid "Activity Types"
msgstr "Activiteitensoorten"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "Activity assigned to"
msgstr "Activiteit toegewezen aan"

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.mail_activity_plan_action_document
msgid ""
"Activity plans are used to assign a list of activities in just a few clicks\n"
"                    (e.g. \"NDA signature process\", \"Workspace workflow\", ...)"
msgstr ""
"Activiteitenplannen worden gebruikt om een lijst met activiteiten toe te wijzen in slechts een paar klikken\n"
"                    (bijv. 'NDA-ondertekeningsproces', 'Workflow werkruimte', ...)"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_type_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_type_id
msgid "Activity type"
msgstr "Soort activiteit"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "Add"
msgstr "Toevoegen"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_pin_actions.xml:0
msgid "Add Custom Action"
msgstr "Aangepaste actie toevoegen"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Add File"
msgstr "Bestand toevoegen"

#. module: documents
#: model:ir.actions.act_window,name:documents.action_folder_form
msgid "Add Folder"
msgstr "Map toevoegen"

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_tag_add_bill
msgid "Add Tag Bill"
msgstr "Label leveranciersfactuur toevoegen"

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_tag_add_validated
msgid "Add Tag Validated"
msgstr "Label toevoegen Gevalideerd"

#. module: documents
#: model:ir.actions.act_window,name:documents.action_url_form
msgid "Add Url"
msgstr "URL Toevoegen"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "Add a tag..."
msgstr "Een label toevoegen..."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "Add an alias tag..."
msgstr "Een aliaslabel toevoegen..."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Add new file"
msgstr "Nieuw bestand toevoegen"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Add people or email addresses..."
msgstr "Mensen of e-mailadressen toevoegen..."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Add people or email addresses:"
msgstr "Mensen of e-mailadressen toevoegen:"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_shortcut.js:0
msgid "Add shortcut"
msgstr "Snelkoppeling toevoegen"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_star.js:0
msgid "Add star"
msgstr "Maak favoriet"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Additional documents you have access to."
msgstr "Aanvullende documenten waartoe je toegang hebt."

#. module: documents
#: model:res.groups,name:documents.group_documents_manager
msgid "Administrator"
msgstr "Beheerder"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_ads
msgid "Ads"
msgstr "Advertenties"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_id
msgid "Alias"
msgstr "Alias"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_contact
msgid "Alias Contact Security"
msgstr "Alias contact beveiliging"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_domain_id
msgid "Alias Domain"
msgstr "Aliasdomein"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_domain
msgid "Alias Domain Name"
msgstr "Aliasdomeinnaam"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_full_name
msgid "Alias Email"
msgstr "E-mailalias"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_name
msgid "Alias Name"
msgstr "Aliasnaam"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_status
msgid "Alias Status"
msgstr "Status alias"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_tag_ids
msgid "Alias Tags"
msgstr "Alias-labels"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_status
msgid "Alias status assessed on the last message received."
msgstr "Status alias beoordeeld op het laatste ontvangen bericht."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_model_id
msgid "Aliased Model"
msgstr "Aliased model"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.js:0
msgid ""
"All users with access to this %(documentType)s or its parent will have edit "
"permissions."
msgstr ""
"Alle gebruikers met toegang tot deze %(documentType)s of zijn ouder hebben "
"bewerkingsrechten."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__access_ids
msgid "Allowed Access"
msgstr "Toegestane toegang"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Already linked Documents: %s"
msgstr "Reeds gekoppelde documenten: %s"

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid ""
"An easy way to process incoming mails is to configure your scanner to send "
"PDFs to"
msgstr ""
"Een gemakkelijke manier om inkomende e-mails te verwerken, is door een "
"scanner te configureren om PDF's te verzenden naar"

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid ""
"An easy way to process incoming mails is to configure your scanner to send "
"PDFs to your workspace email. Scanned files will appear automatically in "
"your workspace. Then, process your documents in bulk with the split tool: "
"launch user defined actions, request a signature, convert to vendor bills "
"with AI, etc."
msgstr ""
"Een eenvoudige manier om inkomende e-mails te verwerken, is door je scanner "
"zo te configureren dat deze PDF's naar je werkruimte-e-mail verzendt. "
"Gescande bestanden verschijnen automatisch in je werkruimte. Verwerk "
"vervolgens je documenten in bulk met de splitstool: start door de gebruiker "
"gedefinieerde acties, vraag een handtekening aan, zet om naar "
"leveranciersfacturen met AI, etc."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "An error occured while uploading."
msgstr "Er is een fout opgetreden tijdens het uploaden."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.js:0
msgid "Anyone on the internet with the link can edit"
msgstr "Iedereen op internet met de link kan bewerken"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.js:0
msgid "Anyone on the internet with the link can view"
msgstr "Iedereen op internet met de link kan het bekijken"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.xml:0
msgid "Anyone with the link"
msgstr "Iedereen met de link"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Archive original file(s)"
msgstr "Archiveer originele bestand(en)"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Are you sure that you want to delete the focused page ?"
msgstr "Weet je zeker dat je de gerichte pagina wilt verwijderen?"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Are you sure that you want to delete the selected page(s)"
msgstr "Weet je zeker dat je de geselecteerde pagina('s) wilt verwijderen?"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Are you sure that you want to delete this page ?"
msgstr "Weet je zeker dat je deze pagina wilt verwijderen?"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.js:0
msgid "Are you sure you want to delete this attachment?"
msgstr "Weet je zeker dat je deze bijlage wilt verwijderen?"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Are you sure you want to permanently erase the document?"
msgstr "Weet je zeker dat je dit document definitief wilt wissen?"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Are you sure you want to permanently erase the documents?"
msgstr "Weet je zeker dat je deze documenten definitief wilt wissen?"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid ""
"As this PDF contains multiple documents, let's split and process in bulk."
msgstr ""
"Omdat deze pdf meerdere documenten bevat, kunnen we deze splitsen en in bulk"
" verwerken."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid ""
"At least one document couldn't be moved due to access rights. Shortcuts have"
" been created."
msgstr ""
"Ten minste één document kon niet worden verplaatst vanwege toegangsrechten. "
"Er zijn snelkoppelingen gemaakt."

#. module: documents
#: model:ir.model,name:documents.model_ir_attachment
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_id
msgid "Attachment"
msgstr "Bijlage"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_attachment_count
msgid "Attachment Count"
msgstr "Aantal bijlagen"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__description
msgid "Attachment Description"
msgstr "Omschrijving bijlage"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_name
msgid "Attachment Name"
msgstr "Naam bijlage"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_type
msgid "Attachment Type"
msgstr "Soort bijlage"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_automations.js:0
msgid "Automations"
msgstr "Automatiseringen"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__available_embedded_actions_ids
msgid "Available Embedded Actions"
msgstr "Beschikbare geïntegreerde acties"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_bill
msgid "Bill"
msgstr "Leveranciersfactuur"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Blank Page"
msgstr "Blanco pagina"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_brochures
msgid "Brochures"
msgstr "Brochures"

#. module: documents
#: model:ir.model.fields,help:documents.field_mail_activity_type__folder_id
msgid "By defining a folder, the upload activities will generate a document"
msgstr ""
"Door een folder te definiëren zullen de geüploade activiteiten een document "
"genereren"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_link_to_record_form_view
msgid "Cancel"
msgstr "Annuleren"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__checksum
msgid "Checksum/SHA1"
msgstr "Checksum/SHA1"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__children_ids
msgid "Children"
msgstr "Onderliggende"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Choose a record to link"
msgstr "Kies een record om te linken"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_details
msgid "Choose or Configure Email Servers"
msgstr "Kies of configureer e-mailservers"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Click on a card to <b>select the document</b>."
msgstr "Klik op een kaart om het <b>document te selecteren</b>."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Click on a thumbnail to <b>preview the document</b>."
msgstr ""
"Klik op een miniatuur om <b>een voorbeeld van het document</b> te zien."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid ""
"Click on the <b>page separator</b>: we don't want to split these two pages "
"as they belong to the same document."
msgstr ""
"Klik op het <b>paginascheidingsteken</b>: we willen deze twee pagina's niet "
"splitsen omdat ze tot hetzelfde document behoren."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Click the cross to <b>exit preview</b>."
msgstr "Klik op het kruis om <b>de preview te verlaten</b>."

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__thumbnail_status__client_generated
msgid "Client Generated"
msgstr "Aangemaakte klant"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.xml:0
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Close"
msgstr "Afsluiten"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Close split tools"
msgstr "Sluit splitstools"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_tag__color
msgid "Color"
msgstr "Kleur"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Common roots for all company users."
msgstr "Gemeenschappelijke wortels voor alle gebruikers van het bedrijf."

#. module: documents
#: model:ir.model,name:documents.model_res_company
msgid "Companies"
msgstr "Bedrijven"

#. module: documents
#. odoo-javascript
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.js:0
#: code:addons/documents/static/src/views/kanban/documents_kanban_record.js:0
#: model:ir.model.fields,field_description:documents.field_documents_document__company_id
msgid "Company"
msgstr "Bedrijf"

#. module: documents
#: model:ir.model,name:documents.model_res_config_settings
msgid "Config Settings"
msgstr "Configuratie-instellingen"

#. module: documents
#: model:ir.ui.menu,name:documents.Config
msgid "Configuration"
msgstr "Configuratie"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model:ir.model,name:documents.model_res_partner
#: model:ir.model.fields,field_description:documents.field_documents_document__partner_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__partner_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Contact"
msgstr "Contact"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Contact your Administrator to get access if needed."
msgstr "Neem contact op met je beheerder als je toegang nodig hebt."

#. module: documents
#: model:documents.tag,name:documents.documents_tag_contracts
msgid "Contracts"
msgstr "Contracten"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "Control panel buttons"
msgstr "Controlepaneel knoppen"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.js:0
msgid "Copy Link"
msgstr "Link kopiëren"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Copy Links"
msgstr "Koppelingen kopiëren"

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_create_activity
msgid "Create Activity"
msgstr "Activiteit maken"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Create Shortcut"
msgstr "Snelkoppeling maken"

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.mail_activity_plan_action_document
msgid "Create a Document Activity Plan"
msgstr "Maak een activiteitenplan voor documenten aan"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_option
msgid "Create a new activity"
msgstr "Nieuwe activiteit aanmaken"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Create and edit new contact \""
msgstr "Nieuw contact maken en bewerken \""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Create new contact \""
msgstr "Nieuw contact maken \""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_tag__create_uid
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Created by"
msgstr "Aangemaakt door"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_date
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__create_date
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__create_date
#: model:ir.model.fields,field_description:documents.field_documents_tag__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Creation Date"
msgstr "Aanmaakdatum"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.xml:0
msgid "Current Version"
msgstr "Huidige versie"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Aangepast bouncebericht"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__create_activity_date_deadline_range_type__days
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__days
msgid "Days"
msgstr "Dagen"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_defaults
msgid "Default Values"
msgstr "Standaardwaarden"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__deletion_delay
#: model:ir.model.fields,help:documents.field_res_config_settings__deletion_delay
msgid "Delay after permanent deletion of the document in the trash (days)"
msgstr ""
"Termijn na het definitieve verwijderen van het document in de prullenbak "
"(dagen)"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.xml:0
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
#: code:addons/documents/static/src/views/list/documents_list_controller.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Delete"
msgstr "Verwijderen"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Delete focused or selected pages"
msgstr "Verwijder gerichte of geselecteerde pagina's"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Delete permanently"
msgstr "Definitief verwijderen"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_res_config_settings__deletion_delay
msgid "Deletion Delay"
msgstr "Verwijdertermijn"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__deletion_delay
msgid "Deletion delay"
msgstr "Verwijdertermijn"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Deletion delay (days)"
msgstr "Verwijdertermijn (dagen)"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_deprecated
msgid "Deprecated"
msgstr "Vervallen"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
#: code:addons/documents/static/src/views/hooks.js:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_rename
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
#: model_terms:ir.ui.view,arch_db:documents.folder_deletion_form
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Discard"
msgstr "Negeren"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Discoverable"
msgstr "Ontdekbaar"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_access__display_name
#: model:ir.model.fields,field_description:documents.field_documents_document__display_name
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__display_name
#: model:ir.model.fields,field_description:documents.field_documents_redirect__display_name
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__display_name
#: model:ir.model.fields,field_description:documents.field_documents_tag__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "Do you want to exit without saving or gather pages into one document ?"
msgstr ""
"Wil je afsluiten zonder de pagina's op te slaan of te verzamelen in één "
"document?"

#. module: documents
#: model:ir.model,name:documents.model_documents_document
#: model:ir.model.fields,field_description:documents.field_documents_access__document_id
#: model:ir.model.fields,field_description:documents.field_documents_redirect__document_id
#: model:ir.model.fields,field_description:documents.field_documents_tag__document_ids
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "Document"
msgstr "Document"

#. module: documents
#: model:ir.model,name:documents.model_documents_access
msgid "Document / Partner"
msgstr "Document / Partner"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.xml:0
msgid "Document Access Internal"
msgstr "Document Toegang Intern"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.xml:0
msgid "Document Access Link"
msgstr "Link naar document"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__document_count
#: model:ir.model.fields,field_description:documents.field_res_partner__document_count
msgid "Document Count"
msgstr "Document aantal"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Document Name"
msgstr "Documentnaam"

#. module: documents
#: model:ir.actions.act_window,name:documents.mail_activity_plan_action_document
msgid "Document Plans"
msgstr "Documentplannen"

#. module: documents
#: model:ir.model,name:documents.model_documents_redirect
msgid "Document Redirect"
msgstr "Document omleiden"

#. module: documents
#: model:ir.model,name:documents.model_documents_request_wizard
msgid "Document Request"
msgstr "Aanvraag document"

#. module: documents
#: model:mail.template,subject:documents.mail_template_document_request
msgid ""
"Document Request {{ object.name != False and ': '+ object.name or '' }}"
msgstr ""
"Documentverzoek {{ object.name != False and ': '+ object.name or '' }}"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: code:addons/documents/models/mail_activity.py:0
msgid "Document Request: %(name)s Uploaded by: %(user)s"
msgstr "Document aanvraag: %(name)s Geüpload door: %(user)s"

#. module: documents
#: model:mail.template,name:documents.mail_template_document_request_reminder
msgid "Document Request: Reminder"
msgstr "Documentverzoek: Herinnering"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__document_token
msgid "Document Token"
msgstr "Document Token"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "Document preview"
msgstr "Voorbeeld document"

#. module: documents
#. odoo-python
#: code:addons/documents/controllers/documents.py:0
msgid "Document uploaded by %(user)s"
msgstr "Document geüpload door %(user)s"

#. module: documents
#: model:mail.template,name:documents.mail_template_document_request
msgid "Document: Document Request"
msgstr "Document: Documentaanvraag"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/list/document_count_field.js:0
msgid "DocumentCountIntegerField"
msgstr "DocumentCountIntegerField"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: code:addons/documents/models/res_partner.py:0
#: model:ir.actions.act_window,name:documents.document_action
#: model:ir.actions.act_window,name:documents.document_action_portal
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__document_ids
#: model:ir.model.fields,field_description:documents.field_res_users__document_count
#: model:ir.ui.menu,name:documents.dashboard
#: model:ir.ui.menu,name:documents.menu_root
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.documents_view_activity
#: model_terms:ir.ui.view,arch_db:documents.portal_my_home_documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Documents"
msgstr "Documenten"

#. module: documents
#: model:ir.model,name:documents.model_documents_link_to_record_wizard
msgid "Documents Link to Record"
msgstr "Documenten link naar record"

#. module: documents
#: model:ir.model,name:documents.model_documents_mixin
msgid "Documents creation mixin"
msgstr "Documenten aanmaken mixin"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "Documents in the Trash cannot be shared"
msgstr "Documenten in de Prullenbak kunnen niet worden gedeeld"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.js:0
msgid "Documents moved to trash will show up here"
msgstr "Documenten in de prullenbak worden hier weergegeven"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.js:0
msgid "Documents shared with you will appear here"
msgstr "Gedeelde documenten verschijnen hier"

#. module: documents
#: model:ir.model,name:documents.model_documents_unlink_mixin
msgid "Documents unlink mixin"
msgstr "Mixin documenten ontkoppelen"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.xml:0
msgid "Done"
msgstr "Gereed"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "Done!"
msgstr "Gereed!"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.xml:0
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_download.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Download"
msgstr "Downloaden"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Download #{document.name}"
msgstr "Download #{document.name}"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Download all files"
msgstr "Download alle bestanden"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_file
msgid "Download file"
msgstr "Bestand downloaden"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Download folder as zip"
msgstr "Map downloaden als zip"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Download zip #{subfolder.name}"
msgstr "Download zip #{subfolder.name}"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_draft
msgid "Draft"
msgstr "Concept"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_drop_zone.xml:0
msgid "Drop files here to upload"
msgstr "Drop hier je bestanden op te uploaden"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_date_deadline_range
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_date_deadline_range
msgid "Due Date In"
msgstr "Vervaldatum in"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_date_deadline_range_type
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_date_deadline_range_type
msgid "Due type"
msgstr "Vervalsoort"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Duplicate"
msgstr "Dupliceren"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
#: code:addons/documents/static/src/views/search/documents_search_panel.xml:0
msgid "Edit"
msgstr "Bewerken"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: model:ir.model.fields.selection,name:documents.selection__documents_access__role__edit
#: model:ir.model.fields.selection,name:documents.selection__documents_document__access_internal__edit
#: model:ir.model.fields.selection,name:documents.selection__documents_document__access_via_link__edit
#: model:ir.model.fields.selection,name:documents.selection__documents_document__user_permission__edit
msgid "Editor"
msgstr "Editor"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "Email"
msgstr "E-mail"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_email
msgid "Email Alias"
msgstr "E-mailalias"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_details
msgid "Email Upload"
msgstr "E-mail uploaden"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__email_cc
msgid "Email cc"
msgstr "E-mail cc"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "E-maildomein bijv. 'example.com' in '<EMAIL>'"

#. module: documents
#: model:ir.actions.act_window,name:documents.mail_alias_action
msgid "Email links"
msgstr "E-mail koppelingen"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#: code:addons/documents/static/src/views/hooks.js:0
#: model:ir.model.fields.selection,name:documents.selection__documents_document__thumbnail_status__error
msgid "Error"
msgstr "Fout"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Escape Preview/Deselect/Exit"
msgstr "Voorbeeld verlaten/deselecteren/afsluiten"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "Exit Split Tools"
msgstr "Verlaat splitstools"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "Exit without saving"
msgstr "Afsluiten zonder opslaan"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_partner_access.xml:0
msgid "Exp:"
msgstr "Exp:"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_expense
msgid "Expense"
msgstr "Declaratie"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_access__expiration_date
msgid "Expiration"
msgstr "Vervaldatum"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/list/documents_list_controller.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Export"
msgstr "Exporteren"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Extra comments..."
msgstr "Extra opmerkingen..."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__favorited_ids
msgid "Favorite of"
msgstr "Favoriet van"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_type_icon/documents_type_icon.xml:0
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__binary
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "File"
msgstr "Bestand"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__datas
msgid "File Content (base64)"
msgstr "Bestandsinhoud (base64)"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__raw
msgid "File Content (raw)"
msgstr "Bestandsinhoud (raw)"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__file_extension
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "File Extension"
msgstr "Bestandsextensie"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__file_size
msgid "File Size"
msgstr "Bestandsgrootte"

#. module: documents
#: model:ir.model,name:documents.model_ir_binary
msgid "File streaming helper model for controllers"
msgstr "Helpermodel voor bestandsstreaming voor controllers"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/kanban/documents_kanban_renderer.xml:0
msgid "Files"
msgstr "Bestanden"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Files Centralization"
msgstr "Bestandencentralisatie"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_deletion_form
msgid "Files will be sent to trash and deleted forever after"
msgstr ""
"Bestanden worden naar de prullenbak verstuurd en definitief verwijderd na"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
msgid "Files will be sent to trash and deleted forever after %s days."
msgstr ""
"Bestanden worden naar de prullenbak verstuurd en definitief verwijderd na %s"
" dagen."

#. module: documents
#: model:documents.tag,name:documents.documents_tag_financial
msgid "Financial"
msgstr "Financieel"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_fiscal
msgid "Fiscal"
msgstr "Fiscaal"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Focus first page of next group"
msgstr "Richten op eerste pagina van volgende groep"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Focus first page of previous group"
msgstr "Richten op eerste pagina van volgende groep"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Focus next page"
msgstr "Richten op volgende pagina"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Focus previous page"
msgstr "Richten op vorige pagina"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
#: code:addons/documents/static/src/views/documents_type_icon/documents_type_icon.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_document__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__folder_id
#: model:ir.model.fields,field_description:documents.field_mail_activity_type__folder_id
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__folder
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "Folder"
msgstr "Map"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/kanban/documents_kanban_renderer.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Folders"
msgstr "Mappen"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_follower_ids
msgid "Followers"
msgstr "Volgers"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_partner_ids
msgid "Followers (Partners)"
msgstr "Volgers (Partners)"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome icoon bijv. fa-tasks"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "Gather in one document"
msgstr "Verzamelen in één document"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_details
msgid "General"
msgstr "Algemeen"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.xml:0
msgid "General access"
msgstr "Algemene toegang"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "GiB"
msgstr "GiB"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Group By"
msgstr "Groeperen op"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_hr
msgid "HR"
msgstr "HR"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__has_message
msgid "Has Message"
msgstr "Heeft bericht"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Hide shortcuts"
msgstr "Snelkoppelingen verbergen"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__previous_attachment_ids
msgid "History"
msgstr "Geschiedenis"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_access__id
#: model:ir.model.fields,field_description:documents.field_documents_document__id
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__id
#: model:ir.model.fields,field_description:documents.field_documents_redirect__id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__id
#: model:ir.model.fields,field_description:documents.field_documents_tag__id
msgid "ID"
msgstr "ID"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"ID van het bovenliggende record dat de alias bezit (bijvoorbeeld: het "
"project bezit de alias van de aangemaakte taken)"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_exception_icon
msgid "Icon"
msgstr "Icoon"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icoon om uitzondering op activiteit aan te geven."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__is_access_via_link_hidden
msgid ""
"If \"True\", only people given direct access to this document will be able "
"to view it. If \"False\", access with the link also given to all who can "
"access the parent folder."
msgstr ""
"Als \"Waar\", dan kunnen alleen mensen die direct toegang hebben tot dit "
"document het bekijken. Als \"False\", dan wordt toegang met de link ook "
"gegeven aan iedereen die toegang heeft tot de bovenliggende map."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Indien aangevinkt vragen nieuwe berichten je aandacht."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_has_error
#: model:ir.model.fields,help:documents.field_documents_document__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Indien aangevinkt hebben sommige berichten een leveringsfout."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Indien ingesteld, wordt deze inhoud automatisch naar niet-geautoriseerde "
"gebruikers verzonden in plaats van het standaardbericht."

#. module: documents
#: model:documents.tag,name:documents.documents_tag_images
msgid "Images"
msgstr "Afbeeldingen"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Impossible to delete folders used by other applications."
msgstr ""
"Onmogelijk om mappen te verwijderen die door andere applicaties worden "
"gebruikt."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Impossible to invite partners on multiple documents at once."
msgstr ""
"Het is niet mogelijk om partners tegelijk op meerdere documenten uit te "
"nodigen."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "In"
msgstr "In"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__thumbnail_status__restricted
msgid "Inaccessible"
msgstr "Ontoegankelijk"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_inbox
#: model:mail.activity.type,name:documents.mail_documents_activity_data_Inbox
msgid "Inbox"
msgstr "Postvak in"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"Incorrect values. Use one of the following for the following fields: "
"%(hints)s.)"
msgstr ""
"Onjuiste waarden. Gebruik een van de volgende voor de volgende velden: "
"%(hints)s.)"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__index_content
msgid "Indexed Content"
msgstr "Geïndexeerde inhoud"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_details.js:0
msgid "Info & Tags"
msgstr "Info & labels"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Info & tags"
msgstr "Info & labels"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.xml:0
msgid "Internal Users"
msgstr "Interne gebruikers"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__access_internal
msgid "Internal Users Rights"
msgstr "Rechten interne gebruikers"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.js:0
msgid "Internal users can edit"
msgstr "Interne gebruikers kunnen bewerken"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.js:0
msgid "Internal users can view"
msgstr "Interne gebruikers kunnen bekijken"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Invalid folder id"
msgstr "Ongeldige map-id"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "Invalid operation"
msgstr "Ongeldige bewerking"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Invalid role."
msgstr "Ongeldige rol."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_editable_attachment
msgid "Is Editable Attachment"
msgstr "Is bewerkbare bijlage"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_favorited
msgid "Is Favorited"
msgstr "Is favoriet"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_is_follower
msgid "Is Follower"
msgstr "Is een volger"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_multipage
msgid "Is considered multipage"
msgstr "Wordt beschouwd als meerdere pagina's"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"It is not possible to move archived documents or documents to archived "
"folders."
msgstr ""
"Het is niet mogelijk om gearchiveerde documenten of documenten naar "
"gearchiveerde mappen te verplaatsen."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"Item(s) you wish to restore are included in archived folders. To restore these items, you must restore the following including folders instead:\n"
"- %(folders_list)s"
msgstr ""
"Item(s) die je wilt herstellen zijn opgenomen in gearchiveerde mappen. Om deze items te herstellen, moet je in plaats daarvan de volgende inclusief mappen herstellen:\n"
"- %(folders_list)s"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Items in trash will be deleted forever after %s days."
msgstr "Items in de prullenbak worden definitief verwijderd na %s dagen."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Items moved to the trash will be deleted forever after %s days."
msgstr "Items in de prullenbak worden definitief verwijderd na %s dagen."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_access__last_access_date
#: model:ir.model.fields,field_description:documents.field_documents_document__last_access_date_group
msgid "Last Accessed On"
msgstr "Laatst bekeken op"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_tag__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__write_date
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__write_date
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__write_date
#: model:ir.model.fields,field_description:documents.field_documents_tag__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_legal
msgid "Legal"
msgstr "Juridische afdeling"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid ""
"Let's process documents in your Inbox.<br/><i>Tip: Use Tags to filter "
"documents and structure your process.</i>"
msgstr ""
"Laten we documenten in je inbox verwerken.<br/><i> Tip: gebruik labels om "
"documenten te filteren en je proces te structureren.</i>"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Let's process these bills: send to Finance workspace."
msgstr ""
"Laten we deze leveranciersfacturen verwerken: stuur naar de werkruimte van "
"de boekhouding."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Let's process this document, coming from our scanner."
msgstr "Laten we dit document, afkomstig van onze scanner, verwerken."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid ""
"Let's tag this mail as legal<br/> <i>Tips: actions can be tailored to your "
"process, according to the workspace.</i>"
msgstr ""
"Laten we deze e-mail voorzien van een juridisch label<br/> <i>Tips: acties "
"kunnen worden aangepast aan het proces, afhankelijk van de werkruimte.</i>"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
#: code:addons/documents/static/src/views/documents_type_icon/documents_type_icon.xml:0
#: model_terms:ir.ui.view,arch_db:documents.documents_link_to_record_form_view
msgid "Link"
msgstr "Link"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_access_via_link_hidden
msgid "Link Access Hidden"
msgstr "Verbinding Toegang Verborgen"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__access_via_link
msgid "Link Access Rights"
msgstr "Link Toegangsrechten"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__url
msgid "Link URL"
msgstr "Link URL"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.js:0
msgid "Link copied to clipboard!"
msgstr "Link gekopieerd naar klembord!"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.xml:0
msgid "Link copied to the clipboard."
msgstr "Link gekopieerd naar het klembord."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "Linked To"
msgstr "Gelinkt aan"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.js:0
msgid "Links copied to clipboard!"
msgstr "Koppelingen gekopieerd naar klembord!"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "Inkomende mail detectie alleen op eerste deel en niet het domein"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Lock"
msgstr "Vergrendelen"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_document__is_locked
msgid "Locked"
msgstr "Vergrendeld"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__lock_uid
msgid "Locked by"
msgstr "Vergrendeld door"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Log a note..."
msgstr "Log een notitie..."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Login"
msgstr "Login"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Logout"
msgstr "Uitloggen"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Mail: %s"
msgstr "Mail: %s"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Manage Versions"
msgstr "Beheer versies"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.js:0
msgid "Manage Versions of \"%s\""
msgstr "Versies van \"%s\" beheren"

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_remove_activities
msgid "Mark activities as completed"
msgstr "Markeer activiteiten als voltooid"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Merge PDFs"
msgstr "PDF's samenvoegen"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_note
msgid "Message"
msgstr "Bericht"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_error
msgid "Message Delivery error"
msgstr "Bericht afleverfout"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_ids
msgid "Messages"
msgstr "Berichten"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "MiB"
msgstr "MiB"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__mimetype
msgid "Mime Type"
msgstr "Mime Type"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Missing documents reference."
msgstr "Verwijzing naar ontbrekende documenten."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__model_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Model"
msgstr "Model"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__accessible_model_ids
msgid "Models"
msgstr "Modellen"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__create_activity_date_deadline_range_type__months
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__months
msgid "Months"
msgstr "Maanden"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "More"
msgstr "Meer"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Move this page to trash"
msgstr "Verplaats deze pagina naar de prullenbak"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Move to Trash"
msgstr "Verplaatsen naar de prullenbak"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_archive.js:0
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
#: code:addons/documents/static/src/views/hooks.js:0
#: model_terms:ir.ui.view,arch_db:documents.folder_deletion_form
msgid "Move to trash"
msgstr "Verplaatsen naar de prullenbak"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
msgid "Move to trash?"
msgstr "Verplaatsen naar de prullenbak?"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Moved to trash"
msgstr "Verplaats naar de prullenbak"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "Must have the link to access"
msgstr "Je moet de link hebben om toegang te krijgen"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "My Activities"
msgstr "Mijn activiteiten"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Mijn activiteit deadline"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "My Documents"
msgstr "Mijn documenten"

#. module: documents
#. odoo-javascript
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.js:0
#: code:addons/documents/static/src/views/kanban/documents_kanban_record.js:0
msgid "My Drive"
msgstr "Mijn Drive"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_access_role_or_last_access_date
msgid "NULL roles must have a set last_access_date"
msgstr "NULL rollen moeten een ingestelde last_access_date hebben"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__name
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__name
#: model:ir.model.fields,field_description:documents.field_documents_tag__name
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Name"
msgstr "Naam"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Name / Extension"
msgstr "Naam / Extensie"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "New"
msgstr "Nieuw"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "New File"
msgstr "Nieuw bestand"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.xml:0
msgid "New Folder"
msgstr "Nieuwe map"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "New Group"
msgstr "Nieuwe groep"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Volgende activiteitenafspraak"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Volgende activiteit deadline"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_summary
msgid "Next Activity Summary"
msgstr "Volgende activiteit overzicht"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_type_id
msgid "Next Activity Type"
msgstr "Volgende activiteit type"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "No activity"
msgstr "Geen activiteit"

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.mail_activity_type_action_document
msgid "No activity types found. Let's create one!"
msgstr "Geen activiteitensoorten gevonden. Laten we er een maken!"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.xml:0
msgid "No alias configured"
msgstr "Geen alias geconfigureerd"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "No document has been selected"
msgstr "Er zijn geen documenten geselecteerd"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/chatter/documents_chatter_overlay.xml:0
msgid "No item selected"
msgstr "Geen item geselecteerd"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.js:0
msgid "No one on the internet can access"
msgstr "Niemand op het internet heeft toegang tot"

#. module: documents
#. odoo-python
#: code:addons/documents/models/ir_actions_server.py:0
msgid "No server actions found for Documents!"
msgstr "Geen serveracties voor documenten gevonden!"

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.mail_alias_action
msgid "No shared links"
msgstr "Geen gedeelte links"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__access_internal__none
#: model:ir.model.fields.selection,name:documents.selection__documents_document__access_via_link__none
#: model:ir.model.fields.selection,name:documents.selection__documents_document__user_permission__none
msgid "None"
msgstr "Geen"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Not a file"
msgstr "Geen bestand"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Not a folder."
msgstr "Geen map."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Not attached"
msgstr "Niet gekoppeld"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "Not set"
msgstr "Niet ingesteld"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_note
msgid "Note"
msgstr "Notitie"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Notify"
msgstr "Informeer"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_needaction_counter
msgid "Number of Actions"
msgstr "Aantal acties"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_error_counter
msgid "Number of errors"
msgstr "Aantal fouten"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Aantal berichten die actie vereisen"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Aantal berichten met leveringsfout"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Odoo Logo"
msgstr "Odoo logo"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Odoo Studio - Customize workflows in minutes"
msgstr "Odoo Studio - Personaliseer werkstromen in minuten"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Odoo Website"
msgstr "Odoo website"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__last_access_date_group__0_older
msgid "Older"
msgstr "Ouder"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Only Documents Managers can create in company folder."
msgstr "Alleen documentbeheerders kunnen documenten in de bedrijfsmap maken."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Only Documents Managers can set aliases."
msgstr "Alleen Documentenbeheerders kunnen aliassen instellen."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Only Documents managers can set an alias."
msgstr "Alleen documentbeheerders kunnen een alias instellen."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.js:0
msgid "Only people with access can open with the link"
msgstr "Alleen mensen met toegang kunnen de link openen"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Open #{document_url.name}"
msgstr "Open #{document_url.name}"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Open folder #{subfolder.name}"
msgstr "Open map #{subfolder.name}"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "Open the permissions panel"
msgstr "Open het paneel met machtigingen"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"Operation not supported. Please use \"Move to Trash\" / `action_archive` "
"instead."
msgstr ""
"De bewerking is niet ondersteund. Gebruik in plaats daarvan \"Verplaats naar"
" prullenbak\" / `actie_archief`."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"Operation not supported. Please use \"Restore\" / `action_unarchive` "
"instead."
msgstr ""
"De bewerking is niet ondersteund. Gebruik in plaats daarvan \"Herstel\" / "
"`actie_unarchive`."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"Optionele ID van een thread (record) waaraan alle inkomende berichten worden"
" gekoppeld, zelfs als ze hierop niet geantwoord hebben. Indien ingesteld, "
"zal dit het aanmaken van nieuwe records uitzetten."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.js:0
msgid "Optional message..."
msgstr "Optioneel bericht..."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.xml:0
msgid "Or send emails to"
msgstr "Of verstuur e-mails naar"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_other
msgid "Other"
msgstr "Overige"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: code:addons/documents/static/src/components/documents_permission_panel/documents_partner_access.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_document__owner_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__requestee_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Owner"
msgstr "Eigenaar"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Owner: #{document.owner_id.name}"
msgstr "Eigenaar: #{document.owner_id.name}"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Owner: #{document_url.owner_id.name}"
msgstr "Eigenaar: #{document_url.owner_id.name}"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Owner: #{subfolder.owner_id.name}"
msgstr "Eigenaar: #{subfolder.owner_id.name}"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_parent_model_id
msgid "Parent Model"
msgstr "Bovenliggend model"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__parent_path
msgid "Parent Path"
msgstr "Bovenliggend pad"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "Bovenliggende record Thread ID"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Bovenliggend model dat de alias bevat. Het model dat de aliasverwijzing bevat,\n"
"is niet noodzakelijk het model dat door alias_model_id wordt gegeven (bijvoorbeeld: project (parent_model) en taak (model))"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "Partial transfer"
msgstr "Gedeeltelijke overdracht"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_access__partner_id
msgid "Partner"
msgstr "Partner"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_partner_access.xml:0
msgid "People with access"
msgstr "Mensen met toegang"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_pinned_folder
msgid "Pinned to Company roots"
msgstr "Vastgemaakt aan wortels van bedrijf"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.not_available
msgid ""
"Please login or contact the person that shared this link for more "
"information."
msgstr ""
"Log in of neem contact op met de persoon die deze link heeft gedeeld voor "
"meer informatie."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Beleid om een bericht te versturen bij een document door gebruik te maken van de mail gateway.\n"
"-iedereen: iedereen mag posten\n"
"-partners: alleen geautoriseerde partners\n"
"-volgers: alleen volgers van een bijbehorend document of leden van gevolgde kanalen\n"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__thumbnail_status__present
msgid "Present"
msgstr "Huidig"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_presentations
msgid "Presentations"
msgstr "Presentaties"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_file
msgid "Preview files"
msgstr "Bestanden bekijken"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_project
msgid "Project"
msgstr "Project"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Put in %s"
msgstr "In %s plaatsen"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__rating_ids
msgid "Ratings"
msgstr "Beoordelingen"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Recent"
msgstr "Recent"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.js:0
msgid "Recently accessed Documents will show up here"
msgstr "Onlangs geraadpleegde documenten worden hier weergegeven"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Recently accessed documents."
msgstr "Onlangs geraadpleegde documenten."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__resource_ref
msgid "Record"
msgstr "Record"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_force_thread_id
msgid "Record Thread ID"
msgstr "Record Thread ID"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Remaining Pages"
msgstr "Resterende pagina's"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/activity/documents_activity_controller.js:0
msgid "Reminder emails have been sent."
msgstr "Herinneringsmails zijn verzonden."

#. module: documents
#: model:mail.template,subject:documents.mail_template_document_request_reminder
msgid ""
"Reminder to upload your document{{ object.name and ' : ' + object.name or ''"
" }}"
msgstr ""
"Herinnering om je document te uploaden {{ object.name and ' : ' + "
"object.name or '' }}"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_remove_partner_button.xml:0
msgid "Remove Access"
msgstr "Verwijder toegang"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_remove_partner_button.xml:0
msgid "Remove Member"
msgstr "Lid verwijderen"

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_tag_remove_inbox
msgid "Remove Tag Inbox"
msgstr "Label inbox verwijderen"

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_tag_remove_to_validate
msgid "Remove Tag To Validate"
msgstr "Label verwijderen om te valideren"

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_remove_tags
msgid "Remove all tags"
msgstr "Alle labels verwijderen"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_star.js:0
msgid "Remove star"
msgstr "Uit favorieten verwijderen"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_rename.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_rename
msgid "Rename"
msgstr "Naam wijzigen"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Request"
msgstr "Verzoek"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__request_activity_id
msgid "Request Activity"
msgstr "Aanvraag activiteit"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Request To"
msgstr "Verzoek aan"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/web/activity/activity_menu_patch.xml:0
msgid "Request a Document"
msgstr "Documentverzoek"

#. module: documents
#: model:ir.actions.act_window,name:documents.action_request_form
msgid "Request a file"
msgstr "Bestand aanvragen"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_request_page
msgid ""
"Requested\n"
"                                ∙"
msgstr ""
"Aangevraagd\n"
"                                ∙"

#. module: documents
#: model:mail.activity.type,name:documents.mail_documents_activity_data_md
msgid "Requested Document"
msgstr "Aangevraagd document"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Requested Documents"
msgstr "Aangevraagde documenten"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__requestee_partner_id
msgid "Requestee Partner"
msgstr "Aanvragende partner"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_model_name
msgid "Res Model Name"
msgstr "Res model naam"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__res_id
msgid "Resource ID"
msgstr "Resource-ID"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_model
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__res_model
msgid "Resource Model"
msgstr "Resource model"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_name
msgid "Resource Name"
msgstr "Resourcenaam"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_user_id
msgid "Responsible"
msgstr "Verantwoordelijke"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_user_id
msgid "Responsible User"
msgstr "Verantwoordelijke gebruiker"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Restore"
msgstr "Terugzetten"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Restricted"
msgstr "Beperkt"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Restricted Folder"
msgstr "Beperkte map"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.xml:0
msgid "Revert changes"
msgstr "Wijzigingen terugdraaien"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_access__role
msgid "Role"
msgstr "Rol"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS fout bij versturen"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_sales
msgid "Sales"
msgstr "Verkoop"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Save"
msgstr "Opslaan"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.js:0
msgid "Save or discard changes first"
msgstr "Wijzigingen eerst opslaan of negeren"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.xml:0
msgid "Saving..."
msgstr "Opslaan…"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Search people"
msgstr "Zoek mensen"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.js:0
msgid "Select a folder to upload a document"
msgstr "Selecteer een map om een document te uploaden"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/kanban/documents_kanban_renderer.js:0
#: code:addons/documents/static/src/views/list/documents_list_renderer.js:0
msgid "Select all"
msgstr "Selecteer alle"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select focused page"
msgstr "Selecteer gerichte pagina"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select next page"
msgstr "Selecteer volgende pagina"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select next pages of the group"
msgstr "Selecteer de volgende pagina's van de groep"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select previous page"
msgstr "Selecteer vorige pagina"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select previous pages of the group"
msgstr "Selecteer de vorige pagina's van de groep"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select/Deselect all pages"
msgstr "Selecteer/deselecteer alle pagina's"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Send"
msgstr "Verzenden"

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_send_to_finance
msgid "Send To Finance"
msgstr "Verzenden naar Financiën"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Send this letter to the legal department, by assigning the right tags."
msgstr ""
"Stuur deze brief naar de juridische afdeling door de juiste labels toe te "
"wijzen."

#. module: documents
#: model:mail.template,description:documents.mail_template_document_request
msgid "Sent to partner when requesting a document from them"
msgstr "Verzonden naar partner bij het aanvragen van een van hun documenten"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_tag__sequence
msgid "Sequence"
msgstr "Reeks"

#. module: documents
#: model:ir.model,name:documents.model_ir_actions_server
msgid "Server Action"
msgstr "Serveractie"

#. module: documents
#. odoo-python
#: code:addons/documents/models/ir_actions_server.py:0
msgid "Server Actions"
msgstr "Server-acties"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Set deletion delay for documents in the Trash"
msgstr "Bepaal verwijdertermijn voor documenten in de prullenbak"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_expiration_date_btn.xml:0
msgid "Set expiration date"
msgstr "Vervaldatum instellen"

#. module: documents
#: model:mail.template,description:documents.mail_template_document_request_reminder
msgid ""
"Set reminders in activities to notify users who didn't upload their "
"requested document"
msgstr ""
"Stel herinnering in op activiteiten om gebruikers die het gevraagde document"
" niet hebben geüpload op de hoogte te stellen"

#. module: documents
#: model:ir.actions.act_window,name:documents.configuration_action
#: model:ir.actions.act_window,name:documents.settings_action
#: model:ir.ui.menu,name:documents.settings_menu
msgid "Settings"
msgstr "Instellingen"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_share.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
#: code:addons/documents/static/src/views/search/documents_search_panel.xml:0
#: code:addons/documents/static/src/web/error_dialog/error_dialog_patch.xml:0
msgid "Share"
msgstr "Delen"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.js:0
msgid "Share: %(documentName)s"
msgstr "Delen: %(documentName)s"

#. module: documents
#. odoo-javascript
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.js:0
#: code:addons/documents/static/src/views/kanban/documents_kanban_record.js:0
msgid "Shared with me"
msgstr "Gedeeld met mij"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Sharing..."
msgstr "Delen bezig..."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__shortcut_ids
msgid "Shortcut"
msgstr "Sneltoets"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "Shortcut created"
msgstr "Snelkoppeling gemaakt"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.js:0
msgid "Shortcuts can only be created one at a time."
msgstr "Snelkoppelingen kunnen maar één tegelijk worden gemaakt."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Shortcuts cannot change target document."
msgstr "Snelkoppelingen kunnen het doeldocument niet wijzigen."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_file
msgid "Size:"
msgstr "Omvang:"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid "Some files could not be uploaded (max size: %s)."
msgstr "Sommige bestanden konden niet worden geüpload (maximale grootte: %s)."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__shortcut_document_id
msgid "Source Document"
msgstr "Brondocument"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "Special folders cannot be shared"
msgstr "Speciale mappen kunnen niet worden gedeeld"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Split"
msgstr "Splitsen"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/attachments/document_file_viewer.xml:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Split PDF"
msgstr "PDF splitsen"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Split all white pages"
msgstr "Splits alle lege pagina's"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Split selected pages"
msgstr "Splits de geselecteerde pagina's"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Starred"
msgstr "Met ster"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status gebaseerd op activiteiten\n"
"Te laat: Datum is al gepasseerd\n"
"Vandaag: Activiteit datum is vandaag\n"
"Gepland: Toekomstige activiteiten."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "Stay here"
msgstr "Hier blijven"

#. module: documents
#: model:ir.ui.menu,name:documents.structure
msgid "Structure"
msgstr "Structuur"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_summary
msgid "Summary"
msgstr "Samenvatting"

#. module: documents
#: model:res.groups,name:documents.group_documents_system
msgid "System Administrator"
msgstr "Systeembeheerder"

#. module: documents
#: model:ir.model,name:documents.model_documents_tag
#: model:ir.model.fields,field_description:documents.field_mail_activity_type__tag_ids
#: model_terms:ir.ui.view,arch_db:documents.tag_view_search
msgid "Tag"
msgstr "Label"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Tag Name"
msgstr "Labelnaam"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_tag_tag_name_unique
msgid "Tag name already used"
msgstr "Tagnaam al gebruikt"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model:ir.actions.act_window,name:documents.tag_action
#: model:ir.model.fields,field_description:documents.field_documents_document__tag_ids
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__tag_ids
#: model:ir.ui.menu,name:documents.category_menu
msgid "Tags"
msgstr "Labels"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_text
msgid "Text"
msgstr "Tekst"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_tag__tooltip
msgid "Text shown when hovering on this tag"
msgstr ""
"Tekst die wordt weergegeven wanneer de muisaanwijzer op dit label wordt "
"geplaatst"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_res_config_settings_check_deletion_delay
msgid "The deletion delay should be positive."
msgstr "De verwijdertermijn moet positief zijn."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/web/error_dialog/error_dialog_patch.js:0
#: code:addons/documents/static/src/web/error_dialog/error_dialog_patch.xml:0
msgid "The document URL has been copied to your clipboard."
msgstr "De URL van het document is gekopieerd naar je klembord."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid "The document has been moved."
msgstr "Het document is verplaatst."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"The following documents can't have alias: \n"
"- %(records)s"
msgstr ""
"De volgende documenten kunnen geen alias hebben:\n"
"- %(records)s"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"The following documents/folders can't be owned by a Portal User: \n"
"- %(partners)s"
msgstr ""
"De volgende documenten/mappen kunnen geen eigendom zijn van een portaalgebruiker:\n"
"- %(partners)s"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "The following documents/shortcuts have a company mismatch: \n"
msgstr ""
"De volgende documenten/snelkoppelingen komen niet overeen met het bedrijf:\n"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "The following documents/shortcuts have a type mismatch: \n"
msgstr "De volgende documenten/sneltoetsen hebben een typefout:\n"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "The following shortcuts cannot be set as documents parents: \n"
msgstr ""
"De volgende snelkoppelingen kunnen niet worden ingesteld als document-"
"ouder:\n"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"Het model waar deze alias bijhoort. Iedere inkomende e-mail dat niet bij een"
" bestaande regel hoort zal leiden tot het aanmaken van een nieuwe regel in "
"dit model (Bijv. een projecttaak)."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"De naam van de e-mailalias, bijvoorbeeld: 'vacature' als je alle e-mails van"
" <EMAIL> wilt afvangen"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__last_access_date_group__1_month
msgid "This Month"
msgstr "Deze maand"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__last_access_date_group__2_week
msgid "This Week"
msgstr "Deze week"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "This action does not exist."
msgstr "Deze actie bestaat niet."

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_document_attachment_unique
msgid "This attachment is already a document"
msgstr "Deze bijlage is al een document"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.not_available
msgid "This document does not exist or is not publicly available."
msgstr "Dit document bestaat niet of is niet openbaar beschikbaar."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_request_page
msgid ""
"This document has been requested.\n"
"                <b onclick=\"document.querySelector('.o_request_upload').click()\" style=\"cursor:pointer;\">Upload it</b>."
msgstr ""
"Dit document is aangevraagd.\n"
"<b onclick=\"document.querySelector('.o_request_upload').click()\" style=\"cursor:pointer;\">Upload het</b>."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "This document has been restored."
msgstr "Dit document is hersteld."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"This file has been sent to the trash and will be deleted forever on the %s"
msgstr ""
"Dit bestand is naar de prullenbak verstuurd en wordt definitief verwijderd "
"op %s"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "This folder does not exist or is not accessible."
msgstr "Deze map bestaat niet of is niet toegankelijk."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "This is a folder"
msgstr "Dirt is een map"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.xml:0
msgid "This is a shortcut"
msgstr "Dit is een snelkoppeling"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "This is a shortcut - Click to access source document"
msgstr "Dit is een snelkoppeling - Klik om naar het brondocument te gaan"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_access_unique_document_access_partner
msgid "This partner is already set on this document."
msgstr "Deze partner is al ingesteld op dit document."

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.mail_activity_type_action_document
msgid ""
"Those represent the different categories of things you have to do (e.g. "
"\"Call\" or \"Send email\")."
msgstr ""
"Die vertegenwoordigen de verschillende categorieën van dingen die je moet "
"doen (bijvoorbeeld \"Bellen\" of \"E-mail verzenden\")."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__thumbnail
msgid "Thumbnail"
msgstr "Miniatuur"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__thumbnail_status
msgid "Thumbnail Status"
msgstr "Miniatuurstatus"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.xml:0
msgid "Tip: configure your scanner to send all documents to this address."
msgstr ""
"Tip: configureer je scanner om alle documenten naar dit adres te sturen."

#. module: documents
#: model:digest.tip,name:documents.digest_tip_documents_0
msgid "Tip: Become a paperless company"
msgstr "Tip: word een papierloos bedrijf"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_to_validate
msgid "To Validate"
msgstr "Te bevestigen"

#. module: documents
#: model:mail.activity.type,name:documents.mail_documents_activity_data_tv
msgid "To validate"
msgstr "Te bevestigen"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__last_access_date_group__3_day
msgid "Today"
msgstr "Vandaag"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/kanban/documents_kanban_renderer.js:0
msgid "Toggle favorite"
msgstr "Favoriet wisselen"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_tag__tooltip
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Tooltip"
msgstr "Tooltip"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Trash"
msgstr "Prullenbak"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Trash Management"
msgstr "Prullenbakbeheer"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__is_editable_attachment
msgid "True if we can edit the link attachment."
msgstr "Dat is waar als we de linkbijlage kunnen bewerken."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__type
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Type"
msgstr "Soort"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type van de geregistreerde uitzonderingsactiviteit."

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__url
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "URL"
msgstr "URL"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"URL %s does not seem complete, as it does not begin with http(s):// or "
"ftp://"
msgstr ""
"De URL %s lijkt niet volledig, aangezien deze niet begint met http(s):// of "
"ftp://"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__url_preview_image
msgid "URL Preview Image"
msgstr "URL voorbeeld afbeelding"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Unavailable action."
msgstr "Niet-beschikbare actie."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Unlock"
msgstr "Ontgrendel"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "Unnamed"
msgstr "Naamloos"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Upload"
msgstr "Upload"

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.document_action
#: model_terms:ir.actions.act_window,help:documents.document_action_portal
msgid ""
"Upload <span class=\"fw-normal\">a file or</span> drag <span class=\"fw-"
"normal\">it here.</span>"
msgstr ""
"Upload <span class=\"fw-normal\">een bestand of</span> sleep <span "
"class=\"fw-normal\">het hier.</span>"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.xml:0
msgid "Upload New Version"
msgstr "Nieuwe versie uploaden"

#. module: documents
#. odoo-python
#: code:addons/documents/models/mail_activity.py:0
msgid "Upload file request"
msgstr "Bestandsverzoek uploaden"

#. module: documents
#: model:ir.model,name:documents.model_res_users
#: model:res.groups,name:documents.group_documents_user
msgid "User"
msgstr "Gebruiker"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
#: code:addons/documents/static/src/components/documents_permission_panel/documents_partner_access.xml:0
msgid "User Access Role"
msgstr "Toegangsrol gebruiker"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__user_permission
msgid "User permission"
msgstr "Gebruiker machtiging"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_vat
msgid "VAT"
msgstr "Btw"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_validated
msgid "Validated"
msgstr "Bevestigd"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.xml:0
msgid ""
"Versions are displayed in the order they were uploaded to Documents.\n"
"                    If you delete the most recent version, the previous one is automatically restored as the current one."
msgstr ""
"Versies worden weergegeven in de volgorde waarin ze naar Documenten zijn geüpload.\n"
"                    Als je de meest recente versie verwijdert, wordt de vorige automatisch hersteld als de huidige versie."

#. module: documents
#: model:documents.tag,name:documents.documents_tag_videos
msgid "Videos"
msgstr "Video's"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: model:ir.model.fields.selection,name:documents.selection__documents_access__role__view
#: model:ir.model.fields.selection,name:documents.selection__documents_document__access_internal__view
#: model:ir.model.fields.selection,name:documents.selection__documents_document__access_via_link__view
#: model:ir.model.fields.selection,name:documents.selection__documents_document__user_permission__view
msgid "Viewer"
msgstr "Viewer"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid ""
"Want to become a <b>paperless company</b>? Let's discover Odoo Documents."
msgstr ""
"Wil je een <b>papierloos bedrijf</b> worden? Laten we Odoo-documenten "
"ontdekken."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__website_message_ids
msgid "Website Messages"
msgstr "Websiteberichten"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__website_message_ids
msgid "Website communication history"
msgstr "Website communicatie geschiedenis"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__create_activity_date_deadline_range_type__weeks
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__weeks
msgid "Weeks"
msgstr "Weken"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "What do you want to do with the remaining pages ?"
msgstr "Wat wil je doen met de resterende pagina's?"

#. module: documents
#: model_terms:web_tour.tour,rainbow_man_message:documents.documents_tour
msgid ""
"Wow... 6 documents processed in a few seconds, You're good.<br>The tour is "
"complete. Try uploading your own documents now."
msgstr ""
"Wow... 6 documenten verwerkt in een paar seconden.<br>De rondleiding is "
"voltooid. Probeer nu je eigen documenten te uploaden."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_partner_access.xml:0
msgid "You"
msgstr "Jij"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to (un)archive documents."
msgstr "Je mag geen documenten (uit)archiveren."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to change ownerships of documents you do not own."
msgstr ""
"Je mag de eigendomsrechten van documenten die je niet bezit niet veranderen."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to delete all these items."
msgstr "Je mag niet al deze items verwijderen."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to execute embedded actions."
msgstr "Het is niet toegestaan ​​om ingesloten acties uit te voeren."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to move (some of) these documents."
msgstr "Je mag (sommige van) deze documenten niet verplaatsen."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to pin/unpin embedded Actions."
msgstr ""
"Het is niet toegestaan ​​om ingesloten acties vast te pinnen of los te "
"pinnen."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to read the permission panel data."
msgstr "Je mag de gegevens in het toestemmingspaneel niet lezen."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to write in this folder."
msgstr "Je mag niet in deze map schrijven."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__attachment_type
msgid ""
"You can either upload a file from your computer or copy/paste an internet "
"link to your file."
msgstr ""
"Je kunt een bestand van je computer uploaden of een internetlink naar je "
"bestand kopiëren/plakken."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_pin_actions.js:0
msgid "You can not pin actions for that folder."
msgstr "Je kunt geen acties voor die map vastzetten."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"You can not update the access of a shortcut, update its target instead."
msgstr ""
"Je kunt de toegang van een snelkoppeling niet bijwerken, maar wel het doel."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You can't access that folder_id."
msgstr "Je hebt geen toegang tot die map_id."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid ""
"You can't create shortcuts in or move documents to this special folder."
msgstr ""
"Je kunt geen snelkoppelingen maken in of documenten verplaatsen naar deze "
"speciale map."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You can't move documents out of folders you cannot edit."
msgstr ""
"Je kunt geen documenten verplaatsen uit mappen die je niet kunt bewerken."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You cannot change the owner of documents you do not own."
msgstr ""
"Je kunt de eigenaar van documenten waarvan je niet de eigenaar bent, niet "
"wijzigen."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_tag.py:0
msgid "You cannot delete tags used in server actions."
msgstr "Je kunt geen labels verwijderen die gebruikt worden in serveracties."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You cannot delete this attachment."
msgstr "Je kunt deze bijlage niet verwijderen."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You cannot duplicate document(s) in the Trash."
msgstr "Je kunt documenten niet dupliceren in de Prullenbak."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "You cannot move folders or files when in the trash."
msgstr ""
"Je kunt geen mappen of bestanden verplaatsen die zich in de prullenbak "
"bevinden."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "You cannot move request in the company folder"
msgstr "Je kunt een verzoek niet verplaatsen in de bedrijfsmap"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You cannot pin an action on that document."
msgstr "Je kunt geen actie aan dat document vastmaken."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You cannot pin that type of action."
msgstr "Je kunt dat soort acties niet vastmaken."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_remove_partner_button.xml:0
msgid "You cannot remove this partner"
msgstr "Je kunt deze partner niet verwijderen"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "You cannot share multiple documents at the same time"
msgstr "Je kunt niet meerdere documenten tegelijk delen"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You do not have sufficient access rights to delete these documents."
msgstr ""
"Je hebt niet voldoende toegangsrechten om deze documenten te verwijderen."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid ""
"You don't have the rights to move documents nor create shortcut to that "
"folder."
msgstr ""
"Je beschikt niet over de rechten om documenten te verplaatsen of een "
"snelkoppeling naar die map te maken."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_drop_zone.xml:0
msgid "You must be in a specific writable workspace to upload files"
msgstr ""
"Je moet in een specifieke aanpasbare werkruimte zijn om bestanden te "
"uploaden"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Your Role: %s"
msgstr "Je rol: %s"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Your individual space."
msgstr "Je individuele ruimte."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.js:0
msgid "Your personal space"
msgstr "Je persoonlijke ruimte"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_details
msgid "alias"
msgstr "alias"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "bytes"
msgstr "bytes"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_deletion_form
msgid "days."
msgstr "dagen."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.js:0
msgid "document"
msgstr "document"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "documents"
msgstr "documenten"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "e.g. Discuss proposal"
msgstr "Bijv. Bespreken van de offerte"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_new_folder
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_rename
msgid "e.g. Finance"
msgstr "Bijv. Financiën"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "e.g. Missing Expense"
msgstr "bijv. Ontbrekende declaratie"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "e.g. To Validate"
msgstr "bijv. Te bevestigen"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "e.g. https://www.youtube.com/watch?v=CP96yVnXNrY"
msgstr "Bijv. https://www.youtube.com/watch?v=CP96yVnXNrY"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_details
msgid "e.g. mycompany.com"
msgstr "bijv. mycompany.com"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "files"
msgstr "bestanden"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.js:0
msgid "folder"
msgstr "map"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "folders,"
msgstr "mappen,"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__is_readonly_model
msgid "is_readonly_model"
msgstr "is_readonly_model"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/chatter/documents_chatter_overlay.xml:0
msgid "items selected"
msgstr "objecten geselecteerd"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "items,"
msgstr "regels,"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "kiB"
msgstr "kiB"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_page/pdf_page.xml:0
msgid "select"
msgstr "selecteer"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.mail_template_document_share
msgid "shared a document with you.<br/>"
msgstr "een document met je gedeeld.<br/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.mail_template_document_share
msgid "shared a folder with you.<br/>"
msgstr "een map met je gedeeld.<br/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "shared by"
msgstr "gedeeld door"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.mail_template_document_share
msgid "shared this document with you:"
msgstr "dit document met je gedeeld:"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.mail_template_document_share
msgid "shared this folder with you:"
msgstr "deze map met je gedeeld:"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "status code: %(status)s, message: %(message)s"
msgstr "statuscode: %(status)s, bericht: %(message)s"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "sub-doc-%s"
msgstr "sub-doc-%s"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "unnamed"
msgstr "naamloos"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid ""
"•\n"
"                                        <b>URL</b>"
msgstr ""
"-\n"
"                                       <b>URL</b>"
