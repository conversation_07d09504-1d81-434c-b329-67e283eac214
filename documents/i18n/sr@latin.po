# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents
#
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <dragan.v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2023
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-06-03 09:02+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Last-Translator: <PERSON><PERSON> <dragan.vukosav<PERSON><PERSON>@gmail.com>, "
"2023\n"
"Language-Team: Serbian (https://app.transifex.com/odoo/teams/41243/sr/)\n"
"Language: sr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.js:0
msgid " + Add a tag"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "%(documentCount)s Documents (%(lockedCount)s locked)"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "%s (copy)"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "%s (remaining pages)"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "%s Documents"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "%s file(s) not moved because they are locked by another user"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "%s new document(s) created"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "%s page(s) deleted"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid ", expired on"
msgstr ""

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid ""
". Scanned files will appear automatically in your workspace. Then, process "
"your documents in bulk with the split tool: launch user defined actions, "
"request a signature, convert to vendor bills with AI, etc."
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_finance_fiscal_year_2017
msgid "2023"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_finance_fiscal_year_2018
msgid "2024"
msgstr ""

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid "<b class=\"tip_title\">Tip: Become a paperless company</b>"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "<b>Deselect this page</b> as we plan to process all bills first."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "<b>Select</b> this page to continue."
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_files_page
msgid ""
"<br/>\n"
"                    is sharing content with you"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "<br/>Powered by"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "<i class=\"fa fa fa-folder text-primary me-2\" title=\"Workspace\"/>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_workspace_page
msgid "<i class=\"fa fa-download fa-fw\"/>  Download All"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_files_page
msgid "<i class=\"fa fa-eye\"/> Preview file"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid ""
"<i class=\"fa fa-lock oe_inline\" title=\"Locked\" invisible=\"not "
"lock_uid\"/>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid ""
"<i class=\"fa fa-tag o_documents_tag_color me-2\" title=\"Tags\" "
"invisible=\"not tag_ids\"/>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_workspace_page
msgid "<i class=\"fa fa-upload\"/>  Upload"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "<span class=\"o_stat_text\">Actions</span>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_document_res_partner_view
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "<span class=\"o_stat_text\">Documents</span>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "<span class=\"o_stat_text\">Related <br/> Record</span>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_workspace_page
msgid "<span title=\"Requested Document\">Requested Document</span>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "<span>&amp;nbsp;Documents.</span>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<span><b> Request</b></span>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<span><b>Requested Document</b></span>"
msgstr ""

#. module: documents
#: model:mail.template,body_html:documents.mail_template_document_request_reminder
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: "
"16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; "
"color: #454748; width: 100%; border-collapse:separate;\"><tr><td "
"align=\"center\">\n"
"                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" "
"width=\"590\" style=\"padding: 16px; background-color: white; color: "
"#454748; border-collapse:separate;\">\n"
"                    <tbody>\n"
"                        <!-- HEADER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;"
"\">\n"
"                                <table border=\"0\" cellpadding=\"0\" "
"cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: "
"white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\">\n"
"                                        <span style=\"font-size: 10px;\">\n"
"                                            Document Request: <br>\n"
"                                            <t t-if=\"object.create_share_id."
"name\">\n"
"                                                <span style=\"font-size: "
"20px; font-weight: bold;\" t-out=\"object.create_share_id.name or ''\">Inbox "
"Financial</span>\n"
"                                            </t>\n"
"                                        </span><br>\n"
"                                    </td><td valign=\"middle\" "
"align=\"right\">\n"
"                                        <img t-attf-src=\"/logo.png?"
"company={{ object.create_uid.company_id.id }}\" style=\"padding: 0px; "
"margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.create_uid."
"company_id.name\">\n"
"                                    </td></tr>\n"
"                                    <tr><td colspan=\"2\" style=\"text-align:"
"center;\">\n"
"                                      <hr width=\"100%\" style=\"background-"
"color:rgb(204,204,204);border:medium none;clear:both;display:block;font-"
"size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- CONTENT -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;"
"\">\n"
"                                <table border=\"0\" cellpadding=\"0\" "
"cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: "
"white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"top\" style=\"font-"
"size: 13px;\">\n"
"                                        <div>\n"
"                                            Hello <t t-out=\"object."
"create_share_id.owner_id.name or ''\">OdooBot</t>,\n"
"                                            <br><br>\n"
"                                            This is a friendly reminder to "
"upload your requested document:\n"
"                                            <br><br>\n"
"                                            <center>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object."
"create_share_id.name\">\n"
"                                                        <b t-out=\"object."
"create_share_id.name or ''\">Inbox Financial</b>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object."
"create_share_id.activity_note\">\n"
"                                                        <i t-out=\"object."
"create_share_id.activity_note or ''\">Example of a note.</i>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <br>\n"
"                                                <div style=\"margin: 16px "
"0px 16px 0px;\">\n"
"                                                    <a t-att-href=\"object."
"create_share_id.full_url\" style=\"background-color: #875A7B; padding: 20px "
"30px 20px 30px; text-decoration: none; color: #fff; border-radius: 5px; font-"
"size:13px;\">\n"
"                                                        Upload the requested "
"document\n"
"                                                    </a>\n"
"                                                </div>\n"
"                                            </center><br>\n"
"                                            Please provide us with the "
"missing document before the link expires (planned on <t t-out=\"object."
"create_share_id.date_deadline or ''\">2021-05-17</t>).\n"
"                                            <br><br>\n"
"                                            Thank you,\n"
"                                            <t t-if=\"user and user."
"signature\">\n"
"                                                <br>\n"
"                                                <t t-out=\"user."
"signature\">--<br>Mitchell Admin</t>\n"
"                                                <br>\n"
"                                            </t>\n"
"                                        </div>\n"
"                                    </td></tr>\n"
"                                    <tr><td style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-"
"color:rgb(204,204,204);border:medium none;clear:both;display:block;font-"
"size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- FOOTER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;"
"\">\n"
"                                <table border=\"0\" cellpadding=\"0\" "
"cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: "
"white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;"
"\">\n"
"                                    <tr><td valign=\"middle\" "
"align=\"left\">\n"
"                                        <t t-out=\"object.create_uid."
"company_id.name or ''\">YourCompany</t>\n"
"                                    </td></tr>\n"
"                                    <tr><td valign=\"middle\" align=\"left\" "
"style=\"opacity: 0.7;\">\n"
"                                        <t t-out=\"object.create_uid."
"company_id.phone or ''\">******-123-4567</t>\n"
"                                        <t t-if=\"object.create_uid."
"company_id.email\">\n"
"                                            | <a t-attf-href=\"'mailto:%s' % "
"{{ object.create_uid.company_id.email }}\" style=\"text-decoration:none; "
"color: #454748;\" t-out=\"object.create_uid.company_id."
"email\"><EMAIL></a>\n"
"                                        </t>\n"
"                                        <t t-if=\"object.create_uid."
"company_id.website\">\n"
"                                            | <a t-att-href=\"object."
"create_uid.company_id.website\" style=\"text-decoration:none; color: #454748;"
"\" t-out=\"object.create_uid.company_id.website\">http://www.example.com</"
"a>\n"
"                                        </t>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                    </table>\n"
"                    </td></tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"                        <table border=\"0\" cellpadding=\"0\" "
"cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: "
"#F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                          <tr><td style=\"text-align: center; font-size: "
"13px;\">\n"
"                            <t t-if=\"object.create_share_id."
"date_deadline\">\n"
"                                This link expires on <b t-out=\"object."
"create_share_id.date_deadline\">2021-05-17</b>.<br>\n"
"                            </t>\n"
"                            Powered by <a target=\"_blank\" href=\"https://"
"www.odoo.com/app/documents\" style=\"color: #875A7B;\">Odoo Documents</a>\n"
"                          </td></tr>\n"
"                        </table>\n"
"                    </td></tr>\n"
"                </table>\n"
"            "
msgstr ""

#. module: documents
#: model:mail.template,body_html:documents.mail_template_document_request
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: "
"16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; "
"color: #454748; width: 100%; border-collapse:separate;\"><tr><td "
"align=\"center\">\n"
"                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" "
"width=\"590\" style=\"padding: 16px; background-color: white; color: "
"#454748; border-collapse:separate;\">\n"
"                    <tbody>\n"
"                        <!-- HEADER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;"
"\">\n"
"                                <table border=\"0\" cellpadding=\"0\" "
"cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: "
"white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\">\n"
"                                        <span style=\"font-size: 10px;\">\n"
"                                            Document Request: <br>\n"
"                                            <t t-if=\"object.name\">\n"
"                                                <span style=\"font-size: "
"20px; font-weight: bold;\" t-out=\"object.name or ''\">Inbox Financial</"
"span>\n"
"                                            </t>\n"
"                                        </span><br>\n"
"                                    </td><td valign=\"middle\" "
"align=\"right\" t-if=\"not object.create_uid.company_id."
"uses_default_logo\">\n"
"                                        <img t-attf-src=\"/logo.png?"
"company={{ object.create_uid.company_id.id }}\" style=\"padding: 0px; "
"margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.create_uid."
"company_id.name\">\n"
"                                    </td></tr>\n"
"                                    <tr><td colspan=\"2\" style=\"text-align:"
"center;\">\n"
"                                      <hr width=\"100%\" style=\"background-"
"color:rgb(204,204,204);border:medium none;clear:both;display:block;font-"
"size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- CONTENT -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;"
"\">\n"
"                                <table border=\"0\" cellpadding=\"0\" "
"cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: "
"white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"top\" style=\"font-"
"size: 13px;\">\n"
"                                        <div>\n"
"                                            Hello <t t-out=\"object.owner_id."
"name or ''\">OdooBot</t>,\n"
"                                            <br><br>\n"
"                                            <t t-out=\"object.create_uid."
"name or ''\">OdooBot</t> (<t t-out=\"object.create_uid.email or "
"''\"><EMAIL></t>) asks you to provide the following document:\n"
"                                            <br><br>\n"
"                                            <center>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object."
"name\">\n"
"                                                        <b t-out=\"object."
"name or ''\">Inbox Financial</b>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object."
"activity_note\">\n"
"                                                        <i t-out=\"object."
"activity_note or ''\">Example of a note.</i>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <br>\n"
"                                                <div style=\"margin: 16px "
"0px 16px 0px;\">\n"
"                                                    <a t-att-href=\"object."
"full_url\" style=\"background-color: #875A7B; padding: 20px 30px 20px 30px; "
"text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                        Upload the requested "
"document\n"
"                                                    </a>\n"
"                                                </div>\n"
"                                            </center><br>\n"
"                                            Please provide us with the "
"missing document<t t-if=\"object.date_deadline\"> before the link expires "
"(planned on <t t-out=\"object.date_deadline\">2021-05-17</t>)</t>.\n"
"                                            <t t-if=\"user and user."
"signature\">\n"
"                                                <br>\n"
"                                                <t t-out=\"user.signature or "
"''\">--<br>Mitchell Admin</t>\n"
"                                                <br>\n"
"                                            </t>\n"
"                                        </div>\n"
"                                    </td></tr>\n"
"                                    <tr><td style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-"
"color:rgb(204,204,204);border:medium none;clear:both;display:block;font-"
"size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- FOOTER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;"
"\">\n"
"                                <table border=\"0\" cellpadding=\"0\" "
"cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: "
"white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;"
"\">\n"
"                                    <tr><td valign=\"middle\" "
"align=\"left\">\n"
"                                        <t t-out=\"object.create_uid."
"company_id.name or ''\">YourCompany</t>\n"
"                                    </td></tr>\n"
"                                    <tr><td valign=\"middle\" align=\"left\" "
"style=\"opacity: 0.7;\">\n"
"                                        <t t-out=\"object.create_uid."
"company_id.phone or ''\">******-123-4567</t>\n"
"                                        <t t-if=\"object.create_uid."
"company_id.email\">\n"
"                                            | <a t-attf-href=\"'mailto:%s' % "
"{{ object.create_uid.company_id.email }}\" style=\"text-decoration:none; "
"color: #454748;\" t-out=\"object.create_uid.company_id.email or "
"''\"><EMAIL></a>\n"
"                                        </t>\n"
"                                        <t t-if=\"object.create_uid."
"company_id.website\">\n"
"                                            | <a t-attf-href=\"'%s' % "
"{{ object.create_uid.company_id.website }}\" style=\"text-decoration:none; "
"color: #454748;\" t-out=\"object.create_uid.company_id.website or "
"''\">http://www.example.com</a>\n"
"                                        </t>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                    </table>\n"
"                    </td></tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"                        <table border=\"0\" cellpadding=\"0\" "
"cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: "
"#F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                          <tr><td style=\"text-align: center; font-size: "
"13px;\">\n"
"                            <t t-if=\"object.date_deadline\">\n"
"                                This link expires on <b t-out=\"object."
"date_deadline or ''\">2021-05-17</b>.<br>\n"
"                            </t>\n"
"                            Powered by <a target=\"_blank\" href=\"https://"
"www.odoo.com/app/documents\" style=\"color: #875A7B;\">Odoo Documents</a>\n"
"                          </td></tr>\n"
"                        </table>\n"
"                    </td></tr>\n"
"                </table>\n"
"            "
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Python rečnik koji će biti procenjen kako bi se pružila podrazumevana "
"vrednost prilikom kreiranja novih zapisa za ovaj alias."

#. module: documents
#: model:ir.model,name:documents.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr ""
"Grupa uslova i aktivnosti koji će biti dostupni svim prilozima koji "
"zadovoljavaju uslove"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_folder.py:0
msgid ""
"A workspace cannot have one of his child defined as Parent Workspace in "
"order to avoid a recursion issue."
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__parent_folder_id
msgid "A workspace will inherit the tags of its parent workspace"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Access"
msgstr "Pristup"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "Access Error"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__group_ids
msgid "Access Groups"
msgstr "Grupe za pristup"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Access Rights"
msgstr "Prava pristupa"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__access_token
msgid "Access Token"
msgstr "Token za pristup"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__action
msgid "Action"
msgstr "Radnja"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__action_count
msgid "Action Count"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Action Name"
msgstr "Naziv radnje"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_needaction
#: model:ir.model.fields,field_description:documents.field_documents_share__message_needaction
msgid "Action Needed"
msgstr "Potrebna akcija"

#. module: documents
#. odoo-javascript
#. odoo-python
#: code:addons/documents/models/documents_folder.py:0
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
#: model:ir.ui.menu,name:documents.workflow_rules_menu
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Actions"
msgstr "Radnje"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__active
#: model:ir.model.fields,field_description:documents.field_documents_folder__active
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Active"
msgstr "Aktivno"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_ids
#: model:ir.ui.menu,name:documents.mail_activities
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Activities"
msgstr "Aktivnosti"

#. module: documents
#: model:ir.model,name:documents.model_mail_activity
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
msgid "Activity"
msgstr "Aktivnost"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Dekoracija izuzeć́a aktivnosti"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_note
msgid "Activity Note"
msgstr ""

#. module: documents
#: model:ir.ui.menu,name:documents.mail_activity_plan_menu
#, fuzzy
msgid "Activity Plans"
msgstr "Aktivnost"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_state
msgid "Activity State"
msgstr "Stanje aktivnosti"

#. module: documents
#: model:ir.model,name:documents.model_mail_activity_type
msgid "Activity Type"
msgstr "Vrsta aktivnosti"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikonica vrste aktivnosti"

#. module: documents
#: model:ir.actions.act_window,name:documents.mail_activity_type_action_document
#: model:ir.ui.menu,name:documents.mail_activity_type_menu
#, fuzzy
msgid "Activity Types"
msgstr "Vrsta aktivnosti"

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.mail_activity_plan_action_document
msgid ""
"Activity plans are used to assign a list of activities in just a few clicks\n"
"                    (e.g. \"NDA signature process\", \"Workspace "
"workflow\", ...)"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_type_id
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_type_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_type_id
msgid "Activity type"
msgstr ""

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_action__action__add
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "Add"
msgstr "Dodaj"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Add File"
msgstr ""

#. module: documents
#: model:ir.actions.act_window,name:documents.action_url_form
msgid "Add Url"
msgstr ""

#. module: documents
#: model:ir.actions.act_window,name:documents.action_workspace_form
#, fuzzy
msgid "Add Workspace"
msgstr "Radni prostor"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Add new file"
msgstr ""

#. module: documents
#: model:res.groups,name:documents.group_documents_manager
msgid "Administrator"
msgstr "Administrator"

#. module: documents
#: model:documents.tag,name:documents.documents_marketing_assets_ads
msgid "Ads"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_id
msgid "Alias"
msgstr "Alias"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_contact
msgid "Alias Contact Security"
msgstr "Bezbednost alias kontakta"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_domain_id
#, fuzzy
msgid "Alias Domain"
msgstr "Domen aliasa"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_domain
#, fuzzy
msgid "Alias Domain Name"
msgstr "Domen aliasa"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_full_name
#, fuzzy
msgid "Alias Email"
msgstr "Domen aliasa"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_name
msgid "Alias Name"
msgstr "Naziv aliasa"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_status
msgid "Alias Status"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_status
msgid "Alias status assessed on the last message received."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_model_id
msgid "Aliased Model"
msgstr "Model aliasa"

#. module: documents
#. odoo-python
#: code:addons/documents/controllers/documents.py:0
msgid "All files uploaded"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__allow_upload
#, fuzzy
msgid "Allow upload"
msgstr "Preuzmi"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_workflow_rule.py:0
msgid "Already linked Documents"
msgstr ""

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid ""
"An easy way to process incoming mails is to configure your scanner to send "
"PDFs to"
msgstr ""

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid ""
"An easy way to process incoming mails is to configure your scanner to send "
"PDFs to your workspace email. Scanned files will appear automatically in "
"your workspace. Then, process your documents in bulk with the split tool: "
"launch user defined actions, request a signature, convert to vendor bills "
"with AI, etc."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "An error occured while uploading."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Archive original file(s)"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Are you sure that you want to delete the focused page ?"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Are you sure that you want to delete the selected page(s)"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Are you sure that you want to delete this page ?"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "Are you sure you want to permanently erase the document?"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "Are you sure you want to permanently erase the documents?"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid ""
"As this PDF contains multiple documents, let's split and process in bulk."
msgstr ""

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_finance_afv
msgid "Ask for Validation"
msgstr "Zatražite validaciju"

#. module: documents
#: model:documents.facet,name:documents.documents_marketing_assets
msgid "Assets"
msgstr "Aktivan"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Attached To"
msgstr "Priloženo"

#. module: documents
#: model:ir.model,name:documents.model_ir_attachment
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_id
msgid "Attachment"
msgstr "Prilog"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_attachment_count
#: model:ir.model.fields,field_description:documents.field_documents_share__message_attachment_count
msgid "Attachment Count"
msgstr "Brojač priloga"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__description
msgid "Attachment Description"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_name
msgid "Attachment Name"
msgstr "Naziv priloga"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_type
msgid "Attachment Type"
msgstr ""

#. module: documents
#: model_terms:documents.folder,description:documents.documents_finance_folder
msgid ""
"Automate your inbox using scanned documents or emails sent to <span "
"class=\"o_folder_description_alias\"><strong>inbox-financial</strong> email "
"alias</span>."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__available_rule_ids
msgid "Available Rules"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_bill
msgid "Bill"
msgstr "Račun"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Blank Page"
msgstr ""

#. module: documents
#: model:documents.folder,name:documents.documents_marketing_brand1_folder
msgid "Brand 1"
msgstr ""

#. module: documents
#: model:documents.folder,name:documents.documents_marketing_brand2_folder
msgid "Brand 2"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_marketing_assets_brochures
msgid "Brochures"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_mail_activity_type__folder_id
msgid "By defining a folder, the upload activities will generate a document"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "Bytes"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__can_upload
msgid "Can Upload"
msgstr "Može da učitava"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Can't share documents of different workspaces."
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_link_to_record_form_view
msgid "Cancel"
msgstr "Otkaži"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_folder.py:0
msgid ""
"Cannot move folder under the given parent as this would create a recursive "
"hierarchy"
msgstr ""

#. module: documents
#: model_terms:documents.folder,description:documents.documents_internal_folder
msgid "Categorize, share and keep track of all your internal documents."
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_facet
#: model:ir.model.fields,field_description:documents.field_documents_tag__facet_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__facet_id
#: model_terms:ir.ui.view,arch_db:documents.facet_view_tree
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Category"
msgstr "Kategorija"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__checksum
msgid "Checksum/SHA1"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_workflow_rule.py:0
msgid "Choose a record to link"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Choose or Configure Email Servers"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Click on a card to <b>select the document</b>."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Click on a thumbnail to <b>preview the document</b>."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid ""
"Click on the <b>page separator</b>: we don't want to split these two pages "
"as they belong to the same document."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Click the cross to <b>exit preview</b>."
msgstr ""

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__thumbnail_status__client_generated
msgid "Client Generated"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Close"
msgstr "Zatvori"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Close split tools"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_facet__color
msgid "Color"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__company_id
#: model:ir.model.fields,field_description:documents.field_documents_folder__company_id
msgid "Company"
msgstr "Kompanija"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__condition_type
msgid "Condition type"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Conditions"
msgstr "Uslovi"

#. module: documents
#: model:ir.model,name:documents.model_res_config_settings
#, fuzzy
msgid "Config Settings"
msgstr "Podešavanje"

#. module: documents
#: model:ir.ui.menu,name:documents.Config
msgid "Configuration"
msgstr "Podešavanje"

#. module: documents
#: model:ir.model,name:documents.model_res_partner
#: model:ir.model.fields,field_description:documents.field_documents_document__partner_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__partner_id
#: model:ir.model.fields,field_description:documents.field_documents_share__partner_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__criteria_partner_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Contact"
msgstr "Kontakt"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Contains"
msgstr "Sadrži"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_Contracts
#: model:documents.tag,name:documents.documents_internal_template_contracts
msgid "Contracts"
msgstr "Ugovori"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "Control panel buttons"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "Kreirajte"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_share_id
msgid "Create Share"
msgstr ""

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_internal_mark
msgid "Create Vendor Bill"
msgstr ""

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.mail_activity_plan_action_document
msgid "Create a Document Activity Plan"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_option
msgid "Create a new activity"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_facet__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_folder__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_share__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_tag__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__create_uid
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Created by"
msgstr "Kreirao"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_date
#: model:ir.model.fields,field_description:documents.field_documents_facet__create_date
#: model:ir.model.fields,field_description:documents.field_documents_folder__create_date
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__create_date
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__create_date
#: model:ir.model.fields,field_description:documents.field_documents_share__create_date
#: model:ir.model.fields,field_description:documents.field_documents_tag__create_date
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__create_date
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Creation Date"
msgstr "Datum Kreiranja"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__condition_type__criteria
msgid "Criteria"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Prilagođena odbijena poruka"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__days
#: model:ir.model.fields.selection,name:documents.selection__documents_share__activity_date_deadline_range_type__days
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__activity_date_deadline_range_type__days
msgid "Days"
msgstr "dana"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_defaults
msgid "Default Values"
msgstr "Podrazumevane vrednosti"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
#, fuzzy
msgid "Default contact"
msgstr "Podrazumevane vrednosti"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
#, fuzzy
msgid "Default owner"
msgstr "Podrazumevane vrednosti"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
msgid "Default values for uploaded documents"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__deletion_delay
#: model:ir.model.fields,help:documents.field_res_config_settings__deletion_delay
msgid "Delay after permanent deletion of the document in the trash (days)"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
#: code:addons/documents/static/src/views/list/documents_list_controller.js:0
#: code:addons/documents/static/src/views/list/folder_list_controller.js:0
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_tree
msgid "Delete"
msgstr "Obriši"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Delete focused or selected pages"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "Delete permanently"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_res_config_settings__deletion_delay
#, fuzzy
msgid "Deletion Delay"
msgstr "Obriši"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__deletion_delay
#, fuzzy
msgid "Deletion delay"
msgstr "Obriši"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Deletion delay (days)"
msgstr ""

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_internal_deprecate
msgid "Deprecate"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_internal_status_deprecated
msgid "Deprecated"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__description
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Description"
msgstr "Opis"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
#: model_terms:ir.ui.view,arch_db:documents.folder_deletion_form
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Discard"
msgstr "Poništi"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__display_name
#: model:ir.model.fields,field_description:documents.field_documents_facet__display_name
#: model:ir.model.fields,field_description:documents.field_documents_folder__display_name
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__display_name
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__display_name
#: model:ir.model.fields,field_description:documents.field_documents_share__display_name
#: model:ir.model.fields,field_description:documents.field_documents_tag__display_name
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__display_name
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__display_name
msgid "Display Name"
msgstr "Naziv za prikaz"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.js:0
msgid "Do you really want to unlink this record?"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "Do you want to exit without saving or gather pages into one document ?"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_document
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
#: model_terms:ir.ui.view,arch_db:documents.documents_view_list_main
msgid "Document"
msgstr "Dokument"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__document_count
#: model:ir.model.fields,field_description:documents.field_res_partner__document_count
msgid "Document Count"
msgstr "Brojač dokumenata"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Document Name"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__owner_id
msgid "Document Owner"
msgstr ""

#. module: documents
#: model:ir.actions.act_window,name:documents.mail_activity_plan_action_document
#, fuzzy
msgid "Document Plans"
msgstr "Dokumenti"

#. module: documents
#: model:ir.model,name:documents.model_documents_request_wizard
msgid "Document Request"
msgstr ""

#. module: documents
#: model:mail.template,subject:documents.mail_template_document_request
msgid "Document Request {{ object.name != False and ': '+ object.name or '' }}"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: code:addons/documents/models/mail_activity.py:0
msgid "Document Request: %(name)s Uploaded by: %(user)s"
msgstr ""

#. module: documents
#: model:mail.template,name:documents.mail_template_document_request_reminder
msgid "Document Request: Reminder"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__has_write_access
msgid "Document User Upload Rights"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_workflow_action
msgid "Document Workflow Tag Action"
msgstr ""

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_share__type__ids
msgid "Document list"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "Document preview"
msgstr ""

#. module: documents
#: model:mail.template,name:documents.mail_template_document_request
msgid "Document: Document Request"
msgstr ""

#. module: documents
#. odoo-javascript
#. odoo-python
#: code:addons/documents/models/documents_folder.py:0
#: code:addons/documents/models/res_partner.py:0
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
#: model:documents.facet,name:documents.documents_finance_documents
#: model:ir.actions.act_window,name:documents.document_action
#: model:ir.model.fields,field_description:documents.field_documents_folder__document_ids
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__document_ids
#: model:ir.model.fields,field_description:documents.field_res_users__document_count
#: model:ir.ui.menu,name:documents.dashboard
#: model:ir.ui.menu,name:documents.menu_root
#: model_terms:ir.ui.view,arch_db:documents.action_view_search
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.documents_view_activity
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Documents"
msgstr "Dokumenti"

#. module: documents
#: model:ir.model,name:documents.model_documents_link_to_record_wizard
msgid "Documents Link to Record"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_share
msgid "Documents Share"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_folder
msgid "Documents Workspace"
msgstr "Dokumenta radni prostor"

#. module: documents
#: model:ir.model,name:documents.model_documents_mixin
msgid "Documents creation mixin"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_unlink_mixin
#, fuzzy
msgid "Documents unlink mixin"
msgstr "Brojač dokumenata"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Does not contain"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__domain
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__domain
#: model:ir.model.fields.selection,name:documents.selection__documents_share__type__domain
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__condition_type__domain
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Domain"
msgstr "Domen"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_files_page
msgid "Download"
msgstr "Preuzmi"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_workspace_page
#, fuzzy
msgid "Download #{document.name}"
msgstr "Učitaj dokument"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_files_page
#: model_terms:ir.ui.view,arch_db:documents.share_workspace_page
msgid "Download all files"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_files_page
#, fuzzy
msgid "Download file"
msgstr "Preuzmi"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_drop_zone.xml:0
msgid "Drop files here to upload"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_date_deadline_range
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_date_deadline_range
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_date_deadline_range
msgid "Due Date In"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_date_deadline_range_type
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_date_deadline_range_type
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_date_deadline_range_type
msgid "Due type"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
msgid "Duplicate"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
#: code:addons/documents/static/src/views/search/documents_search_panel.xml:0
msgid "Edit"
msgstr "Uredi"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.js:0
msgid "Edit the linked record"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_email
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Email Alias"
msgstr "Email Alias"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__email_cc
msgid "Email cc"
msgstr "Email cc"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Enable to upload documents sent by email in workspace"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#: code:addons/documents/static/src/views/hooks.js:0
#: model:ir.model.fields.selection,name:documents.selection__documents_document__thumbnail_status__error
msgid "Error"
msgstr "Greška"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Escape Preview/Deselect/Exit"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__excluded_tag_ids
msgid "Excluded Tags"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.js:0
msgid "Exit Split Tools"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "Exit without saving"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_expense
msgid "Expense"
msgstr "Trošak"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_share__state__expired
msgid "Expired"
msgstr "Isteklo"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/list/documents_list_controller.js:0
#: code:addons/documents/static/src/views/list/folder_list_controller.js:0
msgid "Export"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Extra comments..."
msgstr ""

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_facet_name_unique
msgid "Facet already exists in this folder"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__favorited_ids
msgid "Favorite of"
msgstr ""

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__binary
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "File"
msgstr "Datoteka"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__datas
msgid "File Content (base64)"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__raw
msgid "File Content (raw)"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__file_extension
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "File Extension"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__file_size
msgid "File Size"
msgstr "Veličina Datoteke"

#. module: documents
#: model:ir.model,name:documents.model_ir_binary
msgid "File streaming helper model for controllers"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/controllers/documents.py:0
msgid "File uploaded by:"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Files Centralization"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_deletion_form
msgid "Files will be sent to trash and deleted forever after"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
#: code:addons/documents/static/src/views/list/folder_list_controller.js:0
msgid "Files will be sent to trash and deleted forever after %s days."
msgstr ""

#. module: documents
#: model:documents.folder,name:documents.documents_finance_folder
msgid "Finance"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_financial
msgid "Financial"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_fiscal
msgid "Fiscal"
msgstr ""

#. module: documents
#: model:documents.facet,name:documents.documents_finance_fiscal_year
msgid "Fiscal years"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Focus first page of next group"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Focus first page of previous group"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Focus next page"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Focus previous page"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "Fold"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_mail_activity_type__folder_id
msgid "Folder"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_follower_ids
#: model:ir.model.fields,field_description:documents.field_documents_share__message_follower_ids
msgid "Followers"
msgstr "Pratioci"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_partner_ids
#: model:ir.model.fields,field_description:documents.field_documents_share__message_partner_ids
msgid "Followers (Partners)"
msgstr "Pratioci (Partneri)"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome ikonica npr. fa-tasks"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "Gather in one document"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "Gb"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.facet_view_search
msgid "Group By"
msgstr "Grupiši po"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__read_group_ids
msgid ""
"Groups able to see the workspace and read its documents without create/edit "
"rights."
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__group_ids
msgid "Groups able to see the workspace and read/create/edit its documents."
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_internal_knowledge_hr
msgid "HR"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__has_message
#: model:ir.model.fields,field_description:documents.field_documents_share__has_message
msgid "Has Message"
msgstr "Ima poruku"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_document__previous_attachment_ids
msgid "History"
msgstr "Istorija"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__id
#: model:ir.model.fields,field_description:documents.field_documents_facet__id
#: model:ir.model.fields,field_description:documents.field_documents_folder__id
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__id
#: model:ir.model.fields,field_description:documents.field_documents_share__id
#: model:ir.model.fields,field_description:documents.field_documents_tag__id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__id
msgid "ID"
msgstr "ID"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task "
"creation alias)"
msgstr ""
"ID nadređenog zapisa koji sadrži alias (primer: projekat koji sadrži alias "
"kreiranja zadatka)"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_exception_icon
msgid "Icon"
msgstr "Ikona"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikonica koja označava aktivnost izuzetka."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_needaction
#: model:ir.model.fields,help:documents.field_documents_share__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Ako je označeno, nove poruke zahtevaju vašu pažnju."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_has_error
#: model:ir.model.fields,help:documents.field_documents_document__message_has_sms_error
#: model:ir.model.fields,help:documents.field_documents_share__message_has_error
#: model:ir.model.fields,help:documents.field_documents_share__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Ako je označeno, neke poruke imaju grešku u isporuci."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Ako je podešeno, ovaj sadržaj će se automatski slati neautorizovanim "
"korisnicima umesto podrazumevane poruke."

#. module: documents
#: model:documents.tag,name:documents.documents_marketing_assets_images
msgid "Images"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_finance_status_inbox
#: model:documents.tag,name:documents.documents_internal_status_inbox
#: model:mail.activity.type,name:documents.mail_documents_activity_data_Inbox
msgid "Inbox"
msgstr "Inbox"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__include_sub_folders
msgid "Include Sub Folders"
msgstr ""

#. module: documents
#: model_terms:documents.folder,description:documents.documents_internal_folder
msgid ""
"Incoming letters sent to <span class=\"o_folder_description_alias\">inbox "
"email alias</span> will be added to your inbox automatically."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__index_content
msgid "Indexed Content"
msgstr "Indeksirani sadržaj"

#. module: documents
#: model:documents.folder,name:documents.documents_internal_folder
msgid "Internal"
msgstr "Interni"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_editable_attachment
msgid "Is Editable Attachment"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_favorited
msgid "Is Favorited"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_is_follower
#: model:ir.model.fields,field_description:documents.field_documents_share__message_is_follower
msgid "Is Follower"
msgstr "Je Pratilac"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__is_shared
msgid "Is Shared"
msgstr "Je podeljeno"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_multipage
msgid "Is considered multipage"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_model.js:0
msgid "Items in trash will be deleted forever after %s days."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "Items moved to the trash will be deleted forever after %s days."
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "Kb"
msgstr ""

#. module: documents
#: model:documents.facet,name:documents.documents_internal_knowledge
msgid "Knowledge"
msgstr "Znanje"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_facet__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_folder__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_share__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_tag__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__write_uid
msgid "Last Updated by"
msgstr "Poslednje izmenio/la"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__write_date
#: model:ir.model.fields,field_description:documents.field_documents_facet__write_date
#: model:ir.model.fields,field_description:documents.field_documents_folder__write_date
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__write_date
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__write_date
#: model:ir.model.fields,field_description:documents.field_documents_share__write_date
#: model:ir.model.fields,field_description:documents.field_documents_tag__write_date
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__write_date
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__write_date
msgid "Last Updated on"
msgstr "Poslednje ažuriranje dana"

#. module: documents
#: model:documents.tag,name:documents.documents_internal_knowledge_legal
msgid "Legal"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid ""
"Let's process documents in your Inbox.<br/><i>Tip: Use Tags to filter "
"documents and structure your process.</i>"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Let's process these bills: send to Finance workspace."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Let's process this document, coming from our scanner."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid ""
"Let's tag this mail as legal<br/> <i>Tips: actions can be tailored to your "
"process, according to the workspace.</i>"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__user_specific
msgid "Limit Read Groups to the documents of which they are owner."
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__user_specific_write
msgid "Limit Write Groups to the documents of which they are owner."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
#: model_terms:ir.ui.view,arch_db:documents.documents_link_to_record_form_view
msgid "Link"
msgstr "Veza"

#. module: documents
#. odoo-python
#: code:addons/documents/controllers/documents.py:0
msgid "Link created by:"
msgstr ""

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__create_model__link_to_record
msgid "Link to record"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#, fuzzy
msgid "Links"
msgstr "Veza"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Links cannot be shared and therefore will be excluded."
msgstr ""

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_share__state__live
msgid "Live"
msgstr "Živo"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Lock"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_locked
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Locked"
msgstr "Zaključano"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__lock_uid
msgid "Locked by"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Log a note..."
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Login"
msgstr "Prijava"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Logout"
msgstr ""

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_internal_mad
msgid "Mark As Draft"
msgstr "Označi kao Priprema"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__remove_activities
msgid "Mark all as Done"
msgstr ""

#. module: documents
#: model:documents.folder,name:documents.documents_marketing_folder
msgid "Marketing"
msgstr "Marketing"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "Mb"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Media"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_note
#, fuzzy
msgid "Message"
msgstr "Poruke"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_error
#: model:ir.model.fields,field_description:documents.field_documents_share__message_has_error
msgid "Message Delivery error"
msgstr "Greška u isporuci poruke"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_ids
#: model:ir.model.fields,field_description:documents.field_documents_share__message_ids
msgid "Messages"
msgstr "Poruke"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__mimetype
msgid "Mime Type"
msgstr "Mime Tip"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Miscellaneous"
msgstr "Ostalo"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__model_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Model"
msgstr "Model"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__accessible_model_ids
msgid "Models"
msgstr ""

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__months
#: model:ir.model.fields.selection,name:documents.selection__documents_share__activity_date_deadline_range_type__months
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__activity_date_deadline_range_type__months
msgid "Months"
msgstr "Meseci"

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_finance_mti
msgid "Move To Inbox"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Move this page to trash"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__folder_id
msgid "Move to Workspace"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
#: code:addons/documents/static/src/views/list/folder_list_controller.js:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#: model_terms:ir.ui.view,arch_db:documents.folder_deletion_form
msgid "Move to trash"
msgstr ""

#. module: documents
#. odoo-javascript
#. odoo-python
#: code:addons/documents/models/documents_folder.py:0
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
#: code:addons/documents/static/src/views/list/folder_list_controller.js:0
msgid "Move to trash?"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_search
msgid "Moved To Trash"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Moved to trash"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector_field.js:0
msgid "Multiple values"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "My Activities"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Rok moje aktivnosti"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "My Documents"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "My Favorites"
msgstr "Moje Omiljeno"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__name
#: model:ir.model.fields,field_description:documents.field_documents_facet__name
#: model:ir.model.fields,field_description:documents.field_documents_folder__name
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__name
#: model:ir.model.fields,field_description:documents.field_documents_share__name
#: model:ir.model.fields,field_description:documents.field_documents_tag__name
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__name
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Name"
msgstr "Ime"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Name / Extension"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
msgid "Name of the share link"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
#: code:addons/documents/static/src/views/search/documents_search_panel.xml:0
msgid "New"
msgstr "Novo"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "New File"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "New Group"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "New Tag"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "New Workspace"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Sledeća aktivnost događaj u kalendaru"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Rok za sledeću aktivnosti"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_summary
msgid "Next Activity Summary"
msgstr "Pregled sledeće aktivnosti"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_type_id
msgid "Next Activity Type"
msgstr "Tip sledeće aktivnosti"

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.mail_activity_type_action_document
msgid "No activity types found. Let's create one!"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.xml:0
msgid "No alias configured"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "No document has been selected"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "No limit"
msgstr ""

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.share_action
msgid "No shared links"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Not a file"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Not attached"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_note
msgid "Note"
msgstr "Beleška"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_needaction_counter
#: model:ir.model.fields,field_description:documents.field_documents_share__message_needaction_counter
msgid "Number of Actions"
msgstr "Broj akcija"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__links_count
#, fuzzy
msgid "Number of Links"
msgstr "Broj akcija"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_error_counter
#: model:ir.model.fields,field_description:documents.field_documents_share__message_has_error_counter
msgid "Number of errors"
msgstr "Broj grešaka"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_needaction_counter
#: model:ir.model.fields,help:documents.field_documents_share__message_needaction_counter
#, fuzzy
msgid "Number of messages requiring action"
msgstr "Broj poruka koje zahtevaju akciju"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_has_error_counter
#: model:ir.model.fields,help:documents.field_documents_share__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Broj poruka sa greškom u isporuci"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Odoo Logo"
msgstr "Odoo Logo"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Odoo Website"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__limited_to_single_record
msgid "One record limit"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "Open chatter"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"Opcioni ID niza (zapisa) kom će biti dodate kao prilog sve dolazne mail "
"poruke, čak i ako na njih nisu odgovorili. Ako je podešeno, ovo ć́e u "
"potpunosti onemogućiti stvaranje novih zapisa."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.xml:0
msgid "Or send emails to"
msgstr "Ili pošalji email"

#. module: documents
#: model:documents.tag,name:documents.documents_internal_knowledge_other
msgid "Other"
msgstr "Ostalo"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__user_specific
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Own Documents Only"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__user_specific_write
msgid "Own Documents Only (Write)"
msgstr ""

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_folder_check_user_specific
msgid ""
"Own Documents Only may not be enabled for write groups if it is not enabled "
"for read groups."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__owner_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__requestee_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__criteria_owner_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Owner"
msgstr "Vlasnik"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_workspace_page
msgid "Owner: #{document.owner_id.name}"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_parent_model_id
msgid "Parent Model"
msgstr "Roditeljski Model"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__parent_path
msgid "Parent Path"
msgstr "Nadređena Putanja"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "ID niza nadređenog zapisa"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__parent_folder_id
#: model_terms:ir.ui.view,arch_db:documents.folder_view_search
msgid "Parent Workspace"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not "
"necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Nadređeni model koji sadrži alias. Model koji sadrži alias referencu nije "
"nužno model koji daje alias_model_id (primer: projekat (parent_model) i "
"zadatak (model))"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "Partial transfer"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.not_available
msgid "Please contact the person that shared this link for more information."
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following "
"channels\n"
msgstr ""
"Politika za objavljivanje poruka na dokumentima koristeći mail server.\n"
"- svi: svako može da objavljuje\n"
"- partneri: samo odobreni partneri\n"
"- pratioci: samo pratioci povezanog dokumenta ili članovi sledećih kanala\n"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__thumbnail_status__present
msgid "Present"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_internal_template_presentations
msgid "Presentations"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_files_page
msgid "Preview files"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_internal_template_project
msgid "Project"
msgstr "Projekat"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__rating_ids
#: model:ir.model.fields,field_description:documents.field_documents_share__rating_ids
msgid "Ratings"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Read Access"
msgstr "Pristup koji omogućava čitanje"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__read_group_ids
msgid "Read Groups"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Receiver will be able to upload documents"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Receiver will not be able to upload documents"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__resource_ref
msgid "Record"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_force_thread_id
msgid "Record Thread ID"
msgstr "ID niza zapisa"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Remaining Pages"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/activity/documents_activity_controller.js:0
msgid "Reminder emails have been sent."
msgstr ""

#. module: documents
#: model:mail.template,subject:documents.mail_template_document_request_reminder
msgid ""
"Reminder to upload your document{{ object.name and ' : ' + object.name or "
"'' }}"
msgstr ""

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_action__action__remove
msgid "Remove"
msgstr "Ukloni"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "Replace"
msgstr "Zameni"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_action__action__replace
msgid "Replace by"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__empty
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Request"
msgstr "Zahtev"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__request_activity_id
msgid "Request Activity"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Request To"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/web/activity/activity_menu_patch.xml:0
msgid "Request a Document"
msgstr ""

#. module: documents
#: model:ir.actions.act_window,name:documents.action_request_form
msgid "Request a file"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_request_page
msgid ""
"Requested\n"
"                                ∙"
msgstr ""

#. module: documents
#: model:mail.activity.type,name:documents.mail_documents_activity_data_md
msgid "Requested Document"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#, fuzzy
msgid "Requested Documents"
msgstr "Deljena dokumenta"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__required_tag_ids
msgid "Required Tags"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_model_name
msgid "Res Model Name"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__res_id
msgid "Resource ID"
msgstr "Šifra resursa"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_model
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__res_model
msgid "Resource Model"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_name
msgid "Resource Name"
msgstr "Naziv resursa"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_user_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_user_id
msgid "Responsible"
msgstr "Odgovorno lice"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_user_id
msgid "Responsible User"
msgstr "Odgovorni korisnik"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
#: code:addons/documents/static/src/views/list/folder_list_controller.js:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Restore"
msgstr "Obnovi"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_folder.py:0
msgid "Restricted Folder"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_sms_error
#: model:ir.model.fields,field_description:documents.field_documents_share__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Greška u dostavi SMS-a"

#. module: documents
#: model:documents.tag,name:documents.documents_internal_knowledge_sales
msgid "Sales"
msgstr "Prodaja"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Save"
msgstr "Sačuvaj"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_option
msgid "Schedule Activity"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/kanban/documents_kanban_renderer.js:0
#: code:addons/documents/static/src/views/list/documents_list_renderer.js:0
msgid "Select all"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select focused page"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select next page"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select next pages of the group"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select previous page"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select previous pages of the group"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select/Deselect all pages"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Send this letter to the legal department, by assigning the right tags."
msgstr ""

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_internal_legal
msgid "Send to Legal"
msgstr ""

#. module: documents
#: model:mail.template,description:documents.mail_template_document_request
msgid "Sent to partner when requesting a document from them"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_facet__sequence
#: model:ir.model.fields,field_description:documents.field_documents_folder__sequence
#: model:ir.model.fields,field_description:documents.field_documents_tag__sequence
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__sequence
msgid "Sequence"
msgstr "Niz"

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_finance_2018contracts
#, fuzzy
msgid "Set As 2024 Contracts"
msgstr "Ugovori"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__partner_id
msgid "Set Contact"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__user_id
msgid "Set Owner"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__tag_action_ids
msgid "Set Tags"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Set deletion delay for documents in the Trash"
msgstr ""

#. module: documents
#: model:mail.template,description:documents.mail_template_document_request_reminder
msgid ""
"Set reminders in activities to notify users who didn't upload their "
"requested document"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__has_owner_activity
msgid "Set the activity on the document owner"
msgstr ""

#. module: documents
#: model:ir.actions.act_window,name:documents.configuration_action
#: model:ir.actions.act_window,name:documents.settings_action
#: model:ir.ui.menu,name:documents.settings_menu
msgid "Settings"
msgstr "Podešavanje"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
#: code:addons/documents/static/src/views/search/documents_search_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Share"
msgstr "Podeli"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__share_link_ids
msgid "Share Links"
msgstr "Podeli linkove"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_share.py:0
#, fuzzy
msgid "Share documents"
msgstr "Deljena dokumenta"

#. module: documents
#. odoo-python
#: code:addons/documents/controllers/documents.py:0
msgid "Share link"
msgstr ""

#. module: documents
#: model:ir.actions.act_window,name:documents.share_action
msgid "Share links"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "Share this domain"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "Share this selection"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__type
msgid "Share type"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__create_share_id
msgid "Share used to create this document"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_share.py:0
#, fuzzy
msgid "Share workspace"
msgstr "Radni prostor"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__document_ids
msgid "Shared Documents"
msgstr "Deljena dokumenta"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__tag_ids
msgid "Shared Tags"
msgstr ""

#. module: documents
#: model:ir.ui.menu,name:documents.share_menu
msgid "Shares & Emails"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "Size"
msgstr "Veličina"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_files_page
#, fuzzy
msgid "Size:"
msgstr "Veličina"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Some files could not be uploaded (max size: %s)."
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.not_available
msgid "Sorry, this link is no longer valid."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__link_model
msgid "Specific Model Linked"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "Split"
msgstr "Podeli"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/attachments/document_file_viewer.xml:0
msgid "Split PDF"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Split all white pages"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Split selected pages"
msgstr ""

#. module: documents
#: model:documents.facet,name:documents.documents_finance_status
#: model:documents.facet,name:documents.documents_internal_status
#: model:ir.model.fields,field_description:documents.field_documents_share__state
msgid "Status"
msgstr "Status"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status na osnovu aktivnosti\n"
"Isteklo: Datum postavljen kao rok je već prošao\n"
"Danas: Datum aktivnosti je danas\n"
"Planirano: Buduće aktivnosti."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "Stay here"
msgstr ""

#. module: documents
#: model:ir.ui.menu,name:documents.structure
msgid "Structure"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__children_folder_ids
msgid "Sub workspaces"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_summary
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_summary
msgid "Summary"
msgstr "Pregled"

#. module: documents
#: model:ir.model,name:documents.model_documents_tag
#: model:ir.model.fields,field_description:documents.field_documents_facet__tag_ids
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__tag_id
#: model:ir.model.fields,field_description:documents.field_mail_activity_type__tag_ids
#: model_terms:ir.ui.view,arch_db:documents.tag_view_search
msgid "Tag"
msgstr "Oznaka"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__facet_ids
#: model_terms:ir.ui.view,arch_db:documents.folder_view_tree
msgid "Tag Categories"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.facet_view_form
msgid "Tag Category"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Tag Name"
msgstr "Naziv oznake"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_tag_facet_name_unique
msgid "Tag already exists for this facet"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__facet_ids
msgid "Tag categories defined for this workspace"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
#: model:ir.actions.act_window,name:documents.facet_action
#: model:ir.model.fields,field_description:documents.field_documents_document__tag_ids
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__tag_ids
#: model:ir.ui.menu,name:documents.category_menu
#: model_terms:ir.ui.view,arch_db:documents.facet_view_form
#: model_terms:ir.ui.view,arch_db:documents.facet_view_tree
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Tags"
msgstr "Oznake"

#. module: documents
#: model:documents.facet,name:documents.documents_internal_template
msgid "Templates"
msgstr "Šabloni"

#. module: documents
#: model:documents.tag,name:documents.documents_internal_template_text
msgid "Text"
msgstr "Tekst"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_facet__tooltip
msgid "Text shown when hovering on this tag category or its tags"
msgstr ""

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_res_config_settings_check_deletion_delay
msgid "The deletion delay should be positive."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "The links and requested documents are not shareable."
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming "
"email that does not reply to an existing record will cause the creation of a "
"new record of this model (e.g. a Project Task)"
msgstr ""
"Model (poput Odoo dokumenta) kome odgovara ovaj alias. Bilo koja dolazna "
"email adresa koja ne odgovori na postojeći zapis prouzrokovaće stvaranje "
"novog zapisa ovog modela (npr. Projektni zadatak)"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"Naziv email aliasa, npr. 'poslovi' ako želite da prikupljate email-ove za "
"<<EMAIL>>"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
#: code:addons/documents/static/src/views/inspector/documents_inspector.js:0
msgid "The share url has been copied to your clipboard."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.js:0
msgid "The share url has been copied to your clipboard. Links were excluded."
msgstr ""

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_share_share_unique
msgid "This access token already exists"
msgstr ""

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_document_attachment_unique
msgid "This attachment is already a document"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__group_ids
msgid "This attachment will only be available for the selected user groups"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_request_page
msgid ""
"This document has been requested.\n"
"                <b onclick=\"document.querySelector('.o_request_upload')."
"click()\" style=\"cursor:pointer;\">Upload it</b>."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "This file has been restored"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"This file has been sent to the trash and will be deleted forever on the %s"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__company_id
#: model:ir.model.fields,help:documents.field_documents_folder__company_id
msgid "This workspace will only be available to the selected company"
msgstr ""

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.mail_activity_type_action_document
msgid ""
"Those represent the different categories of things you have to do (e.g. "
"\"Call\" or \"Send email\")."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__thumbnail
msgid "Thumbnail"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__thumbnail_status
msgid "Thumbnail Status"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.xml:0
msgid "Tip: configure your scanner to send all documents to this address."
msgstr ""

#. module: documents
#: model:digest.tip,name:documents.digest_tip_documents_0
msgid "Tip: Become a paperless company"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_finance_status_tc
#: model:documents.tag,name:documents.documents_internal_status_tc
msgid "To Validate"
msgstr "Za potvrditi"

#. module: documents
#: model:mail.activity.type,name:documents.mail_documents_activity_data_tv
msgid "To validate"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/kanban/documents_kanban_renderer.js:0
msgid "Toggle favorite"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_facet__tooltip
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__note
#: model_terms:ir.ui.view,arch_db:documents.facet_view_form
msgid "Tooltip"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_model.js:0
msgid "Trash"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__is_editable_attachment
msgid "True if we can edit the link attachment."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__type
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Type"
msgstr "Vrsta"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Zabeležen tip aktivnosti izuzeća."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__url
#: model:ir.model.fields,field_description:documents.field_documents_share__full_url
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__url
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "URL"
msgstr "URL"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"URL %s does not seem complete, as it does not begin with http(s):// or ftp://"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__url_preview_image
msgid "URL Preview Image"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "Unfold"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Unlock"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "Unnamed"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
#: model_terms:ir.ui.view,arch_db:documents.share_workspace_page
msgid "Upload"
msgstr ""

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.document_action
msgid ""
"Upload <span class=\"fw-normal\">a file or</span> drag <span class=\"fw-"
"normal\">it here.</span>"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "Upload Document"
msgstr "Učitaj dokument"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__email_drop
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Upload by Email"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Upload documents sent to this email in workspace"
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/mail_activity.py:0
msgid "Upload file request"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_res_users
#: model:res.groups,name:documents.group_documents_user
msgid "User"
msgstr "Korisnik"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_vat
msgid "VAT"
msgstr "PDV"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__date_deadline
msgid "Valid Until"
msgstr ""

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_finance_validate
msgid "Validate"
msgstr "Overi"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_status_validated
#: model:documents.tag,name:documents.documents_internal_status_validated
msgid "Validated"
msgstr "Potvrđeno"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_files_page
#, fuzzy
msgid "Validity date:"
msgstr "Overi"

#. module: documents
#: model:documents.tag,name:documents.documents_marketing_assets_Videos
msgid "Videos"
msgstr "Video"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid ""
"Want to become a <b>paperless company</b>? Let's discover Odoo Documents."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__website_message_ids
#: model:ir.model.fields,field_description:documents.field_documents_share__website_message_ids
msgid "Website Messages"
msgstr "Website poruke"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__website_message_ids
#: model:ir.model.fields,help:documents.field_documents_share__website_message_ids
msgid "Website communication history"
msgstr "Istorija website komunikacije"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__weeks
#: model:ir.model.fields.selection,name:documents.selection__documents_share__activity_date_deadline_range_type__weeks
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__activity_date_deadline_range_type__weeks
msgid "Weeks"
msgstr "Nedelje"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "What do you want to do with the remaining pages ?"
msgstr ""

#. module: documents
#: model:ir.actions.act_window,name:documents.workflow_rule_action
msgid "Workflow Actions"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__workflow_rule_id
msgid "Workflow Rule"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_document__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_facet__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_share__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_tag__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__domain_folder_id
#: model_terms:ir.ui.view,arch_db:documents.facet_view_search
#: model_terms:ir.ui.view,arch_db:documents.folder_view_tree
msgid "Workspace"
msgstr "Radni prostor"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
#, fuzzy
msgid "Workspace to share"
msgstr "Radni prostor"

#. module: documents
#: model:ir.actions.act_window,name:documents.folder_action
#: model:ir.ui.menu,name:documents.folder_menu
#: model_terms:ir.ui.view,arch_db:documents.folder_view_tree
msgid "Workspaces"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid ""
"Wow... 6 documents processed in a few seconds, You're good.<br/>The tour is "
"complete. Try uploading your own documents now."
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Write Access"
msgstr "Pisanje"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__group_ids
msgid "Write Groups"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Write a tooltip for the action here"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__attachment_type
msgid ""
"You can either upload a file from your computer or copy/paste an internet "
"link to your file."
msgstr ""

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You don't have the right to move documents to that workspace."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "You don't have the rights to move documents to that workspace"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_drop_zone.xml:0
msgid "You must be in a specific writable workspace to upload files"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "YouTube Video"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
#, fuzzy
msgid "alias"
msgstr "Alias"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
#, fuzzy
msgid "cancel"
msgstr "Otkaži"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "copy link & close"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_deletion_form
msgid "days."
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "delete"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "document"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
#: model_terms:ir.ui.view,arch_db:documents.share_workspace_page
msgid "documents"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "documents selected"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "download"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "e.g. Discuss proposal"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "e.g. Finance"
msgstr "npr. Finansije"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "e.g. Missing Expense"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.facet_view_form
msgid "e.g. Status"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "e.g. To Validate"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "e.g. Validate document"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "e.g. https://www.youtube.com/watch?v=CP96yVnXNrY"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "e.g. mycompany.com"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "file_extension"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_files_page
msgid "files"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__is_readonly_model
msgid "is_readonly_model"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "restore"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_page/pdf_page.xml:0
msgid "select"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/inspector/documents_inspector.xml:0
msgid "selected"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
#: model_terms:ir.ui.view,arch_db:documents.share_workspace_page
msgid "shared by"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "status code: %(status)s, message: %(message)s"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "sub-doc-%s"
msgstr ""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "unnamed"
msgstr ""

#~ msgid "Archive"
#~ msgstr "Arhiva"

#~ msgid "Archived"
#~ msgstr "Arhivirano"

#~ msgid "Future Activities"
#~ msgstr "Buduće aktivnosti"

#~ msgid "Late Activities"
#~ msgstr "Zakasnele aktivnosti"

#~ msgid "Show all records which has next action date is before today"
#~ msgstr ""
#~ "Prikaži sve zapise koji imaju datum sledeće aktivnosti pre današnjeg "
#~ "datuma"

#~ msgid ""
#~ "The owner of records created upon receiving emails on this alias. If this "
#~ "field is not set the system will attempt to find the right owner based on "
#~ "the sender (From) address, or will use the Administrator account if no "
#~ "system user is found for that address."
#~ msgstr ""
#~ "Vlasnik zapisa kreiranih primanjem e-maila sa ovim aliasom. Ako ovo polje "
#~ "nije podešeno, sistem će pokušati da pronađe pravog vlasnika na osnovu "
#~ "adrese (Od) pošiljaoca, ili cće koristiti administratorski nalog ako za "
#~ "tu adresu nije pronađen korisnik sistema."

#~ msgid "Today Activities"
#~ msgstr "Današnje aktivnosti"

#~ msgid "name"
#~ msgstr "Ime"
