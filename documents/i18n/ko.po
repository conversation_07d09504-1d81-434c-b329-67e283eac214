# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON>, 2025
# Wil <PERSON>do<PERSON>, 2025
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-26 20:44+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Sarah Park, 2025\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid " (%s locked)"
msgstr " (%s 잠금)"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "%s (copy)"
msgstr "%s (사본)"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "%s (remaining pages)"
msgstr "%s (남은 페이지)"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "%s Files"
msgstr "%s 파일"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "%s Folders"
msgstr "%s 폴더"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/list/document_count_field.js:0
msgid "%s documents"
msgstr "%s 문서"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid "%s documents have been moved."
msgstr "%s 문서가 이동되었습니다."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "%s file(s) not moved because they are locked by another user"
msgstr "다른 사용자에 의해 잠겨있기 때문에 %s 파일을 이동할 수 없습니다."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.js:0
msgid "%s has been copied in My Drive."
msgstr "%s가 내 드라이브에 복사되었습니다."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.js:0
msgid "%s have been copied in My Drive."
msgstr "%s가 내 드라이브에 복사되었습니다."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "%s new document(s) created"
msgstr "새 문서 %s가 생성되었습니다"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "%s page(s) deleted"
msgstr "%s페이지가 삭제되었습니다"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "%s shared with you"
msgstr "이 폴더를 공유한 %s"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid "%s shortcuts have been created."
msgstr "%s 바로가기가 생성되었습니다."

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid ""
". Scanned files will appear automatically in your workspace. Then, process "
"your documents in bulk with the split tool: launch user defined actions, "
"request a signature, convert to vendor bills with AI, etc."
msgstr ""
". 스캔한 파일은 작업 공간에 자동으로 나타납니다. 이후, 분할 도구를 통해 문서를 일괄 처리합니다. 사용자 지정 작업을 시작하거나 서명"
" 요청 혹은 AI를 사용하여 공급업체 청구서로 변환하는 등의 작업이 가능합니다. "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.mail_template_document_share
msgid ".<br/>"
msgstr ".<br/>"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "1 File"
msgstr "파일 1개"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "1 Folder"
msgstr "폴더 1개"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/list/document_count_field.js:0
msgid "1 document"
msgstr "문서 1건"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_year_previous
msgid "2024"
msgstr "2024"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_year_current
msgid "2025"
msgstr "2025"

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid "<b class=\"tip_title\">Tip: Become a paperless company</b>"
msgstr "<b class=\"tip_title\">팁: 종이 없는 회사를 만듭니다.</b>"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "<b>Deselect this page</b> as we plan to process all bills first."
msgstr "먼저 업체 청구서를 모두 처리할 예정이오니 <b>페이지를 선택 해제하세요</b>."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "<b>Select</b> this page to continue."
msgstr "계속하려면 이 페이지를 <b>선택</b>합니다."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "<br/>Powered by"
msgstr "<br/>제공"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "<i class=\"fa fa-download fa-fw\"/> Download All"
msgstr "<i class=\"fa fa-download fa-fw\"/> 전체 다운로드"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_file
msgid "<i class=\"fa fa-download fa-fw\"/> Download file"
msgstr "<i class=\"fa fa-download fa-fw\"/> 파일 다운로드"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<i class=\"fa fa-external-link me-2\" title=\"This is a shortcut\"/>"
msgstr "<i class=\"fa fa-external-link me-2\" title=\"바로가기입니다\"/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<i class=\"fa fa-external-link text-gray-500\" title=\"This is a shortcut\"/>"
msgstr "<i class=\"fa fa-external-link text-gray-500\" title=\"바로가기입니다.\"/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_file
msgid "<i class=\"fa fa-eye\"/> Preview file"
msgstr "<i class=\"fa fa-eye\"/> 미리보기 파일"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "<i class=\"fa fa-lock oe_inline\" title=\"Locked\" invisible=\"not lock_uid\"/>"
msgstr "<i class=\"fa fa-lock oe_inline\" title=\"Locked\" invisible=\"not lock_uid\"/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "<i class=\"fa fa-upload\"/>  Upload"
msgstr "<i class=\"fa fa-upload\"/> 업로드"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_file
msgid "<i class=\"fa fa-upload\"/> Replace file"
msgstr "<i class=\"fa fa-upload\"/>  파일 바꾸기"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_document_res_partner_view
msgid "<span class=\"o_stat_text\">Documents</span>"
msgstr "<span class=\"o_stat_text\">문서</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "<span class=\"o_stat_text\">Related <br/> Record</span>"
msgstr "<span class=\"o_stat_text\">관련된 <br/> 레코드</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "<span title=\"Requested Document\">Requested Document</span>"
msgstr "<span title=\"Requested Document\">요청된 문서</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "<span>&amp;nbsp;Documents.</span>"
msgstr "<span>&amp;nbsp;문서.</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<span><b> Request</b></span>"
msgstr "<span><b> 요청</b></span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<span><b>Requested Document</b></span>"
msgstr "<span><b>요청된 문서</b></span>"

#. module: documents
#: model:mail.template,body_html:documents.mail_template_document_request
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"                    <tbody>\n"
"                        <!-- HEADER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\">\n"
"                                        <span style=\"font-size: 10px;\">\n"
"                                            Document Request: <br/>\n"
"                                            <t t-if=\"object.name\">\n"
"                                                <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Inbox Financial</span>\n"
"                                            </t>\n"
"                                        </span><br/>\n"
"                                    </td><td valign=\"middle\" align=\"right\" t-if=\"not object.create_uid.company_id.uses_default_logo\">\n"
"                                        <img t-attf-src=\"/logo.png?company={{ object.create_uid.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.create_uid.company_id.name\"/>\n"
"                                    </td></tr>\n"
"                                    <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- CONTENT -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                        <div>\n"
"                                            Hello <t t-out=\"object.owner_id.name or ''\">OdooBot</t>,\n"
"                                            <br/><br/>\n"
"                                            <t t-out=\"object.create_uid.name or ''\">OdooBot</t> (<t t-out=\"object.create_uid.email or ''\"><EMAIL></t>) asks you to provide the following document:\n"
"                                            <br/><br/>\n"
"                                            <center>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.name\">\n"
"                                                        <b t-out=\"object.name or ''\">Inbox Financial</b>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.request_activity_id.note\">\n"
"                                                        <i t-out=\"object.request_activity_id.note or ''\">Example of a note.</i>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <br/>\n"
"                                                <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                    <a t-att-href=\"object.access_url\" style=\"background-color: #875A7B; padding: 20px 30px 20px 30px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                        Upload the requested document\n"
"                                                    </a>\n"
"                                                </div>\n"
"                                            </center><br/>\n"
"                                            Please provide us with the missing document before <t t-out=\"object.request_activity_id.date_deadline\">2021-05-17</t>.\n"
"                                            <t t-if=\"user and user.signature\" data-o-mail-quote-container=\"1\">\n"
"                                                <br/>\n"
"                                                <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                                                <br data-o-mail-quote=\"1\"/>\n"
"                                            </t>\n"
"                                        </div>\n"
"                                    </td></tr>\n"
"                                    <tr><td style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- FOOTER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\" align=\"left\">\n"
"                                        <t t-out=\"object.create_uid.company_id.name or ''\">YourCompany</t>\n"
"                                    </td></tr>\n"
"                                    <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                                        <t t-out=\"object.create_uid.company_id.phone or ''\">******-123-4567</t>\n"
"                                        <t t-if=\"object.create_uid.company_id.email\">\n"
"                                            | <a t-attf-href=\"'mailto:%s' % {{ object.create_uid.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.email or ''\"><EMAIL></a>\n"
"                                        </t>\n"
"                                        <t t-if=\"object.create_uid.company_id.website\">\n"
"                                            | <a t-attf-href=\"'%s' % {{ object.create_uid.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.website or ''\">http://www.example.com</a>\n"
"                                        </t>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                    </table>\n"
"                    </td></tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                          <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"                            Powered by <a target=\"_blank\" href=\"https://www.odoo.com/app/documents\" style=\"color: #875A7B;\">Odoo Documents</a>\n"
"                          </td></tr>\n"
"                        </table>\n"
"                    </td></tr>\n"
"                </table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"                    <tbody>\n"
"                        <!-- HEADER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\">\n"
"                                        <span style=\"font-size: 10px;\">\n"
"                                            문서 요청: <br/>\n"
"                                            <t t-if=\"object.name\">\n"
"                                                <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Inbox Financial</span>\n"
"                                            </t>\n"
"                                        </span><br/>\n"
"                                    </td><td valign=\"middle\" align=\"right\" t-if=\"not object.create_uid.company_id.uses_default_logo\">\n"
"                                        <img t-attf-src=\"/logo.png?company={{ object.create_uid.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.create_uid.company_id.name\"/>\n"
"                                    </td></tr>\n"
"                                    <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- CONTENT -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                        <div>\n"
"                                            안녕하세요, <t t-out=\"object.owner_id.name or ''\">OdooBot</t>님.\n"
"                                            <br/><br/>\n"
"                                            <t t-out=\"object.create_uid.name or ''\">OdooBot</t> (<t t-out=\"object.create_uid.email or ''\"><EMAIL></t>)님이 다음과 같은 문서를 요청하고 있습니다:\n"
"                                            <br/><br/>\n"
"                                            <center>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.name\">\n"
"                                                        <b t-out=\"object.name or ''\">Inbox Financial</b>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.request_activity_id.note\">\n"
"                                                        <i t-out=\"object.request_activity_id.note or ''\">Example of a note.</i>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <br/>\n"
"                                                <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                    <a t-att-href=\"object.access_url\" style=\"background-color: #875A7B; padding: 20px 30px 20px 30px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                        요청 문서 업로드하기\n"
"                                                    </a>\n"
"                                                </div>\n"
"                                            </center><br/>\n"
"                                            누락된 문서는 링크 만료일 전 <t t-out=\"object.request_activity_id.date_deadline\">2021-05-17</t>까지 업로드 해주시기 바랍니다.\n"
"                                            <t t-if=\"user and user.signature\" data-o-mail-quote-container=\"1\">\n"
"                                                <br/>\n"
"                                                <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                                                <br data-o-mail-quote=\"1\"/>\n"
"                                            </t>\n"
"                                        </div>\n"
"                                    </td></tr>\n"
"                                    <tr><td style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- FOOTER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\" align=\"left\">\n"
"                                        <t t-out=\"object.create_uid.company_id.name or ''\">YourCompany</t>\n"
"                                    </td></tr>\n"
"                                    <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                                        <t t-out=\"object.create_uid.company_id.phone or ''\">******-123-4567</t>\n"
"                                        <t t-if=\"object.create_uid.company_id.email\">\n"
"                                            | <a t-attf-href=\"'mailto:%s' % {{ object.create_uid.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.email or ''\"><EMAIL></a>\n"
"                                        </t>\n"
"                                        <t t-if=\"object.create_uid.company_id.website\">\n"
"                                            | <a t-attf-href=\"'%s' % {{ object.create_uid.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.website or ''\">http://www.example.com</a>\n"
"                                        </t>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                    </table>\n"
"                    </td></tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                          <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"                            Powered by <a target=\"_blank\" href=\"https://www.odoo.com/app/documents\" style=\"color: #875A7B;\">Odoo Documents</a>\n"
"                          </td></tr>\n"
"                        </table>\n"
"                    </td></tr>\n"
"                </table>\n"
"            "

#. module: documents
#: model:mail.template,body_html:documents.mail_template_document_request_reminder
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"                    <tbody>\n"
"                        <!-- HEADER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\">\n"
"                                        <span style=\"font-size: 10px;\">\n"
"                                            Document Request: <br/>\n"
"                                            <t t-if=\"object.name\">\n"
"                                                <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Inbox Financial</span>\n"
"                                            </t>\n"
"                                        </span><br/>\n"
"                                    </td><td valign=\"middle\" align=\"right\">\n"
"                                        <img t-attf-src=\"/logo.png?company={{ object.create_uid.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.create_uid.company_id.name\"/>\n"
"                                    </td></tr>\n"
"                                    <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- CONTENT -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                        <div>\n"
"                                            Hello <t t-out=\"object.owner_id.name or ''\">OdooBot</t>,\n"
"                                            <br/><br/>\n"
"                                            This is a friendly reminder to upload your requested document:\n"
"                                            <br/><br/>\n"
"                                            <center>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.name\">\n"
"                                                        <b t-out=\"object.name or ''\">Inbox Financial</b>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.request_activity_id.note\">\n"
"                                                        <i t-out=\"object.request_activity_id.note or ''\">Example of a note.</i>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <br/>\n"
"                                                <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                    <a t-att-href=\"object.access_url\" style=\"background-color: #875A7B; padding: 20px 30px 20px 30px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                        Upload the requested document\n"
"                                                    </a>\n"
"                                                </div>\n"
"                                            </center><br/>\n"
"                                            Please provide us with the missing document before <t t-out=\"object.request_activity_id.date_deadline or ''\">2021-05-17</t>.\n"
"                                            <br/><br/>\n"
"                                            Thank you,\n"
"                                            <t t-if=\"user and user.signature\" data-o-mail-quote-container=\"1\">\n"
"                                                <br/>\n"
"                                                <t t-out=\"user.signature\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                                                <br data-o-mail-quote=\"1\"/>\n"
"                                            </t>\n"
"                                        </div>\n"
"                                    </td></tr>\n"
"                                    <tr><td style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- FOOTER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\" align=\"left\">\n"
"                                        <t t-out=\"object.create_uid.company_id.name or ''\">YourCompany</t>\n"
"                                    </td></tr>\n"
"                                    <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                                        <t t-out=\"object.create_uid.company_id.phone or ''\">******-123-4567</t>\n"
"                                        <t t-if=\"object.create_uid.company_id.email\">\n"
"                                            | <a t-attf-href=\"'mailto:%s' % {{ object.create_uid.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.email\"><EMAIL></a>\n"
"                                        </t>\n"
"                                        <t t-if=\"object.create_uid.company_id.website\">\n"
"                                            | <a t-att-href=\"object.create_uid.company_id.website\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.website\">http://www.example.com</a>\n"
"                                        </t>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                    </table>\n"
"                    </td></tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                          <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"                            Powered by <a target=\"_blank\" href=\"https://www.odoo.com/app/documents\" style=\"color: #875A7B;\">Odoo Documents</a>\n"
"                          </td></tr>\n"
"                        </table>\n"
"                    </td></tr>\n"
"                </table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"                    <tbody>\n"
"                        <!-- HEADER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\">\n"
"                                        <span style=\"font-size: 10px;\">\n"
"                                            문서 요청: <br/>\n"
"                                            <t t-if=\"object.name\">\n"
"                                                <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Inbox Financial</span>\n"
"                                            </t>\n"
"                                        </span><br/>\n"
"                                    </td><td valign=\"middle\" align=\"right\">\n"
"                                        <img t-attf-src=\"/logo.png?company={{ object.create_uid.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.create_uid.company_id.name\"/>\n"
"                                    </td></tr>\n"
"                                    <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- CONTENT -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                        <div>\n"
"                                            안녕하세요, <t t-out=\"object.owner_id.name or ''\">OdooBot</t>님.\n"
"                                            <br/><br/>\n"
"                                            요청된 문서가 업로드되지 않아 안내 메시지 드립니다.\n"
"                                            <br/><br/>\n"
"                                            <center>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.name\">\n"
"                                                        <b t-out=\"object.name or ''\">Inbox Financial</b>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.request_activity_id.note\">\n"
"                                                        <i t-out=\"object.request_activity_id.note or ''\">Example of a note.</i>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <br/>\n"
"                                                <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                    <a t-att-href=\"object.access_url\" style=\"background-color: #875A7B; padding: 20px 30px 20px 30px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                        요청 문서 업로드하기\n"
"                                                    </a>\n"
"                                                </div>\n"
"                                            </center><br/>\n"
"                                            누락된 문서는 링크 만료일 전인 <t t-out=\"object.request_activity_id.date_deadline or ''\">2021-05-17</t>까지 업로드 해주시기 바랍니다.\n"
"                                            <br/><br/>\n"
"                                            감사합니다.\n"
"                                            <t t-if=\"user and user.signature\" data-o-mail-quote-container=\"1\">\n"
"                                                <br/>\n"
"                                                <t t-out=\"user.signature\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                                                <br data-o-mail-quote=\"1\"/>\n"
"                                            </t>\n"
"                                        </div>\n"
"                                    </td></tr>\n"
"                                    <tr><td style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- FOOTER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\" align=\"left\">\n"
"                                        <t t-out=\"object.create_uid.company_id.name or ''\">YourCompany</t>\n"
"                                    </td></tr>\n"
"                                    <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                                        <t t-out=\"object.create_uid.company_id.phone or ''\">******-123-4567</t>\n"
"                                        <t t-if=\"object.create_uid.company_id.email\">\n"
"                                            | <a t-attf-href=\"'mailto:%s' % {{ object.create_uid.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.email\"><EMAIL></a>\n"
"                                        </t>\n"
"                                        <t t-if=\"object.create_uid.company_id.website\">\n"
"                                            | <a t-att-href=\"object.create_uid.company_id.website\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.website\">http://www.example.com</a>\n"
"                                        </t>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                    </table>\n"
"                    </td></tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                          <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"                            Powered by <a target=\"_blank\" href=\"https://www.odoo.com/app/documents\" style=\"color: #875A7B;\">Odoo Documents</a>\n"
"                          </td></tr>\n"
"                        </table>\n"
"                    </td></tr>\n"
"                </table>\n"
"            "

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr "이 별칭에 대한 새 레코드를 만들 때 기본값을 제공하도록 평가되는 Python 사전입니다."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "A destination is required when creating multiple shortcuts at once."
msgstr "한 번에 여러 개의 바로가기를 만들 때는 목적지가 필요합니다."

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_document_folder_id_not_id
msgid "A folder cannot be included in itself"
msgstr "폴더는 폴더 자체에 포함될 수 없습니다."

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_document_shortcut_document_id_not_id
msgid "A shortcut cannot point to itself"
msgstr "바로 가기는 자신을 가리킬 수 없습니다."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid "A shortcut has been created."
msgstr "바로가기가 생성되었습니다."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Access"
msgstr "사용 권한"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "Access Error"
msgstr "접근 에러"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__access_token
#: model:ir.model.fields,field_description:documents.field_documents_redirect__access_token
msgid "Access Token"
msgstr "사용 권한 토큰"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.portal_my_home_documents
msgid "Access all your documents"
msgstr "모든 문서에 액세스"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_access.py:0
msgid "Access documents and partners cannot be changed."
msgstr "문서 및 파트너에 대한 액세스는 수정할 수 없습니다."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Access to Automations"
msgstr "자동화에 대한 액세스"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid "Access to Server Actions"
msgstr "서버 작업에 대한 액세스"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Access to a folder or a document"
msgstr "폴더 또는 문서에 대한 액세스"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_document_document_token_unique
msgid "Access tokens already used."
msgstr "이미 사용한 토큰에 액세스"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__access_url
msgid "Access url"
msgstr "액세스 URL"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Action"
msgstr "추가 작업"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_needaction
msgid "Action Needed"
msgstr "조치 필요"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_pin_actions.xml:0
msgid "Actions on Select"
msgstr "선택 항목에 대한 작업"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__active
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_new_folder
msgid "Active"
msgstr "활성화"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_ids
#: model:ir.ui.menu,name:documents.mail_activities
msgid "Activities"
msgstr "활동"

#. module: documents
#: model:ir.model,name:documents.model_mail_activity
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Activity"
msgstr "활동"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "활동 예외 장식"

#. module: documents
#: model:ir.ui.menu,name:documents.mail_activity_plan_menu
msgid "Activity Plans"
msgstr "활동 계획"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_state
msgid "Activity State"
msgstr "활동 상태"

#. module: documents
#: model:ir.model,name:documents.model_mail_activity_type
msgid "Activity Type"
msgstr "활동 유형"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_type_icon
msgid "Activity Type Icon"
msgstr "활동 유형 아이콘"

#. module: documents
#: model:ir.actions.act_window,name:documents.mail_activity_type_action_document
#: model:ir.ui.menu,name:documents.mail_activity_type_menu
msgid "Activity Types"
msgstr "활동 유형"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "Activity assigned to"
msgstr "활동 배정 대상:"

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.mail_activity_plan_action_document
msgid ""
"Activity plans are used to assign a list of activities in just a few clicks\n"
"                    (e.g. \"NDA signature process\", \"Workspace workflow\", ...)"
msgstr ""
"작업 계획은 몇 번의 클릭으로 작업 목록을 배정하는 데 사용됩니다.\n"
"                    (e.g. \"NDA 서명 절차\", \"작업 공간 워크플로우\", ...)"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_type_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_type_id
msgid "Activity type"
msgstr "업무 유형"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "Add"
msgstr "추가"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_pin_actions.xml:0
msgid "Add Custom Action"
msgstr "사용자 지정 작업 추가"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Add File"
msgstr "파일 추가"

#. module: documents
#: model:ir.actions.act_window,name:documents.action_folder_form
msgid "Add Folder"
msgstr "폴더 추가"

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_tag_add_bill
msgid "Add Tag Bill"
msgstr "청구서 태그 추가"

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_tag_add_validated
msgid "Add Tag Validated"
msgstr "승인 태그 추가"

#. module: documents
#: model:ir.actions.act_window,name:documents.action_url_form
msgid "Add Url"
msgstr "URL 추가"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "Add a tag..."
msgstr "태그 추가..."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "Add an alias tag..."
msgstr "별명 태그 추가..."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Add new file"
msgstr "새로운 파일 추가"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Add people or email addresses..."
msgstr "사람 또는 이메일 주소 추가"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Add people or email addresses:"
msgstr "사람 또는 이메일 주소를 추가하세요:"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_shortcut.js:0
msgid "Add shortcut"
msgstr "바로 가기 추가"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_star.js:0
msgid "Add star"
msgstr "별표 추가"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Additional documents you have access to."
msgstr "액세스할 수 있는 추가 문서"

#. module: documents
#: model:res.groups,name:documents.group_documents_manager
msgid "Administrator"
msgstr "관리자"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_ads
msgid "Ads"
msgstr "추가"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_id
msgid "Alias"
msgstr "별칭"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_contact
msgid "Alias Contact Security"
msgstr "별칭 연락처 보안"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_domain_id
msgid "Alias Domain"
msgstr "별칭 도메인"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_domain
msgid "Alias Domain Name"
msgstr "별칭 도메인 이름"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_full_name
msgid "Alias Email"
msgstr "이메일 별칭"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_name
msgid "Alias Name"
msgstr "별칭 이름"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_status
msgid "Alias Status"
msgstr "별칭 상태"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_tag_ids
msgid "Alias Tags"
msgstr "별칭 태그"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_status
msgid "Alias status assessed on the last message received."
msgstr "최근 받은메일에서 확인한 별칭 상태입니다."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_model_id
msgid "Aliased Model"
msgstr "별칭 모델"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.js:0
msgid ""
"All users with access to this %(documentType)s or its parent will have edit "
"permissions."
msgstr "이 %(documentType)s 또는 상위 문서에 대한 액세스 권한이 있는 모든 사용자에게 편집 권한이 있습니다."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__access_ids
msgid "Allowed Access"
msgstr "허용된 액세스"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Already linked Documents: %s"
msgstr "이미 연결된 문서: %s"

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid ""
"An easy way to process incoming mails is to configure your scanner to send "
"PDFs to"
msgstr "다음의 이메일로 PDF 파일을 받도록 스캐너 설정을 해 두시면 수신 메일을 쉽게 처리할 수 있습니다:"

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid ""
"An easy way to process incoming mails is to configure your scanner to send "
"PDFs to your workspace email. Scanned files will appear automatically in "
"your workspace. Then, process your documents in bulk with the split tool: "
"launch user defined actions, request a signature, convert to vendor bills "
"with AI, etc."
msgstr ""
"받은 메일을 쉽게 처리하려면 작업 공간으로 PDF 파일을 전송하도록 스캐너 설정을 해놓으시면 됩니다. 스캔한 파일은 작업 공간에 자동으로"
" 나타납니다. 이후, 분할 도구를 통해 문서를 일괄 처리합니다 사용자 지정 작업을 시작하거나 서명 요청, AI를 사용하여 공급업체 "
"청구서로 변환하는 등의 작업이 가능합니다."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "An error occured while uploading."
msgstr "업로드 중에 오류가 발생했습니다."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.js:0
msgid "Anyone on the internet with the link can edit"
msgstr "인터넷에서 링크가 있는 사람은 누구나 편집 가능"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.js:0
msgid "Anyone on the internet with the link can view"
msgstr "인터넷에서 링크가 있는 사람은 누구나 보기 가능"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.xml:0
msgid "Anyone with the link"
msgstr "링크가 있는 사람 누구나"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Archive original file(s)"
msgstr "원본 파일 보관"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Are you sure that you want to delete the focused page ?"
msgstr "포커스가 맞춰진 페이지를 삭제하시겠습니까?"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Are you sure that you want to delete the selected page(s)"
msgstr "선택한 페이지를 정말로 삭제하시겠습니까?"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Are you sure that you want to delete this page ?"
msgstr "이 페이지를 정말로 삭제하시겠습니까?"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.js:0
msgid "Are you sure you want to delete this attachment?"
msgstr "이 첨부 파일을 삭제하시겠습니까?"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Are you sure you want to permanently erase the document?"
msgstr "이 문서를 완전히 삭제할까요?"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Are you sure you want to permanently erase the documents?"
msgstr "이 문서를 완전히 삭제할까요?"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid ""
"As this PDF contains multiple documents, let's split and process in bulk."
msgstr "이 PDF 에는 여러 개의 문서가 포함되어 있으므로, 분할하여 일괄 처리하겠습니다."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid ""
"At least one document couldn't be moved due to access rights. Shortcuts have"
" been created."
msgstr "일부 문서는 접근 권한이 부족하여 이동할 수 없습니다. 대신 바로가기가 생성되었습니다."

#. module: documents
#: model:ir.model,name:documents.model_ir_attachment
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_id
msgid "Attachment"
msgstr "첨부 파일"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_attachment_count
msgid "Attachment Count"
msgstr "첨부 파일 수"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__description
msgid "Attachment Description"
msgstr "첨부 파일 설명"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_name
msgid "Attachment Name"
msgstr "첨부 파일명"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_type
msgid "Attachment Type"
msgstr "첨부 파일 유형"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_automations.js:0
msgid "Automations"
msgstr "자동"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__available_embedded_actions_ids
msgid "Available Embedded Actions"
msgstr "사용 가능한 임베디드 액션"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_bill
msgid "Bill"
msgstr "청구"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Blank Page"
msgstr "빈 페이지"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_brochures
msgid "Brochures"
msgstr "브로셔"

#. module: documents
#: model:ir.model.fields,help:documents.field_mail_activity_type__folder_id
msgid "By defining a folder, the upload activities will generate a document"
msgstr "폴더를 정의하면 업로드 활동이 문서를 생성합니다"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_link_to_record_form_view
msgid "Cancel"
msgstr "취소"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__checksum
msgid "Checksum/SHA1"
msgstr "체크섬/SHA1"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__children_ids
msgid "Children"
msgstr "하위"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Choose a record to link"
msgstr "연결할 레코드 선택"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_details
msgid "Choose or Configure Email Servers"
msgstr "이메일 서버를 선택 또는 설정하세요."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Click on a card to <b>select the document</b>."
msgstr "<b>문서를 선택</b>하려면 카드를 클릭하세요."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Click on a thumbnail to <b>preview the document</b>."
msgstr "<b>문서 미리보기</b>를 하려면 썸네일을 클릭하세요."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid ""
"Click on the <b>page separator</b>: we don't want to split these two pages "
"as they belong to the same document."
msgstr "<b>페이지 구분 기호</b>를 선택하세요. 같은 문서에 있는 두 페이지이므로 분할하지 않습니다."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Click the cross to <b>exit preview</b>."
msgstr "X 표시를 클릭하여 <b>미리보기를 종료</b>합니다."

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__thumbnail_status__client_generated
msgid "Client Generated"
msgstr "클라이언트 생성"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.xml:0
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Close"
msgstr "닫기"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Close split tools"
msgstr "분할 도구 닫기"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_tag__color
msgid "Color"
msgstr "색상"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Common roots for all company users."
msgstr "모든 회사 사용자를 위한 공통 루트."

#. module: documents
#: model:ir.model,name:documents.model_res_company
msgid "Companies"
msgstr "회사"

#. module: documents
#. odoo-javascript
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.js:0
#: code:addons/documents/static/src/views/kanban/documents_kanban_record.js:0
#: model:ir.model.fields,field_description:documents.field_documents_document__company_id
msgid "Company"
msgstr "회사"

#. module: documents
#: model:ir.model,name:documents.model_res_config_settings
msgid "Config Settings"
msgstr "환경설정"

#. module: documents
#: model:ir.ui.menu,name:documents.Config
msgid "Configuration"
msgstr "설정"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model:ir.model,name:documents.model_res_partner
#: model:ir.model.fields,field_description:documents.field_documents_document__partner_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__partner_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Contact"
msgstr "연락처"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Contact your Administrator to get access if needed."
msgstr "액세스 권한이 필요한 경우 관리자에게 문의하세요."

#. module: documents
#: model:documents.tag,name:documents.documents_tag_contracts
msgid "Contracts"
msgstr "계약"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "Control panel buttons"
msgstr "제어판 버튼 "

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.js:0
msgid "Copy Link"
msgstr "링크 복사"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Copy Links"
msgstr "링크 복사"

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_create_activity
msgid "Create Activity"
msgstr "활동 생성"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Create Shortcut"
msgstr "바로 가기 만들기"

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.mail_activity_plan_action_document
msgid "Create a Document Activity Plan"
msgstr "문서 작업 계획 생성"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_option
msgid "Create a new activity"
msgstr "새로운 활동 만들기"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Create and edit new contact \""
msgstr "새 연락처 만들기 및 편집 \""

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Create new contact \""
msgstr "새 연락처 만들기 \""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_tag__create_uid
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Created by"
msgstr "작성자"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_date
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__create_date
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__create_date
#: model:ir.model.fields,field_description:documents.field_documents_tag__create_date
msgid "Created on"
msgstr "작성일자"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Creation Date"
msgstr "작성일자"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.xml:0
msgid "Current Version"
msgstr "현재 버전"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "고객 반송 메시지"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__create_activity_date_deadline_range_type__days
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__days
msgid "Days"
msgstr "일"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_defaults
msgid "Default Values"
msgstr "기본값"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__deletion_delay
#: model:ir.model.fields,help:documents.field_res_config_settings__deletion_delay
msgid "Delay after permanent deletion of the document in the trash (days)"
msgstr "휴지통에 있는 문서를 완전히 삭제하기까지의 기간 (날짜 수)"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.xml:0
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
#: code:addons/documents/static/src/views/list/documents_list_controller.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Delete"
msgstr "삭제"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Delete focused or selected pages"
msgstr "포커스 또는 선택된 페이지 삭제"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Delete permanently"
msgstr "완전히 삭제하기"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_res_config_settings__deletion_delay
msgid "Deletion Delay"
msgstr "삭제 대기"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__deletion_delay
msgid "Deletion delay"
msgstr "삭제 대기"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Deletion delay (days)"
msgstr "삭제 대기 (날짜 수)"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_deprecated
msgid "Deprecated"
msgstr "감가상각됨"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
#: code:addons/documents/static/src/views/hooks.js:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_rename
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
#: model_terms:ir.ui.view,arch_db:documents.folder_deletion_form
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Discard"
msgstr "취소"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Discoverable"
msgstr "확인 가능"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_access__display_name
#: model:ir.model.fields,field_description:documents.field_documents_document__display_name
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__display_name
#: model:ir.model.fields,field_description:documents.field_documents_redirect__display_name
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__display_name
#: model:ir.model.fields,field_description:documents.field_documents_tag__display_name
msgid "Display Name"
msgstr "표시명"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "Do you want to exit without saving or gather pages into one document ?"
msgstr "저장하지 않고 종료하거나 페이지를 하나의 문서로 모으시겠습니까?"

#. module: documents
#: model:ir.model,name:documents.model_documents_document
#: model:ir.model.fields,field_description:documents.field_documents_access__document_id
#: model:ir.model.fields,field_description:documents.field_documents_redirect__document_id
#: model:ir.model.fields,field_description:documents.field_documents_tag__document_ids
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "Document"
msgstr "문서"

#. module: documents
#: model:ir.model,name:documents.model_documents_access
msgid "Document / Partner"
msgstr "문서 / 파트너"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.xml:0
msgid "Document Access Internal"
msgstr "내부 문서 액세스"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.xml:0
msgid "Document Access Link"
msgstr "문서 액세스 링크"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__document_count
#: model:ir.model.fields,field_description:documents.field_res_partner__document_count
msgid "Document Count"
msgstr "문서수"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Document Name"
msgstr "문서명"

#. module: documents
#: model:ir.actions.act_window,name:documents.mail_activity_plan_action_document
msgid "Document Plans"
msgstr "문서 계획"

#. module: documents
#: model:ir.model,name:documents.model_documents_redirect
msgid "Document Redirect"
msgstr "문서 리디렉션"

#. module: documents
#: model:ir.model,name:documents.model_documents_request_wizard
msgid "Document Request"
msgstr "문서 요청"

#. module: documents
#: model:mail.template,subject:documents.mail_template_document_request
msgid ""
"Document Request {{ object.name != False and ': '+ object.name or '' }}"
msgstr "문서 요청 {{ object.name != False and ': '+ object.name or '' }}"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: code:addons/documents/models/mail_activity.py:0
msgid "Document Request: %(name)s Uploaded by: %(user)s"
msgstr "문서 요청: %(name)s 업로드한 사람: %(user)s"

#. module: documents
#: model:mail.template,name:documents.mail_template_document_request_reminder
msgid "Document Request: Reminder"
msgstr "문서 요청: 알림"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__document_token
msgid "Document Token"
msgstr "문서 토큰"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "Document preview"
msgstr "문서 미리보기"

#. module: documents
#. odoo-python
#: code:addons/documents/controllers/documents.py:0
msgid "Document uploaded by %(user)s"
msgstr "%(user)s가 업로드한 문서"

#. module: documents
#: model:mail.template,name:documents.mail_template_document_request
msgid "Document: Document Request"
msgstr "문서: 문서 요청"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/list/document_count_field.js:0
msgid "DocumentCountIntegerField"
msgstr "DocumentCountIntegerField"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: code:addons/documents/models/res_partner.py:0
#: model:ir.actions.act_window,name:documents.document_action
#: model:ir.actions.act_window,name:documents.document_action_portal
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__document_ids
#: model:ir.model.fields,field_description:documents.field_res_users__document_count
#: model:ir.ui.menu,name:documents.dashboard
#: model:ir.ui.menu,name:documents.menu_root
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.documents_view_activity
#: model_terms:ir.ui.view,arch_db:documents.portal_my_home_documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Documents"
msgstr "문서"

#. module: documents
#: model:ir.model,name:documents.model_documents_link_to_record_wizard
msgid "Documents Link to Record"
msgstr "레코드로 문서 연결"

#. module: documents
#: model:ir.model,name:documents.model_documents_mixin
msgid "Documents creation mixin"
msgstr "문서 작성 혼합물"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "Documents in the Trash cannot be shared"
msgstr "휴지통에 있는 문서는 공유할 수 없습니다."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.js:0
msgid "Documents moved to trash will show up here"
msgstr "휴지통으로 이동된 문서는 여기에 표시됩니다."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.js:0
msgid "Documents shared with you will appear here"
msgstr "공유된 문서는 여기에 표시됩니다."

#. module: documents
#: model:ir.model,name:documents.model_documents_unlink_mixin
msgid "Documents unlink mixin"
msgstr "문서 연결 해제 믹스인"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.xml:0
msgid "Done"
msgstr "완료"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "Done!"
msgstr "완료!"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.xml:0
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_download.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Download"
msgstr "다운로드"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Download #{document.name}"
msgstr "다운로드됨 #{document.name}"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Download all files"
msgstr "전체 파일 다운로드"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_file
msgid "Download file"
msgstr "파일 다운로드"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Download folder as zip"
msgstr "폴더를 zip으로 다운로드"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Download zip #{subfolder.name}"
msgstr "Download zip #{subfolder.name}"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_draft
msgid "Draft"
msgstr "초안"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_drop_zone.xml:0
msgid "Drop files here to upload"
msgstr "파일을 올릴려면 여기에 끌어다 놓으세요"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_date_deadline_range
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_date_deadline_range
msgid "Due Date In"
msgstr "만기일"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_date_deadline_range_type
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_date_deadline_range_type
msgid "Due type"
msgstr "만기 유형"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Duplicate"
msgstr "복사"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
#: code:addons/documents/static/src/views/search/documents_search_panel.xml:0
msgid "Edit"
msgstr "편집"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: model:ir.model.fields.selection,name:documents.selection__documents_access__role__edit
#: model:ir.model.fields.selection,name:documents.selection__documents_document__access_internal__edit
#: model:ir.model.fields.selection,name:documents.selection__documents_document__access_via_link__edit
#: model:ir.model.fields.selection,name:documents.selection__documents_document__user_permission__edit
msgid "Editor"
msgstr "편집기"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "Email"
msgstr "이메일"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_email
msgid "Email Alias"
msgstr "이메일 별칭"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_details
msgid "Email Upload"
msgstr "이메일 업로드"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__email_cc
msgid "Email cc"
msgstr "이메일 참조"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "이메일 도메인 예: '<EMAIL>'의 'example.com'"

#. module: documents
#: model:ir.actions.act_window,name:documents.mail_alias_action
msgid "Email links"
msgstr "이메일 링크"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#: code:addons/documents/static/src/views/hooks.js:0
#: model:ir.model.fields.selection,name:documents.selection__documents_document__thumbnail_status__error
msgid "Error"
msgstr "오류"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Escape Preview/Deselect/Exit"
msgstr "미리보기 나가기/선택 해제/종료"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "Exit Split Tools"
msgstr "분할 도구 종료"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "Exit without saving"
msgstr "저장하지 않고 종료"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_partner_access.xml:0
msgid "Exp:"
msgstr "예상:"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_expense
msgid "Expense"
msgstr "경비"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_access__expiration_date
msgid "Expiration"
msgstr "만료일"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/list/documents_list_controller.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Export"
msgstr "내보내기"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Extra comments..."
msgstr "추가 댓글..."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__favorited_ids
msgid "Favorite of"
msgstr "즐겨찾기"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_type_icon/documents_type_icon.xml:0
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__binary
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "File"
msgstr "파일"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__datas
msgid "File Content (base64)"
msgstr "파일 컨텐츠(base64)"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__raw
msgid "File Content (raw)"
msgstr "파일 컨텐츠(raw)"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__file_extension
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "File Extension"
msgstr "파일 확장자"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__file_size
msgid "File Size"
msgstr "파일 크기"

#. module: documents
#: model:ir.model,name:documents.model_ir_binary
msgid "File streaming helper model for controllers"
msgstr "컨트롤러용 파일 스트리밍 헬퍼 모델"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/kanban/documents_kanban_renderer.xml:0
msgid "Files"
msgstr "파일"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Files Centralization"
msgstr "파일 중앙화"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_deletion_form
msgid "Files will be sent to trash and deleted forever after"
msgstr "파일을 휴지통으로 이동하여 다음 날짜 이후 완전히 삭제합니다."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
msgid "Files will be sent to trash and deleted forever after %s days."
msgstr "파일을 휴지통으로 이동하여 %s일 이후 완전히 삭제합니다."

#. module: documents
#: model:documents.tag,name:documents.documents_tag_financial
msgid "Financial"
msgstr "재무"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_fiscal
msgid "Fiscal"
msgstr "회계"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Focus first page of next group"
msgstr "다음 그룹의 첫 페이지에 포커스 맞추기"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Focus first page of previous group"
msgstr "이전 그룹의 첫 페이지에 포커스 맞추기"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Focus next page"
msgstr "다음 페이지에 포커스"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Focus previous page"
msgstr "이전 페이지에 포커스"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
#: code:addons/documents/static/src/views/documents_type_icon/documents_type_icon.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_document__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__folder_id
#: model:ir.model.fields,field_description:documents.field_mail_activity_type__folder_id
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__folder
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "Folder"
msgstr "폴더"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/kanban/documents_kanban_renderer.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Folders"
msgstr "폴더"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_follower_ids
msgid "Followers"
msgstr "팔로워"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_partner_ids
msgid "Followers (Partners)"
msgstr "팔로워 (협력사)"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "멋진 아이콘 폰트 예: fa-tasks"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "Gather in one document"
msgstr "하나의 문서로 모으기"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_details
msgid "General"
msgstr "일반"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.xml:0
msgid "General access"
msgstr "일반 액세스"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "GiB"
msgstr "GiB"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Group By"
msgstr "그룹별"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_hr
msgid "HR"
msgstr "인사"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__has_message
msgid "Has Message"
msgstr "메시지가 있습니다"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Hide shortcuts"
msgstr "바로 가기 숨기기"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__previous_attachment_ids
msgid "History"
msgstr "기록"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_access__id
#: model:ir.model.fields,field_description:documents.field_documents_document__id
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__id
#: model:ir.model.fields,field_description:documents.field_documents_redirect__id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__id
#: model:ir.model.fields,field_description:documents.field_documents_tag__id
msgid "ID"
msgstr "ID"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr "별칭을 담고 있는 상위 레코드의 ID (예: 별칭을 생성한 작업을 담고 있는 프로젝트)"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_exception_icon
msgid "Icon"
msgstr "아이콘"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "예외 활동을 표시하기 위한 아이콘"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__is_access_via_link_hidden
msgid ""
"If \"True\", only people given direct access to this document will be able "
"to view it. If \"False\", access with the link also given to all who can "
"access the parent folder."
msgstr ""
"\"True\"로 설정하면 이 문서에 직접 액세스할 수 있는 사용자만 문서를 볼 수 있습니다. “False\"로 설정하면 상위 폴더에 "
"대한 액세스 권한이 있는 사람을 포함하여 링크를 가진 모든 사람이 문서를 볼 수 있습니다."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_needaction
msgid "If checked, new messages require your attention."
msgstr "만약 선택하였으면, 신규 메시지에 주의를 기울여야 합니다."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_has_error
#: model:ir.model.fields,help:documents.field_documents_document__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "이 옵션을 선택하면 일부 정보가 전달 오류를 생성합니다."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr "설정할 경우, 권한이 없는 사용자에게 기본 메시지 대신 이 내용을 전송합니다. "

#. module: documents
#: model:documents.tag,name:documents.documents_tag_images
msgid "Images"
msgstr "이미지"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Impossible to delete folders used by other applications."
msgstr "다른 앱에서 사용하고 있는 폴더는 삭제할 수 없습니다."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Impossible to invite partners on multiple documents at once."
msgstr "여러 문서에 동시에 파트너를 초대할 수 없습니다."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "In"
msgstr "In"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__thumbnail_status__restricted
msgid "Inaccessible"
msgstr "접근 불가"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_inbox
#: model:mail.activity.type,name:documents.mail_documents_activity_data_Inbox
msgid "Inbox"
msgstr "수신함"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"Incorrect values. Use one of the following for the following fields: "
"%(hints)s.)"
msgstr "잘못된 값입니다. 다음 필드에 다음 중 하나를 사용합니다: %(hints)s.)"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__index_content
msgid "Indexed Content"
msgstr "색인화된 콘텐츠"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_details.js:0
msgid "Info & Tags"
msgstr "정보 & 태그"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Info & tags"
msgstr "정보 & 태그"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.xml:0
msgid "Internal Users"
msgstr "내부 사용자"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__access_internal
msgid "Internal Users Rights"
msgstr "내부 사용자 권한"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.js:0
msgid "Internal users can edit"
msgstr "내부 사용자 편집 가능"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.js:0
msgid "Internal users can view"
msgstr "내부 사용자 보기 가능"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Invalid folder id"
msgstr "잘못된 폴더 ID"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "Invalid operation"
msgstr "잘못된 작업"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Invalid role."
msgstr "잘못된 역할입니다."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_editable_attachment
msgid "Is Editable Attachment"
msgstr "편집 가능한 첨부 파일입니다."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_favorited
msgid "Is Favorited"
msgstr "즐겨찾기에 포함되어 있음"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_is_follower
msgid "Is Follower"
msgstr "팔로워임"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_multipage
msgid "Is considered multipage"
msgstr "페이지가 여러 장 있는 것으로 간주합니다."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"It is not possible to move archived documents or documents to archived "
"folders."
msgstr "보관된 문서 또는 보관된 폴더에 있는 문서는 이동할 수 없습니다."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"Item(s) you wish to restore are included in archived folders. To restore these items, you must restore the following including folders instead:\n"
"- %(folders_list)s"
msgstr ""
"복원하려는 항목이 보관된 폴더에 있습니다. 이러한 항목을 복원하려면 대신 다음 폴더도 복원해야 합니다:\n"
"- %(folders_list)s"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Items in trash will be deleted forever after %s days."
msgstr "휴지통에 있는 항목은 %s일 후 완전히 삭제됩니다."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Items moved to the trash will be deleted forever after %s days."
msgstr "휴지통으로 이동된 항목은 %s일 후 완전히 삭제됩니다."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_access__last_access_date
#: model:ir.model.fields,field_description:documents.field_documents_document__last_access_date_group
msgid "Last Accessed On"
msgstr "마지막 액세스 날짜"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_tag__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__write_date
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__write_date
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__write_date
#: model:ir.model.fields,field_description:documents.field_documents_tag__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_legal
msgid "Legal"
msgstr "합법"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid ""
"Let's process documents in your Inbox.<br/><i>Tip: Use Tags to filter "
"documents and structure your process.</i>"
msgstr "받은 메일함에서 문서를 처리해봅시다.<br/><i>도움말: 문서 필터와 작업 구축에 태그를 활용하세요.</i>"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Let's process these bills: send to Finance workspace."
msgstr "이 업체 청구서를 처리해봅시다. 재무 작업 공간으로 전송합니다."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Let's process this document, coming from our scanner."
msgstr "스캐너에서 전송된 이 문서를 처리해봅시다."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid ""
"Let's tag this mail as legal<br/> <i>Tips: actions can be tailored to your "
"process, according to the workspace.</i>"
msgstr ""
"이 메일에 법률 관련 태그를 지정합시다.<br/><i>도움말: 작업 공간에 따라서 프로세스에 맞게 작업을 조정할 수 있습니다.</i>"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
#: code:addons/documents/static/src/views/documents_type_icon/documents_type_icon.xml:0
#: model_terms:ir.ui.view,arch_db:documents.documents_link_to_record_form_view
msgid "Link"
msgstr "링크"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_access_via_link_hidden
msgid "Link Access Hidden"
msgstr "링크 액세스 숨김"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__access_via_link
msgid "Link Access Rights"
msgstr "링크 액세스 권한"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__url
msgid "Link URL"
msgstr "링크 URL"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.js:0
msgid "Link copied to clipboard!"
msgstr "링크가 클립보드에 복사되었습니다!"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.xml:0
msgid "Link copied to the clipboard."
msgstr "링크가 클립보드에 복사되었습니다."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "Linked To"
msgstr "다음과 연결됨"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.js:0
msgid "Links copied to clipboard!"
msgstr "링크가 클립보드에 복사되었습니다!"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "로컬 부품 기반 입고 감지"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Lock"
msgstr "잠금"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_document__is_locked
msgid "Locked"
msgstr "잠김"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__lock_uid
msgid "Locked by"
msgstr "잠근 사람"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Log a note..."
msgstr "메모 기록"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Login"
msgstr "로그인 ID"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Logout"
msgstr "로그아웃"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Mail: %s"
msgstr "메일: %s"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Manage Versions"
msgstr "버전 관리"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.js:0
msgid "Manage Versions of \"%s\""
msgstr " \"%s\"의 버전 관리"

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_remove_activities
msgid "Mark activities as completed"
msgstr "활동을 완료로 표시"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Merge PDFs"
msgstr "PDF 병합"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_note
msgid "Message"
msgstr "메시지"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_error
msgid "Message Delivery error"
msgstr "메시지 전송 오류"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_ids
msgid "Messages"
msgstr "메시지"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "MiB"
msgstr "MiB"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__mimetype
msgid "Mime Type"
msgstr "MIME 유형"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Missing documents reference."
msgstr "누락된 문서 참조"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__model_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Model"
msgstr "모델"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__accessible_model_ids
msgid "Models"
msgstr "모델"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__create_activity_date_deadline_range_type__months
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__months
msgid "Months"
msgstr "월"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "More"
msgstr "더 보기"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Move this page to trash"
msgstr "이 페이지를 휴지통으로 이동"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Move to Trash"
msgstr "휴지통으로 이동"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_archive.js:0
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
#: code:addons/documents/static/src/views/hooks.js:0
#: model_terms:ir.ui.view,arch_db:documents.folder_deletion_form
msgid "Move to trash"
msgstr "휴지통으로 이동"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
msgid "Move to trash?"
msgstr "휴지통으로 이동할까요?"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Moved to trash"
msgstr "휴지통으로 이동"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "Must have the link to access"
msgstr "액세스하려면 링크가 있어야 합니다."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "My Activities"
msgstr "내 활동"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "내 활동 마감일"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "My Documents"
msgstr "내 문서"

#. module: documents
#. odoo-javascript
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.js:0
#: code:addons/documents/static/src/views/kanban/documents_kanban_record.js:0
msgid "My Drive"
msgstr "내 드라이브"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_access_role_or_last_access_date
msgid "NULL roles must have a set last_access_date"
msgstr "NULL 역할에는 last_access_date가 설정되어 있어야 합니다."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__name
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__name
#: model:ir.model.fields,field_description:documents.field_documents_tag__name
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Name"
msgstr "이름"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Name / Extension"
msgstr "이름 / 내선번호"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "New"
msgstr "신규"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "New File"
msgstr "새 파일"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.xml:0
msgid "New Folder"
msgstr "새 폴더"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "New Group"
msgstr "새 그룹"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "다음 활동 캘린더 행사"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "다음 활동 마감일"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_summary
msgid "Next Activity Summary"
msgstr "다음 활동 요약"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_type_id
msgid "Next Activity Type"
msgstr "다음 활동 유형"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "No activity"
msgstr "활동없음"

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.mail_activity_type_action_document
msgid "No activity types found. Let's create one!"
msgstr "활동 유형이 없습니다. 지금 생성해 주세요."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.xml:0
msgid "No alias configured"
msgstr "별칭이 구성되어 있지 않음"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "No document has been selected"
msgstr "선택된 문서가 없습니다."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/chatter/documents_chatter_overlay.xml:0
msgid "No item selected"
msgstr "선택한 항목 없음"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.js:0
msgid "No one on the internet can access"
msgstr "인터넷에서 아무도 액세스할 수 없음"

#. module: documents
#. odoo-python
#: code:addons/documents/models/ir_actions_server.py:0
msgid "No server actions found for Documents!"
msgstr "문서에 대한 서버 작업이 없습니다!"

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.mail_alias_action
msgid "No shared links"
msgstr "공유된 링크가 없습니다."

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__access_internal__none
#: model:ir.model.fields.selection,name:documents.selection__documents_document__access_via_link__none
#: model:ir.model.fields.selection,name:documents.selection__documents_document__user_permission__none
msgid "None"
msgstr "없음"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Not a file"
msgstr "파일이 아닙니다"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Not a folder."
msgstr "폴더가 아닙니다."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Not attached"
msgstr "첨부되지 않았습니다"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "Not set"
msgstr "설정되지 않음"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_note
msgid "Note"
msgstr "노트"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Notify"
msgstr "알림"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_needaction_counter
msgid "Number of Actions"
msgstr "작업 수"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_error_counter
msgid "Number of errors"
msgstr "오류 횟수"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "조치가 필요한 메시지 수"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "전송 오류가 발생한 메시지 수입니다."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Odoo Logo"
msgstr "Odoo 로고"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Odoo Studio - Customize workflows in minutes"
msgstr "Odoo 스튜디오 - 몇 분 만에 워크플로우 사용자 지정"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Odoo Website"
msgstr "Odoo 웹사이트"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__last_access_date_group__0_older
msgid "Older"
msgstr "장기"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Only Documents Managers can create in company folder."
msgstr "문서 관리자만 회사 폴더에 문서를 만들 수 있습니다."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Only Documents Managers can set aliases."
msgstr "문서 관리자만 별칭을 설정할 수 있습니다."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Only Documents managers can set an alias."
msgstr "문서 관리자만 별칭을 설정할 수 있습니다."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.js:0
msgid "Only people with access can open with the link"
msgstr "액세스 권한이 있는 사용자만 링크를 통해 문서를 열 수 있습니다."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Open #{document_url.name}"
msgstr "Open #{document_url.name}"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Open folder #{subfolder.name}"
msgstr "Open folder #{subfolder.name}"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "Open the permissions panel"
msgstr "권한 패널 열기"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"Operation not supported. Please use \"Move to Trash\" / `action_archive` "
"instead."
msgstr "지원되지 않는 작업입니다. \"휴지통으로 이동\" 또는 `action_archive`를 사용하세요."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"Operation not supported. Please use \"Restore\" / `action_unarchive` "
"instead."
msgstr "지원되지 않는 작업입니다. 복원' 또는 `action_unarchive`를 사용하세요."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"회신하지 않은 것까지 모든 받는 메시지를 첨부할 스레드(레코드)의 선택사항 ID. 설정하면 완벽하게 새로운 레코드의 생성을 억제합니다."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.js:0
msgid "Optional message..."
msgstr "선택적 메시지..."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.xml:0
msgid "Or send emails to"
msgstr "또는 다음으로 이메일을 전송합니다."

#. module: documents
#: model:documents.tag,name:documents.documents_tag_other
msgid "Other"
msgstr "기타"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: code:addons/documents/static/src/components/documents_permission_panel/documents_partner_access.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_document__owner_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__requestee_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Owner"
msgstr "소유자"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Owner: #{document.owner_id.name}"
msgstr "소유자: #{document.owner_id.name}"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Owner: #{document_url.owner_id.name}"
msgstr "Owner: #{document_url.owner_id.name}"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Owner: #{subfolder.owner_id.name}"
msgstr "Owner: #{subfolder.owner_id.name}"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_parent_model_id
msgid "Parent Model"
msgstr "상위 모델"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__parent_path
msgid "Parent Path"
msgstr "상위 경로"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "상위 레코드 스레드 ID"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"별칭이 있는 상위 모델입니다. 별칭 참조를 포함하는 모델은 반드시 alias_model_id(예: 프로젝트(parent_model) 및 "
"태스크(model)에서 제공하는 모델일 필요는 없습니다.)"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "Partial transfer"
msgstr "부분 이전"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_access__partner_id
msgid "Partner"
msgstr "협력사"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_partner_access.xml:0
msgid "People with access"
msgstr "액세스 권한이 있는 사람"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_pinned_folder
msgid "Pinned to Company roots"
msgstr "회사 루트에 고정"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.not_available
msgid ""
"Please login or contact the person that shared this link for more "
"information."
msgstr "자세한 내용은 로그인하거나 링크를 공유한 사람에게 문의하세요."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Mailgateway를 사용하여 문서에 메시지를 게시하는 정책.\n"
"- 모든 사용자: 모든 사용자가 게시할 수 있습니다\n"
"- 협력사: 인증된 협력사만 게시할 수 있습니다\n"
"- 팔로워: 관련 문서의 팔로워 또는 팔로잉 중인 채널의 사용자만 게시할 수 있습니다\n"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__thumbnail_status__present
msgid "Present"
msgstr "출근"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_presentations
msgid "Presentations"
msgstr "프리젠테이션"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_file
msgid "Preview files"
msgstr "파일 미리보기"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_project
msgid "Project"
msgstr "프로젝트"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Put in %s"
msgstr "%s 입력"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__rating_ids
msgid "Ratings"
msgstr "평가"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Recent"
msgstr "최근"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.js:0
msgid "Recently accessed Documents will show up here"
msgstr "최근에 액세스한 문서가 여기에 표시됩니다."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Recently accessed documents."
msgstr "최근에 액세스한 문서 수"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__resource_ref
msgid "Record"
msgstr "기록"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_force_thread_id
msgid "Record Thread ID"
msgstr "레코드 스레드 ID"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Remaining Pages"
msgstr "잔여 페이지"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/activity/documents_activity_controller.js:0
msgid "Reminder emails have been sent."
msgstr "이메일 알림이 전송되었습니다."

#. module: documents
#: model:mail.template,subject:documents.mail_template_document_request_reminder
msgid ""
"Reminder to upload your document{{ object.name and ' : ' + object.name or ''"
" }}"
msgstr "문서를 업로드하도록 알림 {{ object.name and ' : ' + object.name or '' }}"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_remove_partner_button.xml:0
msgid "Remove Access"
msgstr "액세스 제거"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_remove_partner_button.xml:0
msgid "Remove Member"
msgstr "구성원 삭제"

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_tag_remove_inbox
msgid "Remove Tag Inbox"
msgstr "받은 편지함 태그 제거"

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_tag_remove_to_validate
msgid "Remove Tag To Validate"
msgstr "승인할 태그 제거"

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_remove_tags
msgid "Remove all tags"
msgstr "모든 태그 제거"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_star.js:0
msgid "Remove star"
msgstr "별표 제거"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_rename.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_rename
msgid "Rename"
msgstr "이름 바꾸기"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Request"
msgstr "요청"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__request_activity_id
msgid "Request Activity"
msgstr "요청 활동"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Request To"
msgstr "요청 대상"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/web/activity/activity_menu_patch.xml:0
msgid "Request a Document"
msgstr "문서 요청"

#. module: documents
#: model:ir.actions.act_window,name:documents.action_request_form
msgid "Request a file"
msgstr "파일 요청"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_request_page
msgid ""
"Requested\n"
"                                ∙"
msgstr ""
"요청됨\n"
"                                ∙"

#. module: documents
#: model:mail.activity.type,name:documents.mail_documents_activity_data_md
msgid "Requested Document"
msgstr "요청한 문서"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Requested Documents"
msgstr "요청 서류"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__requestee_partner_id
msgid "Requestee Partner"
msgstr "요청자 파트너"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_model_name
msgid "Res Model Name"
msgstr "자원 모델 이름"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__res_id
msgid "Resource ID"
msgstr "리소스 ID"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_model
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__res_model
msgid "Resource Model"
msgstr "리소스 모델"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_name
msgid "Resource Name"
msgstr "리소스명"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_user_id
msgid "Responsible"
msgstr "담당자"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_user_id
msgid "Responsible User"
msgstr "담당 사용자"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Restore"
msgstr "복원"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Restricted"
msgstr "제한"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Restricted Folder"
msgstr "제한 폴더"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.xml:0
msgid "Revert changes"
msgstr "변경 사항 되돌리기"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_access__role
msgid "Role"
msgstr "역할"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS 전송 에러"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_sales
msgid "Sales"
msgstr "판매"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Save"
msgstr "저장"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.js:0
msgid "Save or discard changes first"
msgstr "먼저 변경 내용 저장 또는 삭제"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.xml:0
msgid "Saving..."
msgstr "저장 중..."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Search people"
msgstr "사람 검색"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.js:0
msgid "Select a folder to upload a document"
msgstr "문서를 업로드할 폴더를 선택하세요."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/kanban/documents_kanban_renderer.js:0
#: code:addons/documents/static/src/views/list/documents_list_renderer.js:0
msgid "Select all"
msgstr "전체 선택"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select focused page"
msgstr "포커스된 페이지 선택"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select next page"
msgstr "다음 페이지 선택"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select next pages of the group"
msgstr "그룹의 다음 페이지 선택"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select previous page"
msgstr "이전 페이지 선택"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select previous pages of the group"
msgstr "그룹의 이전 페이지 선택"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select/Deselect all pages"
msgstr "모든 페이지 선택/선택 해제"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Send"
msgstr "보내기"

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_send_to_finance
msgid "Send To Finance"
msgstr "재무팀으로 보내기"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Send this letter to the legal department, by assigning the right tags."
msgstr "알맞은 태그를 지정하여 이 메일을 법무팀으로 전송하세요."

#. module: documents
#: model:mail.template,description:documents.mail_template_document_request
msgid "Sent to partner when requesting a document from them"
msgstr "파트너에게 문서를 요청해야 할 경우 파트너에게 전송"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_tag__sequence
msgid "Sequence"
msgstr "순서"

#. module: documents
#: model:ir.model,name:documents.model_ir_actions_server
msgid "Server Action"
msgstr "서버 작업"

#. module: documents
#. odoo-python
#: code:addons/documents/models/ir_actions_server.py:0
msgid "Server Actions"
msgstr "서버 작업"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Set deletion delay for documents in the Trash"
msgstr "휴지통에 있는 문서에 대한 삭제 대기 설정"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_expiration_date_btn.xml:0
msgid "Set expiration date"
msgstr "만료일 설정"

#. module: documents
#: model:mail.template,description:documents.mail_template_document_request_reminder
msgid ""
"Set reminders in activities to notify users who didn't upload their "
"requested document"
msgstr "요청한 문서 업로드를 하지 않은 사용자에게 안내하기 위해 활동 알림을 설정"

#. module: documents
#: model:ir.actions.act_window,name:documents.configuration_action
#: model:ir.actions.act_window,name:documents.settings_action
#: model:ir.ui.menu,name:documents.settings_menu
msgid "Settings"
msgstr "설정"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_share.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
#: code:addons/documents/static/src/views/search/documents_search_panel.xml:0
#: code:addons/documents/static/src/web/error_dialog/error_dialog_patch.xml:0
msgid "Share"
msgstr "공유"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.js:0
msgid "Share: %(documentName)s"
msgstr "공유: %(documentName)s"

#. module: documents
#. odoo-javascript
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.js:0
#: code:addons/documents/static/src/views/kanban/documents_kanban_record.js:0
msgid "Shared with me"
msgstr "나와 공유"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Sharing..."
msgstr "공유 중..."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__shortcut_ids
msgid "Shortcut"
msgstr "바로가기"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "Shortcut created"
msgstr "바로 가기 생성"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.js:0
msgid "Shortcuts can only be created one at a time."
msgstr "바로가기는 한 번에 하나씩 만들어야 합니다."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Shortcuts cannot change target document."
msgstr "바로가기는 대상 문서를 변경할 수 없습니다."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_file
msgid "Size:"
msgstr "크기 :"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid "Some files could not be uploaded (max size: %s)."
msgstr "일부 파일을 업로드할 수 없습니다 (최대 크기: %s)"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__shortcut_document_id
msgid "Source Document"
msgstr "원본 문서"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "Special folders cannot be shared"
msgstr "특수 폴더는 공유할 수 없습니다"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Split"
msgstr "분할"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/attachments/document_file_viewer.xml:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Split PDF"
msgstr "PDF 분할"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Split all white pages"
msgstr "모든 화이트 페이지 분할"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Split selected pages"
msgstr "선택한 페이지 분할"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Starred"
msgstr "별표"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"활동 기준 상태\n"
"기한 초과: 기한이 이미 지났습니다.\n"
"오늘: 활동 날짜가 오늘입니다.\n"
"예정: 향후 계획된 활동입니다."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "Stay here"
msgstr "현재 페이지 유지"

#. module: documents
#: model:ir.ui.menu,name:documents.structure
msgid "Structure"
msgstr "구조"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_summary
msgid "Summary"
msgstr "요약"

#. module: documents
#: model:res.groups,name:documents.group_documents_system
msgid "System Administrator"
msgstr "시스템 관리자"

#. module: documents
#: model:ir.model,name:documents.model_documents_tag
#: model:ir.model.fields,field_description:documents.field_mail_activity_type__tag_ids
#: model_terms:ir.ui.view,arch_db:documents.tag_view_search
msgid "Tag"
msgstr "태그"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Tag Name"
msgstr "태그 이름"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_tag_tag_name_unique
msgid "Tag name already used"
msgstr "이미 사용된 태그 이름"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model:ir.actions.act_window,name:documents.tag_action
#: model:ir.model.fields,field_description:documents.field_documents_document__tag_ids
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__tag_ids
#: model:ir.ui.menu,name:documents.category_menu
msgid "Tags"
msgstr "태그"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_text
msgid "Text"
msgstr "문자"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_tag__tooltip
msgid "Text shown when hovering on this tag"
msgstr "이 태그에 커서를 올리면 표시되는 텍스트"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_res_config_settings_check_deletion_delay
msgid "The deletion delay should be positive."
msgstr "삭제 대기 항목은 양수여야 합니다."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/web/error_dialog/error_dialog_patch.js:0
#: code:addons/documents/static/src/web/error_dialog/error_dialog_patch.xml:0
msgid "The document URL has been copied to your clipboard."
msgstr "문서 URL이 클립보드에 복사되었습니다."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid "The document has been moved."
msgstr "문서가 이동되었습니다."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"The following documents can't have alias: \n"
"- %(records)s"
msgstr ""
"다음 문서에는 별칭을 사용할 수 없습니다: \n"
"- %(records)s"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"The following documents/folders can't be owned by a Portal User: \n"
"- %(partners)s"
msgstr ""
"포털 사용자는 다음 문서/폴더를 소유할 수 없습니다:\n"
"- %(partners)s"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "The following documents/shortcuts have a company mismatch: \n"
msgstr "다음 문서/바로가기가 회사와 일치하지 않습니다:\n"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "The following documents/shortcuts have a type mismatch: \n"
msgstr "다음 문서/바로 가기에 유형이 일치하지 않습니다.: \n"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "The following shortcuts cannot be set as documents parents: \n"
msgstr "다음 바로가기는 상위 문서로 설정할 수 없습니다:\n"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"이 별칭에 해당하는 모델(Odoo 문서 종류)입니다. 모든 받는 메일은 기존 레코드로 기록되지 않고 이 모델의 새 레코드로 작성됩니다. "
"(예. 프로젝트 작업)"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"<<EMAIL>>에 대한 이메일을 수신하도록 하려면 이메일의 별칭 이름을 적어주세요. 예: 'jobs'"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__last_access_date_group__1_month
msgid "This Month"
msgstr "이번 달"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__last_access_date_group__2_week
msgid "This Week"
msgstr "이번 주"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "This action does not exist."
msgstr "이 작업은 존재하지 않습니다."

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_document_attachment_unique
msgid "This attachment is already a document"
msgstr "이 첨부 파일은 이미 문서입니다"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.not_available
msgid "This document does not exist or is not publicly available."
msgstr "이 문서는 존재하지 않거나 공개적으로 사용할 수 없습니다."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_request_page
msgid ""
"This document has been requested.\n"
"                <b onclick=\"document.querySelector('.o_request_upload').click()\" style=\"cursor:pointer;\">Upload it</b>."
msgstr ""
"이 문서를 요청받았습니다.\n"
"                <b onclick=\"document.querySelector('.o_request_upload').click()\" style=\"cursor:pointer;\">업로드하세요</b>."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "This document has been restored."
msgstr "이 문서가 복원되었습니다."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"This file has been sent to the trash and will be deleted forever on the %s"
msgstr "해당 파일을 휴지통으로 이동하여 %s 항목에서 완전히 삭제합니다."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "This folder does not exist or is not accessible."
msgstr "이 폴더는 존재하지 않거나 액세스할 수 없습니다."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "This is a folder"
msgstr "이 폴더는"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.xml:0
msgid "This is a shortcut"
msgstr "바로 가기입니다"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "This is a shortcut - Click to access source document"
msgstr "바로가기입니다 - 원본 문서에 액세스하려면 여기를 클릭하세요."

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_access_unique_document_access_partner
msgid "This partner is already set on this document."
msgstr "이 파트너가 이미 이 문서와 연결되어 있습니다."

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.mail_activity_type_action_document
msgid ""
"Those represent the different categories of things you have to do (e.g. "
"\"Call\" or \"Send email\")."
msgstr "이는 수행해야 하는 작업에 대한 다양한 카테고리를 나타냅니다 (예: \"전화\" 또는 \"이메일 전송\")."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__thumbnail
msgid "Thumbnail"
msgstr "미리보기 이미지"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__thumbnail_status
msgid "Thumbnail Status"
msgstr "썸네일 상태"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.xml:0
msgid "Tip: configure your scanner to send all documents to this address."
msgstr "도움말: 스캐너 설정을 하면 이 주소로 모든 문서를 전송합니다."

#. module: documents
#: model:digest.tip,name:documents.digest_tip_documents_0
msgid "Tip: Become a paperless company"
msgstr "도움말: 종이없는 회사를 만들어 보세요."

#. module: documents
#: model:documents.tag,name:documents.documents_tag_to_validate
msgid "To Validate"
msgstr "승인하기"

#. module: documents
#: model:mail.activity.type,name:documents.mail_documents_activity_data_tv
msgid "To validate"
msgstr "승인하기"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__last_access_date_group__3_day
msgid "Today"
msgstr "오늘"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/kanban/documents_kanban_renderer.js:0
msgid "Toggle favorite"
msgstr "즐겨찾기 토글"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_tag__tooltip
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Tooltip"
msgstr "툴팁"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Trash"
msgstr "휴지통"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Trash Management"
msgstr "휴지통 관리"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__is_editable_attachment
msgid "True if we can edit the link attachment."
msgstr "True 값인 경우 첨부 링크를 편집할 수 있습니다."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__type
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Type"
msgstr "유형"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "레코드에 있는 예외 활동의 유형입니다."

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__url
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "URL"
msgstr "URL"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"URL %s does not seem complete, as it does not begin with http(s):// or "
"ftp://"
msgstr "URL %s이 http(s):// 또는 ftp:// 으로 시작하지 않습니다."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__url_preview_image
msgid "URL Preview Image"
msgstr "URL 미리보기 이미지"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Unavailable action."
msgstr "사용할 수 없는 작업"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Unlock"
msgstr "잠금 해제"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "Unnamed"
msgstr "이름 없음"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Upload"
msgstr "업로드"

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.document_action
#: model_terms:ir.actions.act_window,help:documents.document_action_portal
msgid ""
"Upload <span class=\"fw-normal\">a file or</span> drag <span class=\"fw-"
"normal\">it here.</span>"
msgstr ""
" 파일을 업로드 <span class=\"fw-normal\">하시거나 여기로</span> 드래그 <span class=\"fw-"
"normal\">하세요.</span>"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.xml:0
msgid "Upload New Version"
msgstr "새 버전 업로드"

#. module: documents
#. odoo-python
#: code:addons/documents/models/mail_activity.py:0
msgid "Upload file request"
msgstr "요청받은 파일 업로드"

#. module: documents
#: model:ir.model,name:documents.model_res_users
#: model:res.groups,name:documents.group_documents_user
msgid "User"
msgstr "사용자"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
#: code:addons/documents/static/src/components/documents_permission_panel/documents_partner_access.xml:0
msgid "User Access Role"
msgstr "사용자 액세스 권한"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__user_permission
msgid "User permission"
msgstr "사용자 승인"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_vat
msgid "VAT"
msgstr "부가가치세"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_validated
msgid "Validated"
msgstr "승인됨"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.xml:0
msgid ""
"Versions are displayed in the order they were uploaded to Documents.\n"
"                    If you delete the most recent version, the previous one is automatically restored as the current one."
msgstr ""
"버전은 문서에 업로드된 순서대로 표시됩니다.\n"
"                    최신 버전이 삭제되면 이전 버전이 자동으로 현재 버전으로 복원됩니다."

#. module: documents
#: model:documents.tag,name:documents.documents_tag_videos
msgid "Videos"
msgstr "동영상"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: model:ir.model.fields.selection,name:documents.selection__documents_access__role__view
#: model:ir.model.fields.selection,name:documents.selection__documents_document__access_internal__view
#: model:ir.model.fields.selection,name:documents.selection__documents_document__access_via_link__view
#: model:ir.model.fields.selection,name:documents.selection__documents_document__user_permission__view
msgid "Viewer"
msgstr "뷰어"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid ""
"Want to become a <b>paperless company</b>? Let's discover Odoo Documents."
msgstr "<b>종이 없는 회사</b>를 만들고자 하시나요? Odoo 문서를 확인해보세요."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__website_message_ids
msgid "Website Messages"
msgstr "웹사이트 메시지"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__website_message_ids
msgid "Website communication history"
msgstr "웹사이트 대화 이력"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__create_activity_date_deadline_range_type__weeks
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__weeks
msgid "Weeks"
msgstr "주"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "What do you want to do with the remaining pages ?"
msgstr "남은 페이지로 무엇을 하시겠습니까?"

#. module: documents
#: model_terms:web_tour.tour,rainbow_man_message:documents.documents_tour
msgid ""
"Wow... 6 documents processed in a few seconds, You're good.<br>The tour is "
"complete. Try uploading your own documents now."
msgstr ""
"대단합니다. 몇 초 만에 6건의 문서를 처리했습니다. 잘하셨습니다.<br>투어를 마치셨습니다. 이제 내 문서를 업로드해보세요. "

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_partner_access.xml:0
msgid "You"
msgstr "귀하"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to (un)archive documents."
msgstr "문서를 보관(해제)할 수 없습니다."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to change ownerships of documents you do not own."
msgstr "소유하지 않은 문서의 소유권을 변경할 수 없습니다."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to delete all these items."
msgstr "이 모든 항목을 삭제할 수 없습니다."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to execute embedded actions."
msgstr "임베드된 동작을 실행하는 것은 허용되지 않습니다."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to move (some of) these documents."
msgstr "이러한 문서 중 일부에 대해 이동할 수 있는 권한이 없습니다."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to pin/unpin embedded Actions."
msgstr "임베드된 액션은 고정/고정 해제할 수 없습니다."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to read the permission panel data."
msgstr "권한 패널 데이터를 읽을 수 없습니다."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to write in this folder."
msgstr "이 폴더에 쓰기가 허용되지 않습니다."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__attachment_type
msgid ""
"You can either upload a file from your computer or copy/paste an internet "
"link to your file."
msgstr "컴퓨터에서 파일을 업로드하거나 인터넷 링크를 복사하여 파일에 붙여넣기 할 수 있습니다."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_pin_actions.js:0
msgid "You can not pin actions for that folder."
msgstr "해당 폴더에 대한 고정 작업은 허용되지 않습니다."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"You can not update the access of a shortcut, update its target instead."
msgstr "바로가기에 대한 액세스는 직접 업데이트할 수 없습니다. 대신 대상을 수정하세요."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You can't access that folder_id."
msgstr "해당 folder_id에 액세스할 수 없습니다."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid ""
"You can't create shortcuts in or move documents to this special folder."
msgstr "이 특수 폴더에는 바로 가기를 만들거나 이동할 수 없습니다."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You can't move documents out of folders you cannot edit."
msgstr "편집할 수 없는 폴더에서는 문서를 이동할 수 없습니다."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You cannot change the owner of documents you do not own."
msgstr "소유하지 않은 문서의 소유권은 이전할 수 없습니다."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_tag.py:0
msgid "You cannot delete tags used in server actions."
msgstr "서버 동작에 사용된 태그는 삭제할 수 없습니다."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You cannot delete this attachment."
msgstr "이 첨부 파일을 삭제할 수 없습니다."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You cannot duplicate document(s) in the Trash."
msgstr "휴지통에는 문서를 복제할 수 없습니다."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "You cannot move folders or files when in the trash."
msgstr "휴지통에 있는 폴더나 파일은 이동할 수 없습니다."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "You cannot move request in the company folder"
msgstr "회사 폴더에서는 요청을 이동할 수 없습니다."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You cannot pin an action on that document."
msgstr "해당 문서에는 작업을 고정할 수 없습니다."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You cannot pin that type of action."
msgstr "해당 유형의 작업은 고정할 수 없습니다."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_remove_partner_button.xml:0
msgid "You cannot remove this partner"
msgstr "이 파트너를 제거할 수 없습니다."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "You cannot share multiple documents at the same time"
msgstr "한 번에 여러 문서를 공유할 수 없습니다."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You do not have sufficient access rights to delete these documents."
msgstr "이 문서를 삭제할 수 있는 충분한 권한이 없습니다."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid ""
"You don't have the rights to move documents nor create shortcut to that "
"folder."
msgstr "이 폴더에 대한 문서 이동 또는 바로 가기 생성 권한이 없습니다."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_drop_zone.xml:0
msgid "You must be in a specific writable workspace to upload files"
msgstr "파일을 업로드하려면 쓰기 가능한 특정 작업 공간에 있어야 합니다."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Your Role: %s"
msgstr "귀하의 권한: %s"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Your individual space."
msgstr "나만의 공간."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.js:0
msgid "Your personal space"
msgstr "개인 공간"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_details
msgid "alias"
msgstr "별칭"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "bytes"
msgstr "바이트"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_deletion_form
msgid "days."
msgstr "일."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.js:0
msgid "document"
msgstr "문서"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "documents"
msgstr "문서"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "e.g. Discuss proposal"
msgstr "예: 업무 협의 제안"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_new_folder
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_rename
msgid "e.g. Finance"
msgstr "예. 재무"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "e.g. Missing Expense"
msgstr "예: 경비 누락"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "e.g. To Validate"
msgstr "예: 승인하기"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "e.g. https://www.youtube.com/watch?v=CP96yVnXNrY"
msgstr "예: https://www.youtube.com/watch?v=CP96yVnXNrY"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_details
msgid "e.g. mycompany.com"
msgstr "예: mycompany.com"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "files"
msgstr "파일"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.js:0
msgid "folder"
msgstr "폴더"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "folders,"
msgstr "폴더,"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__is_readonly_model
msgid "is_readonly_model"
msgstr "is_readonly_model"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/chatter/documents_chatter_overlay.xml:0
msgid "items selected"
msgstr "선택된 항목"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "items,"
msgstr "항목,"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "kiB"
msgstr "kiB"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_page/pdf_page.xml:0
msgid "select"
msgstr "선택"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.mail_template_document_share
msgid "shared a document with you.<br/>"
msgstr "문서를 공유했습니다.<br/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.mail_template_document_share
msgid "shared a folder with you.<br/>"
msgstr "폴더를 공유했습니다.<br/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "shared by"
msgstr "다음에 의해 공유됨"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.mail_template_document_share
msgid "shared this document with you:"
msgstr "이 문서를 공유했습니다:"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.mail_template_document_share
msgid "shared this folder with you:"
msgstr "이 폴더를 공유했습니다:"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "status code: %(status)s, message: %(message)s"
msgstr "상태 코드: %(status)s, 메시지: %(message)s"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "sub-doc-%s"
msgstr "sub-doc-%s"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "unnamed"
msgstr "이름 없음"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid ""
"•\n"
"                                        <b>URL</b>"
msgstr ""
"•\n"
"                                        <b>URL</b>"
