# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents
# 
# Translators:
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON> <<EMAIL>>, 2024
# Wil <PERSON><PERSON><PERSON>, 2025
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-26 20:44+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid " (%s locked)"
msgstr "(%s已锁定 )"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "%s (copy)"
msgstr "%s（副本）"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "%s (remaining pages)"
msgstr "%s（剩余页数）"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "%s Files"
msgstr "%s 文件"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "%s Folders"
msgstr "%s文件夹"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/list/document_count_field.js:0
msgid "%s documents"
msgstr "%s文件"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid "%s documents have been moved."
msgstr "已移动 %s 个文件。"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "%s file(s) not moved because they are locked by another user"
msgstr "%s文件未被移动，因为它们被另一个用户锁定"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.js:0
msgid "%s has been copied in My Drive."
msgstr "%s已复制到我的云端硬盘中。"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.js:0
msgid "%s have been copied in My Drive."
msgstr "%s已复制到我的云端硬盘中。"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "%s new document(s) created"
msgstr "%s创建新文件"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "%s page(s) deleted"
msgstr "%s页已删除"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "%s shared with you"
msgstr "%s与您分享"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid "%s shortcuts have been created."
msgstr "已建立 %s 个快捷方式。"

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid ""
". Scanned files will appear automatically in your workspace. Then, process "
"your documents in bulk with the split tool: launch user defined actions, "
"request a signature, convert to vendor bills with AI, etc."
msgstr ""
"处理收到的邮件的一个简单方法是配置扫描仪以将PDF发送到您的工作区电子邮件。扫描的文档将自动出现在您的工作区中。然后，使用split工具批量处理文档：启动用户定义的动作、请求签名、使用AI转换为供应商账单等。"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.mail_template_document_share
msgid ".<br/>"
msgstr ".<br/>"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "1 File"
msgstr "1 文件"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "1 Folder"
msgstr "1 文件夹"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/list/document_count_field.js:0
msgid "1 document"
msgstr "1 文件"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_year_previous
msgid "2024"
msgstr "2024年"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_year_current
msgid "2025"
msgstr "2025"

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid "<b class=\"tip_title\">Tip: Become a paperless company</b>"
msgstr "<b class=\"tip_title\">提示：成为一个无纸化公司</b>"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "<b>Deselect this page</b> as we plan to process all bills first."
msgstr "<b>取消选择此页面</b>，因为我们计划先处理所有账单。"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "<b>Select</b> this page to continue."
msgstr "<b>选择</b>此页继续"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "<br/>Powered by"
msgstr "<br/> 技术提供"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "<i class=\"fa fa-download fa-fw\"/> Download All"
msgstr "<i class=\"fa fa-download fa-fw\"/> 全部下载"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_file
msgid "<i class=\"fa fa-download fa-fw\"/> Download file"
msgstr "<i class=\"fa fa-download fa-fw\"/> 下载文件"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<i class=\"fa fa-external-link me-2\" title=\"This is a shortcut\"/>"
msgstr "<i class=\"fa fa-external-link me-2\" title=\"这是一个快捷方式\"/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<i class=\"fa fa-external-link text-gray-500\" title=\"This is a shortcut\"/>"
msgstr "<i class=\"fa fa-external-link text-gray-500\" title=\"这是一个快捷方式\"/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_file
msgid "<i class=\"fa fa-eye\"/> Preview file"
msgstr "<i class=\"fa fa-eye\"/> 预览文件"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "<i class=\"fa fa-lock oe_inline\" title=\"Locked\" invisible=\"not lock_uid\"/>"
msgstr "<i class=\"fa fa-lock oe_inline\" title=\"Locked\" invisible=\"not lock_uid\"/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "<i class=\"fa fa-upload\"/>  Upload"
msgstr "<i class=\"fa fa-upload\"/>  上传"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_file
msgid "<i class=\"fa fa-upload\"/> Replace file"
msgstr "<i class=\"fa fa-upload\"/> 替换文件"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_document_res_partner_view
msgid "<span class=\"o_stat_text\">Documents</span>"
msgstr "<span class=\"o_stat_text\">文档</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "<span class=\"o_stat_text\">Related <br/> Record</span>"
msgstr "<span class=\"o_stat_text\">相关的<br/>记录</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "<span title=\"Requested Document\">Requested Document</span>"
msgstr "<span title=\"Requested Document\">请求的文档</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "<span>&amp;nbsp;Documents.</span>"
msgstr "<span>&amp;nbsp;文档。</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<span><b> Request</b></span>"
msgstr "<span><b>请求</b></span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<span><b>Requested Document</b></span>"
msgstr "<span><b>请求的文件</b></span>"

#. module: documents
#: model:mail.template,body_html:documents.mail_template_document_request
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"                    <tbody>\n"
"                        <!-- HEADER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\">\n"
"                                        <span style=\"font-size: 10px;\">\n"
"                                            Document Request: <br/>\n"
"                                            <t t-if=\"object.name\">\n"
"                                                <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Inbox Financial</span>\n"
"                                            </t>\n"
"                                        </span><br/>\n"
"                                    </td><td valign=\"middle\" align=\"right\" t-if=\"not object.create_uid.company_id.uses_default_logo\">\n"
"                                        <img t-attf-src=\"/logo.png?company={{ object.create_uid.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.create_uid.company_id.name\"/>\n"
"                                    </td></tr>\n"
"                                    <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- CONTENT -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                        <div>\n"
"                                            Hello <t t-out=\"object.owner_id.name or ''\">OdooBot</t>,\n"
"                                            <br/><br/>\n"
"                                            <t t-out=\"object.create_uid.name or ''\">OdooBot</t> (<t t-out=\"object.create_uid.email or ''\"><EMAIL></t>) asks you to provide the following document:\n"
"                                            <br/><br/>\n"
"                                            <center>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.name\">\n"
"                                                        <b t-out=\"object.name or ''\">Inbox Financial</b>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.request_activity_id.note\">\n"
"                                                        <i t-out=\"object.request_activity_id.note or ''\">Example of a note.</i>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <br/>\n"
"                                                <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                    <a t-att-href=\"object.access_url\" style=\"background-color: #875A7B; padding: 20px 30px 20px 30px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                        Upload the requested document\n"
"                                                    </a>\n"
"                                                </div>\n"
"                                            </center><br/>\n"
"                                            Please provide us with the missing document before <t t-out=\"object.request_activity_id.date_deadline\">2021-05-17</t>.\n"
"                                            <t t-if=\"user and user.signature\" data-o-mail-quote-container=\"1\">\n"
"                                                <br/>\n"
"                                                <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                                                <br data-o-mail-quote=\"1\"/>\n"
"                                            </t>\n"
"                                        </div>\n"
"                                    </td></tr>\n"
"                                    <tr><td style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- FOOTER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\" align=\"left\">\n"
"                                        <t t-out=\"object.create_uid.company_id.name or ''\">YourCompany</t>\n"
"                                    </td></tr>\n"
"                                    <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                                        <t t-out=\"object.create_uid.company_id.phone or ''\">******-123-4567</t>\n"
"                                        <t t-if=\"object.create_uid.company_id.email\">\n"
"                                            | <a t-attf-href=\"'mailto:%s' % {{ object.create_uid.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.email or ''\"><EMAIL></a>\n"
"                                        </t>\n"
"                                        <t t-if=\"object.create_uid.company_id.website\">\n"
"                                            | <a t-attf-href=\"'%s' % {{ object.create_uid.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.website or ''\">http://www.example.com</a>\n"
"                                        </t>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                    </table>\n"
"                    </td></tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                          <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"                            Powered by <a target=\"_blank\" href=\"https://www.odoo.com/app/documents\" style=\"color: #875A7B;\">Odoo Documents</a>\n"
"                          </td></tr>\n"
"                        </table>\n"
"                    </td></tr>\n"
"                </table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"                    <tbody>\n"
"                        <!-- HEADER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\">\n"
"                                        <span style=\"font-size: 10px;\">\n"
"                                            文件请求：<br/>\n"
"                                            <t t-if=\"object.name\">\n"
"                                                <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">收件箱财务</span>\n"
"                                            </t>\n"
"                                        </span><br/>\n"
"                                    </td><td valign=\"middle\" align=\"right\" t-if=\"not object.create_uid.company_id.uses_default_logo\">\n"
"                                        <img t-attf-src=\"/logo.png?company={{ object.create_uid.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.create_uid.company_id.name\"/>\n"
"                                    </td></tr>\n"
"                                    <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- CONTENT -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                        <div>\n"
"                                            <t t-out=\"object.owner_id.name or ''\">OdooBot</t>您好！\n"
"                                            <br/><br/>\n"
"                                            <t t-out=\"object.create_uid.name or ''\">OdooBot</t> (<t t-out=\"object.create_uid.email or ''\"><EMAIL></t>) 要求您提供以下文件：\n"
"                                            <br/><br/>\n"
"                                            <center>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.name\">\n"
"                                                        <b t-out=\"object.name or ''\">收件箱财务</b>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.request_activity_id.note\">\n"
"                                                        <i t-out=\"object.request_activity_id.note or ''\">备注示例文字。</i>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <br/>\n"
"                                                <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                    <a t-att-href=\"object.access_url\" style=\"background-color: #875A7B; padding: 20px 30px 20px 30px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                        上传要求的文件\n"
"                                                    </a>\n"
"                                                </div>\n"
"                                            </center><br/>\n"
"                                            请在 <t t-out=\"object.request_activity_id.date_deadline\">2021-05-17</t>之前，向我们提供缺漏的文件。\n"
"                                            <t t-if=\"user and user.signature\" data-o-mail-quote-container=\"1\">\n"
"                                                <br/>\n"
"                                                <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                                                <br data-o-mail-quote=\"1\"/>\n"
"                                            </t>\n"
"                                        </div>\n"
"                                    </td></tr>\n"
"                                    <tr><td style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- FOOTER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\" align=\"left\">\n"
"                                        <t t-out=\"object.create_uid.company_id.name or ''\">YourCompany</t>\n"
"                                    </td></tr>\n"
"                                    <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                                        <t t-out=\"object.create_uid.company_id.phone or ''\">******-123-4567</t>\n"
"                                        <t t-if=\"object.create_uid.company_id.email\">\n"
"                                            | <a t-attf-href=\"'mailto:%s' % {{ object.create_uid.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.email or ''\"><EMAIL></a>\n"
"                                        </t>\n"
"                                        <t t-if=\"object.create_uid.company_id.website\">\n"
"                                            | <a t-attf-href=\"'%s' % {{ object.create_uid.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.website or ''\">http://www.example.com</a>\n"
"                                        </t>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                    </table>\n"
"                    </td></tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                          <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"                            由 <a target=\"_blank\" href=\"https://www.odoo.com/app/documents\" style=\"color: #875A7B;\">Odoo 文件管理</a>驱动\n"
"                          </td></tr>\n"
"                        </table>\n"
"                    </td></tr>\n"
"                </table>\n"
"            "

#. module: documents
#: model:mail.template,body_html:documents.mail_template_document_request_reminder
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"                    <tbody>\n"
"                        <!-- HEADER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\">\n"
"                                        <span style=\"font-size: 10px;\">\n"
"                                            Document Request: <br/>\n"
"                                            <t t-if=\"object.name\">\n"
"                                                <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Inbox Financial</span>\n"
"                                            </t>\n"
"                                        </span><br/>\n"
"                                    </td><td valign=\"middle\" align=\"right\">\n"
"                                        <img t-attf-src=\"/logo.png?company={{ object.create_uid.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.create_uid.company_id.name\"/>\n"
"                                    </td></tr>\n"
"                                    <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- CONTENT -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                        <div>\n"
"                                            Hello <t t-out=\"object.owner_id.name or ''\">OdooBot</t>,\n"
"                                            <br/><br/>\n"
"                                            This is a friendly reminder to upload your requested document:\n"
"                                            <br/><br/>\n"
"                                            <center>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.name\">\n"
"                                                        <b t-out=\"object.name or ''\">Inbox Financial</b>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.request_activity_id.note\">\n"
"                                                        <i t-out=\"object.request_activity_id.note or ''\">Example of a note.</i>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <br/>\n"
"                                                <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                    <a t-att-href=\"object.access_url\" style=\"background-color: #875A7B; padding: 20px 30px 20px 30px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                        Upload the requested document\n"
"                                                    </a>\n"
"                                                </div>\n"
"                                            </center><br/>\n"
"                                            Please provide us with the missing document before <t t-out=\"object.request_activity_id.date_deadline or ''\">2021-05-17</t>.\n"
"                                            <br/><br/>\n"
"                                            Thank you,\n"
"                                            <t t-if=\"user and user.signature\" data-o-mail-quote-container=\"1\">\n"
"                                                <br/>\n"
"                                                <t t-out=\"user.signature\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                                                <br data-o-mail-quote=\"1\"/>\n"
"                                            </t>\n"
"                                        </div>\n"
"                                    </td></tr>\n"
"                                    <tr><td style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- FOOTER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\" align=\"left\">\n"
"                                        <t t-out=\"object.create_uid.company_id.name or ''\">YourCompany</t>\n"
"                                    </td></tr>\n"
"                                    <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                                        <t t-out=\"object.create_uid.company_id.phone or ''\">******-123-4567</t>\n"
"                                        <t t-if=\"object.create_uid.company_id.email\">\n"
"                                            | <a t-attf-href=\"'mailto:%s' % {{ object.create_uid.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.email\"><EMAIL></a>\n"
"                                        </t>\n"
"                                        <t t-if=\"object.create_uid.company_id.website\">\n"
"                                            | <a t-att-href=\"object.create_uid.company_id.website\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.website\">http://www.example.com</a>\n"
"                                        </t>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                    </table>\n"
"                    </td></tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                          <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"                            Powered by <a target=\"_blank\" href=\"https://www.odoo.com/app/documents\" style=\"color: #875A7B;\">Odoo Documents</a>\n"
"                          </td></tr>\n"
"                        </table>\n"
"                    </td></tr>\n"
"                </table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"                    <tbody>\n"
"                        <!-- HEADER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\">\n"
"                                        <span style=\"font-size: 10px;\">\n"
"                                            文件请求：<br/>\n"
"                                            <t t-if=\"object.name\">\n"
"                                                <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">收件箱财务</span>\n"
"                                            </t>\n"
"                                        </span><br/>\n"
"                                    </td><td valign=\"middle\" align=\"right\">\n"
"                                        <img t-attf-src=\"/logo.png?company={{ object.create_uid.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.create_uid.company_id.name\"/>\n"
"                                    </td></tr>\n"
"                                    <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- CONTENT -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                        <div>\n"
"                                            <t t-out=\"object.owner_id.name or ''\">OdooBot</t>您好！\n"
"                                            <br/><br/>\n"
"                                            温馨提醒，请上传您申请的文件：\n"
"                                            <br/><br/>\n"
"                                            <center>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.name\">\n"
"                                                        <b t-out=\"object.name or ''\">收件箱财务</b>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.request_activity_id.note\">\n"
"                                                        <i t-out=\"object.request_activity_id.note or ''\">说明示例。</i>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <br/>\n"
"                                                <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                    <a t-att-href=\"object.access_url\" style=\"background-color: #875A7B; padding: 20px 30px 20px 30px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                        上传要求的文件\n"
"                                                    </a>\n"
"                                                </div>\n"
"                                            </center><br/>\n"
"                                            请在 <t t-out=\"object.request_activity_id.date_deadline or ''\">2021-05-17</t>之前，向我们提供缺漏的文件。\n"
"                                            <br/><br/>\n"
"                                            谢谢。\n"
"                                            <t t-if=\"user and user.signature\" data-o-mail-quote-container=\"1\">\n"
"                                                <br/>\n"
"                                                <t t-out=\"user.signature\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                                                <br data-o-mail-quote=\"1\"/>\n"
"                                            </t>\n"
"                                        </div>\n"
"                                    </td></tr>\n"
"                                    <tr><td style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- FOOTER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\" align=\"left\">\n"
"                                        <t t-out=\"object.create_uid.company_id.name or ''\">YourCompany</t>\n"
"                                    </td></tr>\n"
"                                    <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                                        <t t-out=\"object.create_uid.company_id.phone or ''\">******-123-4567</t>\n"
"                                        <t t-if=\"object.create_uid.company_id.email\">\n"
"                                            | <a t-attf-href=\"'mailto:%s' % {{ object.create_uid.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.email\"><EMAIL></a>\n"
"                                        </t>\n"
"                                        <t t-if=\"object.create_uid.company_id.website\">\n"
"                                            | <a t-att-href=\"object.create_uid.company_id.website\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.website\">http://www.example.com</a>\n"
"                                        </t>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                    </table>\n"
"                    </td></tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                          <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"                            由 <a target=\"_blank\" href=\"https://www.odoo.com/app/documents\" style=\"color: #875A7B;\">Odoo 文件管理</a>驱动\n"
"                          </td></tr>\n"
"                        </table>\n"
"                    </td></tr>\n"
"                </table>\n"
"            "

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr "为该别名创建新记录时，将对其进行评估以提供默认值的Python字典。"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "A destination is required when creating multiple shortcuts at once."
msgstr "同时创建多个快捷方式时需要目的地。"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_document_folder_id_not_id
msgid "A folder cannot be included in itself"
msgstr "文件夹本身不能包括在内"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_document_shortcut_document_id_not_id
msgid "A shortcut cannot point to itself"
msgstr "快捷方式不能指向自身"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid "A shortcut has been created."
msgstr "已创建快捷方式。"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Access"
msgstr "访问"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "Access Error"
msgstr "访问错误"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__access_token
#: model:ir.model.fields,field_description:documents.field_documents_redirect__access_token
msgid "Access Token"
msgstr "访问令牌"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.portal_my_home_documents
msgid "Access all your documents"
msgstr "访问您的所有文件"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_access.py:0
msgid "Access documents and partners cannot be changed."
msgstr "访问文件和业务伙伴不可更改。"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Access to Automations"
msgstr "访问自动化"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid "Access to Server Actions"
msgstr "访问服务器操作"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Access to a folder or a document"
msgstr "访问文件夹或文件"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_document_document_token_unique
msgid "Access tokens already used."
msgstr "访问令牌已使用"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__access_url
msgid "Access url"
msgstr "访问URL"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Action"
msgstr "操作"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_needaction
msgid "Action Needed"
msgstr "所需操作"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_pin_actions.xml:0
msgid "Actions on Select"
msgstr "选择时的操作"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__active
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_new_folder
msgid "Active"
msgstr "有效"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_ids
#: model:ir.ui.menu,name:documents.mail_activities
msgid "Activities"
msgstr "活动"

#. module: documents
#: model:ir.model,name:documents.model_mail_activity
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Activity"
msgstr "活动"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "活动异常标示"

#. module: documents
#: model:ir.ui.menu,name:documents.mail_activity_plan_menu
msgid "Activity Plans"
msgstr "活动计划"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_state
msgid "Activity State"
msgstr "活动状态"

#. module: documents
#: model:ir.model,name:documents.model_mail_activity_type
msgid "Activity Type"
msgstr "活动类型"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_type_icon
msgid "Activity Type Icon"
msgstr "活动类型图标"

#. module: documents
#: model:ir.actions.act_window,name:documents.mail_activity_type_action_document
#: model:ir.ui.menu,name:documents.mail_activity_type_menu
msgid "Activity Types"
msgstr "活动类型"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "Activity assigned to"
msgstr "活动已分配给"

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.mail_activity_plan_action_document
msgid ""
"Activity plans are used to assign a list of activities in just a few clicks\n"
"                    (e.g. \"NDA signature process\", \"Workspace workflow\", ...)"
msgstr ""
"只需点击几下，活动计划就可用于分配活动列表\n"
"                    （例如：“NDA 签名流程”、“工作区工作流程”等）"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_type_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_type_id
msgid "Activity type"
msgstr "活动类型"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "Add"
msgstr "添加"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_pin_actions.xml:0
msgid "Add Custom Action"
msgstr "添加自定义操作"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Add File"
msgstr "添加文件"

#. module: documents
#: model:ir.actions.act_window,name:documents.action_folder_form
msgid "Add Folder"
msgstr "添加文件夹"

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_tag_add_bill
msgid "Add Tag Bill"
msgstr "添加账单标签"

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_tag_add_validated
msgid "Add Tag Validated"
msgstr "添加已验证标签"

#. module: documents
#: model:ir.actions.act_window,name:documents.action_url_form
msgid "Add Url"
msgstr "添加 URL"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "Add a tag..."
msgstr "加入标签⋯"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "Add an alias tag..."
msgstr "添加别名标签⋯"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Add new file"
msgstr "添加新文件"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Add people or email addresses..."
msgstr "添加人员或电子邮件地址…"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Add people or email addresses:"
msgstr "添加人员或电子邮件地址："

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_shortcut.js:0
msgid "Add shortcut"
msgstr "添加快捷方式"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_star.js:0
msgid "Add star"
msgstr "加入星号"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Additional documents you have access to."
msgstr "您有权访问的其他文件。"

#. module: documents
#: model:res.groups,name:documents.group_documents_manager
msgid "Administrator"
msgstr "管理员"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_ads
msgid "Ads"
msgstr "广告"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_id
msgid "Alias"
msgstr "别名"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_contact
msgid "Alias Contact Security"
msgstr "别名授权于"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_domain_id
msgid "Alias Domain"
msgstr "别名域"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_domain
msgid "Alias Domain Name"
msgstr "别名域名"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_full_name
msgid "Alias Email"
msgstr "别名电子邮件"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_name
msgid "Alias Name"
msgstr "别名"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_status
msgid "Alias Status"
msgstr "别名状态"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_tag_ids
msgid "Alias Tags"
msgstr "别名标记"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_status
msgid "Alias status assessed on the last message received."
msgstr "根据收到的最后一条消息评估别名状态。"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_model_id
msgid "Aliased Model"
msgstr "模型别名"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.js:0
msgid ""
"All users with access to this %(documentType)s or its parent will have edit "
"permissions."
msgstr "所有有权存取此 %(documentType)s 类型或其母级文件的使用者，都将会拥有编辑权限。"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__access_ids
msgid "Allowed Access"
msgstr "允许访问"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Already linked Documents: %s"
msgstr "已经链接的文档：%s"

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid ""
"An easy way to process incoming mails is to configure your scanner to send "
"PDFs to"
msgstr "处理传入邮件的一种简单方法是将扫描仪配置为将 PDF 发送到"

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid ""
"An easy way to process incoming mails is to configure your scanner to send "
"PDFs to your workspace email. Scanned files will appear automatically in "
"your workspace. Then, process your documents in bulk with the split tool: "
"launch user defined actions, request a signature, convert to vendor bills "
"with AI, etc."
msgstr ""
"处理收到的邮件的一个简单方法是配置扫描仪以将PDF发送到您的工作区电子邮件。扫描的文档将自动出现在您的工作区中。然后，使用split工具批量处理文档：启动用户定义的动作、请求签名、使用AI转换为供应商账单等。"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "An error occured while uploading."
msgstr "上传时发生错误。"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.js:0
msgid "Anyone on the internet with the link can edit"
msgstr "互联网上任何拥有链接的人，都可以编辑"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.js:0
msgid "Anyone on the internet with the link can view"
msgstr "互联网上的任何有该链接的人，都可以查看"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.xml:0
msgid "Anyone with the link"
msgstr "任何有链接的人"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Archive original file(s)"
msgstr "归档原始文件"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Are you sure that you want to delete the focused page ?"
msgstr "您确定要删除当前页吗？"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Are you sure that you want to delete the selected page(s)"
msgstr "您确定要删除所选页面吗？"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Are you sure that you want to delete this page ?"
msgstr "您确定要删除此页面吗？"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.js:0
msgid "Are you sure you want to delete this attachment?"
msgstr "您确定要删除此附件吗？"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Are you sure you want to permanently erase the document?"
msgstr "您确定要永久删除文档吗？"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Are you sure you want to permanently erase the documents?"
msgstr "您确定要永久删除文件吗？"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid ""
"As this PDF contains multiple documents, let's split and process in bulk."
msgstr "由于此PDF包含多个文档，所以让我们拆分并批量处理。"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid ""
"At least one document couldn't be moved due to access rights. Shortcuts have"
" been created."
msgstr "由于访问权限问题，至少有一个文档无法移动。已创建快捷方式。"

#. module: documents
#: model:ir.model,name:documents.model_ir_attachment
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_id
msgid "Attachment"
msgstr "附件"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_attachment_count
msgid "Attachment Count"
msgstr "附件数量"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__description
msgid "Attachment Description"
msgstr "附件说明"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_name
msgid "Attachment Name"
msgstr "附件名称"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_type
msgid "Attachment Type"
msgstr "附件类型"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_automations.js:0
msgid "Automations"
msgstr "自动化"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__available_embedded_actions_ids
msgid "Available Embedded Actions"
msgstr "可用的嵌入操作"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_bill
msgid "Bill"
msgstr "账单"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Blank Page"
msgstr "空白页"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_brochures
msgid "Brochures"
msgstr "宣传册"

#. module: documents
#: model:ir.model.fields,help:documents.field_mail_activity_type__folder_id
msgid "By defining a folder, the upload activities will generate a document"
msgstr "通过定义文档夹，上载活动将生成一个文档"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_link_to_record_form_view
msgid "Cancel"
msgstr "取消"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__checksum
msgid "Checksum/SHA1"
msgstr "校验和/SHA1"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__children_ids
msgid "Children"
msgstr "子级"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Choose a record to link"
msgstr "选择一个要链接的记录"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_details
msgid "Choose or Configure Email Servers"
msgstr "选择或配置电子邮件服务器"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Click on a card to <b>select the document</b>."
msgstr "单击卡片<b>选择文档</b>。"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Click on a thumbnail to <b>preview the document</b>."
msgstr "单击缩略图<b>预览文档</b>。"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid ""
"Click on the <b>page separator</b>: we don't want to split these two pages "
"as they belong to the same document."
msgstr "单击<b>页面分隔符</b>：我们不想拆分这两个页面，因为它们属于同一个文档。"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Click the cross to <b>exit preview</b>."
msgstr "点击交叉图示，以<b>离开预览</b>。"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__thumbnail_status__client_generated
msgid "Client Generated"
msgstr "已生成客户"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.xml:0
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Close"
msgstr "关闭"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Close split tools"
msgstr "关闭拆分工具"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_tag__color
msgid "Color"
msgstr "颜色"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Common roots for all company users."
msgstr "所有公司使用者的共同根位置。"

#. module: documents
#: model:ir.model,name:documents.model_res_company
msgid "Companies"
msgstr "公司"

#. module: documents
#. odoo-javascript
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.js:0
#: code:addons/documents/static/src/views/kanban/documents_kanban_record.js:0
#: model:ir.model.fields,field_description:documents.field_documents_document__company_id
msgid "Company"
msgstr "公司"

#. module: documents
#: model:ir.model,name:documents.model_res_config_settings
msgid "Config Settings"
msgstr "配置设定"

#. module: documents
#: model:ir.ui.menu,name:documents.Config
msgid "Configuration"
msgstr "配置"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model:ir.model,name:documents.model_res_partner
#: model:ir.model.fields,field_description:documents.field_documents_document__partner_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__partner_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Contact"
msgstr "联系人"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Contact your Administrator to get access if needed."
msgstr "如有需要，请联系您的管理员获取访问权限。"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_contracts
msgid "Contracts"
msgstr "合同"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "Control panel buttons"
msgstr "控制面板按钮"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.js:0
msgid "Copy Link"
msgstr "复制链接"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Copy Links"
msgstr "复制链接"

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_create_activity
msgid "Create Activity"
msgstr "创建活动"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Create Shortcut"
msgstr "创建快捷方式"

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.mail_activity_plan_action_document
msgid "Create a Document Activity Plan"
msgstr "创建文档活动计划"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_option
msgid "Create a new activity"
msgstr "创建新‎‎活动"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Create and edit new contact \""
msgstr "创建和编辑新联系人“"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Create new contact \""
msgstr "创建新联系人”"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_tag__create_uid
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Created by"
msgstr "创建人"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_date
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__create_date
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__create_date
#: model:ir.model.fields,field_description:documents.field_documents_tag__create_date
msgid "Created on"
msgstr "创建日期"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Creation Date"
msgstr "创建时间"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.xml:0
msgid "Current Version"
msgstr "当前版本"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "自定义退回消息"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__create_activity_date_deadline_range_type__days
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__days
msgid "Days"
msgstr "天"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_defaults
msgid "Default Values"
msgstr "默认值"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__deletion_delay
#: model:ir.model.fields,help:documents.field_res_config_settings__deletion_delay
msgid "Delay after permanent deletion of the document in the trash (days)"
msgstr "在垃圾桶中永久删除文件后的延迟时间（天数）"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.xml:0
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
#: code:addons/documents/static/src/views/list/documents_list_controller.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Delete"
msgstr "删除"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Delete focused or selected pages"
msgstr "删除重点或选定页面"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Delete permanently"
msgstr "永久删除"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_res_config_settings__deletion_delay
msgid "Deletion Delay"
msgstr "删除延迟"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__deletion_delay
msgid "Deletion delay"
msgstr "删除延迟"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Deletion delay (days)"
msgstr "删除延迟（天数）"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_deprecated
msgid "Deprecated"
msgstr "已弃用"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
#: code:addons/documents/static/src/views/hooks.js:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_rename
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
#: model_terms:ir.ui.view,arch_db:documents.folder_deletion_form
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Discard"
msgstr "丢弃"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Discoverable"
msgstr "可被发现"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_access__display_name
#: model:ir.model.fields,field_description:documents.field_documents_document__display_name
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__display_name
#: model:ir.model.fields,field_description:documents.field_documents_redirect__display_name
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__display_name
#: model:ir.model.fields,field_description:documents.field_documents_tag__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "Do you want to exit without saving or gather pages into one document ?"
msgstr "您希望不保存并退出，还是将页面合并到一个文档中？"

#. module: documents
#: model:ir.model,name:documents.model_documents_document
#: model:ir.model.fields,field_description:documents.field_documents_access__document_id
#: model:ir.model.fields,field_description:documents.field_documents_redirect__document_id
#: model:ir.model.fields,field_description:documents.field_documents_tag__document_ids
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "Document"
msgstr "单据"

#. module: documents
#: model:ir.model,name:documents.model_documents_access
msgid "Document / Partner"
msgstr "文件/合作伙伴"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.xml:0
msgid "Document Access Internal"
msgstr "内部的文件访问"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.xml:0
msgid "Document Access Link"
msgstr "文件访问链接"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__document_count
#: model:ir.model.fields,field_description:documents.field_res_partner__document_count
msgid "Document Count"
msgstr "文档数"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Document Name"
msgstr "单据名称"

#. module: documents
#: model:ir.actions.act_window,name:documents.mail_activity_plan_action_document
msgid "Document Plans"
msgstr "文档计划<br>"

#. module: documents
#: model:ir.model,name:documents.model_documents_redirect
msgid "Document Redirect"
msgstr "文件重定向"

#. module: documents
#: model:ir.model,name:documents.model_documents_request_wizard
msgid "Document Request"
msgstr "‎文档请求‎"

#. module: documents
#: model:mail.template,subject:documents.mail_template_document_request
msgid ""
"Document Request {{ object.name != False and ': '+ object.name or '' }}"
msgstr "文档请求 {{ object.name != False and ':'+ object.name or '' }}."

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: code:addons/documents/models/mail_activity.py:0
msgid "Document Request: %(name)s Uploaded by: %(user)s"
msgstr "文件要求：%(name)s上传者：%(user)s"

#. module: documents
#: model:mail.template,name:documents.mail_template_document_request_reminder
msgid "Document Request: Reminder"
msgstr "文档请求：提醒"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__document_token
msgid "Document Token"
msgstr "文件令牌"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "Document preview"
msgstr "文档预览"

#. module: documents
#. odoo-python
#: code:addons/documents/controllers/documents.py:0
msgid "Document uploaded by %(user)s"
msgstr "文件由%(user)s上传"

#. module: documents
#: model:mail.template,name:documents.mail_template_document_request
msgid "Document: Document Request"
msgstr "文档：文档请求"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/list/document_count_field.js:0
msgid "DocumentCountIntegerField"
msgstr "DocumentCountIntegerField"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: code:addons/documents/models/res_partner.py:0
#: model:ir.actions.act_window,name:documents.document_action
#: model:ir.actions.act_window,name:documents.document_action_portal
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__document_ids
#: model:ir.model.fields,field_description:documents.field_res_users__document_count
#: model:ir.ui.menu,name:documents.dashboard
#: model:ir.ui.menu,name:documents.menu_root
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.documents_view_activity
#: model_terms:ir.ui.view,arch_db:documents.portal_my_home_documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Documents"
msgstr "文档"

#. module: documents
#: model:ir.model,name:documents.model_documents_link_to_record_wizard
msgid "Documents Link to Record"
msgstr "文档 链接到记录"

#. module: documents
#: model:ir.model,name:documents.model_documents_mixin
msgid "Documents creation mixin"
msgstr "文档创建混合"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "Documents in the Trash cannot be shared"
msgstr "回收站里的文件无法分享"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.js:0
msgid "Documents moved to trash will show up here"
msgstr "移至垃圾箱的文件将显示在此处"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.js:0
msgid "Documents shared with you will appear here"
msgstr "与您共享的文档将出现在这里"

#. module: documents
#: model:ir.model,name:documents.model_documents_unlink_mixin
msgid "Documents unlink mixin"
msgstr "文件取消链接混入程式"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.xml:0
msgid "Done"
msgstr "已完成"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "Done!"
msgstr "完成！"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.xml:0
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_download.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Download"
msgstr "下载"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Download #{document.name}"
msgstr "下载 #{document.name}"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Download all files"
msgstr "下载所有文件"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_file
msgid "Download file"
msgstr "下载文件"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Download folder as zip"
msgstr "将文件夹下载为 zip"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Download zip #{subfolder.name}"
msgstr "下载zip文件 #{subfolder.name}"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_draft
msgid "Draft"
msgstr "草稿"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_drop_zone.xml:0
msgid "Drop files here to upload"
msgstr "拖动文件到此处上传"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_date_deadline_range
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_date_deadline_range
msgid "Due Date In"
msgstr "截止日期至"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_date_deadline_range_type
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_date_deadline_range_type
msgid "Due type"
msgstr "到期类型"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Duplicate"
msgstr "复制"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
#: code:addons/documents/static/src/views/search/documents_search_panel.xml:0
msgid "Edit"
msgstr "编辑"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: model:ir.model.fields.selection,name:documents.selection__documents_access__role__edit
#: model:ir.model.fields.selection,name:documents.selection__documents_document__access_internal__edit
#: model:ir.model.fields.selection,name:documents.selection__documents_document__access_via_link__edit
#: model:ir.model.fields.selection,name:documents.selection__documents_document__user_permission__edit
msgid "Editor"
msgstr "编辑"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "Email"
msgstr "电子邮件"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_email
msgid "Email Alias"
msgstr "电子邮件别名"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_details
msgid "Email Upload"
msgstr "电子邮件上传"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__email_cc
msgid "Email cc"
msgstr "邮件抄送"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "电子邮件域名，例如 '<EMAIL>' 中的 'example.com'"

#. module: documents
#: model:ir.actions.act_window,name:documents.mail_alias_action
msgid "Email links"
msgstr "电子邮件链接"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#: code:addons/documents/static/src/views/hooks.js:0
#: model:ir.model.fields.selection,name:documents.selection__documents_document__thumbnail_status__error
msgid "Error"
msgstr "错误"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Escape Preview/Deselect/Exit"
msgstr "退出预览/取消选择/退出"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "Exit Split Tools"
msgstr "退出拆分工具"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "Exit without saving"
msgstr "退出时不保存"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_partner_access.xml:0
msgid "Exp:"
msgstr "到期："

#. module: documents
#: model:documents.tag,name:documents.documents_tag_expense
msgid "Expense"
msgstr "费用"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_access__expiration_date
msgid "Expiration"
msgstr "到期日"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/list/documents_list_controller.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Export"
msgstr "导出"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Extra comments..."
msgstr "额外评论"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__favorited_ids
msgid "Favorite of"
msgstr "收藏"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_type_icon/documents_type_icon.xml:0
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__binary
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "File"
msgstr "文件"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__datas
msgid "File Content (base64)"
msgstr "文件内容(base64)"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__raw
msgid "File Content (raw)"
msgstr "文件内容(raw)"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__file_extension
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "File Extension"
msgstr "文件扩展"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__file_size
msgid "File Size"
msgstr "文件大小"

#. module: documents
#: model:ir.model,name:documents.model_ir_binary
msgid "File streaming helper model for controllers"
msgstr "控制器文件流帮助器模型"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/kanban/documents_kanban_renderer.xml:0
msgid "Files"
msgstr "文件"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Files Centralization"
msgstr "文件集中"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_deletion_form
msgid "Files will be sent to trash and deleted forever after"
msgstr "文件将被发送到垃圾箱并永久删除"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
msgid "Files will be sent to trash and deleted forever after %s days."
msgstr "文件将被发送到垃圾箱，并在 %s 天后被永久删除。"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_financial
msgid "Financial"
msgstr "财务"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_fiscal
msgid "Fiscal"
msgstr "财务"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Focus first page of next group"
msgstr "聚焦下一组第一页"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Focus first page of previous group"
msgstr "聚焦前一组第一页"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Focus next page"
msgstr "聚焦下一页"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Focus previous page"
msgstr "聚焦上一页"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
#: code:addons/documents/static/src/views/documents_type_icon/documents_type_icon.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_document__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__folder_id
#: model:ir.model.fields,field_description:documents.field_mail_activity_type__folder_id
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__folder
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "Folder"
msgstr "文件夹"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/kanban/documents_kanban_renderer.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Folders"
msgstr "文件夹"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_follower_ids
msgid "Followers"
msgstr "关注者"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_partner_ids
msgid "Followers (Partners)"
msgstr "关注者（合作伙伴）"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font Awesome图标，例如：fa-tasks"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "Gather in one document"
msgstr "汇集到一个文档"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_details
msgid "General"
msgstr "通用"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.xml:0
msgid "General access"
msgstr "一般访问"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "GiB"
msgstr "GiB"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Group By"
msgstr "分组方式"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_hr
msgid "HR"
msgstr "HR"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__has_message
msgid "Has Message"
msgstr "有消息"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Hide shortcuts"
msgstr "隐藏快捷方式"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__previous_attachment_ids
msgid "History"
msgstr "历史"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_access__id
#: model:ir.model.fields,field_description:documents.field_documents_document__id
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__id
#: model:ir.model.fields,field_description:documents.field_documents_redirect__id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__id
#: model:ir.model.fields,field_description:documents.field_documents_tag__id
msgid "ID"
msgstr "ID"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr "上级记录ID支持别名(例如:项目支持任务创建别名)"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_exception_icon
msgid "Icon"
msgstr "图标"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "指示异常活动的图标。"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__is_access_via_link_hidden
msgid ""
"If \"True\", only people given direct access to this document will be able "
"to view it. If \"False\", access with the link also given to all who can "
"access the parent folder."
msgstr "如果 “True”，则只有直接访问此文档的人员才能查看它。如果为 “False”，则还向所有可以访问母文件夹的人提供链接访问。"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_needaction
msgid "If checked, new messages require your attention."
msgstr "如果勾选此项，则需要查看新消息。"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_has_error
#: model:ir.model.fields,help:documents.field_documents_document__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "如果勾选此项， 某些消息将出现发送错误。"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr "如果设置，此内容将自动发送给未经授权的用户，而不是默认消息。"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_images
msgid "Images"
msgstr "图像"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Impossible to delete folders used by other applications."
msgstr "无法删除其他应用程序使用的文件夹。"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Impossible to invite partners on multiple documents at once."
msgstr "无法同时在多个文件上邀请合作伙伴。"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "In"
msgstr "在"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__thumbnail_status__restricted
msgid "Inaccessible"
msgstr "不可访问"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_inbox
#: model:mail.activity.type,name:documents.mail_documents_activity_data_Inbox
msgid "Inbox"
msgstr "收件箱"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"Incorrect values. Use one of the following for the following fields: "
"%(hints)s.)"
msgstr "不正确的值。对以下字段使用以下选项之一：%(hints)s)"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__index_content
msgid "Indexed Content"
msgstr "已索引的内容"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_details.js:0
msgid "Info & Tags"
msgstr "信息和标签"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Info & tags"
msgstr "信息和标签"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.xml:0
msgid "Internal Users"
msgstr "内部用户"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__access_internal
msgid "Internal Users Rights"
msgstr "内部用户权限"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.js:0
msgid "Internal users can edit"
msgstr "内部用户可编辑"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.js:0
msgid "Internal users can view"
msgstr "内部用户可查看"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Invalid folder id"
msgstr "无效文件夹ID"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "Invalid operation"
msgstr "无效操作"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Invalid role."
msgstr "无效角色。"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_editable_attachment
msgid "Is Editable Attachment"
msgstr "是可编辑的附件"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_favorited
msgid "Is Favorited"
msgstr "很受欢迎"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_is_follower
msgid "Is Follower"
msgstr "是关注者"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_multipage
msgid "Is considered multipage"
msgstr "为多页"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"It is not possible to move archived documents or documents to archived "
"folders."
msgstr "无法将存档文件或文件移动到存档文件夹。"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"Item(s) you wish to restore are included in archived folders. To restore these items, you must restore the following including folders instead:\n"
"- %(folders_list)s"
msgstr ""
"您要还原的项目包含在存档文件夹中。要还原这些项目，必须还原以下文件夹：\n"
"- %(folders_list)s"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Items in trash will be deleted forever after %s days."
msgstr "垃圾箱中的项目将在 %s 天后被永久删除。"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Items moved to the trash will be deleted forever after %s days."
msgstr "移至垃圾箱的项目将在 %s 天后被永久删除。"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_access__last_access_date
#: model:ir.model.fields,field_description:documents.field_documents_document__last_access_date_group
msgid "Last Accessed On"
msgstr "最后访问时间"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_tag__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__write_date
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__write_date
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__write_date
#: model:ir.model.fields,field_description:documents.field_documents_tag__write_date
msgid "Last Updated on"
msgstr "上次更新日期"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_legal
msgid "Legal"
msgstr "法律"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid ""
"Let's process documents in your Inbox.<br/><i>Tip: Use Tags to filter "
"documents and structure your process.</i>"
msgstr "让我们处理收件箱中的文档。<br/><i>提示：使用标记筛选文档并组织流程。</i>"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Let's process these bills: send to Finance workspace."
msgstr "让我们处理这些账单：发送到财务工作区。"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Let's process this document, coming from our scanner."
msgstr "让我们处理这个文档，从我们的扫描仪。"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid ""
"Let's tag this mail as legal<br/> <i>Tips: actions can be tailored to your "
"process, according to the workspace.</i>"
msgstr "让我们把这封邮件标记为法律<br/> <i>提示：根据工作空间，动作可以根据你的流程进行调整。</i>"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
#: code:addons/documents/static/src/views/documents_type_icon/documents_type_icon.xml:0
#: model_terms:ir.ui.view,arch_db:documents.documents_link_to_record_form_view
msgid "Link"
msgstr "链接"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_access_via_link_hidden
msgid "Link Access Hidden"
msgstr "链接访问隐藏"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__access_via_link
msgid "Link Access Rights"
msgstr "链接访问权限"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__url
msgid "Link URL"
msgstr "链接网址"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.js:0
msgid "Link copied to clipboard!"
msgstr "链接被复制到剪贴板!"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.xml:0
msgid "Link copied to the clipboard."
msgstr "链接已复制到剪贴板。"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "Linked To"
msgstr "链接到"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.js:0
msgid "Links copied to clipboard!"
msgstr "链接已复制到剪贴板！"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "收件时检测本地的别名域"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Lock"
msgstr "锁定"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_document__is_locked
msgid "Locked"
msgstr "已锁定"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__lock_uid
msgid "Locked by"
msgstr "锁定者"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Log a note..."
msgstr "记录一个备注..."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Login"
msgstr "登录"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Logout"
msgstr "注销"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Mail: %s"
msgstr "邮件：%s"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Manage Versions"
msgstr "管理版本"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.js:0
msgid "Manage Versions of \"%s\""
msgstr "管理不同版本的 “%s”"

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_remove_activities
msgid "Mark activities as completed"
msgstr "将活动标记为已完成"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Merge PDFs"
msgstr "合并PDF"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_note
msgid "Message"
msgstr "信息"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_error
msgid "Message Delivery error"
msgstr "消息发送错误"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_ids
msgid "Messages"
msgstr "消息"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "MiB"
msgstr "MiB"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__mimetype
msgid "Mime Type"
msgstr "MIME 类型"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Missing documents reference."
msgstr "缺少参考文档。"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__model_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Model"
msgstr "模型"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__accessible_model_ids
msgid "Models"
msgstr "模型"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__create_activity_date_deadline_range_type__months
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__months
msgid "Months"
msgstr "个月"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "More"
msgstr "更多"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Move this page to trash"
msgstr "将此页移至回收站"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Move to Trash"
msgstr "移至回收站"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_archive.js:0
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
#: code:addons/documents/static/src/views/hooks.js:0
#: model_terms:ir.ui.view,arch_db:documents.folder_deletion_form
msgid "Move to trash"
msgstr "移动到回收站"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
msgid "Move to trash?"
msgstr "移动到垃圾桶？"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Moved to trash"
msgstr "移动到垃圾箱"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "Must have the link to access"
msgstr "必须具有访问链接"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "My Activities"
msgstr "我的活动"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "我的活动截止时间"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "My Documents"
msgstr "我的文档"

#. module: documents
#. odoo-javascript
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.js:0
#: code:addons/documents/static/src/views/kanban/documents_kanban_record.js:0
msgid "My Drive"
msgstr "我的驱动器"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_access_role_or_last_access_date
msgid "NULL roles must have a set last_access_date"
msgstr "NULL 角色必须有一组 last_access_date"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__name
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__name
#: model:ir.model.fields,field_description:documents.field_documents_tag__name
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Name"
msgstr "名称"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Name / Extension"
msgstr "名称 / 扩展名"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "New"
msgstr "新建"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "New File"
msgstr "新建文件"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.xml:0
msgid "New Folder"
msgstr "新文件夹"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "New Group"
msgstr "新建组"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "下一活动日历事件"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "下一活动截止日期"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_summary
msgid "Next Activity Summary"
msgstr "下一活动摘要"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_type_id
msgid "Next Activity Type"
msgstr "下一活动类型"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "No activity"
msgstr "没有活动"

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.mail_activity_type_action_document
msgid "No activity types found. Let's create one!"
msgstr "未找到活动类型。让我们创建一个！"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.xml:0
msgid "No alias configured"
msgstr "未设置别名"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "No document has been selected"
msgstr "尚未选择文档"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/chatter/documents_chatter_overlay.xml:0
msgid "No item selected"
msgstr "未选择项目"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.js:0
msgid "No one on the internet can access"
msgstr "互联网上无人可以访问"

#. module: documents
#. odoo-python
#: code:addons/documents/models/ir_actions_server.py:0
msgid "No server actions found for Documents!"
msgstr "未找到有关文件管理的服务器操作！"

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.mail_alias_action
msgid "No shared links"
msgstr "没有共享链接"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__access_internal__none
#: model:ir.model.fields.selection,name:documents.selection__documents_document__access_via_link__none
#: model:ir.model.fields.selection,name:documents.selection__documents_document__user_permission__none
msgid "None"
msgstr "无"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Not a file"
msgstr "不是文件"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Not a folder."
msgstr "不是文件夹"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Not attached"
msgstr "未连接"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "Not set"
msgstr "未设置"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_note
msgid "Note"
msgstr "备注"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Notify"
msgstr "通知"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_needaction_counter
msgid "Number of Actions"
msgstr "操作数量"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_error_counter
msgid "Number of errors"
msgstr "错误数量"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "需要采取行动的消息数量"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "发送错误的消息的数量"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Odoo Logo"
msgstr "Odoo 徽标"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Odoo Studio - Customize workflows in minutes"
msgstr "Odoo Studio - 在几分钟内定制工作流程"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Odoo Website"
msgstr "Odoo 网站"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__last_access_date_group__0_older
msgid "Older"
msgstr "过时"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Only Documents Managers can create in company folder."
msgstr "只有文档管理员才可以在公司文件夹里创建。"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Only Documents Managers can set aliases."
msgstr "只有文档管理员可以设置别名。"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Only Documents managers can set an alias."
msgstr "只有文档管理员才能设置别名。"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_settings.js:0
msgid "Only people with access can open with the link"
msgstr "只有有权访问的人才能打开该链接"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Open #{document_url.name}"
msgstr "开启 #{document_url.name}"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Open folder #{subfolder.name}"
msgstr "打开文件夹 #{subfolder.name}"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "Open the permissions panel"
msgstr "打开权限面板"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"Operation not supported. Please use \"Move to Trash\" / `action_archive` "
"instead."
msgstr "不支持操作。请改用 “移至垃圾箱”/“action_archive”。"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"Operation not supported. Please use \"Restore\" / `action_unarchive` "
"instead."
msgstr "不支持操作。请使用 “还原”/“action_unarchive ”。"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr "无论是否回复，所有的接收的邮件都将附上一条线索（记录）选配的ID。如果设置了，这个将完全阻止新记录的创建。"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.js:0
msgid "Optional message..."
msgstr "可选消息…"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.xml:0
msgid "Or send emails to"
msgstr "或发送电子邮件至"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_other
msgid "Other"
msgstr "其他"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: code:addons/documents/static/src/components/documents_permission_panel/documents_partner_access.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_document__owner_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__requestee_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Owner"
msgstr "所有者"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Owner: #{document.owner_id.name}"
msgstr "拥有人: #{document.owner_id.name}"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Owner: #{document_url.owner_id.name}"
msgstr "所有者: #{document_url.owner_id.name}"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Owner: #{subfolder.owner_id.name}"
msgstr "所有者: #{subfolder.owner_id.name}"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_parent_model_id
msgid "Parent Model"
msgstr "上级模型"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__parent_path
msgid "Parent Path"
msgstr "上级路径"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "上级记录ID"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"父级模型拥有别名。拥有别名参考的模型不一定是alias_model_id给出的模型 (例如：project(parent_model) 和任务 "
"(模型))"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "Partial transfer"
msgstr "部分转移"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_access__partner_id
msgid "Partner"
msgstr "合作伙伴"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_partner_access.xml:0
msgid "People with access"
msgstr "有权限的人"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_pinned_folder
msgid "Pinned to Company roots"
msgstr "置顶于公司根级资料册"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.not_available
msgid ""
"Please login or contact the person that shared this link for more "
"information."
msgstr "请登录或联系共享此链接的人员以获取更多信息。"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"使用邮件网关在文档中发布消息的政策：\n"
"- 所有人：任何人都可以发布\n"
"- 合作伙伴：仅限认证通过的合作伙伴\n"
"- 关注者：仅限关联文档或关注频道的成员\n"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__thumbnail_status__present
msgid "Present"
msgstr "出勤"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_presentations
msgid "Presentations"
msgstr "展示"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_file
msgid "Preview files"
msgstr "预览文件"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_project
msgid "Project"
msgstr "项目"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "Put in %s"
msgstr "放入 %s"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__rating_ids
msgid "Ratings"
msgstr "点评"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Recent"
msgstr "最近"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.js:0
msgid "Recently accessed Documents will show up here"
msgstr "最近访问的文件会在此处显示"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Recently accessed documents."
msgstr "最近访问的文件。"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__resource_ref
msgid "Record"
msgstr "记录"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__alias_force_thread_id
msgid "Record Thread ID"
msgstr "记录线索ID"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Remaining Pages"
msgstr "剩余页数"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/activity/documents_activity_controller.js:0
msgid "Reminder emails have been sent."
msgstr "已发送提醒邮件。"

#. module: documents
#: model:mail.template,subject:documents.mail_template_document_request_reminder
msgid ""
"Reminder to upload your document{{ object.name and ' : ' + object.name or ''"
" }}"
msgstr "提醒上传文件{{ 对象.名称和 ' : ' + 对象.名称或 '' }}"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_remove_partner_button.xml:0
msgid "Remove Access"
msgstr "删除访问"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_remove_partner_button.xml:0
msgid "Remove Member"
msgstr "移除成员"

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_tag_remove_inbox
msgid "Remove Tag Inbox"
msgstr "删除标签收件箱"

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_tag_remove_to_validate
msgid "Remove Tag To Validate"
msgstr "移除标签以进行验证"

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_remove_tags
msgid "Remove all tags"
msgstr "删除所有标签"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_star.js:0
msgid "Remove star"
msgstr "删除星标"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_rename.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_rename
msgid "Rename"
msgstr "重命名"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Request"
msgstr "请求"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__request_activity_id
msgid "Request Activity"
msgstr "请求活动"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Request To"
msgstr "请求"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/web/activity/activity_menu_patch.xml:0
msgid "Request a Document"
msgstr "请求的文档"

#. module: documents
#: model:ir.actions.act_window,name:documents.action_request_form
msgid "Request a file"
msgstr "‎请求文件‎"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_request_page
msgid ""
"Requested\n"
"                                ∙"
msgstr ""
"要求\n"
"                                ∙"

#. module: documents
#: model:mail.activity.type,name:documents.mail_documents_activity_data_md
msgid "Requested Document"
msgstr "请求的文档"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Requested Documents"
msgstr "要求提供的文件"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__requestee_partner_id
msgid "Requestee Partner"
msgstr "被请求业务伙伴"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_model_name
msgid "Res Model Name"
msgstr "资源模型名称"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__res_id
msgid "Resource ID"
msgstr "资源ID"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_model
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__res_model
msgid "Resource Model"
msgstr "资源模型"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_name
msgid "Resource Name"
msgstr "资源名称"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_user_id
msgid "Responsible"
msgstr "负责人"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_user_id
msgid "Responsible User"
msgstr "责任用户"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/form/folder_form_controller.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Restore"
msgstr "还原"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Restricted"
msgstr "受限"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Restricted Folder"
msgstr "限制的文件夹"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.xml:0
msgid "Revert changes"
msgstr "还原变更"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_access__role
msgid "Role"
msgstr "角色"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_sms_error
msgid "SMS Delivery error"
msgstr "短信发送错误"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_sales
msgid "Sales"
msgstr "销售"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Save"
msgstr "保存"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.js:0
msgid "Save or discard changes first"
msgstr "先保存或放弃更改"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.xml:0
msgid "Saving..."
msgstr "正在保存"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Search people"
msgstr "搜索人员"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.js:0
msgid "Select a folder to upload a document"
msgstr "选择文件夹上传文件"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/kanban/documents_kanban_renderer.js:0
#: code:addons/documents/static/src/views/list/documents_list_renderer.js:0
msgid "Select all"
msgstr "全选"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select focused page"
msgstr "选择特定页面"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select next page"
msgstr "选择下一页"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select next pages of the group"
msgstr "选择该组的下页"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select previous page"
msgstr "选择前页"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select previous pages of the group"
msgstr "选择该组的前页"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Select/Deselect all pages"
msgstr "选择/取消选择所有页面"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Send"
msgstr "发送"

#. module: documents
#: model:ir.actions.server,name:documents.ir_actions_server_send_to_finance
msgid "Send To Finance"
msgstr "发送给财务"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid "Send this letter to the legal department, by assigning the right tags."
msgstr "把这封信寄到法律部门，通过分配正确的标签。"

#. module: documents
#: model:mail.template,description:documents.mail_template_document_request
msgid "Sent to partner when requesting a document from them"
msgstr "在向合作伙伴索取文档时发送给他们"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_tag__sequence
msgid "Sequence"
msgstr "序列"

#. module: documents
#: model:ir.model,name:documents.model_ir_actions_server
msgid "Server Action"
msgstr "服务器操作"

#. module: documents
#. odoo-python
#: code:addons/documents/models/ir_actions_server.py:0
msgid "Server Actions"
msgstr "服务器操作"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Set deletion delay for documents in the Trash"
msgstr "设置删除回收站中文件的延迟时间"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_access_expiration_date_btn.xml:0
msgid "Set expiration date"
msgstr "设定到期日"

#. module: documents
#: model:mail.template,description:documents.mail_template_document_request_reminder
msgid ""
"Set reminders in activities to notify users who didn't upload their "
"requested document"
msgstr "在活动中设置提醒，通知未上传所需文档的用户"

#. module: documents
#: model:ir.actions.act_window,name:documents.configuration_action
#: model:ir.actions.act_window,name:documents.settings_action
#: model:ir.ui.menu,name:documents.settings_menu
msgid "Settings"
msgstr "设置"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_item_share.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
#: code:addons/documents/static/src/views/search/documents_search_panel.xml:0
#: code:addons/documents/static/src/web/error_dialog/error_dialog_patch.xml:0
msgid "Share"
msgstr "分享"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.js:0
msgid "Share: %(documentName)s"
msgstr "分享：%(documentName)s"

#. module: documents
#. odoo-javascript
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.js:0
#: code:addons/documents/static/src/views/kanban/documents_kanban_record.js:0
msgid "Shared with me"
msgstr "请与我分享"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
msgid "Sharing..."
msgstr "分享"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__shortcut_ids
msgid "Shortcut"
msgstr "快捷"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "Shortcut created"
msgstr "已创建快捷方式"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
#: code:addons/documents/static/src/views/search/documents_control_panel.js:0
msgid "Shortcuts can only be created one at a time."
msgstr "快捷方式一次只能创建一个。"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Shortcuts cannot change target document."
msgstr "快捷方式不能改变目标文件。"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_file
msgid "Size:"
msgstr "大小："

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid "Some files could not be uploaded (max size: %s)."
msgstr "某些文件无法上传（最大文件：%s）。"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__shortcut_document_id
msgid "Source Document"
msgstr "源单据"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "Special folders cannot be shared"
msgstr "特殊的文件夹不能分享"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Split"
msgstr "拆分"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/attachments/document_file_viewer.xml:0
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
msgid "Split PDF"
msgstr "拆分PDF"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "Split all white pages"
msgstr "分割所有白页"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
msgid "Split selected pages"
msgstr "分割选定页面"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Starred"
msgstr "星标消息"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"基于活动的状态\n"
"逾期：超出到期日期\n"
"今天：活动日期是今天\n"
"计划：未来活动。"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "Stay here"
msgstr "呆在这里"

#. module: documents
#: model:ir.ui.menu,name:documents.structure
msgid "Structure"
msgstr "结构"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_activity_summary
msgid "Summary"
msgstr "摘要"

#. module: documents
#: model:res.groups,name:documents.group_documents_system
msgid "System Administrator"
msgstr "系统管理员"

#. module: documents
#: model:ir.model,name:documents.model_documents_tag
#: model:ir.model.fields,field_description:documents.field_mail_activity_type__tag_ids
#: model_terms:ir.ui.view,arch_db:documents.tag_view_search
msgid "Tag"
msgstr "标签"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Tag Name"
msgstr "标签名称"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_tag_tag_name_unique
msgid "Tag name already used"
msgstr "标签名称已被使用"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model:ir.actions.act_window,name:documents.tag_action
#: model:ir.model.fields,field_description:documents.field_documents_document__tag_ids
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__tag_ids
#: model:ir.ui.menu,name:documents.category_menu
msgid "Tags"
msgstr "标签"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_text
msgid "Text"
msgstr "文本"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_tag__tooltip
msgid "Text shown when hovering on this tag"
msgstr "将鼠标悬停在此标签上时显示的文本"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_res_config_settings_check_deletion_delay
msgid "The deletion delay should be positive."
msgstr "删除延迟应为正值。"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/web/error_dialog/error_dialog_patch.js:0
#: code:addons/documents/static/src/web/error_dialog/error_dialog_patch.xml:0
msgid "The document URL has been copied to your clipboard."
msgstr "文件URL已复制到剪贴板。"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/core/document_service.js:0
msgid "The document has been moved."
msgstr "该文件已被移除。"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"The following documents can't have alias: \n"
"- %(records)s"
msgstr ""
"以下文件不可有別名：\n"
"- %(records)s"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"The following documents/folders can't be owned by a Portal User: \n"
"- %(partners)s"
msgstr ""
"以下文件/资料夹不可由门户网站使用者拥有：\n"
"- %(partners)s"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "The following documents/shortcuts have a company mismatch: \n"
msgstr "下列文件/快捷方式中的公司不相符：\n"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "The following documents/shortcuts have a type mismatch: \n"
msgstr "以下文件/捷径的类型不相符：\n"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "The following shortcuts cannot be set as documents parents: \n"
msgstr "以下快捷方式不能被设置为文件上级：\n"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"相应于这个别名对应的模型(Odoo单据种类)。任何一封不属于对某个已存在的记录的到来邮件，将导致此模块中新记录的创建(例如：一个新的项目任务)。"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr "邮件别名的名称，例如：如果您要收取 <<EMAIL>> 的EMail,别名就要设为：\"jobs\""

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__last_access_date_group__1_month
msgid "This Month"
msgstr "本月"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__last_access_date_group__2_week
msgid "This Week"
msgstr "本周"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "This action does not exist."
msgstr "这个动作不存在。"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_document_attachment_unique
msgid "This attachment is already a document"
msgstr "此附件已是文档"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.not_available
msgid "This document does not exist or is not publicly available."
msgstr "此文件不存在或未公开提供。"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_request_page
msgid ""
"This document has been requested.\n"
"                <b onclick=\"document.querySelector('.o_request_upload').click()\" style=\"cursor:pointer;\">Upload it</b>."
msgstr ""
"已要求提供此文档。\n"
"                <b onclick=\"document.querySelector('.o_request_upload').click()\" style=\"cursor:pointer;\">上传</b>。"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "This document has been restored."
msgstr "此文件已恢复。"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"This file has been sent to the trash and will be deleted forever on the %s"
msgstr "该文件已被发送到垃圾箱，并将在 %s 上被永久删除。"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "This folder does not exist or is not accessible."
msgstr "该文件夹不存在或无法访问。"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "This is a folder"
msgstr "这是文件夹"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.xml:0
msgid "This is a shortcut"
msgstr "这是一个快捷方式"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "This is a shortcut - Click to access source document"
msgstr "这是快捷方式 - 点击可访问源文件"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_access_unique_document_access_partner
msgid "This partner is already set on this document."
msgstr "该合作伙伴已在本文档中设置。"

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.mail_activity_type_action_document
msgid ""
"Those represent the different categories of things you have to do (e.g. "
"\"Call\" or \"Send email\")."
msgstr "这些代表了你必须做的不同类别的事情（例如：\"打电话 \"或 \"发送电子邮件\"）"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__thumbnail
msgid "Thumbnail"
msgstr "缩略图"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__thumbnail_status
msgid "Thumbnail Status"
msgstr "缩略图状态"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.xml:0
msgid "Tip: configure your scanner to send all documents to this address."
msgstr "提示：将你的扫描仪配置为将所有文档发送到这个地址。"

#. module: documents
#: model:digest.tip,name:documents.digest_tip_documents_0
msgid "Tip: Become a paperless company"
msgstr "小贴士：成为无纸化公司"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_to_validate
msgid "To Validate"
msgstr "待验证"

#. module: documents
#: model:mail.activity.type,name:documents.mail_documents_activity_data_tv
msgid "To validate"
msgstr "‎待核准‎"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__last_access_date_group__3_day
msgid "Today"
msgstr "今天"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/kanban/documents_kanban_renderer.js:0
msgid "Toggle favorite"
msgstr "切换收藏"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_tag__tooltip
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Tooltip"
msgstr "工具提示"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Trash"
msgstr "回收站"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Trash Management"
msgstr "垃圾管理"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__is_editable_attachment
msgid "True if we can edit the link attachment."
msgstr "如果我们可以编辑链接的附件，则为真。"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__type
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Type"
msgstr "类型"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "记录中异常活动的类型。"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__url
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "URL"
msgstr "网址"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"URL %s does not seem complete, as it does not begin with http(s):// or "
"ftp://"
msgstr "URL %s 似乎不完整，因为它不是以 http(s):// 或 ftp:// 开头"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__url_preview_image
msgid "URL Preview Image"
msgstr "URL预览图像"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Unavailable action."
msgstr "无法执行的操作。"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Unlock"
msgstr "解锁"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "Unnamed"
msgstr "未命名的"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "Upload"
msgstr "上传"

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.document_action
#: model_terms:ir.actions.act_window,help:documents.document_action_portal
msgid ""
"Upload <span class=\"fw-normal\">a file or</span> drag <span class=\"fw-"
"normal\">it here.</span>"
msgstr ""
"上传<span class=\"fw-normal\">文件或</span><span class=\"fw-"
"normal\">将其拖到此处。</span>"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.xml:0
msgid "Upload New Version"
msgstr "上传新版本"

#. module: documents
#. odoo-python
#: code:addons/documents/models/mail_activity.py:0
msgid "Upload file request"
msgstr "上传文件请求"

#. module: documents
#: model:ir.model,name:documents.model_res_users
#: model:res.groups,name:documents.group_documents_user
msgid "User"
msgstr "用户"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_member_invite.xml:0
#: code:addons/documents/static/src/components/documents_permission_panel/documents_partner_access.xml:0
msgid "User Access Role"
msgstr "用户访问角色"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__user_permission
msgid "User permission"
msgstr "用户权限"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_vat
msgid "VAT"
msgstr "增值税"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_validated
msgid "Validated"
msgstr "已验证"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_manage_versions_panel/documents_manage_versions_panel.xml:0
msgid ""
"Versions are displayed in the order they were uploaded to Documents.\n"
"                    If you delete the most recent version, the previous one is automatically restored as the current one."
msgstr ""
"版本按照上传到文档的顺序显示。\n"
"                    如果删除最新版本，则之前的版本会自动恢复为当前版本。"

#. module: documents
#: model:documents.tag,name:documents.documents_tag_videos
msgid "Videos"
msgstr "视频"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
#: model:ir.model.fields.selection,name:documents.selection__documents_access__role__view
#: model:ir.model.fields.selection,name:documents.selection__documents_document__access_internal__view
#: model:ir.model.fields.selection,name:documents.selection__documents_document__access_via_link__view
#: model:ir.model.fields.selection,name:documents.selection__documents_document__user_permission__view
msgid "Viewer"
msgstr "查看器"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/js/tours/documents.js:0
msgid ""
"Want to become a <b>paperless company</b>? Let's discover Odoo Documents."
msgstr "想成为一家无纸化公司吗？我们来看看Odoo文档。"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__website_message_ids
msgid "Website Messages"
msgstr "网站消息"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__website_message_ids
msgid "Website communication history"
msgstr "网站沟通记录"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__create_activity_date_deadline_range_type__weeks
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__weeks
msgid "Weeks"
msgstr "周"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_exit_dialog/pdf_exit_dialog.xml:0
msgid "What do you want to do with the remaining pages ?"
msgstr "您想如何处理剩余页面？"

#. module: documents
#: model_terms:web_tour.tour,rainbow_man_message:documents.documents_tour
msgid ""
"Wow... 6 documents processed in a few seconds, You're good.<br>The tour is "
"complete. Try uploading your own documents now."
msgstr "哇... 几秒钟内处理了 6 个文档。<br>浏览结束。现在尝试上传您自己的文档。"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_partner_access.xml:0
msgid "You"
msgstr "您"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to (un)archive documents."
msgstr "不允许（取消）归档文件。"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to change ownerships of documents you do not own."
msgstr "不允许更改不属于您的文件的所有权。"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to delete all these items."
msgstr "您不能删除所有这些项目。"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to execute embedded actions."
msgstr "不允许执行嵌入式操作。"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to move (some of) these documents."
msgstr "您无权移动（部分）这些文件"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to pin/unpin embedded Actions."
msgstr "不允许固定/取消固定嵌入的操作。"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to read the permission panel data."
msgstr "您没有权限读取权限面板数据。"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You are not allowed to write in this folder."
msgstr "不允许在此文件夹中写入内容。"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__attachment_type
msgid ""
"You can either upload a file from your computer or copy/paste an internet "
"link to your file."
msgstr "您可以从您的电脑上传一个文件或者复制/粘贴一个 Internet 链接到您的文件."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/cog_menu/documents_cog_menu_pin_actions.js:0
msgid "You can not pin actions for that folder."
msgstr "您无法固定该文件夹的操作。"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid ""
"You can not update the access of a shortcut, update its target instead."
msgstr "您不能更新快捷方式的访问权限，只能更新其目标。"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You can't access that folder_id."
msgstr "您无法访问 folder_id。"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid ""
"You can't create shortcuts in or move documents to this special folder."
msgstr "您无法在此特殊文件夹中创建快捷方式，或将文档移动到此特殊文件夹。"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You can't move documents out of folders you cannot edit."
msgstr "您无法将文档移出无法编辑的文件夹。"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You cannot change the owner of documents you do not own."
msgstr "无法更改不属于您的文档的所有者。"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_tag.py:0
msgid "You cannot delete tags used in server actions."
msgstr "您无法删除服务器操作中使用的标签。"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You cannot delete this attachment."
msgstr "您无法删除此附件。"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You cannot duplicate document(s) in the Trash."
msgstr "您不能复制回收站中的文档。"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
msgid "You cannot move folders or files when in the trash."
msgstr "您不能移动回收站里的文件夹或文件。"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid "You cannot move request in the company folder"
msgstr "你不能移动公司文件夹里的请求"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You cannot pin an action on that document."
msgstr "您无法固定该文档的操作。"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You cannot pin that type of action."
msgstr "您无法固定此类操作。"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_remove_partner_button.xml:0
msgid "You cannot remove this partner"
msgstr "您不能移除该合作伙伴"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_controller_mixin.xml:0
msgid "You cannot share multiple documents at the same time"
msgstr "您不能同时共享多个文档"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "You do not have sufficient access rights to delete these documents."
msgstr "您没有足够的访问权限删除这些文件。"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/documents_model_mixin.js:0
#: code:addons/documents/static/src/views/search/documents_search_panel.js:0
msgid ""
"You don't have the rights to move documents nor create shortcut to that "
"folder."
msgstr "您没有权限移动文件或为该文件夹创建快捷方式。"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_drop_zone.xml:0
msgid "You must be in a specific writable workspace to upload files"
msgstr "您必须在特定的可写工作区才能上传文件"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Your Role: %s"
msgstr "您的角色：%s"

#. module: documents
#. odoo-python
#: code:addons/documents/models/documents_document.py:0
msgid "Your individual space."
msgstr "您的个人空间。"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/helper/documents_action_helper.js:0
msgid "Your personal space"
msgstr "您的个人空间"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_details
msgid "alias"
msgstr "别名"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "bytes"
msgstr "字节"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_deletion_form
msgid "days."
msgstr "日."

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.js:0
msgid "document"
msgstr "单据"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "documents"
msgstr "文档"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "e.g. Discuss proposal"
msgstr "例如：讨论提案"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_new_folder
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_rename
msgid "e.g. Finance"
msgstr "例如：Finance"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "e.g. Missing Expense"
msgstr "例如：缺失的费用"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "e.g. To Validate"
msgstr "例如：为了验证"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "e.g. https://www.youtube.com/watch?v=CP96yVnXNrY"
msgstr "e.g. https://www.youtube.com/watch?v=CP96yVnXNrY"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form_details
msgid "e.g. mycompany.com"
msgstr "e.g. mycompany.com"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "files"
msgstr "文件"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_permission_panel/documents_permission_panel.js:0
msgid "folder"
msgstr "纸夹"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "folders,"
msgstr "文件夹、"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__is_readonly_model
msgid "is_readonly_model"
msgstr "is_readonly_model"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/chatter/documents_chatter_overlay.xml:0
msgid "items selected"
msgstr "所选项目"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/components/documents_details_panel/documents_details_panel.xml:0
msgid "items,"
msgstr "个项目，"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "kiB"
msgstr "千字节"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_page/pdf_page.xml:0
msgid "select"
msgstr "选择"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.mail_template_document_share
msgid "shared a document with you.<br/>"
msgstr "与您分享了一份文件。<br/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.mail_template_document_share
msgid "shared a folder with you.<br/>"
msgstr "与您分享了一个文件夹。<br/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid "shared by"
msgstr "分享者"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.mail_template_document_share
msgid "shared this document with you:"
msgstr "与您分享这份文件："

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.mail_template_document_share
msgid "shared this folder with you:"
msgstr "与您分享这个文件夹："

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/views/hooks.js:0
msgid "status code: %(status)s, message: %(message)s"
msgstr "状态代码：%(status)s，消息：%(message)s"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "sub-doc-%s"
msgstr "sub-doc-%s"

#. module: documents
#. odoo-javascript
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
msgid "unnamed"
msgstr "未命名"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_folder_page
msgid ""
"•\n"
"                                        <b>URL</b>"
msgstr ""
"•\n"
"                                      <b>URL</b>"
