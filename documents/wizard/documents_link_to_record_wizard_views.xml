<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="documents_link_to_record_form_view" model="ir.ui.view">
        <field name="name">documents.link_to_record_wizard.form</field>
        <field name="model">documents.link_to_record_wizard</field>
        <field name="arch" type="xml">
            <form>
                <group>
                    <field name="is_readonly_model" invisible="1"/>
                    <field name="document_ids" widget="many2many_tags"/>
                    <field name="model_id" readonly="is_readonly_model" options="{'no_open': True, 'no_create': True}"/>
                    <field name="resource_ref" options="{'model_field': 'model_id', 'no_create': True}" invisible="not model_id" groups="base.group_erp_manager"/>
                    <field name="resource_ref" options="{'hide_model': True, 'no_create': True}" invisible="not model_id" groups="!base.group_erp_manager"/>
                </group>
                <footer>
                    <button name="link_to" type="object" data-hotkey="q"
                            string="Link" class="oe_highlight" invisible="not resource_ref"/>
                    <button special="cancel" data-hotkey="x" string="Cancel"/>
                </footer>
            </form>
        </field>
    </record>
</odoo>
