<?xml version="1.0" encoding="UTF-8"?>
<templates>
    <t t-name="documents.DocumentsKanbanRenderer" t-inherit="web.KanbanRenderer" t-inherit-mode="primary">
        <xpath expr="//div[hasclass('o_kanban_renderer')]" position="attributes">
            <attribute name="class" add="o_documents_view o_renderer_with_search_panel position-relative" separator=" "/>
            <attribute name="t-on-click">onGlobalClick</attribute>
        </xpath>
        <xpath expr="//div[hasclass('o_kanban_renderer')]" position="after">
            <!-- TODO: clean -->
            <t t-set="chatterRecord" t-value="targetRecords.length === 0  ? this.getContainerRecord() : targetRecords[0]"/>
            <t t-set="multipleSelection" t-value="targetRecords.length &gt; 1"/>
            <t t-set="chatterDisabled" t-value="multipleSelection || !chatterRecord.data || typeof chatterRecord.data.id !== 'number'"/>
            <div t-if="this.chatterState.visible"
                class="o_document_chatter_container o-mail-ChatterContainer"
                t-att-class="{'mt-2': isMobile, 'h-100 o-aside': !isMobile, 'documents_chatter_disabled': chatterDisabled, 'd-flex flex-column' : !chatterDisabled}"
                t-ref="chatterContainer">
                <t t-if="chatterDisabled" t-call="documents.DocumentsViews.ChatterOverlay">
                    <t t-if="multipleSelection" t-set="itemsNumber" t-value="targetRecords.length"/>
                </t>
                <DocumentsDetailsPanel t-if="!chatterDisabled" record="chatterRecord" nbViewItems="getNbViewItems()" />
                <Chatter t-if="chatterRecord.data and documentService.userIsInternal"
                    has_activities="!chatterRecord.data.res_model || chatterRecord.data.res_model === 'documents.document'"
                    threadId="typeof chatterRecord.data.id === 'number' and chatterRecord.data.id"
                    threadModel="'documents.document'"
                    compactHeight="true"
                    isChatterAside="!isMobile"/>
            </div>
        </xpath>
        <xpath expr="//div[hasclass('o_kanban_renderer')]" position="inside">
            <DocumentsDropZone parentRoot="root"/>
            <DocumentsFileViewer parentRoot="root" t-props="getDocumentsAttachmentViewerProps()"/>
        </xpath>
        <t t-foreach="getGroupsOrRecords()" position="attributes">
            <attribute name="t-if" add="!this.isRecentFolder" separator="and"/>
        </t>
        <t t-foreach="getGroupsOrRecords()" position="before">
            <div t-if="this.isRecentFolder" class="w-100">
                <!-- RECENT view -->
                <div t-foreach="getGroupsOrRecords()" t-as="group" t-key="group.key"
                    class="d-flex flex-row flex-wrap">
                    <h3 class="o_documents_title m-2 border-bottom w-100" t-out="group.group.displayName"/>
                    <t t-set="folders" t-value="group.group.list.records.filter((d) => d.data.type === 'folder')"/>
                    <t t-set="files" t-value="group.group.list.records.filter((d) => d.data.type !== 'folder')"/>
                    <div t-foreach="folders" t-as="record" t-key="record.id">
                        <t t-set="group" t-as="group.group"/>
                        <KanbanRecord
                            archInfo="props.archInfo"
                            list="props.list"
                            record="record"
                            templates="props.archInfo.templateDocs"
                        />
                    </div>
                    <div class="w-100"/>
                    <div t-foreach="files" t-as="record" t-key="record.id">
                        <t t-set="group" t-as="group.group"/>
                        <KanbanRecord
                            archInfo="props.archInfo"
                            list="props.list"
                            record="record"
                            templates="props.archInfo.templateDocs"
                        />
                    </div>
                </div>
            </div>
            <t t-if="hasFolders() and !this.props.list.isGrouped">
                <h3 class="o_documents_title m-2 w-100 fs-4">Folders</h3>
                <t t-foreach="getFolderRecords()" t-as="folderRecord" t-key="folderRecord.key">
                    <KanbanRecord
                        archInfo="props.archInfo"
                        list="props.list"
                        record="folderRecord.record"
                        templates="props.archInfo.templateDocs"
                    />
                </t>
                <!-- kanban ghost cards are used to properly space last elements. -->
                <div t-foreach="[,,,,,,]" t-as="i" t-key="i_index" class="o_kanban_record o_kanban_ghost flex-grow-1 flex-md-shrink-1 flex-shrink-0" />
                <h3 t-if="hasFiles()" class="o_documents_title m-2 w-100 fs-4">Files</h3>
            </t>
            <FileUploadProgressContainer fileUploads="this.documentUploads" Component="constructor.components.FileUploadProgressKanbanRecord" shouldDisplay="(upload) => !upload.data.get('document_id')"/>
        </t>
        <t t-call="web.ActionHelper" position="replace">
            <t t-if="props.noContentHelp">
                <DocumentsActionHelper noContentHelp="props.noContentHelp"/>
            </t>
        </t>
    </t>
</templates>
