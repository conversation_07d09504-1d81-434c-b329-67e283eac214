<?xml version="1.0" encoding="UTF-8"?>
<templates>
    <t t-name="documents.DocumentsDropZone">
        <t t-if="state.dragOver">
            <div class="h-100 w-100 start-0 position-absolute"
                t-att-class="{'o_documents_drop_over_zone': canDrop, 'o_documents_drop_over_unauthorized_zone': !canDrop}"
                t-attf-style="top: {{state.topOffset}}px !important; z-index: 10;"
                t-on-drop.prevent="onDrop">
                <div class="h-100 w-100 position-sticky">
                    <div t-if="canDrop" class="o_documents_upload_text text-center text-white">
                        <i class="d-block fa fa-upload fa-9x mb-4"/>
                        <span>Drop files here to upload</span>
                    </div>
                    <div t-else="" class="o_documents_upload_text text-center text-danger">
                        <i class="d-block fa fa-times-circle fa-9x mb-4"/>
                        <span>You must be in a specific writable workspace to upload files</span>
                    </div>
                </div>
            </div>
        </t>
    </t>
</templates>
