<?xml version="1.0" encoding="UTF-8"?>
<templates>
    <t t-name="documents.DocumentsActionHelper">
        <div class="o_view_nocontent">
            <div class="o_nocontent_help">
                <t t-out="noContentHelp"/>
                <t t-if="selectedFolderId and !['MY', 'RECENT', 'SHARED', 'TRASH', 'COMPANY'].includes(selectedFolderId)">
                    <div t-if="state.mailTo">
                        <p class="fw-normal">Or send emails to
                            <a t-attf-href="mailto:{{state.mailTo}}">
                                <t t-esc="state.mailTo"/>
                            </a>
                        </p>
                        <p class="fw-normal"><em>Tip: configure your scanner to send all documents to this address.</em></p>
                    </div>
                    <p t-else="">No alias configured</p>
                </t>
            </div>
        </div>
    </t>
</templates>
