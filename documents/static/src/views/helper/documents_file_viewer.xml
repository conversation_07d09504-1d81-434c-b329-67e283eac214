<?xml version="1.0" encoding="UTF-8"?>
<templates>
    <t t-name="documents.DocumentsFileViewer">
        <div t-if="props.previewStore.attachments" t-ref="root"
            t-attf-style="top: {{state.topOffset}}px !important; left: {{state.leftOffset}}px !important; z-index: 990; background-color: rgba(0, 0, 0, 0.7);"
            class="position-absolute w-100 h-100 start-0 d-flex justify-content-center align-items-center"
            t-on-keydown="onGlobalKeydown"
        >
            <FileViewer
                files="props.previewStore.attachments"
                startIndex="props.previewStore.startIndex"
                modal="false"
            />
        </div>
    </t>
</templates>
