<?xml version="1.0" encoding="utf-8"?>
<templates xml:space="preserve">

    <t t-inherit="documents.PermissionPanel" t-inherit-mode="extension">
        <xpath expr="//CopyButton" position="attributes">
            <attribute name="disabled" add="(state.access.user_permission === 'view' and state.access.access_via_link === 'none')" separator=" or"/>
        </xpath>
        <xpath expr="//section[hasclass('o_documents_permission_panel_member_invite')]" position="replace"/>
        <xpath expr="//section[hasclass('o_documents_permission_panel_access_partners')]" position="replace"/>
        <xpath expr="//DocumentsAccessSettings" position="attributes">
            <attribute name="disabled">state.loading || state.access.user_permission !== 'edit'</attribute>
        </xpath>
    </t>

</templates>
