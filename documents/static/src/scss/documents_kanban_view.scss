
////// ======  Dashboard Kanban View ==========
////// ========================================

.o_documents_drag_icon {
    padding: 0px 3px;
    color: white;
    font-weight: bold;
    background-color: $o-brand-primary;
    border: 1px solid $o-brand-primary;
    border-radius: 3px;
    @include o-position-absolute($top: 0, $right: 0);
    z-index: -9999;
}

.o_documents_kanban_view  .o_content {
    .o_kanban_renderer {
        --KanbanRecord-padding-v: 0;
        --KanbanRecord-padding-h: 0;

        overflow: auto;
        transition: background 0.3s;
        height: 100%;
        width: 100%;

        .o_kanban_record {
            display: flex;
            flex-direction: column;
            width: 260px;
            margin: .5rem;
            border: none;

            @include media-breakpoint-down(lg) {
                width: 160px;
                flex-basis: auto;
            }
            .o_kanban_progress_card {
                height: 100%;
            }

            > div:nth-of-type(1) {
                border: $border-width solid var(--border-color);
            }

            &.o_drag_hover {
                & > div {
                    --border-color: #{$o-success};

                    background-color: mix($o-success, $o-view-background-color, 10%);
                }
            }

            &.o_drag_invalid {
                & > div {
                    --border-color: #{$o-danger} !important;

                    background-color: mix($o-danger, $o-view-background-color, 10%) !important;
                }
            }

            &:not(.o_folder_record):not(:has(.o_documents_pre_18_1)) {
                min-height: 200px;
            }

            &.o_kanban_ghost {
                min-height: 0 !important;
            }

            &:hover .o-file-upload-progress-bar-abort {
                display: inline-block;
                z-index: 5;
            }

            .o_kanban_stack {
                width: calc(100% - 6px);
                border: $border-width solid $border-color;
                height: 100%;
                background-color: $o-view-background-color;
                &:nth-of-type(1){
                    z-index: $o-dms-kanban-stack-zindex;
                }
                &:nth-of-type(2) {
                    position: absolute;
                    z-index: $o-dms-kanban-stack-zindex - 1;
                    transform: translate(3px, 3px);
                }
                &:nth-of-type(3) {
                    position: absolute;
                    z-index: $o-dms-kanban-stack-zindex - 2;
                    transform: translate(6px, 6px);
                }
            }

            &:not(:has(.o_documents_pre_18_1)) .o_kanban_record_body {
                max-height: 1rem;
                line-height: 1rem;
            }

            .o_documents_in_folder {
                font-size: 13px;
            }
        }

        .o_kanban_record .o_kanban_attachment {
            &.o_record_selected, &.o_record_selected ~ div  {
                --border-color: #{$o-component-active-border};

                opacity: 1;
                background-color: $o-component-active-bg;
            }

            &.oe_file_request {
                border-style: dashed;
                background-color: #fff0;
            }

            .o_kanban_image_wrapper {
                background-color: $body-bg;
                height: 140px;
                width: 100%;
                display: flex;
                align-items: center;
                justify-content: space-around;
                .o_image.o_image_thumbnail {
                    height: 70px;
                    width: 70px;
                }

                .o_documents_image {
                    height: 100%;
                }

                img {
                    max-width: 100%;
                    max-height: 100%;
                }

                .fa-star,.fa-star-o {
                    &:before {
                        position: relative;
                        z-index: 1;
                    }
                    &:after {
                        position: absolute;
                        content: "\f005";
                        color: white;
                        z-index: 0;
                        right: 8px;
                    }
                }
            }

            .o_request_image .o_kanban_image_wrapper {
                background-color: #f1f1f1;
                opacity: 0.5;

                &:hover {
                    background-color: $gray-200;
                }
            }

            .oe_kanban_previewer {
                cursor: zoom-in;
            }

            .o_kanban_record_title {
                margin-bottom: 0;
            }

            .o_record_selector {
                @include o-hover-text-color($gray-400, $gray-800 );
                @include o-position-absolute($o-kanban-inside-vgutter - 4px , $o-kanban-inside-hgutter - 4px);
                padding: $o-dms-p-tiny;
                font-size: 16px;
                text-shadow: 0 0 2px black;
                z-index: 4;
                transform: translate(-6px);
            }

            @include media-breakpoint-up(lg) {
                &.o_documents_pre_18_1:not(:hover) {
                    .fa-eye-slash,.fa-star-o,.o_field_kanban_activity:has(.fa-clock-o) {
                        visibility: hidden;
                    }
                }
            }

            .fa-star-o {
                &:hover:before {
                    content: "\f005";
                }
            }

            .fa-star {
                &:hover:before {
                    content: "\f006";
                }
            }

            .o_field_kanban_activity {
                margin-top: 2px;
            }
        }

        .o_kanban_record:not(.o_kanban_ghost){
            &.o_record_selected, &.o_record_selected ~ .o_kanban_stack {
                opacity: 1;
                border-color: $o-component-active-border;
                background-color: $o-component-active-bg;
            }
        }

        .o_record_selected {
            .o_record_selector{
                @include o-hover-opacity(0.8, 1);

                &:before{
                    color: var(--KanbanColumn__highlight-background);
                    content:"\f058";
                    padding: 0px 1px;
                    background-color: var(--KanbanColumn__highlight-border);
                    border-radius: 50%;
                }
            }
        }

        &.o_documents_drop_over {
            background: $gray-700;

            .o_kanban_record {
                opacity: 0.2;
                filter: blur(1px);
            }

            .o_view_nocontent {
                display: none;
            }
        }

        &.o_documents_drop_over_unauthorized {
            @extend .o_documents_drop_over;
            background: $red-100;
        }
    }
}


////// ==========  Share modal ==============
////// ======================================

.o_url_group {
    .o_field_copy {
        padding: $input-btn-padding-y-lg $input-btn-padding-x-lg;
        color: $text-muted;
        text-align: left;
        font-style: italic;
        border-color: $gray-500;

        .o_clipboard_button {
            box-shadow: 1px 0 0 1px $o-brand-primary;
            border-radius: inherit; // To always get the container border radius;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }
    }
}


////// ==========-  Animations ==============
////// ======================================

@keyframes o_documents_preview_in {
   0% {
       opacity: 0;
       transform: scaleX(0.50) scaleY(0.50);
   }

   100% {
       opacity: 1;
       transform: scaleX(1.00) scaleY(1.00);
   }
}

////// =============== Buttons ===============
////// =======================================

.o_documents_main_buttons .fa {
    width: 14px !important;
}

////// =============== Sample Data ===============
////// ===========================================

.o_view_sample_data {

    h3.o_documents_title {
        @include o-sample-data-disabled;
    }

}
