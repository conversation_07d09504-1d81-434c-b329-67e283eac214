////// ========== Search Panel ==============
////// ======================================
.o_search_panel > .o_search_panel_section {
    .o_drag_over_selector {
        &.o_search_panel_filter_value, > header {
            background-color: $component-active-bg;
            font-weight: $font-weight-bold;
            color: $gray-900;

            .o_search_panel_label_title {
                padding-left: map-get($spacers, 2);
            }

            /* Firefox firing dragleave when dragging over text
            https://bugzilla.mozilla.org/show_bug.cgi?id=1478562 */
            .o_search_panel_label {
                pointer-events: none;
            }
        }

    }
}

.o_documents_kanban_view .o_control_panel {
    @include media-breakpoint-down(md) {
        padding-bottom: 0 !important;
    }
}

.o_documents_kanban_view .o_control_panel_actions,
.o_documents_list_view .o_control_panel_actions {
    justify-content: center !important;

    @include media-breakpoint-down(md) {
        padding-top: 8px;
    }
}

.o_documents_action_dropdown button {
    width: max-content;
}

////// =========== Drag & Drop ==============
////// ======================================

.o_documents_drop_over_zone {

    &:after {
        content: "";
        @include o-position-absolute(4px, 6px, 8px, 6px);
        border: 2px dashed white;
    }

    .o_documents_upload_text {
        @include o-position-absolute($top: 50%, $left: 50%);
        transform: translate(-50%, -50%);
        pointer-events: none;

        span {
            font-size: 20px;
        }
    }
}

.o_documents_drop_over_unauthorized_zone {
    @extend .o_documents_drop_over_zone;
    &:after {
        border: 2px dashed map-get($o-theme-text-colors, 'danger');
    }
}

.o_documents_drop_over {
    align-items: flex-start;
    overflow: auto;
    transition: background 0.3s;
    height: 100%;
    background: $gray-700;

    table {
        opacity: 0.2;
        filter: blur(1px);
    }

    .o_view_nocontent {
        display: none;
    }
}

.o_documents_drop_over_unauthorized {
    @extend .o_documents_drop_over;
    background: $red-100;
}

////// ========== Progress Bars =============
////// ======================================


.o_documents_list_view .o_documents_content .o_documents_view.o_list_renderer {
    height: 100%;

    .o_documents_locked {
        margin-left: 5px;
    }
}

.o_documents_content {

    .o_kanban_record:not(.o_kanban_ghost), .o_inspector_tag_remove, .o_inspector_model, .o_inspector_open_chatter {
        cursor: pointer;
        user-select: none;
    }

    .o_documents_tag_color {
        color: $o-dms-color-tag;
    }

    .o_documents_view {
        .o_file_upload_progress_bar {
            .o_upload_cross {
                padding: $o-dms-p-tiny;
            }
        }
    }

    .o_field_widget.o_required_modifier{
        font-weight: bold;
    }
}

.o_form_view .o_conf_mail {
    color: $link-color;
}


////// =========== Permission Panel ==============
////// ===========================================

.o_documents_permission_panel_select_invite {
    max-height: 5rem;
    max-width: 32.8rem;
    overflow-y: auto;
}

.o_documents_permission_panel_avatar {
    --Avatar-size: 2.7145rem;
}

.o_documents_permission_panel_select_menu_overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
}

.o_documents_permission_panel_access_partners {
    max-height: 11.5rem;
    overflow-y: auto;
}

@media (hover: hover) { // Devices supporting hover
    .o_documents_permission_panel_access_partners {
        .o_documents_remove_partner_btn, .o_documents_expiration_date_btn {
            visibility: hidden;
        }
        .o_documents_access_partner:hover {
            .o_documents_remove_partner_btn, .o_documents_expiration_date_btn {
                visibility: visible;
            }
            .o_documents_unset_expiration_date, .o_documents_set_expiration_date  {
                cursor: pointer;
            }
        }
    }
}

////// ========== Chatter ===================
////// ======================================

.o_document_chatter_container {
    border-left: 1px solid var(--border-color);
    .o-mail-Chatter-topbar {
        padding-top: 0.25rem;
        @include media-breakpoint-up(lg) {
            &:not(:hover) {
                overflow-x: hidden !important;
            }
        }

    }
    &.o-mail-ChatterContainer.o-aside {
        width: calc(#{$o-mail-Chatter-minWidth} + 20px + var(--Chatter-asideExtraWidth));
    }
}
