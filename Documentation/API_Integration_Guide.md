# API Integration Guide

## Overview

This guide explains how to integrate and manage API endpoints in the mobile app template. The system is designed to allow easy switching between different API environments with a single configuration change.

## Architecture

### Core Components

1. **APIConfiguration.swift** - Central configuration management
2. **APIEndpoints.swift** - Endpoint definitions based on OpenAPI spec
3. **APIClient.swift** - Enhanced HTTP client
4. **EarnBaseAPIService.swift** - High-level API service layer
5. **API Models** - Type-safe request/response models

### Key Features

- ✅ Single point of endpoint configuration
- ✅ Environment switching (Development, Staging, Production, Custom)
- ✅ Type-safe API models based on OpenAPI specification
- ✅ Automatic retry logic and error handling
- ✅ Accept-Language header support
- ✅ Real-time configuration updates
- ✅ UI for environment switching

## Quick Start

### 1. Changing API Endpoint

To change the API endpoint for the entire app, you only need to update one place:

```swift
// Option 1: Use predefined environments
APIConfiguration.shared.setupForDevelopment()
APIConfiguration.shared.setupForStaging()
APIConfiguration.shared.setupForProduction()

// Option 2: Use custom URL
APIConfiguration.shared.setupCustom(baseURL: "https://your-api.com/api/v1")

// Option 3: Direct environment change
APIConfiguration.shared.setEnvironment(.production)
```

### 2. Using the API Service

```swift
// Get API service instance
let apiService = EarnBaseAPIService.shared

// Example: Sign in
do {
    let response = try await apiService.signIn(
        login: "<EMAIL>",
        password: "password123"
    )
    print("Access token: \(response.accessToken)")
} catch {
    print("Sign in failed: \(error)")
}

// Example: Get public events
do {
    let events = try await apiService.getPublicEvents()
    print("Found \(events.events.count) events")
} catch {
    print("Failed to fetch events: \(error)")
}
```

### 3. Environment Switching UI

Add the environment switcher to your settings:

```swift
import SwiftUI

struct SettingsView: View {
    var body: some View {
        List {
            Section("API Configuration") {
                APIEnvironmentSwitcherView.settingsRow()
            }
        }
    }
}
```

## API Endpoints

### Core System
- `GET /` - API information
- `GET /health` - Health check
- `GET /modules` - Available modules

### Authentication
- `POST /auth/sign-in` - User sign in
- `POST /auth/register` - User registration
- `GET /auth/me` - Current user info
- `POST /auth/refresh` - Refresh token
- `POST /auth/sign-out` - Sign out
- `POST /auth/forgot-password` - Password reset request
- `POST /auth/reset-password` - Reset password
- `POST /auth/verify-email` - Email verification
- `POST /auth/change-password` - Change password

### Public APIs
- `GET /public/events` - List public events
- `GET /public/events/{id}` - Event details
- `POST /public/events/{id}/registrations` - Register for event
- `GET /public/courses` - List public courses
- `GET /public/courses/{id}` - Course details
- `GET /public/classes` - List public classes
- `GET /public/locations` - List locations

### Files
- `POST /files/` - Upload file
- `GET /files/{token}` - Get file by token

### Chatbot
- `POST /public/chatbot/sessions` - Create chat session
- `GET /public/chatbot/sessions/{id}` - Get session
- `POST /public/chatbot/sessions/{id}/messages` - Send message

## Configuration Options

### Environment Types

```swift
enum APIEnvironment {
    case development    // https://lms-dev.ebill.vn/api/v1/
    case staging       // https://lms-dev.ebill.vn/api/v1/
    case production    // https://lms-dev.ebill.vn/api/v1/
    case custom        // User-defined URL
}
```

### Default Headers

The system automatically adds these headers to all requests:

```swift
"Content-Type": "application/json"
"Accept": "application/json"
"X-Platform": "iOS"
"X-App-Version": "1.0.0"
"Accept-Language": "en" // Based on device locale
```

### Retry Configuration

```swift
// Default retry settings
timeout: 30.0 seconds
retryAttempts: 3
retryDelay: 1.0 seconds
```

## API Models

### Authentication Models

```swift
// Sign in request
struct UnifiedLoginRequest: Codable {
    let login: String        // Username or email
    let password: String
    let deviceInfo: DeviceInfo?
}

// Sign in response
struct EnhancedTokenResponse: Codable {
    let accessToken: String
    let refreshToken: String?
    let tokenType: String
    let expiresIn: Int
    let user: UserInfo
}
```

### Public API Models

```swift
// Event model
struct PublicEvent: Codable {
    let id: Int
    let title: String
    let description: String?
    let startDate: String
    let endDate: String?
    let location: String?
    let maxParticipants: Int?
    let currentParticipants: Int
}

// Course model
struct PublicCourse: Codable {
    let id: Int
    let title: String
    let description: String?
    let duration: String?
    let level: String?
    let price: Double?
    let instructor: CourseInstructor?
}
```

## Error Handling

### Network Errors

```swift
enum NetworkError: Error {
    case invalidURL
    case noData
    case decodingError
    case serverError(Int)
    case unauthorized
    case networkUnavailable
}
```

### API Errors

```swift
struct HTTPValidationError: Codable {
    let detail: [ValidationErrorDetail]?
}

struct ValidationErrorDetail: Codable {
    let loc: [String]
    let msg: String
    let type: String
}
```

## Best Practices

### 1. Environment Management

- Use Development environment during development
- Use Staging for testing with real-like data
- Use Production for release builds
- Use Custom for testing with local servers

### 2. Error Handling

```swift
do {
    let result = try await apiService.getPublicEvents()
    // Handle success
} catch NetworkError.unauthorized {
    // Handle authentication error
} catch NetworkError.serverError(let code) {
    // Handle server errors
} catch {
    // Handle other errors
}
```

### 3. Configuration Updates

Listen for configuration changes:

```swift
NotificationCenter.default.publisher(for: .apiConfigurationChanged)
    .sink { _ in
        // Handle configuration change
        // Refresh data, update UI, etc.
    }
    .store(in: &cancellables)
```

### 4. Testing

```swift
// Test connection
Task {
    do {
        let status = try await EarnBaseAPIService.shared.healthCheck()
        print("API is healthy: \(status.success)")
    } catch {
        print("API health check failed: \(error)")
    }
}
```

## Troubleshooting

### Common Issues

1. **Invalid URL Error**
   - Check if the base URL is properly formatted
   - Ensure it includes protocol (https://)

2. **Network Timeout**
   - Check internet connection
   - Verify server is accessible
   - Consider increasing timeout value

3. **Authentication Errors**
   - Verify token is valid and not expired
   - Check if endpoint requires authentication

4. **Parsing Errors**
   - Verify API response matches expected model
   - Check for missing or extra fields

### Debug Information

```swift
// Get debug info
let debugInfo = APIConfiguration.shared.debugInfo
print("Current configuration: \(debugInfo)")

// Validate configuration
let isValid = APIConfiguration.shared.validateConfiguration()
print("Configuration is valid: \(isValid)")
```

## Migration Guide

### From Old System

1. Replace direct endpoint strings with `APIEndpoints` constants
2. Update API calls to use `EarnBaseAPIService`
3. Replace hardcoded base URLs with `APIConfiguration`
4. Update models to match OpenAPI specification

### Example Migration

```swift
// Old way
let url = "https://api.example.com/auth/login"

// New way
let url = APIConfiguration.shared.buildURL(endpoint: APIEndpoints.Auth.signIn)
```

## Support

For questions or issues:
1. Check this documentation
2. Review the OpenAPI specification
3. Test with the API environment switcher
4. Check debug information and logs
