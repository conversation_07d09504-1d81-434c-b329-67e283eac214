# Instructor Login API Integration

## Overview

This document explains the integration of the LMS instructor login API into the mobile app. The system ensures that only users with instructor roles can access the application.

## API Endpoint

```
POST https://lms-dev.ebill.vn/api/v1/auth/sign-in
```

## Request Format

```json
{
  "username": "admin",
  "password": "earnbase@2025",
  "device_info": {
    "device_id": "test-device-001",
    "device_name": "iPhone 12 Pro Max",
    "device_type": "mobile",
    "os_name": "iOS",
    "os_version": "15.7",
    "app_version": "1.0.0",
    "browser_name": "Safari",
    "browser_version": "16.6",
    "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_7 like Mac OS X) AppleWebKit/605.1.15",
    "ip_address": "*************",
    "location": {
      "city": "Hanoi",
      "country": "Vietnam",
      "latitude": 21.0285,
      "longitude": 105.8542
    }
  },
  "remember_me": false,
  "mfa_code": "123456"
}
```

## Implementation

### 1. Models Created

#### InstructorAuthModels.swift
- `InstructorLoginRequest` - Request model with device info
- `InstructorLoginResponse` - Response model with user data
- `InstructorUser` - User model with instructor-specific fields
- `JWTHelper` - Helper for JWT token validation and role checking

#### Key Features:
- ✅ Device information collection
- ✅ JWT token role validation
- ✅ Instructor role verification
- ✅ Type-safe models

### 2. Service Layer

#### InstructorAuthService.swift
- Handles instructor authentication
- Validates instructor roles from JWT token
- Manages authentication state
- Integrates with existing app architecture

#### Key Methods:
```swift
// Login with instructor credentials
func login(username: String, password: String, rememberMe: Bool) async throws

// Logout and clear data
func logout() async

// Check if user has instructor role
var isInstructor: Bool { get }
```

### 3. Integration Points

#### AuthViewModel Updates
- Modified to use `InstructorAuthService`
- Changed email field to username
- Added instructor role validation
- Maintains existing UI compatibility

#### AuthStateManager Updates
- Integrated with `InstructorAuthService`
- Observes instructor auth changes
- Converts instructor data to app user model
- Maintains backward compatibility

## Usage

### 1. Login Flow

```swift
// In AuthViewModel
func login() async {
    try await InstructorAuthService.shared.login(
        username: loginEmail, // Using email field as username
        password: loginPassword,
        rememberMe: rememberMe
    )
    
    // Update auth state with instructor data
    if let instructor = InstructorAuthService.shared.currentInstructor {
        let convertedUser = convertInstructorToUser(instructor)
        await authStateManager.updateTokenAndUser(
            token: TokenManager.shared.getToken() ?? "",
            user: convertedUser
        )
    }
}
```

### 2. Role Validation

```swift
// Automatic role validation in login
guard loginData.user.isInstructor else {
    throw InstructorAuthError(
        code: "ACCESS_DENIED", 
        message: "Access denied. Only instructors can access this application."
    )
}
```

### 3. JWT Token Validation

```swift
// Check if token has instructor role
let hasInstructorRole = JWTHelper.hasInstructorRole(token: token)

// Extract roles from token
let roles = JWTHelper.extractRoles(from: token)

// Check if token is expired
let isExpired = JWTHelper.isTokenExpired(token)
```

## Security Features

### 1. Role-Based Access Control
- Only users with instructor roles can login
- JWT token validation for role verification
- Automatic logout if role is invalid

### 2. Token Management
- Secure token storage in Keychain
- Automatic token refresh
- Token expiration handling

### 3. Device Information
- Comprehensive device tracking
- Location information (optional)
- User agent and platform details

## Configuration

### 1. API Endpoint Configuration

```swift
// Set LMS API endpoint
APIConfiguration.shared.setupCustom(baseURL: "https://lms-dev.ebill.vn/api/v1")
```

### 2. Default Credentials (Development)

```swift
// In LoginView.swift
private func setupDemoCredentials() {
    authViewModel.loginEmail = "admin"
    authViewModel.loginPassword = "earnbase@2025"
}
```

## Testing

### 1. Test View Available
- `InstructorLoginTestView.swift` - Comprehensive test interface
- Real-time status monitoring
- API connection testing
- Login/logout testing

### 2. Test Credentials
- **Username**: `admin`
- **Password**: `earnbase@2025`

### 3. Test Steps
1. Open `InstructorLoginTestView`
2. Verify API endpoint is correct
3. Test API connection
4. Test login with credentials
5. Verify instructor role validation
6. Test logout functionality

## Error Handling

### 1. Common Errors

```swift
// Access denied for non-instructors
InstructorAuthError(
    code: "ACCESS_DENIED",
    message: "Access denied. Only instructors can access this application."
)

// Invalid credentials
InstructorAuthError(
    code: "LOGIN_FAILED", 
    message: "Invalid username or password"
)

// Network errors
NetworkError.serverError(statusCode)
NetworkError.networkUnavailable
```

### 2. Error Display
- User-friendly error messages
- Automatic error handling in UI
- Detailed logging for debugging

## Navigation Flow

### 1. Successful Login
```
LoginView → (Authentication Success) → HomeView
```

### 2. Failed Login
```
LoginView → (Show Error Alert) → LoginView
```

### 3. Role Validation Failure
```
LoginView → (Access Denied Error) → LoginView
```

## Monitoring and Debugging

### 1. Console Logging
```
🔐 AuthViewModel: login() called with username: admin
🔐 Attempting instructor login for: admin
✅ Instructor login successful: Admin User
✅ Instructor authenticated: Admin User
```

### 2. Authentication State
- Real-time authentication status
- Current instructor information
- Role and permission details
- Token validation status

## Future Enhancements

### 1. Multi-Factor Authentication (MFA)
- Support for MFA codes
- SMS/Email verification
- Biometric authentication

### 2. Enhanced Security
- Certificate pinning
- Request signing
- Advanced token validation

### 3. Offline Support
- Cached authentication
- Offline mode detection
- Sync when online

## Troubleshooting

### 1. Login Fails
- Check API endpoint configuration
- Verify credentials are correct
- Check network connectivity
- Review server logs

### 2. Role Validation Fails
- Verify JWT token contains instructor role
- Check role name mapping
- Validate token structure

### 3. Navigation Issues
- Ensure AuthStateManager is updated
- Check authentication state binding
- Verify navigation logic

## Support

For issues or questions:
1. Check console logs for detailed error messages
2. Use `InstructorLoginTestView` for debugging
3. Verify API endpoint and credentials
4. Review JWT token structure and roles
