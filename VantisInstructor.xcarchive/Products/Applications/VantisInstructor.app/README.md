# Icons Directory

<PERSON><PERSON><PERSON> mục này chứa các icon đư<PERSON><PERSON> sử dụng trong UI của Vantis Instructor app.

## 📁 Cấu trúc thư mục

```
Icons/
├── README.md           # File hướng dẫn này
├── app-icons/          # Icon ứng dụng (App Icon)
├── tab-icons/          # Icon cho tab bar
├── navigation-icons/   # Icon cho navigation bar
├── button-icons/       # Icon cho các button
├── status-icons/       # Icon trạng thái (success, error, warning)
└── feature-icons/      # Icon cho các tính năng cụ thể
```

## 🎨 Quy tắc đặt tên

- Sử dụng kebab-case: `icon-name.png`
- <PERSON><PERSON> gồm kích thước: `<EMAIL>`, `<EMAIL>`
- Mô tả rõ ràng: `home-tab-icon.png`, `settings-button.png`

## 📐 Kích thướ<PERSON> khu<PERSON>ến nghị

### Tab Bar Icons
- @1x: 25x25pt
- @2x: 50x50px  
- @3x: 75x75px

### Navigation Bar Icons
- @1x: 22x22pt
- @2x: 44x44px
- @3x: 66x66px

### Button Icons
- Small: 16x16pt
- Medium: 24x24pt
- Large: 32x32pt

## 🔄 Cách sử dụng

1. **Thêm vào Assets.xcassets**: Copy icon vào Assets.xcassets để Xcode tự động quản lý
2. **Sử dụng trong code**:
   ```swift
   Image("icon-name")
   Image(systemName: "icon-name") // Cho SF Symbols
   ```

## 📝 Lưu ý

- Ưu tiên sử dụng SF Symbols khi có thể
- Đảm bảo icon có độ tương phản tốt
- Test icon trên cả Light và Dark mode
- Sử dụng format PNG hoặc PDF (vector)
