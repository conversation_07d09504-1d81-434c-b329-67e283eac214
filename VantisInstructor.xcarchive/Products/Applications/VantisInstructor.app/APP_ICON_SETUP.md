# App Icon Setup Guide

## ✅ Đã hoàn thành

<PERSON> `logo-vantis-navicon.png` đã được thiết lập làm App Icon cho Vantis Instructor app.

## 📁 Vị trí file

- **Source**: `VantisInstructor/Resources/Icons/app-icons/logo-vantis-navicon.png`
- **Assets**: `VantisInstructor/Assets.xcassets/AppIcon.appiconset/logo-vantis-navicon.png`

## 🎯 Cấu hình hiện tại

App Icon đã được cấu hình cho:
- ✅ Light mode (1024x1024)
- ✅ Dark mode (1024x1024) 
- ✅ Tinted mode (1024x1024)

## 🔧 Tối ưu hóa (<PERSON><PERSON><PERSON> chọn)

Để có chất lượng tốt nhất, bạn có thể tạo các kích thước khác nhau:

### Kích thước khu<PERSON>ến nghị:
- **1024x1024px** - App Store (đã có)
- **180x180px** - iPhone @3x
- **120x120px** - iPhone @2x
- **167x167px** - iPad Pro @2x
- **152x152px** - iPad @2x
- **76x76px** - iPad @1x

### Cách tạo các kích thước:
1. Sử dụng tool như Sketch, Figma, hoặc online resizer
2. Đặt tên: `<EMAIL>`, `<EMAIL>`
3. Thêm vào AppIcon.appiconset

## 📱 Kiểm tra

1. Mở Xcode
2. Chọn `VantisInstructor.xcodeproj`
3. Vào `Assets.xcassets` → `AppIcon`
4. Kiểm tra logo hiển thị đúng
5. Build và test trên simulator/device

## 🎨 Lưu ý thiết kế

- Icon nên có background trong suốt hoặc solid
- Tránh text quá nhỏ
- Đảm bảo nhìn rõ ở kích thước nhỏ
- Test trên cả Light và Dark mode
