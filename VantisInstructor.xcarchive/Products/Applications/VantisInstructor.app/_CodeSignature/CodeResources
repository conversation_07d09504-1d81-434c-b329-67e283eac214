<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>APP_ICON_SETUP.md</key>
		<data>
		uQZLIPGAZaJAaGBrWrT0UvK2g5g=
		</data>
		<key><EMAIL></key>
		<data>
		Ohj2lDi8jjBEwrJ8u22sWgO+ddQ=
		</data>
		<key>AppIcon76x76@2x~ipad.png</key>
		<data>
		mnGASjq5QS3GgWBDoLFjX5gcm6Q=
		</data>
		<key>Assets.car</key>
		<data>
		v2McTjfzh7X4gBMslQ+F8Qf6Sfs=
		</data>
		<key>AuthenticationTestPlan.md</key>
		<data>
		VaIeIorbQ4j1kFT/sh8SaGEtG/w=
		</data>
		<key>BeVietnamPro-Bold.ttf</key>
		<data>
		g4kvGOrnk+tx9yQIxpmlmjNETB4=
		</data>
		<key>BeVietnamPro-Medium.ttf</key>
		<data>
		jUoNBDmIXFyGGKG/2kXM5iUI6po=
		</data>
		<key>BeVietnamPro-Regular.ttf</key>
		<data>
		5PoUzdPWYrY3Pm6DFYMaRiHfZao=
		</data>
		<key>BeVietnamPro-SemiBold.ttf</key>
		<data>
		ppUnx+XPEGay7mvad1icjec2+Us=
		</data>
		<key>CHECKIN_FEATURE.md</key>
		<data>
		Iwjk4Yny5yLiTebBSnI7x8nyGqc=
		</data>
		<key>Info.plist</key>
		<data>
		OhuSgdqxvLY44rN/oHLQko343cY=
		</data>
		<key>PkgInfo</key>
		<data>
		n57qDP4tZfLD1rCS43W0B4LQjzE=
		</data>
		<key>QuizManagementAPIDebug.md</key>
		<data>
		2PYw74sJg8Od2XnKRDLYH0OKvT0=
		</data>
		<key>README.md</key>
		<data>
		oYSekBmlkz2n7XryqZzkW5xVywk=
		</data>
		<key>capture_quiz_logs.sh</key>
		<data>
		rOMTKPFxs+56VWKb/Z2j6RO7d6E=
		</data>
		<key>embedded.mobileprovision</key>
		<data>
		OFZ07X9LxmSaf03+sEbqOJozgAM=
		</data>
		<key>logo-vantis-navicon.png</key>
		<data>
		IJgb7mySW4ZF4Ku0QgOfUddIy34=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>APP_ICON_SETUP.md</key>
		<dict>
			<key>hash2</key>
			<data>
			066LrZx7ZZ2RbyCk8ACI5/CQvffVzHkTfzDyfTctlos=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			zwqrR8zDga7wKDcGuqJRBrPMNqoCDnu+cP0fmhEesbE=
			</data>
		</dict>
		<key>AppIcon76x76@2x~ipad.png</key>
		<dict>
			<key>hash2</key>
			<data>
			+bKE2Qs+a2lXHLV21UkqOndy2w9Pk+9F70mzm4vfqsA=
			</data>
		</dict>
		<key>Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			BCxa8dtSjy087BfeLQF60EWu1kJhIG4h0ETfj1wCfg0=
			</data>
		</dict>
		<key>AuthenticationTestPlan.md</key>
		<dict>
			<key>hash2</key>
			<data>
			bsEA7qxoY+y844Ks8jYZfhTJbPOKjg/R15TzzfTGiP4=
			</data>
		</dict>
		<key>BeVietnamPro-Bold.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			f3OP5cQ8iHKAeyDi0w1CFjYY3opNr39IqTmtrDLBaEc=
			</data>
		</dict>
		<key>BeVietnamPro-Medium.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			tggyv6D80BUVgRLGTX4/2tOwxih9GCP4WjEDY26EUmg=
			</data>
		</dict>
		<key>BeVietnamPro-Regular.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			zR726dfbKK1c24imXMvmk4cOYNNAt5HzSdJINCtP5MM=
			</data>
		</dict>
		<key>BeVietnamPro-SemiBold.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			vY4n6wJyC52R5Z5PEKkIeGQyGfJc5qjZpPBqiojTu3E=
			</data>
		</dict>
		<key>CHECKIN_FEATURE.md</key>
		<dict>
			<key>hash2</key>
			<data>
			ybgl+Fm7STZv7HtbJ4q+Ge7Qj2LyXgWA2B/ia8yI48o=
			</data>
		</dict>
		<key>QuizManagementAPIDebug.md</key>
		<dict>
			<key>hash2</key>
			<data>
			XavXiS9mGlghVggrEKzAKPRd/JFif4a05QdvoksjzhY=
			</data>
		</dict>
		<key>README.md</key>
		<dict>
			<key>hash2</key>
			<data>
			HJn4q+h3RCsJsEIokNkfdU/QTQDYR3X4M94Kt1Kv/Ws=
			</data>
		</dict>
		<key>capture_quiz_logs.sh</key>
		<dict>
			<key>hash2</key>
			<data>
			kjVOKSw27GOAvryMoO7I2mHLP7Ryf1qNNGWNgC7aAYA=
			</data>
		</dict>
		<key>embedded.mobileprovision</key>
		<dict>
			<key>hash2</key>
			<data>
			WcOgZTGi3CtxQlOzlpLiBw/+nXLFaa8ZhW6cqIo27Hg=
			</data>
		</dict>
		<key>logo-vantis-navicon.png</key>
		<dict>
			<key>hash2</key>
			<data>
			0Cl7K6Bo7mM/qWngHTj2c/H49aR3CqzIB07P3BHIBrE=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
