# 🔍 **QUIZ MANAGEMENT API DEBUG LOG**

## **Overview**
This document captures the full request and response details when accessing the Quiz Management screen to help debug the persistent error dialog issue.

---

## **📡 API REQUEST DETAILS**

### **Request Information**
- **Timestamp**: `[Will be logged automatically]`
- **Screen**: Quiz Management
- **Action**: Load Quizzes (Initial Load / Pull to Refresh)
- **User**: `[Current logged-in user]`

### **HTTP Request**
```http
Method: GET
URL: https://lms-dev.ebill.vn/api/v1/instructors/quizzes?page=1&limit=20
Headers:
  Content-Type: application/json
  Accept: application/json
  User-Agent: MobileApp/1.0
  Authorization: Bearer [TOKEN_WILL_BE_LOGGED]
```

### **Request Parameters**
```json
{
  "page": 1,
  "limit": 20,
  "filters": {
    // Any applied filters will be shown here
  }
}
```

---

## **📥 API RESPONSE DETAILS**

### **Response Headers**
```http
Status Code: [WILL_BE_LOGGED]
Content-Type: [WILL_BE_LOGGED]
Content-Length: [WILL_BE_LOGGED]
Date: [WILL_BE_LOGGED]
Server: [WILL_BE_LOGGED]
```

### **Response Body**
```json
{
  // Full response body will be logged here
}
```

---

## **🚨 ERROR DETAILS (If Any)**

### **Error Information**
- **Error Type**: `[Error class name]`
- **Error Code**: `[HTTP status code or error code]`
- **Error Message**: `[Detailed error message]`
- **Error Description**: `[Localized description]`

### **Network Error Details**
```
Domain: [Error domain]
Code: [Error code]
UserInfo: [Error user info dictionary]
Underlying Error: [Any underlying errors]
```

---

## **🔐 AUTHENTICATION DETAILS**

### **Token Information**
- **Token Exists**: `[YES/NO]`
- **Token Length**: `[Number of characters]`
- **Token Preview**: `[First 20 characters]...`
- **Token Type**: `[Bearer/Other]`
- **Is Logged In**: `[YES/NO]`

### **User Information**
- **User ID**: `[User ID]`
- **User Email**: `[User email]`
- **User Role**: `[User role]`
- **Display Name**: `[User display name]`

---

## **⚙️ CONFIGURATION DETAILS**

### **API Configuration**
- **Base URL**: `[Current base URL]`
- **Environment**: `[DEBUG/RELEASE]`
- **API System**: `[LMS/EarnBase]`
- **Timeout**: `[Request timeout]`

### **Device Information**
- **Device**: `[Device model]`
- **iOS Version**: `[iOS version]`
- **App Version**: `[App version]`
- **Simulator**: `[YES/NO]`

---

## **📊 TIMING INFORMATION**

### **Performance Metrics**
- **Request Start Time**: `[Timestamp]`
- **Request End Time**: `[Timestamp]`
- **Total Duration**: `[Duration in milliseconds]`
- **Token Validation Time**: `[Duration]`
- **Network Time**: `[Duration]`

---

## **🔄 RETRY INFORMATION**

### **Retry Details**
- **Retry Attempts**: `[Number of retries]`
- **Retry Reasons**: `[Reasons for retries]`
- **Final Result**: `[Success/Failure]`

---

## **📝 ADDITIONAL LOGS**

### **Console Logs**
```
[Timestamp] 🔐 UnifiedAuthManager: Token validation started
[Timestamp] 🔧 API Configuration: Using LMS system
[Timestamp] 🌐 APIClient: Starting request to instructors/quizzes
[Timestamp] 🌐 APIClient: Request headers set
[Timestamp] 🌐 APIClient: Authorization header added
[Timestamp] 🌐 APIClient: Request sent
[Timestamp] 🌐 APIClient: Response received
[Timestamp] 📱 QuizManagementViewModel: Processing response
[Timestamp] 📱 QuizManagementViewModel: [Success/Error] result
```

### **Debug Button Output**
```
🔍 ===== DEBUG API CONNECTION =====
🔍 DEBUG BUTTON TAPPED!
🔍 Is logged in: [YES/NO]
🔍 Token exists: [YES/NO]
🔍 Token length: [number] characters
🔍 Token preview: [preview]...
🔍 User found: [name] (ID: [id])
🔍 User role: [role]
🔍 Base URL: [url]
🔍 Full URL: [full_endpoint_url]
🔍 ================================
```

---

## **🎯 ANALYSIS SECTION**

### **Issue Identification**
- **Root Cause**: `[To be determined from logs]`
- **Error Category**: `[Authentication/Network/Server/Data]`
- **Reproducible**: `[YES/NO]`
- **Frequency**: `[Always/Sometimes/Rare]`

### **Recommended Actions**
1. `[Action based on analysis]`
2. `[Action based on analysis]`
3. `[Action based on analysis]`

---

## **📋 TESTING CHECKLIST**

### **Pre-Test Setup**
- [ ] App freshly installed
- [ ] User logged in successfully
- [ ] Network connection available
- [ ] Console logging enabled

### **Test Steps**
1. [ ] Launch app
2. [ ] Login with instructor credentials
3. [ ] Navigate to Quiz Management screen
4. [ ] Tap "🔍 Debug API" button
5. [ ] Trigger quiz loading (pull to refresh)
6. [ ] Capture all logs and responses
7. [ ] Document any error dialogs

### **Post-Test Analysis**
- [ ] Review request/response logs
- [ ] Analyze error patterns
- [ ] Identify authentication issues
- [ ] Check API endpoint consistency
- [ ] Verify token validity

---

## **🔧 HOW TO CAPTURE THIS DATA**

### **Console Logging**
```bash
# Monitor all app logs
xcrun simctl spawn "iPhone 16 Pro" log show --predicate 'subsystem == "com.mobile-app-template"' --info

# Monitor network logs specifically
xcrun simctl spawn "iPhone 16 Pro" log show --predicate 'category == "network"' --info

# Monitor authentication logs
xcrun simctl spawn "iPhone 16 Pro" log show --predicate 'subsystem CONTAINS "auth"' --info
```

### **Network Monitoring Tools**
- **Charles Proxy**: For detailed HTTP traffic analysis
- **Proxyman**: Alternative network debugging tool
- **Xcode Network Debugger**: Built-in network inspection

---

**📅 Last Updated**: `[Date will be updated when logs are captured]`
**👤 Captured By**: `[Person capturing the logs]`
**🎯 Purpose**: Debug Quiz Management error dialog issue
