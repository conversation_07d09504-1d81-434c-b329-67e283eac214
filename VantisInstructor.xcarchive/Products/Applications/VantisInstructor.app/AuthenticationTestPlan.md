# 🧪 **COMPREHENSIVE AUTHENTICATION TESTING PLAN**

## **Overview**
This testing plan validates the unified authentication system and resolves the Quiz Management error dialog issue.

## **Phase 1: Authentication Flow Testing**

### **Test 1: Login Flow Validation**
**Objective**: Verify unified authentication works correctly

**Steps**:
1. Launch app
2. Navigate to login screen
3. Enter valid instructor credentials
4. Verify successful login
5. Check token storage
6. Verify user data storage

**Expected Results**:
- ✅ Login successful
- ✅ Token saved in TokenManager
- ✅ User data saved correctly
- ✅ API Configuration set to LMS
- ✅ isAuthenticated = true

**Debug Commands**:
```bash
# Check token in simulator
xcrun simctl spawn "iPhone 16 Pro" log show --predicate 'subsystem == "com.mobile-app-template"' --info
```

### **Test 2: Token Validation**
**Objective**: Verify token validation works before API calls

**Steps**:
1. Login successfully
2. Navigate to Quiz Management
3. Trigger loadQuizzes()
4. Check token validation logs

**Expected Results**:
- ✅ Token validation called before API
- ✅ Valid token passes validation
- ✅ API call proceeds with valid token

### **Test 3: API Configuration Consistency**
**Objective**: Verify correct API endpoints are used

**Steps**:
1. Login with LMS credentials
2. Check API configuration
3. Verify Quiz API calls use LMS endpoints
4. Monitor network traffic

**Expected Results**:
- ✅ API base URL: https://lms-dev.ebill.vn/api/v1/
- ✅ Quiz endpoint: instructors/quizzes
- ✅ Authorization header: Bearer [token]

## **Phase 2: Error Handling Testing**

### **Test 4: Token Expiration Handling**
**Objective**: Verify proper handling of expired tokens

**Steps**:
1. Login successfully
2. Manually expire token (or wait)
3. Trigger Quiz Management load
4. Verify error handling

**Expected Results**:
- ✅ Token validation fails
- ✅ User redirected to login
- ✅ Clear error message shown
- ✅ No generic error dialog

### **Test 5: Network Error Handling**
**Objective**: Test various network error scenarios

**Test Cases**:
- No internet connection
- Server timeout
- Server 500 error
- Invalid response format

**Expected Results**:
- ✅ Specific error messages for each scenario
- ✅ Retry options where appropriate
- ✅ No app crashes

### **Test 6: Authentication Error Handling**
**Objective**: Test authentication-specific errors

**Test Cases**:
- Invalid credentials
- Account not instructor role
- Server authentication error

**Expected Results**:
- ✅ Clear error messages
- ✅ No token saved on failure
- ✅ User remains on login screen

## **Phase 3: Integration Testing**

### **Test 7: End-to-End Quiz Management Flow**
**Objective**: Complete flow from login to quiz data display

**Steps**:
1. Fresh app install
2. Login with instructor credentials
3. Navigate to Quiz Management
4. Verify quiz data loads successfully
5. Test refresh functionality

**Expected Results**:
- ✅ No error dialogs
- ✅ Quiz data displays correctly
- ✅ Refresh works without errors

### **Test 8: Multiple Authentication Attempts**
**Objective**: Test robustness of authentication system

**Steps**:
1. Login with invalid credentials (should fail)
2. Login with valid credentials (should succeed)
3. Logout
4. Login again (should succeed)

**Expected Results**:
- ✅ Failed login handled gracefully
- ✅ Successful login works
- ✅ Logout clears data properly
- ✅ Re-login works correctly

## **Phase 4: Performance Testing**

### **Test 9: Token Validation Performance**
**Objective**: Ensure token validation doesn't impact UX

**Steps**:
1. Login successfully
2. Navigate between screens rapidly
3. Monitor token validation timing

**Expected Results**:
- ✅ Token validation < 100ms
- ✅ No UI blocking
- ✅ Smooth navigation

### **Test 10: Memory and Resource Usage**
**Objective**: Verify no memory leaks in auth system

**Steps**:
1. Login/logout cycle 10 times
2. Monitor memory usage
3. Check for retained objects

**Expected Results**:
- ✅ Memory usage stable
- ✅ No memory leaks
- ✅ Proper cleanup on logout

## **Debug Tools and Commands**

### **Network Monitoring**:
```bash
# Monitor network requests
xcrun simctl spawn "iPhone 16 Pro" log show --predicate 'category == "network"' --info
```

### **Authentication Logs**:
```bash
# Monitor auth-specific logs
xcrun simctl spawn "iPhone 16 Pro" log show --predicate 'subsystem CONTAINS "auth"' --info
```

### **Token Inspection**:
```swift
// Add to debug button
print("🔍 Current token: \(TokenManager.shared.getToken() ?? "NONE")")
print("🔍 Token valid: \(await UnifiedAuthManager.shared.validateToken())")
```

## **Success Criteria**

### **Primary Goals**:
- ✅ No error dialogs on Quiz Management screen
- ✅ Successful authentication with LMS system
- ✅ Proper token management and validation
- ✅ Clear, actionable error messages

### **Secondary Goals**:
- ✅ Improved user experience
- ✅ Robust error handling
- ✅ Performance optimization
- ✅ Code maintainability

## **Rollback Plan**

If issues arise:
1. Revert to previous authentication system
2. Re-enable mock data temporarily
3. Investigate specific failures
4. Apply targeted fixes

## **Post-Testing Actions**

1. Remove debug logging from production
2. Update documentation
3. Create user guides for error scenarios
4. Plan for future authentication enhancements
