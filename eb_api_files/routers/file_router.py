# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

"""
File API Router

Provides endpoints for file operations:
- Get file: Get a file by access_token with preview or download options
- Upload file: Upload a file and return attachment details
"""

import io
import base64
from typing import Annotated
from fastapi import APIRouter, Depends, UploadFile, Response, Request
from fastapi.responses import StreamingResponse
from odoo.api import Environment
from odoo.http import content_disposition
from odoo.addons.fastapi.dependencies import odoo_env
from odoo.addons.eb_api_core.dependencies import current_request_id
from odoo.addons.eb_api_core.utils.exceptions import api_exception
from odoo.addons.eb_api_core.schemas.error import ErrorCode
from odoo.addons.eb_api_core.utils.decorators import auto_error_response
from odoo.addons.eb_api_core.utils import get_request_logger, trace

from odoo.addons.eb_api_files.schemas.file import FileModel, FileResponseSchema

_logger = get_request_logger(__name__)

# Không sử dụng prefix ở đây, prefix sẽ được thêm khi đăng ký router trong api.py
file_router = APIRouter()

PREVIEWABLE_TYPES = [
    "image/png",
    "image/jpeg",
    "image/jpg",
    "application/pdf",
]

@file_router.get(
    "/{access_token}",
    response_model=FileResponseSchema,
    name="get_file_by_token"
)
@auto_error_response([404, 500])
@trace()
async def get_file(
    access_token: str,
    env: Annotated[Environment, Depends(odoo_env)],
    request_id: Annotated[str, Depends(current_request_id)],
    request: Request,
    is_download: bool = False,
    is_preview: bool = False,
) -> FileResponseSchema | Response:
    """Get file by access token

    Args:
        access_token: The file's access token
        is_download: Whether to download the file
        is_preview: Whether to preview the file (applies to images and PDFs)
        request: FastAPI request object

    Returns:
        File information or the file itself for download/preview
    """
    _logger.info(f"Getting file with access_token: {access_token}, requestId={request_id}")

    if is_download and is_preview:
        is_download = False

    attachment = (
        env["ir.attachment"]
        .with_context(skip_res_field_check=True)
        .sudo()
        .search([("access_token", "=", access_token)], limit=1)
    )

    if not attachment:
        _logger.warning(f"File not found with access_token: {access_token}, requestId={request_id}")
        raise api_exception(
            error_code=ErrorCode.NOT_FOUND,
            detail="File not found",
            status_code=404
        )

    # Generate URLs using request.url_for
    base_url = str(request.url_for('get_file_by_token', access_token=attachment.access_token))
    download_url = f"{base_url}?is_download=true"
    preview_url = f"{base_url}?is_preview=true" if attachment.mimetype in PREVIEWABLE_TYPES else None

    if any([is_download, is_preview]) is False:
        _logger.info(f"Returning file info for access_token: {access_token}, requestId={request_id}")
        file_model = FileModel(
            id=attachment.id,
            name=attachment.name,
            access_token=attachment.access_token,
            type=attachment.mimetype,
            size=attachment.file_size,
            url=base_url,
            download_url=download_url,
            preview_url=preview_url
        )
        return FileResponseSchema(
            success=True,
            message="File information retrieved successfully",
            data=file_model
        )

    if is_preview:
        if attachment.mimetype in PREVIEWABLE_TYPES:
            _logger.info(f"Previewing file: {attachment.name}, requestId={request_id}")
            stream = io.BytesIO(base64.b64decode(attachment.datas))
            return StreamingResponse(content=stream, media_type=attachment.mimetype)
        else:
             _logger.warning(f"Preview not supported for file type: {attachment.mimetype}, access_token: {access_token}, requestId={request_id}")
             raise api_exception(
                 error_code=ErrorCode.VALIDATION_ERROR,
                 detail=f"Preview is not supported for this file type ({attachment.mimetype})",
                 status_code=400
            )

    # Return download
    _logger.info(f"Downloading file: {attachment.name}, requestId={request_id}")
    return Response(
        content=base64.b64decode(attachment.datas),
        media_type="application/octet-stream",
        headers={"Content-Disposition": content_disposition(attachment.name)},
    )


@file_router.post(
    "/",
    response_model=FileResponseSchema,
    name="upload_file_endpoint"
)
@auto_error_response([400, 500])
@trace()
async def upload_file(
    env: Annotated[Environment, Depends(odoo_env)],
    file: UploadFile,
    request_id: Annotated[str, Depends(current_request_id)],
    request: Request
) -> FileResponseSchema:
    """Upload a file

    Args:
        file: The file to upload
        request: FastAPI request object

    Returns:
        Details of the uploaded attachment including URLs
    """
    _logger.info(f"Uploading file: {file.filename}, requestId={request_id}")

    try:
        file_content = await file.read()

        attachment = (
            env["ir.attachment"]
            .sudo()
            .create(
                {
                    "name": file.filename,
                    "datas": base64.b64encode(file_content),
                    "type": "binary",
                    "mimetype": file.content_type,
                    "public": True,
                }
            )
        )

        _logger.info(f"File uploaded successfully: {file.filename}, id={attachment.id}, requestId={request_id}")

        # Generate URLs for the newly uploaded file
        base_url = str(request.url_for('get_file_by_token', access_token=attachment.access_token))
        download_url = f"{base_url}?is_download=true"
        preview_url = f"{base_url}?is_preview=true" if attachment.mimetype in PREVIEWABLE_TYPES else None

        file_model = FileModel(
            id=attachment.id,
            name=attachment.name,
            access_token=attachment.access_token,
            type=attachment.mimetype,
            size=attachment.file_size,
            url=base_url,
            download_url=download_url,
            preview_url=preview_url
        )

        return FileResponseSchema(
            success=True,
            message="File uploaded successfully",
            data=file_model
        )
    except Exception as e:
        _logger.error(f"Error uploading file: {file.filename}, error: {str(e)}, requestId={request_id}")
        raise api_exception(
            error_code=ErrorCode.INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload file: {str(e)}",
        )