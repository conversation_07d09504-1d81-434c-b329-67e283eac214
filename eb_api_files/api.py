# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

"""
API registration for the eb_api_files module.

This module provides FastAPI endpoints for file operations:
- Get file by access_token
- Upload files
"""

import logging

from fastapi import APIRouter
from odoo.addons.eb_api_core.utils.decorators import register_api_module

_logger = logging.getLogger(__name__)


@register_api_module(
    module_name="files",
    description="API for file management with preview, download and upload capabilities",
    version="1.0",
    app_name="files_api",
)
class FilesAPI:
    """API module for file operations endpoints"""

    # Router instance
    router = APIRouter()

    # Import routers để đăng ký endpoints
    from odoo.addons.eb_api_files.routers.file_router import file_router

    # Đăng ký sub-routers với prefix /files để hoạt động với endpoint /api/v1
    router.include_router(file_router, prefix="/files", tags=["files"])
