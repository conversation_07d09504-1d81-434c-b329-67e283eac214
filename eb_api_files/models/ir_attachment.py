# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

from odoo import api, models, fields


class IrAttachment(models.Model):
    _inherit = "ir.attachment"

    file_url_preview = fields.Char(
        string="Preview URL",
        compute="_compute_file_urls",
        store=True,
        help="URL to preview the file",
    )
    file_url_download = fields.Char(
        string="Download URL",
        compute="_compute_file_urls",
        store=True,
        help="URL to download the file",
    )

    @api.depends("access_token")
    def _compute_file_urls(self):
        """Tính toán các URL cho file dựa trên access_token"""
        base_url = self.env["ir.config_parameter"].sudo().get_param("web.base.url")
        if not base_url:
            base_url = ""

        for attachment in self:
            if attachment.access_token:
                file_url = f"{base_url}/api/v1/files/{attachment.access_token}"
                attachment.file_url_preview = f"{file_url}?is_preview=true"
                attachment.file_url_download = f"{file_url}?is_download=true"
            else:
                attachment.file_url_preview = False
                attachment.file_url_download = False

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            if "access_token" not in vals:
                vals["access_token"] = self.env[
                    "ir.attachment"
                ]._generate_access_token()
        return super(IrAttachment, self).create(vals_list)
