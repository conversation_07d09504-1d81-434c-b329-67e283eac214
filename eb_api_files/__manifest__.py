# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

{
    "name": "EarnBase API Files",
    "version": "********.0",
    "category": "API",
    "summary": "API for file attachments management",
    "description": """
        Module providing APIs for file attachments, leveraging Odoo's ir.attachment model.
        - Get files with preview or download options
        - Upload files
    """,
    "author": "eBill and Vantis Vietnam",
    "website": "https://earnbase.io",
    "license": "LGPL-3",
    "depends": [
        "base",
        "fastapi",
        "eb_api_core",
    ],
    "data": [],
    "demo": [],
    "installable": True,
    "application": False,
    "auto_install": False,
    "post_init_hook": "post_init_hook",
    "uninstall_hook": "uninstall_hook",
} 