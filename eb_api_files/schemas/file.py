# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

from typing import Optional
from pydantic import BaseModel, Field

from odoo.addons.eb_api_core.schemas.base import ResponseBase


class FileModel(BaseModel):
    """Schema representing a file attachment"""
    
    id: int
    name: str
    access_token: str
    type: Optional[str] = None
    size: Optional[int] = None
    url: Optional[str] = Field(None, description="Direct URL to access the file info/preview.")
    download_url: Optional[str] = Field(None, description="URL to force download the file.")
    preview_url: Optional[str] = Field(None, description="URL to attempt previewing the file (if supported).")


class FileResponseSchema(ResponseBase[FileModel]):
    """Response schema for file operations"""
    
    data: Optional[FileModel] = None 