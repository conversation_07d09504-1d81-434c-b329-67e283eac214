//
//  InstructorLoginTestView.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 22/7/25.
//

import SwiftUI

struct InstructorLoginTestView: View {
    @StateObject private var instructorAuth = InstructorAuthService.shared
    @State private var username = "admin"
    @State private var password = "earnbase@2025"
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var testResults: [String] = []
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header
                    VStack(spacing: 8) {
                        Text("Instructor Login Test")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                        
                        Text("Test API integration with LMS")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .padding(.top)
                    
                    // Current Status
                    statusSection
                    
                    // Login Form
                    loginFormSection
                    
                    // Test Results
                    testResultsSection
                    
                    Spacer()
                }
                .padding()
            }
            .navigationBarHidden(true)
            .alert("Result", isPresented: $showingAlert) {
                Button("OK") { }
            } message: {
                Text(alertMessage)
            }
        }
    }
    
    // MARK: - Status Section
    private var statusSection: some View {
        VStack(spacing: 12) {
            Text("Current Status")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 8) {
                StatusRow(
                    title: "Authentication",
                    value: instructorAuth.isAuthenticated ? "✅ Authenticated" : "❌ Not Authenticated",
                    color: instructorAuth.isAuthenticated ? .green : .red
                )
                
                StatusRow(
                    title: "API Endpoint",
                    value: APIConfiguration.shared.baseURL,
                    color: .blue
                )
                
                if let instructor = instructorAuth.currentInstructor {
                    StatusRow(
                        title: "Instructor",
                        value: instructor.displayName,
                        color: .green
                    )
                    
                    StatusRow(
                        title: "Roles",
                        value: instructor.roles.map { $0.name }.joined(separator: ", "),
                        color: .orange
                    )
                }
                
                StatusRow(
                    title: "Loading",
                    value: instructorAuth.isLoading ? "🔄 Loading..." : "✅ Ready",
                    color: instructorAuth.isLoading ? .orange : .green
                )
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
    }
    
    // MARK: - Login Form Section
    private var loginFormSection: some View {
        VStack(spacing: 16) {
            Text("Login Credentials")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 12) {
                HStack {
                    Text("Username:")
                        .frame(width: 80, alignment: .leading)
                    TextField("Username", text: $username)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .autocapitalization(.none)
                }
                
                HStack {
                    Text("Password:")
                        .frame(width: 80, alignment: .leading)
                    SecureField("Password", text: $password)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                }
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
            
            // Action Buttons
            VStack(spacing: 12) {
                Button(action: testLogin) {
                    HStack {
                        if instructorAuth.isLoading {
                            ProgressView()
                                .scaleEffect(0.8)
                        }
                        Text("Test Login")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(12)
                }
                .disabled(instructorAuth.isLoading)
                
                if instructorAuth.isAuthenticated {
                    Button(action: testLogout) {
                        Text("Test Logout")
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.red)
                            .foregroundColor(.white)
                            .cornerRadius(12)
                    }
                }
                
                Button(action: testAPIConnection) {
                    Text("Test API Connection")
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.green)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                }
                
                Button(action: clearTestResults) {
                    Text("Clear Results")
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.gray)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                }
            }
        }
    }
    
    // MARK: - Test Results Section
    private var testResultsSection: some View {
        VStack(spacing: 12) {
            Text("Test Results")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            if testResults.isEmpty {
                Text("No test results yet")
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
            } else {
                LazyVStack(spacing: 8) {
                    ForEach(Array(testResults.enumerated()), id: \.offset) { index, result in
                        HStack {
                            Text("\(index + 1).")
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .frame(width: 20, alignment: .leading)
                            
                            Text(result)
                                .font(.caption)
                                .frame(maxWidth: .infinity, alignment: .leading)
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                    }
                }
            }
        }
    }
    
    // MARK: - Actions
    private func testLogin() {
        addTestResult("🔄 Starting login test...")
        
        Task {
            do {
                try await instructorAuth.login(
                    username: username,
                    password: password,
                    rememberMe: false
                )
                
                await MainActor.run {
                    addTestResult("✅ Login successful!")
                    if let instructor = instructorAuth.currentInstructor {
                        addTestResult("👤 Logged in as: \(instructor.displayName)")
                        addTestResult("📧 Email: \(instructor.email)")
                        addTestResult("🎭 Roles: \(instructor.roles.map { $0.name }.joined(separator: ", "))")
                        addTestResult("✅ Is Instructor: \(instructor.isInstructor)")
                    }
                    alertMessage = "Login successful!"
                    showingAlert = true
                }
                
            } catch {
                await MainActor.run {
                    addTestResult("❌ Login failed: \(error.localizedDescription)")
                    alertMessage = "Login failed: \(error.localizedDescription)"
                    showingAlert = true
                }
            }
        }
    }
    
    private func testLogout() {
        addTestResult("🔄 Starting logout test...")
        
        Task {
            await instructorAuth.logout()
            
            await MainActor.run {
                addTestResult("✅ Logout successful!")
                alertMessage = "Logout successful!"
                showingAlert = true
            }
        }
    }
    
    private func testAPIConnection() {
        addTestResult("🔄 Testing API connection...")
        
        Task {
            do {
                let healthStatus = try await EarnBaseAPIService.shared.healthCheck()
                
                await MainActor.run {
                    addTestResult("✅ API connection successful!")
                    addTestResult("📊 Health status: \(healthStatus.success)")
                    if let message = healthStatus.message {
                        addTestResult("💬 Message: \(message)")
                    }
                    alertMessage = "API connection successful!"
                    showingAlert = true
                }
                
            } catch {
                await MainActor.run {
                    addTestResult("❌ API connection failed: \(error.localizedDescription)")
                    alertMessage = "API connection failed: \(error.localizedDescription)"
                    showingAlert = true
                }
            }
        }
    }
    
    private func clearTestResults() {
        testResults.removeAll()
    }
    
    private func addTestResult(_ result: String) {
        let timestamp = DateFormatter.timeFormatter.string(from: Date())
        testResults.append("[\(timestamp)] \(result)")
    }
}

// MARK: - Supporting Views
struct StatusRow: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        HStack {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .frame(width: 100, alignment: .leading)
            
            Text(value)
                .font(.caption)
                .foregroundColor(color)
                .frame(maxWidth: .infinity, alignment: .leading)
        }
    }
}

// MARK: - Extensions
extension DateFormatter {
    static let timeFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.timeStyle = .medium
        return formatter
    }()
}

// MARK: - Preview
struct InstructorLoginTestView_Previews: PreviewProvider {
    static var previews: some View {
        InstructorLoginTestView()
    }
}
