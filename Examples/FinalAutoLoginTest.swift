//
//  FinalAutoLoginTest.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 22/7/25.
//

import SwiftUI

struct FinalAutoLoginTest: View {
    @State private var testResults: [String] = []
    @State private var isRunning = false
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var testTimer: Timer?
    @State private var testStartTime: Date?
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header
                    VStack(spacing: 8) {
                        Text("Final Auto-Login Test")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                        
                        Text("Ultimate test to ensure NO auto-login")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .padding(.top)
                    
                    // Current Status
                    currentStatusSection
                    
                    // Test Controls
                    testControlsSection
                    
                    // Test Results
                    testResultsSection
                    
                    Spacer()
                }
                .padding()
            }
            .navigationBarHidden(true)
            .alert("Test Result", isPresented: $showingAlert) {
                But<PERSON>("OK") { }
            } message: {
                Text(alertMessage)
            }
        }
        .onDisappear {
            stopContinuousTest()
        }
    }
    
    // MARK: - Current Status Section
    private var currentStatusSection: some View {
        VStack(spacing: 12) {
            Text("Real-Time Authentication Status")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 8) {
                StatusRow(
                    title: "InstructorAuth",
                    value: InstructorAuthService.shared.isAuthenticated ? "🚨 AUTHENTICATED" : "✅ Clean",
                    color: InstructorAuthService.shared.isAuthenticated ? .red : .green
                )
                
                StatusRow(
                    title: "TokenManager",
                    value: TokenManager.shared.isLoggedIn ? "🚨 HAS TOKEN" : "✅ Clean",
                    color: TokenManager.shared.isLoggedIn ? .red : .green
                )
                
                StatusRow(
                    title: "UserDefaults",
                    value: hasUserDefaultsData() ? "🚨 HAS DATA" : "✅ Clean",
                    color: hasUserDefaultsData() ? .red : .green
                )
                
                StatusRow(
                    title: "Keychain",
                    value: hasKeychainData() ? "🚨 HAS DATA" : "✅ Clean",
                    color: hasKeychainData() ? .red : .green
                )
                
                StatusRow(
                    title: "Test Status",
                    value: isRunning ? "🔍 Running" : "⏸️ Stopped",
                    color: isRunning ? .blue : .gray
                )
                
                if let startTime = testStartTime {
                    StatusRow(
                        title: "Test Duration",
                        value: "\(Int(Date().timeIntervalSince(startTime)))s",
                        color: .blue
                    )
                }
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
    }
    
    // MARK: - Test Controls Section
    private var testControlsSection: some View {
        VStack(spacing: 16) {
            Text("Test Controls")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 12) {
                Button(action: runSingleTest) {
                    Text("Run Single Test")
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                }
                .disabled(isRunning)
                
                Button(action: startContinuousTest) {
                    Text(isRunning ? "Stop Continuous Test" : "Start Continuous Test")
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(isRunning ? Color.red : Color.green)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                }
                
                Button(action: simulateAppRestart) {
                    Text("Simulate App Restart")
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.orange)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                }
                .disabled(isRunning)
                
                Button(action: forceNuclearClean) {
                    Text("Nuclear Clean (Clear Everything)")
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.black)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                }
                
                Button(action: clearTestResults) {
                    Text("Clear Results")
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.gray)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                }
            }
        }
    }
    
    // MARK: - Test Results Section
    private var testResultsSection: some View {
        VStack(spacing: 12) {
            Text("Test Results")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            if testResults.isEmpty {
                Text("No test results yet")
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
            } else {
                LazyVStack(spacing: 8) {
                    ForEach(Array(testResults.enumerated()), id: \.offset) { index, result in
                        HStack {
                            Text("\(index + 1).")
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .frame(width: 20, alignment: .leading)
                            
                            Text(result)
                                .font(.caption)
                                .frame(maxWidth: .infinity, alignment: .leading)
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(getResultColor(result))
                        .cornerRadius(8)
                    }
                }
            }
        }
    }
    
    // MARK: - Test Methods
    
    private func runSingleTest() {
        addTestResult("🧪 Running single auto-login test...")
        
        // Check all auth states
        checkAllAuthStates()
        
        // Summary
        let hasIssues = testResults.suffix(10).contains { $0.contains("🚨") }
        addTestResult(hasIssues ? "❌ ISSUES FOUND!" : "✅ NO AUTO-LOGIN DETECTED")
        
        alertMessage = hasIssues ? "❌ Auto-login detected!" : "✅ No auto-login found"
        showingAlert = true
    }
    
    private func startContinuousTest() {
        if isRunning {
            stopContinuousTest()
        } else {
            isRunning = true
            testStartTime = Date()
            addTestResult("🔄 Starting continuous monitoring...")
            
            testTimer = Timer.scheduledTimer(withTimeInterval: 2.0, repeats: true) { _ in
                checkAllAuthStates()
            }
        }
    }
    
    private func stopContinuousTest() {
        isRunning = false
        testStartTime = nil
        testTimer?.invalidate()
        testTimer = nil
        addTestResult("⏸️ Stopped continuous monitoring")
    }
    
    private func simulateAppRestart() {
        addTestResult("🔄 Simulating app restart...")
        
        // Force nuclear clean
        forceNuclearClean()
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            addTestResult("🔄 Creating new auth instances...")
            
            // Create new instances
            let _ = AuthStateManager()
            let _ = InstructorAuthService.shared
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                addTestResult("🔄 Checking for auto-login after restart simulation...")
                checkAllAuthStates()
                
                let hasIssues = testResults.suffix(5).contains { $0.contains("🚨") }
                addTestResult(hasIssues ? "❌ AUTO-LOGIN AFTER RESTART!" : "✅ NO AUTO-LOGIN AFTER RESTART")
            }
        }
    }
    
    private func forceNuclearClean() {
        addTestResult("☢️ Performing nuclear clean...")
        
        // Clear InstructorAuthService
        InstructorAuthService.shared.isAuthenticated = false
        InstructorAuthService.shared.currentInstructor = nil
        InstructorAuthService.shared.authError = nil
        
        // Clear TokenManager
        TokenManager.shared.clearToken()
        
        // Clear ALL UserDefaults
        let allKeys = [
            "linkx_access_token", "linkx_current_user", "saved_email",
            "access_token", "refresh_token", "user_id", "current_user",
            "instructor_token", "instructor_user", "biometric_enabled"
        ]
        
        for key in allKeys {
            UserDefaults.standard.removeObject(forKey: key)
        }
        UserDefaults.standard.synchronize()
        
        // Clear ALL Keychain
        let keychainManager = KeychainManager.shared
        let keychainKeys = [
            "access_token", "refresh_token", "saved_credentials",
            "linkx_access_token", "linkx_refresh_token", "instructor_token"
        ]
        
        for key in keychainKeys {
            try? keychainManager.delete(key: key)
        }
        
        try? keychainManager.deleteAccessToken()
        try? keychainManager.deleteRefreshToken()
        
        addTestResult("☢️ Nuclear clean complete")
    }
    
    private func checkAllAuthStates() {
        // Check InstructorAuthService
        if InstructorAuthService.shared.isAuthenticated {
            addTestResult("🚨 InstructorAuthService.isAuthenticated = true")
        }
        
        if InstructorAuthService.shared.currentInstructor != nil {
            addTestResult("🚨 InstructorAuthService.currentInstructor = \(InstructorAuthService.shared.currentInstructor!.displayName)")
        }
        
        // Check TokenManager
        if TokenManager.shared.isLoggedIn {
            addTestResult("🚨 TokenManager.isLoggedIn = true")
        }
        
        if TokenManager.shared.getToken() != nil {
            addTestResult("🚨 TokenManager has token")
        }
        
        // Check UserDefaults
        if hasUserDefaultsData() {
            addTestResult("🚨 UserDefaults contains auth data")
        }
        
        // Check Keychain
        if hasKeychainData() {
            addTestResult("🚨 Keychain contains auth data")
        }
        
        // If no issues found in this check
        let recentResults = testResults.suffix(10)
        if !recentResults.contains(where: { $0.contains("🚨") }) {
            addTestResult("✅ All auth states clean")
        }
    }
    
    private func hasUserDefaultsData() -> Bool {
        let keys = ["linkx_access_token", "linkx_current_user", "saved_email", "access_token"]
        return keys.contains { UserDefaults.standard.object(forKey: $0) != nil }
    }
    
    private func hasKeychainData() -> Bool {
        let keychainManager = KeychainManager.shared
        return (try? keychainManager.loadAccessToken()) != nil ||
               (try? keychainManager.loadRefreshToken()) != nil ||
               (try? keychainManager.loadString(key: "saved_credentials")) != nil
    }
    
    private func clearTestResults() {
        testResults.removeAll()
    }
    
    private func addTestResult(_ result: String) {
        let timestamp = DateFormatter.timeFormatter.string(from: Date())
        testResults.append("[\(timestamp)] \(result)")
    }
    
    private func getResultColor(_ result: String) -> Color {
        if result.contains("🚨") || result.contains("❌") {
            return Color.red.opacity(0.1)
        } else if result.contains("⚠️") {
            return Color.orange.opacity(0.1)
        } else if result.contains("✅") {
            return Color.green.opacity(0.1)
        } else {
            return Color(.systemGray6)
        }
    }
}

// MARK: - Preview
struct FinalAutoLoginTest_Previews: PreviewProvider {
    static var previews: some View {
        FinalAutoLoginTest()
    }
}
