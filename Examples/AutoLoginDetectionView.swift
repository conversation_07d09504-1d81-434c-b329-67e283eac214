//
//  AutoLoginDetectionView.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 22/7/25.
//

import SwiftUI

struct AutoLoginDetectionView: View {
    @StateObject private var instructorAuth = InstructorAuthService.shared
    @StateObject private var authStateManager = AuthStateManager()
    @StateObject private var authViewModel = AuthViewModel()
    
    @State private var testResults: [String] = []
    @State private var isMonitoring = false
    @State private var monitoringStartTime: Date?
    @State private var showingAlert = false
    @State private var alertMessage = ""
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header
                    VStack(spacing: 8) {
                        Text("Auto-Login Detection")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                        
                        Text("Monitor for any automatic login attempts")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .padding(.top)
                    
                    // Current Status
                    currentStatusSection
                    
                    // Monitoring Controls
                    monitoringControlsSection
                    
                    // Test Results
                    testResultsSection
                    
                    Spacer()
                }
                .padding()
            }
            .navigationBarHidden(true)
            .alert("Detection Result", isPresented: $showingAlert) {
                Button("OK") { }
            } message: {
                Text(alertMessage)
            }
        }
        .onAppear {
            setupMonitoring()
        }
    }
    
    // MARK: - Current Status Section
    private var currentStatusSection: some View {
        VStack(spacing: 12) {
            Text("Current Authentication Status")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 8) {
                StatusRow(
                    title: "InstructorAuth",
                    value: instructorAuth.isAuthenticated ? "🚨 AUTHENTICATED" : "✅ Not Authenticated",
                    color: instructorAuth.isAuthenticated ? .red : .green
                )
                
                StatusRow(
                    title: "AuthStateManager",
                    value: authStateManager.isAuthenticated ? "🚨 AUTHENTICATED" : "✅ Not Authenticated",
                    color: authStateManager.isAuthenticated ? .red : .green
                )
                
                StatusRow(
                    title: "AuthViewModel",
                    value: authViewModel.isAuthenticated ? "🚨 AUTHENTICATED" : "✅ Not Authenticated",
                    color: authViewModel.isAuthenticated ? .red : .green
                )
                
                StatusRow(
                    title: "TokenManager",
                    value: TokenManager.shared.isLoggedIn ? "⚠️ Has Token" : "✅ No Token",
                    color: TokenManager.shared.isLoggedIn ? .orange : .green
                )
                
                StatusRow(
                    title: "Monitoring",
                    value: isMonitoring ? "🔍 Active" : "⏸️ Stopped",
                    color: isMonitoring ? .blue : .gray
                )
                
                if let startTime = monitoringStartTime {
                    StatusRow(
                        title: "Duration",
                        value: "\(Int(Date().timeIntervalSince(startTime)))s",
                        color: .blue
                    )
                }
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
    }
    
    // MARK: - Monitoring Controls Section
    private var monitoringControlsSection: some View {
        VStack(spacing: 16) {
            Text("Monitoring Controls")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 12) {
                Button(action: startMonitoring) {
                    Text(isMonitoring ? "Restart Monitoring" : "Start Monitoring")
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                }
                
                Button(action: stopMonitoring) {
                    Text("Stop Monitoring")
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.red)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                }
                .disabled(!isMonitoring)
                
                Button(action: forceCleanAndTest) {
                    Text("Force Clean & Test")
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.purple)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                }
                
                Button(action: simulateAppRestart) {
                    Text("Simulate App Restart")
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.orange)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                }
                
                Button(action: clearTestResults) {
                    Text("Clear Results")
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.gray)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                }
            }
        }
    }
    
    // MARK: - Test Results Section
    private var testResultsSection: some View {
        VStack(spacing: 12) {
            Text("Detection Results")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            if testResults.isEmpty {
                Text("No events detected yet")
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
            } else {
                LazyVStack(spacing: 8) {
                    ForEach(Array(testResults.enumerated()), id: \.offset) { index, result in
                        HStack {
                            Text("\(index + 1).")
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .frame(width: 20, alignment: .leading)
                            
                            Text(result)
                                .font(.caption)
                                .frame(maxWidth: .infinity, alignment: .leading)
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(result.contains("🚨") ? Color.red.opacity(0.1) : Color(.systemGray6))
                        .cornerRadius(8)
                    }
                }
            }
        }
    }
    
    // MARK: - Monitoring Logic
    
    private func setupMonitoring() {
        // Monitor InstructorAuthService
        instructorAuth.$isAuthenticated
            .sink { [weak self] isAuth in
                if isAuth && self?.isMonitoring == true {
                    self?.addTestResult("🚨 AUTO-LOGIN DETECTED: InstructorAuthService.isAuthenticated = true")
                    self?.alertMessage = "🚨 Auto-login detected in InstructorAuthService!"
                    self?.showingAlert = true
                }
            }
            .store(in: &cancellables)
        
        // Monitor AuthStateManager
        authStateManager.$isAuthenticated
            .sink { [weak self] isAuth in
                if isAuth && self?.isMonitoring == true {
                    self?.addTestResult("🚨 AUTO-LOGIN DETECTED: AuthStateManager.isAuthenticated = true")
                    self?.alertMessage = "🚨 Auto-login detected in AuthStateManager!"
                    self?.showingAlert = true
                }
            }
            .store(in: &cancellables)
        
        // Monitor AuthViewModel
        authViewModel.$isAuthenticated
            .sink { [weak self] isAuth in
                if isAuth && self?.isMonitoring == true {
                    self?.addTestResult("🚨 AUTO-LOGIN DETECTED: AuthViewModel.isAuthenticated = true")
                    self?.alertMessage = "🚨 Auto-login detected in AuthViewModel!"
                    self?.showingAlert = true
                }
            }
            .store(in: &cancellables)
        
        // Monitor current user changes
        instructorAuth.$currentInstructor
            .sink { [weak self] user in
                if user != nil && self?.isMonitoring == true {
                    self?.addTestResult("🚨 AUTO-LOGIN DETECTED: InstructorAuthService.currentInstructor set to \(user?.displayName ?? "unknown")")
                }
            }
            .store(in: &cancellables)
        
        authStateManager.$currentUser
            .sink { [weak self] user in
                if user != nil && self?.isMonitoring == true {
                    self?.addTestResult("🚨 AUTO-LOGIN DETECTED: AuthStateManager.currentUser set to \(user?.displayName ?? "unknown")")
                }
            }
            .store(in: &cancellables)
        
        authViewModel.$currentUser
            .sink { [weak self] user in
                if user != nil && self?.isMonitoring == true {
                    self?.addTestResult("🚨 AUTO-LOGIN DETECTED: AuthViewModel.currentUser set to \(user?.displayName ?? "unknown")")
                }
            }
            .store(in: &cancellables)
    }
    
    @State private var cancellables = Set<AnyCancellable>()
    
    private func startMonitoring() {
        isMonitoring = true
        monitoringStartTime = Date()
        addTestResult("🔍 Started monitoring for auto-login attempts...")
        
        // Log initial state
        addTestResult("📊 Initial state - InstructorAuth: \(instructorAuth.isAuthenticated), AuthState: \(authStateManager.isAuthenticated), AuthViewModel: \(authViewModel.isAuthenticated)")
    }
    
    private func stopMonitoring() {
        isMonitoring = false
        monitoringStartTime = nil
        addTestResult("⏸️ Stopped monitoring")
    }
    
    private func forceCleanAndTest() {
        addTestResult("🧹 Force cleaning all auth data...")
        
        // Force complete logout
        AuthDebugUtils.shared.forceCompleteLogout()
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            let isClean = AuthDebugUtils.shared.verifyCompleteLogout()
            addTestResult("🧹 Force clean result: \(isClean ? "✅ CLEAN" : "❌ NOT CLEAN")")
            
            if !isClean {
                addTestResult("⚠️ Some auth data remains - check for auto-login sources")
            }
            
            // Start monitoring after clean
            startMonitoring()
        }
    }
    
    private func simulateAppRestart() {
        addTestResult("🔄 Simulating app restart...")
        
        // Stop monitoring
        stopMonitoring()
        
        // Force clean
        AuthDebugUtils.shared.forceCompleteLogout()
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            // Create new instances (simulating app restart)
            addTestResult("🔄 Creating new auth instances...")
            
            // Start monitoring
            startMonitoring()
            
            addTestResult("🔄 App restart simulation complete - monitoring for auto-login...")
        }
    }
    
    private func clearTestResults() {
        testResults.removeAll()
    }
    
    private func addTestResult(_ result: String) {
        let timestamp = DateFormatter.timeFormatter.string(from: Date())
        testResults.append("[\(timestamp)] \(result)")
    }
}

// MARK: - Preview
struct AutoLoginDetectionView_Previews: PreviewProvider {
    static var previews: some View {
        AutoLoginDetectionView()
    }
}
