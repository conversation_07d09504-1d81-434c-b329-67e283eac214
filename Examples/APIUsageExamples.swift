//
//  APIUsageExamples.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 22/7/25.
//

import Foundation
import SwiftUI

// MARK: - API Usage Examples
class APIUsageExamples {
    
    private let apiService = EarnBaseAPIService.shared
    
    // MARK: - Environment Configuration Examples
    
    /// Example: Switch to different environments
    func configurationExamples() {
        // Switch to development environment
        APIConfiguration.shared.setupForDevelopment()
        
        // Switch to staging environment
        APIConfiguration.shared.setupForStaging()
        
        // Switch to production environment
        APIConfiguration.shared.setupForProduction()
        
        // Use custom API URL
        APIConfiguration.shared.setupCustom(baseURL: "https://my-custom-api.com/api/v1")
        
        // Get current configuration info
        let debugInfo = APIConfiguration.shared.debugInfo
        print("Current API config: \(debugInfo)")
    }
    
    // MARK: - Authentication Examples
    
    /// Example: User sign in
    func signInExample() async {
        do {
            let deviceInfo = UnifiedLoginRequest.DeviceInfo(
                deviceId: UIDevice.current.identifierForVendor?.uuidString,
                deviceName: UIDevice.current.name,
                platform: "iOS",
                appVersion: "1.0.0"
            )
            
            let response = try await apiService.signIn(
                login: "<EMAIL>",
                password: "password123",
                deviceInfo: deviceInfo
            )
            
            print("Sign in successful!")
            print("Access Token: \(response.accessToken)")
            print("User: \(response.user.email)")
            
            // Save token for future requests
            TokenManager.shared.saveToken(response.accessToken)
            
        } catch {
            print("Sign in failed: \(error)")
        }
    }
    
    /// Example: User registration
    func registerExample() async {
        do {
            let request = UserRegistrationRequest(
                username: "newuser",
                email: "<EMAIL>",
                password: "securePassword123",
                firstName: "John",
                lastName: "Doe",
                acceptTerms: true
            )
            
            let response = try await apiService.register(request: request)
            
            print("Registration successful!")
            print("User ID: \(response.user.id)")
            print("Email verification required: \(response.requiresEmailVerification)")
            
        } catch {
            print("Registration failed: \(error)")
        }
    }
    
    /// Example: Get current user info
    func getCurrentUserExample() async {
        do {
            let userInfo = try await apiService.getCurrentUser()
            
            print("Current user: \(userInfo.user.email)")
            print("Permissions: \(userInfo.permissions ?? [])")
            print("Roles: \(userInfo.roles ?? [])")
            
        } catch {
            print("Failed to get user info: \(error)")
        }
    }
    
    /// Example: Password reset flow
    func passwordResetExample() async {
        do {
            // Step 1: Request password reset
            let forgotResponse = try await apiService.forgotPassword(email: "<EMAIL>")
            print("Password reset email sent: \(forgotResponse.resetTokenSent)")
            
            // Step 2: Reset password with token (user gets this from email)
            let resetResponse = try await apiService.resetPassword(
                token: "reset_token_from_email",
                newPassword: "newSecurePassword123"
            )
            print("Password reset successful: \(resetResponse.success)")
            
        } catch {
            print("Password reset failed: \(error)")
        }
    }
    
    // MARK: - Public Events Examples
    
    /// Example: Get and display public events
    func getPublicEventsExample() async {
        do {
            let eventsResponse = try await apiService.getPublicEvents()
            
            print("Found \(eventsResponse.events.count) events")
            
            for event in eventsResponse.events {
                print("Event: \(event.title)")
                print("Date: \(event.startDate)")
                print("Participants: \(event.currentParticipants)/\(event.maxParticipants ?? 0)")
                print("---")
            }
            
        } catch {
            print("Failed to fetch events: \(error)")
        }
    }
    
    /// Example: Register for an event
    func registerForEventExample() async {
        do {
            let registrationRequest = PublicEventRegistrationCreate(
                firstName: "John",
                lastName: "Doe",
                email: "<EMAIL>",
                phone: "+1234567890",
                organization: "Tech Company",
                jobTitle: "Developer",
                specialRequirements: "Vegetarian meal",
                marketingConsent: true
            )
            
            let response = try await apiService.createEventRegistration(
                eventId: 123,
                request: registrationRequest
            )
            
            print("Registration successful!")
            print("Registration ID: \(response.registrationId)")
            print("Confirmation Code: \(response.confirmationCode ?? "N/A")")
            
        } catch {
            print("Event registration failed: \(error)")
        }
    }
    
    // MARK: - Public Courses Examples
    
    /// Example: Search and browse courses
    func browseCourses() async {
        do {
            // Get all courses
            let allCourses = try await apiService.getPublicCourses()
            print("Total courses: \(allCourses.totalCount)")
            
            // Search for specific courses
            let searchResults = try await apiService.searchCourses(
                query: "iOS Development",
                page: 1,
                pageSize: 10
            )
            
            for course in searchResults.courses {
                print("Course: \(course.title)")
                print("Level: \(course.level ?? "N/A")")
                print("Price: $\(course.price ?? 0)")
                print("Students: \(course.totalStudents)")
                print("---")
            }
            
        } catch {
            print("Failed to browse courses: \(error)")
        }
    }
    
    /// Example: Enroll in a course
    func enrollInCourseExample() async {
        do {
            let enrollmentRequest = PublicEnrollmentCreate(
                firstName: "Jane",
                lastName: "Smith",
                email: "<EMAIL>",
                phone: "+1234567890",
                dateOfBirth: "1990-01-01",
                address: "123 Main St, City, State",
                emergencyContact: EmergencyContact(
                    name: "John Smith",
                    relationship: "Spouse",
                    phone: "+1234567891"
                ),
                paymentMethod: "credit_card",
                specialRequirements: "None",
                marketingConsent: true
            )
            
            let response = try await apiService.createCourseEnrollment(
                courseId: 456,
                request: enrollmentRequest
            )
            
            print("Enrollment successful!")
            print("Enrollment ID: \(response.enrollmentId)")
            print("Payment Status: \(response.paymentStatus ?? "N/A")")
            
        } catch {
            print("Course enrollment failed: \(error)")
        }
    }
    
    // MARK: - Public Classes Examples
    
    /// Example: Find and filter classes
    func findClassesExample() async {
        do {
            let classes = try await apiService.getPublicClasses(
                page: 1,
                perPage: 20,
                courseId: 456,
                startDateFrom: "2024-01-01",
                learningType: "online"
            )
            
            print("Found \(classes.classes.count) classes")
            
            for classItem in classes.classes {
                print("Class: \(classItem.name)")
                print("Start Date: \(classItem.startDate)")
                print("Learning Type: \(classItem.learningType ?? "N/A")")
                print("Available Spots: \((classItem.maxStudents ?? 0) - classItem.currentStudents)")
                print("---")
            }
            
        } catch {
            print("Failed to find classes: \(error)")
        }
    }
    
    // MARK: - Location Examples
    
    /// Example: Browse training locations
    func browseLocationsExample() async {
        do {
            let locations = try await apiService.getPublicLocations(
                page: 1,
                perPage: 10,
                city: "San Francisco",
                sortBy: "name",
                sortOrder: "asc"
            )
            
            print("Found \(locations.locations.count) locations")
            
            for location in locations.locations {
                print("Location: \(location.name)")
                print("City: \(location.city ?? "N/A")")
                print("Address: \(location.address ?? "N/A")")
                print("Facilities: \(location.facilities?.joined(separator: ", ") ?? "N/A")")
                print("---")
            }
            
        } catch {
            print("Failed to browse locations: \(error)")
        }
    }
    
    // MARK: - Error Handling Examples
    
    /// Example: Comprehensive error handling
    func errorHandlingExample() async {
        do {
            let _ = try await apiService.getPublicEvents()
            
        } catch NetworkError.unauthorized {
            print("User is not authenticated")
            // Redirect to login screen
            
        } catch NetworkError.serverError(let statusCode) {
            print("Server error with status code: \(statusCode)")
            // Show appropriate error message
            
        } catch NetworkError.networkUnavailable {
            print("Network is unavailable")
            // Show offline mode or retry option
            
        } catch NetworkError.decodingError {
            print("Failed to parse server response")
            // Log error for debugging
            
        } catch {
            print("Unexpected error: \(error)")
            // Show generic error message
        }
    }
    
    // MARK: - Configuration Monitoring Example
    
    /// Example: Listen for API configuration changes
    func monitorConfigurationChanges() {
        NotificationCenter.default.addObserver(
            forName: .apiConfigurationChanged,
            object: nil,
            queue: .main
        ) { _ in
            print("API configuration changed!")
            print("New base URL: \(APIConfiguration.shared.baseURL)")
            
            // Refresh data, update UI, etc.
            Task {
                await self.refreshAllData()
            }
        }
    }
    
    private func refreshAllData() async {
        // Refresh all cached data when API configuration changes
        print("Refreshing all data due to configuration change...")
    }
    
    // MARK: - Health Check Example
    
    /// Example: Test API connectivity
    func testAPIConnectivity() async {
        do {
            let healthStatus = try await apiService.healthCheck()
            
            if healthStatus.success {
                print("✅ API is healthy and reachable")
            } else {
                print("⚠️ API responded but reported issues")
            }
            
        } catch {
            print("❌ API health check failed: \(error)")
        }
    }
}

// MARK: - SwiftUI Integration Examples

struct APIExampleView: View {
    @State private var events: [PublicEvent] = []
    @State private var isLoading = false
    @State private var errorMessage: String?
    
    private let apiService = EarnBaseAPIService.shared
    
    var body: some View {
        NavigationView {
            List(events, id: \.id) { event in
                VStack(alignment: .leading, spacing: 4) {
                    Text(event.title)
                        .font(.headline)
                    
                    Text(event.description ?? "No description")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("Date: \(event.startDate)")
                        .font(.caption)
                }
            }
            .navigationTitle("Events")
            .refreshable {
                await loadEvents()
            }
            .task {
                await loadEvents()
            }
            .alert("Error", isPresented: .constant(errorMessage != nil)) {
                Button("OK") {
                    errorMessage = nil
                }
            } message: {
                Text(errorMessage ?? "")
            }
        }
    }
    
    private func loadEvents() async {
        isLoading = true
        errorMessage = nil
        
        do {
            let response = try await apiService.getPublicEvents()
            await MainActor.run {
                self.events = response.events
                self.isLoading = false
            }
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
                self.isLoading = false
            }
        }
    }
}
