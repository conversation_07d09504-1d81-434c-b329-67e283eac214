//
//  CompleteLogoutTest.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 22/7/25.
//

import SwiftUI

struct CompleteLogoutTest: View {
    @State private var testResults: [String] = []
    @State private var isRunning = false
    @State private var showingAlert = false
    @State private var alertMessage = ""
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header
                    VStack(spacing: 8) {
                        Text("Complete Logout Test")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                        
                        Text("Comprehensive test to ensure no auto-login")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .padding(.top)
                    
                    // Test Controls
                    testControlsSection
                    
                    // Test Results
                    testResultsSection
                    
                    Spacer()
                }
                .padding()
            }
            .navigationBarHidden(true)
            .alert("Test Result", isPresented: $showingAlert) {
                Button("OK") { }
            } message: {
                Text(alertMessage)
            }
        }
    }
    
    // MARK: - Test Controls Section
    private var testControlsSection: some View {
        VStack(spacing: 16) {
            Text("Test Controls")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 12) {
                Button(action: runCompleteTest) {
                    HStack {
                        if isRunning {
                            ProgressView()
                                .scaleEffect(0.8)
                        }
                        Text("Run Complete Logout Test")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(12)
                }
                .disabled(isRunning)
                
                Button(action: testCurrentState) {
                    Text("Test Current State")
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.green)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                }
                
                Button(action: forceCompleteClean) {
                    Text("Force Complete Clean")
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.red)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                }
                
                Button(action: clearTestResults) {
                    Text("Clear Results")
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.gray)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                }
            }
        }
    }
    
    // MARK: - Test Results Section
    private var testResultsSection: some View {
        VStack(spacing: 12) {
            Text("Test Results")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            if testResults.isEmpty {
                Text("No test results yet")
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
            } else {
                LazyVStack(spacing: 8) {
                    ForEach(Array(testResults.enumerated()), id: \.offset) { index, result in
                        HStack {
                            Text("\(index + 1).")
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .frame(width: 20, alignment: .leading)
                            
                            Text(result)
                                .font(.caption)
                                .frame(maxWidth: .infinity, alignment: .leading)
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(getResultColor(result))
                        .cornerRadius(8)
                    }
                }
            }
        }
    }
    
    // MARK: - Test Methods
    
    private func runCompleteTest() {
        isRunning = true
        testResults.removeAll()
        
        addTestResult("🧪 Starting complete logout test...")
        
        Task {
            await performCompleteLogoutTest()
            
            await MainActor.run {
                isRunning = false
                let hasIssues = testResults.contains { $0.contains("❌") || $0.contains("🚨") }
                alertMessage = hasIssues ? "❌ Test found issues - check results" : "✅ All tests passed!"
                showingAlert = true
            }
        }
    }
    
    private func performCompleteLogoutTest() async {
        // Step 1: Check initial state
        await MainActor.run {
            addTestResult("📊 Step 1: Checking initial state...")
            checkCurrentAuthState()
        }
        
        // Step 2: Force complete logout
        await MainActor.run {
            addTestResult("🧹 Step 2: Performing force complete logout...")
            AuthDebugUtils.shared.forceCompleteLogout()
        }
        
        // Wait for logout to complete
        try? await Task.sleep(nanoseconds: 2_000_000_000) // 2 seconds
        
        // Step 3: Verify clean state
        await MainActor.run {
            addTestResult("🔍 Step 3: Verifying clean state...")
            let isClean = AuthDebugUtils.shared.verifyCompleteLogout()
            addTestResult(isClean ? "✅ Clean state verified" : "❌ Clean state verification failed")
        }
        
        // Step 4: Create new auth instances (simulate app restart)
        await MainActor.run {
            addTestResult("🔄 Step 4: Creating new auth instances...")
            testNewAuthInstances()
        }
        
        // Step 5: Wait and check for auto-login
        await MainActor.run {
            addTestResult("⏱️ Step 5: Waiting 5 seconds to detect auto-login...")
        }
        
        try? await Task.sleep(nanoseconds: 5_000_000_000) // 5 seconds
        
        // Step 6: Final verification
        await MainActor.run {
            addTestResult("🎯 Step 6: Final verification...")
            checkForAutoLogin()
        }
    }
    
    private func testCurrentState() {
        addTestResult("📊 Testing current authentication state...")
        checkCurrentAuthState()
    }
    
    private func forceCompleteClean() {
        addTestResult("🧹 Forcing complete clean...")
        AuthDebugUtils.shared.forceCompleteLogout()
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            let isClean = AuthDebugUtils.shared.verifyCompleteLogout()
            addTestResult(isClean ? "✅ Force clean successful" : "❌ Force clean failed")
        }
    }
    
    private func checkCurrentAuthState() {
        // Check InstructorAuthService
        let instructorAuth = InstructorAuthService.shared
        addTestResult("📱 InstructorAuthService.isAuthenticated: \(instructorAuth.isAuthenticated)")
        addTestResult("👤 InstructorAuthService.currentInstructor: \(instructorAuth.currentInstructor?.displayName ?? "nil")")
        
        if instructorAuth.isAuthenticated {
            addTestResult("🚨 WARNING: InstructorAuthService is authenticated!")
        }
        
        // Check TokenManager
        let tokenManager = TokenManager.shared
        addTestResult("🔑 TokenManager.isLoggedIn: \(tokenManager.isLoggedIn)")
        addTestResult("🔑 TokenManager.hasToken: \(tokenManager.getToken() != nil)")
        
        if tokenManager.isLoggedIn {
            addTestResult("🚨 WARNING: TokenManager shows logged in!")
        }
        
        // Check UserDefaults
        let hasUserDefaultsToken = UserDefaults.standard.string(forKey: "linkx_access_token") != nil
        let hasUserDefaultsUser = UserDefaults.standard.data(forKey: "linkx_current_user") != nil
        let hasSavedEmail = UserDefaults.standard.string(forKey: "saved_email") != nil
        
        addTestResult("💾 UserDefaults token: \(hasUserDefaultsToken)")
        addTestResult("💾 UserDefaults user: \(hasUserDefaultsUser)")
        addTestResult("💾 UserDefaults saved email: \(hasSavedEmail)")
        
        if hasUserDefaultsToken || hasUserDefaultsUser {
            addTestResult("🚨 WARNING: UserDefaults contains auth data!")
        }
        
        // Check Keychain
        let hasKeychainToken = (try? KeychainManager.shared.loadAccessToken()) != nil
        let hasRefreshToken = (try? KeychainManager.shared.loadRefreshToken()) != nil
        let hasSavedCredentials = (try? KeychainManager.shared.loadString(key: "saved_credentials")) != nil
        
        addTestResult("🔐 Keychain access token: \(hasKeychainToken)")
        addTestResult("🔐 Keychain refresh token: \(hasRefreshToken)")
        addTestResult("🔐 Keychain saved credentials: \(hasSavedCredentials)")
        
        if hasKeychainToken || hasRefreshToken || hasSavedCredentials {
            addTestResult("🚨 WARNING: Keychain contains auth data!")
        }
    }
    
    private func testNewAuthInstances() {
        // Create new AuthStateManager instance
        let newAuthStateManager = AuthStateManager()
        addTestResult("🆕 Created new AuthStateManager - isAuthenticated: \(newAuthStateManager.isAuthenticated)")
        
        if newAuthStateManager.isAuthenticated {
            addTestResult("🚨 CRITICAL: New AuthStateManager is auto-authenticated!")
        }
        
        // Test InstructorAuthService (singleton)
        let instructorAuth = InstructorAuthService.shared
        addTestResult("🆕 InstructorAuthService (singleton) - isAuthenticated: \(instructorAuth.isAuthenticated)")
        
        if instructorAuth.isAuthenticated {
            addTestResult("🚨 CRITICAL: InstructorAuthService is auto-authenticated!")
        }
    }
    
    private func checkForAutoLogin() {
        addTestResult("🔍 Checking for auto-login after waiting...")
        
        // Re-check all auth states
        let instructorAuth = InstructorAuthService.shared
        let tokenManager = TokenManager.shared
        
        if instructorAuth.isAuthenticated {
            addTestResult("🚨 AUTO-LOGIN DETECTED: InstructorAuthService became authenticated!")
        } else {
            addTestResult("✅ InstructorAuthService remained unauthenticated")
        }
        
        if tokenManager.isLoggedIn {
            addTestResult("🚨 AUTO-LOGIN DETECTED: TokenManager shows logged in!")
        } else {
            addTestResult("✅ TokenManager remained logged out")
        }
        
        // Final summary
        let hasAutoLogin = instructorAuth.isAuthenticated || tokenManager.isLoggedIn
        addTestResult(hasAutoLogin ? "❌ AUTO-LOGIN DETECTED!" : "✅ NO AUTO-LOGIN DETECTED")
    }
    
    private func clearTestResults() {
        testResults.removeAll()
    }
    
    private func addTestResult(_ result: String) {
        let timestamp = DateFormatter.timeFormatter.string(from: Date())
        testResults.append("[\(timestamp)] \(result)")
    }
    
    private func getResultColor(_ result: String) -> Color {
        if result.contains("🚨") || result.contains("❌") {
            return Color.red.opacity(0.1)
        } else if result.contains("⚠️") {
            return Color.orange.opacity(0.1)
        } else if result.contains("✅") {
            return Color.green.opacity(0.1)
        } else {
            return Color(.systemGray6)
        }
    }
}

// MARK: - Preview
struct CompleteLogoutTest_Previews: PreviewProvider {
    static var previews: some View {
        CompleteLogoutTest()
    }
}
