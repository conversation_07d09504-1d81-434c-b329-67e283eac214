//
//  DebugCurrentState.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 22/7/25.
//

import SwiftUI

struct DebugCurrentState: View {
    @State private var debugInfo: [String] = []
    @State private var showingAlert = false
    @State private var alertMessage = ""
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header
                    VStack(spacing: 8) {
                        Text("Debug Current State")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                        
                        Text("Check all authentication states")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .padding(.top)
                    
                    // Controls
                    VStack(spacing: 12) {
                        Button(action: refreshDebugInfo) {
                            Text("Refresh Debug Info")
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color.blue)
                                .foregroundColor(.white)
                                .cornerRadius(12)
                        }
                        
                        But<PERSON>(action: forceCompleteClean) {
                            Text("Force Complete Clean")
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color.red)
                                .foregroundColor(.white)
                                .cornerRadius(12)
                        }
                        
                        But<PERSON>(action: clearDebugInfo) {
                            Text("Clear Debug Info")
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color.gray)
                                .foregroundColor(.white)
                                .cornerRadius(12)
                        }
                    }
                    
                    // Debug Info
                    VStack(spacing: 12) {
                        Text("Debug Information")
                            .font(.headline)
                            .frame(maxWidth: .infinity, alignment: .leading)
                        
                        if debugInfo.isEmpty {
                            Text("No debug info yet - tap Refresh")
                                .foregroundColor(.secondary)
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color(.systemGray6))
                                .cornerRadius(12)
                        } else {
                            LazyVStack(spacing: 8) {
                                ForEach(Array(debugInfo.enumerated()), id: \.offset) { index, info in
                                    HStack {
                                        Text("\(index + 1).")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                            .frame(width: 20, alignment: .leading)
                                        
                                        Text(info)
                                            .font(.caption)
                                            .frame(maxWidth: .infinity, alignment: .leading)
                                    }
                                    .padding(.horizontal, 12)
                                    .padding(.vertical, 8)
                                    .background(getInfoColor(info))
                                    .cornerRadius(8)
                                }
                            }
                        }
                    }
                    
                    Spacer()
                }
                .padding()
            }
            .navigationBarHidden(true)
            .alert("Debug Result", isPresented: $showingAlert) {
                Button("OK") { }
            } message: {
                Text(alertMessage)
            }
        }
        .onAppear {
            refreshDebugInfo()
        }
    }
    
    // MARK: - Debug Methods
    
    private func refreshDebugInfo() {
        debugInfo.removeAll()
        
        addDebugInfo("=== AUTHENTICATION STATE DEBUG ===")
        addDebugInfo("Timestamp: \(DateFormatter.debugFormatter.string(from: Date()))")
        addDebugInfo("")
        
        // 1. InstructorAuthService
        addDebugInfo("1. InstructorAuthService:")
        let instructorAuth = InstructorAuthService.shared
        addDebugInfo("   - isAuthenticated: \(instructorAuth.isAuthenticated)")
        addDebugInfo("   - currentInstructor: \(instructorAuth.currentInstructor?.displayName ?? "nil")")
        addDebugInfo("   - isLoading: \(instructorAuth.isLoading)")
        addDebugInfo("   - authError: \(instructorAuth.authError ?? "nil")")
        
        if instructorAuth.isAuthenticated {
            addDebugInfo("   🚨 ISSUE: InstructorAuthService is authenticated!")
        }
        
        addDebugInfo("")
        
        // 2. AuthStateManager (create new instance to test)
        addDebugInfo("2. AuthStateManager (new instance):")
        let authStateManager = AuthStateManager()
        addDebugInfo("   - isAuthenticated: \(authStateManager.isAuthenticated)")
        addDebugInfo("   - currentUser: \(authStateManager.currentUser?.displayName ?? "nil")")
        addDebugInfo("   - isLoading: \(authStateManager.isLoading)")
        
        if authStateManager.isAuthenticated {
            addDebugInfo("   🚨 ISSUE: New AuthStateManager is authenticated!")
        }
        
        addDebugInfo("")
        
        // 3. TokenManager
        addDebugInfo("3. TokenManager:")
        let tokenManager = TokenManager.shared
        addDebugInfo("   - isLoggedIn: \(tokenManager.isLoggedIn)")
        addDebugInfo("   - hasToken: \(tokenManager.getToken() != nil)")
        
        if let token = tokenManager.getToken() {
            addDebugInfo("   - token preview: \(String(token.prefix(30)))...")
            addDebugInfo("   🚨 ISSUE: TokenManager has token!")
        } else {
            addDebugInfo("   - token: nil")
        }
        
        addDebugInfo("")
        
        // 4. UserDefaults
        addDebugInfo("4. UserDefaults:")
        let userDefaultsKeys = [
            "linkx_access_token",
            "linkx_current_user", 
            "saved_email",
            "biometric_enabled",
            "access_token",
            "refresh_token"
        ]
        
        var hasUserDefaultsData = false
        for key in userDefaultsKeys {
            let hasValue = UserDefaults.standard.object(forKey: key) != nil
            addDebugInfo("   - \(key): \(hasValue)")
            if hasValue {
                hasUserDefaultsData = true
            }
        }
        
        if hasUserDefaultsData {
            addDebugInfo("   🚨 ISSUE: UserDefaults contains auth data!")
        }
        
        addDebugInfo("")
        
        // 5. Keychain
        addDebugInfo("5. Keychain:")
        let keychainManager = KeychainManager.shared
        
        let keychainKeys = [
            "access_token",
            "refresh_token", 
            "saved_credentials",
            "linkx_access_token"
        ]
        
        var hasKeychainData = false
        for key in keychainKeys {
            let hasValue = (try? keychainManager.loadString(key: key)) != nil
            addDebugInfo("   - \(key): \(hasValue)")
            if hasValue {
                hasKeychainData = true
            }
        }
        
        if hasKeychainData {
            addDebugInfo("   🚨 ISSUE: Keychain contains auth data!")
        }
        
        addDebugInfo("")
        
        // 6. AuthViewModel (create new instance to test)
        addDebugInfo("6. AuthViewModel (new instance):")
        let authViewModel = AuthViewModel()
        
        // Wait a bit for bindings to settle
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            addDebugInfo("   - isAuthenticated: \(authViewModel.isAuthenticated)")
            addDebugInfo("   - currentUser: \(authViewModel.currentUser?.displayName ?? "nil")")
            addDebugInfo("   - isLoading: \(authViewModel.isLoading)")
            
            if authViewModel.isAuthenticated {
                addDebugInfo("   🚨 ISSUE: New AuthViewModel is authenticated!")
            }
            
            addDebugInfo("")
            addDebugInfo("=== DEBUG COMPLETE ===")
            
            // Summary
            let hasIssues = debugInfo.contains { $0.contains("🚨") }
            if hasIssues {
                addDebugInfo("")
                addDebugInfo("❌ ISSUES FOUND - Auto-login sources detected!")
                alertMessage = "❌ Issues found - check debug info"
            } else {
                addDebugInfo("")
                addDebugInfo("✅ NO ISSUES FOUND - All auth states clean")
                alertMessage = "✅ All auth states are clean"
            }
            
            showingAlert = true
        }
    }
    
    private func forceCompleteClean() {
        addDebugInfo("🧹 Performing force complete clean...")
        
        // Use AuthDebugUtils
        AuthDebugUtils.shared.forceCompleteLogout()
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            let isClean = AuthDebugUtils.shared.verifyCompleteLogout()
            addDebugInfo(isClean ? "✅ Force clean successful" : "❌ Force clean failed")
            
            // Refresh debug info after clean
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                refreshDebugInfo()
            }
        }
    }
    
    private func clearDebugInfo() {
        debugInfo.removeAll()
    }
    
    private func addDebugInfo(_ info: String) {
        debugInfo.append(info)
    }
    
    private func getInfoColor(_ info: String) -> Color {
        if info.contains("🚨") || info.contains("❌") {
            return Color.red.opacity(0.1)
        } else if info.contains("⚠️") {
            return Color.orange.opacity(0.1)
        } else if info.contains("✅") {
            return Color.green.opacity(0.1)
        } else {
            return Color(.systemGray6)
        }
    }
}

// MARK: - Extensions
extension DateFormatter {
    static let debugFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .medium
        return formatter
    }()
}

// MARK: - Preview
struct DebugCurrentState_Previews: PreviewProvider {
    static var previews: some View {
        DebugCurrentState()
    }
}
