//
//  LogoutTestView.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 22/7/25.
//

import SwiftUI

struct LogoutTestView: View {
    @StateObject private var instructorAuth = InstructorAuthService.shared
    @StateObject private var authStateManager = AuthStateManager()
    @State private var testResults: [String] = []
    @State private var showingAlert = false
    @State private var alertMessage = ""
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header
                    VStack(spacing: 8) {
                        Text("Logout Test")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                        
                        Text("Test logout functionality and verify clean state")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .padding(.top)
                    
                    // Current Status
                    currentStatusSection
                    
                    // Test Actions
                    testActionsSection
                    
                    // Test Results
                    testResultsSection
                    
                    Spacer()
                }
                .padding()
            }
            .navigationBarHidden(true)
            .alert("Test Result", isPresented: $showingAlert) {
                Button("OK") { }
            } message: {
                Text(alertMessage)
            }
        }
    }
    
    // MARK: - Current Status Section
    private var currentStatusSection: some View {
        VStack(spacing: 12) {
            Text("Current Authentication Status")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 8) {
                StatusRow(
                    title: "InstructorAuth",
                    value: instructorAuth.isAuthenticated ? "✅ Authenticated" : "❌ Not Authenticated",
                    color: instructorAuth.isAuthenticated ? .green : .red
                )
                
                StatusRow(
                    title: "AuthStateManager",
                    value: authStateManager.isAuthenticated ? "✅ Authenticated" : "❌ Not Authenticated",
                    color: authStateManager.isAuthenticated ? .green : .red
                )
                
                StatusRow(
                    title: "TokenManager",
                    value: TokenManager.shared.isLoggedIn ? "✅ Has Token" : "❌ No Token",
                    color: TokenManager.shared.isLoggedIn ? .green : .red
                )
                
                StatusRow(
                    title: "UserDefaults",
                    value: hasUserDefaultsData() ? "⚠️ Has Data" : "✅ Clean",
                    color: hasUserDefaultsData() ? .orange : .green
                )
                
                StatusRow(
                    title: "Keychain",
                    value: hasKeychainData() ? "⚠️ Has Data" : "✅ Clean",
                    color: hasKeychainData() ? .orange : .green
                )
                
                if let instructor = instructorAuth.currentInstructor {
                    StatusRow(
                        title: "Current User",
                        value: instructor.displayName,
                        color: .blue
                    )
                } else if let user = authStateManager.currentUser {
                    StatusRow(
                        title: "Current User",
                        value: user.displayName,
                        color: .blue
                    )
                }
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
    }
    
    // MARK: - Test Actions Section
    private var testActionsSection: some View {
        VStack(spacing: 16) {
            Text("Test Actions")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 12) {
                Button(action: testInstructorLogout) {
                    Text("Test Instructor Logout")
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.red)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                }
                
                Button(action: testAuthStateLogout) {
                    Text("Test AuthState Logout")
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.orange)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                }
                
                Button(action: testCompleteLogout) {
                    Text("Test Complete Logout")
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.purple)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                }
                
                Button(action: testForceCleanup) {
                    Text("Force Complete Cleanup")
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.black)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                }
                
                Button(action: verifyCleanState) {
                    Text("Verify Clean State")
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.green)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                }
                
                Button(action: clearTestResults) {
                    Text("Clear Results")
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.gray)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                }
            }
        }
    }
    
    // MARK: - Test Results Section
    private var testResultsSection: some View {
        VStack(spacing: 12) {
            Text("Test Results")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            if testResults.isEmpty {
                Text("No test results yet")
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
            } else {
                LazyVStack(spacing: 8) {
                    ForEach(Array(testResults.enumerated()), id: \.offset) { index, result in
                        HStack {
                            Text("\(index + 1).")
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .frame(width: 20, alignment: .leading)
                            
                            Text(result)
                                .font(.caption)
                                .frame(maxWidth: .infinity, alignment: .leading)
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                    }
                }
            }
        }
    }
    
    // MARK: - Test Actions
    
    private func testInstructorLogout() {
        addTestResult("🔄 Testing InstructorAuthService logout...")
        
        Task {
            await instructorAuth.logout()
            
            await MainActor.run {
                addTestResult("✅ InstructorAuthService logout completed")
                addTestResult("📊 InstructorAuth.isAuthenticated: \(instructorAuth.isAuthenticated)")
                addTestResult("👤 InstructorAuth.currentInstructor: \(instructorAuth.currentInstructor?.displayName ?? "nil")")
                
                alertMessage = "Instructor logout completed"
                showingAlert = true
            }
        }
    }
    
    private func testAuthStateLogout() {
        addTestResult("🔄 Testing AuthStateManager logout...")
        
        Task {
            await authStateManager.logout()
            
            await MainActor.run {
                addTestResult("✅ AuthStateManager logout completed")
                addTestResult("📊 AuthState.isAuthenticated: \(authStateManager.isAuthenticated)")
                addTestResult("👤 AuthState.currentUser: \(authStateManager.currentUser?.displayName ?? "nil")")
                
                alertMessage = "AuthState logout completed"
                showingAlert = true
            }
        }
    }
    
    private func testCompleteLogout() {
        addTestResult("🔄 Testing complete logout (both services)...")
        
        Task {
            // Logout from both services
            await instructorAuth.logout()
            await authStateManager.logout()
            
            await MainActor.run {
                addTestResult("✅ Complete logout finished")
                addTestResult("📊 InstructorAuth: \(instructorAuth.isAuthenticated)")
                addTestResult("📊 AuthState: \(authStateManager.isAuthenticated)")
                addTestResult("📊 TokenManager: \(TokenManager.shared.isLoggedIn)")
                
                alertMessage = "Complete logout finished"
                showingAlert = true
            }
        }
    }
    
    private func testForceCleanup() {
        addTestResult("🔄 Testing force complete cleanup...")
        
        AuthDebugUtils.shared.forceCompleteLogout()
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            addTestResult("✅ Force cleanup completed")
            addTestResult("📊 Verification: \(AuthDebugUtils.shared.verifyCompleteLogout() ? "CLEAN" : "NOT CLEAN")")
            
            alertMessage = "Force cleanup completed"
            showingAlert = true
        }
    }
    
    private func verifyCleanState() {
        addTestResult("🔍 Verifying clean state...")
        
        let isClean = AuthDebugUtils.shared.verifyCompleteLogout()
        
        addTestResult("📊 InstructorAuth.isAuthenticated: \(instructorAuth.isAuthenticated)")
        addTestResult("📊 AuthState.isAuthenticated: \(authStateManager.isAuthenticated)")
        addTestResult("📊 TokenManager.isLoggedIn: \(TokenManager.shared.isLoggedIn)")
        addTestResult("📊 UserDefaults clean: \(!hasUserDefaultsData())")
        addTestResult("📊 Keychain clean: \(!hasKeychainData())")
        addTestResult("🎯 Overall result: \(isClean ? "✅ CLEAN" : "❌ NOT CLEAN")")
        
        alertMessage = isClean ? "✅ State is clean" : "❌ State is not clean"
        showingAlert = true
    }
    
    private func clearTestResults() {
        testResults.removeAll()
    }
    
    private func addTestResult(_ result: String) {
        let timestamp = DateFormatter.timeFormatter.string(from: Date())
        testResults.append("[\(timestamp)] \(result)")
    }
    
    // MARK: - Helper Methods
    
    private func hasUserDefaultsData() -> Bool {
        return UserDefaults.standard.string(forKey: "linkx_access_token") != nil ||
               UserDefaults.standard.data(forKey: "linkx_current_user") != nil ||
               UserDefaults.standard.string(forKey: "saved_email") != nil
    }
    
    private func hasKeychainData() -> Bool {
        let hasAccessToken = (try? KeychainManager.shared.loadAccessToken()) != nil
        let hasRefreshToken = (try? KeychainManager.shared.loadRefreshToken()) != nil
        let hasSavedCredentials = (try? KeychainManager.shared.loadString(key: "saved_credentials")) != nil
        
        return hasAccessToken || hasRefreshToken || hasSavedCredentials
    }
}

// MARK: - Preview
struct LogoutTestView_Previews: PreviewProvider {
    static var previews: some View {
        LogoutTestView()
    }
}
